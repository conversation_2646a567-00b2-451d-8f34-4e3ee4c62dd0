package com.imile.hrms.api.freelancer.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 附件
 * <AUTHOR>
 */
@Data
public class FreelancerAutoRegistryAttachmentApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附件名称
     */
    private String attachmentName;
    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 文件路径
     */
    private String urlPath;

}
