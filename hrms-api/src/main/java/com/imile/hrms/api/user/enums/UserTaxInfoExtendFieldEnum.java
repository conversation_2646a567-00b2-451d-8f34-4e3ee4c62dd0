package com.imile.hrms.api.user.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/6/21
 */
@Getter
public enum UserTaxInfoExtendFieldEnum {
    /**
     * 人员税务信息扩展字段枚举
     */
    ABN_NAME("abnName", "ABN名称"),
    ABN_NUMBER("abnNumber", "ABN号码"),
    IS_GST_REGISTERED("isGstRegistered", "是否提供GST"),
    ;

    private final String key;

    private final String desc;

    UserTaxInfoExtendFieldEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
