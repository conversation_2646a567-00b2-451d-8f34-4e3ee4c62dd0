package com.imile.hrms.api.attendance.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} SalaryDriverAttendanceApiParam
 * {@code @since:} 2024-12-26 19:32
 * {@code @description:} 薪酬调用获取司机考勤数据入参
 */
@Data
public class SalaryDriverAttendanceApiParam implements Serializable {
    private static final long serialVersionUID = 4395382305948103582L;

    private List<String> userCodeList;

    private Date startTime;

    private Date endTime;
}
