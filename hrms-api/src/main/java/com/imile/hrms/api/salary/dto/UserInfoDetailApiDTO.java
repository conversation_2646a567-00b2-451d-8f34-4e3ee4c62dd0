package com.imile.hrms.api.salary.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/3/9
 */
@Data
public class UserInfoDetailApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 员工姓名
     */
    private String userName;
    /**
     * 员工英文名
     */
    private String userNameEn;
    /**
     * 国家
     */
    @Deprecated
    private String country;
    /**
     * 企业微信id
     */
    private String relationId;

    /**
     * 员工头像地址
     */
    private String profilePhotoUrl;

    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;

    /**
     * 岗位信息
     */
    private Long postId;


    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;
    /**
     * 部门
     */
    private Long deptId;
    /**
     * 网点
     */
    private Long ocId;

    /**
     * 所属国
     */
    private String originCountry;
}
