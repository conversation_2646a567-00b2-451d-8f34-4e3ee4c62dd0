package com.imile.hrms.api.user.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/9/20 16:14
 * @version: 1.0
 */
@Data
public class BizModelApiDTO implements Serializable {
    private static final long serialVersionUID = -2604735021175687321L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 业务节点名称（中文）
     */
    private String bizModelNameCn;

    /**
     * 业务节点名称（英文）
     */
    private String bizModelNameEn;

    /**
     * 是否用于财务费用结算
     */
    private Integer isCostSettlement;

    /**
     * 启用状态
     */
    private String status;
}
