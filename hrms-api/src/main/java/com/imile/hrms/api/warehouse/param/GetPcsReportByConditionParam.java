package com.imile.hrms.api.warehouse.param;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetPcsReportByConditionParam extends BaseQuery {

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 网点
     */
    private List<Long> ocIdList;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 操作状态
     */
    private String pcsStatus;

    /**
     * 人员搜索Key
     */
    private String searchUserKey;

    /**
     * 报告类型 DAY:日报 WEEK:周报 MONTH:月报
     */
    private String reportType;

    /**
     * 当日结果
     */
    private Integer attendanceStatus;

    /**
     * 班次类型列表
     */
    private List<Integer> classesTypeList;

    public void check() {
        Assert.notNull(vendorCode, "vendorCode cannot be empty");
        Assert.notNull(startDate, "startDate cannot be empty");
        Assert.notNull(endDate, "endDate cannot be empty");
    }

}
