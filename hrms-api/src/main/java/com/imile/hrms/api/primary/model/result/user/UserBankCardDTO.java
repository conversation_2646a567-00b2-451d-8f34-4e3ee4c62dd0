package com.imile.hrms.api.primary.model.result.user;

import com.imile.hrms.api.primary.model.component.Field;
import com.imile.hrms.api.user.enums.UserPaymentInfoExtendFieldEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBankCardDTO implements Serializable {

    private static final long serialVersionUID = 3192981608736166600L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 开户行
     */
    private String bankBranch;

    /**
     * 银行户名
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String accountNumber;

    /**
     * 扩展字段列表
     *
     * @see UserPaymentInfoExtendFieldEnum
     */
    private List<Field> extendFieldList;
}
