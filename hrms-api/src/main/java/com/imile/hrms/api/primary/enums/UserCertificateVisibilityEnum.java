package com.imile.hrms.api.primary.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/23
 */
@Getter
public enum UserCertificateVisibilityEnum {
    /**
     * 人员证件可见度
     */
    ALL(1, "全部可见"),
    PART(2, "部分可见"),
    ;

    private final Integer visibility;

    private final String desc;

    UserCertificateVisibilityEnum(Integer visibility, String desc) {
        this.visibility = visibility;
        this.desc = desc;
    }
}
