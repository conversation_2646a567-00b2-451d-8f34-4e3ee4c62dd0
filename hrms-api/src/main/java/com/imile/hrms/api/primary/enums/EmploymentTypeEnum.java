package com.imile.hrms.api.primary.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
@Getter
public enum EmploymentTypeEnum {
    /**
     * 用工类型
     */
    DEFAULT("", "", "缺省值", ""),
    EMPLOYEE("Employee", EmployeeNatureEnum.INTERNAL.getCode(), "员工", "Employee"),
    SUB_EMPLOYEE("SubEmployee", EmployeeNatureEnum.INTERNAL.getCode(), "挂靠", "Sponsorship"),
    OS_FIXED_SALARY("OSFixedsalary", EmployeeNatureEnum.EXTERNAL.getCode(), "劳务派遣", "Manpower OS"),
    OS_PER_DELIVERED("OSPerdelivered", EmployeeNatureEnum.EXTERNAL.getCode(), "合作伙伴", "Partner-employee"),
    FREELANCER("Freelancer", EmployeeNatureEnum.EXTERNAL.getCode(), "众包", "Freelancer"),
    INTERN("Intern", EmployeeNatureEnum.INTERNAL.getCode(), "实习生", "Intern"),
    PART_TIMER("PartTimer", EmployeeNatureEnum.INTERNAL.getCode(), "兼职", "Part-timer"),
    CONSULTANT("Consultant", EmployeeNatureEnum.EXTERNAL.getCode(), "顾问", "Consultant"),
    ;
    private final String code;
    private final String nature;
    private final String desc;
    private final String descEn;

    EmploymentTypeEnum(String code, String nature, String desc, String descEn) {
        this.code = code;
        this.nature = nature;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static EmploymentTypeEnum valueOfCode(String code) {
        for (EmploymentTypeEnum employmentTypeEnum : EmploymentTypeEnum.values()) {
            if (employmentTypeEnum.getCode().equals(code)) {
                return employmentTypeEnum;
            }
        }
        return DEFAULT;
    }
}
