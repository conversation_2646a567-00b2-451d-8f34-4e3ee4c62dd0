package com.imile.hrms.api.user.api;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.user.enums.DriverDynamicFieldEnum;
import com.imile.hrms.api.user.param.DriverFilterParam;
import com.imile.hrms.api.user.param.SupplierDriverAuditParam;
import com.imile.hrms.api.user.result.DriverDynamicInfoDTO;
import com.imile.hrms.api.user.result.UserCertificateDTO;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
public interface DriverApi {

    /**
     * 审核供应商司机
     *
     * @param param SupplierDriverAuditParam
     * @return Boolean
     */
    RpcResult<Boolean> auditSupplierDriver(SupplierDriverAuditParam param);

    /**
     * 获取司机ID分页列表
     *
     * @param param DriverFilterParam
     * @return PaginationResult<Long>
     */
    RpcResult<PaginationResult<Long>> getDriverIdPageList(DriverFilterParam param);

    /**
     * 批量获取司机动态信息
     *
     * @param idList           司机ID列表
     * @param dynamicFieldList 动态字段列表（详见DriverDynamicFieldEnum）
     * @return List<DriverDynamicInfoDTO>
     */
    RpcResult<List<DriverDynamicInfoDTO>> listDriverDynamicInfo(List<Long> idList, List<DriverDynamicFieldEnum> dynamicFieldList);

    /**
     * 获取司机证件列表
     *
     * @param id 司机ID
     * @return List<UserCertificateDTO>
     */
    RpcResult<List<UserCertificateDTO>> getDriverCertificateList(Long id);
}
