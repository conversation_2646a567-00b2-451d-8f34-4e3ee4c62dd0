package com.imile.hrms.api.primary.model.param.user;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Data
public class UserEditParam implements Serializable {

    private static final long serialVersionUID = -7529866319560059086L;

    /**
     * 人员编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String userCode;

    /**
     * 人员证件名
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String userName;

    /**
     * 人员昵称
     */
    private String userNickName;

    /**
     * 人员性别（1:男 2:女）
     */
    private Integer userSex;

    /**
     * 出生日期
     */
    private Date userBirthday;

    /**
     * 国籍编码
     */
    private String nationalityCode;

    /**
     * 国际区号
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String countryCallingCode;

    /**
     * 联系电话
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String userPhone;

    /**
     * 用工类型（详见枚举类：EmploymentTypeEnum）
     *
     * @see com.imile.hrms.api.primary.enums.EmploymentTypeEnum
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String employeeType;

    /**
     * 汇报上级人员编码
     */
    private String leaderUserCode;

    /**
     * 工作邮箱
     */
    private String workEmail;

    /**
     * 工作岗位ID
     */
    private Long postId;

    /**
     * 所属部门编码（办公室员工时必传）
     */
    private String deptCode;

    /**
     * 所属网点编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String ocCode;

    /**
     * 所属结算主体编码
     */
    private String settlementCenterCode;

    /**
     * 所属供应商编码
     */
    private String vendorCode;

    /**
     * 常驻地国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCountry;

    /**
     * 常驻地省份
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationProvince;

    /**
     * 常驻地城市
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCity;

    /**
     * 人员证件列表
     */
    private List<UserCertificateSaveParam> userCertificateList;

    /**
     * 人员银行卡列表
     */
    @Valid
    private List<UserBankCardSaveParam> userBankCardList;

    /**
     * 人员证件可见度（详见枚举类：UserCertificateVisibilityEnum）
     *
     * @see com.imile.hrms.api.primary.enums.UserCertificateVisibilityEnum
     */
    private Integer userCertificateVisibility;

    /**
     * 职能（多个时英文逗号分隔）
     *
     * @see com.imile.hrms.api.primary.enums.DriverFuntionalEnum
     */
    private String functional;

    /**
     * 业务数据
     * 主数据消息广播时传递给下游系统
     */
    private Object bizData;
}
