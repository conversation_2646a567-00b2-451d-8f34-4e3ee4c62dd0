/*
package com.imile.hrms.api.user.api;

import com.imile.hrms.api.user.dto.UserExtendAttrInfoDTO;
import com.imile.hrms.api.user.param.UserExtendAttrParam;

import java.util.List;

*/
/**
 * <AUTHOR>
 * @since 2025/4/10
 *//*

public interface UserExtendAttrApi {

    */
/**
     * 根据属性Key相关条件查询
     *
     * @param param UserExtendAttrParam
     * @return UserExtendAttrDTO
     *//*

    List<UserExtendAttrInfoDTO> selectByAttrKeyCondition(UserExtendAttrParam param);

    */
/**
     * 批量获取人员指定扩展信息
     *
     * @param userIdList  人员ID列表
     * @param attrKeyList 属性键列表
     * @return List<UserExtendAttrInfoDTO>
     *//*

    List<UserExtendAttrInfoDTO> getUserExtendAttrList(List<Long> userIdList, List<String> attrKeyList);
}
*/
