package com.imile.hrms.api.freelancer.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class FreelancerPaymentInfoApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * CPF NUMBER
     */
    private String cpfNumber;

    /**
     * CPF NUMBER重复
     */
    private String cpfNumberDuplicate;

    /**
     * CNPJ NUMBER
     */
    private String cnpjNumber;

    /**
     * CNPJ NUMBER重复
     */
    private String cnpjNumberDuplicate;

    /**
     * CPF证件信息
     */
    private String cpfCertificatePath;
    private String cpfCertificatePathHttps;

    /**
     * CNPJ证件信息
     */
    private String cnpjCertificatePath;
    private String cnpjCertificatePathHttps;

    /**
     * IE
     */
    private String ie;

    /**
     * 税务ID
     */
    private String taxId;

    /**
     * 税务ID来源
     */
    private String taxIdSource;



}
