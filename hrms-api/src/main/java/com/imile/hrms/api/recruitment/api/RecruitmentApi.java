package com.imile.hrms.api.recruitment.api;

import com.imile.hrms.api.recruitment.model.UserOfferSalaryDTO;
import com.imile.hrms.api.salary.dto.UserInfoApiDTO;
import com.imile.hrms.api.salary.query.CrmUserQuery;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * 招聘api
 *
 * <AUTHOR>
 */
public interface RecruitmentApi {
    /**
     * 获取用户offer薪资数据
     * @param userCode
     * @return
     */
    RpcResult<UserOfferSalaryDTO> getOfferSalary(String userCode);

}
