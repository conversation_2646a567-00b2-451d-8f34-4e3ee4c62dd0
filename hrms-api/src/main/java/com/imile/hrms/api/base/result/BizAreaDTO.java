package com.imile.hrms.api.base.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizAreaDTO implements Serializable {

    private static final long serialVersionUID = -539815238203830350L;

    /**
     * 业务领域id
     */
    private Long bizAreaId;

    /**
     * 业务领域中文名
     */
    private String bizAreaNameCn;

    /**
     * 业务领域英文名
     */
    private String bizAreaNameEn;
}
