package com.imile.hrms.api.organization.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @date 2023/2/21 16:37
 */
@Data
public class PostApiQuery extends BaseQuery implements Serializable {
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位列表
     */
    private List<Long> postIdList;
    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 状态
     */
    private String status;
}
