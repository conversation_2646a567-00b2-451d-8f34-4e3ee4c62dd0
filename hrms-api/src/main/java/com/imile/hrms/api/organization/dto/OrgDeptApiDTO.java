package com.imile.hrms.api.organization.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 部门信息
 * <AUTHOR>
 */
@Data
public class OrgDeptApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    private Long id;

    /**
     * 企业id
     */
    private Long orgId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 网点中心编码
     */
    private String ocCenterCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;

    /**
     * 是否作业部门
     */
    private Integer isOperationDept;

    /**
     * 网点code
     */
    private String ocCode;

    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 状态
     */
    private String status;


    /**
     * 部门属性
     */
    private List<OrgDeptAttributeApiDTO> deptAttributeApiList;

    /**
     * 是否被逻辑删
     */
    private Integer deleteFlag;

}