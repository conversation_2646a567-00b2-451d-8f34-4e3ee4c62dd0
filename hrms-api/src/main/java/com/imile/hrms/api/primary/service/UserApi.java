package com.imile.hrms.api.primary.service;

import com.imile.hrms.api.primary.model.param.user.UserConditionParam;
import com.imile.hrms.api.primary.model.param.user.UserCreateParam;
import com.imile.hrms.api.primary.model.param.user.UserEditParam;
import com.imile.hrms.api.primary.model.param.user.UserStatusSwitchParam;
import com.imile.hrms.api.primary.model.result.user.UserBankCardDTO;
import com.imile.hrms.api.primary.model.result.user.UserBaseDTO;
import com.imile.hrms.api.primary.model.result.user.UserDTO;
import com.imile.rpc.common.RpcResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/6
 */
@Deprecated
public interface UserApi {

    /**
     * 创建人员
     * 接口过期请对接genesis服务
     *
     * @param param UserCreateParam
     * @return String
     */
    @Deprecated
    RpcResult<String> createUser(UserCreateParam param);

    /**
     * 编辑人员
     * 接口过期请对接genesis服务
     *
     * @param param UserEditParam
     * @return Boolean
     */
    @Deprecated
    RpcResult<Boolean> editUser(UserEditParam param);

    /**
     * 切换人员状态
     * 接口过期请对接genesis服务
     *
     * @param param UserStatusSwitchParam
     * @return Boolean
     */
    @Deprecated
    RpcResult<Boolean> switchUserStatus(UserStatusSwitchParam param);

    /**
     * 根据人员编码获取人员
     * 接口过期请对接genesis服务
     *
     * @param userCode 人员编码
     * @return UserDTO
     */
    @Deprecated
    RpcResult<UserDTO> getUserByCode(String userCode);

    /**
     * 根据动态条件获取人员（最多返回1000条）
     * 接口过期请对接genesis服务
     *
     * @param param UserConditionParam
     * @return List<UserDTO>
     */
    @Deprecated
    RpcResult<List<UserDTO>> listUserByCondition(UserConditionParam param);

    /**
     * 获取人员编码与汇报链映射
     *
     * @param userCodeList 人员编码列表
     * @return Map
     */
    RpcResult<Map<String, List<UserBaseDTO>>> getUserLeaderChainMap(List<String> userCodeList);

    /**
     * 获取人员银行卡列表
     *
     * @param userCode 人员编码
     * @return List<UserBankCardDTO>
     */
    RpcResult<List<UserBankCardDTO>> getUserBankCardList(String userCode);
}
