package com.imile.hrms.api.freelancer.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class FreelancerInfoApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 编码
     */
    private String userCode;

    /****************************************************众包司机***************************************************************/

    /**
     * 年龄
     */
    private String age;

    /**
     * 城市
     */
    private String city;
    /**
     * 省
     */
    private String province;
    /**
     * 国家
     */
    private String country;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 邮编id
     */
    private List<DriverZipCodeApiDTO> zipCodeList;

    /**
     * 工作时间 开始
     */
    private String workingStartTime;

    /**
     * 工作时间 结束
     */
    private String workingEndTime;

    /**
     * 身份证件信息
     */
    private FreelancerDriverCertificateInfoApiDTO idCard;

    /**
     * 驾驶证件信息
     */
    private FreelancerDriverCertificateInfoApiDTO driverLicense;

    /**
     * 行驶证证件信息
     */
    private FreelancerDriverCertificateInfoApiDTO vehicleLicense;

    /**
     * 车型
     */
    private String workingVehicleType;

    /**
     * 车牌号码
     */
    private String vehicleNumber;

    /**
     * Payment信息
     */
    private FreelancerPaymentInfoApiDTO paymentInfoDTO;

    /**
     * 是否供应商外包
     */
    private Integer vendorDriver;


    /**********************************************自主注册众包司机*******************************************************/

    /**
     * 是否自主注册 0:否 / 1：是
     */
    private Integer autoRegistry;

    /**
     * 众包自主注册
     */
    private FreelancerInfoAutoRegistryApiDTO freelancerInfoAutoRegistry;

}
