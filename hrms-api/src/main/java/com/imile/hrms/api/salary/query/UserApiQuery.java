package com.imile.hrms.api.salary.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/20
 */
@Data
public class UserApiQuery implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 名称 模糊匹配
     */
    private String userName;
    /**
     * 岗位名称
     */
    private List<Long> postIds;
}
