package com.imile.hrms.api.warehouse.api;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.warehouse.dto.UserEmploymentFormDTO;
import com.imile.hrms.api.warehouse.dto.WarehousePcsMonthReportCountDTO;
import com.imile.hrms.api.warehouse.dto.WarehousePcsMonthReportPassRateDTO;
import com.imile.hrms.api.warehouse.dto.WarehousePcsReportDTO;
import com.imile.hrms.api.warehouse.param.GetPcsReportByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportCountByConditionParam;
import com.imile.hrms.api.warehouse.param.UserEmploymentFormParam;
import com.imile.rpc.common.RpcResult;

import java.util.List;


/**
 * PCS调用接口
 *
 * <AUTHOR>
 * @since 2024/9/30
 */
public interface WarehouseExternalApi {

    RpcResult<PaginationResult<WarehousePcsReportDTO>> pcsDateReport(GetPcsReportByConditionParam param);

    RpcResult<PaginationResult<WarehousePcsReportDTO>> pcsWeekOrMonthReport(GetPcsReportByConditionParam param);

    RpcResult<WarehousePcsMonthReportCountDTO> pcsDateReportCount(GetPcsReportCountByConditionParam param);

    RpcResult<WarehousePcsMonthReportPassRateDTO> pcsDateMonthReportPassRate(GetPcsReportCountByConditionParam param);

    RpcResult<List<UserEmploymentFormDTO>> getEmploymentFormByUserCodes(UserEmploymentFormParam param);
}
