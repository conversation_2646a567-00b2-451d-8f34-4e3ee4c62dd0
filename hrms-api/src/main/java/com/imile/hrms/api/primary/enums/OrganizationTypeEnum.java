package com.imile.hrms.api.primary.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Getter
public enum OrganizationTypeEnum {
    /**
     * 组织类型(主数据)
     */
    DEPARTMENT("DEPARTMENT", "部门"),

    /**
     * 网点（TMS网点）
     */
    STATION("STATION", "网点"),

    /**
     * 网点下部门（重构系统）
     */
    STATION_DEPARTMENT("STATION_DEPARTMENT", "网点下部门"),
    ;

    private final String code;

    private final String desc;

    OrganizationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
