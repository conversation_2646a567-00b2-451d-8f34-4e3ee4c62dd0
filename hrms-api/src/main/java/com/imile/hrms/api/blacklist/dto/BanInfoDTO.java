package com.imile.hrms.api.blacklist.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询用户黑名单信息DTO
 *
 * @ClassName BlacklistInfoDTO
 * <AUTHOR>
 * @Date 2023/5/17 10:38
 */
@Data
public class BanInfoDTO implements Serializable {

    private static final long serialVersionUID = 7374060205848252134L;
    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 封禁理由
     */
    private String reason;

    /**
     * 是否封禁，1封禁状态、0正常状态
     * 封禁状态：banStatus=1、3
     * 正常状态：banStatus=2，或该用户从未被禁用
     */
    private Integer isBan;

    /**
     * 封禁状态，1封禁中、2封禁结束、3封禁处理中
     *  ——封禁完成：黑名单日期截止小于当前日期
     *  ——封禁中：黑名单截止日期大于当前日期
     *  ——封禁处理中：黑名单截止日期大于当前日期 且 司机存在欠款欠货
     */
    private Integer banStatus;

    /**
     * 封禁截止日期
     */
    private Date endDate;
}
