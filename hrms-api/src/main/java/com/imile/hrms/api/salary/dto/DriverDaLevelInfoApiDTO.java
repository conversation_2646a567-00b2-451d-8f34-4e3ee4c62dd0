package com.imile.hrms.api.salary.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-12-28
 * @version: 1.0
 */
@Data
public class DriverDaLevelInfoApiDTO implements Serializable {

    private static final long serialVersionUID = -8740142196254556124L;

    /**
     * 用户编码/账号
     */
    private String userCode;

    /**
     * 司机等级
     */
    private String daLevel;

    /**
     * 最大日派单量
     */
    private Integer maxSpr;

    /**
     * 当前司机等级
     */
    private String currentLevel;

    /**
     * 使用场景
     */
    private List<String> affectFunctionList;
}
