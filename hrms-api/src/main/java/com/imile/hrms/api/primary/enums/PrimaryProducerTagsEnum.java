package com.imile.hrms.api.primary.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@Getter
public enum PrimaryProducerTagsEnum {
    /**
     * 主数据生产者标签枚举
     */
    USER_INFO("USER_INFO", "人员信息"),
    USER_STATUS("USER_STATUS", "人员状态"),
    ORGANIZATION_INFO("ORGANIZATION_INFO", "组织信息"),
    ORGANIZATION_STATUS("ORGANIZATION_STATUS", "组织状态"),
    ;

    private final String code;

    private final String desc;

    PrimaryProducerTagsEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
