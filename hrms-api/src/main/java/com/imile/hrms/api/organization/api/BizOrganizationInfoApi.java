package com.imile.hrms.api.organization.api;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.organization.dto.CountryConfigApiDTO;
import com.imile.hrms.api.organization.dto.DeptRegionInfoApiDTO;
import com.imile.hrms.api.organization.dto.EntDeptInfoByLevelApiDTO;
import com.imile.hrms.api.organization.dto.EntOcOperationRecordDTO;
import com.imile.hrms.api.organization.dto.EntPostApiDTO;
import com.imile.hrms.api.organization.dto.OrgDeptApiDTO;
import com.imile.hrms.api.organization.dto.OrgDeptTreeApiDTO;
import com.imile.hrms.api.organization.dto.OrgPostInfoApiDTO;
import com.imile.hrms.api.organization.dto.OrgProjectDTO;
import com.imile.hrms.api.organization.dto.OrgSettlementCenterDTO;
import com.imile.hrms.api.organization.dto.OrgUserInfoApiDTO;
import com.imile.hrms.api.organization.dto.OrgUserInfoDetailApiDTO;
import com.imile.hrms.api.organization.dto.SelectUserApiDTO;
import com.imile.hrms.api.organization.dto.SyncCountryKingdeeDTO;
import com.imile.hrms.api.organization.dto.UserInfoApiDTO;
import com.imile.hrms.api.organization.query.PostApiQuery;
import com.imile.hrms.api.organization.query.SelectUserApiQuery;
import com.imile.hrms.api.organization.query.SelectUserInfoApiQuery;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * api
 *
 * <AUTHOR>
 */
@Deprecated
public interface BizOrganizationInfoApi {

    /**
     * 根据用户 code 获取用户信息
     *
     * @param userCodeList 用户 code 数组
     * @return RpcResult<List < OrgUserInfoApiDTO>>
     */
    RpcResult<List<OrgUserInfoApiDTO>> getUserInfoByUserCodeList(List<String> userCodeList);

    /**
     * 根据业务领域id(集合)获取部门ID
     *
     * @param bizArea 领域id
     * @return RpcResult<List < Long>>
     */
    RpcResult<List<Long>> getDeptIdInfoByBizArea(Integer bizArea);


    /**
     * 根据业务领域id(集合)获取部门下所有员工
     *
     * @param bizArea 领域id
     * @return RpcResult<List < OrgUserInfoApiDTO>>
     */
    RpcResult<List<OrgUserInfoApiDTO>> getUserInfoByBizArea(Integer bizArea);


    /**
     * 根据部门id(集合)获取部门下所有员工
     *
     * @param deptIds
     * @return
     */
    RpcResult<List<OrgUserInfoApiDTO>> getUserInfoByDeptIds(List<Long> deptIds);

    /**
     * 根据部门id(集合)获取部门下所有员工code
     *
     * @param deptIds
     * @return
     */
    RpcResult<List<String>> getUserCodesByDeptIds(List<Long> deptIds);

    /**
     * 根据国家获取部门
     *
     * @param country
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getDeptInfo(String country);

    /**
     * 根据子公司获取所有网点
     *
     * @param country
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getStationByCountry(String country);

    /**
     * 根据子公司获取所有部门
     *
     * @param country
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getDeptByCountry(String country);

    /**
     * 获取所有结算主体
     *
     * @return List<OrgSettlementCenterDTO>
     */
    RpcResult<List<OrgSettlementCenterDTO>> getSettlement();

    /**
     * 获取结算主体绑定的员工
     *
     * @param companyOrgIds
     * @return
     */
    RpcResult<List<OrgUserInfoApiDTO>> getUserBySettlement(List<Long> companyOrgIds);

    /**
     * 根据orgId获取结算组织信息
     *
     * @param companyOrgIds
     * @return
     */
    RpcResult<List<OrgSettlementCenterDTO>> getSettlementByOrgId(List<Long> companyOrgIds);

    /**
     * 根据id获取部门
     *
     * @param deptIds
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getDeptByIds(List<Long> deptIds);

//    /**
//     * 根据公司id获取公司
//     *
//     * @param companyIds
//     * @return
//     */
//    RpcResult<List<OrgCompanyApiDTO>> getCompanyByIds(List<Long> companyIds);

//    /**
//     * 根据所有国家
//     */
//    RpcResult<List<OrgCompanyApiDTO>> selectAllCompany();

    /**
     * 根据网点code获取网点
     *
     * @param ocCodes
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getStationByCodes(List<String> ocCodes);

    /**
     * 根据userCode获取员工详情
     *
     * @param userCode
     * @return
     */
    RpcResult<OrgUserInfoDetailApiDTO> userInfoDetail(String userCode);

    /**
     * 根据userCode获取员工详情
     *
     * @param userCodes
     * @return
     */
    RpcResult<List<OrgUserInfoDetailApiDTO>> userInfoDetailByUserCodes(List<String> userCodes);

    /**
     * 根据项目code获取项目信息
     *
     * @param projectCodes
     * @return
     */
    RpcResult<List<OrgProjectDTO>> getProjectInfoByCode(List<String> projectCodes);

    /**
     * 新增编辑国家-同步金蝶
     *
     * @param
     * @return
     */
    RpcResult<Boolean> syncCountryKingdee(SyncCountryKingdeeDTO dto);


    RpcResult<List<OrgDeptTreeApiDTO>> selectTreeByDeptIds(List<Long> deptIds);


    RpcResult<PaginationResult<SelectUserApiDTO>> selectUserList(SelectUserApiQuery userQuery);

    /**
     * 岗位id获取岗位信息
     *
     * @param postIds
     * @return
     */
    RpcResult<List<OrgPostInfoApiDTO>> listByPostIds(List<Long> postIds);

    /**
     * 通过层级关系获取
     *
     * @param parentId
     * @return
     */
    RpcResult<List<EntDeptInfoByLevelApiDTO>> listByParentId(Long parentId);

    /**
     * 通过国家获取区号信息
     */
    RpcResult<CountryConfigApiDTO> getCountryConfig(CountryConfigApiDTO dto);


    /**
     * 获取HERMES可见的待处理操作记录列表
     *
     * @return List<EntOcOperationRecordDTO>
     */
    RpcResult<List<EntOcOperationRecordDTO>> listUnhandledOperationRecord4Hermes();

    /**
     * 国家获取区域
     */
    RpcResult<List<DeptRegionInfoApiDTO>> getRegion(String country);

    RpcResult<List<EntDeptInfoByLevelApiDTO>> getOneDeptForPms(Integer isFindTwo);


    /**
     * 根据id获取部门
     *
     * @param deptIdList
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getDeptByIdList(List<Long> deptIdList);

    /**
     * 根据网点code获取网点
     *
     * @param ocCodeList
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> getStationByCodeList(List<String> ocCodeList);

    /**
     * 根据项目code获取项目信息
     *
     * @param projectCodeList
     * @return
     */
    RpcResult<List<OrgProjectDTO>> getProjectByCodeList(List<String> projectCodeList);


    /**
     * 获取全部启用岗位信息
     *
     * @return RpcResult<List < OrgPostInfoApiDTO>>
     */
    RpcResult<List<OrgPostInfoApiDTO>> getPostList();


    /**
     * 岗位id获取岗位信息
     *
     * @param postName
     * @return
     */
    RpcResult<List<OrgPostInfoApiDTO>> getByPostName(String postName);

    /**
     * 岗位查询分页
     *
     * @param postApiQuery postApiQuery
     * @return RpcResult<PaginationResult < OrgPostInfoApiDTO>>
     */
    RpcResult<PaginationResult<EntPostApiDTO>> getPostPage(PostApiQuery postApiQuery);


    /**
     * 查询用户基本信息
     *
     * @param selectUserInfoApiQuery selectUserInfoApiQuery
     * @return RpcResult<List < UserInfoApiDTO>>
     */
    RpcResult<List<UserInfoApiDTO>> selectUserInfoList(SelectUserInfoApiQuery selectUserInfoApiQuery);

    /**
     * 查询用户基本信息
     */
    RpcResult<PaginationResult<UserInfoApiDTO>> selectUserInfoPage(SelectUserInfoApiQuery selectUserInfoApiQuery);


    /**
     * 查询所有的部门
     * @return
     */
    RpcResult<List<OrgDeptApiDTO>> selectAllDept();

}
