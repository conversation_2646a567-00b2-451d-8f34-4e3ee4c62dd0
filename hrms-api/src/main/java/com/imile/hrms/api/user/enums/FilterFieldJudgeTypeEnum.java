package com.imile.hrms.api.user.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/18
 */
@Getter
public enum FilterFieldJudgeTypeEnum {

    /**
     * 过滤字段判断类型枚举
     */
    UNKNOWN(0, "未知"),
    EQUAL(1, "等于"),
    NOT_EQUAL(2, "不等于"),
    /**
     * 模糊查询（仅支持UserFilterFieldEnum.KEYWORD）
     */
    LIKE(3, "模糊查询"),
    ;

    private final Integer type;

    private final String desc;

    FilterFieldJudgeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static FilterFieldJudgeTypeEnum valueOfType(Integer type) {
        for (FilterFieldJudgeTypeEnum typeEnum : FilterFieldJudgeTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return UNKNOWN;
    }
}
