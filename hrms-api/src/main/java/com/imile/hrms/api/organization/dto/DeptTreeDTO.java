package com.imile.hrms.api.organization.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptTreeDTO implements Serializable {

    private static final long serialVersionUID = 7252125051486997385L;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 上级部门编码
     */
    private String parentDeptCode;

    /**
     * 部门级别
     */
    private Integer deptLevel;

    /**
     * 部门状态
     */
    private String deptStatus;

    /**
     * 下级部门列表
     */
    private List<DeptTreeDTO> childrenList;
}
