package com.imile.hrms.api.user.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBaseInfoDTO implements Serializable {


    private static final long serialVersionUID = -6501660108525516092L;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员名称
     */
    private String userName;

    /**
     * 是否司机（0:否 1:是）
     */
    private Integer isDriver;

    /**
     * 用工类型
     *
     * @see com.imile.hrms.api.primary.enums.EmploymentTypeEnum
     */
    private String employeeType;

    /**
     * 账号状态
     *
     * @see com.imile.hrms.api.primary.enums.CommonStatusEnum
     */
    private String status;

    /**
     * 工作状态
     *
     * @see com.imile.hrms.common.enums.WorkStatusEnum
     */
    private String workStatus;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;
}
