package com.imile.hrms.api.user.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AchievementResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 当前绩效
     */
    private String currentRate;
    /**
     * 上次绩效
     */
    private String previousRate;
    /**
     * 当前年度
     */
    private String currentYear;
    /**
     * 上次年度
     */
    private String previousYear;
    /**
     * 当前周期
     */
    private String currentCycleType;
    /**
     * 上次周期
     */
    private String previousCycleType;

}
