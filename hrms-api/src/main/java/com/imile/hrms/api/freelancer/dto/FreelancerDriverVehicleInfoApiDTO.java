package com.imile.hrms.api.freelancer.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class FreelancerDriverVehicleInfoApiDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 类型
     */
    private String type;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 车辆型号
     */
    private String vehicleModel;

    /**
     * 最大承载重量KG
     */
    private BigDecimal maxLoadWeight;

    /**
     * 最大承载体积m³
     */
    private BigDecimal maxLoadVolume;

    /**
     * 最大包裹数
     */
    private Integer maxPackageCount;

    /**
     * 每天工作时长
     */
    private Integer dailyWorkingHours;

    /**
     * 工作时间窗
     */
    private List<String> availableTimePeriod;

    /**
     * 派送日期--每周几
     */
    private List<String> deliveryDate;

    /**
     * 派送区域
     */
    private List<FreelancerDriverDeliveryPreferenceZoneApiDTO> preferredDeliveryArea;

    /**
     * 工作开始时间
     */
    private String workTimeStart;

    /**
     * 工作结束时间
     */
    private String workTimeEnd;

    /**
     * 休息时长 (单位：秒)
     */
    private Integer breakTime;


}
