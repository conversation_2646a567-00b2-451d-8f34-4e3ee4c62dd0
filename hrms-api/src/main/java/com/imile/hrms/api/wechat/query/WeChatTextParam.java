package com.imile.hrms.api.wechat.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/1
 */
@Data
@Builder
public class WeChatTextParam implements Serializable {

    private static final long serialVersionUID = -509435391372474738L;

    /**
     * 成员ID列表
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private List<String> userCodes;

    /**
     * 描述
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String content;

    private String contentEn;

    // 防止别人报错
    public WeChatTextParam() {

    }

    public WeChatTextParam(List<String> userCodes, String content, String contentEn) {
        this.userCodes = userCodes;
        this.content = content;
        this.contentEn = contentEn;
    }

}
