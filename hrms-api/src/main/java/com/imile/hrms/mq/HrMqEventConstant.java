package com.imile.hrms.mq;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-10-8
 * @version: 1.0
 */
public class HrMqEventConstant {

    /**
     * 修改考勤日历
     */
    public static final String ATTENDANCE_UPDATE_TAG = "attendanceUpdateTag";


    /**
     * 修改打卡配置
     */
    public static final String PUNCH_UPDATE_TAG = "punchUpdateTag";


    /**
     * 人员变动修改考勤及打卡相关信息
     */
    public static final String ATTENDANCE_AND_PUNCH_UPDATE_TAG = "attendanceAndPunchUpdateTag";

    /**
     * 离职发送MQ给BPM的tag
     */
    @Deprecated
    public static final String BPM_USER_DIMISSION_TAG = "DIMISSION";

    /**
     * 众包司机审核
     */
    public static final String FREELANCER_DRIVER_REVIEW = "freelancerDriverReview";

    /**
     * 人员数据同步中控
     */
    public static final String SYNC_EMPLOYEE_DRIVER_TO_ZK_TAG = "syncEmployeeDriverToZk";

    /**
     * 同步部门至下游系统（hermes、企微、金蝶）
     */
    public static final String SYNC_DEPT_TO_DOWNSTREAM = "syncDeptToDownstream";

    /**
     * 同步员工信息至ucenter
     */
    public static final String SYNC_USER_INFO_TO_UCENTER = "syncUserInfoToUcenter";

    /**
     * 岗位信息变更
     */
    public static final String POST_INFO_CHANGE = "postInfoChange";

    /**
     * 职位族类信息变更
     */
    public static final String POST_FAMILY_INFO_CHANGE = "postFamilyInfoChange";
}
