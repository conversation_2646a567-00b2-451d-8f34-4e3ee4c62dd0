package com.imile.hrms.aop;

import com.alibaba.fastjson.JSON;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.log.factory.LoggerFactory;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Logger;

@Component
@Aspect
@Order(1)
@Slf4j
public class ForbidRepeatClickInterceptor {

    @Autowired
    private ImileRedisClient imileRedisClient;

    @Pointcut("@annotation(com.imile.hrms.common.annotation.ForbidRepeatClick)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object forbidRepeatClick(ProceedingJoinPoint pjp) throws Throwable {
        //1、根据入参方法名获取组装的redis的key值
        String redisKey = getRedisKey(pjp);
        log.info("ForbidRepeatClickInterceptor->forbidRepeatClick->redisKey:{}", redisKey);

        if (imileRedisClient.tryLock(redisKey, 2)) {
            log.info("ForbidRepeatClickInterceptor->forbidRepeatClick->redisKey:notexist");
            //2、当前方法同一时间段无完全同参数调用，则继续往下执行
            Object res = pjp.proceed();
            //2.1 执行后将数据从redis删除
            //imileRedisClient.delete(redisKey);
            return res;
        }

        //3、当前方法同一时间段具有相同参数执行，则不再执行，直接返回错误标识
        log.info("ForbidRepeatClickInterceptor->forbidRepeatClick->redisKey:exist");
        Result commonResponse = new Result();
        commonResponse.setMessage("Do not click repeatedly");
        return commonResponse;
    }

    /**
     * 获取存储的redis的key值
     *
     * @param pjp
     * @return
     */
    private String getRedisKey(ProceedingJoinPoint pjp) {

        // 1、获取被代理的对象类型
        String className = pjp.getTarget().getClass().getName();

        // 2、获取当前代理的方法名
        String methodName = pjp.getSignature().getName();

        // 3、获取入参并转换成jason串
        String convertJson = convertArgsToJson(pjp.getArgs());

        String redisKey = className + "->" + methodName + "->" + convertJson;

        return redisKey;
    }

    /**
     * 将传入的参数拼接成json类型的字符串
     *
     * @param args
     * @return
     */
    private String convertArgsToJson(Object[] args) {
        StringBuilder convertJson = new StringBuilder();
        for (Object object : args) {
            if (!(object instanceof HttpServletRequest)) {  // 此处判断不能舍去
                convertJson.append(JSON.toJSONString(object));
            }
        }
        return convertJson.toString();
    }
}

