package com.imile.hrms.web.user.vo;

import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserInfoByUserIdsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 业务国家编码
     */
    private String country;
    /**
     * 员工姓名
     */
    private String userName;
    /**
     * 员工英文名
     */
    private String userNameEn;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 平台映射id 企业微信
     */
    private String relationId;

    /**
     * 员工头像地址
     */
    @HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;

    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;

    /**
     * 岗位信息
     */
    private Long postId;


    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 部门
     */
    private Long deptId;
    /**
     * 网点
     */
    private Long ocId;

}
