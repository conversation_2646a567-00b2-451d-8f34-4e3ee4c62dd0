package com.imile.hrms.web.user.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * H5页面VO类
 * <AUTHOR>
 */
@Data
public class UserInfoExpandNoLoginVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;

    private Integer isDriver;
    /**
     * 员工基础信息
     */
    private BaseUserInfoVO userInfo;
    /**
     * 员工扩展信息
     */
    private UserExtendInfoVO userExtendInfo;
    /**
     * 员工支付信息
     */
    private List<HrmsUserExtendPayInfoVO> userExtendPayInfos;
    /**
     * 员工证件信息
     */
    private List<UserCertificateInfoVO> userCertificateInfos;
    /**
     * 员工学历信息
     */
    private List<UserEducationInfoVO> userEducationInfos;



}
