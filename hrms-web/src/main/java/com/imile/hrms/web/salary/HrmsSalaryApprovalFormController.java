package com.imile.hrms.web.salary;


import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.salary.query.HrmsSalaryItemExportQuery;
import com.imile.hrms.dao.salary.query.HrmsSalaryItemRelationExportQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.approval.param.ApplicationFormCancelParam;
import com.imile.hrms.service.approval.param.ApplicationFormDeleteParam;
import com.imile.hrms.service.approval.param.ApplicationFormDetailParam;
import com.imile.hrms.service.approval.vo.ApprovalResultVO;
import com.imile.hrms.service.salary.HrmsEmployeeSalaryInfoService;
import com.imile.hrms.service.salary.HrmsSalaryApprovalFormService;
import com.imile.hrms.service.salary.param.SalaryAddParam;
import com.imile.hrms.service.salary.param.SalaryApplyCountryCurrencyParam;
import com.imile.hrms.service.salary.param.SalaryApplyCountryParam;
import com.imile.hrms.service.salary.param.SalaryCostOrgInfoParam;
import com.imile.hrms.service.salary.param.SalaryFormInfoParam;
import com.imile.hrms.service.salary.param.SalaryFormTableParam;
import com.imile.hrms.service.salary.param.SalaryFormTitleInfoParam;
import com.imile.hrms.service.salary.param.SalaryFormUserParam;
import com.imile.hrms.service.salary.vo.HrmsSalaryItemExportVO;
import com.imile.hrms.service.salary.vo.HrmsSalaryItemRelationExportVO;
import com.imile.hrms.service.salary.vo.SalaryCostOrgInfoVO;
import com.imile.hrms.service.salary.vo.SalaryCurrencyPrecisionVO;
import com.imile.hrms.service.salary.vo.SalaryEmployeeDetailInfoUserListVO;
import com.imile.hrms.service.salary.vo.SalaryEmployeeDetailInfoVO;
import com.imile.hrms.service.salary.vo.SalaryFormInfoVO;
import com.imile.hrms.service.salary.vo.SalaryFormTableVO;
import com.imile.hrms.service.salary.vo.SalaryFormTitleInfoVO;
import com.imile.hrms.service.salary.vo.SalaryFromDetailVO;
import com.imile.hrms.web.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 薪资申请单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Slf4j
@RestController
@RequestMapping("/salary/approval")
public class HrmsSalaryApprovalFormController extends BaseController {

    @Autowired
    private HrmsSalaryApprovalFormService hrmsSalaryApprovalFormService;
    @Autowired
    private ImileRedisClient redissonClient;
    @Autowired
    private HrmsEmployeeSalaryInfoService hrmsEmployeeSalaryInfoService;
    @Autowired
    private ConverterService converterService;

    private static final String SYNC_LOCK = "HRMS:LOCK:SALARY_SYNC:";

    /**
     * 薪资申请(点击新增按钮调用 新增/暂存)
     */
    @PostMapping("/add")
    public Result<ApprovalResultVO> salaryAdd(@RequestBody @Validated SalaryAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO = new ApprovalResultVO();
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = hrmsSalaryApprovalFormService.salaryAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 薪资申请(暂存后更新/驳回后更新)
     */
    @PostMapping("/update")
    public Result<ApprovalResultVO> salaryUpdate(@RequestBody @Validated SalaryAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO = new ApprovalResultVO();
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = hrmsSalaryApprovalFormService.salaryUpdate(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 薪资申请预览
     */
    @PostMapping("/preview")
    public Result<List<ApprovalDetailStepRecordDTO>> salaryPreview(@RequestBody @Validated SalaryAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = hrmsSalaryApprovalFormService.salaryPreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 取消
     */
    @PostMapping("/cancel")
    public Result<Boolean> cancel(@RequestBody @Validated ApplicationFormCancelParam param) {
        hrmsSalaryApprovalFormService.cancel(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 详情
     */
    @PostMapping("/detail")
    public Result<SalaryFromDetailVO> detail(@RequestBody @Validated ApplicationFormDetailParam param) {
        SalaryFromDetailVO detail = hrmsSalaryApprovalFormService.getFromDetail(param.getFormId());
        converterService.withAnnotationForSingle(detail);
        return Result.ok(detail);
    }

    /**
     * 获取发薪人员
     */
    @PostMapping("/user/list")
    public Result<SalaryEmployeeDetailInfoUserListVO> userList(@RequestBody @Validated SalaryFormUserParam param) {
        return Result.ok(hrmsEmployeeSalaryInfoService.userFormList(param));
    }

    /**
     * 获取标头信息
     */
    @PostMapping("/title/info")
    public Result<List<SalaryFormTitleInfoVO>> titleInfoList(@RequestBody @Validated SalaryFormTitleInfoParam param) {
        List<SalaryFormTitleInfoVO> salaryFormUserVOList = hrmsSalaryApprovalFormService.titleInfoList(param);
        return Result.ok(salaryFormUserVOList);
    }


    /**
     * 列表查询
     */
    @PostMapping("/list")
    public Result<PaginationResult<SalaryFormInfoVO>> list(@RequestBody @Validated SalaryFormInfoParam param) {
        PaginationResult<SalaryFormInfoVO> resultVO = hrmsSalaryApprovalFormService.list(param);
        return Result.ok(resultVO);
    }


    /**
     * 费用承担组织获取
     */
    @PostMapping("/cost/org/list")
    public Result<List<SalaryCostOrgInfoVO>> costOrgInfoList(@RequestBody @Validated SalaryCostOrgInfoParam param) {
        List<SalaryCostOrgInfoVO> infoVOS = hrmsSalaryApprovalFormService.costOrgInfoList(param);
        return Result.ok(infoVOS);
    }

    /**
     * 算薪国家（申请国）获取
     */
    @PostMapping("/apply/country/list")
    public Result<List<String>> applyCountryList(@RequestBody @Validated SalaryApplyCountryParam param) {
        List<String> infoVOS = hrmsSalaryApprovalFormService.applyCountryList(param);
        return Result.ok(infoVOS);
    }


    /**
     * 费用项/费用部门表格获取
     */
    @PostMapping("/salary/form/table/info")
    public Result<SalaryFormTableVO> salaryFormTableInfo(@RequestBody @Validated SalaryFormTableParam param) {
        SalaryFormTableVO tableVO = hrmsSalaryApprovalFormService.salaryFormTableInfo(param);
        return Result.ok(tableVO);
    }

    /**
     * 算薪国家下币种获取
     */
    @PostMapping("/apply/country/currency/list")
    public Result<List<SalaryCurrencyPrecisionVO>> applyCountryCurrencyList(@RequestBody @Validated SalaryApplyCountryCurrencyParam param) {
        List<SalaryCurrencyPrecisionVO> infoVOS = hrmsSalaryApprovalFormService.applyCountryCurrencyList(param);
        return Result.ok(infoVOS);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @Validated ApplicationFormDeleteParam param) {
        hrmsSalaryApprovalFormService.delete(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 费用项导出
     */
    @PostMapping("list/export")
    public Result<PaginationResult<HrmsSalaryItemExportVO>> itemListExport(HttpServletRequest request, HrmsSalaryItemExportQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<HrmsSalaryItemExportVO> res = hrmsSalaryApprovalFormService.itemListExport(query);
        return Result.ok(res);
    }

    /**
     * 费用项&增减项映射导出
     */
    @PostMapping("item/relation/export")
    public Result<PaginationResult<HrmsSalaryItemRelationExportVO>> itemRelationExport(HttpServletRequest request, HrmsSalaryItemRelationExportQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<HrmsSalaryItemRelationExportVO> res = hrmsSalaryApprovalFormService.itemRelationExport(query);
        return Result.ok(res);
    }
}
