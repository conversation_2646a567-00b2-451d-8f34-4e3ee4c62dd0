package com.imile.hrms.web.approval.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.approval.dto.OverTimeApprovalListDTO;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.approval.HrmsApprovalFormService;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.approval.param.ApplicationFormCancelParam;
import com.imile.hrms.service.approval.param.ApplicationFormDeleteParam;
import com.imile.hrms.service.approval.param.OverTimeAddParam;
import com.imile.hrms.service.approval.param.OverTimeCalcParam;
import com.imile.hrms.service.approval.param.OverTimeDetailParam;
import com.imile.hrms.service.approval.param.OverTimeImportAddListParam;
import com.imile.hrms.service.approval.param.OverTimeListParam;
import com.imile.hrms.service.approval.vo.ApprovalResultListVO;
import com.imile.hrms.service.approval.vo.ApprovalResultVO;
import com.imile.hrms.service.approval.vo.OverTimeApprovalCloverListVO;
import com.imile.hrms.service.approval.vo.OverTimeApprovalFromListVO;
import com.imile.hrms.service.approval.vo.OverTimeApprovalFromVO;
import com.imile.hrms.service.approval.vo.UserOverTimeImportVO;
import com.imile.hrms.web.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 加班审批单据主表(hrms.hrms_approval_form)表控制层
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/approval/form/overtime")
public class HrmsApprovalFormController extends BaseController {

    @Autowired
    private HrmsApprovalFormService hrmsApprovalFormService;
    @Autowired
    private ImileRedisClient redissonClient;
    @Autowired
    private ConverterService converterService;
    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_OVERTIME_SYNC:";


    /**
     * 加班申请(点击新增按钮调用 新增/暂存)
     */
    @PostMapping("/add")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalResultVO> overTimeAdd(@RequestBody @Validated OverTimeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO = new ApprovalResultVO();
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = hrmsApprovalFormService.overTimeAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 加班申请(暂存后更新/驳回后更新)
     */
    @PostMapping("/update")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalResultVO> overTimeUpdate(@RequestBody @Validated OverTimeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO = new ApprovalResultVO();
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = hrmsApprovalFormService.overTimeUpdate(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 详情
     */
    @PostMapping("/detail")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<OverTimeApprovalFromVO> detail(@RequestBody OverTimeDetailParam param) {
        OverTimeApprovalFromVO detail = hrmsApprovalFormService.getApprovalFromDetail(param.getApprovalFormId());
        return Result.ok(detail);
    }

    /**
     * 加班列表
     */
    @PostMapping("/list")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<PaginationResult<OverTimeApprovalFromListVO>> list(@RequestBody OverTimeListParam param) {
        PaginationResult<OverTimeApprovalFromListVO> detail = hrmsApprovalFormService.list(param);
        // 处理注解
        converterService.withAnnotation(detail.getResults());
        return Result.ok(detail);
    }

    /**
     * 加班列表
     */
    @PostMapping("/clover/list")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<PaginationResult<OverTimeApprovalCloverListVO>> cloverList(@RequestBody OverTimeListParam param) {
        PaginationResult<OverTimeApprovalListDTO> listDTO = hrmsApprovalFormService.cloverList(param);
        PaginationResult<OverTimeApprovalCloverListVO> listVO = this.convertPage(listDTO, OverTimeApprovalCloverListVO.class);

        return Result.ok(listVO);
    }

    /**
     * 加班数据导入
     */
    @PostMapping("/import")
    public Result<List<UserOverTimeImportVO>> overTimeImport(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);

        log.info("------->import over time jobId:{}", callBackParam.getJobId());

        long startTime = System.currentTimeMillis();

        List<UserOverTimeImportVO> importoverTimeList = JSON.parseArray(callBackParam.getPageData(), UserOverTimeImportVO.class);
        if (ObjectUtil.isEmpty(importoverTimeList)) {
            return Result.ok(Collections.emptyList());
        }
        // 将开始时间、结束时间、时长 转换为对应类型
        importoverTimeList.forEach(userOverTimeImportVO -> {
            if (ObjectUtil.isNotEmpty(userOverTimeImportVO.getOverTimeStartDateString())) {
                DateTime startDate = DateUtil.parse(userOverTimeImportVO.getOverTimeStartDateString(), DatePattern.NORM_DATETIME_PATTERN);
                userOverTimeImportVO.setOverTimeStartDate(startDate);
            }
            if (ObjectUtil.isNotEmpty(userOverTimeImportVO.getOverTimeEndDateString())) {
                DateTime endDate = DateUtil.parse(userOverTimeImportVO.getOverTimeEndDateString(), DatePattern.NORM_DATETIME_PATTERN);
                userOverTimeImportVO.setOverTimeEndDate(endDate);
            }
            if (ObjectUtil.isNotEmpty(userOverTimeImportVO.getOverTimeDurationString())) {
                BigDecimal spendTime = new BigDecimal(userOverTimeImportVO.getOverTimeDurationString());
                userOverTimeImportVO.setOverTimeDuration(spendTime);
            }
        });

        List<UserOverTimeImportVO> allImportList = hrmsApprovalFormService.overTimeImportImport(importoverTimeList);

        log.info(" ------------ import over time,spendTime:{}ms --------------", System.currentTimeMillis() - startTime);

        return Result.ok(allImportList);
    }

    /**
     * 加班申请(点击新增按钮调用 新增/暂存)
     */
    @PostMapping("/import/add")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalResultListVO> overTimeImportAdd(@RequestBody @Validated OverTimeImportAddListParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultListVO resultVO = new ApprovalResultListVO();
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = hrmsApprovalFormService.overTimeImportAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }


    /**
     * 取消
     */
    @PostMapping("/cancel")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> cancel(@RequestBody @Validated ApplicationFormCancelParam param) {
        hrmsApprovalFormService.cancel(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> delete(@RequestBody @Validated ApplicationFormDeleteParam param) {
        hrmsApprovalFormService.delete(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 加班申请预览
     */
    @PostMapping("/preview")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> preview(@RequestBody @Validated OverTimeAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = hrmsApprovalFormService.overtimePreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 加班申请预计加班时长计算
     */
    @PostMapping("/calc")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<BigDecimal> calc(@RequestBody @Validated OverTimeCalcParam param) {
        BigDecimal time = hrmsApprovalFormService.overtimeCalc(param);
        return Result.ok(time);
    }

    /**
     * 校验登录用户常驻国是否包含加班权限
     */
    @GetMapping("/checkLoginUserAuth")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> checkLoginUserAuth() {
        Boolean isHaveOverTimeAuth = hrmsApprovalFormService.checkUserOverTimeAuth();
        return Result.ok(isHaveOverTimeAuth);
    }
}
