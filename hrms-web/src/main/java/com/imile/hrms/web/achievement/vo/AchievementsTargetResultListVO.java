package com.imile.hrms.web.achievement.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 指标结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
public class AchievementsTargetResultListVO {

    private static final long serialVersionUID = 1L;


    /**
     * 目标明细id
     */
    private Long targetItemId;


    /**
     * 备注
     */
    private String remark;

    /**
     * 完成值
     */
    private BigDecimal completionValue;

    /**
     * 完成率
     */
    private String completionRate;

    /**
     * 上一次完成率
     */
    private String lastCompletionRate;

    /**
     * 上一次创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCreateDate;

    /**
     * 上一次完成值
     */
    private BigDecimal lastCompletionValue;



}
