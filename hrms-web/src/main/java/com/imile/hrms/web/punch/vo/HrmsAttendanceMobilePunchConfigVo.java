package com.imile.hrms.web.punch.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.imile.hrms.common.annotation.OutWithTimeZone;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 移动打卡规则VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
public class HrmsAttendanceMobilePunchConfigVo implements Serializable {


    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     * 前端不展示这个字段
     */
    private String punchConfigType;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     * 前端展示这个字段
     */
    private String punchConfigTypeDesc;

    /**
     * 打卡方式
     */
    private String punchCardType;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建日期
     */
    @OutWithTimeZone
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改日期
     */
    @OutWithTimeZone
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdDate;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 最大补卡天数
     */
    private Long maxRepunchDays;

    /**
     * 最大补卡次数
     */
    private Long maxRepunchNumber;

    /**
     * 主负责人
     */
    private String principalUserCode;

    /**
     * 子负责人
     */
    private String subUserCodes;
}
