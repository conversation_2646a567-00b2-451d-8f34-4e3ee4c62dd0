package com.imile.hrms.web.salary;

import com.alibaba.fastjson.JSON;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.dao.salary.param.HrmsSalaryVendorInfoParam;
import com.imile.hrms.dao.salary.query.SalaryUserDetailQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.salary.HrmsEmployeeSalaryInfoService;
import com.imile.hrms.service.salary.param.HrmsSalaryVendorBankInfoParam;
import com.imile.hrms.service.salary.param.SalaryEmployeeInfoUpdateParam;
import com.imile.hrms.service.salary.param.SalaryUserExportParam;
import com.imile.hrms.service.salary.param.SalaryUserInfoExportParam;
import com.imile.hrms.service.salary.vo.HrmsSalaryVendorBankInfoVO;
import com.imile.hrms.service.salary.vo.HrmsSalaryVendorInfoVO;
import com.imile.hrms.service.salary.vo.SalaryEmployeeDetailInfoNoticeVO;
import com.imile.hrms.service.salary.vo.SalaryEmployeeDetailInfoVO;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.salary.param.HrmsEmployeeSalaryInfoDeleteParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/salary/info")
@Slf4j
public class HrmsEmployeeSalaryInfoController extends BaseController {

    @Autowired
    private HrmsEmployeeSalaryInfoService employeeSalaryInfoService;

    @Autowired
    private ConverterService converterService;

    /**
     * 员工薪资导入模版生成
     */
    @PostMapping("/user/title/export")
    public Result<String> salaryUserInfoTitleExport(@RequestBody @Validated SalaryUserInfoExportParam param) {
        String url = employeeSalaryInfoService.salaryUserInfoTitleExport(param);
        return Result.ok(url);
    }

    /**
     * 员工薪资导入
     */
    @PostMapping("/import")
    public Result<List<Map<String, String>>> importEmployeeSalary(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        // 数据解析
        log.info("callBackParam:{}", JSON.toJSONString(callBackParam));
        List<Map<String, String>> failMap = employeeSalaryInfoService.importEmployeeSalary(callBackParam.getPageData());
        return Result.ok(failMap);
    }

    /**
     * 员工薪资导出(员工明细查询导出)
     */
    @PostMapping("/export")
    public Result<PaginationResult<Map<String, String>>> export(HttpServletRequest request, SalaryUserDetailQuery query) {
        setExcelCallBackParam(request, query);
        query.setShowCount(50000);
        PaginationResult<Map<String, String>> mapPaginationResult = employeeSalaryInfoService.export(query);
        return Result.ok(mapPaginationResult);
    }

    /**
     * 审批员工薪资导出(2个地方，审批单据中员工信息导出，发起审批时选择人员后导出)
     */
    @PostMapping("/salary/user/export")
    public Result<PaginationResult<Map<String, String>>> salaryUserExport(HttpServletRequest request, SalaryUserExportParam query) {
        log.info("salaryUserExport | SalaryUserExportParam:{}", JSON.toJSON(query));
        setExcelCallBackParam(request, query);
        query.setShowCount(50000);
        PaginationResult<Map<String, String>> mapPaginationResult = employeeSalaryInfoService.salaryUserExport(query);
        return Result.ok(mapPaginationResult);
    }

    /**
     * 删除员工薪资数据
     *
     * @param param
     */
    @PostMapping("/delete")
    public Result<Boolean> deleteDataByIds(@RequestBody HrmsEmployeeSalaryInfoDeleteParam param) {
        return Result.ok(employeeSalaryInfoService.deleteDataByIds(param.getIds()));
    }

    /**
     * 列表查询
     */
    @PostMapping("list")
    public Result<PaginationResult<SalaryEmployeeDetailInfoVO>> list(@RequestBody @Validated SalaryUserDetailQuery query) {
        PaginationResult<SalaryEmployeeDetailInfoVO> pageResult = employeeSalaryInfoService.salaryUserInfoList(query);
        return Result.ok(pageResult);
    }

    /**
     * 列表查询
     */
    @PostMapping("/notice")
    public Result<SalaryEmployeeDetailInfoNoticeVO> notice(@RequestBody @Validated SalaryUserDetailQuery query) {
        query.setShowCount(50000);
        return Result.ok(employeeSalaryInfoService.notice(query));
    }


    /**
     * 更新
     */
    @PostMapping("update")
    public Result<Boolean> update(@RequestBody @Validated SalaryEmployeeInfoUpdateParam param) {
        employeeSalaryInfoService.update(param);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 供应商薪资付款--供应商下拉列表
     */
    @PostMapping("/vendorList")
    public Result<List<HrmsSalaryVendorInfoVO>> vendorList(@RequestBody @Validated HrmsSalaryVendorInfoParam param) {
        return Result.ok(employeeSalaryInfoService.vendorList(param));
    }

    /**
     * 供应商薪资付款--供应商开户银行下拉列表
     */
    @PostMapping("/vendorBankList")
    public Result<List<HrmsSalaryVendorBankInfoVO>> vendorBankList(@RequestBody @Validated HrmsSalaryVendorBankInfoParam param) {
        return Result.ok(employeeSalaryInfoService.vendorBankList(param));
    }


}
