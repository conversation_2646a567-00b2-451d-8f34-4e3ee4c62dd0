package com.imile.hrms.web.newAttendance.test;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigDateQuery;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarManageAdapter;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/11 
 * @Description
 */
@RestController
@RequestMapping("/calendar-manage")
@RequiredArgsConstructor
public class CalendarManageAdapterController {

    private final CalendarManageAdapter calendarManageAdapter;

    //==================HrmsAttendanceConfigManage适配=====================================

    @GetMapping("/isEnableNewModule")
    public Boolean isEnableNewModule() {
        return calendarManageAdapter.isEnableNewModule();
    }

    @GetMapping("/isDoubleWriteMode")
    public Boolean isDoubleWriteMode() {
        return calendarManageAdapter.isDoubleWriteMode();
    }

    @GetMapping("/getActiveById")
    public HrmsAttendanceConfigDO getActiveById(Long id) {
        return calendarManageAdapter.getActiveById(id);
    }

    @GetMapping("/getRangeRecords")
    public List<HrmsAttendanceConfigDetailDO> getRangeRecords(
            @RequestParam Long userId,
            @RequestParam Long year,
            @RequestParam String country) {
        return calendarManageAdapter.getRangeRecords(userId, year, country);
    }

    @GetMapping("/getRangeRecordsByMonth")
    public List<HrmsAttendanceConfigDetailDO> getRangeRecords(
            @RequestParam Long userId,
            @RequestParam Long year,
            @RequestParam Long month,
            @RequestParam String country) {
        return calendarManageAdapter.getRangeRecords(userId, year, month, country);
    }

    @GetMapping("/getHrmsAttendanceConfigDetailDOS")
    public List<HrmsAttendanceConfigDetailDO> getHrmsAttendanceConfigDetailDOS(
            @RequestParam Long userId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam String country) {
        return calendarManageAdapter.getHrmsAttendanceConfigDetailDOS(userId, startTime, endTime, country);
    }

    @PostMapping("/selectAttendanceConfigDetailDOS")
    public List<HrmsAttendanceConfigDetailDO> selectAttendanceConfigDetailDOS(
            @RequestBody List<Long> userIdList,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam String country) {
        return calendarManageAdapter.selectAttendanceConfigDetailDOS(userIdList, startTime, endTime, country);
    }

    @GetMapping("/getLatestAttendanceConfig")
    public HrmsAttendanceConfigDetailDO getLatestAttendanceConfig(
            @RequestParam Long userId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date punchTime) {
        return calendarManageAdapter.getLatestAttendanceConfig(userId, punchTime);
    }

    @PostMapping("/selectByIds")
    public List<HrmsAttendanceConfigDO> selectByIds(@RequestBody List<Long> ids) {
        return calendarManageAdapter.selectByIds(ids);
    }

    @PostMapping("/selectByCountryList")
    public List<HrmsAttendanceConfigDO> selectByCountryList(@RequestBody List<String> countryList) {
        return calendarManageAdapter.selectByCountryList(countryList);
    }

    @PostMapping("/selectLatestByIds")
    public List<HrmsAttendanceConfigDO> selectLatestByIds(@RequestBody List<Long> ids) {
        return calendarManageAdapter.selectLatestByIds(ids);
    }

//    @GetMapping("/getMobileLatestAttendanceConfig")
//    public HrmsAttendanceConfigDetailDO getMobileLatestAttendanceConfig(
//            @RequestParam Long userId,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date punchTime) {
//        return calendarManageAdapter.getMobileLatestAttendanceConfig(userId, punchTime);
//    }

    //==================HrmsAttendanceConfigDetailManage适配=====================================

    @PostMapping("/selectListByConfigIds")
    public List<HrmsAttendanceConfigDetailDO> selectListByConfigIds(@RequestBody List<Long> attendanceConfigList) {
        return calendarManageAdapter.selectListByConfigIds(attendanceConfigList);
    }

    @PostMapping("/selectListByConfigIdsAndYear")
    public List<HrmsAttendanceConfigDetailDO> selectListByConfigIdsAndYear(
            @RequestBody List<Long> attendanceConfigList,
            @RequestParam List<Integer> years) {
        return calendarManageAdapter.selectListByConfigIdsAndYear(attendanceConfigList, years);
    }

    //==================HrmsAttendanceConfigRangeManage适配=====================================

    @PostMapping("/selectConfigRange")
    public List<HrmsAttendanceConfigRangeDO> selectConfigRange(@RequestBody List<Long> bizIds) {
        return calendarManageAdapter.selectConfigRange(bizIds);
    }

    @PostMapping("/selectAttendanceConfigByDate")
    public List<HrmsAttendanceConfigRangeDO> selectAttendanceConfigByDate(@RequestBody AttendanceConfigDateQuery query) {
        return calendarManageAdapter.selectAttendanceConfigByDate(query);
    }

    @PostMapping("/selectAttendanceConfigByIds")
    public List<HrmsAttendanceConfigRangeDO> selectAttendanceConfigByIds(@RequestBody List<Long> configIdList) {
        return calendarManageAdapter.selectAttendanceConfigByIds(configIdList);
    }
}
