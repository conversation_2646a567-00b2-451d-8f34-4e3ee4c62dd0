package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class UserInfoListVO implements Serializable {
    private String id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 工号
     */
    private String workNo;
    /**
     * 所属国家名称
     */
    private String countryName;
    /**
     * 员工账号
     */
    private String userCode;
    /**
     * 公司邮箱
     */
    private String email;
    /**
     * 工作岗位名称
     */
    private String postName;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 职级名称
     */
    private String gradeType;
    /**
     * 职级数
     */
    private String gradeNo;
    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeTypeDesc;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 在职状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    @Deprecated
    private String workStatus;
    /**
     * 在职状态
     */
    @Deprecated
    private String workStatusDesc;

    /**
     * 原始在职状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_STATUS, ref = "originWorkStatusDesc")
    private String originWorkStatus;

    /**
     * 在职状态
     */
    private String originWorkStatusDesc;

    /**
     * 启用状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
    private String status;

    /**
     * 启用状态
     */
    private String statusDesc;


    /**
     * 所属网点名称
     */
    private String ocName;

    /**
     * 所属业务节点名称
     */
    private String bizModelName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 结算主体名称
     */
    private String settlementCenterName;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 所属国
     */
    private String originCountryName;
}
