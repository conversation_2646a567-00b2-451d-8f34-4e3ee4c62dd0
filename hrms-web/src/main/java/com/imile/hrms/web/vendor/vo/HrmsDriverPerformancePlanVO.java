package com.imile.hrms.web.vendor.vo;

import lombok.Data;

import java.util.Date;

/**
 * @author: milo
 * @createDate: 2022-10-20
 */
@Data
public class HrmsDriverPerformancePlanVO {

    /**
     * id
     */
    private Long id;

    /**
     * 历史记录id
     */
    private Long originalId;

    /**
     * 绩效方案名称
     */
    private String performancePlanName;

    /**
     * 适用供应商类型
     */
    private String applicableType;

    /**
     * 适用网点
     */
    private String branches;

    /**
     * 考核周期（月）
     */
    private String monthAssess;

    /**
     * 考核周期（周）
     */
    private String weekAssess;

    /**
     * 最近修改员工姓名
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    private Date lastUpdDate;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date failureTime;

}
