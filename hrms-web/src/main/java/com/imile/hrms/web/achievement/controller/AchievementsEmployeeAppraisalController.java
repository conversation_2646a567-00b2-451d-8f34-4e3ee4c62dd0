package com.imile.hrms.web.achievement.controller;


import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.ForbidRepeatClick;
import com.imile.hrms.common.annotation.NoAuthRequired;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.achievement.AchievementsEmployeeAppraisalEntrustService;
import com.imile.hrms.service.achievement.AchievementsEmployeeAppraisalOperationLogService;
import com.imile.hrms.service.achievement.AchievementsEmployeeAppraisalService;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalAppraiserAssignParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustCancelParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustCheckResultDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustCreateParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustHandleParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustPrepareCheckParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalExerciserAssignParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalListDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalOperationLogListDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalOperationLogQueryDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalQueryDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalRemindParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalSaveParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalTargetAuditParamDTO;
import com.imile.hrms.service.achievement.param.AchievementFinalEvaluationStageParam;
import com.imile.hrms.service.achievement.param.AchievementsEmployeeAppraisalAppraiserAssignDeptParam;
import com.imile.hrms.service.achievement.param.AchievementsEmployeeBathAssignDeptParam;
import com.imile.hrms.service.achievement.param.AchievementsSetAuditStageParam;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.achievement.convert.AchievementConvert;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalAppraiserAssignParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalEntrustCancelParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalEntrustCreateParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalEntrustHandleParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalEntrustPrepareCheckParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalExerciserAssignParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalOperationLogQuery;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalQuery;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalRemindParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalSaveParam;
import com.imile.hrms.web.achievement.param.AchievementsEmployeeAppraisalTargetAuditParam;
import com.imile.hrms.service.achievement.param.AchievementsSetSelfEvaluationStageParam;
import com.imile.hrms.web.achievement.vo.AchievementsEmployeeAppraisalEntrustCheckResultVO;
import com.imile.hrms.web.achievement.vo.AchievementsEmployeeAppraisalListVO;
import com.imile.hrms.web.achievement.vo.AchievementsEmployeeAppraisalOperationLogListVO;
import com.imile.util.BeanUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 员工考核
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/employee/appraisal")
public class AchievementsEmployeeAppraisalController extends BaseController {

    @Autowired
    private AchievementsEmployeeAppraisalService achievementsEmployeeAppraisalService;

    @Autowired
    private AchievementsEmployeeAppraisalEntrustService achievementsEmployeeAppraisalEntrustService;

    @Autowired
    private AchievementsEmployeeAppraisalOperationLogService achievementsEmployeeAppraisalOperationLogService;

    @Autowired
    private ConverterService converterService;
    @Autowired
    private AchievementConvert achievementConvert;

    /**
     * 获取考核关系列表
     *
     * @param query AchievementsEmployeeAppraisalQuery
     * @return PaginationResult<AchievementsEmployeeAppraisalListVO>
     */
    @PostMapping("/list")
    @NoAuthRequired
    public Result<PaginationResult<AchievementsEmployeeAppraisalListVO>> listPage(
            @RequestBody @Validated AchievementsEmployeeAppraisalQuery query) {
        AchievementsEmployeeAppraisalQueryDTO finalQuery
                = BeanUtils.convert(query, AchievementsEmployeeAppraisalQueryDTO.class);
        PaginationResult<AchievementsEmployeeAppraisalListDTO> pageList
                = achievementsEmployeeAppraisalService.listPage(finalQuery);
        PaginationResult<AchievementsEmployeeAppraisalListVO> result
                = super.convertPage(pageList, AchievementsEmployeeAppraisalListVO.class);
        // 带注解的字段填充
        result.setResults(converterService.withAnnotation(result.getResults()));
        achievementConvert.employeeAppraisalListEncrypt(result);
        return Result.ok(result);
    }

    /**
     * 设置考核关系
     *
     * @param param AchievementsEmployeeAppraisalSaveParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody @Validated AchievementsEmployeeAppraisalSaveParam param) {
        AchievementsEmployeeAppraisalSaveParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalSaveParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalService.save(finalParam));
    }

    /**
     * 设置行权人
     *
     * @param param AchievementsEmployeeAppraisalExerciserAssignParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/exerciser/assign")
    public Result<Boolean> assignExerciser(
            @RequestBody @Validated AchievementsEmployeeAppraisalExerciserAssignParam param) {
        AchievementsEmployeeAppraisalExerciserAssignParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalExerciserAssignParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalService.assignExerciser(finalParam));
    }

    /**
     * 考核委托预校验
     *
     * @param param AchievementsEmployeeAppraisalEntrustPrepareCheckParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/entrust/prepare/check")
    public Result<AchievementsEmployeeAppraisalEntrustCheckResultVO> prepareCheck(
            @RequestBody AchievementsEmployeeAppraisalEntrustPrepareCheckParam param) {
        AchievementsEmployeeAppraisalEntrustPrepareCheckParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalEntrustPrepareCheckParamDTO.class);
        AchievementsEmployeeAppraisalEntrustCheckResultDTO result
                = achievementsEmployeeAppraisalEntrustService.prepareCheck(finalParam);
        return Result.ok(BeanUtils.convert(result, AchievementsEmployeeAppraisalEntrustCheckResultVO.class));
    }

    /**
     * 发起考核委托
     *
     * @param param AchievementsEmployeeAppraisalEntrustCreateParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/entrust/create")
    public Result<Boolean> createEntrust(@RequestBody @Validated AchievementsEmployeeAppraisalEntrustCreateParam param) {
        AchievementsEmployeeAppraisalEntrustCreateParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalEntrustCreateParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalEntrustService.createEntrust(finalParam));
    }

    /**
     * 取消考核委托
     *
     * @param param AchievementsEmployeeAppraisalEntrustCancelParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/entrust/cancel")
    public Result<Boolean> cancelEntrust(@RequestBody @Validated AchievementsEmployeeAppraisalEntrustCancelParam param) {
        AchievementsEmployeeAppraisalEntrustCancelParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalEntrustCancelParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalEntrustService.cancelEntrust(finalParam));
    }

    /**
     * 处理考核委托
     *
     * @param param AchievementsEmployeeAppraisalEntrustHandleParam
     * @return Boolean
     */
    @NoAuthRequired
    @ForbidRepeatClick
    @PostMapping("/entrust/handle")
    public Result<Boolean> handleEntrust(@RequestBody @Validated AchievementsEmployeeAppraisalEntrustHandleParam param) {
        AchievementsEmployeeAppraisalEntrustHandleParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalEntrustHandleParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalEntrustService.handleEntrust(finalParam));
    }

    /**
     * 设置考核责任人
     *
     * @param param AchievementsEmployeeAppraisalAppraiserAssignParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/appraiser/assign")
    public Result<Boolean> assignAppraiser(@RequestBody @Validated AchievementsEmployeeAppraisalAppraiserAssignParam param) {
        AchievementsEmployeeAppraisalAppraiserAssignParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalAppraiserAssignParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalService.assignAppraiser(finalParam));
    }

    /**
     * 提醒
     *
     * @param param AchievementsEmployeeAppraisalRemindParam
     * @return Boolean
     */
    @ForbidRepeatClick
    @PostMapping("/remind")
    public Result<Boolean> remind(@RequestBody @Validated AchievementsEmployeeAppraisalRemindParam param) {
        AchievementsEmployeeAppraisalRemindParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalRemindParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalService.doRemind(finalParam));
    }

    /**
     * 审核目标
     *
     * @param param AchievementsEmployeeAppraisalTargetAuditParam
     * @return Boolean
     */
    @NoAuthRequired
    @ForbidRepeatClick
    @PostMapping("/target/audit")
    public Result<Boolean> auditTarget(@RequestBody @Validated AchievementsEmployeeAppraisalTargetAuditParam param) {
        AchievementsEmployeeAppraisalTargetAuditParamDTO finalParam
                = BeanUtils.convert(param, AchievementsEmployeeAppraisalTargetAuditParamDTO.class);
        return Result.ok(achievementsEmployeeAppraisalService.auditTarget(finalParam));
    }

    /**
     * 获取考核关系操作日志列表
     *
     * @param query AchievementsEmployeeAppraisalOperationLogQuery
     * @return List<AchievementsEmployeeAppraisalOperationLogListVO>
     */
    @NoAuthRequired
    @PostMapping("/log/list")
    public Result<List<AchievementsEmployeeAppraisalOperationLogListVO>> listOperationLog(
            @RequestBody @Validated AchievementsEmployeeAppraisalOperationLogQuery query) {
        AchievementsEmployeeAppraisalOperationLogQueryDTO finalQuery
                = BeanUtils.convert(query, AchievementsEmployeeAppraisalOperationLogQueryDTO.class);
        List<AchievementsEmployeeAppraisalOperationLogListDTO> operationLogList
                = achievementsEmployeeAppraisalOperationLogService.listOperationLog(finalQuery);
        return Result.ok(BeanUtils.convert(AchievementsEmployeeAppraisalOperationLogListVO.class, operationLogList));
    }

    /**
     * 导出考核关系  26050
     *
     * @param request HttpServletRequest
     * @param query   AchievementsEmployeeAppraisalQuery
     * @return PaginationResult<AchievementsEmployeeAppraisalListVO>
     */
    @NoAuthRequired
    @PostMapping("/export")
    public Result<PaginationResult<AchievementsEmployeeAppraisalListVO>> listExport(
            HttpServletRequest request, AchievementsEmployeeAppraisalQuery query) {
        setExcelCallBackParam(request, query);
        query.setShowCount(50000);
        AchievementsEmployeeAppraisalQueryDTO finalQuery
                = BeanUtils.convert(query, AchievementsEmployeeAppraisalQueryDTO.class);
        // 临时处理 List类型的传参无法直接转换 需要前端传String再分割
        if (StringUtils.isNotBlank(query.getDeptIds())) {
            finalQuery.setDeptIdList(Arrays.asList((Long[]) ConvertUtils.convert(query.getDeptIds().split(","), Long.class)));
        }
        if (StringUtils.isNotBlank(query.getPostIds())) {
            finalQuery.setPostIdList(Arrays.asList((Long[]) ConvertUtils.convert(query.getPostIds().split(","), Long.class)));
        }
        if (StringUtils.isNotBlank(query.getEmployeeTypes())) {
            finalQuery.setEmployeeTypeList(Arrays.asList(query.getEmployeeTypes().split(",")));
        }
        PaginationResult<AchievementsEmployeeAppraisalListDTO> pageList
                = achievementsEmployeeAppraisalService.listPage(finalQuery);
        PaginationResult<AchievementsEmployeeAppraisalListVO> result
                = super.convertPage(pageList, AchievementsEmployeeAppraisalListVO.class);
        // 带注解的字段填充
        result.setResults(converterService.withAnnotation(result.getResults()));
        return Result.ok(result);
    }

    /**
     * 设置进入审核阶段
     * @param param
     * @return
     */
    @PostMapping("/setAuditStage")
    public Result setAuditStage(@RequestBody AchievementsSetAuditStageParam param) {
        achievementsEmployeeAppraisalService.setAuditStage(param);
        return Result.ok();
    }

    /**
     * 设置考核责任人所属部门
     * @param param
     * @return
     */
    @PostMapping("/appraiser/assign/dept")
    public Result assignAppraiserDept(@RequestBody @Validated AchievementsEmployeeAppraisalAppraiserAssignDeptParam param) {
        achievementsEmployeeAppraisalService.assignAppraiserDept(param);
        return Result.ok();
    }
    /**
     * 设置进入自评环节
     * @param param
     * @return
     */
    @PostMapping("/setSelfEvaluationStage")
    public Result setSelfEvaluationStage(@RequestBody @Validated AchievementsSetSelfEvaluationStageParam param) {
        achievementsEmployeeAppraisalService.setSelfEvaluationStage(param);
        return Result.ok();
    }

    /**
     * 强制进入终评环节
     * @param param
     * @return
     */
    @PostMapping("/setFinalEvaluationStage")
    public Result setFinalEvaluationStage(@RequestBody AchievementFinalEvaluationStageParam param) {
        achievementsEmployeeAppraisalService.setFinalEvaluationStage(param);
        return Result.ok();
    }
    /**
     * 批量设置考核部门
     * @param param
     */
    @PostMapping("/batch/assign/dept")
    public Result batchAssignDept(@RequestBody AchievementsEmployeeBathAssignDeptParam param) {
        achievementsEmployeeAppraisalService.batchAssignDept(param);
        return Result.ok();
    }

    /**
     * 剩余人员强制进入终评环节
     * @param param
     * @return
     */
    @PostMapping("/setFinalEvaluationStage/force")
    public Result setFinalEvaluationStageForce(@RequestBody AchievementFinalEvaluationStageParam param) {
        achievementsEmployeeAppraisalService.setFinalEvaluationStageForce(param);
        return Result.ok();
    }

}
