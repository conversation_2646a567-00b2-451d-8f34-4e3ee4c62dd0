package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RegisterUserInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户英文名称
     */
    private String userNameEn;
    /**
     * 用户企业邮箱
     */
    private String email;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 个人邮箱(非公司邮箱)
     */
    private String personalEmail;

    /**
     * 计划入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 入职状态 入职状态：待发送邀请、待员工登记、待确认入职、已入职、已放弃入职
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ENTRY_STATUS, ref = "entryStatusDesc")
    private String entryStatus;

    private String entryStatusDesc;
    /**
     * 是否发送邮件
     */
    private Boolean isSendEmail;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 所属国
     */
    private String originCountry;
}
