package com.imile.hrms.web.training.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.Date;

@Data
public class HrmsUserTrainingRecordListVO extends ResourceQuery {
    private static final long serialVersionUID = 9172114589801759172L;

    private Long id;

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 所属部门id
     */
    private Long deptId;

    private String deptName;

    /**
     * 工作岗位
     */
    private Long postId;

    private String postName;

    /**
     * 培训日期
     */
    private Date date;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.TRAINING_USER_STATUS, ref = "statusDesc")
    private String status;

    private String statusDesc;

    /**
     * 培训主题
     */
    private String trainingTopic;

    /**
     * 培训类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.TRAINING_TYPE, ref = "trainingTypeDesc")
    private String trainingType;
    private String trainingTypeDesc;

    /**
     * 培训场次
     */
    private String trainingSession;

    /**
     * 培训讲师编码
     */
    private String trainerCode;

    /**
     * 培训讲师名称
     */
    private String trainerName;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

}
