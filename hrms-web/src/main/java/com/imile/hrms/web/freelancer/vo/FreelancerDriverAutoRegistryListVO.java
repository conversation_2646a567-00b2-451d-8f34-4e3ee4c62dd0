package com.imile.hrms.web.freelancer.vo;

import lombok.Data;

import java.util.Date;

/**
 * 众包司机信息
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverAutoRegistryListVO {
    /**
     * 审批单id
     */
    private Long approvalId;

    /**
     * 审批状态（审批流状态）
     */
    private String approvalStatus;

    /**
     * id
     */
    private Long id;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国家
     */
    private String country;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    private Date lastUpdDate;

}
