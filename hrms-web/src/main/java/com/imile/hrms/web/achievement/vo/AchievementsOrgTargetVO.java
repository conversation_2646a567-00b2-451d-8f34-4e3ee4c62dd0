package com.imile.hrms.web.achievement.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 组织目标
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsOrgTargetVO{


    private Long targetId;

    private Long id;

    /**
     * 活动id
     */
    private Long eventId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 状态 0 未提交 1 已提交 2 已确认结果
     */
    private String status;

    /**
     * 状态
     */
    private String statusDesc;

    /**
     * 子考核周期 默认0:无周期 1季度 2月度 3周
     */
    private Integer cycleType;

    /**
     * 子考核周期 默认0:无周期 1季度 2月度 3周
     */
    private String cycleTypeDesc;

    /**
     * 是否关联运营指标：默认0否 1是
     */
    private Integer isRelatedOperationalIndicators;

    /**
     * 运营指标范围
     */
    private String scopeOfOperationalIndicators;

    /**
     * 运营组织id
     */
    private Long ocId;

    /**
     * 运营组织id
     */
    private String ocCode;

    /**
     * 组织id的父级id
     */
    private Long parentId;

    /**
     * 总得分
     */
    private BigDecimal score;

    /**
     * 组织负责人
     */
    private String leaderName;

    /**
     * 部门名称
     */
    private String deptName;

}
