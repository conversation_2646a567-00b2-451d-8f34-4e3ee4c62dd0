package com.imile.hrms.web.salary;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.ExceptionConstant;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.StatusEnum;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.salary.*;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.dao.common.ConfigNoStatusSwitchParamDTO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.salary.dto.ConfigSelectDTO;
import com.imile.hrms.dao.salary.dto.SalaryConfigDTO;
import com.imile.hrms.dao.salary.dto.SalaryConfigDetailDTO;
import com.imile.hrms.dao.salary.param.*;
import com.imile.hrms.dao.salary.query.SalaryConfigQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.salary.HrmsSalaryConfigService;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.salary.vo.ConfigSelectVO;
import com.imile.hrms.web.salary.vo.CycleDetailSelectVO;
import com.imile.hrms.web.salary.vo.SalaryConfigDetailVO;
import com.imile.hrms.web.salary.vo.SalaryConfigVO;
import com.imile.util.json.JsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 计薪方案配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @menu 计薪方案配置管理
 * @since 2021-12-29
 */
@RestController
@RequestMapping("/salary/config")
public class HrmsSalaryConfigController extends BaseController {
    @Autowired
    private HrmsSalaryConfigService hrmsSalaryConfigService;
    @Autowired
    private ConverterService converterService;
    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;


    /**
     * 计薪方案配置列表查询
     *
     * @return
     * @status done
     */
    @PostMapping("/list")
    public Result<PaginationResult<SalaryConfigVO>> list(@RequestBody SalaryConfigQuery query) {
        query.setCountry(query.getCountry());
        PaginationResult<SalaryConfigDTO> res = hrmsSalaryConfigService.list(query);
        PaginationResult<SalaryConfigVO> attendanceConfigVOPaginationResult = this.convertPage(res, SalaryConfigVO.class);
        // 返回时区处理
        converterService.withAnnotation(attendanceConfigVOPaginationResult.getResults());
        return Result.ok(attendanceConfigVOPaginationResult);
    }

    /**
     * 计薪方案配置详情
     *
     * @param salaryConfigNo 计薪方案配置编码
     * @return
     * @status done
     */
    @PostMapping("/detail")
    public Result<SalaryConfigDetailVO> detail(@RequestParam String salaryConfigNo) {

        SalaryConfigDetailDTO salaryConfigDetailDTO = hrmsSalaryConfigService.detail(salaryConfigNo);
        SalaryConfigDetailVO salaryConfigDetailVO = HrmsCollectionUtils.convertSingle(salaryConfigDetailDTO, SalaryConfigDetailVO.class);
        converterService.withAnnotation(SalaryConfigDetailVO.class).accept(salaryConfigDetailVO);
        return Result.ok(salaryConfigDetailVO);
    }

    /**
     * 修改计薪方案
     *
     * @return
     * @status done
     */
    @DuplicateSubmit
    @PostMapping("/update")
    public Result<Boolean> update(@Validated(Groups.Update.class) @RequestBody SalaryConfigUpdateParam salaryConfigUpdateParam) {
        // 参数校验
        // 薪资组成：1.必须包含一条基础薪资，2，必须加起来等于100
        validateAddParam(salaryConfigUpdateParam);
        //  添加
        hrmsSalaryConfigService.update(salaryConfigUpdateParam);

        return Result.ok(Boolean.TRUE);
    }

    /**
     * 新增计薪方案
     *
     * @return
     * @status done
     */
    @DuplicateSubmit
    @PostMapping("/add")
    public Result<Boolean> add(@Validated(Groups.Add.class) @RequestBody SalaryConfigAddParam salaryConfigAddParam) {
        // 参数校验
        // 薪资组成：1.必须包含一条基础薪资，2，必须加起来等于100
        validateAddParam(salaryConfigAddParam);
        //  添加
        hrmsSalaryConfigService.add(salaryConfigAddParam);

        return Result.ok(Boolean.TRUE);
    }

    /**
     * 获取周期类型下拉数据
     *
     * @param cycleType 计薪周期类型 MONTH/ WEEK
     * @return
     * @status done
     */
    @PostMapping("/getCycleDetailList")
    public Result<List<CycleDetailSelectVO>> getCycleDetailList(String cycleType) {
        CycleTypeEnum cycleTypeEnum = CycleTypeEnum.getInstance(cycleType);
        BusinessLogicException.checkTrue(cycleTypeEnum == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "cycleType");
        return Result.ok(HrmsCollectionUtils.convert(cycleTypeEnum.getCycleDetailList(), CycleDetailSelectVO.class));
    }

    /**
     * 停启用状态修改
     *
     * @param statusSwitchParamDTO
     * @return
     * @status done
     */
    @RequestMapping(value = "/status/switch", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<Boolean> statusSwitch(@Validated(Groups.Update.class) @RequestBody ConfigNoStatusSwitchParamDTO statusSwitchParamDTO) {
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchParamDTO.getStatus());
        if (statusEnum == null) {
            return Result.fail(MsgCodeConstant.PARAM_INVALID, "status");
        }
        hrmsSalaryConfigService.statusSwitch(statusSwitchParamDTO);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 下拉列表
     *
     * @param query 状态
     * @return
     * @status done
     */
    @PostMapping("selectList")
    public Result<List<ConfigSelectVO>> selectList(SalaryConfigQuery query) {
        List<ConfigSelectDTO> configSelectDTOS = hrmsSalaryConfigService.selectList(query);
        return Result.ok(HrmsCollectionUtils.convert(configSelectDTOS, ConfigSelectVO.class));
    }

    /**
     * 校验参数：
     * 薪资组成：1.必须包含一条基础薪资，2，必须加起来等于100
     * 加班补贴 不能为空
     *
     * @param salaryConfigAddParam
     */
    private void validateAddParam(SalaryConfigAddParam salaryConfigAddParam) {
        // 计薪方式
        SalaryMethodEnum salaryMethodEnum = SalaryMethodEnum.getInstance(salaryConfigAddParam.getSalaryMethod());
        BusinessLogicException.checkTrue(salaryMethodEnum == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryMethod");

        // 如果是计件 则校验 每件薪资即可
        // 如果是 计时 则需要校验薪资组成及加班相关配置
        // 如果是计件+计时，则需要校验每件薪资、薪资组成及加班相关配置
        switch (salaryMethodEnum) {
            case PIECE:
                validateForPiece(salaryConfigAddParam);
                break;
            case TIME:
                validateForTime(salaryConfigAddParam);
                break;
            case TIME_AND_PIECE:
                validateForTime(salaryConfigAddParam);
                validateForPiece(salaryConfigAddParam);
                break;
            default:
        }
    }

    /**
     * 校验 计薪方式为计时的参数
     *
     * @param salaryConfigAddParam
     */
    private void validateForPiece(SalaryConfigAddParam salaryConfigAddParam) {
        BusinessLogicException.checkTrue(StringUtils.isBlank(salaryConfigAddParam.getPieceSalaryType()), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "pieceSalaryType");
        // 固定值。必须校验是否为空
        if (InputTypeEnum.FIXED_VALUE.name().equals(salaryConfigAddParam.getPieceSalaryType())) {
            BusinessLogicException.checkTrue(salaryConfigAddParam.getPieceSalary() == null || salaryConfigAddParam.getPieceSalary().compareTo(BigDecimal.ZERO) < 0, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "pieceSalary");
        }
    }

    /**
     * 校验 计薪方式为计时的参数
     * 薪资组成：1.必须包含一条基础薪资，2，必须加起来等于100
     * 校验加班补贴相关配置
     *
     * @param salaryConfigAddParam
     */
    private void validateForTime(SalaryConfigAddParam salaryConfigAddParam) {
        List<SalaryItemParam> salaryItems = salaryConfigAddParam.getSalaryItems();
        BusinessLogicException.checkTrue(CollectionUtils.isEmpty(salaryItems), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryItems");
        BusinessLogicException.checkTrue(salaryConfigAddParam.getIsContainsDayOff() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "isContainsDayOff");
        // 校验加班补贴相关配置
        Integer baseCount = 0;
        BigDecimal totalRatio = BigDecimal.ZERO;
        for (SalaryItemParam salaryItem : salaryItems) {
            BusinessLogicException.checkTrue(StringUtils.isBlank(salaryItem.getSalaryItemType()), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryItemType");
            BusinessLogicException.checkTrue(StringUtils.isBlank(salaryItem.getSalaryItemName()), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryItemName");
            // 比例必须在0~100之间
            if (salaryConfigAddParam.getSalaryType().equals(BusinessConstant.ZERO)) {
                BusinessLogicException.checkTrue(salaryItem.getSalaryRatio() == null || salaryItem.getSalaryRatio().compareTo(BigDecimal.ZERO) < 0 || salaryItem.getSalaryRatio().compareTo(BusinessConstant.HUNDRED) > 0, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryRatio");
                if (SalaryItemTypeEnum.BASE.name().equals(salaryItem.getSalaryItemType())) {
                    // 设置基础薪资比例
                    salaryConfigAddParam.setBaseSalaryRatio(salaryItem.getSalaryRatio());
                    baseCount++;
                }
                totalRatio = totalRatio.add(salaryItem.getSalaryRatio());
            }

            if (salaryItem.getSalaryItemId() == null) {
                // 设置项目ID
                salaryItem.setSalaryItemId(iHrmsIdWorker.nextId());
            }
        }
        //百分比薪资组成
        if (salaryConfigAddParam.getSalaryType().equals(BusinessConstant.ZERO)) {
            // 基础薪资有且仅能有一条 ,且薪资比例加起来必须等于100
            BusinessLogicException.checkTrue(baseCount != 1 || totalRatio.compareTo(BusinessConstant.HUNDRED) != 0, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryItems");
        }
        // 校验加班补贴相关配置
        validateOvertimeParam(salaryConfigAddParam.getOvertimeConfig());
    }

    private void validateOvertimeParam(OvertimeParam overtimeParam) {
        BusinessLogicException.checkTrue(overtimeParam == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "salaryItems");

        validateOvertimeItemParam(overtimeParam.getPresent());
        validateOvertimeItemParam(overtimeParam.getWeekend());
        validateOvertimeItemParam(overtimeParam.getHoliday());
    }

    private void validateOvertimeItemParam(OvertimeItemParam overtimeItemParam) {
        BusinessLogicException.checkTrue(overtimeItemParam == null || OvertimeAllowanceTypeEnum.getInstance(overtimeItemParam.getAllowanceType()) == null || overtimeItemParam.getValue() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "overtimeConfig");
    }

}
