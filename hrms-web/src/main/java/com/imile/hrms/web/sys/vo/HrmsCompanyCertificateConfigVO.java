package com.imile.hrms.web.sys.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HrmsCompanyCertificateConfigVO implements Serializable {
    private static final long serialVersionUID = -5958833272054117183L;

    /**
     * 所属国
     */
    private String country;
    /**
     * 必填证件
     */
    private List<CertificateTypeVO> mustHandlerCertificates;
    /**
     * 几选1必填
     */
    private List<CertificateTypeVO> needHandlerCertificates;
    /**
     * 选填
     */
    private List<CertificateTypeVO> needNotHandlerCertificates;
}
