package com.imile.hrms.web.user.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;

/**
 * 入职邀请生成token的内容对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserTokenDTO {
    public static final String DEFAULT_DELIMITER = ",";
    private Long userId;

    /**
     * 所属国
     */
    private String country;

    private Integer driver;

    private String employeeType;

}
