package com.imile.hrms.web.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class HrmsOtherSalaryConfigAddParam implements Serializable {
    /**
     * 适用国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * 增项/减项薪资
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String type;

    /**
     * 增项/减项薪资明细项key
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String itemKey;

    /**
     * 增项/减项薪资明细项名称
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String itemName;

    /**
     * 增项/减项薪资明细项名称
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String itemNameEn;

    /**
     * 排序
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer sort;

    /**
     * 状态
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String status;
}
