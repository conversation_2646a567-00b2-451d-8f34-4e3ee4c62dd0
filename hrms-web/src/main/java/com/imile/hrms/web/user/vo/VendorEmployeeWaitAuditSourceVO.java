package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-8-30
 * @version: 1.0
 */
@Data
public class VendorEmployeeWaitAuditSourceVO {
    /**
     * 姓名
     */
    private String userName;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 国籍
     */
    private String countryName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 性别
     */
    private String sex;

    /**
     * 原网点
     */
    private String originalDeptName;

    /**
     * 变更后网点
     */
    private String afterAuditDeptName;

    /**
     * 岗位
     */
    private String postName;

    /**
     * 员工性质
     */
    private String employeeTypeDesc;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 上级员工编码
     */
    private String leaderUserCode;
    /**
     * 上级员工名称
     */
    private String leaderUserName;

    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 身份证过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idCardExpireDate;
    /**
     * 居住证
     */
    private String residencyPermit;
    /**
     * 居住证过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date residencyPermitExpireDate;
    /**
     * 护照
     */
    private String passPort;
    /**
     * 护照过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date passPortExpireDate;
    /**
     * 工作签证
     */
    private String workVisa;
    /**
     * 工作签证过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workVisaExpireDate;
    /**
     * 旅游签
     */
    private String touristVisa;
    /**
     * 旅游签证过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date touristVisaExpireDate;
    /**
     * 商务签
     */
    private String businessVisa;
    /**
     * 商务签过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date businessVisaExpireDate;
    /**
     * 司机驾照
     */
    private String drivingLicense;
    /**
     * 驾照过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date drivingLicenseExpireDate;

    /**
     * 提交人姓名
     */
    private String submitUserName;

    /**
     * 提交人账号
     */
    private String submitUserCode;

    /**
     * 提交时间
     */
    private Date submitDate;
}
