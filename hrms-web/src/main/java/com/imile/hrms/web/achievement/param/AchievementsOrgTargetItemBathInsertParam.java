package com.imile.hrms.web.achievement.param;

import com.imile.hrms.service.achievement.dto.TargetItemBathAddDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 组织目标明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsOrgTargetItemBathInsertParam {


    private String status;

    @NotNull(message = "活动id不能为空")
    private Long eventId;

    List<TargetItemBathAddDTO> targetItemList;
}
