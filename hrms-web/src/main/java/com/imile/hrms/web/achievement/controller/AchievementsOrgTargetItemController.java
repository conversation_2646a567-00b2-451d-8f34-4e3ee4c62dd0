package com.imile.hrms.web.achievement.controller;


import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.ForbidRepeatClick;
import com.imile.hrms.service.achievement.AchievementsOrgTargetItemService;
import com.imile.hrms.service.achievement.dto.*;
import com.imile.hrms.service.achievement.param.checkMergeParam;
import com.imile.hrms.web.achievement.param.*;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetItemHeadVO;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetItemSuperiorRankVO;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetItemValueListVO;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetItemValueVO;
import com.imile.util.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.imile.hrms.web.BaseController;

import javax.validation.Valid;
import java.util.List;

/**
 * @menu 组织目标明细表
 * <AUTHOR>
 * @since 2023-03-30
 */
@RestController
@RequestMapping("/targetItem")
public class AchievementsOrgTargetItemController extends BaseController {


    @Autowired
    private AchievementsOrgTargetItemService achievementsOrgTargetItemService;

    /**
     * 目标新增(指标批量新增)
     * @param param
     * @return
     */
    @PostMapping("/bathAdd")
    @ForbidRepeatClick
    public Result bathAdd(@RequestBody @Valid AchievementsOrgTargetItemBathInsertParam param) {
        AchievementsOrgTargetItemBathInsertDTO dto = BeanUtils.convert(param,AchievementsOrgTargetItemBathInsertDTO.class);
        achievementsOrgTargetItemService.bathAdd(dto);
        return Result.ok();
    }


    /**
     * 指标表头列表
     * @param deptId
     * @param eventId
     * @return
     */
    @PostMapping("/headList")
    public Result<List<AchievementsOrgTargetItemHeadVO>> headList(Long deptId, Long eventId) {

        //列表查询
        List<AchievementsOrgTargetItemHeadDTO> list = achievementsOrgTargetItemService.headList(deptId,eventId);

        //转换为VO
        List<AchievementsOrgTargetItemHeadVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetItemHeadVO.class, list);

        return Result.ok(resultDTOList);
    }


    /**
     * 目标值列表
     * @param param
     * @return
     */
    @PostMapping("/valueList")
    public Result<List<AchievementsOrgTargetItemValueVO>> valueList(@RequestBody ValueListParam param) {
        List<AchievementsOrgTargetItemValueDTO> dtoList=achievementsOrgTargetItemService.valueList(param.getDeptIds(),param.getEventId());
        List<AchievementsOrgTargetItemValueVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetItemValueVO.class, dtoList);
        return Result.ok(resultDTOList);
    }

    /**
     * 部门id集合
     * @param code
     * @param eventId
     * @return
     */
    @PostMapping("/deptList")
    public Result<List<Long>> deptList(String code, Long eventId) {
        return Result.ok(achievementsOrgTargetItemService.deptList(code,eventId));
    }


    /**
     * 部门能否合并校验
     * @param param
     * @return
     */
    @PostMapping("/checkMerge")
    public Result checkMerge(@RequestBody checkMergeParam param) {
        achievementsOrgTargetItemService.checkMerge(param.getDeptIds(),param.getEventId());
        return Result.ok();
    }

    /**
     * 单个部门能否查看
     * @param param
     * @return
     */
    @PostMapping("/checkDept")
    public Result<Boolean> checkDept(@RequestBody checkDeptParam param) {
        return Result.ok(achievementsOrgTargetItemService.checkDept(param.getDeptId(),param.getEventId()));
    }

    /**
     * 完成值列表及本级组织排名
     * @param param
     * @return
     */
    @PostMapping("/completeValueList")
    public Result<List<AchievementsOrgTargetItemValueListVO>> completeValueList(@RequestBody completeValueParam param) {
        CompleteValueDTO dto = BeanUtils.convert(param, CompleteValueDTO.class);
        List<AchievementsOrgTargetItemValueListDTO> achievementsOrgTargetItemValueListDTO=achievementsOrgTargetItemService.completeValueList(dto);
        List<AchievementsOrgTargetItemValueListVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetItemValueListVO.class, achievementsOrgTargetItemValueListDTO);
        return Result.ok(resultDTOList);
    }

    /**
     * 完成值列表及上级组织排名
     */

    @PostMapping("/completeValueSuperiorRankList")
    public Result<List<AchievementsOrgTargetItemSuperiorRankVO>> completeValueSuperiorRankList(@RequestBody completeValueParam param) {
        CompleteValueDTO dto = BeanUtils.convert(param, CompleteValueDTO.class);
        List<AchievementsOrgTargetItemSuperiorRankDTO> achievementsOrgTargetItemValueListDTO=achievementsOrgTargetItemService.completeValueSuperiorRankList(dto);
        List<AchievementsOrgTargetItemSuperiorRankVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetItemSuperiorRankVO.class, achievementsOrgTargetItemValueListDTO);
        return Result.ok(resultDTOList);
    }


}
