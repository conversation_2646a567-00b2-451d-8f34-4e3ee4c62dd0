package com.imile.hrms.web.punch.controller;

import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.service.punch.WarehouseExternalService;
import com.imile.hrms.service.punch.param.warehouse.GetOcListByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportCountByConditionParam;
import com.imile.hrms.service.punch.vo.warehouse.OcVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehousePcsMonthReportCountVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehousePcsMonthReportPassRateVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehousePcsReportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 仓内考勤提供外部服务
 * <AUTHOR>
 * @since 2024/9/27
 */
@Slf4j
@RestController
@RequestMapping("/external/warehouse")
public class WarehouseExternalController {
    @Resource
    private WarehouseExternalService warehouseExternalService;

    /**
     * 获取指定日期供应商操作站点
     */
    @PostMapping("/getOcListByCondition")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<List<OcVO>> getOcListByCondition(@RequestBody @Validated GetOcListByConditionParam param) {
        return Result.ok(warehouseExternalService.getOcListByCondition(param));
    }

    /**
     * 获取PCS每日状态概览
     */
    @PostMapping("/pcs/dateReport")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<PaginationResult<WarehousePcsReportVO>> pcsDateReport(@RequestBody @Validated GetPcsReportByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsDateReport(param));
    }

    /**
     * 获取PCS周度或月度状态概览
     */
    @PostMapping("/pcs/weekOrMonthReport")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<PaginationResult<WarehousePcsReportVO>> pcsWeekOrMonthReport(@RequestBody @Validated GetPcsReportByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsWeekOrMonthReport(param));
    }

    /**
     * 获取PCS每日仓内操作状态数量
     */
    @PostMapping("/pcs/dateReportCount")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<WarehousePcsMonthReportCountVO> pcsDateReportCount(@RequestBody @Validated GetPcsReportCountByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsDateReportCount(param));
    }

    /**
     * 获取仓内月度操作合格率
     */
    @PostMapping("/pcs/dateMonthReportPassRate")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<WarehousePcsMonthReportPassRateVO> pcsDateMonthReportPassRate(@RequestBody @Validated GetPcsReportCountByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsDateMonthReportPassRate(param));
    }

    /**
     * 刷新历史pcs状态
     */
    @GetMapping("/refresh/historyPcsStatus")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Void> refreshHistoryPcsStatus() {
        warehouseExternalService.refreshHistoryPcsStatus();
        return Result.ok();
    }
}
