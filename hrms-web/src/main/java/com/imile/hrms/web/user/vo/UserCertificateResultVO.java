package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserCertificateResultVO implements Serializable {
    private static final long serialVersionUID = 4389146324426692321L;

    private Long id;

    /**
     * 证件类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.CERTIFICATE_TYPE_CODE, ref = "certificateTypeCodeDesc")
    private String certificateTypeCode;
    /**
     * 证件类型
     */
    private String certificateTypeCodeDesc;
    /**
     * 证件编码
     */
    private String certificateCode;
    /**
     * 证件到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateExpireDate;
    /**
     * 处理方法 MUST_HANDLER,NEED_HANDLER,NEED_NOT_HANDLER
     */
    private String handlerMethod;

}
