package com.imile.hrms.web.user.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.imile.common.exception.BusinessException;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.util.CryptoUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.user.dto.UserInfoExpandDTO;
import com.imile.hrms.web.user.dto.UserTokenDTO;
import com.imile.hrms.web.user.vo.UserInfoExpandNoLoginVO;
import com.imile.hrms.web.user.vo.UserInfoExpandVO;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/11
 */
@Slf4j
@Component
public class UserAssembler {
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private ImileRedisClient imileRedisClient;
    @Autowired
    private IHrmsIdWorker hrmsIdWorker;

    /**
     * 生成Token
     *
     * @param userTokenDTO
     * @return
     */
    public String generateToken(UserTokenDTO userTokenDTO) {
        String uuid = hrmsIdWorker.uuid();
        // 放入缓存中
        imileRedisClient.set(uuid, userTokenDTO, DateUtil.offsetDay(new Date(), hrmsProperties.getEntry().getInviteLinkEffectiveDays()).getTime());
        return uuid;
    }

    /**
     * 生成签名
     *
     * @param expireTime
     * @param userId
     * @return
     */
    public String sign(Long expireTime, Long userId) {
        return CryptoUtil.sign(hrmsProperties.getEntry().getSignKey(), expireTime + "" + userId);
    }

    /**
     * 校验token，校验成功，返回解密后的内容
     *
     * @param token
     * @return
     */
    public UserTokenDTO verifyToken(String token) throws BusinessException {
        if (StringUtils.isEmpty(token)) {
            throw BusinessException.get(HrmsErrorCodeEnums.TOKEN_EXCEPTION.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.TOKEN_EXCEPTION.getDesc()));
        }
        UserTokenDTO userTokenDTO = imileRedisClient.get(token, UserTokenDTO.class);
        //解析异常 ====> token无效
        if (userTokenDTO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.HYPERLINK_EXPIRE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.TOKEN_EXCEPTION.getDesc()));
        }

        return userTokenDTO;
    }

    public UserInfoExpandVO getUserInfoExpandVO(UserInfoExpandDTO userInfo) {

        UserInfoExpandVO userInfoExpandVO = new UserInfoExpandVO();
        BeanUtil.copyProperties(userInfo, userInfoExpandVO);
        return userInfoExpandVO;
    }

    public UserInfoExpandNoLoginVO getUserInfoExpandNoLoginVO(UserInfoExpandDTO userInfo) {

        UserInfoExpandNoLoginVO userInfoExpandVO = new UserInfoExpandNoLoginVO();
        BeanUtil.copyProperties(userInfo, userInfoExpandVO);
        return userInfoExpandVO;
    }


    public static void main(String[] args) {
        System.out.println(UUID.randomUUID().toString().replaceAll("-", ""));
    }
}
