package com.imile.hrms.web.salary;

import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.salary.HrmsSalaryItemTemplateConfigService;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigAddParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigCountryQueryParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigDeleteParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigDetailParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigInfoParam;
import com.imile.hrms.service.salary.vo.SalaryItemTemplateConfigCountryDetailVO;
import com.imile.hrms.service.salary.vo.SalaryItemTemplateConfigDetailVO;
import com.imile.hrms.service.salary.vo.SalaryItemTemplateConfigInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/25 14:06
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/salary/item/template/config")
public class HrmsSalaryItemTemplateConfigController {

    @Autowired
    private HrmsSalaryItemTemplateConfigService hrmsSalaryItemTemplateConfigService;
    @Autowired
    private ConverterService converterService;

    /**
     * 列表查询
     */
    @PostMapping("/list")
    public Result<PaginationResult<SalaryItemTemplateConfigInfoVO>> list(@RequestBody @Validated SalaryItemTemplateConfigInfoParam param) {
        PaginationResult<SalaryItemTemplateConfigInfoVO> resultVO = hrmsSalaryItemTemplateConfigService.list(param);
        List<SalaryItemTemplateConfigInfoVO> registerUserInfoVOList = converterService.withAnnotation(resultVO.getResults());
        resultVO.setResults(registerUserInfoVOList);
        return Result.ok(resultVO);
    }

    /**
     * 详情
     */
    @PostMapping("/detail")
    public Result<SalaryItemTemplateConfigDetailVO> detail(@RequestBody @Validated SalaryItemTemplateConfigDetailParam param) {
        SalaryItemTemplateConfigDetailVO resultVO = hrmsSalaryItemTemplateConfigService.detail(param);
        converterService.withAnnotationForSingle(resultVO);
        return Result.ok(resultVO);
    }


    /**
     * 删除
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @Validated SalaryItemTemplateConfigDeleteParam param) {
        hrmsSalaryItemTemplateConfigService.delete(param);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 新增
     */
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody @Validated SalaryItemTemplateConfigAddParam param) {
        hrmsSalaryItemTemplateConfigService.add(param);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 更新
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Validated SalaryItemTemplateConfigAddParam param) {
        hrmsSalaryItemTemplateConfigService.update(param);
        return Result.ok(Boolean.TRUE);
    }


    /**
     * 获取所有国家下的薪资项模版
     */
    @PostMapping("/country/list")
    public Result<List<SalaryItemTemplateConfigCountryDetailVO>> countryList(@RequestBody @Validated SalaryItemTemplateConfigCountryQueryParam param) {
        List<SalaryItemTemplateConfigCountryDetailVO> resultVO = hrmsSalaryItemTemplateConfigService.countryList(param);
        converterService.withAnnotation(resultVO);
        return Result.ok(resultVO);
    }
}
