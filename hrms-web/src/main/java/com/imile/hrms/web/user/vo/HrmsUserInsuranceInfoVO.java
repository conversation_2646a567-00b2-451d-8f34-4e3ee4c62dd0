package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class HrmsUserInsuranceInfoVO implements Serializable {

    /**
     * 保险等级类型
     */
    private String insuranceCategory;

    /**
     * 保险到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date insuranceExpiryDate;

    /**
     * 保险附件
     */
    @HyperLink(ref = "insuranceFileUrlHttps")
    private String insuranceFileUrl;
    private String insuranceFileUrlHttps;

    private String insuranceFileName;
}
