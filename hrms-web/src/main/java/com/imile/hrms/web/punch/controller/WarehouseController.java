package com.imile.hrms.web.punch.controller;

import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.integration.ipep.OssApiVo;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.face.FaceEngineService;
import com.imile.hrms.service.punch.WarehouseAttendanceHandlerService;
import com.imile.hrms.service.punch.WarehouseClassesService;
import com.imile.hrms.service.punch.WarehouseOcService;
import com.imile.hrms.service.punch.WarehouseReadService;
import com.imile.hrms.service.punch.WarehouseService;
import com.imile.hrms.service.punch.WarehouseSupplierService;
import com.imile.hrms.service.punch.WarehouseUserService;
import com.imile.hrms.service.punch.param.warehouse.AddUserParam;
import com.imile.hrms.service.punch.param.warehouse.BingShiftParam;
import com.imile.hrms.service.punch.param.warehouse.GetClassListByConditionParam;
import com.imile.hrms.service.punch.param.warehouse.GetOcListByVendorCodeParam;
import com.imile.hrms.service.punch.param.warehouse.GetVendorListByOcListParam;
import com.imile.hrms.service.punch.param.warehouse.NoBingShiftReportParam;
import com.imile.hrms.service.punch.param.warehouse.OcUserParam;
import com.imile.hrms.service.punch.param.warehouse.ReportParam;
import com.imile.hrms.service.punch.param.warehouse.SimpleReportParam;
import com.imile.hrms.service.punch.param.warehouse.UpdateWarehouseWorkClassesParam;
import com.imile.hrms.service.punch.param.warehouse.UpdateWarehouseWorkOcParam;
import com.imile.hrms.service.punch.param.warehouse.UpdateWarehouseWorkVendorParam;
import com.imile.hrms.service.punch.vo.warehouse.BusZoneListVO;
import com.imile.hrms.service.punch.vo.warehouse.ClassesDetailVO;
import com.imile.hrms.service.punch.vo.warehouse.ClassesWebVO;
import com.imile.hrms.service.punch.vo.warehouse.DateDetailReportVO;
import com.imile.hrms.service.punch.vo.warehouse.DateReportSimpleVO;
import com.imile.hrms.service.punch.vo.warehouse.DateReportVO;
import com.imile.hrms.service.punch.vo.warehouse.MonthReportVO;
import com.imile.hrms.service.punch.vo.warehouse.OcVO;
import com.imile.hrms.service.punch.vo.warehouse.PunchConfigAndClassesVO;
import com.imile.hrms.service.punch.vo.warehouse.ReportVO;
import com.imile.hrms.service.punch.vo.warehouse.UserVO;
import com.imile.hrms.service.punch.vo.warehouse.VendorClassesConfirmVO;
import com.imile.hrms.service.punch.vo.warehouse.VendorVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehouseOcUserVO;
import com.imile.hrms.web.BaseController;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内服务控制器
 * @date 2024/6/29 17:46:47
 */
@RestController
@RequestMapping("/warehouse")
public class WarehouseController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(WarehouseController.class);

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private WarehouseReadService warehouseReadService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private WarehouseClassesService warehouseClassesService;

    @Resource
    private WarehouseUserService warehouseUserService;

    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;

    @Resource
    private FaceEngineService faceEngineService;

    @Resource
    private ConverterService converterService;

    /**
     * 查询国家列表
     */
    @GetMapping("/getCountryList")
    public Result<List<BusZoneListVO>> getCountryList() {
        return Result.ok(warehouseReadService.getCountryList());
    }

    /**
     * 指定条件查询网点列表
     */
    @PostMapping("/getOcListByCountry")
    public Result<List<OcVO>> getOcListByCondition(@Validated @RequestBody GetVendorListByOcListParam param) {
        return Result.ok(warehouseOcService.getOcListByCondition(param));
    }

    /**
     * 指定条件查询供应商
     */
    @PostMapping("/getVendorListByCondition")
    public Result<List<VendorVO>> getVendorListByCondition(@Validated @RequestBody GetVendorListByOcListParam param) {
        return Result.ok(warehouseSupplierService.getVendorListByCondition(param));
    }

    /**
     * 根据供应商编码查询服务网点列表
     */
    @PostMapping("/getOcListByVendorCode")
    public Result<List<OcVO>> getOcListByVendorCode(@Validated @RequestBody GetOcListByVendorCodeParam param) {
        return Result.ok(warehouseOcService.getOcListByVendorCode(param));
    }

    /**
     * 查询班次列表
     */
    @PostMapping("/web/classes/list")
    public Result<List<ClassesWebVO>> getClassesList(@RequestBody GetClassListByConditionParam param) {
        return Result.ok(warehouseClassesService.getClassesList(param));
    }

    /**
     * 查询班次列表
     * 调用方：财务
     * 入参是hermes网点Id列表
     */
    @PostMapping("/outer/classes/list")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<List<ClassesWebVO>> getOuterClassesList(@RequestBody GetClassListByConditionParam param) {
        List<ClassesWebVO> vos = warehouseClassesService.getOuterClassesList(param);
        return Result.ok(vos);
    }

    /**
     * 供应商班次确认详情
     */
    @GetMapping("/confirm/detail")
    public Result<VendorClassesConfirmVO> vendorClassedConfirmDetailWeb(@RequestParam("id") Long id) {
        VendorClassesConfirmVO result = warehouseReadService.vendorClassedConfirmDetailWeb(id);
        converterService.withAnnotationForSingle(result);
        if (CollectionUtils.isNotEmpty(result.getAbnormalVOList())) {
            converterService.withAnnotation(result.getAbnormalVOList());
        }
        return Result.ok(result);
    }

    /**
     * 查询考勤组和班次列表
     */
    @GetMapping("/classes/map")
    public Result<PunchConfigAndClassesVO> getPunchConfigAndClasses(@RequestParam("ocId") Long ocId) {
        PunchConfigAndClassesVO vo = warehouseClassesService.getPunchConfigAndClasses(ocId);
        return Result.ok(vo);
    }

    /**
     * 班次详情
     */
    @GetMapping("/classes/detail")
    public Result<List<ClassesDetailVO>> getClassesDetail(@RequestParam("classesId") Long classesId) {
        List<ClassesDetailVO> vos = warehouseClassesService.getClassesDetail(classesId);
        return Result.ok(vos);
    }

    /**
     * 增加/修改仓内外包人员
     * 如果是新增，则会加一次证件录入
     * 员工管理页面编辑信息保存也是调用这个接口
     */
    @PostMapping("/saveOrUpdateUser")
    public Result<UserVO> saveOrUpdateUser(@Validated @RequestBody AddUserParam param) {
        log.info("saveOrUpdateUser={}", param);
        param.setSource(BusinessConstant.HRMS_WEB);
        return Result.ok(warehouseUserService.saveOrUpdateUser(param));
    }


    /**
     * 仓内日报
     */
    @PostMapping("/report/date")
    public Result<PaginationResult<DateReportVO>> dateReport(@Validated @RequestBody ReportParam param) {
        PaginationResult<DateReportVO> v = warehouseReadService.dateReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 仓内日报导出
     **/
    @PostMapping("/export/date")
    public Result<PaginationResult<DateReportVO>> dateExport(HttpServletRequest request, ReportParam param) {
        setExcelCallBackParam(request, param);
        PaginationResult<DateReportVO> v = warehouseReadService.dateReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 仓内日报详细
     */
    @GetMapping("/report/date/detail")
    public Result<DateDetailReportVO> dateReportDetail(@RequestParam(value = "id") Long id) {
        DateDetailReportVO v = warehouseReadService.dateReportDetail(id);
        converterService.withAnnotationForSingle(v.getAttendanceResult());
        if (CollectionUtils.isNotEmpty(v.getWarehouseRecordList())) {
            converterService.withAnnotation(v.getWarehouseRecordList());
            v.getWarehouseRecordList().forEach(record -> converterService.withAnnotationForSingle(record.getFaceRecognitionDetail()));
        }
        if (CollectionUtils.isNotEmpty(v.getAttendanceResult().getAttendanceAbnormalTypes())) {
            converterService.withAnnotation(v.getAttendanceResult().getAttendanceAbnormalTypes());
        }
        if (CollectionUtils.isNotEmpty(v.getAttendanceAbnormalList())) {
            converterService.withAnnotation(v.getAttendanceAbnormalList());
        }
        if (CollectionUtils.isNotEmpty(v.getLeaveDetailList())) {
            converterService.withAnnotation(v.getLeaveDetailList());
        }
        return Result.ok(v);
    }

    /**
     * 仓内月报
     */
    @PostMapping("/report/month")
    public Result<PaginationResult<MonthReportVO>> monthReport(@Validated @RequestBody ReportParam param) {
        PaginationResult<MonthReportVO> v = warehouseReadService.monthReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 仓内月报导出
     **/
    @PostMapping("/export/month")
    public Result<PaginationResult<MonthReportVO>> monthExport(HttpServletRequest request, ReportParam param) {
        setExcelCallBackParam(request, param);
        PaginationResult<MonthReportVO> v = warehouseReadService.monthReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 查询账号网点权限下待配置班次考勤记录数量
     */
    @GetMapping("/count/notBindShift")
    public Result<Integer> notBindShiftCount() {
        return Result.ok(warehouseReadService.noBindShiftCount());
    }

    /**
     * 待配置班次网点下拉列表
     */
    @GetMapping("/getNoBingShiftOcList")
    public Result<List<OcVO>> getNoBingShiftOcList() {
        return Result.ok(warehouseOcService.getNoBingShiftOcList());
    }

    /**
     * 仓内无绑定班次日报
     */
    @PostMapping("/report/date/noBingShift")
    public Result<List<DateReportVO>> dateReportNoBingShift(@Validated @RequestBody NoBingShiftReportParam param) {
        List<DateReportVO> v = warehouseReadService.dateReportNoBingShift(param);
        return Result.ok(v);
    }

    /**
     * 绑定班次
     */
    @PostMapping("/bingShift")
    public Result<Boolean> bingShift(@Validated @RequestBody BingShiftParam param) {
        return Result.ok(warehouseService.bingShift(param));
    }


    /**
     * 简易日报列表
     * 批量修改网点&供应商&班次
     */
    @PostMapping("/simple/report/date")
    public Result<List<DateReportSimpleVO>> simpleDateReport(@Validated @RequestBody SimpleReportParam param) {
        List<DateReportSimpleVO> v = warehouseReadService.simpleDateReport(param);
        return Result.ok(v);
    }

    /**
     * 仓内考勤更新工作供应商
     */
    @PostMapping("/update/workVendor")
    public Result<Void> updateWorkVendor(@Validated @RequestBody UpdateWarehouseWorkVendorParam param) {
        warehouseService.updateWorkVendor(param);
        return Result.ok();
    }

    /**
     * 仓内考勤更新工作网点
     */
    @PostMapping("/update/workOc")
    public Result<Void> updateWorkOc(@Validated @RequestBody UpdateWarehouseWorkOcParam param) {
        warehouseService.updateWorkOc(param);
        return Result.ok();
    }

    /**
     * 仓内考勤更新工作班次
     */
    @PostMapping("/update/workClasses")
    public Result<Void> updateWorkClasses(@Validated @RequestBody UpdateWarehouseWorkClassesParam param) {
        warehouseService.updateWorkClasses(param);
        return Result.ok();
    }


    /**
     * 网点员工管理页面
     */
    @PostMapping("/ocUserList")
    public Result<PaginationResult<ReportVO>> ocUserList(@Validated @RequestBody OcUserParam param) {
        PaginationResult<ReportVO> v = warehouseUserService.ocUserList(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 网点员工详情
     */
    @GetMapping("/ocUserDetail")
    public Result<WarehouseOcUserVO> ocUserDetail(@RequestParam(value = "id") Long id) {
        WarehouseOcUserVO v = warehouseUserService.ocUserDetail(id);
        converterService.withAnnotationForSingle(v);
        if (CollectionUtils.isNotEmpty(v.getCertificateVOList())) {
            converterService.withAnnotation(v.getCertificateVOList());
        }
        return Result.ok(v);
    }

    /**
     * 删除入仓记录
     */
    @PostMapping("/delete/warehouseDetail")
    public Result<Void> deleteWarehouseDetail(@Validated @RequestBody WarehouseDetailParam param) {
        warehouseService.deleteWarehouseDetail(param);
        return Result.ok();
    }

    /**
     * 重试刷新考勤异常计算结果
     */
    @PostMapping("/retry/calculate/abnormal")
    public Result<Void> retryCalculateAbnormal(@Validated @RequestBody WarehouseDetailParam param) {
        warehouseAttendanceHandlerService.retryCalculateAbnormal(param);
        return Result.ok();
    }

    /**
     * 删除人脸库记录
     */
    @GetMapping("/delete/faceFeature")
    public Result<Void> deleteFaceFeature(@RequestParam(value = "userCodes") String userCodes) {
        warehouseService.deleteFaceFeature(userCodes);
        return Result.ok();
    }

    /**
     * 仓内考勤补推财务
     */
    @PostMapping("/retry/push/fin")
    public Result<Void> retryPushFin(@Validated @RequestBody WarehouseDetailParam param) {
        warehouseService.retryPushFin(param);
        return Result.ok();
    }

    /**
     * 获取OSS附件完整url
     */
    @GetMapping("/oss/fileUrl")
    public Result<OssApiVo> getOSSFileUrl(@RequestParam(value = "fileKey") String fileKey) {
        return Result.ok(warehouseReadService.getOssFileUrl(fileKey));
    }

    /**
     * 人脸库扫描相似人脸不同账号
     */
    @GetMapping("/scanDuplicateFace")
    public Result<Void> scanDuplicateFace() {
        faceEngineService.scanDuplicateFace();
        return Result.ok();
    }

    /**
     * 初始化MEX历史劳务员工用工形式
     */
    @GetMapping("/mex/refreshWarehouseEmployeeEmployeeForm")
    public Result<Void> refreshWarehouseEmployeeEmployeeForm() {
        warehouseUserService.refreshWarehouseEmployeeEmployeeForm();
        return Result.ok();
    }

    /**
     * 仓内考勤异常计算结果
     */
    @GetMapping("/calculate/abnormal")
    public Result<Void> calculateAbnormal(@RequestParam(value = "userCode") String userCode,
                                          @RequestParam(value = "dayId") Long dayId) {
        warehouseAttendanceHandlerService.calculateAbnormal(userCode, dayId);
        return Result.ok();
    }
}
