package com.imile.hrms.web.vehicle.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-28
 * @version: 1.0
 */
@Data
public class VehicleStationConfigDeleteParam {
    /**
     * 配置表ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;
}
