package com.imile.hrms.web.punch.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.imile.hrms.common.annotation.OutWithTimeZone;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 考勤打卡规则DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendancePunchConfigVo implements Serializable {


    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 是否为默认
     */
    private Integer isDefault;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     * 前端不展示这个字段
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PUNCH_TYPE, ref = "punchConfigTypeDesc")
    private String punchConfigType;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     * 前端展示这个字段
     */
    private String punchConfigTypeDesc;

    /**
     * 打卡方式
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PUNCH_CARD_TYPE, ref = "punchCardTypeDesc")
    private String punchCardType;

    private String punchCardTypeDesc;


    /**
     * 使用范围记录
     */
    List<HrmsAttendancePunchConfigRangeVo> rangeRecords;

    /**
     * 已绑定员工数
     */
    private Integer employeeCount;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建日期
     */
    @OutWithTimeZone
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改日期
     */
    @OutWithTimeZone
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdDate;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否为主子负责人,可编辑
     */
    private Boolean isPrinciple;

    /**
     * 是否为主负责人
     */
    private Boolean isMaster;
}
