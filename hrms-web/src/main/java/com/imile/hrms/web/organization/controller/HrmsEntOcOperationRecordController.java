package com.imile.hrms.web.organization.controller;


import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.service.organization.HrmsEntOcService;
import com.imile.hrms.service.organization.param.DeptOcOperateRecordExportParam;
import com.imile.hrms.service.organization.param.DeptOcOperateRecordPageParam;
import com.imile.hrms.service.organization.result.HrmsEntOcOperationRecordVO;
import com.imile.hrms.web.BaseController;
import com.imile.util.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 网点管理
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@RestController
@RequestMapping("/ent/oc/operation/record")
public class HrmsEntOcOperationRecordController extends BaseController {

    @Autowired
    private HrmsEntOcService hrmsEntOcService;

    /**
     * 获取网点操作记录列表
     */
    @PostMapping("/list")
    public Result<PaginationResult<HrmsEntOcOperationRecordVO>> listOperationRecord(@RequestBody DeptOcOperateRecordPageParam param) {
        return Result.ok(hrmsEntOcService.list4Hrms(param));
    }

    /**
     * 获取待处理网点操作记录
     * todo 发布完成之后删除这个方法
     *
     * @return List<HrmsOcOperationRecordVO>
     */
    @GetMapping("/unhandled/list")
    @Deprecated
    public Result<List<HrmsEntOcOperationRecordVO>> listUnhandledOperationRecord() {
        return Result.ok(hrmsEntOcService.listUnhandledOperationRecord4Hrms());
    }

    /**
     * 导出网点操作记录列表
     */
    @PostMapping("/export")
    public Result<PaginationResult<HrmsEntOcOperationRecordVO>> exportOperationRecord(HttpServletRequest request, DeptOcOperateRecordExportParam param) {
        setExcelCallBackParam(request, param);
        DeptOcOperateRecordPageParam pageParam = BeanUtils.convert(param, DeptOcOperateRecordPageParam.class);
        pageParam.setShowCount(50000);
        if (StringUtils.isNotBlank(param.getOperationType())) {
            pageParam.setOperationType(Integer.parseInt(param.getOperationType()));
        }
        if (StringUtils.isNotBlank(param.getRecordStatus())) {
            pageParam.setRecordStatus(Integer.parseInt(param.getRecordStatus()));
        }
        return Result.ok(hrmsEntOcService.list4Hrms(pageParam));
    }

    /**
     * 忽略网点操作记录
     *
     * @param id 网点操作记录ID
     * @return Boolean
     */
    @PostMapping("/ignore")
    public Result<Boolean> ignoreOperationRecord(@RequestParam("id") Long id) {
        return Result.ok(hrmsEntOcService.ignoreOperationRecord(id));
    }

    /**
     * 处理网点操作记录
     *
     * @param id 网点操作记录ID
     * @return Boolean
     */
    @PostMapping("handle")
    @Deprecated
    public Result<Boolean> handleOperationRecord(@RequestParam("id") Long id) {
        return Result.ok(hrmsEntOcService.handleOperationRecord(id));
    }

}
