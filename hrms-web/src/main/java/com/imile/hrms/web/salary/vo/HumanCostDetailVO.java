package com.imile.hrms.web.salary.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 人力成本明细
 * <AUTHOR>
 * @date 2022/1/6
 */
@Data
public class HumanCostDetailVO implements Serializable {
    private static final long serialVersionUID = -5924293262768576170L;

    private Long id;
    /**
     * 员工姓名
     */
    private String userName;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 编码
     */
    private String userCode;
    /**
     * 国家
     */
    private String country;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 员工性质
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    private String employeeTypeDesc;

    /**
     * 出勤工资 一个周期内员工的出勤工资
     */
    private BigDecimal attendanceSalary;

    /**
     * 加班补贴 一个周期内员工的节假日加班补贴
     */
    private BigDecimal overtimePay;

    /**
     * 派件数 一个周期内员工的派件数
     */
    private Integer dldCnt;

    /**
     * 计件薪资 一个周期内员工的计件薪资
     */
    private BigDecimal dldSalary;

    /**
     * 单位缴纳社保公积金费用 一个周期内员工的单位缴纳社保公积金费用
     */
    private BigDecimal orgSocialAndAccumulationPay;

    /**
     * 激励薪资 一个周期内员工的激励薪资
     */
    private BigDecimal incentiveSalary;

    /**
     * 其他增项 一个周期内员工的其他增项
     */
    private BigDecimal otherIncreaseSalary;

    /**
     * 供应商服务费
     */
    private BigDecimal vendorServicePay;

    /**
     * 人力成本 一个周期内员工公司的人力成本
     */
    private BigDecimal orgTotalPay;

    /**
     * 时间周期
     */
    private String timeCycle;

    /**
     * 币种
     */
    private String currency;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

}
