package com.imile.hrms.web.achievement.controller;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hermes.enterprise.dto.EntOcTreeApiDTO;
import com.imile.hrms.common.annotation.ForbidRepeatClick;
import com.imile.hrms.common.annotation.NoAuthRequired;
import com.imile.hrms.common.enums.achievement.IndicatorCompletEnum;
import com.imile.hrms.common.util.PageUtil;
import com.imile.hrms.dao.achievement.dto.TargetItemBoardListDTO;
import com.imile.hrms.service.achievement.AchievementsOrgTargetItemService;
import com.imile.hrms.service.achievement.AchievementsOrgTargetService;
import com.imile.hrms.service.achievement.dto.AchievementDeptTotalScoreVO;
import com.imile.hrms.service.achievement.dto.AchievementsItemListDTO;
import com.imile.hrms.service.achievement.dto.AchievementsOrgTargetAddDTO;
import com.imile.hrms.service.achievement.dto.AchievementsOrgTargetDTO;
import com.imile.hrms.service.achievement.dto.AchievementsOrgTargetItemDTO;
import com.imile.hrms.service.achievement.dto.AchievementsOrgTargetItemImportDTO;
import com.imile.hrms.service.achievement.dto.AchievementsOrgTargetItemImportParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsOrgTargetStatisticsListDTO;
import com.imile.hrms.service.achievement.param.AchievementsItemStatisticsListParam;
import com.imile.hrms.service.achievement.param.CopyOrgTargetParam;
import com.imile.hrms.service.achievement.param.HistoryOrgTargetParam;
import com.imile.hrms.service.achievement.param.OperationTargetListParam;
import com.imile.hrms.service.achievement.param.QueryOrgTargetParam;
import com.imile.hrms.service.achievement.param.SubmitTargetParam;
import com.imile.hrms.service.achievement.vo.OperationTargetItemVO;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.achievement.param.AchievementsItemParam;
import com.imile.hrms.web.achievement.param.AchievementsOrgTargetItemImportParam;
import com.imile.hrms.web.achievement.param.AchievementsOrgTargetParam;
import com.imile.hrms.service.achievement.param.AchievementsSetOrgTargetParam;
import com.imile.hrms.service.achievement.param.checkMergeParam;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetItemVO;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetStatisticsListVO;
import com.imile.hrms.web.achievement.vo.AchievementsOrgTargetVO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @menu 组织目标
 * <AUTHOR>
 * @since 2023-03-29
 */
@RestController
@RequestMapping("/achievementsTarget")
@Slf4j
public class AchievementsOrgTargetController extends BaseController {


    @Autowired
    private AchievementsOrgTargetItemService achievementsOrgTargetItemService;

    @Autowired
    private AchievementsOrgTargetService achievementsOrgTargetService;

    /**
     * 指标列表
     * @param query
     * @return
     */
    @PostMapping("/list")
    @NoAuthRequired
    public Result<List<AchievementsOrgTargetItemVO>> list(@RequestBody @Valid AchievementsItemParam query) {

        AchievementsItemListDTO listDTO = BeanUtils.convert(query, AchievementsItemListDTO.class);

        //列表查询
        List<AchievementsOrgTargetItemDTO> list = achievementsOrgTargetItemService.findList(listDTO);

        //转换为VO
        List<AchievementsOrgTargetItemVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetItemVO.class, list);

        return Result.ok(resultDTOList);
    }

    /**
     * 运营指标列表
     * @param param
     */
    @PostMapping("/operation/target/list")
    @NoAuthRequired
    public Result<List<OperationTargetItemVO>> operationTargetList(@RequestBody @Valid OperationTargetListParam param) {
        List<OperationTargetItemVO> list = achievementsOrgTargetItemService.operationTargetList(param);
        return Result.ok(list);
    }

    /**
     * 指标列表（统计版）
     * @param query
     * @return
     */
    @PostMapping("/statistics/list")
    @NoAuthRequired
    public Result<List<AchievementsOrgTargetStatisticsListVO>> statisticsList(@RequestBody AchievementsItemStatisticsListParam query) {
        //列表查询
        List<AchievementsOrgTargetStatisticsListDTO> list = achievementsOrgTargetItemService.statisticsList(query);

        //转换为VO
        List<AchievementsOrgTargetStatisticsListVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetStatisticsListVO.class, list);

        return Result.ok(resultDTOList);
    }

    /**
     * 指标列表（统计版）导出
     * @param query
     * @param request
     * @return
     */
    @PostMapping("/statistics/list/export")
    @NoAuthRequired
    public Result<PaginationResult<AchievementsOrgTargetStatisticsListVO>> statisticsListExport(HttpServletRequest request, AchievementsItemStatisticsListParam query) {
        setExcelCallBackParam(request, query);
        String deptIds = query.getDeptIdStr();
        String indicatorLibraryCodes = query.getIndicatorLibraryCodeStr();
        String cycleNumStr = query.getCycleNumStr();
        if (StringUtils.isEmpty(deptIds)) {
            return Result.ok();
        }
        List<Long> deptIdList = deptIds.split(",").length > 0 ? Arrays.stream(deptIds.split(",")).map(Long::parseLong).collect(Collectors.toList()) : new ArrayList<>();
        query.setDeptIds(deptIdList);

        if (StringUtils.isNotEmpty(indicatorLibraryCodes)) {
            List<String> indicatorLibraryIdList = indicatorLibraryCodes.split(",").length > 0 ? Arrays.stream(indicatorLibraryCodes.split(",")).collect(Collectors.toList()) : new ArrayList<>();
            query.setIndicatorLibraryCodes(indicatorLibraryIdList);
        }
        List<Integer> cycleNumAll = new ArrayList<>();
        if (StringUtils.isNotEmpty(cycleNumStr)) {
            List<String> cycleNumList = cycleNumStr.split(",").length > 0 ? Arrays.stream(cycleNumStr.split(",")).collect(Collectors.toList()) : new ArrayList<>();
            // 判断集合是否包含1 2 3 4 5 6 7 8 9 10 11 12 one 或 two 或 three 或 four 转成数字1 2 3 4 5 6 7 8 9 10 11 12
            List<Integer> cycleNumsByQuarter = cycleNumList.stream().map(cycleNum -> {
                if (cycleNum.equals("one")) {
                    return 1;
                } else if (cycleNum.equals("two")) {
                    return 2;
                } else if (cycleNum.equals("three")) {
                    return 3;
                } else if (cycleNum.equals("four")) {
                    return 4;
                }
                return Integer.parseInt(cycleNum);
            }).collect(Collectors.toList());
            // 对cycleNumList 过滤掉 one two three four 转成数字集合List<Integer> cycleNumListByMonth
            List<Integer> cycleNumListByMonth = cycleNumList.stream().filter(cycleNum -> !cycleNum.equals("one") && !cycleNum.equals("two") && !cycleNum.equals("three") && !cycleNum.equals("four")).map(Integer::parseInt).collect(Collectors.toList());

            cycleNumAll.addAll(cycleNumsByQuarter);
            cycleNumAll.addAll(cycleNumListByMonth);
            query.setCycleNums(cycleNumAll);
        }

        //列表查询
        List<AchievementsOrgTargetStatisticsListDTO> list = achievementsOrgTargetItemService.statisticsList(query);

        if (StringUtils.isNotEmpty(cycleNumStr)) {
            List<String> cycleNumList = Arrays.asList(cycleNumStr.split(","));
            List<AchievementsOrgTargetStatisticsListDTO> filteredList = new ArrayList<>();

            for (String cycleNum : cycleNumList) {
                for (AchievementsOrgTargetStatisticsListDTO dto : list) {
                    int cycleNumValue = dto.getCycleNum();
                    int cycleType = dto.getCycleType();

                    // 处理总周期
                    if (cycleNumValue == 0 && cycleNum.equals("0")) {
                        filteredList.add(dto); // 全年
                        continue;
                    }

                    // 处理月份
                    if ((cycleType == 2 || cycleType == 3) && cycleNum.equals(String.valueOf(cycleNumValue))) {
                        filteredList.add(dto); // 1月到12月
                    } else if (cycleType == 1) {
                        String quarter = null;
                        switch (cycleNumValue) {
                            case 1:
                                quarter = "one";
                                break;
                            case 2:
                                quarter = "two";
                                break;
                            case 3:
                                quarter = "three";
                                break;
                            case 4:
                                quarter = "four";
                                break;
                        }
                        if (cycleNum.equals(quarter)) {
                            filteredList.add(dto); // 1季度到4季度
                        }
                    }
                }
            }

            list = filteredList; // 更新过滤后的列表
        }


        // 根据部门id排序 部门id相同再根据周期升序
        list.sort(Comparator.comparing(AchievementsOrgTargetStatisticsListDTO::getDeptId).thenComparing(AchievementsOrgTargetStatisticsListDTO::getCycleNum));
        
        //转换为VO
        List<AchievementsOrgTargetStatisticsListVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetStatisticsListVO.class, list);

        Page<AchievementsOrgTargetStatisticsListVO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PaginationResult<AchievementsOrgTargetStatisticsListVO> paginationResult = PageUtil.get(resultDTOList, page, query);
        paginationResult.setResults(resultDTOList);

        return Result.ok(paginationResult);
    }


    /**
     * 目标新增(指标批量新增)
     * @param param
     * @return
     */
    @PostMapping("/add")
    @ForbidRepeatClick
    @NoAuthRequired
    public Result<Boolean> add(@RequestBody @Valid AchievementsOrgTargetParam param) {
        AchievementsOrgTargetAddDTO addDTO = BeanUtils.convert(param, AchievementsOrgTargetAddDTO.class);
        achievementsOrgTargetItemService.add(addDTO);
        return Result.ok();
    }

    /**
     * 目标编辑(指标批量编辑)
     * @param param
     * @return
     */
    @PostMapping("/update")
    @ForbidRepeatClick
    @NoAuthRequired
    public Result<Boolean> update(@RequestBody AchievementsOrgTargetParam param) {
        AchievementsOrgTargetAddDTO addDTO = BeanUtils.convert(param, AchievementsOrgTargetAddDTO.class);
        achievementsOrgTargetItemService.update(addDTO);
        return Result.ok();
    }

    /**
     * 指标看板展开列表
     * @param id(父类指标id)
     * @return
     */
    @PostMapping("/boardList")
    @NoAuthRequired
    public Result<List<AchievementsOrgTargetItemVO>> boardList(Long id,String userCode){
        List<TargetItemBoardListDTO> list =achievementsOrgTargetItemService.boardList(id,userCode);
        return Result.ok(BeanUtils.convert(AchievementsOrgTargetItemVO.class, list));
    }


    /**
     * 获取指标状态
     * @param eventId
     * @param deptId
     * @return 0 未提交 1 已提交 2 已确认结果
     */
    @PostMapping("/targetList")
    @NoAuthRequired
    public Result<AchievementsOrgTargetVO> targetList(@RequestParam Long eventId, @RequestParam Long deptId){
        AchievementsOrgTargetDTO dto = achievementsOrgTargetItemService.targetList(eventId,deptId);
        AchievementsOrgTargetVO vo = BeanUtils.convert(dto,AchievementsOrgTargetVO.class);
        return Result.ok(vo);
    }

    /**
     * 完成结果列表 
     * @param query
     * @return
     */
    @PostMapping("/resultList")
    public Result<List<AchievementsOrgTargetItemVO>> resultList(@RequestBody AchievementsItemParam query){

        AchievementsItemListDTO listDTO = BeanUtils.convert(query, AchievementsItemListDTO.class);
        //列表查询
        List<AchievementsOrgTargetItemDTO> list = achievementsOrgTargetItemService.resultList(listDTO);

        //转换为VO
        List<AchievementsOrgTargetItemVO> resultDTOList = BeanUtils.convert(AchievementsOrgTargetItemVO.class, list);

        return Result.ok(resultDTOList);
    }


    /**
     * 现有指标导入
     *
     * @param request
     * @return
     */
    @PostMapping("/import")
    public Result<List<AchievementsOrgTargetItemImportDTO>> importItem(HttpServletRequest request, AchievementsOrgTargetItemImportParam param) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        // 数据解析
        log.info("callBackParam:{}", JSON.toJSONString(callBackParam));
        List<AchievementsOrgTargetItemImportDTO> importList = JSON.parseArray(callBackParam.getPageData(), AchievementsOrgTargetItemImportDTO.class);
        if (CollectionUtils.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }AchievementsOrgTargetItemImportParamDTO achievementsOrgTargetItemImportParamDTO = new AchievementsOrgTargetItemImportParamDTO();
        achievementsOrgTargetItemImportParamDTO.setItem(importList);
        achievementsOrgTargetItemImportParamDTO.setEventId(param.getEventId());
        achievementsOrgTargetItemImportParamDTO.setDeptId(param.getDeptId());
        List<AchievementsOrgTargetItemImportDTO> failList = achievementsOrgTargetItemService.importItem(achievementsOrgTargetItemImportParamDTO);
        return Result.ok(failList);
    }
    /**
     * 确认结果
     */
    @PostMapping("/confirmResult")
    @ForbidRepeatClick
    public Result confirmResult(@RequestBody checkMergeParam param){
        achievementsOrgTargetItemService.confirmResult(param);
        return Result.ok();
    }

    /**
     * 复制考核组织
     * @param param
     */
    @PostMapping("/copyOrgTarget")
    @ForbidRepeatClick
    public Result copyOrgTarget(@RequestBody @Valid CopyOrgTargetParam param){
        achievementsOrgTargetService.copyOrgTarget(param);
        return Result.ok();
    }

    /**
     * 设置组织考核
     * @param param
     */
    @PostMapping("/setOrgTarget")
    public Result setOrgTarget(@RequestBody AchievementsSetOrgTargetParam param){
        achievementsOrgTargetService.setOrgTarget(param);
        return Result.ok();
    }

    /**
     * 新增/更新设置组织考核
     */
    @PostMapping("/saveOrUpdateOrgTarget")
    @ForbidRepeatClick
    public Result updateOrgTarget(@RequestBody @Valid AchievementsSetOrgTargetParam param){
        achievementsOrgTargetService.updateOrgTarget(param);
        return Result.ok();
    }

    /**
     * 查询历史设置
     */
    @PostMapping("/historyOrgTarget")
    public Result<AchievementsOrgTargetVO> historyOrgTarget(@RequestBody HistoryOrgTargetParam param){
        AchievementsOrgTargetDTO dto = achievementsOrgTargetService.queryHistoryOrgTarget(param);
        AchievementsOrgTargetVO vo = BeanUtils.convert(dto,AchievementsOrgTargetVO.class);
        return Result.ok(vo);
    }

    /**
     * 查询组织考核设置
     */
    @PostMapping("/queryOrgTarget")
    @NoAuthRequired
    public Result<List<AchievementsOrgTargetVO>> queryOrgTarget(@RequestBody QueryOrgTargetParam param){
        List<AchievementsOrgTargetDTO> dtoList = achievementsOrgTargetService.queryOrgTarget(param);
        List<AchievementsOrgTargetVO> voList = BeanUtils.convert(AchievementsOrgTargetVO.class, dtoList);
        return Result.ok(voList);
    }

    /**
     * 查询组织考核设置-快照组织
     */
    @PostMapping("/queryOrgTargetSnapshot")
    @NoAuthRequired
    public Result<List<AchievementsOrgTargetVO>> queryOrgTargetSnapshot(@RequestBody QueryOrgTargetParam param){
        List<AchievementsOrgTargetDTO> dtoList = achievementsOrgTargetService.queryOrgTargetSnapshot(param);
        List<AchievementsOrgTargetVO> voList = BeanUtils.convert(AchievementsOrgTargetVO.class, dtoList);
        return Result.ok(voList);
    }

    /**
     * 查询组织考核设置-导出
     */
    @PostMapping("/queryOrgTarget/export")
    @NoAuthRequired
    public Result<PaginationResult<AchievementsOrgTargetVO>> queryOrgTargetExport(HttpServletRequest request, QueryOrgTargetParam param){
        setExcelCallBackParam(request, param);
        List<AchievementsOrgTargetDTO> dtoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getSnapshotVersion())) {
            dtoList = achievementsOrgTargetService.queryOrgTargetSnapshot(param);
        } else {
            dtoList = achievementsOrgTargetService.queryOrgTarget(param);
        }
        List<AchievementsOrgTargetVO> voList = BeanUtils.convert(AchievementsOrgTargetVO.class, dtoList);
        Page<AchievementsOrgTargetVO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PaginationResult<AchievementsOrgTargetVO> paginationResult = PageUtil.get(voList, page, param);
        paginationResult.setResults(voList);
        return Result.ok(paginationResult);
    }

    /**
     * 运营组织树列表
     */
    @PostMapping("/operation/deptList")
    public Result<List<EntOcTreeApiDTO>> operationDeptList(){
        List<EntOcTreeApiDTO> deptTreeList = achievementsOrgTargetService.operationDeptList();
        return Result.ok(deptTreeList);
    }

    /**
     * 组织总得分
     */
    @PostMapping("/orgTotalScore")
    @NoAuthRequired
    public Result<List<AchievementDeptTotalScoreVO>> orgTotalScore(@RequestBody List<Long> targetIds){
        return Result.ok(achievementsOrgTargetService.orgTotalScore(targetIds));
    }

    /**
     * 目标提交
     */
    @PostMapping("/submit")
    @ForbidRepeatClick
    public Result submit(@RequestBody SubmitTargetParam param){
        achievementsOrgTargetService.submit(param);
        return Result.ok();
    }

}
