package com.imile.hrms.web.achievement.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 员工考核操作日志表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class AchievementsEmployeeAppraisalOperationLogListVO {

    /**
     * 操作日志ID
     */
    private Long id;

    /**
     * 员工考核ID
     */
    private Long employeeAppraisalId;

    /**
     * 操作标题
     */
    private String operationTitle;

    /**
     * 操作码
     *
     * @see com.imile.hrms.common.enums.achievement.AchievementsEmployeeAppraisalOperationCodeEnum
     */
    private String operationCode;

    /**
     * 操作描述
     */
    private String operationDescription;

    /**
     * 操作描述
     */
    private String operationDescriptionEn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
}
