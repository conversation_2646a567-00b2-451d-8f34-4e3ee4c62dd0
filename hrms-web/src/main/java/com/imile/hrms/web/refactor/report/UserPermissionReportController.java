package com.imile.hrms.web.refactor.report;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.service.refactor.report.param.UserPermissionReportExportParam;
import com.imile.hrms.service.refactor.report.param.UserPermissionReportPageParam;
import com.imile.hrms.service.refactor.report.result.UserPermissionReportListBO;
import com.imile.hrms.service.refactor.report.service.UserPermissionReportService;
import com.imile.hrms.web.BaseController;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.HRMSRoleMenuDTO;
import com.imile.permission.api.dto.HRMSUserRoleDTO;
import com.imile.permission.api.dto.MenuTreeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 人员权限报表
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Validated
@RestController
@RequestMapping("user/permission/report")
@Slf4j
public class UserPermissionReportController extends BaseController {

    @Resource
    private UserPermissionReportService userPermissionReportService;

    /**
     * 列表
     */
    @PostMapping("/list")
    public Result<PaginationResult<UserPermissionReportListBO>> list(@RequestBody UserPermissionReportPageParam param) {
        return Result.ok(userPermissionReportService.list(param));
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public Result<PaginationResult<UserPermissionReportListBO>> export(HttpServletRequest request, UserPermissionReportExportParam param) {
        super.setExcelCallBackParam(request, param);
        return Result.ok(userPermissionReportService.list(param.convert()));
    }

    /**
     * 查询用户角色
     */
    @GetMapping("/role")
    Result<HRMSUserRoleDTO> getUserRole(@NotBlank(message = ValidCodeConstant.NOT_BLANK) String userCode) {
        return Result.ok(userPermissionReportService.getUserRole(userCode));
    }

    /**
     * 查全量菜单
     */
    @GetMapping("all/menu/tree")
    Result<List<MenuTreeDTO>> getMenuTree() {
        return Result.ok(userPermissionReportService.getMenuTree());
    }

    /**
     * 通过角色查有权限菜单
     */
    @GetMapping("/permission/menu")
    Result<List<HRMSRoleMenuDTO>> getPermissionMenuByRoleId(@NotNull(message = ValidCodeConstant.NOT_NULL) Long roleId) {
        return Result.ok(userPermissionReportService.getPermissionMenuByRoleId(roleId));
    }

    /**
     * 查询有权限主数据(部门)
     */
    @GetMapping("/dept/permission")
    Result<DataPermissionApiDTO> getDeptMasterPermission(@NotBlank(message = ValidCodeConstant.NOT_BLANK) String userCode) {
        return Result.ok(userPermissionReportService.getDeptMasterPermission(userCode));
    }
}
