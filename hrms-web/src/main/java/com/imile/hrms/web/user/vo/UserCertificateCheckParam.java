package com.imile.hrms.web.user.vo;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UserCertificateCheckParam implements Serializable {
    private static final long serialVersionUID = 8683240555480994419L;

    /**
     * 证件类型
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String certificateType;
    /**
     * 证件编码
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String certificateCode;

    /**
     * 需要二次入职的类型
     * null:不需要二次入职
     * DIMISSION:离职后再二次入职
     * CANCEL_ENTRY：已放弃入职后再二次入职
     */
    private String entryAgainType;
}
