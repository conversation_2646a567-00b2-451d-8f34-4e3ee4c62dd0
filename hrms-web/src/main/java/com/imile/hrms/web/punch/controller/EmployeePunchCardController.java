package com.imile.hrms.web.punch.controller;

import cn.hutool.core.bean.BeanUtil;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.query.BaseQuery;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.CryptoUtil;
import com.imile.hrms.dao.punch.dto.AttendancePunchConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsPunchCardRecordDTO;
import com.imile.hrms.dao.punch.dto.UserPunchListExportDTO;
import com.imile.hrms.dao.punch.param.CheckUserPunchParam;
import com.imile.hrms.dao.punch.param.PunchParam;
import com.imile.hrms.dao.punch.param.UserPunchParam;
import com.imile.hrms.dao.punch.query.UserPunchCardRecordQuery;
import com.imile.hrms.dao.punch.query.UserPunchConfigQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.punch.EmployeePunchCardService;
import com.imile.hrms.service.punch.dto.PunchCardRecordQueryDTO;
import com.imile.hrms.service.punch.dto.PunchCardRecordResultDTO;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.punch.param.EmployeePunchCardExportParam;
import com.imile.hrms.web.punch.vo.AttendancePunchConfigVO;
import com.imile.hrms.web.punch.vo.HrmsPunchCardRecordVO;
import com.imile.hrms.web.punch.vo.PunchCardRecordVO;
import com.imile.hrms.web.punch.vo.UserPunchInfoVO;
import com.imile.hrms.web.user.vo.EmployeePunchRecordListExportVO;
import com.imile.hrms.web.user.vo.UserPunchExportListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @menu 打卡功能记录
 * @email <EMAIL>
 * @date 2022/2/14
 */

@Slf4j
@RestController
@RequestMapping("/punch/record/card")
public class EmployeePunchCardController extends BaseController {

    @Autowired
    private EmployeePunchCardService employeePunchCardService;

    @Autowired
    private ImileRedisClient redisClient;

    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private ConverterService converterService;


    private static final String PREFIX = "biz:punch:employeePunch:";


    private static final Long FIFTEEN_SECOND = 15L;


    /**
     * 获取该用户这一天的打卡配置
     *
     * @param userPunchConfigQuery
     * @return
     */
    @PostMapping("/getConfig")
    @NoHrmsLoginAuthRequired
    @NoLoginAuthRequired
    public Result<AttendancePunchConfigVO> getConfig(@RequestBody UserPunchConfigQuery userPunchConfigQuery) {
        AttendancePunchConfigDTO attendancePunchConfig = employeePunchCardService.getAttendancePunchConfig(userPunchConfigQuery);
        AttendancePunchConfigVO attendancePunchConfigVO = BeanUtil.copyProperties(attendancePunchConfig, AttendancePunchConfigVO.class);
        return Result.ok(attendancePunchConfigVO);
    }


    @PostMapping("/punchRecord")
    public Result<PaginationResult<PunchCardRecordVO>> listPunchCardRecord(EmployeePunchCardExportParam param, HttpServletRequest request) {
        this.setExcelCallBackParam(request, param);
        PunchCardRecordQueryDTO queryDTO = new PunchCardRecordQueryDTO();
        queryDTO.setCountry(param.getCountry());
        queryDTO.setStartDate(param.getStartDate());
        queryDTO.setEndDate(param.getEndDate());
        PaginationResult<PunchCardRecordResultDTO> listPunchList = employeePunchCardService.listPunchCardRecord(queryDTO);
        PaginationResult<PunchCardRecordVO> voPaginationResult = this.convertPage(listPunchList, PunchCardRecordVO.class);
        converterService.withAnnotation(voPaginationResult.getResults());
        return Result.ok(voPaginationResult);
    }

    /**
     * 获取本日打卡记录信息
     *
     * @return
     */
    @PostMapping("getPunchInfo")
    @NoHrmsLoginAuthRequired
    @NoLoginAuthRequired
    public Result<UserPunchInfoVO> getPunchInfo(@RequestBody @Validated CheckUserPunchParam checkUserPunchParam) {
      /*  log.info("=====>userCode:{},checkUserPunchParam:{}.", RequestInfoHolder.getUserCode(),checkUserPunchParam);
        UserPunchInfoDTO userPunchInfo = employeePunchCardService.getUserPunchInfo(checkUserPunchParam);
        UserPunchInfoVO userPunchInfoVO = BeanUtil.copyProperties(userPunchInfo, UserPunchInfoVO.class);*/
        UserPunchInfoVO userPunchInfoVO = new UserPunchInfoVO();
        return Result.ok(userPunchInfoVO);
    }

    /**
     * 打卡记录列表
     *
     * @param query
     * @return
     */
    @PostMapping("/list")
    public Result<PaginationResult<HrmsPunchCardRecordVO>> list(@RequestBody UserPunchCardRecordQuery query) {
        PaginationResult<HrmsPunchCardRecordDTO> result = employeePunchCardService.listRecord(query);
        PaginationResult<HrmsPunchCardRecordVO> voPaginationResult = this.convertPage(result, HrmsPunchCardRecordVO.class);
        converterService.withAnnotation(voPaginationResult.getResults());
        return Result.ok(voPaginationResult);
    }

    /**
     * 打卡记录统计导出
     *
     * @param query
     * @return
     */
    @PostMapping("list/export")
    public Result<PaginationResult<UserPunchExportListVO>> listExport(HttpServletRequest request, UserPunchCardRecordQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<UserPunchListExportDTO> res = employeePunchCardService.export(query);
        PaginationResult<UserPunchExportListVO> resultVO = this.convertPage(res, UserPunchExportListVO.class);
        List<UserPunchExportListVO> userPunchExportListVOS = converterService.withAnnotation(resultVO.getResults());
        resultVO.setResults(userPunchExportListVOS);
        return Result.ok(resultVO);
    }

    /**
     * 打卡记录导出
     *
     * @param query
     * @return
     */
    @PostMapping("list/record/export")
    public Result<PaginationResult<EmployeePunchRecordListExportVO>> punchRecordListExport(HttpServletRequest request, UserPunchCardRecordQuery query) {
        setExcelCallBackParam(request, query);
        PaginationResult<HrmsPunchCardRecordDTO> res = employeePunchCardService.punchRecordListExport(query);
        PaginationResult<EmployeePunchRecordListExportVO> resultVO = this.convertPage(res, EmployeePunchRecordListExportVO.class);
        List<EmployeePunchRecordListExportVO> employeePunchRecordListExportVOList = converterService.withAnnotation(resultVO.getResults());
        resultVO.setResults(employeePunchRecordListExportVOList);
        return Result.ok(resultVO);
    }

    /**
     * 校验及解密
     *
     * @return
     */
    private UserPunchParam decodeParam(PunchParam punchParam) {
        //校验时间戳参数 ,系统获取到的时间戳参数不能大于前端传的时间戳5秒
        Long timestrapt = punchParam.getTimestrapt();

        //系统获取到的时间戳参数不能大于前端传的时间戳60秒
        if (hrmsProperties.getPunch().isPunchTimeParamSwitch()) {
            long offset = System.currentTimeMillis() - timestrapt;
            BusinessLogicException.checkTrue(offset > hrmsProperties.getPunch().getTimeDiff() || offset < -hrmsProperties.getPunch().getTimeDiff(), HrmsErrorCodeEnums.TIME_STAMP_ERROR.getCode(), HrmsErrorCodeEnums.TIME_STAMP_ERROR.getDesc());
        }
        //校验签名
        String sign = CryptoUtil.sign(String.valueOf(timestrapt), punchParam.getPunchData());
        BusinessLogicException.checkTrue(!sign.equals(punchParam.getSign()), HrmsErrorCodeEnums.SIGN_ERROR.getCode(), HrmsErrorCodeEnums.SIGN_ERROR.getDesc());
        //解密
        UserPunchParam userPunchParam = CryptoUtil.aseDecryptByDefaultKey(punchParam.getPunchData(), UserPunchParam.class);
        return userPunchParam;

    }


    public ExcelCallBackParam setExcelCallBackParam(HttpServletRequest request, BaseQuery query) {
        //excel回调返回数据
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);

        //业务分页入参适配
        query.setCurrentPage(callBackParam.getPageNum());
        query.setShowCount(callBackParam.getPageSize());
        query.setTotalResult(callBackParam.getTotalCount());

        //是否查询count
        Boolean isCount = (query.getCurrentPage() == 1);
        query.setCount(isCount);
        query.setPageEnabled(true);
        return callBackParam;
    }

}
