package com.imile.hrms.web.account.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 账号基本类型 相关属性
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/6
 */
@Data
public class AccountTypeVO implements Serializable {
    private static final long serialVersionUID = 6697699633816750561L;

    /**
     * 标识员工工号、员工邮箱账号、员工TMS账号等
     */
    private String accountTypeCode;

    /**
     * 是否勾选，复选框默认值
     */
    private Boolean checked;

    /**
     * 是否允许操作，当为false时，复选框取checked的默认值后不允许修改
     */
    private Boolean allowOperation;

    /**
     * 账号类型中文名
     */
    @JsonIgnore
    private String accountTypeNameCn;
    /**
     * 账号类型英文名
     */
    @JsonIgnore
    private String accountTypeNameEn;
    /**
     * 根据系统语言自动取对应的中英文名，前端展示取该字段
     */
    private String accountTypeNameByLang;

    public String getAccountTypeNameByLang() {
        if (StringUtils.isNotBlank(accountTypeNameByLang)) {
            return accountTypeNameByLang;
        }
        return RequestInfoHolder.isChinese() ? this.accountTypeNameCn : this.getAccountTypeNameEn();
    }
}
