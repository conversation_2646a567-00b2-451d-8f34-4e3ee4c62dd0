package com.imile.hrms.web.salary.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
public class ConfigItemVO {
    /**
     * 唯一标识
     */
    private Long id;
    /**
     * 配置项名称
     */
    private String configName;
    /**
     * 基数缴纳类型 基础薪资比例、各项自定义、固定值、薪资总额
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.BASE_PAYMENT_TYPE, ref = "basePaymentTypeDesc")
    private String basePaymentType;
    private String basePaymentTypeDesc;
    /**
     * 缴纳基数值 有可能是数值，也可能是比例
     */
    private BigDecimal baseValue;
    /**
     * 单位缴纳比例
     */
    private BigDecimal orgPaymentRate;
    /**
     * 个人缴纳比例
     */
    private BigDecimal personPaymentRate;


}
