package com.imile.hrms.web.travelExpenses.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 差旅费用表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
public class HrmsTravelExpensesListVO{

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家编码
     */
    private String code;

    /**
     * 已配置费用项
     */
    private String expensesName;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 待生效时间
     */
    private Date futureTime;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 最后更新人
     */
    private String lastUpdUserCode;

    /**
     * 最后更新人
     */
    private String lastUpdUserName;

    /**
     * 编辑类型：0 编辑/查看 1 编辑待生效配置
     */
    private String updateType;

    /**
     * 币种
     */
    private String currency;

}
