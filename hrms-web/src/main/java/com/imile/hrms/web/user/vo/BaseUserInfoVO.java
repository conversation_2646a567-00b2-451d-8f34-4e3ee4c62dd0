package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息展示基础类
 *
 * <AUTHOR>
 */
@Data
public class BaseUserInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 姓名
     */
    private String userName;

    private Integer isDriver;
    /**
     * 员工头像地址
     */
    @HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;

    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 姓名全拼
     */
    private String userNamePinyin;

    /**
     * 所属国家名称
     */
    private String countryName;
    /**
     * 所属国家编码
     */
    private String countryCode;
    /**
     * 性别 性别(1:男,2:女)
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SEX, ref = "sexDesc")
    private Integer sex;
    /**
     * 性别 性别(1:男,2:女)
     */
    private String sexDesc;
    /**
     * 是否必须开通英文名
     */
    private boolean mustHandlerUserNameEn;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="Asia/Shanghai")
    private Date birthday;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 国际区号
     */
    private String countryCallingCode;

    private Long countryCallingId;

    private String employeeType;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date entryDate;


}
