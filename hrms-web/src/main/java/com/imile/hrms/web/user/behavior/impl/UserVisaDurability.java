package com.imile.hrms.web.user.behavior.impl;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.common.enums.DurabillyTypeEnum;
import com.imile.hrms.dao.user.dto.UserInfoExpandParamDTO;
import com.imile.hrms.dao.user.dto.UserVisaParamDTO;
import com.imile.hrms.dao.user.model.UserVisaDO;
import com.imile.hrms.manage.user.UserVisaManage;
import com.imile.hrms.service.user.UserVisaService;
import com.imile.hrms.service.user.param.UserVisaSaveParam;
import com.imile.hrms.web.user.behavior.Durability;
import com.imile.util.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10
 */
@Component
public class UserVisaDurability implements Durability {

    @Autowired
    private UserVisaManage userVisaManage;

    @Autowired
    private UserVisaService userVisaService;

    @Override
    public Boolean durability(UserInfoExpandParamDTO param) {
        List<UserVisaParamDTO> userVisaList = param.getUserVisaList();
        DataDifferHolder<UserVisaDO> userVisaDifferHolder
                = userVisaService.differ(param.getUserId(), BeanUtils.convert(UserVisaSaveParam.class, userVisaList));
        userVisaManage.doSave(userVisaDifferHolder);
        return Boolean.TRUE;
    }

    @Override
    public String getDurabilly() {
        return DurabillyTypeEnum.USER_VISA_INFO.getCode();
    }
}
