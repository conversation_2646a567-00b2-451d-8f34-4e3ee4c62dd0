package com.imile.hrms.web.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-29
 * @version: 1.0
 */
@Data
public class VendorDriverTypeParam implements Serializable {
    private static final long serialVersionUID = -8879359182739680304L;
    /**
     * 当前用户id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;
}
