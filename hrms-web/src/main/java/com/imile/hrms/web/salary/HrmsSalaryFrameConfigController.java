package com.imile.hrms.web.salary;


import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.service.salary.HrmsSalaryFrameConfigService;
import com.imile.hrms.service.salary.param.HrmsSalaryFrameConfigListParam;
import com.imile.hrms.service.salary.param.HrmsSalaryFrameConfigUpdateParam;
import com.imile.hrms.service.salary.vo.HrmsSalaryFrameConfigListVO;
import com.imile.hrms.service.salary.vo.HrmsSalaryFrameConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.imile.hrms.web.BaseController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 薪资框架配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@RestController
@RequestMapping("/salary/frame/config")
public class HrmsSalaryFrameConfigController extends BaseController {

    @Autowired
    private HrmsSalaryFrameConfigService hrmsSalaryFrameConfigService;

    /**
     * 列表
     *
     * @param param
     * @return
     */
    @PostMapping("/list")
    public Result<HrmsSalaryFrameConfigVO> list(@RequestBody HrmsSalaryFrameConfigListParam param) {
        return Result.ok(hrmsSalaryFrameConfigService.list(param));
    }

    /**
     * 编辑
     *
     * @param param
     * @return
     */
    @PostMapping("/update")
    public Result<String> update(@RequestBody HrmsSalaryFrameConfigUpdateParam param) {
        hrmsSalaryFrameConfigService.update(param);
        return Result.ok();
    }

    /**
     * 导出 26093
     */
    @PostMapping("/export")
    public Result<PaginationResult<HrmsSalaryFrameConfigListVO>> export(HttpServletRequest request, HrmsSalaryFrameConfigListParam param) {
        setExcelCallBackParam(request, param);
        param.setShowCount(50000);
        PaginationResult<HrmsSalaryFrameConfigListVO> mapPaginationResult = hrmsSalaryFrameConfigService.list(param).getResult();
        return Result.ok(mapPaginationResult);
    }

    /**
     * 批量导入模版生成
     */
    @PostMapping("/template/export")
    public Result<String> templateExport(HttpServletRequest request, HrmsSalaryFrameConfigListParam param) {
        param.setShowCount(50000);
        return Result.ok(hrmsSalaryFrameConfigService.templateExport(param));
    }

    /**
     * 批量导入薪资框架配置  26094
     */
    @PostMapping("/import")
    public Result<List<Map<String, String>>> importSalaryFrameConfig(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        // 数据解析
        List<Map<String, String>> failMap = hrmsSalaryFrameConfigService.importSalaryFrameConfig(callBackParam.getPageData());
        return Result.ok(failMap);
    }

}
