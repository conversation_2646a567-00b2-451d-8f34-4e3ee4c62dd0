package com.imile.hrms.web.freelancer.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础信息
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverDeliveryPreferenceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 每天工作时长
     */
    private Integer dailyWorkingHours;

    /**
     * 网点
     */
    private Long ocId;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 派送偏好区域
     */
    private List<FreelancerDriverDeliveryPreferenceZoneVO> preferredDeliveryArea;

    /**
     * 派送班次
     */
    private LinkedHashMap<String, List<String>> availableTimePeriod;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

}
