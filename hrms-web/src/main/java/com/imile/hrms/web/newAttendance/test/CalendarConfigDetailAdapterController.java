package com.imile.hrms.web.newAttendance.test;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigDetailQuery;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDetailAdapter;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/11 
 * @Description
 */
@RestController
@RequestMapping("/calendarConfigDetailAdapter")
public class CalendarConfigDetailAdapterController {

    @Resource
    private CalendarConfigDetailAdapter calendarConfigDetailAdapter;


    @PostMapping("/listLatestRecords")
    public List<HrmsAttendanceConfigDetailDO> listLatestRecords(@RequestBody AttendanceConfigDetailQuery configDetailQuery) {
        return calendarConfigDetailAdapter.listLatestRecords(configDetailQuery);
    }

    @PostMapping("/updateToOld")
    public void updateToOld(@RequestParam Long attendanceConfigId, @RequestParam List<Integer> yearList) {
        calendarConfigDetailAdapter.updateToOld(attendanceConfigId, yearList);
    }

    @PostMapping("/listRecords")
    public List<HrmsAttendanceConfigDetailDO> listRecords(@RequestBody AttendanceConfigDetailQuery configDetailQuery) {
        return calendarConfigDetailAdapter.listRecords(configDetailQuery);
    }

    @GetMapping("/selectListByConfigIds")
    public List<HrmsAttendanceConfigDetailDO> selectListByConfigIds(@RequestParam List<Long> attendanceConfigList) {
        return calendarConfigDetailAdapter.selectListByConfigIds(attendanceConfigList);
    }

    @GetMapping("/selectListByConfigIdsAndYear")
    public List<HrmsAttendanceConfigDetailDO> selectListByConfigIdsAndYear(@RequestParam List<Long> attendanceConfigList, @RequestParam List<Integer> years) {
        return calendarConfigDetailAdapter.selectListByConfigIdsAndYear(attendanceConfigList, years);
    }

    @PostMapping("/updateBatchById")
    public void updateBatchById(@RequestBody List<HrmsAttendanceConfigDetailDO> oldDataList) {
        calendarConfigDetailAdapter.updateBatchById(oldDataList);
    }

    @PostMapping("/saveBatch")
    public void saveBatch(@RequestBody List<HrmsAttendanceConfigDetailDO> oldDataList) {
        calendarConfigDetailAdapter.saveBatch(oldDataList);
    }
}
