package com.imile.hrms.web.training.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.service.training.dto.HrmsUserTrainingRecordDTO;
import com.imile.hrms.web.user.vo.AttachmentVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class HrmsTrainingInfoAddVO implements Serializable {
    private static final long serialVersionUID = -6524374246618429670L;

    /**
     * 培训主题
     */
    private String trainingTopic;

    /**
     * 培训类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.TRAINING_TYPE, ref = "trainingTypeDesc")
    private String trainingType;
    private String trainingTypeDesc;

    /**
     * 培训场次
     */
    private String trainingSession;

    /**
     * 培训日期
     */
    private Date date;

    /**
     * 培训开始时间
     */
    private Date trainingStartTime;

    /**
     * 培训结束时间
     */
    private Date trainingEndTime;

    /**
     * 培训时长
     */
    private BigDecimal trainingDuration;

    /**
     * 培训网点id
     */
    private Long deptId;

    /**
     * 培训讲师编码
     */
    private String trainerCode;

    /**
     * 培训讲师名称
     */
    private String trainerName;

    /**
     * 附件
     */
    private List<AttachmentVO> attachments;

    /**
     * 状态 未开始、签到完成、结果录入中、已完成
     */
    private String status;


    List<HrmsUserTrainingRecordDTO> trainingRecordList;

}
