package com.imile.hrms.web.other;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.service.log.OperationRecordService;
import com.imile.hrms.service.log.OperationSceneService;
import com.imile.hrms.service.log.OperationService;
import com.imile.hrms.service.log.param.OperationQueryParam;
import com.imile.hrms.service.log.param.OperationRecordQueryParam;
import com.imile.hrms.service.log.result.OperationBO;
import com.imile.hrms.service.log.result.OperationRecordBO;
import com.imile.hrms.service.log.result.OperationSceneBO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 操作记录
 *
 * <AUTHOR>
 * @since 2024/8/1
 */
@Validated
@RestController
@RequestMapping("/operation")
public class OperationController {

    @Resource
    private OperationSceneService operationSceneService;

    @Resource
    private OperationService operationService;
    @Resource
    private OperationRecordService operationRecordService;

    /**
     * 获取操作场景下拉列表
     *
     * @param operationModuleCode 操作模块编码
     * @return List<OperationSceneBO>
     */
    @GetMapping("/scene/select/list")
    public Result<List<OperationSceneBO>> getOperationSceneSelectList(
            @NotBlank(message = ValidCodeConstant.NOT_BLANK) @RequestParam String operationModuleCode) {
        return Result.ok(operationSceneService.getOperationSceneSelectList(operationModuleCode));
    }

    /**
     * 获取操作下拉列表
     *
     * @param param OperationQueryParam
     * @return List<OperationBO>
     */
    @PostMapping("/select/list")
    public Result<List<OperationBO>> getOperationSelectList(@Validated @RequestBody OperationQueryParam param) {
        return Result.ok(operationService.getOperationSelectList(param));
    }

    /**
     * 获取操作记录列表
     *
     * @param param OperationRecordQueryParam
     * @return PaginationResult<OperationRecordBO>
     */
    @PostMapping("/record/list")
    public Result<PaginationResult<OperationRecordBO>> getOperationRecordList(@Validated @RequestBody OperationRecordQueryParam param) {
        return Result.ok(operationRecordService.getOperationRecordList(param));
    }
}
