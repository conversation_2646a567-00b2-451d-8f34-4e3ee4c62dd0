/*
package com.imile.hrms.web.salary;

import com.alibaba.fastjson.JSON;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.salary.HrmsSalaryAttendanceUserInfoService;
import com.imile.hrms.service.salary.param.SalaryAttendanceMessageDeleteParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceTitleExportParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceUserDetailParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceUserLockParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceUserParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceUserRefreshParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceUserUpdateParam;
import com.imile.hrms.service.salary.param.SalarySchemeParam;
import com.imile.hrms.service.salary.vo.SalaryAttendanceUserDetailVO;
import com.imile.hrms.service.salary.vo.SalaryAttendanceUserVO;
import com.imile.hrms.service.salary.vo.SalarySchemeVO;
import com.imile.hrms.web.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

*/
/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/6 17:37
 * @version: 1.0
 *//*

@Slf4j
@RestController
@RequestMapping("/salary/attendance")
public class HrmsSalaryAttendanceUserInfoController extends BaseController {

    @Autowired
    private HrmsSalaryAttendanceUserInfoService hrmsSalaryAttendanceUserInfoService;
    @Autowired
    private ConverterService converterService;
    @Autowired
    private ImileRedisClient redissonClient;

    private static final String SYNC_LOCK = "HRMS:LOCK:SALARY_ATTENDANCE_SYNC:";

    */
/**
     * 计薪方案周期获取
     *//*

    @PostMapping("/scheme/list")
    public Result<List<SalarySchemeVO>> schemeList(@RequestBody @Validated SalarySchemeParam param) {
        List<SalarySchemeVO> resultVO = hrmsSalaryAttendanceUserInfoService.schemeList(param);
        return Result.ok(resultVO);
    }

    */
/**
     * 列表查询
     *//*

    @PostMapping("/list")
    public Result<PaginationResult<SalaryAttendanceUserVO>> list(@RequestBody @Validated SalaryAttendanceUserParam param) {
        PaginationResult<SalaryAttendanceUserVO> resultVO = hrmsSalaryAttendanceUserInfoService.list(param);
        List<SalaryAttendanceUserVO> registerUserInfoVOList = converterService.withAnnotation(resultVO.getResults());
        resultVO.setResults(registerUserInfoVOList);
        return Result.ok(resultVO);
    }

    */
/**
     * 用户考勤数据详情
     *//*

    @PostMapping("/detail")
    public Result<SalaryAttendanceUserDetailVO> detail(@RequestBody @Validated SalaryAttendanceUserDetailParam param) {
        SalaryAttendanceUserDetailVO resultVO = hrmsSalaryAttendanceUserInfoService.detail(param);
        converterService.withAnnotationForSingle(resultVO);
        return Result.ok(resultVO);
    }

    */
/**
     * 用户考勤数据编辑
     *//*

    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Validated SalaryAttendanceUserUpdateParam param) {
        hrmsSalaryAttendanceUserInfoService.update(param);
        return Result.ok(Boolean.TRUE);
    }


    */
/**
     * 用户考勤数据锁定&撤销锁定
     *//*

    @PostMapping("/lock")
    public Result<Boolean> lock(@RequestBody @Validated SalaryAttendanceUserLockParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        try {
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            hrmsSalaryAttendanceUserInfoService.lock(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(Boolean.TRUE);
    }

    */
/**
     * 用户考勤数据刷新
     *//*

    @PostMapping("/refresh")
    public Result<Boolean> refresh(@RequestBody @Validated SalaryAttendanceUserRefreshParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        try {
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), HrmsErrorCodeEnums.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            hrmsSalaryAttendanceUserInfoService.refresh(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(Boolean.TRUE);
    }

    */
/**
     * 消息提示关闭
     *//*

    @PostMapping("/message/delete")
    public Result<Boolean> messageDelete(@RequestBody @Validated SalaryAttendanceMessageDeleteParam param) {
        hrmsSalaryAttendanceUserInfoService.messageDelete(param);
        return Result.ok(Boolean.TRUE);
    }


    */
/**
     * 员工考勤数据导入模版生成
     *//*

    @PostMapping("/title/export")
    public Result<String> attendanceTitleExport(@RequestBody @Validated SalaryAttendanceTitleExportParam param) {
        String url = hrmsSalaryAttendanceUserInfoService.attendanceTitleExport(param);
        return Result.ok(url);
    }

    */
/**
     * 员工薪资考勤数据导入
     *//*

    @PostMapping("/import")
    public Result<List<Map<String, String>>> importUserAttendance(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        log.info("callBackParam:{}", JSON.toJSONString(callBackParam));
        List<Map<String, String>> failMap = hrmsSalaryAttendanceUserInfoService.importUserAttendance(callBackParam.getPageData());
        return Result.ok(failMap);
    }


    */
/**
     * 员工薪资考勤数据导出
     *//*

    @PostMapping("/export")
    public Result<PaginationResult<Map<String, String>>> exportUserAttendance(HttpServletRequest request, SalaryAttendanceUserParam param) {
        setExcelCallBackParam(request, param);
        param.setShowCount(50000);
        PaginationResult<Map<String, String>> mapPaginationResult = hrmsSalaryAttendanceUserInfoService.exportUserAttendance(param);
        return Result.ok(mapPaginationResult);
    }
}
*/
