package com.imile.hrms.web.achievement.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 指标积分表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Data
public class AchievementsTargetScoreVO {


    private Long id;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 目标明细id
     */
    private Long targetItemId;

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String month;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上一次得分
     */
    private BigDecimal latestScore;

    /**
     * 本次得分
     */
    private BigDecimal score;

    /**
     * 完成日期
     */
    private String comDate;

    /**
     * 扩展信息
     */
    private String extend;

    private Integer isLatest;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private Date createDate;

    @TableField(value = "create_user_code", fill = FieldFill.INSERT)
    private String createUserCode;

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "last_upd_date", fill = FieldFill.INSERT_UPDATE)
    private Date lastUpdDate;

    @TableField(value = "last_upd_user_code", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdUserCode;

    @TableField(value = "last_upd_user_name", fill = FieldFill.INSERT_UPDATE)
    private String lastUpdUserName;

}
