package com.imile.hrms.web.bpm.controller;

import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.dao.bpm.dto.ApprovalTipCountDTO;
import com.imile.hrms.dao.bpm.dto.ApprovalTipDTO;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.approval.dto.ApprovalConfigPreviewDTO;
import com.imile.hrms.service.bpm.BpmApprovalService;
import com.imile.hrms.service.bpm.dto.*;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.approval.vo.ApprovalConfigItemPreviewVO;
import com.imile.hrms.web.approval.vo.ApprovalConfigPreviewVO;
import com.imile.hrms.web.bpm.param.*;
import com.imile.hrms.web.bpm.vo.*;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-11-18
 * @version: 1.0
 */
@RestController
@RequestMapping("/bpm/approval")
public class BpmApprovalController extends BaseController {

    @Autowired
    private BpmApprovalService bpmApprovalService;
    @Autowired
    private ConverterService converterService;

    /**
     * 该国家是否开启审批流
     */
    @PostMapping("/template/judge")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalTemplateJudgeVO> templateJudge(@RequestBody TemplateJudgeDTO param) {
        ApprovalTemplateJudgeDTO approvalTemplateJudgeDTO = bpmApprovalService.templateJudge(param);
        return Result.ok(BeanUtils.convert(approvalTemplateJudgeDTO, ApprovalTemplateJudgeVO.class));
    }


    /**
     * 审批提示列表查询
     */
    @PostMapping("/tip/select")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<PaginationResult<ApprovalTipVO>> selectApprovalTip(@RequestBody ApprovalTipParam param) {
        ApprovalTipQueryDTO queryDTO = BeanUtils.convert(param, ApprovalTipQueryDTO.class);
        PaginationResult<ApprovalTipDTO> paginationResult = bpmApprovalService.selectApprovalTip(queryDTO);
        PaginationResult<ApprovalTipVO> resultVO = this.convertPage(paginationResult, ApprovalTipVO.class);
        return Result.ok(resultVO);
    }

    /**
     * 审批提示列表未读数量统计
     */
    @PostMapping("/tip/count")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalTipCountVO> selectApprovalTipCount(@RequestBody ApprovalTipCountParam param) {
        ApprovalTipCountDTO approvalTipCountDTO = bpmApprovalService.selectApprovalTipCount(param.getIsDriver());
        return Result.ok(BeanUtils.convert(approvalTipCountDTO, ApprovalTipCountVO.class));
    }

    /**
     * 审批提示列表操作更新
     */
    @PostMapping("/tip/update")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> updateApprovalTip(@RequestBody ApprovalTipUpdateParam param) {
        ApprovalTipUpdateDTO approvalTipUpdateDTO = BeanUtils.convert(param, ApprovalTipUpdateDTO.class);
        bpmApprovalService.updateApprovalTip(approvalTipUpdateDTO);
        return Result.ok(Boolean.TRUE);
    }


    /**
     * 审核中/驳回审批流在HR/TMS进行取消
     */
    @PostMapping("/cancel")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> approvalCancel(@RequestBody ApprovalCancelParam param) {
        ApprovalCancelDTO approvalCancelDTO = BeanUtils.convert(param, ApprovalCancelDTO.class);
        bpmApprovalService.approvalCancel(approvalCancelDTO);
        return Result.ok(Boolean.TRUE);
    }


    /**
     * HR新增预览
     */
    @PostMapping("/hr/add/preview")
    public Result<ApprovalConfigPreviewVO> getHrAddPreviewInfo(@RequestBody HrAddPreviewParam param) {
        HrAddPreviewDTO hrAddPreviewDTO = BeanUtils.convert(param, HrAddPreviewDTO.class);
        ApprovalConfigPreviewDTO processDTO = bpmApprovalService.getHrAddPreviewInfo(hrAddPreviewDTO);
        if (processDTO != null) {
            ApprovalConfigPreviewVO processVO = BeanUtils.convert(processDTO, ApprovalConfigPreviewVO.class);
            converterService.withAnnotationForSingle(processVO.getCreateApprovalUserDTO());
            List<ApprovalConfigItemPreviewVO> itemVOList = BeanUtils.convert(ApprovalConfigItemPreviewVO.class, processDTO.getItemDTOList());
            processVO.setItemVOList(itemVOList);
            if (CollectionUtils.isNotEmpty(itemVOList)) {
                for (ApprovalConfigItemPreviewVO configItemPreviewVO : itemVOList) {
                    converterService.withAnnotation(configItemPreviewVO.getApprovalUserDTOList());
                }
            }
            return Result.ok(processVO);

        }
        return Result.ok(new ApprovalConfigPreviewVO());
    }

    /**
     * HR离职预览
     */
    @PostMapping("/hr/dimission/preview")
    public Result<ApprovalConfigPreviewVO> getHrDimissionPreviewInfo(@RequestBody HrDimissionPreviewParam param) {
        ApprovalConfigPreviewDTO processDTO = bpmApprovalService.getHrDimissionPreviewInfo(param.getUserId());
        if (processDTO != null) {
            ApprovalConfigPreviewVO processVO = BeanUtils.convert(processDTO, ApprovalConfigPreviewVO.class);
            converterService.withAnnotationForSingle(processVO.getCreateApprovalUserDTO());
            List<ApprovalConfigItemPreviewVO> itemVOList = BeanUtils.convert(ApprovalConfigItemPreviewVO.class, processDTO.getItemDTOList());
            processVO.setItemVOList(itemVOList);
            if (CollectionUtils.isNotEmpty(itemVOList)) {
                for (ApprovalConfigItemPreviewVO configItemPreviewVO : itemVOList) {
                    converterService.withAnnotation(configItemPreviewVO.getApprovalUserDTOList());
                }
            }
            return Result.ok(processVO);

        }
        return Result.ok(new ApprovalConfigPreviewVO());
    }

    /**
     * HR调动预览
     */
    @PostMapping("/hr/transfer/preview")
    public Result<ApprovalConfigPreviewVO> getHrTransferPreviewInfo(@RequestBody HrTransferPreviewParam param) {
        HrTransferPreviewDTO previewDTO = BeanUtils.convert(param, HrTransferPreviewDTO.class);
        ApprovalConfigPreviewDTO processDTO = bpmApprovalService.getHrTransferPreviewInfo(previewDTO);
        if (processDTO != null) {
            ApprovalConfigPreviewVO processVO = BeanUtils.convert(processDTO, ApprovalConfigPreviewVO.class);
            converterService.withAnnotationForSingle(processVO.getCreateApprovalUserDTO());
            List<ApprovalConfigItemPreviewVO> itemVOList = BeanUtils.convert(ApprovalConfigItemPreviewVO.class, processDTO.getItemDTOList());
            processVO.setItemVOList(itemVOList);
            if (CollectionUtils.isNotEmpty(itemVOList)) {
                for (ApprovalConfigItemPreviewVO configItemPreviewVO : itemVOList) {
                    converterService.withAnnotation(configItemPreviewVO.getApprovalUserDTOList());
                }
            }
            return Result.ok(processVO);

        }
        return Result.ok(new ApprovalConfigPreviewVO());
    }


    /**
     * TMS员工管理NEW/TMS供应商员工管理发起新增预览
     */
    @PostMapping("/tms/add/preview")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalConfigPreviewVO> getTmsAddPreviewInfo(@RequestBody TmsAddPreviewParam param) {
        TmsAddPreviewDTO tmsAddPreviewDTO = BeanUtils.convert(param, TmsAddPreviewDTO.class);
        ApprovalConfigPreviewDTO processDTO = bpmApprovalService.getTmsAddPreviewInfo(tmsAddPreviewDTO);
        if (processDTO != null) {
            ApprovalConfigPreviewVO processVO = BeanUtils.convert(processDTO, ApprovalConfigPreviewVO.class);
            converterService.withAnnotationForSingle(processVO.getCreateApprovalUserDTO());
            List<ApprovalConfigItemPreviewVO> itemVOList = BeanUtils.convert(ApprovalConfigItemPreviewVO.class, processDTO.getItemDTOList());
            processVO.setItemVOList(itemVOList);
            if (CollectionUtils.isNotEmpty(itemVOList)) {
                for (ApprovalConfigItemPreviewVO configItemPreviewVO : itemVOList) {
                    converterService.withAnnotation(configItemPreviewVO.getApprovalUserDTOList());
                }
            }
            return Result.ok(processVO);

        }
        return Result.ok(new ApprovalConfigPreviewVO());
    }

    /**
     * TMS员工管理NEW/TMS供应商员工管理发起离职预览
     */
    @PostMapping("/tms/dimission/preview")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<ApprovalConfigPreviewVO> getTmsDimissionPreviewInfo(@RequestBody TmsDimissionPreviewParam param) {
        ApprovalConfigPreviewDTO processDTO = bpmApprovalService.getTmsDimissionPreviewInfo(param.getUserId());
        if (processDTO != null) {
            ApprovalConfigPreviewVO processVO = BeanUtils.convert(processDTO, ApprovalConfigPreviewVO.class);
            converterService.withAnnotationForSingle(processVO.getCreateApprovalUserDTO());
            List<ApprovalConfigItemPreviewVO> itemVOList = BeanUtils.convert(ApprovalConfigItemPreviewVO.class, processDTO.getItemDTOList());
            processVO.setItemVOList(itemVOList);
            if (CollectionUtils.isNotEmpty(itemVOList)) {
                for (ApprovalConfigItemPreviewVO configItemPreviewVO : itemVOList) {
                    converterService.withAnnotation(configItemPreviewVO.getApprovalUserDTOList());
                }
            }
            return Result.ok(processVO);

        }
        return Result.ok(new ApprovalConfigPreviewVO());
    }

    private BpmApprovalPreviewVO previewDataConversion(BpmApprovalPreviewDTO previewDTO) {
        BpmApprovalPreviewVO bpmApprovalPreviewVO = BeanUtils.convert(previewDTO, BpmApprovalPreviewVO.class);
        List<ApprovalEmptyRecordVO> approvalEmptyRecordVOList = new ArrayList<>();
        bpmApprovalPreviewVO.setApprovalEmptyRecordVOList(approvalEmptyRecordVOList);
        for (ApprovalEmptyRecordDTO approvalEmptyRecordDTO : previewDTO.getApprovalEmptyRecordDTOList()) {
            ApprovalEmptyRecordVO approvalEmptyRecordVO = BeanUtils.convert(approvalEmptyRecordDTO, ApprovalEmptyRecordVO.class);
            List<ApprovalUserInfoVO> approvalUserInfoVOList = BeanUtils.convert(ApprovalUserInfoVO.class, approvalEmptyRecordDTO.getApprovalUserInfoDTOList());
            approvalEmptyRecordVO.setApprovalUserInfoVOList(approvalUserInfoVOList);
            approvalEmptyRecordVOList.add(approvalEmptyRecordVO);
        }
        return bpmApprovalPreviewVO;
    }
}
