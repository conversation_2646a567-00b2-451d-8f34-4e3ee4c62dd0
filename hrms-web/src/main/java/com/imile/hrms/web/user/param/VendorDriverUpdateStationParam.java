package com.imile.hrms.web.user.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.common.enums.ValidCodeConstantExtension;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class VendorDriverUpdateStationParam implements Serializable {
    private static final long serialVersionUID = 6833964504828633206L;

    /**
     * 用户id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 网点编码
     */
    @NotBlank(message = ValidCodeConstantExtension.SYSTEM_UPGRADE)
    private String ocCode;

    /**
     * 编辑来源  待审核界面/审核通过界面
     */
    private String editSource;
}
