package com.imile.hrms.web.user.vo;

import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class TransferUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 汇报上级ID
     */
    private Long leaderId;

    /**
     * 原部门名称
     */
    private String deptName;

    /**
     * 原岗位名称
     */
    private String postName;


    /**
     * 原职级
     */
    private String rankSequence;

    /**
     * 原职级
     */
    private Integer gradeNo;

    /**
     * 原职等
     */
    private String jobGrade;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 工作性质
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    /**
     * 工作性质
     */
    private String employeeTypeDesc;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 手机号
     */
    private String phone;
    @HyperLink(ref = "profilePhotoUrlHttps")
    /**
     * 员工头像地址
     */
    private String profilePhotoUrl;
    /**
     * 头像
     */
    private String profilePhotoUrlHttps;
    /**
     * 司机
     */
    private Integer isDriver;


    private Long deptId;
}
