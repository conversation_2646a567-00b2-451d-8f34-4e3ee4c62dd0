package com.imile.hrms.web.punch.vo;

import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.dao.punch.dto.PunchCycleOffDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 员工班次配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendanceClassEmployeeConfigVO implements Serializable {

    /**
     * 用户
     */
    private Long userId;

    /**
     * workNo
     */
    private String workNo;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 国家
     */
    private String country;

    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 打卡方案ID
     */
    private Long punchConfigId;


    /**
     * 打卡方案名称
     */
    private String punchConfigName;


    /**
     * 打卡方案No
     */
    private String punchConfigNo;

    /**
     * 图片url
     */
    @HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;


    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;


    /**
     * 班次信息
     */
    private List<DayConfigTypeVO> classDetail;

    /**
     * 周期及可用天数信息  废弃
     */
    private List<PunchCycleOffDTO> punchCycleOffDTOList;

    /**
     * 该用户可以选择的该打卡规则可以选择的排班类型
     */
    List<HrmsAttendancePunchDayTypeVO> attendancePunchDayTypeList;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 是否是循环排班 1是
     */
    private Integer isCycleShift;

    /**
     * 是否排班限制 0:不限制 1:限制
     */
    private Integer schedulingLimit;
}
