package com.imile.hrms.web.attendance.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-5-11
 * @version: 1.0
 */
@Data
public class AttendanceBatchConcreteTypeParam implements Serializable {

    private static final long serialVersionUID = 4318119896790218636L;
    /**
     * 当前考勤类型 :出勤，缺勤，请假,销假
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String updateType;
}
