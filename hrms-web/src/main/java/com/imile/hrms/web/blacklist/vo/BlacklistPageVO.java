package com.imile.hrms.web.blacklist.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * 分页查询结果
 *
 * @ClassName BlacklistPageVO
 * <AUTHOR>
 * @Date 2023/5/15 19:43
 */
@Data
public class BlacklistPageVO {


    /**
     * 黑名单主键
     */
    private Long id;

    /**
     * 用户账号
     */
    private String employeeId;

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_STATUS, ref = "workStatusDesc")
    private String workStatus;

    /**
     * 工作状态描述
     */
    private String workStatusDesc;

    /**
     * 当前国家下全部证件类型
     */
    private Integer certificateTypeTotal;

    /**
     * 已存在证件类型
     */
    private Integer certificateTypeCount;


    /**
     * 封禁状态，1封禁中、2封禁结束、3封禁处理中
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.BAN_STATUS, ref = "banStatusName")
    private Integer banStatus;


    /**
     * 封禁状态，1封禁中、2封禁结束、3封禁处理中
     */
    private String banStatusName;

    /**
     * 黑名单截止日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 封禁理由
     */
    private String reason;

    /**
     * 操作时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date recordDate;

    /**
     * 操作用户
     */
    private String recordUserName;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 电话号码数
     */
    private Integer phoneCount;
}
