package com.imile.hrms.web.user.behavior.impl;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.common.enums.DurabillyTypeEnum;
import com.imile.hrms.dao.user.dto.UserDependentParamDTO;
import com.imile.hrms.dao.user.dto.UserInfoExpandParamDTO;
import com.imile.hrms.dao.user.model.UserDependentDO;
import com.imile.hrms.manage.user.UserDependentManage;
import com.imile.hrms.service.user.UserDependentService;
import com.imile.hrms.service.user.param.UserDependentSaveParam;
import com.imile.hrms.web.user.behavior.Durability;
import com.imile.util.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Component
public class UserDependentDurability implements Durability {

    @Autowired
    private UserDependentManage userDependentManage;

    @Autowired
    private UserDependentService userDependentService;

    @Override
    public Boolean durability(UserInfoExpandParamDTO param) {
        List<UserDependentParamDTO> userDependentList = param.getUserDependentList();
        DataDifferHolder<UserDependentDO> userDependentDifferHolder = userDependentService.differ(
                param.getUserId(), BeanUtils.convert(UserDependentSaveParam.class, userDependentList));
        userDependentManage.doSave(userDependentDifferHolder);
        return Boolean.TRUE;
    }

    @Override
    public String getDurabilly() {
        return DurabillyTypeEnum.USER_DEPENDENT_INFO.getCode();
    }
}
