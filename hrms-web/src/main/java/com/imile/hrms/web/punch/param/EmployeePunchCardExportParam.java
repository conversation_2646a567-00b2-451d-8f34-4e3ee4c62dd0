package com.imile.hrms.web.punch.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.query.BaseQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class EmployeePunchCardExportParam extends BaseQuery {

    /**
     * 导出开始时间(对应考勤打卡时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    /**
     * 导出截止时间(对应考勤打卡时间)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    /**
     * 部门id
     */
    private Long deptId;

    private String country;
}
