package com.imile.hrms.web.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-11-22
 * @version: 1.0
 */
@Data
public class VendorEmployeeDetailParam {

    /**
     * user_info表用户id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 审批记录表ID
     */
    @NotNull
    private Long bpmApprovalRecordId;
}
