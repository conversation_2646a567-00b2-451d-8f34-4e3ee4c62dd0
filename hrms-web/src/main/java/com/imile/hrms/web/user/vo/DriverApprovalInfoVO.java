package com.imile.hrms.web.user.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-12-9
 * @version: 1.0
 */
@Data
public class DriverApprovalInfoVO {

    private Long userId;

    /**
     * 审批记录ID
     */
    private Long bpmApprovalRecordId;

    /**
     * 审批单ID
     */
    private Long approvalId;

    /**
     * 审批类型
     */
    private String approvalType;

    /**
     * 审批类型DESC
     */
    private String approvalTypeDesc;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 性别 性别(1:男,2:女)
     */
    private Integer sex;

    /**
     * 网点
     */
    private Long stationId;

    /**
     * 网点名称
     */
    private String stationName;

    /**
     * 是否DTL  1是  0不是
     */
    private Integer isDtl;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 员工性质，外包，内部
     */
    private String employeeTypeDesc;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 职能
     */
    private String functional;

    /**
     * 派件|上门提货
     */
    private String functionalTxt;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 新增用户编码(创建/申请)
     */
    private String createUserCode;

    /**
     * 新增用户名
     */
    private String createUserName;

    /**
     * 操作人账号(驳回/通过)
     */
    private String operationUserCode;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 操作时间
     */
    private Date operationDate;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 驾照
     */
    private String drivingLicense;
}

