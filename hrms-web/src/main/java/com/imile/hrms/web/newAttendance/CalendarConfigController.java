package com.imile.hrms.web.newAttendance;

import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarConfigQuery;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarLegalLeaveConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarLegalLeaveConfigListQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.leave.vo.LegalLeaveConfigDetailVO;
import com.imile.hrms.service.leave.vo.LegalLeaveConfigVO;
import com.imile.hrms.service.newAttendance.calendar.command.CalendarAddCommand;
import com.imile.hrms.service.newAttendance.calendar.command.CalendarLegalLeaveConfigAddCommand;
import com.imile.hrms.service.newAttendance.calendar.command.CalendarStatusSwitchCommand;
import com.imile.hrms.service.newAttendance.calendar.command.CalendarUpdateCommand;
import com.imile.hrms.service.newAttendance.calendar.dto.CalendarConfigDTO;
import com.imile.hrms.service.newAttendance.calendar.dto.CalendarConfigDetailDTO;
import com.imile.hrms.service.newAttendance.calendar.dto.CalendarConfigRangeDTO;
import com.imile.hrms.service.newAttendance.calendar.dto.CalendarConfigSelectDTO;
import com.imile.hrms.web.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 新考勤日历controller
 * <AUTHOR> chen
 * @Date 2025/2/7 
 * @Description
 */
@RequestMapping("/attendance/v2/calendar/config")
@RestController
@RequiredArgsConstructor
public class CalendarConfigController extends BaseController {

//    private final CalendarConfigApplicationService applicationService;
//    private final ConverterService converterService;
//
//
//    /**
//     * 分页查询考勤日历方案
//     */
//    @PostMapping("/list")
//    public Result<PaginationResult<CalendarConfigDTO>> list(@RequestBody CalendarConfigQuery query){
//        PaginationResult<CalendarConfigDTO> paginationResult = applicationService.list(query);
//        // 返回时区处理
//        converterService.withAnnotation(paginationResult.getResults());
//        return Result.ok(paginationResult);
//    }
//
//    /**
//     * 查询考勤日历方案详情
//     */
//    @PostMapping("/detail")
//    public Result<CalendarConfigDetailDTO> detail(@Validated @RequestBody CalendarConfigDetailQuery configDetailQuery) {
//        return Result.ok(applicationService.detail(configDetailQuery));
//    }
//
//    /**
//     * 考勤日历添加
//     */
//    @DuplicateSubmit
//    @PostMapping("/add")
//    public Result<List<CalendarConfigRangeDTO>> add(@Validated(Groups.Add.class) @RequestBody CalendarAddCommand addCommand){
//        return Result.ok(applicationService.add(addCommand));
//    }
//
//    /**
//     * 考勤日历修改
//     */
//    @PostMapping("/update")
//    public Result<List<CalendarConfigRangeDTO>> update(@Validated(Groups.Update.class) @RequestBody CalendarUpdateCommand updateCommand) {
//        return Result.ok(applicationService.update(updateCommand));
//    }
//
//    /**
//     * 考勤日历停启用
//     */
//    @PostMapping("/status/switch")
//    public Result<List<CalendarConfigRangeDTO>> statusSwitch(
//            @Validated(Groups.Update.class) @RequestBody CalendarStatusSwitchCommand statusSwitchCommand) {
//        return Result.ok(applicationService.statusSwitch(statusSwitchCommand));
//    }
//
//
//    /**
//     * 考勤日历名称下拉框
//     */
//    @PostMapping("/select/list")
//    public Result<List<CalendarConfigSelectDTO>> selectList(@RequestParam String country) {
//        return Result.ok(applicationService.selectList(country));
//    }
//
//
//    /**
//     * 法定假期新增:废弃
//     */
//    @PostMapping("/legal/leave/add")
//    @Deprecated
//    public Result<Boolean> addLegalLeaveConfig(@Validated @RequestBody CalendarLegalLeaveConfigAddCommand addCommand) {
//        applicationService.addCalendarLegalLeaveConfig(addCommand);
//        return Result.ok(Boolean.TRUE);
//    }
//
//    /**
//     * 法定假期详情
//     */
//    @PostMapping("/legal/leave/detail")
//    public Result<LegalLeaveConfigDetailVO> detailLegalLeaveConfig(@RequestBody @Valid CalendarLegalLeaveConfigDetailQuery detailQuery) {
//        return Result.ok(applicationService.queryLegalLeaveDetail(detailQuery));
//    }
//
//    /**
//     * 法定假期列表：废弃
//     */
//    @PostMapping("/legal/leave/list")
//    @Deprecated
//    public Result<PaginationResult<LegalLeaveConfigVO>> listLegalLeaveConfig(@RequestBody @Valid CalendarLegalLeaveConfigListQuery listQuery) {
//        PaginationResult<LegalLeaveConfigVO> paginationResult = applicationService.queryLegalLeave(listQuery);
//        return Result.ok(paginationResult);
//    }
}
