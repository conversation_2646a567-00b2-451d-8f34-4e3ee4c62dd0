package com.imile.hrms.web.vehicle.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-27
 * @version: 1.0
 */
@Data
public class WarningConfigParam extends ResourceQuery implements Serializable {
    private static final long serialVersionUID = 8642963304513574563L;

    /**
     * 配置适用国家
     */
    private String country;

    /**
     * 网点ID（为空全选）
     */
    private Long stationId;
}
