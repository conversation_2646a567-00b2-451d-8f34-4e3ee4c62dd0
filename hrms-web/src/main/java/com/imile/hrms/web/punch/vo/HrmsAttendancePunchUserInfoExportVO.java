package com.imile.hrms.web.punch.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class HrmsAttendancePunchUserInfoExportVO implements Serializable {

    private static final long serialVersionUID = -3441843665929140296L;

    private String userCode;

    private String userName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 地理国
     */
    private String country;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 绑定时间
     */
    private Date createDate;
}
