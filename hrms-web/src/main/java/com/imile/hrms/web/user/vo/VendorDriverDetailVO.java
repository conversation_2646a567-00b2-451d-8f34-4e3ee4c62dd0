package com.imile.hrms.web.user.vo;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.genesis.api.model.component.Phone;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.service.user.result.UserCertificateBO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class VendorDriverDetailVO implements Serializable {
    private static final long serialVersionUID = 126345972770150432L;

    private Long id;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户系统账号名称
     */
    private String sysAccountName;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 网点编码
     */
    private String ocCode;
    /**
     * 网点名称
     */
    private String ocName;
    /**
     * 变更后网点编码
     */
    private String changedOcCode;
    /**
     * 变更后网点名称
     */
    private String changedOcName;
    /**
     * 电话
     */
    private Phone phone;
    /**
     * 人员证件列表
     */
    private List<UserCertificateBO> userCertificateList;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人
     */
    private String createUserCode;
    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * type  新建司机  网点变更
     */
    private String type;

    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商公司id
     */
    private Long vendorOrgId;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;
    /**
     * 职能
     */
    private List<String> functionalList;

    /**
     * 车辆类型
     */
    private String vehicleModel;

    /**
     * 人脸照片相对路径-短链接
     */
    private String faceFileKey;
    /**
     * 人脸照片完整路径
     */
    private String facePath;


/******************************************************巴西外包司机*******************************************************************/

    /**
     * 年龄
     */
    private String age;

    /**
     * 城市
     */
    @Length(max = 50, message = ValidCodeConstant.LENGTH)
    private String city;
    /**
     * 省
     */
    @Length(max = 50, message = ValidCodeConstant.LENGTH)
    private String province;
    /**
     * 国家
     */
    @Length(max = 25, message = ValidCodeConstant.LENGTH)
    private String country;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 邮箱
     */
    private String personalEmail;

    /**
     * 邮编id
     */
    private List<DriverZipCodeVO> zipCodeList;

    /**
     * 工作时间 开始
     */
    private String workingStartTime;

    /**
     * 工作时间 结束
     */
    private String workingEndTime;

    /**
     * 身份证件信息
     */
    private VendorDriverCertificateInfoVO idCard;

    /**
     * 驾驶证件信息
     */
    private VendorDriverCertificateInfoVO driverLicense;

    /**
     * 行驶证证件信息
     */
    private VendorDriverCertificateInfoVO vehicleLicense;

    /**
     * 车型
     */
    private String workingVehicleType;

    /**
     * 车牌号码
     */
    private String vehicleNumber;

    /**
     * Payment信息
     */
    private VendorDriverPaymentInfoVO paymentInfo;



}
