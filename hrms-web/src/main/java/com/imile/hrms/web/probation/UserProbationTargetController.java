package com.imile.hrms.web.probation;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.util.HrmsValidationUtil;
import com.imile.hrms.service.probation.UserProbationTargetService;
import com.imile.hrms.service.probation.bo.UserProbationTargetBO;
import com.imile.hrms.service.probation.param.Submit;
import com.imile.hrms.service.probation.param.TempStorage;
import com.imile.hrms.service.probation.param.UserProbationTargetSubmitParam;
import com.imile.hrms.service.probation.param.UserProbationUrgeParam;
import com.imile.hrms.web.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 员工试用期目标
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@RestController
@RequestMapping("/probation/target")
public class UserProbationTargetController extends BaseController {

    @Resource
    private UserProbationTargetService userProbationTargetService;


    /**
     * 试用期目标详情
     */
    @GetMapping("/list")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<UserProbationTargetBO> getTargetList(
            @NotNull(message = ValidCodeConstant.NOT_NULL) @RequestParam Long userProbationId) {
        return Result.ok(userProbationTargetService.getTargetList(userProbationId));
    }

    /**
     * 试用期目标(保存/编辑/提交)
     */
    @PostMapping("/save")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> save(@RequestBody UserProbationTargetSubmitParam param) {
        // 分组校验（暂存的时候不校验必填项、提交的时候需要校验）
        Class<?> groups = param.getIsSubmit() == 1 ?  Submit.class : TempStorage.class;
        HrmsValidationUtil.validate(param, groups);
        return Result.ok(userProbationTargetService.save(param));
    }

    /**
     * 催办(试用期目标、周期总结、试用期总结)
     */
    @PostMapping("/urge")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<Boolean> urgeProcess(@Validated @RequestBody UserProbationUrgeParam param) {
        return Result.ok(userProbationTargetService.urgeProcess(param));
    }
}
