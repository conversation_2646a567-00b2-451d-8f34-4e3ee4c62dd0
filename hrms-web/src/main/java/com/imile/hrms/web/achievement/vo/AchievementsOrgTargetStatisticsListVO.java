package com.imile.hrms.web.achievement.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 组织目标明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsOrgTargetStatisticsListVO {

    private Long id;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 指标库id
     */
    private Long indicatorLibraryId;

    /**
     * 指标分类
     */
    private String targetType;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标名称En
     */
    private String targetNameEn;

    /**
     * kpi分类
     */
    private String kpiType;

    /**
     * kpi分类描述
     */
    private String kpiTypeDesc;

    /**
     * 组织名称
     */
    private String deptName;

    /**
     * 部门负责人
     */
    private String leaderName;
    
    /**
     * 组织id
     */
    private Long deptId;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 基线值
     */
    private BigDecimal baseValue;

    /**
     * 目标值
     */
    private BigDecimal targetValue;

    /**
     * 挑战值
     */
    private BigDecimal challengeValue;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 完成值
     */
    private BigDecimal completionValue;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 创建人
     */
    private String createUserName;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    /**
     * 对齐目标名称
     */
    private String parentTargetName;

    /**
     * 可见性
     */
    private String visibility;

    /**
     * 指标性质01正向 02反向)
     */
    private String targetProperties;

    /**
     * 完成值来源(1prism 0手动 2运营)
     */
    private String isComplet;

    /**
     * 计算规则01按比例 02按绝对 03手动填写
     */
    private String calculationRuleType;

    /**
     * 完成值备注
     */
    private String resultRemark;

    /**
     * 子节点数量
     */
    private Integer childCount;

    /**
     * 指标库编码
     */
    private String indicatorLibraryCode;

    /**
     * 累计规则01平均值 02求和
     */
    private String accumulationRuleType;

    /**
     * 周期数
     */
    private Integer cycleNum;

    /**
     * 子考核周期 默认0:无周期 1季度 2月度 3周 4全年
     */
    private Integer cycleType;

    /**
     * 统计说明
     */
    private String statisticalDescription;

    /**
     * 周期数描述
     */
    private String cycleNumDesc;

    private String cycleNumDescEn;
}
