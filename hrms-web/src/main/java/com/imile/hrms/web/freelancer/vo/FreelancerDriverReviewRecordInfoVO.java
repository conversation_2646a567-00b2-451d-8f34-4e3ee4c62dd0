package com.imile.hrms.web.freelancer.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 审批信息
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverReviewRecordInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审批时间
     */
    private Date createDate;

    /**
     * 审批结果
     */
    private String reviewResult;

    /**
     * 审批原因
     */
    private String reason;

}
