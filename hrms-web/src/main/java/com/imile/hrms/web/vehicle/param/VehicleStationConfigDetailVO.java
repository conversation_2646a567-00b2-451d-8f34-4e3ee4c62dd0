package com.imile.hrms.web.vehicle.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.service.vehicle.dto.VehicleStandardFuelConsumptionDTO;
import com.imile.hrms.service.vehicle.dto.VehicleStandardMileageDTO;
import com.imile.hrms.service.vehicle.dto.WarningConfigVehicleDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-28
 * @version: 1.0
 */
@Data
public class VehicleStationConfigDetailVO {

    /**
     * 配置表ID
     */
    private Long id;

    /**
     * 所属模板(基础配置)ID
     */
    private Long templateConfigId;

    /**
     * 配置适用国家
     */
    private String country;

    /**
     * 配置适用网点ID
     */
    private Long deptId;

    /**
     * 配置适用网点
     */
    private String station;


    /**
     * 适用网点的标准里程
     */
    private BigDecimal standardMileage;

    /**
     * 车辆信息
     */
    private List<WarningConfigVehicleDTO> vehicleDTOList;

    /**
     * 车辆标准里程配置
     */
    private List<VehicleStandardMileageDTO> standardMileageDTOList;

    /**
     * 车辆标准油耗配置
     */
    private List<VehicleStandardFuelConsumptionDTO> standardFuelConsumptionDTOList;
}
