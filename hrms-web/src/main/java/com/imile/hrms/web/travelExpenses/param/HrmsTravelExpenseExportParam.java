package com.imile.hrms.web.travelExpenses.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.Date;

@Data
public class HrmsTravelExpenseExportParam extends ResourceQuery {

    /**
     * 结束时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;
}
