package com.imile.hrms.web.sys.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ResponsibleSettlementSubjectDetailVO extends ResponsibleSettlementSubjectVO{

    /**
     * 责任会计 岗位列表
     */
    @ApiModelProperty(value = "责任会计岗位ID列表", example = "[123,456]")
    List<Long> accountingPostIds;

    /**
     * 责任财务经理 岗位列表
     */
    @ApiModelProperty(value = "责任财务经理岗位ID列表", example = "[789,012]")
    List<Long> financeManagerPostIds;
}
