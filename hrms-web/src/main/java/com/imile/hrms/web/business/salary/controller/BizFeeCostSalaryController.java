package com.imile.hrms.web.business.salary.controller;

import com.alibaba.fastjson.JSON;
import com.imile.common.constant.ExceptionConstant;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.util.UserInfoUtil;
import com.imile.hrms.dao.bussiness.salary.dto.BizFeeCostSalaryDTO;
import com.imile.hrms.dao.bussiness.salary.dto.ImportCostSalaryParamDTO;
import com.imile.hrms.dao.bussiness.salary.dto.ImportEmpSalaryDTO;
import com.imile.hrms.dao.bussiness.salary.dto.ImportOcSalaryDTO;
import com.imile.hrms.dao.bussiness.salary.query.BizFeeCostSalaryQuery;

import com.imile.hrms.dao.organization.dto.EntDeptDTO;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.business.salary.BizFeeCostSalaryService;
import com.imile.hrms.service.organization.HrmsEntDeptService;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.business.salary.convert.BizFeeCostSalaryConvert;
import com.imile.hrms.web.business.salary.param.CostSalaryBatchDeleteParam;
import com.imile.hrms.web.business.salary.vo.BizFeeCostSalaryVO;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * @menu 薪资管理
 * <AUTHOR>
 * @Title: 薪资管理
 * @date 2021/10/19 3:31 下午
 */
@RestController
@RequestMapping("/cost/salary")
public class BizFeeCostSalaryController extends BaseController {

    @Resource
    private BizFeeCostSalaryService bizFeeCostSalaryService;

    @Resource
    private BizFeeCostSalaryConvert bizFeeCostSalaryConvert;

    @Resource
    private ConverterService converterService;
    @Autowired
    private HrmsEntDeptService entDeptService;

    /**
     *
     * 人员薪资导入
     */
    @PostMapping(value = "/importEmpSalary")
    public Result<List<ImportEmpSalaryDTO>> importEmpSalary(HttpServletRequest request, ImportCostSalaryParamDTO importCostParamDTO) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<ImportEmpSalaryDTO> importEmpSalaryDTOS = JSON.parseArray(callBackParam.getPageData(), ImportEmpSalaryDTO.class);

        if (CollectionUtils.isEmpty(importEmpSalaryDTOS)) {
            return Result.getFailResult(ExceptionConstant.ENTITY_NULL, "导入数据为空");
        }
        Boolean intoDB = callBackParam.getMode() == 2;
        List<ImportEmpSalaryDTO> failImportList = bizFeeCostSalaryService.importEmpSalary(importEmpSalaryDTOS, importCostParamDTO, intoDB);
        return Result.ok(failImportList);
    }

    /**
     *
     * 网点薪资导入
     */
    @PostMapping(value = "/importOcSalary")
    public Result<List<ImportOcSalaryDTO>> importOcSalary(HttpServletRequest request, ImportCostSalaryParamDTO importCostParamDTO) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<ImportOcSalaryDTO> importOcSalaryDTOS = JSON.parseArray(callBackParam.getPageData(), ImportOcSalaryDTO.class);

        if (CollectionUtils.isEmpty(importOcSalaryDTOS)) {
            return Result.getFailResult(ExceptionConstant.ENTITY_NULL, "导入数据为空");
        }
        Boolean intoDB = callBackParam.getMode() == 2;
        List<ImportOcSalaryDTO> failImportList = bizFeeCostSalaryService.importOcSalary(importOcSalaryDTOS, importCostParamDTO, intoDB);
        return Result.ok(failImportList);
    }

    /**
     *
     * 分页查询
     *
     */
    @PostMapping(value = "/getPageList")
    public Result<PaginationResult<BizFeeCostSalaryVO>> getPageList(@RequestBody BizFeeCostSalaryQuery query) {
        UserInfoDTO userInfoDTO = UserInfoUtil.getUserInfo();
        query.setOrgId(userInfoDTO.getOrgId());
        PaginationResult<BizFeeCostSalaryVO> result = bizFeeCostSalaryConvert.convert(bizFeeCostSalaryService.getPageList(query));
        if (CollectionUtils.isNotEmpty(result.getResults())) {
            result.getResults().stream().forEach(o -> {
                EntDeptDTO entDeptDTO = entDeptService.getById(o.getDeptId());
                o.setOrganizationCode(Optional.ofNullable(entDeptDTO).map(EntDeptDTO::getOrganizationCode).orElse(null));
            });
            converterService.withAnnotation(result.getResults());
        }
        return Result.ok(result);
    }

    /**
     *
     * 编辑
     *
     */
    @PostMapping(value = "/edit")
    public Result<Boolean> update(@RequestBody BizFeeCostSalaryDTO bizFeeCostSalaryDTO) {
        return Result.ok(bizFeeCostSalaryService.update(bizFeeCostSalaryDTO));
    }

    /**
     *
     * 删除
     *
     */
    @PostMapping(value = "/delete/{id}")
    public Result<Boolean> delete(@NotNull(message = ValidCodeConstant.NOT_NULL) @PathVariable Long id) {
        return Result.ok(bizFeeCostSalaryService.deleteById(id));
    }

    /**
     *
     * 导出
     *
     */
    @PostMapping(value = "/exportCost")
    public Result<PaginationResult<BizFeeCostSalaryVO>> export(HttpServletRequest request, BizFeeCostSalaryQuery query) {
        UserInfoDTO userInfoDTO = UserInfoUtil.getUserInfo();
        query.setOrgId(userInfoDTO.getOrgId());
        setExcelCallBackParam(request, query);
        PaginationResult<BizFeeCostSalaryVO> convert = bizFeeCostSalaryConvert.convert(bizFeeCostSalaryService.getPageList(query));
        if (CollectionUtils.isNotEmpty(convert.getResults())) {
            converterService.withAnnotation(convert.getResults());
        }
        return Result.ok(convert);
    }

    @PostMapping(value = "/batchDelete")
    public Result<Boolean> batchDelete(@RequestBody CostSalaryBatchDeleteParam param) {
        return Result.ok(bizFeeCostSalaryService.batchDelete(param.getIds()));
    }


    @PostMapping(value = "/batchDeleteByParam")
    public Result<Boolean> batchDeleteByParam(@RequestBody BizFeeCostSalaryQuery query) {
        return Result.ok(bizFeeCostSalaryService.batchDeleteByParam(query));
    }
}
