package com.imile.hrms.web.travelExpenses.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoAuthRequired;
import com.imile.hrms.dao.travelExpenses.dto.HrmsTravelExpensesConfigExportDTO;
import com.imile.hrms.service.travelExpenses.HrmsTravelExpensesService;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpenseExportDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesBaseConfigDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesConfigDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesConfigImportDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesDetailDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesListDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesUpdateDTO;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.travelExpenses.param.HrmsTravelExpenseExportParam;
import com.imile.hrms.web.travelExpenses.param.HrmsTravelExpensesDetailParam;
import com.imile.hrms.web.travelExpenses.param.HrmsTravelExpensesParam;
import com.imile.hrms.web.travelExpenses.param.HrmsTravelExpensesUpdateParam;
import com.imile.hrms.web.travelExpenses.vo.HrmsTravelExpensesBaseConfigVO;
import com.imile.hrms.web.travelExpenses.vo.HrmsTravelExpensesConfigExportVO;
import com.imile.hrms.web.travelExpenses.vo.HrmsTravelExpensesConfigVO;
import com.imile.hrms.web.travelExpenses.vo.HrmsTravelExpensesListVO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;

/**
 * 差旅费用表
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@RestController
@RequestMapping("/travelExpenses/")
@Slf4j
public class HrmsTravelExpensesController extends BaseController {

    @Autowired
    private HrmsTravelExpensesService hrmsTravelExpensesService;


    /**
     * 差旅费用列表
     *
     * @param query
     * @return
     */
    @PostMapping("list")
    public Result<List<HrmsTravelExpensesListVO>> list(@RequestBody HrmsTravelExpensesParam query) {
        HrmsTravelExpensesDTO dto = BeanUtils.convert(query, HrmsTravelExpensesDTO.class);
        List<HrmsTravelExpensesListDTO> listDto = hrmsTravelExpensesService.list(dto);
        return Result.ok(BeanUtils.convert(HrmsTravelExpensesListVO.class, listDto));
    }

    /**
     * 差旅费用详情
     *
     * @param query
     * @return
     */
    @PostMapping("detail")
    @NoAuthRequired
    public Result<List<HrmsTravelExpensesConfigVO>> detail(@RequestBody HrmsTravelExpensesDetailParam query) {
        HrmsTravelExpensesDetailDTO dto = BeanUtils.convert(query, HrmsTravelExpensesDetailDTO.class);
        List<HrmsTravelExpensesConfigDTO> list = hrmsTravelExpensesService.detail(dto);
        return Result.ok(BeanUtils.convert(HrmsTravelExpensesConfigVO.class,list));
    }

    /**
     * 差旅费用更新
     *
     * @param query
     * @return
     */
    @PostMapping("update")
    public Result<Boolean> update(@RequestBody HrmsTravelExpensesUpdateParam query) {
        HrmsTravelExpensesUpdateDTO dto = BeanUtils.convert(query, HrmsTravelExpensesUpdateDTO.class);
        hrmsTravelExpensesService.update(dto);
        return Result.ok();
    }

    /**
     * 差旅费用项 导入
     */
    @PostMapping("import")
    public Result<List<HrmsTravelExpensesConfigImportDTO>> expenseImport(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        log.info("------->import expenseImport jobId:{}", callBackParam.getJobId());
        long startTime = System.currentTimeMillis();

        List<HrmsTravelExpensesConfigImportDTO> importList = JSON.parseArray(callBackParam.getPageData(), HrmsTravelExpensesConfigImportDTO.class);

        //讲list集合转成json格式
        log.info(" ------------ import expenseImport,importList:{} --------------", JSON.toJSONString(importList));

        if (CollectionUtil.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }

        List<HrmsTravelExpensesConfigImportDTO> failList = hrmsTravelExpensesService.expenseImport(importList);

        log.info(" ------------ import expenseImport,spendTime:{}ms --------------", System.currentTimeMillis() - startTime);
        return Result.ok(failList);
    }


    /**
     * 差旅费用项 导出
     *
     * @param query
     * @return
     */
    @PostMapping("list/export")
    public Result<PaginationResult<HrmsTravelExpensesConfigExportVO>> listExport(HttpServletRequest request, HrmsTravelExpenseExportParam query) {
        setExcelCallBackParam(request, query);
        query.setShowCount(50000);
        HrmsTravelExpenseExportDTO exportDto = BeanUtils.convert(query, HrmsTravelExpenseExportDTO.class);
        PaginationResult<HrmsTravelExpensesConfigExportDTO> res = hrmsTravelExpensesService.listExport(exportDto);
        PaginationResult<HrmsTravelExpensesConfigExportVO> resVo = this.convertPage(res, HrmsTravelExpensesConfigExportVO.class);
        return Result.ok(resVo);
    }

    /**
     * 差旅费用项 列表
     *
     * @return
     */
    @PostMapping("expense/list")
    public Result<List<HrmsTravelExpensesBaseConfigVO>> expenseList() {
        List<HrmsTravelExpensesBaseConfigDTO> listDto = hrmsTravelExpensesService.expenseList();
        return Result.ok(BeanUtils.convert(HrmsTravelExpensesBaseConfigVO.class, listDto));
    }

    /**
     * 测试
     *
     * @return
     */
//    @PostMapping("test")
//    public Result test() {
//        hrmsTravelExpensesService.test();
//        return Result.ok();
//    }


}
