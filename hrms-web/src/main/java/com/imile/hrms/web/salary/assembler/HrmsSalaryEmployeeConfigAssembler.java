package com.imile.hrms.web.salary.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.imile.hrms.dao.salary.dto.*;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.web.salary.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/7
 */
@Slf4j
@Component
public class HrmsSalaryEmployeeConfigAssembler {
    @Autowired
    private ConverterService converterService;

    /**
     * 将详情DTO转换为对应的detailVO
     *
     * @param detailDTO
     * @return
     */
    public SalaryEmployeeConfigDetailVO convertToDetailVO(SalaryEmployeeConfigDetailDTO detailDTO) {
        // 返回参数处理
        // 基本属性
        SalaryEmployeeConfigDetailVO detailVO = BeanUtil.copyProperties(detailDTO, SalaryEmployeeConfigDetailVO.class, "socialConfigDetailInfo", "accumulationConfigDetailInfo");
        converterService.withAnnotation(SalaryEmployeeConfigDetailVO.class).accept(detailVO);

        // 公积金转换
        SalaryAccumulationConfigDetailDTO accumulationConfigDetailInfoDTO = detailDTO.getAccumulationConfigDetailInfo();
        if (accumulationConfigDetailInfoDTO != null) {
            // 复制基本属性
            SalaryAccumulationConfigDetailVO salaryAccumulationConfigDetailVO = BeanUtil.copyProperties(accumulationConfigDetailInfoDTO, SalaryAccumulationConfigDetailVO.class, "baseConfigItem", "configItems");
            // 处理基础项
            salaryAccumulationConfigDetailVO.setBaseConfigItem(Optional.ofNullable(accumulationConfigDetailInfoDTO.getBaseConfigItem())
                    .map(baseConfigItemDTO -> {
                        SalaryEmployeeBaseConfigVO baseConfigItemVO = BeanUtil.copyProperties(baseConfigItemDTO, SalaryEmployeeBaseConfigVO.class);
                        converterService.withAnnotation(SalaryEmployeeBaseConfigVO.class).accept(baseConfigItemVO);
                        return baseConfigItemVO;
                    })
                    .orElse(null));
            // 处理项目属性
            salaryAccumulationConfigDetailVO.setConfigItems(convertConfigItems(accumulationConfigDetailInfoDTO.getConfigItems()));
            detailVO.setAccumulationConfigDetailInfo(salaryAccumulationConfigDetailVO);
        }

        // 社保转换
        AbstractSalaryConfigDetailDTO socialConfigDetailDTO = detailDTO.getSocialConfigDetailInfo();
        if (socialConfigDetailDTO != null) {
            AbstractSalaryConfigDetailVO salaryConfigDetailVO;
            // 处理基础项 使用员工薪酬的社保配置代替社保方案中的社保配置
            if (socialConfigDetailDTO instanceof MexSalarySocialConfigDetailDTO) {
                salaryConfigDetailVO = new MexSalarySocialConfigDetailVO();

                // 墨西哥社保
                MexSalaryBaseConfigItemVO baseConfigItemVO = BeanUtil.copyProperties(((MexSalarySocialConfigDetailDTO) socialConfigDetailDTO).getBaseConfigItem(), MexSalaryBaseConfigItemVO.class);
                converterService.withAnnotation(MexSalaryBaseConfigItemVO.class).accept(baseConfigItemVO);
                ((MexSalarySocialConfigDetailVO) salaryConfigDetailVO).setBaseConfigItem(baseConfigItemVO);
            } else {
                // 普通社保
                salaryConfigDetailVO = new SalarySocialConfigDetailVO();
                SalaryBaseConfigItemVO baseConfigItemVO = BeanUtil.copyProperties(((SalarySocialConfigDetailDTO) socialConfigDetailDTO).getBaseConfigItem(), SalaryEmployeeBaseConfigVO.class);
                converterService.withAnnotation(SalaryBaseConfigItemVO.class).accept(baseConfigItemVO);
                ((SalarySocialConfigDetailVO) salaryConfigDetailVO).setBaseConfigItem(baseConfigItemVO);
            }
            // 处理项目属性
            salaryConfigDetailVO.setConfigItems(convertConfigItems(socialConfigDetailDTO.getConfigItems()));
            // 复制基本属性
            BeanUtil.copyProperties(socialConfigDetailDTO, salaryConfigDetailVO, "baseConfigItem", "configItems");
            detailVO.setSocialConfigDetailInfo(salaryConfigDetailVO);
            // 自选项社保-已选社保缴纳基数 组成选项
            if (CollectionUtils.isNotEmpty(detailDTO.getSalarySocialEmployeeItemDTOS())) {
                List<SalarySocialEmployeeItemVO> salarySocialEmployeeItemVOS = detailDTO.getSalarySocialEmployeeItemDTOS().stream().map(o -> convertSalarySocialEmployeeItemsVO(o)).collect(Collectors.toList());
                detailVO.setSalarySocialEmployeeItemVOS(salarySocialEmployeeItemVOS);
            }
        }
        // 计薪方案数据字典转换处理
        converterService.withAnnotation(SalaryConfigDetailVO.class).accept(detailVO.getSalaryConfigDetailInfo());

        return detailVO;

    }

    private List<ConfigItemVO> convertConfigItems(List<ConfigItemDTO> configItems) {
        return Optional.ofNullable(configItems)
                .map(configItemDTOS -> {
                    List<ConfigItemVO> configItemVOs = configItemDTOS.stream().map(configItemDTO -> (ConfigItemVO) BeanUtil.copyProperties(configItemDTO, SalaryEmployeeConfigItemVO.class))
                            .collect(Collectors.toList());
                    // 数据字典处理
                    converterService.withAnnotation(configItemVOs);
                    return configItemVOs;
                })
                .orElse(new ArrayList<>());
    }

    private SalarySocialEmployeeItemVO convertSalarySocialEmployeeItemsVO(SalarySocialEmployeeItemDTO salarySocialEmployeeItemDTO) {
        if (salarySocialEmployeeItemDTO == null) {
            return null;
        }

        SalarySocialEmployeeItemVO itemsVO = new SalarySocialEmployeeItemVO();

        BeanUtil.copyProperties(salarySocialEmployeeItemDTO, itemsVO);

        List<SalaryEmployeeItemVO> salaryEmployeeItemVOS = Optional.ofNullable(salarySocialEmployeeItemDTO.getSalaryEmployeeItems())
                .map(itemDTOS -> {
                    List<SalaryEmployeeItemVO> itemsVOS = itemDTOS.stream().map(itemDTO -> (SalaryEmployeeItemVO) BeanUtil.copyProperties(itemDTO, SalaryEmployeeItemVO.class))
                            .collect(Collectors.toList());
                    return itemsVOS;
                })
                .orElse(new ArrayList<>());
        itemsVO.setSalaryEmployeeItems(salaryEmployeeItemVOS);
        return itemsVO;
    }

}
