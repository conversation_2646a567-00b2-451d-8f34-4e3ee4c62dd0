package com.imile.hrms.web.vendor.vo;

import com.imile.common.page.PaginationResult;
import lombok.Data;

import java.util.List;

/**
 * @author: milo
 * @createDate: 2022-10-20
 */
@Data
public class HrmsDriverPerformanceDetailInfoVO {

    /**
     * 司机考核信息（周期内）
     */
    private PaginationResult<HrmsDriverIndicatorsDetailVO> indicatorsDetailPage;

    /**
     * 网点总分KPI
     */
    private String totalScores;

    /**
     * 网点绩效列表
     */
    private List<HrmsVendorPerformanceVO> vendorPerformanceList;


}
