package com.imile.hrms.web.attendance.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-2-24
 * @version: 1.0
 */
@Data
public class AttendanceMonthExportVO implements Serializable {

    private static final long serialVersionUID = 4426421381836346438L;


    /**
     * 序号
     */
    private String sr;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 部门组织编码
     */
    private String organizationCode;

    /**
     * 部门
     */
    private String department;

    /**
     * 岗位
     */
    private String designation;

    /**
     * 入职时间
     */
    private Date joiningDate;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 身份证号码
     */
    private String iqamaNumber;

    /**
     * 员工类型
     */
    private String character;

    /**
     * 供应商名称
     */
    private String vendorName;


    /**
     * 出勤
     */
    private BigDecimal present;

    /**
     * 缺勤
     */
    private BigDecimal absent;

    /**
     * 休息日
     */
    private BigDecimal offDay;

    /**
     * 病假
     */
    private BigDecimal sickLeave;

    /**
     * 年假
     */
    private BigDecimal annualLeave;

    /**
     * 丧假
     */
    private BigDecimal deathLeave;

    /**
     * 婚嫁
     */
    private BigDecimal marriageLeave;

    /**
     * 产假
     */
    private BigDecimal chileBirthLeave;

    /**
     * 开斋节
     */
    private BigDecimal hajjLeave;

    /**
     * 公共假期
     */
    private BigDecimal publicHoliday;


    /**
     * 异常的
     */
    private BigDecimal abnormal;


    /**
     * 辞职
     */
    private BigDecimal resigned;


    /**
     * 辞退
     */
    private BigDecimal terminated;


    /**
     * 计薪天数
     */
    private BigDecimal payableDays;

    /**
     * 考勤周期
     */
    private String attendanceCycle;

    /**
     * 天
     */
    public String day25;

    public String day26;

    public String day27;

    public String day28;

    public String day29;

    public String day30;

    public String day31;

    public String day01;

    public String day02;

    public String day03;

    public String day04;

    public String day05;

    public String day06;

    public String day07;

    public String day08;

    public String day09;

    public String day10;

    public String day11;

    public String day12;

    public String day13;

    public String day14;

    public String day15;

    public String day16;

    public String day17;

    public String day18;

    public String day19;

    public String day20;

    public String day21;

    public String day22;

    public String day23;

    public String day24;

    //public String day20230302;
}

