package com.imile.hrms.web.user.vo;

import com.imile.hrms.service.user.dto.DriverLevelInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-2-10
 * @version: 1.0
 */
@Data
public class DriverLevelConfigDetailVO {
    /**
     * id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 国家
     */
    private String country;

    /**
     * 网点
     */
    private String station;

    /**
     * 网点ID(默认规则时，为ALL,自定义规则时，为多个ID拼接)
     */
    private String stationId;

    /**
     * 员工性质
     */
    private String employeeType;

    /**
     * 结算日期
     */
    private String conclusionDate;

    /**
     * 适用场景
     */
    private String affectFunction;

    /**
     * 结算指标
     */
    private String settlementIndex;

    /**
     * 最少出勤天数
     */
    private Integer limitPresentDays;

    /**
     * 状态(停启用)
     */
    private String status;

    /**
     * 等级信息
     */
    private List<DriverLevelInfoDTO> driverLevelInfoDTOList;
}
