package com.imile.hrms.web.user.controller;

import com.alibaba.fastjson.JSON;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.service.excel.UserOutsourceDriverUpdateImportProcessor;
import com.imile.hrms.service.excel.UserOutsourceEmployeeUpdateImportProcessor;
import com.imile.hrms.service.excel.UserUpdateImportProcessor;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.refactor.user.param.UserOutsourceDriverUpdateImportParam;
import com.imile.hrms.service.refactor.user.param.UserOutsourceEmployeeUpdateImportParam;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.UserWagesCardService;
import com.imile.hrms.service.user.param.EmailListParam;
import com.imile.hrms.service.user.param.UserAssociateQueryParam;
import com.imile.hrms.service.user.param.UserCertificateValidateParam;
import com.imile.hrms.service.user.param.UserEmailCheckParam;
import com.imile.hrms.service.user.param.UserUpdateImportParam;
import com.imile.hrms.service.user.result.UserOptionBO;
import com.imile.hrms.service.user.result.UserWagesCardHistoryBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 人员
 *
 * <AUTHOR>
 * @date 2024/4/11
 */
@Validated
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;
    @Resource
    private UserCertificateService userCertificateService;
    @Resource
    private UserWagesCardService userWagesCardService;
    @Resource
    private UserUpdateImportProcessor userUpdateImportProcessor;
    @Resource
    private UserOutsourceEmployeeUpdateImportProcessor userOutsourceEmployeeUpdateImportProcessor;
    @Resource
    private UserOutsourceDriverUpdateImportProcessor userOutsourceDriverUpdateImportProcessor;

    /**
     * 获取人员联想列表
     * 最多返回满足条件的前50条
     *
     * @param param UserAssociateQueryParam
     * @return List<UserOptionBO>
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @PostMapping("/associate/list")
    public Result<List<UserOptionBO>> getUserAssociateList(@RequestBody UserAssociateQueryParam param) {
        return Result.ok(userService.getUserAssociateList(param));
    }

    /**
     * 校验人员证件
     *
     * @param param UserCertificateValidateParam
     * @return UserCertificateCheckResultDTO
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @PostMapping("/certificate/check")
    public Result<UserCertificateCheckResultDTO> validateUserCertificate(@RequestBody UserCertificateValidateParam param) {
        return Result.ok(userCertificateService.validateUserCertificate(param));
    }

    /**
     * 校验人员证件是否重复
     *
     * @param param UserCertificateDuplicateCheckParam
     * @return UserCertificateCheckResultDTO
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @PostMapping("/certificate/duplicate/check")
    public Result<UserCertificateCheckResultDTO> checkUserCertificateDuplicate(
            @Validated @RequestBody UserCertificateDuplicateCheckParam param) {
        return Result.ok(userCertificateService.checkUserCertificateDuplicate(param));
    }

    /**
     * 获取人员工资卡历史列表
     *
     * @param originalId 原始ID
     * @return List<UserWagesCardHistoryBO>
     */
    @GetMapping("/wages/card/history/list")
    public Result<List<UserWagesCardHistoryBO>> getUserWagesCardHistoryList(
            @NotNull(message = ValidCodeConstant.NOT_NULL) @RequestParam Long originalId) {
        return Result.ok(userWagesCardService.getUserWagesCardHistoryList(originalId));
    }

    /**
     * 根据用户名称或邮件查询邮箱
     *
     * @param param EmailListParam
     * @return List<String>
     */
    @GetMapping("/email")
    public Result<PaginationResult<String>> getEmailList(@RequestBody EmailListParam param) {
        return Result.ok(userService.getEmailList(param));
    }

    /**
     * 待更新人员导入
     *
     * @param request HttpServletRequest
     * @return List<UserUpdateImportParam>
     */
    @PostMapping("/update/import")
    public Result<List<UserUpdateImportParam>> importUser4Update(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<UserUpdateImportParam> dataList = JSON.parseArray(callBackParam.getPageData(), UserUpdateImportParam.class);
        if (CollectionUtils.isEmpty(dataList)) {
            return Result.ok(Collections.emptyList());
        }
        return Result.ok(userUpdateImportProcessor.importData(dataList));
    }

    /**
     * 待更新外包员工导入
     *
     * @param request HttpServletRequest
     * @return List<UserUpdate4OutsourcingEmployeeImportParam>
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @PostMapping("/outsourcing/employee/update/import")
    public Result<List<UserOutsourceEmployeeUpdateImportParam>> importUser4OutsourcingEmployeeUpdate(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<UserOutsourceEmployeeUpdateImportParam> dataList = JSON.parseArray(callBackParam.getPageData(), UserOutsourceEmployeeUpdateImportParam.class);
        if (CollectionUtils.isEmpty(dataList)) {
            return Result.ok(Collections.emptyList());
        }
        return Result.ok(userOutsourceEmployeeUpdateImportProcessor.importData(dataList));
    }

    /**
     * 待更新外包司机导入
     *
     * @param request HttpServletRequest
     * @return List<UserUpdate4OutsourcingDriverImportParam>
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @PostMapping("/outsourcing/driver/update/import")
    public Result<List<UserOutsourceDriverUpdateImportParam>> importUser4OutsourcingDriverUpdate(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);
        List<UserOutsourceDriverUpdateImportParam> dataList = JSON.parseArray(callBackParam.getPageData(), UserOutsourceDriverUpdateImportParam.class);
        if (CollectionUtils.isEmpty(dataList)) {
            return Result.ok(Collections.emptyList());
        }
        return Result.ok(userOutsourceDriverUpdateImportProcessor.importData(dataList));
    }

    /**
     * 获取人员动态信息
     *
     * @param userCode 人员编码
     * @return UserDynamicInfoDTO
     */
    @GetMapping("/dynamic/info")
    public Result<UserDynamicInfoDTO> getUserDynamicInfo(
            @NotBlank(message = ValidCodeConstant.NOT_NULL) @RequestParam String userCode,
            @RequestParam String[] dynamicFields) {
        List<UserDynamicFieldEnum> dynamicFieldList = Arrays.stream(dynamicFields)
                .map(UserDynamicFieldEnum::valueOfKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Result.ok(userService.getUserDynamicInfo(userCode, dynamicFieldList));
    }

    /**
     * 判断邮箱是否存在
     *
     * @param param UserEmailCheckParam
     * @return Boolean
     */
    @PostMapping("/enterprise/email/flag")
    public Result<Boolean> isEmailExist(@Validated @RequestBody UserEmailCheckParam param) {
        return Result.ok(userService.isEmailExist(param));
    }
}
