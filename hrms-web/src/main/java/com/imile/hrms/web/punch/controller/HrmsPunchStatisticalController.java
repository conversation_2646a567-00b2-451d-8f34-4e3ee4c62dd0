package com.imile.hrms.web.punch.controller;

import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.dao.punch.dto.HrmsPunchStatisticalDTO;
import com.imile.hrms.dao.punch.dto.PunchResultDTO;
import com.imile.hrms.dao.punch.dto.StatisticalPunchDTO;
import com.imile.hrms.dao.punch.query.AttendancePunchRecordDetailQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchStatisticalQuery;
import com.imile.hrms.service.punch.HrmsAttendanceEmployeePunchDayService;
import com.imile.hrms.service.punch.HrmsAttendanceEmployeePunchRecordService;
import com.imile.hrms.web.BaseController;
import com.imile.hrms.web.punch.vo.HrmsPunchStatisticalVO;

import com.imile.hrms.web.punch.vo.PunchResultVO;
import com.imile.hrms.web.punch.vo.StatisticalPunchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @menu 打卡记录统计
 * @Title: 打卡统计
 * @date 2022/02/11
 */
@RestController
@RequestMapping("punch/statistical")
public class HrmsPunchStatisticalController extends BaseController {

    @Autowired
    private HrmsAttendanceEmployeePunchDayService punchDayService;

    @Autowired
    private HrmsAttendanceEmployeePunchRecordService punchRecordService;


    /**
     * 统计记录
     */
    @PostMapping("/record")
    @NoLoginAuthRequired
    public Result<HrmsPunchStatisticalVO> statisticalList(@Validated @RequestBody AttendancePunchStatisticalQuery statisticalQuery) {
        HrmsPunchStatisticalDTO hrmsPunchStatisticalDTO = punchDayService.statisticalList(statisticalQuery);
        return Result.ok(HrmsCollectionUtils.convertSingle(hrmsPunchStatisticalDTO, HrmsPunchStatisticalVO.class));
    }

    /**
     * 打卡详情
     *
     * @param statisticalQuery
     * @return
     */
    @PostMapping("/detail")
    @NoLoginAuthRequired
    public Result<StatisticalPunchVO> statisticalDetail(@Validated @RequestBody AttendancePunchRecordDetailQuery statisticalQuery) {
        StatisticalPunchDTO recordUserPunchDTOList = punchRecordService.statisticalDetail(statisticalQuery);
        StatisticalPunchVO statisticalPunchVO = HrmsCollectionUtils.convertSingle(recordUserPunchDTOList, StatisticalPunchVO.class);
        return Result.ok(statisticalPunchVO);
    }

    /**
     * 打卡统计结果获取
     *
     * @param statisticalQuery
     * @return
     */
    @PostMapping("/result")
    @NoLoginAuthRequired
    public Result<PunchResultVO> monthResult(@Validated @RequestBody AttendancePunchStatisticalQuery statisticalQuery) {
        PunchResultDTO punchResultDTO = punchDayService.monthResult(statisticalQuery);
        PunchResultVO punchResultVO = HrmsCollectionUtils.convertSingle(punchResultDTO, PunchResultVO.class);
        return Result.ok(punchResultVO);
    }
}
