package com.imile.hrms.web.academy.controller;

import com.imile.common.result.Result;
import com.imile.hrms.manage.mail.MailManage;
import com.imile.hrms.service.academy.AcademyService;
import com.imile.hrms.service.academy.param.AcademyQuery;
import com.imile.hrms.service.academy.vo.DeptVO;
import com.imile.hrms.service.academy.vo.PostVO;
import com.imile.hrms.service.academy.vo.UserInfoVO;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 学院系统对接 前端控制器
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@RestController
@RequestMapping("/AcademyController")
public class AcademyController {

    @Autowired
    private AcademyService academyService;

    @Autowired
    private MailManage mailManage;

    /**
     * 员工列表
     *
     * @param query
     * @return
     */
    @PostMapping("/userList")
    @NoLoginRequired
    public Result<List<UserInfoVO>> userList(@RequestBody AcademyQuery query) {
        return Result.ok(academyService.userList(query));
    }

    /**
     * 部门列表
     *
     * @param query
     * @return
     */
    @PostMapping("/deptList")
    @NoLoginRequired
    public Result<List<DeptVO>> deptList(@RequestBody AcademyQuery query) {
        return Result.ok(academyService.deptList(query));
    }

    /**
     * 岗位列表
     *
     * @param query
     * @return
     */
    @PostMapping("/postList")
    @NoLoginRequired
    public Result<List<PostVO>> postList(@RequestBody AcademyQuery query) {
        return Result.ok(academyService.postList(query));
    }
}
