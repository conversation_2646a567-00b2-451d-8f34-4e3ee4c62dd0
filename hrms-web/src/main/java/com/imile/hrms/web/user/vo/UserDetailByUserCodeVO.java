package com.imile.hrms.web.user.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserDetailByUserCodeVO implements Serializable {
    private static final long serialVersionUID = 2506105180925309102L;

    private Long userId;

    private String userCode;

    private String userName;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

    /**
     * 所属国家
     */
    private String country;
    /**
     * 所属部门
     */
    private Long deptId;

    private String deptName;

    /**
     * 所属部门链
     */
    private String deptChain;

    /**
     * 所属网点
     */
    private Long ocId;

    private String ocName;

    /**
     * 工作岗位
     */
    private Long postId;

    private String postName;

    /**
     * 企业微信ID
     */
    private String wecomId;

    /**
     * 是否有考勤权限  1有
     */
    private Integer isAttendanceRole;


    private String settlementCenterCode;

    private String settlementCenterName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;
}
