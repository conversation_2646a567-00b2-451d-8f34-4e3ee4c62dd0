package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/26
 */
public class UserInfoListExportVO implements Serializable {
    private String id;

    /**
     * 姓名
     */
    private String userName;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 所属国家名称
     */
    private String countryName;
    /**
     * 员工账号
     */
    private String userCode;
    /**
     * 公司邮箱
     */
    private String email;
    /**
     * 工作岗位名称
     */
    private String postName;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 职级名称
     */
    private String gradeType;
    /**
     * 职级数
     */
    private String gradeNo;
    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeTypeDesc;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 在职状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    private String workStatus;
    /**
     * 在职状态
     */
    private String workStatusDesc;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 上级员工名称
     */
    private String leaderUserName;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 居住证
     */
    private String residencyPermit;
    /**
     * 护照
     */
    private String passPort;
    /**
     * 工作签证
     */
    private String workVisa;
    /**
     * 旅游签
     */
    private String touristVisa;
    /**
     * 商务签
     */
    private String businessVisa;
    /**
     * 司机驾照
     */
    private String drivingLicense;



}
