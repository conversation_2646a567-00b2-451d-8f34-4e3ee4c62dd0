package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.blacklist.dto.BlacklistSettingDTO;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.dao.user.model.HrmsUserDimissionOperationRecordDO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DimmisionVO {

    private Long id;

    private String workNo;

    private Long userId;

    private String userCode;
    /**
     * 计划离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date planDimissionDate;

    /**
     * 实际离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date actualDimissionDate;

    /**
     * 离职原因 离职原因
     */
    private String dimissionReason;

    /**
     * 离职原因 离职原因
     */
    private String dimissionReasonDesc;

    /**
     * 原因备注
     */
    private String remark;

    /**
     * 单据备注
     */
    private String approvalRemark;

    /**
     * 离职类型:01离职 02辞退
     */
    private String resignationType;

    /**
     * 离职类型:01离职 02辞退
     */
    private String resignationTypeDesc;

    /**
     * 工作接交人id
     */
    private Long transfereeId;
    /**
     * 工作接交人id
     */
    private String transfereeUserName;

    /**
     * 工作接交人账号
     */
    private String transfereeUserCode;

    /**
     * 附件
     */
    private List<AttachmentVO> attachments;

    /**
     * 黑名单设置（用于离职自动拉黑）
     */
    private BlacklistSettingDTO blacklistSetting;

    /**
     * hr附件(二期新增)
     */
    private List<AttachmentDTO> hrAttachments;

    /**
     * 操作记录
     */
    private List<HrmsUserDimissionOperationRecordDO> operationRecord;
}
