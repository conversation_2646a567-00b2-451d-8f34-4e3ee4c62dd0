package com.imile.hrms.web.freelancer.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 详情参数
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverInfoDetailParam {

    /**
     * 众包司机id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 类型
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String reviewStatus;

}
