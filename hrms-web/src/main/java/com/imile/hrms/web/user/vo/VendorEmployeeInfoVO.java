package com.imile.hrms.web.user.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VendorEmployeeInfoVO implements Serializable {
    private static final long serialVersionUID = 6528009621702186997L;


    private Long id;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;


    /**
     * 用户部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 原部门id
     */
    private Long originalDeptId;

    /**
     * 原部门名称
     */
    private String originalDeptName;


    /**
     * 要变更的部门id
     */
    private Long afterAuditDeptId;
    /**
     * 要变更的部门名称
     */
    private String afterAuditDeptName;
    /**
     * 类型(新建司机，网点变更)
     */
    private String type;
    /**
     * 证件号类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.CERTIFICATE_TYPE_CODE, ref = "certificateTypeCodeDesc")
    private String certificateTypeCode;
    /**
     * 证件号类型描述
     */
    private String certificateTypeCodeDesc;
    /**
     * 证件号
     */
    private String certificateCode;
    /**
     * 驾驶证号码
     */
    private String license;
    /**
     * 电话
     */
    private String phone;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人
     */
    private String createUserCode;
    /**
     * 创建人名称
     */
    private String createUserName;
    /**
     * 审核状态
     */
    private String status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * 审核人编码
     */
    private String auditUserCode;

    /**
     * 审核人用户名
     */
    private String auditUserName;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;

    /**
     * 最后修改用户名
     */
    private String lastUpdUserName;

    /**
     * 停启用
     * ACTIVE:启用
     * DISABLED:停用
     * <p>
     * 对应的离职，在职
     */
    private String workStatus;

    /**
     * 汇报上级编码
     */
    private Long leaderId;


    /**
     * 系统账号名称
     */
    private String sysAccountName;

    /**
     * 性别 性别(1:男,2:女)
     */
    private Integer sex;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 停启用
     * ACTIVE:启用
     * DISABLED:停用
     */
    private String userStatus;

    /**
     * DTL code
     */
    private String dtlUserCode;

    /**
     * DLT name
     */
    private String dtlUserName;

    /**
     * 审批记录表ID
     */
    private Long bpmApprovalRecordId;

    /**
     * 审批单ID
     */
    private Long approvalId;

    /**
     * 审批类型
     */
    private String approvalType;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

}
