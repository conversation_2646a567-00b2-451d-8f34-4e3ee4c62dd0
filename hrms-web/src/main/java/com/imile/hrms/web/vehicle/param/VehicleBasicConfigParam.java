package com.imile.hrms.web.vehicle.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.service.vehicle.dto.VehicleStandardFuelConsumptionDTO;
import com.imile.hrms.service.vehicle.dto.VehicleStandardMileageDTO;
import com.imile.hrms.service.vehicle.dto.WarningConfigVehicleDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-28
 * @version: 1.0
 */
@Data
public class VehicleBasicConfigParam {
    /**
     * 配置表ID，新增时无需传递，更新时必传
     */
    private Long id;

    /**
     * 国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;


    /**
     * 车辆信息
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<WarningConfigVehicleDTO> vehicleDTOList;

    /**
     * 车辆标准里程配置
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<VehicleStandardMileageDTO> standardMileageDTOList;

    /**
     * 车辆标准油耗配置
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<VehicleStandardFuelConsumptionDTO> standardFuelConsumptionDTOList;

    /**
     * 0新增   1更新
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;

    /**
     * 更新时必传，是否更新已生成的存量具体配置数据  0不更新  1更新
     */
    private Integer isUpdateStockData;
}
