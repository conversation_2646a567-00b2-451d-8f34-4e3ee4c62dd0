package com.imile.hrms.web.travelExpenses.param;

import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesConfigDTO;
import com.imile.hrms.service.travelExpenses.dto.HrmsTravelExpensesConfigGradeRelationDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 差旅费用表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
public class HrmsTravelExpensesUpdateParam {


    /**
     * 国家编码
     */
    private String countryCode;


    private Long id;

    /**
     * 生效时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectTime;

    /**
     * 差旅费用配置信息
     */
    private List<HrmsTravelExpensesConfigDTO> list;

    /**
     * 职级列表
     */
    private List<HrmsTravelExpensesConfigGradeRelationDTO> gradeList;

}
