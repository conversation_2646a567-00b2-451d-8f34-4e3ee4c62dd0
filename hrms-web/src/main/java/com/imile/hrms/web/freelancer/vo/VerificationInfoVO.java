package com.imile.hrms.web.freelancer.vo;


import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import java.io.Serializable;

/**
 * 证件信息
 *
 * <AUTHOR>
 */
@Data
public class VerificationInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 证件背面路径
     */
    @HyperLink(ref = "certificatePathHttps")
    private String certificatePath;
    private String certificatePathHttps;

}
