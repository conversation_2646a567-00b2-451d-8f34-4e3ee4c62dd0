package com.imile.hrms.web.salary.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 自选项类型社保已选社保缴纳基数组成项
 * <AUTHOR>
 */
@Data
public class SalaryEmployeeItemVO implements Serializable {
    private static final long serialVersionUID = 345635284191679780L;
    /**
     * 项目ID
     */
    private Long salaryItemId;
    /**
     * 薪资组成类型，不能为空，BASE:基础薪资 OTHER:其他
     */
    private String salaryItemType;
    /**
     * 薪资组成名称，不能为空
     */
    private String salaryItemName;

    /**
     * 占薪资比例，不能为空不包含% ，比如81.5%，则传81.5即可
     */
    private BigDecimal salaryRatio;
    /**
     * 薪资金额
     */
    private BigDecimal salaryValue;
}
