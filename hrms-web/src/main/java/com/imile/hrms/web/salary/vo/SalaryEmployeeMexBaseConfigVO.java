package com.imile.hrms.web.salary.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/4
 */
@Data
public class SalaryEmployeeMexBaseConfigVO extends MexSalaryBaseConfigItemVO {
    /**
     * 假期的溢价比例薪资计算 输入填写对应的值
     */
    private BigDecimal dailyVacationPremiumValue;
    /**
     * 圣诞节津贴计算输入填写对应的值
     */
    private BigDecimal christmasBonusOrAguinaldoValue;
}
