package com.imile.hrms.web.punch.vo;

import com.imile.hrms.common.annotation.HyperLink;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDayPunchRecordVo {

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 图片url
     */
    @HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;

    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;

    /**
     * 部门名称
     */
    private String  deptName;

    /**
     * 岗位名称
     */
    private String  postName;

    /**
     * 考勤打卡名称
     */
    private String punchConfigName;

    /**
     * 返回具体打卡数据
     */
    List<HrmsRecordUserPunchVO> punchWayName;


}
