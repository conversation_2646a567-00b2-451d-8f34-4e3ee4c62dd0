package com.imile.hrms.web.freelancer.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 基础信息
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverBasicInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * firstName
     */
    private String firstName;

    /**
     * middleName
     */
    private String middleName;

    /**
     * lastName
     */
    private String lastName;

    /**
     * 国家
     */
    private FreelancerDriverDeliveryPreferenceZoneVO country;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 省
     */
    private FreelancerDriverDeliveryPreferenceZoneVO state;

    /**
     * 市
     */
    private FreelancerDriverDeliveryPreferenceZoneVO city;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 详细地址
     */
    private String detailAddress;

}
