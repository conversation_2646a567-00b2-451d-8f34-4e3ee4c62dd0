package com.imile.hrms.web.attendance.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-5-23
 * @version: 1.0
 */
@Data
public class AttendanceUpdateParam extends ResourceQuery {

    /**
     * 当前用户id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 当前用户编码
     */
    private String userCode;

    /**
     * 当前用户部门ID
     */
    private Long deptId;

    /**
     * 当前用户岗位ID
     */
    private Long postId;

    /**
     * 年
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long year;

    /**
     * 月份
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long month;

    /**
     * 具体修改的天数，支持批量操作，批量仅支持考勤/缺勤/请假   不支持销假的批量操作
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private List<DayBatchParam> dayBatchParamList;

    /**
     * 更新类型 :出勤，缺勤，请假,销假,置空
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String updateType;

    /**
     * 具体出勤/缺勤/请假 类型  (P,OJT,AL,ML等等)
     */
    //@NotNull(message = ValidCodeConstant.NOT_NULL)
    private String concreteType;

    /**
     * 加班小时数
     */
    private BigDecimal overtimeHours;

    /**
     * 应出勤小时数
     */
    private BigDecimal shouldAttendanceHours;

    /**
     * 出勤小时数
     */
    private BigDecimal attendanceHours;

    /**
     * 请假小时数
     */
    private BigDecimal leaveHours;

    /**
     * 销假时间
     */
    private BigDecimal destroyHours;

    /**
     * 销假类型：用户当天可能请了多种类型的假，销假的时候需要选择不同的假来消除
     */
    private String destroyLeaveType;

    /**
     * 请假类型(全称)
     */
    private String leaveType;

    /**
     * 图片路径
     */
    private String picturePath;

    /**
     * 备注
     */
    private String attendanceRemark;

    /**
     * 岗位名称
     */
    private String postName;

}
