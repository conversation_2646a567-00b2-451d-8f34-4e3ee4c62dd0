package com.imile.hrms.web.bpm.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-12-5
 * @version: 1.0
 */
@Data
public class ApprovalCancelParam {

    /**
     * 审批记录ID
     */
    @NotNull
    private Long bpmApprovalRecordId;

    /**
     * 来源  审核中界面/已驳回界面
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String selectSource;

}
