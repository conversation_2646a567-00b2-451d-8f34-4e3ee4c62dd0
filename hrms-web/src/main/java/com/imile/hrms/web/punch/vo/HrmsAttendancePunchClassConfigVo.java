package com.imile.hrms.web.punch.vo;



import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 打卡时间设置的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendancePunchClassConfigVo implements Serializable {

    private Long id;
    /**
     * 班次id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 出勤时长
     */
    private BigDecimal attendanceHours;

    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;

    /**
     * 打卡时间配置
     */
    List<HrmsAttendancePunchClassItemConfigVO> classItemConfigList;
}
