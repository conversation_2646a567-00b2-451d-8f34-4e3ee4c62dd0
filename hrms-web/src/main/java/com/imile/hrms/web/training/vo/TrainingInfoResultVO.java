package com.imile.hrms.web.training.vo;

import com.imile.hrms.web.user.vo.UserInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class TrainingInfoResultVO implements Serializable {
    private static final long serialVersionUID = 6856771325070676519L;

    private Long id;

    /**
     * 培训主题
     */
    private String trainingTopic;

    /**
     * 培训类型
     */
    private String trainingType;

    /**
     * 培训场次
     */
    private String trainingSession;

    /**
     * 培训日期
     */
    private Date date;

    /**
     * dayid 示例：20220124
     */
    private String dayId;

    /**
     * 培训开始时间
     */
    private Date trainingStartTime;

    /**
     * 培训结束时间
     */
    private Date trainingEndTime;

    /**
     * 培训时长
     */
    private BigDecimal trainingDuration;

    /**
     * 培训网点id
     */
    private Long deptId;

    private String deptName;

    /**
     * 培训讲师编码
     */
    private String trainerCode;

    /**
     * 培训讲师名称
     */
    private String trainerName;

    /**
     * 状态 未开始、签到完成、结果录入中、已完成
     */
    private String status;

    /**
     * 邀请人信息
     */
    private List<UserInfoVO> inviteeUserList;

    /**
     * 参与人信息
     */
    private List<UserInfoVO> participantUserList;

}
