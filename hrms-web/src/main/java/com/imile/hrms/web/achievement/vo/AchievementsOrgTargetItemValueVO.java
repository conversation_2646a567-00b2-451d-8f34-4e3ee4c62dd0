package com.imile.hrms.web.achievement.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 组织目标完成值VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsOrgTargetItemValueVO {

    private Long id;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * kpi分类
     */
    private String kpiType;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门名称-En
     */
    private String deptNameEn;

    /**
     * 负责人
     */
    private String userName;


    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 基线值
     */
    private BigDecimal baseValue;

    /**
     * 目标值
     */
    private BigDecimal targetValue;

    /**
     * 挑战值
     */
    private BigDecimal challengeValue;

    /**
     * 完成值
     */
    private BigDecimal completionValue;

    /**
     * 完成率
     */
    private String completionRate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 指标库id
     */
    private Long indicatorLibraryId;



}
