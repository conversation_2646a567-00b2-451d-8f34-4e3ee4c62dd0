//package com.imile.hrms.web.attendance.convert;
//
//import com.alibaba.fastjson.JSON;
//import com.imile.common.exception.BusinessException;
//import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
//import com.imile.hrms.common.enums.attendance.AttendanceUpdateTypeEnum;
//import com.imile.hrms.common.enums.leave.LeaveCodeAssociateEnum;
//import com.imile.hrms.service.attendance.dto.AttendanceUpdateDTO;
//import com.imile.hrms.service.attendance.dto.DayBatchDTO;
//import com.imile.hrms.web.attendance.param.AttendanceUpdateParam;
//import com.imile.hrms.web.attendance.param.DayBatchParam;
//import com.imile.util.lang.I18nUtils;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * @description:
// * @author: taokang
// * @createDate: 2022-5-23
// * @version: 1.0
// */
//public class AttendanceConvert {
//
//    public static AttendanceUpdateDTO convertFromParam(AttendanceUpdateParam attendanceUpdateParam) {
//        if (attendanceUpdateParam != null) {
//            AttendanceUpdateDTO attendanceUpdateDTO = new AttendanceUpdateDTO();
//            attendanceUpdateDTO.setUserId(attendanceUpdateParam.getUserId());
//            attendanceUpdateDTO.setUserCode(attendanceUpdateParam.getUserCode());
//            attendanceUpdateDTO.setCompanyId(attendanceUpdateParam.getCompanyId());
//            attendanceUpdateDTO.setDeptId(attendanceUpdateParam.getDeptId());
//            attendanceUpdateDTO.setPostId(attendanceUpdateParam.getPostId());
//            attendanceUpdateDTO.setYear(attendanceUpdateParam.getYear());
//            attendanceUpdateDTO.setMonth(attendanceUpdateParam.getMonth());
//            attendanceUpdateDTO.setAttendanceRemark(attendanceUpdateParam.getAttendanceRemark());
//            attendanceUpdateDTO.setPostName(attendanceUpdateParam.getPostName());
//            List<DayBatchParam> dayBatchParamList = attendanceUpdateParam.getDayBatchParamList();
//            List<DayBatchDTO> dayBatchDTOList = new ArrayList<>();
//            dayBatchParamList.forEach(item -> {
//                DayBatchDTO dayBatchDTO = new DayBatchDTO();
//                dayBatchDTO.setDay(item.getDay());
//                dayBatchDTO.setDate(item.getDate());
//                dayBatchDTO.setDayId(item.getDayId());
//                dayBatchDTOList.add(dayBatchDTO);
//            });
//            attendanceUpdateDTO.setDayBatchDTOList(dayBatchDTOList);
//            attendanceUpdateDTO.setUpdateType(attendanceUpdateParam.getUpdateType());
//            attendanceUpdateDTO.setShouldAttendanceHours(attendanceUpdateParam.getShouldAttendanceHours());
//            attendanceUpdateDTO.setOvertimeHours(attendanceUpdateParam.getOvertimeHours());
//            attendanceUpdateDTO.setAttendanceHours(attendanceUpdateParam.getAttendanceHours());
//            attendanceUpdateDTO.setDestroyHours(attendanceUpdateParam.getDestroyHours());
//            attendanceUpdateDTO.setDestroyLeaveType(attendanceUpdateParam.getDestroyLeaveType());
//            attendanceUpdateDTO.setLeaveHours(attendanceUpdateParam.getLeaveHours());
//            attendanceUpdateDTO.setPicturePath(attendanceUpdateParam.getPicturePath());
//            attendanceUpdateDTO.setConcreteType(attendanceUpdateParam.getConcreteType());
//            if (!StringUtils.equalsIgnoreCase(attendanceUpdateParam.getUpdateType(), AttendanceUpdateTypeEnum.DESTROY_LEAVE.getCode()) && StringUtils.isBlank(attendanceUpdateParam.getConcreteType())) {
//                throw BusinessException.get(HrmsErrorCodeEnums.CONCRETE_TYPE_IS_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.CONCRETE_TYPE_IS_NOT_EMPTY.getDesc()));
//            }
//            if (StringUtils.equalsIgnoreCase(attendanceUpdateParam.getUpdateType(), AttendanceUpdateTypeEnum.LEAVE.getCode())) {
//                //请假操作
//                //具体的请假类型转换，简码转为详细编码
//                LeaveCodeAssociateEnum associateEnum = LeaveCodeAssociateEnum.getInstanceByShortCode(attendanceUpdateParam.getConcreteType());
//                if (associateEnum == null) {
//                    //没有这种请假类型
//                    throw BusinessException.get(HrmsErrorCodeEnums.SYSTEM_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SYSTEM_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
//                }
//                attendanceUpdateDTO.setLeaveType(associateEnum.getFullCode());
//            }
//            attendanceUpdateDTO.setResourceType(attendanceUpdateParam.getResourceType());
//            attendanceUpdateDTO.setOrganizationIds(attendanceUpdateParam.getOrganizationIds());
//            attendanceUpdateDTO.setResourceSql(attendanceUpdateParam.getResourceSql());
//
//            return attendanceUpdateDTO;
//        }
//        return null;
//    }
//}
