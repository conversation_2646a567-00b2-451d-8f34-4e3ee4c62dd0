package com.imile.hrms.web.punch.vo;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 *用户打卡使用的wifi和名称
 */
@Data
public class HrmsPunchDataVO {
    /**
     * mac地址
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String macAddress;
    /**
     * wifi名称
     */
    private String wifiName;
    /**
     * 地址
     */
    private String placeName;
    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     *纬度
     */
    private BigDecimal latitude;
    /**
     * 地点 PLACE_PUNCH_WAYS
     * wifi WIFI_PUNCH_WAYS
     */
    private String punchWay;
}
