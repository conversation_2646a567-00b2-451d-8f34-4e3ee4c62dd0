package com.imile.hrms.web.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/5
 */
@Data
public class SalaryEmployeeConfigDetailVO implements Serializable {
    private static final long serialVersionUID = 1784631755439326597L;
    /**
     * ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 计薪方案相关信息
     */
    private SalaryConfigDetailVO salaryConfigDetailInfo;
    /**
     * 每件费用,员工薪酬配置这里取该值，请勿取计薪方案中的每件费用(pieceSalary)
     */
    private BigDecimal pieceFee;

    /**
     * 社保相关配置
     */
    private AbstractSalaryConfigDetailVO socialConfigDetailInfo;

    /**
     * 自选项类型社保已选社保缴纳基数组成项
     */
    private List<SalarySocialEmployeeItemVO> salarySocialEmployeeItemVOS;

    /**
     * 公积金相关配置
     */
    private SalaryAccumulationConfigDetailVO accumulationConfigDetailInfo;
    /**
     * 薪资总额
     */
    private BigDecimal salary;
    /**
     * 日薪
     */
    private BigDecimal dailySalary;
    /**
     * 基础日薪
     */
    private BigDecimal baseDailySalary;
    /**
     * 时薪
     */
    private BigDecimal wage;
    /**
     * 基础时薪
     */
    private BigDecimal baseWage;
    /**
     * 签证费
     */
    private BigDecimal visaFee;

    /**
     * 医保费
     */
    private BigDecimal medicareFee;

    /**
     * 机票费用
     */
    private BigDecimal airTicketFee;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;
    /**
     * 税收
     */
    private BigDecimal taxationFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectTime;
    /**
     * 调薪原因
     */
    private String salaryAdjustmentReason;

    /**
     * 薪资组成，基础薪资有且仅有一条。 JSON数组
     */
    private List<SalaryItemVO> salaryItems;
}
