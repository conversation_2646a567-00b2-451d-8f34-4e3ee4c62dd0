package com.imile.hrms.web.vendor.vo;

import lombok.Data;

/**
 * @author: milo
 * @createDate: 2022-10-20
 */
@Data
public class HrmsDriverPerformanceTargetVO {

    private Long id;

    /**
     * 绩效方案表id
     */
    private String performancePlanId;

    /**
     * 指标类型：1、司机出勤 2、运单签收  3、COD货款当日未归班 4、货物当日未归班 5、投诉 6、货损及遗失
     */
    private String targetType;

    /**
     * 预警值
     */
    private String warningValue;

    /**
     * 目标值
     */
    private String targetValue;

    /**
     * 分值占比
     */
    private String scoresOf;

    /**
     * 分值
     */
    private String score;

    /**
     * 1 加分项/ 0 减分项
     */
    private Integer addMinus;
}
