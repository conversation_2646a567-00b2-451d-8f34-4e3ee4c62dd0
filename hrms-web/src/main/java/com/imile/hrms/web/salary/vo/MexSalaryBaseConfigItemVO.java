package com.imile.hrms.web.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.salary.dto.IBaseConfigItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 墨西哥基础配置
 *
 * <AUTHOR>
 */
@Data
public class MexSalaryBaseConfigItemVO implements IBaseConfigItemVO {
    /**
     * 每月uma 墨西哥国家公布的uma数据
     */
    private BigDecimal currentUma;

    /**
     * 假期天数 假期天数
     */
    private Integer vacationDay;

    /**
     * 假期的溢价比例薪资计算类型 固定填写或输入填写
     */
    private String dailyVacationPremium;

    /**
     * 溢价比例 溢价比例
     */
    private BigDecimal premiumRate;

    /**
     * 圣诞节津贴计算类型 固定填写或输入填写
     */
    private String christmasBonusOrAguinaldo;

    /**
     * 圣诞津贴倍数 圣诞津贴倍数
     */
    private BigDecimal aguinaldoRate;

    /**
     * 圣诞节计入日期 圣诞节津贴计入日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date aguinaldoDate;

    /**
     * 最大 BLS与current_uma之间倍数关系
     */
    private BigDecimal maxBlsRate;
    /**
     * 假期的溢价比例薪资计算 输入填写对应的值
     */
    private BigDecimal dailyVacationPremiumValue;
    /**
     * 圣诞节津贴计算输入填写对应的值
     */
    private BigDecimal christmasBonusOrAguinaldoValue;
}
