package com.imile.hrms.web.punch.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 移动考勤打卡详情视图
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class HrmsAttendanceMobilePunchDetailVO implements Serializable {

    private static final long serialVersionUID = -8353788780940787859L;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 用户常住地国家
     */
    private String locationCountry;

    /**
     * 打卡规则配置
     */
    private HrmsAttendanceMobilePunchConfigVo punchConfigVO;

    /**
     * 班次配置
     */
    private HrmsAttendancePunchClassConfigVo punchClassConfigVO;

    /**
     * 班次详情配置
     */
    private List<HrmsAttendanceMobilePunchClassItemConfigVO> punchClassItemConfigVO;

    /**
     * 打卡记录
     */
    private List<HrmsAttendanceMobilePunchCardRecordVO> punchCardRecordVO;

    /**
     * 请假/外勤记录
     */
    private List<HrmsAttendanceMobileFormVO> attendanceFormVOS;

    /**
     * 考勤异常类型(枚举值)
     */
    private List<HrmsAttendanceMobileAbnormalVO> abnormalPunchVOS;

}
