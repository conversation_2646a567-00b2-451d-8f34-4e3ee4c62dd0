package com.imile.hrms.web.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.web.account.vo.AccountLastRecordVO;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * 账号信息类
 */
@Data
public class UserInfoVO extends BaseUserInfoVO {

    /**
     * 工号
     */
    private String workNo;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;
    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeTypeDesc;
    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 供应商id
     */
    private String vendorName;

    /**
     * 是否是司机
     */
    private Integer isDriver;
    /**
     * 是否是司机领导(dtl)
     */
    private Integer isDtl;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门链
     */
    private String deptChain;

    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 工作岗位
     */
    private Long postId;
    /**
     * 工作岗位名称
     */
    private String postName;

    /**
     * 职级id
     */
    private Long gradeId;

    /**
     * 职级序列
     */
    private String rankSequence;
    /**
     * 职级名称
     */
    private String gradeName;

    /**
     * 职级数
     */
    private Integer gradeNo;

    /**
     * 职级等级
     */
    private String jobGrade;

    /**
     * 上级用户id
     */
    private Long leaderId;
    /**
     * 上级用户名称
     */
    private String leaderName;


    /**
     * 猎头公司
     */
    private Long recruitmentCompanyId;
    /**
     * 猎头公司
     */
    private String recruitmentCompanyName;
    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dimissionDate;

    /**
     * 实际离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDimissionDate;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_STATUS, ref = "workStatusDesc")
    private String workStatus;
    private String workStatusDesc;
    /**
     * 入职时长
     */
    private Integer entryTime;
    /**
     * 离职原因
     */
    private String dimissionReason;
    /**
     * 接交人名称
     */
    private String transfereeUserName;
    /**
     * 系统账号名称
     */
    private String sysAccountName;
    /**
     * 附件
     */
    private List<AttachmentVO> attachments;

    /**
     * 账号开通情况
     */
    private List<AccountLastRecordVO> accounts;

    /**
     * 所属网点id
     */
    private Long ocId;
    /**
     * 所属网点名称
     */
    private String ocName;
    /**
     * 所属业务节点id
     */
    private Long bizModelId;

    /**
     * 所属业务节点名称
     */
    private String bizModelName;
    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 结算主体编码
     */
    private String settlementCenterCode;

    /**
     * 结算主体名称
     */
    private String settlementCenterName;

    /**
     * 是否是网点型部门
     */
    private Integer isOperationDept;

    private Integer isBizModelRelation;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 所属国
     */
    private String originCountryName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 是否全球派遣（0:否 1:是）
     */
    private Integer isGlobalRelocation;
}
