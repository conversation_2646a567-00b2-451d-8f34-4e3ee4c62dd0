package com.imile.hrms.web.user.controller;

import com.imile.common.result.Result;
import com.imile.hrms.dao.user.dto.BaseOptionDTO;
import com.imile.hrms.service.user.OperatorService;
import com.imile.hrms.service.user.param.OperatorUserAssociateQueryParam;
import com.imile.hrms.service.user.result.UserSelectorBO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@RestController
@RequestMapping("/operator")
public class OperatorController {

    @Resource
    private OperatorService operatorService;

    /**
     * 获取操作人可见一级部门列表
     * 仅HRMS内部使用
     *
     * @return List<BaseOptionDTO>
     */
    @GetMapping("/visible/first/level/dept/list")
    public Result<List<BaseOptionDTO>> getVisibleFirstLevelDeptList() {
        return Result.ok(operatorService.getVisibleFirstLevelDeptList());
    }

    /**
     * 获取操作人可见人员联想列表
     * 仅HRMS内部使用
     *
     * @param param OperatorUserAssociateQueryParam
     * @return List<UserSelectorBO>
     */
    @PostMapping("/visible/user/associate/list")
    public Result<List<UserSelectorBO>> getVisibleUserAssociateList(
            @RequestBody OperatorUserAssociateQueryParam param) {
        return Result.ok(operatorService.getVisibleUserAssociateList(param));
    }
}
