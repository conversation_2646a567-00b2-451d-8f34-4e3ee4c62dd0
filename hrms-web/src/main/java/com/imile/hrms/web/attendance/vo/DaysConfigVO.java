package com.imile.hrms.web.attendance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/14
 */
@Data
public class DaysConfigVO implements Serializable {
    private static final long serialVersionUID = -5508340063755848814L;
    /**
     * 日期，yyyy-MM-dd格式
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
    /**
     * 日期类型：WEEKEND 休息日 ，HOLIDAY 节假日
     */
    private String dayType;
}
