package com.imile.hrms.web.salary;

import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.salary.HrmsSalarySubmitTemplateConfigService;
import com.imile.hrms.service.salary.param.HrmsSalarySubmitTemplateConfigAddEditParam;
import com.imile.hrms.service.salary.param.HrmsSalarySubmitTemplateConfigItemHistoryParam;
import com.imile.hrms.service.salary.param.HrmsSalarySubmitTemplateConfigItemListParam;
import com.imile.hrms.service.salary.param.HrmsSalarySubmitTemplateConfigPageParam;
import com.imile.hrms.service.salary.param.HrmsSalarySubmitTemplateConfigSwitchStatusParam;
import com.imile.hrms.service.salary.vo.HrmsSalarySubmitTemplateConfigDetailVO;
import com.imile.hrms.service.salary.vo.HrmsSalarySubmitTemplateConfigDisabledTipsVO;
import com.imile.hrms.service.salary.vo.HrmsSalarySubmitTemplateConfigHistoryVO;
import com.imile.hrms.service.salary.vo.HrmsSalarySubmitTemplateConfigPageVO;
import com.imile.hrms.service.salary.vo.SalaryItemConfigCountryDetailVO;
import com.imile.hrms.web.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 薪资数据提报设置
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Slf4j
@RestController
@RequestMapping("/salary/submit/template/config")
public class HrmsSalarySubmitTemplateConfigController extends BaseController {

    @Autowired
    private HrmsSalarySubmitTemplateConfigService hrmsSalarySubmitTemplateConfigService;

    @Autowired
    private ConverterService converterService;

    /**
     * 列表
     */
    @PostMapping("/pageList")
    public Result<PaginationResult<HrmsSalarySubmitTemplateConfigPageVO>> pageList(@RequestBody HrmsSalarySubmitTemplateConfigPageParam param) {
        PaginationResult<HrmsSalarySubmitTemplateConfigPageVO> result = hrmsSalarySubmitTemplateConfigService.pageList(param);
        converterService.withAnnotation(result.getResults());
        return Result.ok(result);
    }

    /**
     * 详情
     */
    @PostMapping("/detail")
    public Result<HrmsSalarySubmitTemplateConfigDetailVO> detail(@RequestParam Long id) {
        return Result.ok(hrmsSalarySubmitTemplateConfigService.detail(id));
    }

    /**
     * 新增提报数据模板
     */
    @PostMapping("/add")
    public Result<String> add(@RequestBody HrmsSalarySubmitTemplateConfigAddEditParam param) {
        hrmsSalarySubmitTemplateConfigService.add(param);
        return Result.ok();
    }

    /**
     * 编辑提报数据模板
     */
    @PostMapping("/edit")
    public Result<String> edit(@RequestBody HrmsSalarySubmitTemplateConfigAddEditParam param) {
        hrmsSalarySubmitTemplateConfigService.edit(param);
        return Result.ok();
    }

    /**
     * 生效月份起始可选择月
     */
    @PostMapping("/effectDateStart")
    public Result<Long> effectDateStart(@RequestParam Long id) {
        return Result.ok(hrmsSalarySubmitTemplateConfigService.effectDateStart(id));
    }

    /**
     * 获取所有国家下的薪资项
     */
    @PostMapping("/itemList")
    public Result<List<SalaryItemConfigCountryDetailVO>> itemList(@RequestBody HrmsSalarySubmitTemplateConfigItemListParam param) {
        List<SalaryItemConfigCountryDetailVO> resultVO = hrmsSalarySubmitTemplateConfigService.itemList(param);
        converterService.withAnnotation(resultVO);
        resultVO.forEach(item -> {
            converterService.withAnnotation(item.getItemConfigDetailVOList());
        });
        return Result.ok(resultVO);
    }

    /**
     * 停用提报数据模板时提示
     */
    @PostMapping("/disableTips")
    public Result<HrmsSalarySubmitTemplateConfigDisabledTipsVO> disableTips(@RequestBody HrmsSalarySubmitTemplateConfigSwitchStatusParam param) {
        return Result.ok(hrmsSalarySubmitTemplateConfigService.disableTips(param));
    }

    /**
     * 停用提报数据模板
     */
    @PostMapping("/switchStatus")
    public Result<String> switchStatus(@RequestBody HrmsSalarySubmitTemplateConfigSwitchStatusParam param) {
        hrmsSalarySubmitTemplateConfigService.switchStatus(param);
        return Result.ok();
    }

    /**
     * 详情
     */
    @PostMapping("/copyDetail")
    public Result<HrmsSalarySubmitTemplateConfigDetailVO> copyDetail(@RequestParam Long id) {
        return Result.ok(hrmsSalarySubmitTemplateConfigService.copyDetail(id));
    }

    /**
     * 历史记录
     */
    @PostMapping("/history")
    public Result<PaginationResult<HrmsSalarySubmitTemplateConfigHistoryVO>> history(@RequestBody HrmsSalarySubmitTemplateConfigItemHistoryParam param) {
        return Result.ok(hrmsSalarySubmitTemplateConfigService.history(param));
    }

}
