package com.imile.hrms.web.punch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.OutWithTimeZone;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 员工每日打卡记录表 只记录最新的记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendanceEmployeePunchListVo {

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy.MM.dd", timezone = "GMT+8")
    private Date dayTime;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 邮箱
     */

    private String email;

    /**
     * 日期属性
     * 前端不展示这个字段
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.DATE_TYPE, ref = "dayTypeDesc")
    private String dayType;

    /**
     * 显示这个 日期属性描述
     */
    private String dayTypeDesc;

    /**
     * 考勤打卡规则id
     */
    private Long punchConfigId;

    /**
     * 考勤打卡名称
     */
    private String punchConfigName;

    /**
     * 用户实际上班打卡时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date punchInTime;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     * 前端不展示这个字段
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PUNCH_TYPE, ref = "punchConfigTypeDesc")
    private String punchConfigType;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     * 前端展示这个字段
     */
    private String punchConfigTypeDesc;

    /**
     * 用户实际下班打卡时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date punchOutTime;


    /**
     * 上班时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date configPunchInTime;

    /**
     * 下班时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date configPunchOutTime;

    /**
     * 打卡结果 NORMAL:正常，EARLY:早退，LATE:迟到，SERIOUS_LATE:严重迟到，ABSENTEEISM:旷工迟到，NOT_SIGNED:未打卡
     */
    private String timeResult;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String  postName;


    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    private Date lastUpdDate;


}
