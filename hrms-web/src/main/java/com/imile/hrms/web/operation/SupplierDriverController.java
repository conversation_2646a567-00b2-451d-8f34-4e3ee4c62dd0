package com.imile.hrms.web.operation;

import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.service.operation.SupplierDriverService;
import com.imile.hrms.service.operation.param.SupplierDriverQueryParam;
import com.imile.hrms.service.operation.param.SupplierDriverSaveParam;
import com.imile.hrms.service.operation.param.SupplierDriverStatusSwitchParam;
import com.imile.hrms.service.operation.result.SupplierDriverDetailBO;
import com.imile.hrms.service.operation.result.SupplierDriverListBO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/6
 */
@Validated
@RestController
@RequestMapping("/supplier/driver")
public class SupplierDriverController {

    @Resource
    private SupplierDriverService supplierDriverService;

    /**
     * 保存供应商司机
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @DuplicateSubmit
    @PostMapping("/save")
    public Result<Boolean> saveSupplierDriver(@Validated @RequestBody SupplierDriverSaveParam param) {
        return Result.ok(supplierDriverService.saveSupplierDriver(param));
    }

    /**
     * 停启用供应商司机
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @DuplicateSubmit
    @PostMapping("/status/switch")
    public Result<Boolean> switchVendorDriverStatus(@Validated @RequestBody SupplierDriverStatusSwitchParam param) {
        return Result.ok(supplierDriverService.switchSupplierDriverStatus(param));
    }

    /**
     * 获取供应商司机详情
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @GetMapping("/detail")
    public Result<SupplierDriverDetailBO> getSupplierDriverDetail(
            @NotNull(message = ValidCodeConstant.NOT_NULL) @RequestParam Long id) {
        return Result.ok(supplierDriverService.getSupplierDriverDetail(id));
    }

    /**
     * 获取供应商司机列表
     */
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    @PostMapping("/list")
    public Result<PaginationResult<SupplierDriverListBO>> getSupplierDriverList(@Validated @RequestBody SupplierDriverQueryParam param) {
        return Result.ok(supplierDriverService.getSupplierDriverList(param));
    }
}
