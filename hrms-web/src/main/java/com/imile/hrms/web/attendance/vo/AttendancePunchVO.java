package com.imile.hrms.web.attendance.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/26
 */
@Data
public class AttendancePunchVO {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 配置上班时间
     */
    private Date configPunchInTime;
    /**
     * 配置下班时间
     */
    private Date configPunchOutTime;
    /**
     * 上班打卡时间
     */
    private Date actuallyPunchInTime;
    /**
     * 下班打卡时间
     */
    private Date actuallyPunchOutTime;
    /**
     * 当前日期
     */
    private Date currentDate;
    /**
     * 打卡配置NO
     */
    private String punchConfigNo;
    /**
     * 打卡配置类型CLASS_WORK（按班次），FREE_WORK（自由上班）FIXED_WORK（固定上下班）
     */
    private String punchConfigType;
    /**
     * 周几
     */
    private String weekDay;
    /**
     * PRESENT（工作日）、WEEKEND（休息日）、HOLIDAY（节假日）
     */
    private String dayType;
    /**
     * 打卡配置为CLASS_WORK,是否已经给员工安排了排班
     */
    private Boolean isScheduleConfig;

}
