package com.imile.hrms.web.salary;


import cn.hutool.core.date.DateUtil;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.dao.salary.dto.SalaryEmployeeDetailDTO;
import com.imile.hrms.dao.salary.query.HumanCostStatisticsQuery;
import com.imile.hrms.dao.salary.query.SalaryEmployeeDetailQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.business.salary.dto.EmployeeAccruedPayDTO;
import com.imile.hrms.service.business.salary.dto.EmployeeHumanCostDTO;
import com.imile.hrms.service.business.salary.dto.HumanCostDetailDTO;
import com.imile.hrms.service.salary.HrmsSalaryEmployeeDetailService;
import com.imile.hrms.web.salary.vo.*;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import com.imile.hrms.web.BaseController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/6
 * @menu 薪资明细
 */
@RestController
@RequestMapping("salary/employee/detail")
public class HrmsSalaryEmployeeDetailController extends BaseController {

    @Autowired
    private HrmsSalaryEmployeeDetailService hrmsSalaryEmployeeDetailService;

    @Autowired
    private ConverterService converterService;
    /**
     * 列表查询
     * @param query
     * @return
     */
    @PostMapping("list")
    public Result<PaginationResult<SalaryEmployeeDetailVO>> list(@RequestBody SalaryEmployeeDetailQuery query){
        return Result.ok(salaryEmployeeDetail(query));
    }

    /**
     * 列表导出
     * @param request
     * @param query
     * @return
     */
    @PostMapping("list/export")
    public Result<PaginationResult<SalaryEmployeeDetailExportVO>> listExport(HttpServletRequest request , SalaryEmployeeDetailQuery query){
        this.setExcelCallBackParam(request,query);
        PaginationResult<SalaryEmployeeDetailVO> result = salaryEmployeeDetail(query);
        PaginationResult<SalaryEmployeeDetailExportVO> exportVOList = this.convertPage(result, SalaryEmployeeDetailExportVO.class);
        if (CollectionUtils.isNotEmpty(exportVOList.getResults())) {
            Map<String, SalaryEmployeeDetailVO> collect = result.getResults().stream().collect(Collectors.toMap(o -> o.getUserCode() + "-" + o.getDeptName() + "-" + o.getPostName() + "-" + o.getTimeCycle(), o -> o, (v1, v2) -> v1));
            for (SalaryEmployeeDetailExportVO item : exportVOList.getResults()) {
                String key = item.getUserCode() + "-" + item.getDeptName() + "-" + item.getPostName() + "-" + item.getTimeCycle();
                SalaryEmployeeDetailVO detailVO = collect.get(key);
                BigDecimal offDayOvertimePay = detailVO.getOffDayOvertimePay() != null ? detailVO.getOffDayOvertimePay() : BigDecimal.ZERO;
                BigDecimal workdayOvertimePay = detailVO.getWorkdayOvertimePay() != null ? detailVO.getWorkdayOvertimePay() : BigDecimal.ZERO;
                BigDecimal holidayOvertimePay = detailVO.getHolidayOvertimePay() != null ? detailVO.getHolidayOvertimePay() : BigDecimal.ZERO;
                BigDecimal overtimePay = offDayOvertimePay.add(workdayOvertimePay).add(holidayOvertimePay).setScale(2, BigDecimal.ROUND_HALF_UP);
                item.setOvertimePay(overtimePay);
            }
        }
        return Result.ok(exportVOList);
    }

    /**
     *  统计应付薪资
     * @param query
     * @return
     */
    @PostMapping("getAccruedPay")
    public Result<EmployeeAccruedPayVO> getAccruedPay(@RequestBody SalaryEmployeeDetailQuery query) {
        return Result.ok(getTotalAccruedPay(query));
    }


    /**
     *  统计人力成本明细
     * @param query
     * @return
     */
    @PostMapping("listHumanCost")
    public Result<PaginationResult<HumanCostDetailVO>> listHumanCostStatistics(@RequestBody HumanCostStatisticsQuery query) {
        return Result.ok(humanCostDetail(query));
    }

    /**
     *  统计人力成本
     * @param query
     * @return
     */
    @PostMapping("getToatalHumanCost")
    public Result<EmployeeHumanCostVO> getToatalHumanCost(@RequestBody HumanCostStatisticsQuery query) {
        return Result.ok(getHumanCost(query));
    }

    /**
     * 查询Attendance Detail list
     *
     * @param query
     * @return
     */
    private PaginationResult<SalaryEmployeeDetailVO> salaryEmployeeDetail(SalaryEmployeeDetailQuery query) {
        if(query.getEndTime()!=null){
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }
        PaginationResult<SalaryEmployeeDetailDTO> pageResult = hrmsSalaryEmployeeDetailService.list(query);

        PaginationResult<SalaryEmployeeDetailVO> salaryEmployeeDetailVOPaginationResult = this.convertPage(pageResult, SalaryEmployeeDetailVO.class);
        // 返回时区处理
        converterService.withAnnotation(salaryEmployeeDetailVOPaginationResult.getResults());
        return salaryEmployeeDetailVOPaginationResult;

    }

    private PaginationResult<HumanCostDetailVO> humanCostDetail(HumanCostStatisticsQuery query) {
        if(query.getEndTime()!=null){
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }
        PaginationResult<HumanCostDetailDTO> pageResult = hrmsSalaryEmployeeDetailService.listHumanCostStatistics(query);

        PaginationResult<HumanCostDetailVO> paginationResult = this.convertPage(pageResult, HumanCostDetailVO.class);
        // 返回时区处理
        converterService.withAnnotation(paginationResult.getResults());
        return paginationResult;

    }

    private EmployeeAccruedPayVO getTotalAccruedPay(SalaryEmployeeDetailQuery query) {
        if(query.getEndTime()!=null){
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }

        EmployeeAccruedPayDTO employeeAccruedPayDTO = hrmsSalaryEmployeeDetailService.getToatalAccruedPay(query);
        return BeanUtils.convert(employeeAccruedPayDTO, EmployeeAccruedPayVO.class);
    }

    private EmployeeHumanCostVO getHumanCost(HumanCostStatisticsQuery query) {
        if(query.getEndTime()!=null){
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }

        EmployeeHumanCostDTO employeeHumanCostDTO = hrmsSalaryEmployeeDetailService.getToatalHumanCost(query);
        return BeanUtils.convert(employeeHumanCostDTO, EmployeeHumanCostVO.class);
    }
}
