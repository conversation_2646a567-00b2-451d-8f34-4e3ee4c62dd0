package com.imile.hrms.web.achievement.param;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 绩效活动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsEventsCheckParam {


    private Long id;

    /**
     * 活动名称
     */
    private String eventName;

    /**
     * 考核周期类型
     */
    private String cycleType;

    /**
     * 活动适用范围(1组织 2员工)
     */
    private String eventScope;

    /**
     * 活动可见员工
     */
    private List<Long> userIds;



}
