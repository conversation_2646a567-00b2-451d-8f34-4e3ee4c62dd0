package com.imile.hrms.web.organization.controller;

import com.imile.common.component.repeat.DuplicateSubmit;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.common.validator.Groups;
import com.imile.hrms.common.annotation.NoAuthRequired;
import com.imile.hrms.common.annotation.NoHrmsLoginAuthRequired;
import com.imile.hrms.common.annotation.NoLoginAuthRequired;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.dao.common.StatusSwitchParamDTO;
import com.imile.hrms.dao.organization.dto.EntGradeDTO;
import com.imile.hrms.dao.organization.query.GradeQuery;
import com.imile.hrms.service.organization.HrmsEntGradeService;
import com.imile.hrms.web.organization.vo.GradeSelectVO;
import com.imile.hrms.web.organization.vo.GradeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.imile.hrms.web.BaseController;

import java.util.List;

/**
 * <AUTHOR>
 * @menu 职级接口
 * @since 2021-11-05
 */
@RestController
@RequestMapping("ent/grade")
public class HrmsEntGradeController extends BaseController {

    @Autowired
    private HrmsEntGradeService service;

    /**
     * 岗位列表查询
     *
     * @param query
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public Result<PaginationResult<GradeVO>> gradeList(@RequestBody GradeQuery query) {
        //前置参数处理,查询
        PaginationResult<EntGradeDTO> res = service.gradeList(handlerPostQuery(query));
        return Result.ok(this.convertPage(res, GradeVO.class));
    }

    /**
     * 新增岗位
     *
     * @param entGradeDTO
     * @return
     */
    @PostMapping("add")
    @DuplicateSubmit
    public Result<Boolean> addGrade(@Validated(Groups.Add.class) @RequestBody EntGradeDTO entGradeDTO) {
        return Result.ok(service.addGrade(entGradeDTO));
    }

    /**
     * 修改岗位
     *
     * @param entGradeDTO
     * @return
     */
    @PostMapping("update")
    public Result<Boolean> updateGrade(@Validated(Groups.Update.class) @RequestBody EntGradeDTO entGradeDTO) {
        return Result.ok(service.updateGrade(entGradeDTO));
    }

    /**
     * 修改状态
     *
     * @param statusSwitchParamDTO
     * @return
     */
    @PostMapping("/status/switch")
    public Result<Boolean> statusSwitch(@Validated(Groups.Update.class) @RequestBody StatusSwitchParamDTO statusSwitchParamDTO) {
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchParamDTO.getStatus());
        if (statusEnum == null) {
            return Result.fail(MsgCodeConstant.PARAM_INVALID, "status");
        }
        return Result.ok(service.statusSwitch(statusSwitchParamDTO));
    }

    /**
     * 职级下拉接口
     *
     * @param
     * @return
     */
    @PostMapping("selectList")
    @NoLoginAuthRequired
    @NoHrmsLoginAuthRequired
    public Result<List<GradeSelectVO>> selectList() {

        return Result.ok(HrmsCollectionUtils.convert(service.selectList(), GradeSelectVO.class));
    }


    /**
     * 查询类处理
     *
     * @param query
     * @return
     */
    private GradeQuery handlerPostQuery(GradeQuery query) {
        //去除gradeName前后空格
        query.setGradeType(query.getGradeType().trim());
        return query;
    }

}
