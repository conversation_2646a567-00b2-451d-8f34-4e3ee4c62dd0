package com.imile.hrms.interceptor;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.hrms.common.entity.BaseDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * MybatisPlus填充公共字段
 */
@Slf4j
@Component
public class MybatisMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATE_DATE = "createDate";
    private static final String LAST_UPD_DATE = "lastUpdDate";
    private static final String CREATE_USER_CODE = "createUserCode";
    private static final String CREATE_USER_NAME = "createUserName";
    private static final String LAST_UPD_USER_CODE = "lastUpdUserCode";
    private static final String LAST_UPD_USER_NAME = "lastUpdUserName";
    private static final String ADMIN = "admin";


    @Override
    public void insertFill(MetaObject metaObject) {
        Date time = new Date();
        BaseDO baseDO = null;
        try {
            baseDO = (BaseDO) metaObject.getOriginalObject();
        } catch (Exception e) {
            log.error("MybatisPlus填充公共字段对象转换错误,不影响使用");
        }
        if (baseDO != null) {
            if (baseDO.getCreateDate() == null) {
                setFieldValByName(CREATE_DATE, time, metaObject);
            }
            if (baseDO.getLastUpdDate() == null) {
                setFieldValByName(LAST_UPD_DATE, time, metaObject);
            }
        } else {
            setFieldValByName(CREATE_DATE, time, metaObject);
            setFieldValByName(LAST_UPD_DATE, time, metaObject);
        }

        UserContext userInfo = RequestInfoHolder.getLoginInfo();
        setFieldValByName(CREATE_USER_CODE, userInfo == null ? ADMIN : userInfo.getUserCode(), metaObject);
        setFieldValByName(CREATE_USER_NAME, userInfo == null ? ADMIN : userInfo.getUserName(), metaObject);
        setFieldValByName(LAST_UPD_USER_CODE, userInfo == null ? ADMIN : userInfo.getUserCode(), metaObject);
        setFieldValByName(LAST_UPD_USER_NAME, userInfo == null ? ADMIN : userInfo.getUserName(), metaObject);

    }

    /**
     * 需要使用update(T model, Wrapper<T> updateWrapper) 才能生效
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        UserContext userInfo = RequestInfoHolder.getLoginInfo();
        Date time = new Date();
        setFieldValByName(LAST_UPD_USER_CODE, userInfo == null ? ADMIN : userInfo.getUserCode(), metaObject);
        setFieldValByName(LAST_UPD_USER_NAME, userInfo == null ? ADMIN : userInfo.getUserName(), metaObject);
        setFieldValByName(LAST_UPD_DATE, time, metaObject);

    }

    @Override
    public MetaObjectHandler setFieldValByName(String fieldName, Object fieldVal, MetaObject metaObject) {
        Object value = metaObject.getValue(fieldName);
        // 如果值不为空，则表示已经设置过值，此时不再设置值
        if (value == null && Objects.nonNull(fieldVal) && metaObject.hasSetter(fieldName)) {
            metaObject.setValue(fieldName, fieldVal);
        }
        return this;
    }
}

