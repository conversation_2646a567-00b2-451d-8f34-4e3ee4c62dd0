package com.imile.hrms.job.leave;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigIsCarryOverEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigIsInvalidEnum;
import com.imile.hrms.common.enums.leave.LeaveTypeEnum;
import com.imile.hrms.common.util.CommonUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigCarryOverDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigCarryOverRangeDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveDetailDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.dao.user.query.CompanyLeaveQuery;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.dao.user.query.UserLeaveDetailQuery;
import com.imile.hrms.dao.user.query.UserLeaveStageDetailQuery;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.manage.company.HrmsCompanyLeaveConfigManage;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.manage.user.HrmsUserLeaveDetailManage;
import com.imile.hrms.manage.user.HrmsUserLeaveStageDetailManage;
import com.imile.hrms.service.leave.HrmsCompanyLeaveConfigCarryOverService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.hrms.common.entity.BaseDOUtil.fillDOInsert;

/**
 * {@code @author:} allen
 * {@code @className:} UserLeaveCarryOverHandler
 * {@code @since:} 2024-04-29 16:22
 * {@code @description:} 用户假期结转处理
 */
@Slf4j
@Component
public class UserLeaveCarryOverHandler {
    @Autowired
    private HrmsCompanyLeaveConfigManage companyLeaveConfigManage;
    @Autowired
    private HrmsCompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Autowired
    private CountryService countryService;
    @Autowired
    private HrmsUserInfoDao userInfoDao;
    @Autowired
    private HrmsUserLeaveDetailManage userLeaveDetailManage;
    @Autowired
    private HrmsUserLeaveStageDetailManage hrmsUserLeaveStageDetailManage;
    @Autowired
    private HrmsUserEntryRecordManage userEntryRecordManage;
    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;
    private static final Integer PAGE_SIZE_DEFAULT = 5000;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_CARRY_OVER_HANDLER)
    public ReturnT<String> userLeaveCarryOverHandler(String content) {
        UserLeaveCarryOverHandler.UserLeaveCarryOverHandlerParma param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, UserLeaveCarryOverHandler.UserLeaveCarryOverHandlerParma.class) : new UserLeaveCarryOverHandler.UserLeaveCarryOverHandlerParma();
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】参数为空");
            return ReturnT.SUCCESS;
        }
        // 定时任务执行默认参数
        this.buildDefaultParam(param);
        XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】开始执行，参数为：{}", JSON.toJSONString(param));
        List<String> countryList;
        if (CollectionUtils.isEmpty(param.getCountryArrayList())) {
            CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            List<HrmsCompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.selectCompanyLeave(companyLeaveQuery);
            if (companyLeaveConfigList.isEmpty()) {
                XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】companyLeaveConfigList is Empty");
                return ReturnT.SUCCESS;
            }
            // 获取通过假期分组
            countryList = companyLeaveConfigList.stream().map(HrmsCompanyLeaveConfigDO::getCountry).collect(Collectors.toList());
        } else {
            countryList = param.getCountryArrayList();
        }
        // 查询人员
        int currentPage = 1, pageSize = PAGE_SIZE_DEFAULT;
        Page<HrmsUserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .locationCountryList(countryList)
                .userCodes(param.getUserCodeList())
                .employeeTypes(param.getEmployeeTypeList())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDelete(IsDeleteEnum.NO.getCode())
                .build();
        PageInfo<HrmsUserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userDaoQuery));
        int userCount = BusinessConstant.ZERO.intValue();
        // 总记录数
        List<HrmsUserInfoDO> pageUserInfoList = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
            userCount = pageUserInfoList.size();
            handleUserCarryOver(pageUserInfoList, countryList, param);
        }
        XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】 | currentPage:{},pageSize:{},total:{},pages：{}"
                , currentPage, pageSize, pageInfo.getTotal(), pageInfo.getPages());
        while (currentPage < pageInfo.getPages()) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】 | 进入while循环");
            currentPage++;
            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userDaoQuery));
            pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】 | while循环：pageUserInfoList size:{}，pageUserInfoList：{}"
                        , pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                userCount = userCount + pageUserInfoList.size();
                handleUserCarryOver(pageUserInfoList, countryList, param);
            }
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】 | while循环：currentPage:{},pageSize:{},total:{}"
                    , currentPage, pageSize, pageInfo.getTotal());
        }
        XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】 | currentPage {}，userCount {} while循环结束", currentPage, userCount);
        return ReturnT.SUCCESS;
    }

    private void handleUserCarryOver(List<HrmsUserInfoDO> userInfoList, List<String> countryList
            , UserLeaveCarryOverHandlerParma param) {
        // 查询用户假期详情
        List<Long> userIds = userInfoList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
        UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                .userIds(userIds)
                .leaveNameList(param.getLeaveNameList())
                .build();
        // 获取员工入职信息
        List<HrmsUserEntryRecordDO> userEntryRecordList = userEntryRecordManage.selectUserEntryByUserIds(userIds);
        // 获取该国家所有员工拥有的假期
        List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailManage.selectUserLeaveDetail(userLeaveDetailQuery);
        // 获取该国家所有员工拥有的假期的主键
        List<Long> leaveIdList = userLeaveDetailDOList.stream().map(HrmsUserLeaveDetailDO::getId).collect(Collectors.toList());
        // 通过用户详情获取假期规则主键及假期规则配置
        List<Long> configIdList = userLeaveDetailDOList.stream().map(HrmsUserLeaveDetailDO::getConfigId).distinct().collect(Collectors.toList());
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.getByIdList(configIdList);
        // 获取该国家所有员工拥有的生效的并且未结转假期详情数据
        UserLeaveStageDetailQuery userLeaveStageDetailQuery = new UserLeaveStageDetailQuery();
        userLeaveStageDetailQuery.setIsInvalid(WhetherEnum.NO.getKey());
        userLeaveStageDetailQuery.setLeaveMark(WhetherEnum.NO.getKey());
        userLeaveStageDetailQuery.setLeaveIdList(leaveIdList);
        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = hrmsUserLeaveStageDetailManage.selectByCondition(userLeaveStageDetailQuery);
        // 由于需要获取时区，所以这边获取国家的列表
        List<String> countryInfoList = companyLeaveConfigList.stream().map(HrmsCompanyLeaveConfigDO::getCountry).distinct().collect(Collectors.toList());
        // 获取假期结转规则
        List<HrmsCompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList = companyLeaveConfigCarryOverService.selectByLeaveId(configIdList);
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        if (CollectionUtils.isEmpty(countryList)) {
            // 如果没有指定国家，则查询有设置假期的所有国家
            countryQuery.setCountryNames(countryInfoList);
        }
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));
        // 获取当前时间
        Date now = this.getNowDateByParam(param);
        // 新增用户假期集合
        List<HrmsUserLeaveDetailDO> addUserLeaveDetailList = Lists.newArrayList();
        // 更新用户假期集合
        List<HrmsUserLeaveDetailDO> updateUserLeaveDetailDOList = new ArrayList<>();
        // 新增用户假期详情集合
        List<HrmsUserLeaveStageDetailDO> addUserLeaveStageDetailList = Lists.newArrayList();
        // 更新用户假期详情集合
        List<HrmsUserLeaveStageDetailDO> updateUserLeaveStageDetailList = Lists.newArrayList();
        // 新增用户假期操作记录集合
        List<HrmsUserLeaveRecordDO> addUserLeaveRecordList = Lists.newArrayList();

        // 分组通过国家处理
        for (String country : countryList) {
            // 获取国家对应时区
            String timeZone = countryConfigMap.getOrDefault(country, "");
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("该国家:{},不存在国家时区", country);
                continue;
            }
            XxlJobLogger.log("当前国家:{},时区:{}", country, timeZone);
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            if (param.getIsUseCustomLocalTime()) {
                // 自定义时间，不根据国家时区来计算
                dateTime = now;
            }
            XxlJobLogger.log("【当前时间明天的时间】，北京时间:{},当前国家:{},当地时间:{}", now, country, dateTime);
            int year = DateUtil.year(dateTime);
            int month = DateUtil.month(dateTime) + 1;
            int day = DateUtil.dayOfMonth(dateTime);
            int hour = DateUtil.hour(dateTime, true);
            XxlJobLogger.log("当前国家:{},年:{},月:{},日:{}", country, year, month, day);

            // 获取每一个国家当前的时间，每一个国家年底的最后一天做结转假期数据
            if (month != param.localMonth || day != param.localDay || hour != param.localHour) {
                XxlJobLogger.log("当前国家:{},本地时间：{},非年底最后一天，不做结转假期余额数据", country, dateTime);
                continue;
            }
            // 过滤国家下员工
            List<HrmsUserInfoDO> userInfoByCountry = userInfoList.stream()
                    .filter(user -> country.equals(user.getLocationCountry()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userInfoByCountry)) {
                XxlJobLogger.log("该国家:{},不存在人员", country);
                continue;
            }
            List<Long> userIdByCountry = userInfoByCountry.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
            Map<Long, List<HrmsUserInfoDO>> userMapByCountry = userInfoByCountry.stream().collect(Collectors.groupingBy(HrmsUserInfoDO::getId));
            // 过滤国家下员工对应得假期详情
            List<HrmsUserLeaveDetailDO> leaveDetailByCountry = userLeaveDetailDOList.stream()
                    .filter(detail -> userIdByCountry.contains(detail.getUserId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveDetailByCountry)) {
                XxlJobLogger.log("该国家:{},不存在人员假期详情", country);
                continue;
            }
            // 过滤员工对应得假期详情id
            List<Long> userLeaveDetailIdList = leaveDetailByCountry.stream().map(HrmsUserLeaveDetailDO::getId).collect(Collectors.toList());
            // 过滤员工对应得假期规则
            List<Long> userConfigIdList = leaveDetailByCountry.stream().map(HrmsUserLeaveDetailDO::getConfigId).collect(Collectors.toList());
            List<HrmsCompanyLeaveConfigDO> userCompanyLeaveConfig = companyLeaveConfigList.stream()
                    .filter(config -> userConfigIdList.contains(config.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userCompanyLeaveConfig)) {
                XxlJobLogger.log("该国家:{},不存在对应人员假期规则", country);
                continue;
            }
            // 过滤员工对应得假期结转规则
            List<Long> userLeaveConfigId = userCompanyLeaveConfig.stream().map(HrmsCompanyLeaveConfigDO::getId).collect(Collectors.toList());
            List<HrmsCompanyLeaveConfigCarryOverDO> userCarryOverList = companyLeaveConfigCarryOverList.stream()
                    .filter(carry -> userLeaveConfigId.contains(carry.getLeaveId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userCarryOverList)) {
                XxlJobLogger.log("该国家:{},不存在对应人员假期结转规则", country);
                continue;
            }
            // 将国家的假期结转规则根据leaveId转为map
            Map<Long, HrmsCompanyLeaveConfigCarryOverDO> userLeaveConfigCarryOverMap = userCarryOverList.stream()
                    .collect(Collectors.toMap(HrmsCompanyLeaveConfigCarryOverDO::getLeaveId, Function.identity()));
            // 获取假期余额详情:非结转数据
            List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailByCountry = userLeaveStageDetailList.stream()
                    .filter(item -> userLeaveDetailIdList.contains(item.getLeaveId())
                            && WhetherEnum.NO.getKey().equals(item.getLeaveMark()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userLeaveStageDetailByCountry)) {
                XxlJobLogger.log("该国家:{},不存在对应人员假期非结转数据", country);
                continue;
            }
            Map<Long, List<HrmsUserLeaveStageDetailDO>> userLeaveStageDetailMap = userLeaveStageDetailByCountry.stream()
                    .collect(Collectors.groupingBy(HrmsUserLeaveStageDetailDO::getLeaveId));

            // 遍历人员对应得假期配置
            for (HrmsCompanyLeaveConfigDO companyLeaveConfig : userCompanyLeaveConfig) {
                // 结转校验
                if (!checkCarryOver(companyLeaveConfig, userLeaveConfigCarryOverMap)) {
                    continue;
                }
                Long configId = companyLeaveConfig.getId();
                String configName = companyLeaveConfig.getLeaveName();
                DateTime date = DateUtil.date();
                HrmsCompanyLeaveConfigCarryOverDO companyLeaveConfigCarryOver = userLeaveConfigCarryOverMap.get(configId);
                // 校验完成，开始获取假期余额处理用户假期结转数据
                List<HrmsUserLeaveDetailDO> userConfigLeaveDetail = leaveDetailByCountry.stream()
                        .filter(detail -> configId.equals(detail.getConfigId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userConfigLeaveDetail)) {
                    XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},不存在该假期的记录", country, configId, configName);
                    continue;
                }

                if (ObjectUtil.equal(companyLeaveConfigCarryOver.getIsInvalid(), LeaveConfigIsInvalidEnum.YES.getType())) {
                    // 永久有效不做任何操作，永远只会有一条非结转数据，不存在结转数据
                    // 如果假期永久有效，那么一定是可结转的，结转的时间就是用户假期剩余时间
                    // 新增结转的数据
                    for (HrmsUserLeaveDetailDO userLeaveDetailDO : userConfigLeaveDetail) {
                        // 获取该假期之前总的分钟数
                        BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                        // 获取结转的总余额
                        BigDecimal carryOverTotalMinutes = BigDecimal.ZERO;
                        // 获取该假期的用户id
                        Long userId = userLeaveDetailDO.getUserId();
                        HrmsUserInfoDO userInfo = CollectionUtils.isEmpty(userMapByCountry.get(userId)) ? null : userMapByCountry.get(userId).get(0);
                        if (ObjectUtil.isNull(userInfo)) {
                            XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},用户id:{},不存在该用户", country, configId, configName, userId);
                            continue;
                        }
                        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailByLeaveId = userLeaveStageDetailMap.get(userLeaveDetailDO.getId());
                        if (CollectionUtils.isEmpty(userLeaveStageDetailByLeaveId)) {
                            XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},用户id:{},该用户不存在假期余额", country, configId, configName, userId);
                            continue;
                        }
                        // 失效用户对假期详情表中非结转的数据
                        for (HrmsUserLeaveStageDetailDO userLeaveStageDetail : userLeaveStageDetailByLeaveId) {
                            this.buildUpdateLeaveStageDetail(userLeaveStageDetail, date, updateUserLeaveStageDetailList);
                            oldTotalMinutes = oldTotalMinutes.add(userLeaveStageDetail.getLeaveResidueMinutes());
                        }
                        // 新增失效操作记录
                        if (oldTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                            addLeaveRecord(userInfo, configId, oldTotalMinutes, addUserLeaveRecordList
                                    , "false", dateTime, companyLeaveConfig);
                        }
                        for (HrmsUserLeaveStageDetailDO leaveStageDetail : userLeaveStageDetailByLeaveId) {
                            if (leaveStageDetail.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            // 新增结转假期余额
                            HrmsUserLeaveStageDetailDO userLeaveStageDetailDO = this.buildLeaveStageDetail(leaveStageDetail
                                    , userLeaveDetailDO.getId(), date, leaveStageDetail.getLeaveResidueMinutes()
                                    , addUserLeaveStageDetailList);
                            carryOverTotalMinutes = carryOverTotalMinutes.add(userLeaveStageDetailDO.getLeaveResidueMinutes());
                        }
                        // 新增结转重新发假操作记录
                        if (carryOverTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                            addLeaveRecord(userInfo, configId, carryOverTotalMinutes, addUserLeaveRecordList
                                    , "true", dateTime, companyLeaveConfig);
                        }
                    }
                    // 假期永久有效
                    continue;
                }
                // 如果假期非永久有效，需要结转
                if (ObjectUtil.equal(companyLeaveConfigCarryOver.getIsCarryOver(), LeaveConfigIsCarryOverEnum.YES.getType())) {
                    // 获取最大结转天数
                    Integer maxCarryOverDay = companyLeaveConfigCarryOver.getMaxCarryOverDay();
                    if (ObjectUtil.isNull(maxCarryOverDay)) {
                        XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},假期结转规则：非永久有效，假期余额可结转，但是最大结转天数未配置", country, configId, configName);
                        continue;
                    }
                    // 将天转换成分钟，一天8个小时，一小时60分钟
                    int maxCarryOverMinutes = maxCarryOverDay * 8 * 60;
                    // 将int转换为BigDecimal
                    BigDecimal maxCarryOverMinutesBigDecimal = BigDecimal.valueOf(maxCarryOverMinutes);
                    for (HrmsUserLeaveDetailDO userLeaveDetailDO : userConfigLeaveDetail) {
                        // 获取该假期之前总的分钟数
                        BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                        // 获取结转的总余额
                        BigDecimal carryOverTotalMinutes = BigDecimal.ZERO;
                        // 备份一个最大结转天数，每一次循环不同的假期
                        BigDecimal maxCarryOverMinutesBigDecimalBak = maxCarryOverMinutesBigDecimal;
                        // 获取该假期的用户id
                        Long userId = userLeaveDetailDO.getUserId();
                        HrmsUserInfoDO userInfo = CollectionUtils.isEmpty(userMapByCountry.get(userId)) ? null : userMapByCountry.get(userId).get(0);
                        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailByLeaveId = userLeaveStageDetailMap.get(userLeaveDetailDO.getId());
                        if (CollectionUtils.isEmpty(userLeaveStageDetailByLeaveId)) {
                            XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},用户id:{},该用户不存在假期余额", country, configId, configName, userId);
                            continue;
                        }
                        // 如果该人员的假期余额只有一个阶梯
                        if (userLeaveStageDetailByLeaveId.size() == 1) {
                            HrmsUserLeaveStageDetailDO userLeaveStageDetail = userLeaveStageDetailByLeaveId.get(0);
                            this.buildUpdateLeaveStageDetail(userLeaveStageDetail, date, updateUserLeaveStageDetailList);
                            oldTotalMinutes = oldTotalMinutes.add(userLeaveStageDetail.getLeaveResidueMinutes());
                            // 新增操作记录
                            if (oldTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                                addLeaveRecord(userInfo, configId, oldTotalMinutes, addUserLeaveRecordList
                                        , "false", dateTime, companyLeaveConfig);
                            }
                            if (userLeaveStageDetail.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            HrmsUserLeaveStageDetailDO userLeaveStageDetailDO = this.buildLeaveStageDetail(userLeaveStageDetail
                                    , userLeaveDetailDO.getId(), date
                                    , maxCarryOverMinutesBigDecimalBak
                                    , addUserLeaveStageDetailList);
                            carryOverTotalMinutes = carryOverTotalMinutes.add(userLeaveStageDetailDO.getLeaveResidueMinutes());


                            // 新增操作记录
                            if (carryOverTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                                addLeaveRecord(userInfo, configId, carryOverTotalMinutes, addUserLeaveRecordList
                                        , "true", dateTime, companyLeaveConfig);
                            }
                            continue;
                        }
                        // 否则就是多个阶段的余额，优先结转薪资比率高的
                        List<HrmsUserLeaveStageDetailDO> reversedUserLeaveStageDetailList = userLeaveStageDetailByLeaveId.stream()
                                .sorted(Comparator.comparing(HrmsUserLeaveStageDetailDO::getPercentSalary).reversed())
                                .collect(Collectors.toList());

                        // 失效掉用户对假期详情表中非结转的数据
                        for (HrmsUserLeaveStageDetailDO userLeaveStageDetail : userLeaveStageDetailByLeaveId) {
                            this.buildUpdateLeaveStageDetail(userLeaveStageDetail, date, updateUserLeaveStageDetailList);
                            oldTotalMinutes = oldTotalMinutes.add(userLeaveStageDetail.getLeaveResidueMinutes());
                        }
                        // 新增操作记录
                        if (oldTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                            addLeaveRecord(userInfo, configId, oldTotalMinutes, addUserLeaveRecordList
                                    , "false", dateTime, companyLeaveConfig);
                        }

                        // 记录用户假期详情集合
                        List<HrmsUserLeaveStageDetailDO> recordUserLeaveStageDetailList = Lists.newArrayList();

                        for (HrmsUserLeaveStageDetailDO userLeaveStageDetail : reversedUserLeaveStageDetailList) {
                            BigDecimal leaveResidueMinutes = userLeaveStageDetail.getLeaveResidueMinutes();
                            if (leaveResidueMinutes.compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            // 如果该阶段的假期余额大于最大结转天数，那么结转的时间就是最大结转天数
                            if (leaveResidueMinutes.compareTo(maxCarryOverMinutesBigDecimalBak) >= 0) {
                                HrmsUserLeaveStageDetailDO userLeaveStageDetailDO = this.buildLeaveStageDetail(userLeaveStageDetail
                                        , userLeaveDetailDO.getId(), date, maxCarryOverMinutesBigDecimalBak
                                        , addUserLeaveStageDetailList);
                                // 为了后面计算时长准备
                                recordUserLeaveStageDetailList.add(userLeaveStageDetailDO);
                                // 这边直接break，因为已经结转了最大结转天数
                                break;
                            }
                            // 如果该阶段的假期余额小于最大结转天数，那么结转的时间就是假期余额
                            HrmsUserLeaveStageDetailDO userLeaveStageDetailDO = this.buildLeaveStageDetail(userLeaveStageDetail
                                    , userLeaveDetailDO.getId(), date, userLeaveStageDetail.getLeaveResidueMinutes()
                                    , addUserLeaveStageDetailList);
                            // 为了后面计算时长准备
                            recordUserLeaveStageDetailList.add(userLeaveStageDetailDO);

                            // 更新结转时间，为后续循环准备
                            maxCarryOverMinutesBigDecimalBak = maxCarryOverMinutesBigDecimalBak.subtract(leaveResidueMinutes);
                        }
                        // 计算结转的总余额
                        for (HrmsUserLeaveStageDetailDO recordStageDetail : recordUserLeaveStageDetailList) {
                            carryOverTotalMinutes = carryOverTotalMinutes.add(recordStageDetail.getLeaveResidueMinutes());
                        }
                        // 新增操作记录
                        if (carryOverTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                            addLeaveRecord(userInfo, configId, carryOverTotalMinutes, addUserLeaveRecordList
                                    , "true", dateTime, companyLeaveConfig);
                        }
                    }
                    // 如果假期非永久有效，不可结转,直接失效非结转数据
                    continue;
                }
                // 不可结转：直接删除非结转数据
                XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},假期结转规则：非永久有效，假期余额不可结转，所以直接删除非结转数据。", country, configId, configName);
                for (HrmsUserLeaveDetailDO userLeaveDetailDO : userConfigLeaveDetail) {
                    // 获取该假期之前总的分钟数
                    BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                    // 获取该假期的用户id
                    Long userId = userLeaveDetailDO.getUserId();
                    HrmsUserInfoDO userInfo = CollectionUtils.isEmpty(userMapByCountry.get(userId)) ? null : userMapByCountry.get(userId).get(0);
                    List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailByLeaveId = userLeaveStageDetailMap.get(userLeaveDetailDO.getId());
                    if (CollectionUtils.isEmpty(userLeaveStageDetailByLeaveId)) {
                        XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},用户id:{},该用户不存在假期余额", country, configId, configName, userId);
                        continue;
                    }
                    // 逻辑删除用户对假期详情表中非结转的数据
                    for (HrmsUserLeaveStageDetailDO userLeaveStageDetail : userLeaveStageDetailByLeaveId) {
                        this.buildUpdateLeaveStageDetail(userLeaveStageDetail, date, updateUserLeaveStageDetailList);
                        oldTotalMinutes = oldTotalMinutes.add(userLeaveStageDetail.getLeaveResidueMinutes());
                    }
                    // 新增操作记录
                    if (oldTotalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                        addLeaveRecord(userInfo, configId, oldTotalMinutes, addUserLeaveRecordList
                                , "false", dateTime, companyLeaveConfig);
                    }
                }
            }
        }
        // 落库
        userLeaveDetailManage.userLeaveBalanceDaysUpdate(addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveStageDetailList, updateUserLeaveDetailDOList);
    }

    /**
     * 参数默认值设置
     *
     * @param param
     */
    private void buildDefaultParam(UserLeaveCarryOverHandlerParma param) {
        if (ObjectUtil.isNull(param.getLocalYear())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】年份为空，设置默认值为当年");
            // 获取当前年份
            int year = DateUtil.year(DateUtil.date());
            param.setLocalYear(year);
        }
        if (ObjectUtil.isNull(param.getLocalMonth())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】年份为空，设置默认值12");
            param.setLocalMonth(BusinessConstant.MONTH_YEAR);
        }
        if (ObjectUtil.isNull(param.getLocalDay())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】日为空，设置默认值31");
            param.setLocalDay(BusinessConstant.MONTH_DAY);
        }
        if (ObjectUtil.isNull(param.getLocalHour())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】时为空，设置默认值23");
            param.setLocalHour(BusinessConstant.HOUR_DAY_LAST);
        }
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            param.setEmployeeTypeList(Arrays.asList(param.getEmployeeType().split(",")));
        }
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            param.setUserCodeList(Arrays.asList(param.getUserCodes().split(",")));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            param.setCountryArrayList(Arrays.asList(param.getCountryList().split(",")));
        }
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            param.setLeaveNameList(Arrays.asList(param.getLeaveName().split(",")));
        }
    }

    private HrmsUserLeaveStageDetailDO buildLeaveStageDetail(HrmsUserLeaveStageDetailDO leaveStageDetail
            , Long leaveId
            , DateTime date, BigDecimal maxCarryOverMinutesBigDecimal
            , List<HrmsUserLeaveStageDetailDO> addUserLeaveStageDetailList) {
        HrmsUserLeaveStageDetailDO hrmsUserLeaveStageDetailDO = new HrmsUserLeaveStageDetailDO();
        hrmsUserLeaveStageDetailDO.setLeaveId(leaveId);
        hrmsUserLeaveStageDetailDO.setLeaveMark(WhetherEnum.YES.getKey());
        hrmsUserLeaveStageDetailDO.setIsInvalid(WhetherEnum.NO.getKey());
        hrmsUserLeaveStageDetailDO.setLeaveResidueMinutes(leaveStageDetail.getLeaveResidueMinutes());
        // 如果假期余额大于最大结转天数，那么结转的时间就是最大结转天数
        if (leaveStageDetail.getLeaveResidueMinutes().compareTo(maxCarryOverMinutesBigDecimal) > 0) {
            hrmsUserLeaveStageDetailDO.setLeaveResidueMinutes(maxCarryOverMinutesBigDecimal);
        }
        // 结转数据设置已使用为0
        hrmsUserLeaveStageDetailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
        hrmsUserLeaveStageDetailDO.setStage(leaveStageDetail.getStage());
        hrmsUserLeaveStageDetailDO.setPercentSalary(leaveStageDetail.getPercentSalary());
        hrmsUserLeaveStageDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
        hrmsUserLeaveStageDetailDO.setIsDelete(IsDeleteEnum.NO.getCode());
        hrmsUserLeaveStageDetailDO.setIssueDate(leaveStageDetail.getIssueDate());
        hrmsUserLeaveStageDetailDO.setInvalidDate(leaveStageDetail.getInvalidDate());
        hrmsUserLeaveStageDetailDO.setRecordVersion(1L);
        hrmsUserLeaveStageDetailDO.setCreateDate(date);
        hrmsUserLeaveStageDetailDO.setCreateUserCode("xxl-job");
        hrmsUserLeaveStageDetailDO.setCreateUserName("xxl-job");
        hrmsUserLeaveStageDetailDO.setLastUpdDate(date);
        hrmsUserLeaveStageDetailDO.setLastUpdUserCode("xxl-job");
        hrmsUserLeaveStageDetailDO.setLastUpdUserName("xxl-job");
        addUserLeaveStageDetailList.add(hrmsUserLeaveStageDetailDO);
        return hrmsUserLeaveStageDetailDO;
    }

    private void buildUpdateLeaveStageDetail(HrmsUserLeaveStageDetailDO userLeaveStageDetail, DateTime date
            , List<HrmsUserLeaveStageDetailDO> updateUserLeaveStageDetailList) {
        userLeaveStageDetail.setIsInvalid(WhetherEnum.YES.getKey());
        userLeaveStageDetail.setLastUpdDate(date);
        userLeaveStageDetail.setLastUpdUserCode("xxl-job");
        userLeaveStageDetail.setLastUpdUserName("xxl-job");
        updateUserLeaveStageDetailList.add(userLeaveStageDetail);
    }

    /**
     * 结转校验
     *
     * @param companyLeaveConfig
     * @param userLeaveConfigCarryOverMap
     * @return
     */
    private Boolean checkCarryOver(HrmsCompanyLeaveConfigDO companyLeaveConfig
            , Map<Long, HrmsCompanyLeaveConfigCarryOverDO> userLeaveConfigCarryOverMap) {
        // 获取假期配置id
        Long leaveId = companyLeaveConfig.getId();
        // 假期类型
        String leaveName = companyLeaveConfig.getLeaveName();
        // 有效期预览
        String useStartDate = companyLeaveConfig.getUseStartDate();
        String useEndDate = companyLeaveConfig.getUseEndDate();
        if (StringUtils.isBlank(useStartDate) || StringUtils.isBlank(useEndDate)) {
            XxlJobLogger.log("error：假期配置id:{},假期名称:{},假期有效期未配置", leaveId, leaveName);
            return false;
        }
        // 获取假期结转规则
        HrmsCompanyLeaveConfigCarryOverDO companyLeaveConfigCarryOver = userLeaveConfigCarryOverMap.get(leaveId);
        if (ObjectUtil.isNull(companyLeaveConfigCarryOver)) {
            XxlJobLogger.log("error：当前国家:{},假期名称:{},不存在假期结转规则", leaveId, leaveName);
            return false;
        }
        // 校验假期结转规则
        // 是否永久有效
        if (ObjectUtil.isNull(companyLeaveConfigCarryOver.getIsInvalid()) || ObjectUtil.equal(companyLeaveConfigCarryOver.getIsInvalid(), LeaveConfigIsInvalidEnum.DEFAULT.getType())) {
            XxlJobLogger.log("error：假期配置id:{},假期名称:{},假期结转规则：是否永久有效未配置", leaveId, leaveName);
            return false;
        }
        // 是否可结转
        if (ObjectUtil.isNull(companyLeaveConfigCarryOver.getIsCarryOver()) || ObjectUtil.equal(companyLeaveConfigCarryOver.getIsCarryOver(), LeaveConfigIsCarryOverEnum.DEFAULT.getType())) {
            XxlJobLogger.log("error：假期配置id:{},假期名称:{},假期结转规则：是否可结转未配置", leaveId, leaveName);
            return false;
        }

        // 永久有效情况
        // 如果假期永久有效，那么一定是可结转的，结转的时间就是用户假期剩余时间
        if (ObjectUtil.equal(companyLeaveConfigCarryOver.getIsInvalid(), LeaveConfigIsInvalidEnum.YES.getType())
                && ObjectUtil.notEqual(companyLeaveConfigCarryOver.getIsCarryOver(), LeaveConfigIsCarryOverEnum.YES.getType())) {
            XxlJobLogger.log("error：假期配置id:{},假期名称:{},假期结转规则：永久有效，假期余额不是可结转", leaveId, leaveName);
            return false;
        }
        // 非永久有效情况
        // 如果假期不是永久有效，并且是可结转的，需要判断参数是否配置
        if (ObjectUtil.equal(companyLeaveConfigCarryOver.getIsInvalid(), LeaveConfigIsInvalidEnum.NO.getType())
                && ObjectUtil.equal(companyLeaveConfigCarryOver.getIsCarryOver(), LeaveConfigIsCarryOverEnum.YES.getType())) {
            if (ObjectUtil.isNull(companyLeaveConfigCarryOver.getMaxCarryOverDay()) ||
                    ObjectUtil.equal(companyLeaveConfigCarryOver.getMaxCarryOverDay(), 0)) {
                XxlJobLogger.log("error：假期配置id:{},假期名称:{},假期结转规则：非永久有效，假期余额是可结转，但是最大结转天数未配置", leaveId, leaveName);
                return false;

            }
            if (ObjectUtil.isNull(companyLeaveConfigCarryOver.getInvalidDate()) ||
                    ObjectUtil.equal(companyLeaveConfigCarryOver.getInvalidDate(), 0)) {
                XxlJobLogger.log("error：假期配置id:{},假期名称:{},假期结转规则：非永久有效，假期余额是可结转，但是失效日期未配置", leaveId, leaveName);
                return false;
            }
        }
        return true;
    }

    /**
     * 获取当前时间
     *
     * @param param
     * @return
     */
    private Date getNowDateByParam(UserLeaveCarryOverHandlerParma param) {
        Date now = new Date();
        // 获取当前时间，明天
        if (param.getIsUseCustomLocalTime()) {
            // 根据当前时间年份月日时生成日期
            String localDate = param.getLocalYear() + "-" + param.localMonth + "-" + param.localDay + " " + param.localHour + ":00:00";
            now = DateUtil.parse(localDate);
        }
        return now;
    }

    /**
     * 增加假期调整操作记录
     *
     * @param userInfo               用户信息
     * @param leaveConfigId          假期类型
     * @param totalMinutes           假期总分钟数
     * @param addUserLeaveRecordList 假期操作记录集合
     * @param flag                   是否销假【true:销假，false:请假】
     * @param date                   本地当前时间
     * @param companyLeaveConfig     假期规则
     */
    private void addLeaveRecord(HrmsUserInfoDO userInfo, Long leaveConfigId, BigDecimal totalMinutes
            , List<HrmsUserLeaveRecordDO> addUserLeaveRecordList, String flag, Date date
            , HrmsCompanyLeaveConfigDO companyLeaveConfig) {
        HrmsUserLeaveRecordDO recordDO = new HrmsUserLeaveRecordDO();
        recordDO.setId(iHrmsIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(date);
        recordDO.setDayId(Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN)));
        recordDO.setConfigId(leaveConfigId);
        recordDO.setLeaveName(companyLeaveConfig.getLeaveName());
        recordDO.setLeaveType(companyLeaveConfig.getLeaveType());
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
        } else {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
        }
        recordDO.setLeaveMinutes(totalMinutes);
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setRemark("系统初始化【假期数据结转】/System initialization");
        } else {
            recordDO.setRemark("系统初始化【用户假期数据结转-删除旧数据】/System initialization");
        }
        fillDOInsert(recordDO);
        // 防止查询时间相同，导致根据创建时间排序出现先后顺序问题【根据创建时间倒序之后，过期清零在新年初始化上面就比较奇怪】，所以这边新年初始化创建时间需要在过期清零后面，
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setCreateDate(DateUtil.offsetSecond(new Date(), 1));
            recordDO.setLastUpdDate(DateUtil.offsetSecond(new Date(), 1));
        }
        // 这边修改一下操作人code操作人name，跟发放余额记录区分一下，因为发放余额会拿操作记录做校验。
        recordDO.setOperationUserCode("xxl-job-carry-over");
        recordDO.setOperationUserName("xxl-job-carry-over");
        addUserLeaveRecordList.add(recordDO);
    }

    /**
     * 假期结转处理器参数
     */
    @Data
    private static class UserLeaveCarryOverHandlerParma {
        /**
         * 所属国
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;
        /**
         * 假期类型
         */
        private String leaveName;

        /**
         * 用工类型
         */
        private String employeeType;

        /**
         * 定义当地时间年份
         */
        private Integer localYear;

        /**
         * 定义当地时间的月份
         */
        private Integer localMonth = 12;

        /**
         * 定义当地时间的日
         */
        private Integer localDay = 31;

        /**
         * 当地时间的小时
         */
        private Integer localHour = 23;

        /**
         * 是否启用自定义当前时间：true使用自定义时间。false使用当前时间
         */
        private Boolean isUseCustomLocalTime = false;

        /**
         * 程序逻辑转换
         */
        @NotNull
        private List<String> employeeTypeList = new ArrayList<>();
        @NotNull
        private List<String> userCodeList = new ArrayList<>();
        @NotNull
        private List<String> countryArrayList = new ArrayList<>();
        @NotNull
        private List<String> leaveNameList = new ArrayList<>();
    }
}
