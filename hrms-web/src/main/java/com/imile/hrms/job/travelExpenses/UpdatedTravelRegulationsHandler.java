package com.imile.hrms.job.travelExpenses;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.travelExpenses.model.HrmsTravelExpensesConfigDO;
import com.imile.hrms.dao.travelExpenses.model.HrmsTravelExpensesDO;
import com.imile.hrms.service.travelExpenses.HrmsTravelExpensesConfigService;
import com.imile.hrms.service.travelExpenses.HrmsTravelExpensesService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UpdatedTravelRegulationsHandler {

    @Autowired
    private HrmsTravelExpensesService hrmsTravelExpensesService;

    @Autowired
    private HrmsTravelExpensesConfigService hrmsTravelExpensesConfigService;

    @XxlJob(BusinessConstant.JobHandler.UPDATE_TRAVEL_REGULATION)
    public ReturnT<String> updatedTravelRegulationsHandler(String param) {
        List<HrmsTravelExpensesDO> list = hrmsTravelExpensesService.list();
        List<HrmsTravelExpensesConfigDO> expensesConfigAllList = new ArrayList<>();
        for (HrmsTravelExpensesDO hrmsTravelExpensesDO : list) {
            List<Date> dateList =new ArrayList<>();
            dateList.add(hrmsTravelExpensesDO.getEffectTime());
            dateList.add(hrmsTravelExpensesDO.getFutureTime());
            List<HrmsTravelExpensesConfigDO> expensesConfigList = hrmsTravelExpensesConfigService.getBaseMapper().selectList(new LambdaQueryWrapper<HrmsTravelExpensesConfigDO>()
                    .eq(HrmsTravelExpensesConfigDO::getCountryCode, hrmsTravelExpensesDO.getCode())
                    .eq(HrmsTravelExpensesConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                    .le(HrmsTravelExpensesConfigDO::getEffectTime, new Date())//当前日期小于等于生效时间
                    .in(HrmsTravelExpensesConfigDO::getEffectTime, dateList)
            );
            expensesConfigAllList.addAll(expensesConfigList);
        }
        for (HrmsTravelExpensesConfigDO hrmsTravelExpensesConfigDO : expensesConfigAllList) {
            HrmsTravelExpensesConfigDO hrmsTravelExpensesConfigNewDO = new HrmsTravelExpensesConfigDO();
            Date beginTime = hrmsTravelExpensesConfigDO.getBeginTime();
            Date endTime = hrmsTravelExpensesConfigDO.getEndTime();
            //endTime的年份增加1
            endTime.setYear(endTime.getYear() + 1);
            beginTime.setYear(beginTime.getYear() + 1);
            hrmsTravelExpensesConfigDO.setBeginTime(beginTime);
            hrmsTravelExpensesConfigDO.setEndTime(endTime);

        }
        hrmsTravelExpensesConfigService.updateBatchById(expensesConfigAllList);
        return ReturnT.SUCCESS;
    }
}
