/*
package com.imile.hrms.job.punch;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.dao.achievement.model.AchievementsOrgTargetItemDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassItemConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceUserCardConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.punch.HrmsAttendancePunchClassConfigManage;
import com.imile.hrms.manage.punch.HrmsAttendancePunchClassItemConfigManage;
import com.imile.hrms.manage.punch.HrmsAttendancePunchConfigManage;
import com.imile.hrms.service.approval.dto.AttendanceDayCycleDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

*/
/**
 * @description: 旧的打卡规则生成法定工作时长/休息时长
 * @author: taokang
 * @createDate: 2023-8-5
 * @version: 1.0
 *//*

@Slf4j
@Component
public class PunchRestInitHandler {

    @Autowired
    private HrmsAttendancePunchConfigManage hrmsAttendancePunchConfigManage;
    @Autowired
    private HrmsAttendancePunchClassConfigManage hrmsAttendancePunchClassConfigManage;
    @Autowired
    private HrmsAttendancePunchClassItemConfigManage hrmsAttendancePunchClassItemConfigManage;

    @XxlJob(BusinessConstant.JobHandler.PUNCH_REST_INIT_HANDLER)
    public ReturnT<String> punchRestInitHandler(String content) {
        PunchRestInitHandler.PunchRestInitHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, PunchRestInitHandler.PunchRestInitHandlerParam.class) : new PunchRestInitHandler.PunchRestInitHandlerParam();

        List<HrmsAttendancePunchConfigDO> punchConfigDOList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCompanyIds())) {
            List<Long> companyIds = getCompanyIds(param.getCompanyIds());
            punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByCompanyIdList(companyIds);
        }
        if (StringUtils.isNotBlank(param.getPunchConfigNo())) {
            punchConfigDOList = hrmsAttendancePunchConfigManage.selectAttendancePunchByNoList(Arrays.asList(param.getPunchConfigNo()));
        }
        Date initDate = DateUtil.parse("2023-07-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        punchConfigDOList = punchConfigDOList.stream().filter(item -> item.getExpireTime().compareTo(initDate) > -1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            XxlJobLogger.log("没有符合条件的打卡规则");
            return ReturnT.SUCCESS;
        }
        List<Long> punchConfigIdList = punchConfigDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsAttendancePunchClassConfigDO> punchClassConfigDOList = hrmsAttendancePunchClassConfigManage.selectClassByPunchConfigIdList(punchConfigIdList);
        List<Long> punchClassConfigIdList = punchClassConfigDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(punchClassConfigIdList);
        List<HrmsAttendancePunchClassConfigDO> updateClassList = new ArrayList<>();
        for (HrmsAttendancePunchConfigDO configDO : punchConfigDOList) {
            List<HrmsAttendancePunchClassConfigDO> oldPunchClassConfigDOList = punchClassConfigDOList.stream().filter(item -> item.getPunchConfigId().equals(configDO.getId())).collect(Collectors.toList());
            for (HrmsAttendancePunchClassConfigDO classConfigDO : oldPunchClassConfigDOList) {
                List<HrmsAttendancePunchClassItemConfigDO> oldPunchClassItemConfigDOList = punchClassItemConfigDOList.stream().filter(item -> item.getPunchClassId().equals(classConfigDO.getId())).collect(Collectors.toList());
                BigDecimal attendanceHours = BigDecimal.ZERO;
                for (HrmsAttendancePunchClassItemConfigDO itemConfigDO : oldPunchClassItemConfigDOList) {
                    if (itemConfigDO.getPunchTimeInterval() != null) {
                        attendanceHours = attendanceHours.add(itemConfigDO.getPunchTimeInterval());
                    }
                }
                classConfigDO.setAttendanceHours(attendanceHours);
                classConfigDO.setLegalWorkingHours(attendanceHours);
                BaseDOUtil.fillDOUpdate(classConfigDO);
                updateClassList.add(classConfigDO);
            }
        }
        hrmsAttendancePunchClassConfigManage.batchUpdate(updateClassList);
*/
/*        List<Long> punchConfigIdList = punchConfigDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsAttendancePunchClassConfigDO> punchClassConfigDOList = hrmsAttendancePunchClassConfigManage.selectClassByPunchConfigIdList(punchConfigIdList);
        List<Long> punchClassConfigIdList = punchClassConfigDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigDOList = hrmsAttendancePunchClassItemConfigManage.selectItemConfigByClassId(punchClassConfigIdList);
        Map<String, List<HrmsAttendancePunchConfigDO>> punchConfigMap = punchConfigDOList.stream().collect(Collectors.groupingBy(HrmsAttendancePunchConfigDO::getPunchConfigNo));

        List<HrmsAttendancePunchClassConfigDO> updateClassList = new ArrayList<>();
        List<HrmsAttendancePunchClassItemConfigDO> updateItemList = new ArrayList<>();

        for (Map.Entry<String, List<HrmsAttendancePunchConfigDO>> entry : punchConfigMap.entrySet()) {
            List<HrmsAttendancePunchConfigDO> newPunchConfigList = entry.getValue().stream().filter(item -> item.getIsLatest().equals(BusinessConstant.Y)).collect(Collectors.toList());
            List<HrmsAttendancePunchConfigDO> oldPunchConfigList = entry.getValue().stream().filter(item -> item.getIsLatest().equals(BusinessConstant.N)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newPunchConfigList) || CollectionUtils.isEmpty(oldPunchConfigList)) {
                continue;
            }
            List<HrmsAttendancePunchClassConfigDO> newPunchClassConfigDOList = punchClassConfigDOList.stream().filter(item -> item.getPunchConfigId().equals(newPunchConfigList.get(0).getId())).collect(Collectors.toList());
            for (HrmsAttendancePunchConfigDO oldPunchConfigDO : oldPunchConfigList) {
                List<HrmsAttendancePunchClassConfigDO> oldPunchClassConfigDOList = punchClassConfigDOList.stream().filter(item -> item.getPunchConfigId().equals(oldPunchConfigDO.getId())).collect(Collectors.toList());
                for (HrmsAttendancePunchClassConfigDO oldClassConfigDO : oldPunchClassConfigDOList) {
                    List<HrmsAttendancePunchClassConfigDO> existNewPunchClassConfigDOList = newPunchClassConfigDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getClassName(), oldClassConfigDO.getClassName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(existNewPunchClassConfigDOList)) {
                        XxlJobLogger.log("编码:{},班次名称:{} 旧的规则的班次名称在新规则中没有找到", oldPunchConfigDO.getPunchConfigNo(), oldClassConfigDO.getClassName());
                        continue;
                    }
                    oldClassConfigDO.setLegalWorkingHours(existNewPunchClassConfigDOList.get(0).getLegalWorkingHours());
                    oldClassConfigDO.setAttendanceHours(existNewPunchClassConfigDOList.get(0).getAttendanceHours());
                    BaseDOUtil.fillDOUpdate(oldClassConfigDO);
                    updateClassList.add(oldClassConfigDO);

                    List<HrmsAttendancePunchClassItemConfigDO> oldPunchClassItemConfigDOList = punchClassItemConfigDOList.stream().filter(item -> item.getPunchClassId().equals(oldClassConfigDO.getId())).collect(Collectors.toList());
                    List<HrmsAttendancePunchClassItemConfigDO> newPunchClassItemConfigDOList = punchClassItemConfigDOList.stream().filter(item -> item.getPunchClassId().equals(existNewPunchClassConfigDOList.get(0).getId())).collect(Collectors.toList());
                    for (HrmsAttendancePunchClassItemConfigDO oldClassItemConfigDO : oldPunchClassItemConfigDOList) {
                        List<HrmsAttendancePunchClassItemConfigDO> existNewPunchClassItemConfigDOList = newPunchClassItemConfigDOList.stream()
                                .filter(item -> item.getSortNo().equals(oldClassItemConfigDO.getSortNo())
                                        && item.getEarliestPunchInTime().compareTo(oldClassItemConfigDO.getEarliestPunchInTime()) == 0
                                        && item.getLatestPunchOutTime().compareTo(oldClassItemConfigDO.getLatestPunchOutTime()) == 0).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(existNewPunchClassItemConfigDOList)) {
                            XxlJobLogger.log("编码:{},班次名称:{},时刻序号:{} 旧的规则的班次名称的时刻在新规则中没有找到", oldPunchConfigDO.getPunchConfigNo(), oldClassConfigDO.getClassName(), oldClassItemConfigDO.getSortNo());
                            continue;
                        }
                        oldClassItemConfigDO.setRestStartTime(existNewPunchClassItemConfigDOList.get(0).getRestStartTime());
                        oldClassItemConfigDO.setRestEndTime(existNewPunchClassItemConfigDOList.get(0).getRestEndTime());
                        BaseDOUtil.fillDOUpdate(oldClassItemConfigDO);
                        updateItemList.add(oldClassItemConfigDO);
                    }
                }
            }

            //落库
            hrmsAttendancePunchConfigManage.punchRestInitUpdate(updateClassList, updateItemList);
        }*//*

        return ReturnT.SUCCESS;

    }


    private List<Long> getCompanyIds(String param) {
        if (StringUtils.isNotBlank(param)) {
            Long[] arr = (Long[]) ConvertUtils.convert(param.split(","), Long.class);
            return Arrays.asList(arr);
        }
        return new ArrayList<>();
    }


    @Data
    private static class PunchRestInitHandlerParam {

        private String punchConfigNo;

        private String companyIds;
    }
}
*/
