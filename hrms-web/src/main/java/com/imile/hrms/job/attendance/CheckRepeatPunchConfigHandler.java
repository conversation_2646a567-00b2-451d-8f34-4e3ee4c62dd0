package com.imile.hrms.job.attendance;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CheckRepeatPunchConfigHandler {

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
//    @Autowired
//    private HrmsAttendancePunchConfigRangeDao punchConfigRangeDao;

    @Resource
    private EntDeptNewService entDeptNewService;

    @Resource
    private PunchConfigDaoFacade punchConfigDaoFacade;



    @XxlJob(BusinessConstant.JobHandler.CHECK_REPEAT_PUNCH_CONFIG_HANDLER)
    public ReturnT<String> checkRepeatPunchConfigHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.CHECK_REPEAT_PUNCH_CONFIG_HANDLER, param);
        CheckRepeatPunchConfigHandler.AttendanceHandlerParam handlerParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, CheckRepeatPunchConfigHandler.AttendanceHandlerParam.class) : new CheckRepeatPunchConfigHandler.AttendanceHandlerParam();

        List<String> userCodeList;
        if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
            userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
            List<HrmsUserInfoDO> userInfoDOList = selectUser(null, userCodeList, null);
            handler(userInfoDOList);
            return ReturnT.SUCCESS;
        }

        List<String> countryList = entDeptNewService.getCountryList(handlerParam.getCountryList());


        //查询公司下规定时间之类的所有员工的打卡记录
        for (String country : countryList) {
            List<HrmsUserInfoDO> userInfoDOList = selectUser(country, null, handlerParam.getIsDriver());
            handler(userInfoDOList);
        }
        return ReturnT.SUCCESS;

    }


    private void handler(List<HrmsUserInfoDO> userInfoDOList) {
        List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeDOList = selectPunchConfigRange(userInfoDOList);
        if (CollectionUtils.isEmpty(punchConfigRangeDOList)) {
            return;
        }
        List<String> userCodes = new ArrayList<>();
        Map<Long, List<HrmsAttendancePunchConfigRangeDO>> punchConfigMap = punchConfigRangeDOList.stream().collect(Collectors.groupingBy(o -> o.getBizId()));
        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
            boolean flag = false;

            Long userId = userInfoDO.getId();
            List<HrmsAttendancePunchConfigRangeDO> rangeList = punchConfigMap.get(userId);
            if (CollectionUtils.isEmpty(rangeList)) {
                XxlJobLogger.log("XXL-JOB,  {} userCode:{}，没有打卡规则", BusinessConstant.JobHandler.CHECK_REPEAT_PUNCH_CONFIG_HANDLER, userInfoDO.getUserCode());
                continue;
            }

            for (int i = 0; i < rangeList.size(); i++) {
                if (flag) {
                    break;
                }
                if (i == rangeList.size() -1) {
                    continue;
                }
                HrmsAttendancePunchConfigRangeDO rangeDO = rangeList.get(i);
                Date effectTime = rangeDO.getEffectTime();
                Date expireTime = rangeDO.getExpireTime();

                for (int j = i+1; j < rangeList.size(); j++) {
                    HrmsAttendancePunchConfigRangeDO temp = rangeList.get(j);
                    Date effectTimeTemp = temp.getEffectTime();
                    Date expireTimeTemp = temp.getExpireTime();

                    if (effectTime.compareTo(effectTimeTemp) == 0 && expireTime.compareTo(expireTimeTemp) == 0) {
                        flag = true;
                        userCodes.add(userInfoDO.getUserCode());
                        break;
                    }
                    /*if (effectTime.compareTo(effectTimeTemp) > 0 && effectTime.compareTo(expireTimeTemp) < 0) {
                        userCodes.add(userInfoDO.getUserCode());
                        break;
                    }
                    if (expireTime.compareTo(effectTimeTemp) > 0 && expireTime.compareTo(expireTimeTemp) < 0) {
                        userCodes.add(userInfoDO.getUserCode());
                        break;
                    }
                    if (effectTimeTemp.compareTo(effectTime) > 0 && effectTimeTemp.compareTo(expireTime) < 0) {
                        userCodes.add(userInfoDO.getUserCode());
                        break;
                    }
                    if (expireTimeTemp.compareTo(effectTime) > 0 && expireTimeTemp.compareTo(expireTime) < 0) {
                        userCodes.add(userInfoDO.getUserCode());
                        break;
                    }*/

                    if (effectTime.compareTo(expireTimeTemp) >=0 || expireTime.compareTo(effectTimeTemp) <= 0) {
                        continue;
                    }
                    flag = true;
                    userCodes.add(userInfoDO.getUserCode());
                    break;
                }
            }
        }
        XxlJobLogger.log("XXL-JOB,  {} 有重复打卡规则的员工：userCodes:{}", BusinessConstant.JobHandler.CHECK_REPEAT_PUNCH_CONFIG_HANDLER, JSON.toJSONString(userCodes));
    }



    private List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRange(List<HrmsUserInfoDO> userInfoDOList) {
        List<Long> userIds = userInfoDOList.stream().filter(o -> StringUtils.isNotBlank(o.getUserCode())).map(o -> o.getId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
//        LambdaQueryWrapper<HrmsAttendancePunchConfigRangeDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.in(HrmsAttendancePunchConfigRangeDO::getBizId, userIds);
//        queryWrapper.eq(HrmsAttendancePunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
//        List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.list(queryWrapper);
        List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeDOList = punchConfigDaoFacade.getRangeAdapter().listByBizIds(userIds);

        return punchConfigRangeDOList;
    }

    /**
     * 查询用户信息
     *
     * @param country 国家
     * @param isDriver 是否司机
     * @return List<HrmsUserInfoDO>
     */
    private List<HrmsUserInfoDO> selectUser(String country, List<String> userCodes, Integer isDriver) {
        //查询对应公司的员工
        LambdaQueryWrapper<HrmsUserInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        wrapper.eq(!Objects.isNull(country),HrmsUserInfoDO::getLocationCountry, country);
        if (isDriver != null) {
            wrapper.eq(HrmsUserInfoDO::getIsDriver, isDriver);
        }
        if (CollectionUtils.isNotEmpty(userCodes)) {
            wrapper.in(HrmsUserInfoDO::getUserCode, userCodes);
        }
        wrapper.isNotNull(HrmsUserInfoDO::getUserCode);
        return hrmsUserInfoDao.list(wrapper);
    }


    @Data
    private static class AttendanceHandlerParam {
        /**
         * 公司ID
         */
        private String countryList;
        /**
         * 员工账号
         */
        private String userCodes;

        private Integer isDriver;
    }

}
