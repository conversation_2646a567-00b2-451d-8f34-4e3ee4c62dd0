package com.imile.hrms.job.monitor;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.hermes.vehicle.api.VehicleDriverApi;
import com.imile.hermes.vehicle.api.VehicleInfoApi;
import com.imile.hermes.vehicle.dto.VehicleDriverInfoApiDTO;
import com.imile.hermes.vehicle.dto.VehicleMonitorApiDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.util.DateConvertUtils;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.user.dao.HrmsDriverDeliveryInfoDao;
import com.imile.hrms.dao.user.dao.HrmsNotAttendanceDriverDao;
import com.imile.hrms.dao.user.model.HrmsNotAttendanceDriverDO;
import com.imile.hrms.dao.vehicle.dao.HrmsVacantVehicleDao;
import com.imile.hrms.dao.vehicle.model.HrmsVacantVehicleDO;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.service.common.WeChatMsgService;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 空置车辆信息监控
 */
@Slf4j
@Component
public class VacantVehicleMonitorHandler {

    @Autowired
    protected IHrmsIdWorker iHrmsIdWorker;
    @Reference(version = "1.0.0", check = false)
    private VehicleInfoApi vehicleInfoApi;
    @Autowired
    private HrmsVacantVehicleDao vacantVehicleDao;
    @Autowired
    private HrmsNotAttendanceDriverDao notAttendanceDriverDao;


    @XxlJob(BusinessConstant.JobHandler.VACANT_VEHICLE_MONITOR_HANDLER)
    public ReturnT<String> executeJob(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.VACANT_VEHICLE_MONITOR_HANDLER, param);
        VacantVehicleMonitorHandler.HandlerParam handlerParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, VacantVehicleMonitorHandler.HandlerParam.class) : new VacantVehicleMonitorHandler.HandlerParam();
        Date date = handlerParam.getDate() == null ? DateUtils.dayBegin(new Date()) : DateUtils.dayBegin(handlerParam.getDate());

        List<String> countryList = new ArrayList<>();
        countryList.add("MEX");
        if (StringUtils.isNotBlank(handlerParam.getCountryList())) {
            countryList = Arrays.asList(handlerParam.getCountryList().split(","));
        }

        Date trueDate = HrmsDateUtil.pushDate(date, -1);
        String dayId = DateUtil.format(trueDate, DatePattern.PURE_DATE_PATTERN);

        for (String country : countryList) {

            //获取当前国家当天未出勤司机名单
            List<HrmsNotAttendanceDriverDO> attendanceDriverDOS = notAttendanceDriverDao.listByDate(dayId, country);
            XxlJobLogger.log("XXL-JOB,  {}, dayId:{},country:{},attendanceDriverDOS:{}", BusinessConstant.JobHandler.VACANT_VEHICLE_MONITOR_HANDLER, dayId, country, JSON.toJSONString(attendanceDriverDOS));

            //根据网点分组
            Map<String, List<HrmsNotAttendanceDriverDO>> driverMap = attendanceDriverDOS.stream().collect(Collectors.groupingBy(HrmsNotAttendanceDriverDO::getOcCode));

            //网点车辆信息
            List<VehicleMonitorApiDTO> monitorApiDTOS = vehicleInfoApi.getStationVehicleInfo(country).getResult();
            XxlJobLogger.log("XXL-JOB,  {},country:{},monitorApiDTOS:{}", BusinessConstant.JobHandler.VACANT_VEHICLE_MONITOR_HANDLER, country, JSON.toJSONString(monitorApiDTOS));

            if (CollectionUtils.isEmpty(monitorApiDTOS)) {
                return ReturnT.SUCCESS;
            }

            List<HrmsVacantVehicleDO> list = new ArrayList<>();
            for (VehicleMonitorApiDTO item : monitorApiDTOS) {
                HrmsVacantVehicleDO vacantVehicleDO = new HrmsVacantVehicleDO();
                vacantVehicleDO.setId(iHrmsIdWorker.nextId());
                vacantVehicleDO.setCountry(country);
                vacantVehicleDO.setDate(trueDate);
                vacantVehicleDO.setDayId(Long.valueOf(dayId));
                vacantVehicleDO.setIsOwn(item.getIsOwn());
                vacantVehicleDO.setOcCode(item.getOcCode());
                vacantVehicleDO.setOcName(item.getOcName());
                vacantVehicleDO.setVehicleNum(item.getVehicleNum());
                vacantVehicleDO.setMaintenanceVehicleNum(item.getMaintenanceVehicleNum());
                vacantVehicleDO.setAverageMaintenanceDays(item.getAverageMaintenanceDays());
//            vacantVehicleDO.setWorkVehicleNum(item.getWorkVehicleNum());
//            vacantVehicleDO.setFreeVehicleNum(item.getFreeVehicleNum());

                List<String> driverCodes = new ArrayList<>();
                List<HrmsNotAttendanceDriverDO> driverDOS = driverMap.get(item.getOcCode());
                if (CollectionUtils.isNotEmpty(driverDOS)) {
                    //该网点有未出勤司机
                    driverCodes = driverDOS.stream().map(HrmsNotAttendanceDriverDO::getDriverCode).distinct().collect(Collectors.toList());
                }

                //绑定车辆的司机信息
                List<VehicleDriverInfoApiDTO> bindVehicleDriverList = item.getBindVehicleDriverList();
                if (CollectionUtils.isEmpty(bindVehicleDriverList)) {
                    vacantVehicleDO.setNoBindVehicleNum(item.getFreeVehicleNum());
                    vacantVehicleDO.setWorkVehicleNum(0);
                    vacantVehicleDO.setNoAttendanceVehicleNum(0);
                    vacantVehicleDO.setFreeVehicleNum(item.getFreeVehicleNum());
                    BigDecimal freeVehicleRate = BigDecimal.valueOf(vacantVehicleDO.getFreeVehicleNum()).divide(BigDecimal.valueOf(vacantVehicleDO.getVehicleNum()), 4, BigDecimal.ROUND_HALF_UP);
                    vacantVehicleDO.setFreeVehicleRate(freeVehicleRate.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    list.add(vacantVehicleDO);
                    continue;
                }

                //根据车辆分组，车辆可绑定多个司机
                Map<Long, List<VehicleDriverInfoApiDTO>> byVehicleIdMap = bindVehicleDriverList.stream().collect(Collectors.groupingBy(VehicleDriverInfoApiDTO::getVehicleId));

                int workVehicleNum = 0;
                int notAttendanceVehicleNum = 0;
                for (Map.Entry<Long, List<VehicleDriverInfoApiDTO>> entry : byVehicleIdMap.entrySet()) {
                    List<VehicleDriverInfoApiDTO> value = entry.getValue();
                    List<String> vehicleDriverCodes = value.stream().map(VehicleDriverInfoApiDTO::getDriverCode).distinct().collect(Collectors.toList());
                    vehicleDriverCodes.removeIf(driverCodes::contains);

                    //绑定司机中只要有一个出勤，则认为该车出勤
                    if (CollectionUtils.isNotEmpty(vehicleDriverCodes)) {
                        workVehicleNum++;
                        continue;
                    }
                    //绑定司机都未出勤，则认为该车未出勤
                    notAttendanceVehicleNum++;
                }

                vacantVehicleDO.setWorkVehicleNum(workVehicleNum);
                vacantVehicleDO.setNoAttendanceVehicleNum(notAttendanceVehicleNum);
                vacantVehicleDO.setNoBindVehicleNum(item.getFreeVehicleNoBindNum());
                vacantVehicleDO.setFreeVehicleNum(vacantVehicleDO.getNoBindVehicleNum() + vacantVehicleDO.getNoAttendanceVehicleNum());

                BigDecimal freeVehicleRate = BigDecimal.valueOf(vacantVehicleDO.getFreeVehicleNum()).divide(BigDecimal.valueOf(vacantVehicleDO.getVehicleNum()), 4, BigDecimal.ROUND_HALF_UP);
                vacantVehicleDO.setFreeVehicleRate(freeVehicleRate.multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));

                list.add(vacantVehicleDO);
            }

            if (CollectionUtils.isNotEmpty(list)) {
                vacantVehicleDao.saveBatch(list);
            }
        }

        return ReturnT.SUCCESS;
    }

    @Data
    private static class HandlerParam {

        private Date date;

        /**
         * 国家
         */
        private String countryList;
    }
}
