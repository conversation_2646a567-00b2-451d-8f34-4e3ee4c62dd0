package com.imile.hrms.job.achievement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.achievement.model.AchievementEmployeeConclusionDO;
import com.imile.hrms.dao.achievement.model.AchievementsEmployeeAppraisalDO;
import com.imile.hrms.service.achievement.AchievementEmployeeConclusionService;
import com.imile.hrms.service.achievement.AchievementsEmployeeAppraisalService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AchievementResultEncryptionHandler {

    @Autowired
    private AchievementEmployeeConclusionService achievementEmployeeConclusionService;

    @Autowired
    private AchievementsEmployeeAppraisalService achievementsEmployeeAppraisalService;

    @XxlJob("AchievementResultEncryption")
    public ReturnT<String> AchievementStatusChangeHandler(String param) {
        List<Long> ids = null;
        if (StringUtils.isNotEmpty(param)) {
            List<AchievementsEmployeeAppraisalDO> appraisalList = achievementsEmployeeAppraisalService.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementsEmployeeAppraisalDO>()
                    .eq(AchievementsEmployeeAppraisalDO::getIsDelete, IsDeleteEnum.NO.getCode())
                    .eq(AchievementsEmployeeAppraisalDO::getEventId, param)
            );
            ids = appraisalList.stream().map(AchievementsEmployeeAppraisalDO::getId).collect(Collectors.toList());
        }

        List<AchievementEmployeeConclusionDO> list = achievementEmployeeConclusionService.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementEmployeeConclusionDO>()
                .eq(AchievementEmployeeConclusionDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(CollectionUtils.isNotEmpty(ids), AchievementEmployeeConclusionDO::getEmployeeAppraisalId, ids)
        );

        // 加密
        for (AchievementEmployeeConclusionDO item : list) {
            String leaderRate = item.getLeaderRate();
            Long employeeAppraisalId = item.getEmployeeAppraisalId();
            String gradeNoSecret = achievementEmployeeConclusionService.encryption(employeeAppraisalId, leaderRate);
            item.setLeaderRate(gradeNoSecret);
        }
        achievementEmployeeConclusionService.updateBatchById(list);

        return ReturnT.SUCCESS;
    }
}
