package com.imile.hrms.job.punch;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.service.punch.UserPunchRiskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/5 
 * @Description 用户打卡风险分析定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserPunchRiskAnalysisHandler {
    
    private final UserPunchRiskService userPunchRiskService;
    
    @XxlJob(BusinessConstant.JobHandler.USER_PUNCH_RISK_ANALYSIS_HANDLER)
    public ReturnT<String> userPunchRiskAnalysisHandler(String content) {
        log.info("用户打卡风险分析定时任务开始执行，参数：{}", content);
        
        try {
            // 解析参数
            UserPunchRiskAnalysisParam param = parseParam(content);
            if (param == null) {
                log.warn("参数解析失败，使用默认参数");
                param = createDefaultParam();
            }
            
            // 验证参数
            if (!validateParam(param)) {
                log.error("参数验证失败：{}", JSON.toJSONString(param));
                return ReturnT.FAIL;
            }
            
            log.info("开始分析打卡风险，参数：{}", JSON.toJSONString(param));
            
            // 执行风险分析
            userPunchRiskService.analyzePunchRisk(
                param.getStartDayId(), 
                param.getEndDayId(), 
                param.getUserCodes()
            );
            
            log.info("用户打卡风险分析定时任务执行完成");
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("用户打卡风险分析定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }
    
    /**
     * 解析参数
     */
    private UserPunchRiskAnalysisParam parseParam(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        
        try {
            return JSON.parseObject(content, UserPunchRiskAnalysisParam.class);
        } catch (Exception e) {
            log.error("参数解析异常：{}", content, e);
            return null;
        }
    }
    
    /**
     * 创建默认参数（分析前一天的数据）
     */
    private UserPunchRiskAnalysisParam createDefaultParam() {
        UserPunchRiskAnalysisParam param = new UserPunchRiskAnalysisParam();
        
        // 默认分析前一天的数据
        Date yesterday = DateUtil.offsetDay(new Date(), -1);
        Long dayId = Long.valueOf(DateUtil.format(yesterday, DatePattern.PURE_DATE_PATTERN));
        
        param.setStartDayId(dayId);
        param.setEndDayId(dayId);
        
        return param;
    }
    
    /**
     * 验证参数
     */
    private boolean validateParam(UserPunchRiskAnalysisParam param) {
        if (param == null) {
            log.error("参数为空");
            return false;
        }
        
        if (param.getStartDayId() == null) {
            log.error("开始日期不能为空");
            return false;
        }
        
        if (param.getEndDayId() == null) {
            log.error("结束日期不能为空");
            return false;
        }
        
        if (param.getStartDayId() > param.getEndDayId()) {
            log.error("开始日期不能大于结束日期");
            return false;
        }
        
        // 验证日期格式（YYYYMMDD）
        String startDayStr = param.getStartDayId().toString();
        String endDayStr = param.getEndDayId().toString();
        
        if (startDayStr.length() != 8 || endDayStr.length() != 8) {
            log.error("日期格式错误，应为YYYYMMDD格式");
            return false;
        }
        
        try {
            DateUtil.parse(startDayStr, DatePattern.PURE_DATE_PATTERN);
            DateUtil.parse(endDayStr, DatePattern.PURE_DATE_PATTERN);
        } catch (Exception e) {
            log.error("日期格式验证失败", e);
            return false;
        }
        
        return true;
    }
    
    /**
     * 定时任务参数
     */
    @Data
    public static class UserPunchRiskAnalysisParam {
        
        /**
         * 开始日期 (YYYYMMDD)
         */
        private Long startDayId;
        
        /**
         * 结束日期 (YYYYMMDD)
         */
        private Long endDayId;
        
        /**
         * 用户编码列表，为空则分析所有用户
         */
        private List<String> userCodes;
    }
}
