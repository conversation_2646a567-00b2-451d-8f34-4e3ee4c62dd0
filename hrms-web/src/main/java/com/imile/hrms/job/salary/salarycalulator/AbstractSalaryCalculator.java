package com.imile.hrms.job.salary.salarycalulator;

import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.TransferInOrOutTypeEnum;
import com.imile.hrms.common.enums.attendance.AttendanceUpdateTypeEnum;
import com.imile.hrms.common.enums.salary.CycleTypeEnum;
import com.imile.hrms.common.enums.salary.OvertimeAllowanceTypeEnum;
import com.imile.hrms.common.enums.salary.SalaryItemTypeEnum;
import com.imile.hrms.common.util.BigDecimalUtil;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.dao.attendance.dto.AttendanceDTO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.attendance.query.UserAttendanceQuery;
import com.imile.hrms.dao.salary.dto.*;
import com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeDetailDO;
import com.imile.hrms.dao.user.dao.HrmsUserTransferRecordDao;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserTransferRecordDO;
import com.imile.hrms.manage.attendance.HrmsAttendanceEmployeeDetailManage;
import com.imile.hrms.manage.salary.HrmsSalaryEmployeeConfigManage;
import com.imile.hrms.manage.user.UserTransferManage;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/7
 */
public abstract class AbstractSalaryCalculator {

    @Autowired
    private HrmsSalaryEmployeeConfigManage hrmsSalaryEmployeeConfigManage;

    @Autowired
    private HrmsUserTransferRecordDao hrmsUserTransferRecordDao;
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private HrmsAttendanceEmployeeDetailManage hrmsAttendanceEmployeeDetailManage;

    @Resource
    private UserTransferManage userTransferManage;

    /**
     * 填充薪资
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param detail
     * @param attendanceDTO
     * @param userEntryRecordDO
     */
    public abstract void salaryCalculate(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, SalaryEmployeeConfigDetailDTO detail, AttendanceDTO attendanceDTO, HrmsUserEntryRecordDO userEntryRecordDO, Integer needAttendanceDayCnt, BigDecimal absentDay);

    /**
     * 获取计薪方案类型
     *
     * @return
     */
    public abstract String getSalaryMethodEnum();

    /**
     * 计算出勤薪资
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param detail
     * @param userEntryRecordDO
     * @param needAttendanceDayCnt
     */
    protected void calculatorAttendanceSalary(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, SalaryEmployeeConfigDetailDTO detail, HrmsUserEntryRecordDO userEntryRecordDO, Integer needAttendanceDayCnt, BigDecimal absentDay) {
        //计算日薪
        SalaryConfigDetailDTO salaryConfigDetailInfo = detail.getSalaryConfigDetailInfo();
        //月应出勤天数
        BigDecimal monthAttendanceDays = salaryConfigDetailInfo.getMonthAttendanceDays();
        //薪资总额
        BigDecimal salary = detail.getSalary();
        hrmsSalaryEmployeeDetailDO.setSalary(salary);
        //薪资组成项目
        List<SalaryItemDTO> salaryItems = detail.getSalaryConfigDetailInfo().getSalaryItems();
        //遍历项目，获取基础薪资比例
        for (SalaryItemDTO salaryItemDTO : salaryItems) {
            if (!StringUtils.equalsIgnoreCase(SalaryItemTypeEnum.BASE.name(), salaryItemDTO.getSalaryItemType())) {
                 continue;
            }
            //基础薪资设置
            if (salaryItemDTO.getSalaryComponentType().equals(BusinessConstant.ZERO)) {
                hrmsSalaryEmployeeDetailDO.setBaseSalary(BigDecimalUtil.multiply(detail.getSalary(), BigDecimalUtil.divide(salaryItemDTO.getSalaryRatio(), BusinessConstant.HUNDRED, BusinessConstant.DEFAULT_BIT)));
                break;
            }
            hrmsSalaryEmployeeDetailDO.setBaseSalary(salaryItemDTO.getSalaryValue());
            break;
        }
        //日薪
        BigDecimal daySalary = BigDecimalUtil.divide(salary, BigDecimal.valueOf(30), BusinessConstant.DEFAULT_BIT);
        //判断当前计薪方案是周还是月
        //若为周
        CycleTypeEnum cycleTypeEnum = CycleTypeEnum.getInstance(hrmsSalaryEmployeeDetailDO.getCycleType());
        switch (cycleTypeEnum) {
            case MONTH:
                //月出勤薪资
                calculateMonthAttendanceSalary(hrmsSalaryEmployeeDetailDO, userEntryRecordDO, daySalary, needAttendanceDayCnt, absentDay);
                break;
            case WEEK:
                //周出勤工资
                hrmsSalaryEmployeeDetailDO.setAttendanceSalary(BigDecimalUtil.multiply(daySalary, hrmsSalaryEmployeeDetailDO.getSalaryDayCnt()));
                break;
            default:
                break;
        }

    }

    /**
     * 计算月出勤薪资
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param userEntryRecordDO
     * @param daySalary
     * @param needAttendanceDayCnt
     */
    private void calculateMonthAttendanceSalary(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, HrmsUserEntryRecordDO userEntryRecordDO, BigDecimal daySalary, Integer needAttendanceDayCnt, BigDecimal absentDay) {

        boolean calculateSalarySubtraction = hrmsProperties.getAttendance().isCalculateSalarySubtraction();
        BigDecimal subtract = BigDecimal.ZERO;
        Integer dayDiff = HrmsDateUtil.getDateDiff(hrmsSalaryEmployeeDetailDO.getStartTime(), hrmsSalaryEmployeeDetailDO.getEndTime()) + 1;

        if (calculateSalarySubtraction) {
            /*UserAttendanceQuery query = new UserAttendanceQuery();
            query.setUserIds(Arrays.asList(hrmsSalaryEmployeeDetailDO.getUserId()));
            query.setCompanyId(hrmsSalaryEmployeeDetailDO.getCompanyId());
            query.setStartTime(hrmsSalaryEmployeeDetailDO.getStartTime());
            query.setEndTime(hrmsSalaryEmployeeDetailDO.getEndTime());
            List<HrmsAttendanceEmployeeDetailDO> attendanceList = hrmsAttendanceEmployeeDetailManage.userAttendanceList(query);

            List<HrmsAttendanceEmployeeDetailDO> presentList = attendanceList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getAttendanceType(), AttendanceUpdateTypeEnum.PRESENT.getCode())).collect(Collectors.toList());
            List<HrmsAttendanceEmployeeDetailDO> absentList = attendanceList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getAttendanceType(), AttendanceUpdateTypeEnum.ABSENT.getCode())).collect(Collectors.toList());
            List<HrmsAttendanceEmployeeDetailDO> leaveList = attendanceList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getAttendanceType(), AttendanceUpdateTypeEnum.LEAVE.getCode())).collect(Collectors.toList());

            BigDecimal absentDay = BigDecimal.valueOf(dayDiff - attendanceList.size());
            if (CollectionUtils.isNotEmpty(absentList)) {
                absentDay = absentDay.add(BigDecimal.valueOf(absentList.size()));
            }

            if (CollectionUtils.isNotEmpty(presentList)) {
                int presentDay = presentList.size();
                BigDecimal presentHours = presentList.stream().map(o -> o.getAttendanceHours()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal presentDays = presentHours.divide(BigDecimal.valueOf(8), 2, BigDecimal.ROUND_HALF_UP);
                absentDay = absentDay.add(BigDecimal.valueOf(presentDay).subtract(presentDays));
            }

            if (CollectionUtils.isNotEmpty(leaveList)) {
                BigDecimal leaveHours = leaveList.stream().map(o -> o.getLeaveHours().multiply(BigDecimal.ONE.subtract(o.getLeavePercentSalary()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal leaveDays = leaveHours.divide(BigDecimal.valueOf(8), 2, BigDecimal.ROUND_HALF_UP);
                absentDay = absentDay.add(leaveDays);
            }
            subtract = absentDay;*/
            subtract = absentDay.equals(BigDecimal.valueOf(31)) ? BigDecimal.valueOf(30) : absentDay;

        } else {
            // 判断是否全勤
            subtract = hrmsSalaryEmployeeDetailDO.getAttendanceDayCnt().subtract(new BigDecimal(needAttendanceDayCnt));
            if (BigDecimal.ZERO.compareTo(subtract) == 0) {
                //若全勤，出勤薪资=薪资总额
                hrmsSalaryEmployeeDetailDO.setAttendanceSalary(hrmsSalaryEmployeeDetailDO.getSalary());
                return;
            }
            //如果不是全勤，就查计薪天数
            BigDecimal salaryDayCnt = hrmsSalaryEmployeeDetailDO.getSalaryDayCnt();
            subtract = salaryDayCnt.subtract(new BigDecimal(needAttendanceDayCnt));
        }


        // 判断是否早于该计薪周期入职
        if (Boolean.TRUE.equals(checkEntry(userEntryRecordDO, hrmsSalaryEmployeeDetailDO))) {
            //出勤薪资=薪资总额-工作日缺勤天数*日薪
            //计薪天数
            BigDecimal salaryDay = hrmsSalaryEmployeeDetailDO.getSalaryDayCnt();
            //应出勤天数
            BigDecimal needAttendanceDay = hrmsSalaryEmployeeDetailDO.getNeedAttendanceDayCnt();
            if (dayDiff == null && salaryDay.compareTo(needAttendanceDay) >= 0) {
                hrmsSalaryEmployeeDetailDO.setAttendanceSalary(hrmsSalaryEmployeeDetailDO.getSalary());
                return;
            }
            if (dayDiff != null && salaryDay.compareTo(BigDecimal.valueOf(dayDiff)) >= 0) {
                hrmsSalaryEmployeeDetailDO.setAttendanceSalary(hrmsSalaryEmployeeDetailDO.getSalary());
                return;
            }
            hrmsSalaryEmployeeDetailDO.setAttendanceSalary(hrmsSalaryEmployeeDetailDO.getSalary().subtract(BigDecimalUtil.multiply(daySalary, subtract).setScale(2, BigDecimal.ROUND_HALF_UP)));
            return;
        }
        // 出勤薪资=日薪*计薪天数
        hrmsSalaryEmployeeDetailDO.setAttendanceSalary(BigDecimalUtil.multiply(daySalary, hrmsSalaryEmployeeDetailDO.getSalaryDayCnt()));

    }

    /**
     * 判断计薪开始时间是否在计薪周期
     *
     * @param userEntryRecordDO
     * @param hrmsSalaryEmployeeDetailDO
     * @return
     */
    private Boolean checkEntry(HrmsUserEntryRecordDO userEntryRecordDO, HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO) {
        if (!userEntryRecordDO.getEntryDate().before(hrmsSalaryEmployeeDetailDO.getStartTime())) {
            return Boolean.FALSE;
        }
        //查询调动记录
        HrmsUserTransferRecordDO transfer = userTransferManage.getTransfer(hrmsSalaryEmployeeDetailDO.getUserId(), hrmsSalaryEmployeeDetailDO.getCountry(), null);
        if (transfer == null) {
            return Boolean.TRUE;
        }
        if (transfer.getTransferTime().after(userEntryRecordDO.getEntryDate())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * 计算加班薪资
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param detail
     * @param attendanceDTO
     */
    protected void calculatorOverTimeSalary(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, SalaryEmployeeConfigDetailDTO detail, AttendanceDTO attendanceDTO) {
        //若出勤记录为空，则直接return
        if (attendanceDTO == null) {
            XxlJobLogger.log("员工{}没有出勤记录,不计算加班", detail.getUserId());
            return;
        }
        //获取加班薪酬配置
        OvertimeDTO overtimeConfig = detail.getSalaryConfigDetailInfo().getOvertimeConfig();
        //计算日薪
        SalaryConfigDetailDTO salaryConfigDetailInfo = detail.getSalaryConfigDetailInfo();
        //月应出勤天数
        BigDecimal monthAttendanceDays = salaryConfigDetailInfo.getMonthAttendanceDays();
        //基础薪资
        BigDecimal baseSalary = hrmsSalaryEmployeeDetailDO.getBaseSalary();
        //基础日薪
        BigDecimal daySalary = BigDecimalUtil.divide(baseSalary, BigDecimal.valueOf(30), BusinessConstant.DEFAULT_BIT);
        //基础时薪
        BigDecimal dayAttendanceHours = salaryConfigDetailInfo.getDayAttendanceHours();
        BigDecimal hourSalary = BigDecimalUtil.divide(daySalary, dayAttendanceHours, BusinessConstant.DEFAULT_BIT);
        //工作日加班配置
        OvertimeItemDTO workday = overtimeConfig.getPresent();
        BigDecimal workdayOvertimePay = getOvertimeSalary(workday, daySalary, hourSalary, attendanceDTO.getOvertimeDaysOnPresentDay(), attendanceDTO.getOvertimeHoursOnPresentDay());
        hrmsSalaryEmployeeDetailDO.setWorkdayOvertimePay(workdayOvertimePay);
        //休息日加班配置
        OvertimeItemDTO offDay = overtimeConfig.getWeekend();
        BigDecimal offDayOvertimePay = getOvertimeSalary(offDay, daySalary, hourSalary, attendanceDTO.getOvertimeDaysOnWeekend(), attendanceDTO.getOvertimeHoursOnWeekend());
        hrmsSalaryEmployeeDetailDO.setOffDayOvertimePay(offDayOvertimePay);
        //节假日加班配置
        OvertimeItemDTO holiday = overtimeConfig.getHoliday();
        BigDecimal holidayOvertimePay = getOvertimeSalary(holiday, daySalary, hourSalary, attendanceDTO.getOvertimeDaysOnHoliday(), attendanceDTO.getOvertimeHoursOnHoliday());
        hrmsSalaryEmployeeDetailDO.setHolidayOvertimePay(holidayOvertimePay);
    }

    /**
     * @param overtimeConfig
     * @param daySalary
     * @param hourSalary
     * @param overtimeDay
     * @return
     */
    private BigDecimal getOvertimeSalary(OvertimeItemDTO overtimeConfig, BigDecimal daySalary, BigDecimal hourSalary, BigDecimal overtimeDay, BigDecimal overtimeHour) {
        //获取加班类型
        OvertimeAllowanceTypeEnum overtimePay = OvertimeAllowanceTypeEnum.getInstance(overtimeConfig.getAllowanceType());
        BigDecimal overtimeSalary = BigDecimal.ZERO;
        switch (overtimePay) {
            case FIXED_VALUE:
                overtimeSalary = BigDecimalUtil.multiply(overtimeConfig.getValue(), overtimeDay);
                break;
            case MULTIPLE_BASE_DAY:
                overtimeSalary = BigDecimalUtil.multiply(BigDecimalUtil.multiply(overtimeConfig.getValue(), daySalary), overtimeDay);
                break;
            case MULTIPLE_BASE_HOUR:
                overtimeSalary = BigDecimalUtil.multiply(BigDecimalUtil.multiply(overtimeConfig.getValue(), hourSalary), overtimeHour);
                break;
            default:
                break;
        }
        return overtimeSalary;
    }


    /**
     * 计算计件薪资
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param detail
     * @param attendanceDTO
     */
    protected void calculatorPieceSalary(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, SalaryEmployeeConfigDetailDTO detail, AttendanceDTO attendanceDTO) {
        //获取计件薪酬配置
        BigDecimal pieceFee = Optional.ofNullable(detail.getPieceFee()).orElse(BigDecimal.ZERO);
        //获取总共签收数
        Integer dldCnt = Optional.ofNullable(attendanceDTO.getDeliveredCount()).orElse(0);
        //签收金额
        BigDecimal dldSalary = BigDecimalUtil.multiply(pieceFee, dldCnt);

        hrmsSalaryEmployeeDetailDO.setDldSalary(dldSalary);
    }

    /**
     * 计算公积金
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param detail
     */
    protected void calculatorAccumulation(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, SalaryEmployeeConfigDetailDTO detail, HrmsUserEntryRecordDO userEntryRecordDO, BigDecimal absentDay) {
        //获取公积金基础配置
        SalaryAccumulationConfigDetailDTO accumulationConfigDetailInfo = detail.getAccumulationConfigDetailInfo();
        if (accumulationConfigDetailInfo == null) {
            return;
        }
        //公积金个人配置
        List<ConfigItemDTO> configItems = detail.getAccumulationConfigDetailInfo().getConfigItems();
        //公积金配置
        for (ConfigItemDTO item : configItems) {
            SalaryEmployeeConfigItemDTO configItemDTO = (SalaryEmployeeConfigItemDTO) item;
            BigDecimal baseValue = configItemDTO.getActualBaseValue();
            hrmsSalaryEmployeeConfigManage.calculatorValue(hrmsSalaryEmployeeDetailDO, item, baseValue, false, null, userEntryRecordDO, absentDay);
        }

    }


    /**
     * 计算社保
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param detail
     */
    protected void calculatorSocial(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, SalaryEmployeeConfigDetailDTO detail, HrmsUserEntryRecordDO userEntryRecordDO, BigDecimal absentDay) {
        //获取社保基础配置
        AbstractSalaryConfigDetailDTO socialConfigDetailInfo = detail.getSocialConfigDetailInfo();
        if (socialConfigDetailInfo == null) {
            return;
        }
        //判断是墨西哥
        if (socialConfigDetailInfo instanceof MexSalarySocialConfigDetailDTO) {
            MexSalarySocialConfigDetailDTO mexSalarySocialConfigDetailDTO = (MexSalarySocialConfigDetailDTO) socialConfigDetailInfo;
            List<ConfigItemDTO> configItems = mexSalarySocialConfigDetailDTO.getConfigItems();
            //处理墨西哥社保
            for (ConfigItemDTO item : configItems) {
                SalaryEmployeeConfigItemDTO configItemDTO = (SalaryEmployeeConfigItemDTO) item;
                BigDecimal baseValue = configItemDTO.getActualBaseValue();
                hrmsSalaryEmployeeConfigManage.calculatorValue(hrmsSalaryEmployeeDetailDO, item, baseValue, true, mexSalarySocialConfigDetailDTO.getCalculationType(), userEntryRecordDO, absentDay);
            }

        } else if (socialConfigDetailInfo instanceof SalarySocialConfigDetailDTO) {
            SalarySocialConfigDetailDTO salarySocialConfigDetailDTO = (SalarySocialConfigDetailDTO) socialConfigDetailInfo;
            //处理普通社保
            List<ConfigItemDTO> configItems = salarySocialConfigDetailDTO.getConfigItems();
            for (ConfigItemDTO item : configItems) {
                SalaryEmployeeConfigItemDTO configItemDTO = (SalaryEmployeeConfigItemDTO) item;
                BigDecimal baseValue = configItemDTO.getActualBaseValue();
                hrmsSalaryEmployeeConfigManage.calculatorValue(hrmsSalaryEmployeeDetailDO, item, baseValue, false, salarySocialConfigDetailDTO.getCalculationType(), userEntryRecordDO, absentDay);
            }
        }
    }

    /**
     * 计算公司应支出和个人总薪资
     *
     * @param hrmsSalaryEmployeeDetailDO
     */
    protected void calculatorSalary(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO) {

        BigDecimal orgTotalPay = new SalaryHelpBuilder()
                //出勤薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getAttendanceSalary())
                //工作日加班补贴
                .addSalary(hrmsSalaryEmployeeDetailDO.getWorkdayOvertimePay())
                //休息日加班补贴
                .addSalary(hrmsSalaryEmployeeDetailDO.getOffDayOvertimePay())
                //节假日加班补贴
                .addSalary(hrmsSalaryEmployeeDetailDO.getHolidayOvertimePay())
                //计件薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getDldSalary())
                //社保公积金
                .addSalary(hrmsSalaryEmployeeDetailDO.getOrgSocialAndAccumulationPay())
                //激励薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getIncentiveSalary())
                //其他增项
                .addSalary(hrmsSalaryEmployeeDetailDO.getOtherIncreaseSalary())
                //.addSalary(hrmsSalaryEmployeeDetailDO.getTaxation())
                //签证费
                .addSalary(hrmsSalaryEmployeeDetailDO.getVisaPay())
                //医保
                .addSalary(hrmsSalaryEmployeeDetailDO.getMedicarePay())
                //机票费
                .addSalary(hrmsSalaryEmployeeDetailDO.getAirticketPay())
                //服务费
                .addSalary(hrmsSalaryEmployeeDetailDO.getServicePay())
                //税收费用
                .addSalary(hrmsSalaryEmployeeDetailDO.getTaxationPay())
                //其他
                .addSalary(hrmsSalaryEmployeeDetailDO.getOtherPay())
                .getSalary();
        hrmsSalaryEmployeeDetailDO.setOrgTotalPay(orgTotalPay);

        BigDecimal employeeGrossPay = new SalaryHelpBuilder()
                //出勤薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getAttendanceSalary())
                //工作日加班补贴
                .addSalary(hrmsSalaryEmployeeDetailDO.getWorkdayOvertimePay())
                //休息日加班补贴
                .addSalary(hrmsSalaryEmployeeDetailDO.getOffDayOvertimePay())
                //节假日加班补贴
                .addSalary(hrmsSalaryEmployeeDetailDO.getHolidayOvertimePay())
                //计件薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getDldSalary())
                //社保公积金
                .subSalary(hrmsSalaryEmployeeDetailDO.getPersonSocialAndAccumulationPay())
                //激励薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getIncentiveSalary())
                //其他增项
                .addSalary(hrmsSalaryEmployeeDetailDO.getOtherIncreaseSalary())
                //其他减项
                .subSalary(hrmsSalaryEmployeeDetailDO.getOtherDecreaseSalary())
                .getSalary();
        hrmsSalaryEmployeeDetailDO.setEmployeeGrossPay(employeeGrossPay);

        BigDecimal accruedPay = new SalaryHelpBuilder()
                //出勤薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getAttendanceSalary())
                //计件薪资
                .addSalary(hrmsSalaryEmployeeDetailDO.getDldSalary())
                .addSalary(hrmsSalaryEmployeeDetailDO.getWorkdayOvertimePay())
                .addSalary(hrmsSalaryEmployeeDetailDO.getOffDayOvertimePay())
                .addSalary(hrmsSalaryEmployeeDetailDO.getHolidayOvertimePay())
                .subSalary(hrmsSalaryEmployeeDetailDO.getPersonSocialAndAccumulationPay())
                .addSalary(hrmsSalaryEmployeeDetailDO.getIncentiveSalary())
                .addSalary(hrmsSalaryEmployeeDetailDO.getOtherIncreaseSalary())
                .subSalary(hrmsSalaryEmployeeDetailDO.getOtherDecreaseSalary())
                .subSalary(hrmsSalaryEmployeeDetailDO.getTaxation())
                //其他
                .subSalary(hrmsSalaryEmployeeDetailDO.getTaxationPay())
                .getSalary();


        hrmsSalaryEmployeeDetailDO.setAccruedPay(accruedPay);




    }


}
