package com.imile.hrms.job.attendance;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.service.attendance.AttendanceGenerateService;
import com.imile.hrms.service.attendance.dto.DayAttendanceHandlerDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/9/22 17:12
 * @version: 1.0
 */
@Slf4j
@Component
public class AttendanceGenerateTestHandler {

    @Autowired
    private AttendanceGenerateService attendanceGenerateService;

    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_GENERATE_TEST_HANDLER)
    public ReturnT<String> attendanceGenerateTestHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.ATTENDANCE_GENERATE_TEST_HANDLER, param);
        AttendanceGenerateTestHandler.AttendanceGenerateTestHandlerParam handlerParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, AttendanceGenerateTestHandler.AttendanceGenerateTestHandlerParam.class) : new AttendanceGenerateTestHandler.AttendanceGenerateTestHandlerParam();
        if (handlerParam.getStartDayId() == null || handlerParam.getEndDayId() == null) {
            handlerParam.setStartDayId(20230801L);
            handlerParam.setEndDayId(20230801L);

        }
        Long startDayId = handlerParam.getStartDayId();
        Long endDayId = handlerParam.getEndDayId();

        Long tempDayId = startDayId;
        while (tempDayId <= endDayId) {
            DayAttendanceHandlerDTO dayAttendanceHandlerDTO = new DayAttendanceHandlerDTO();
            dayAttendanceHandlerDTO.setCountryList(handlerParam.getCountryList());
            dayAttendanceHandlerDTO.setUserCodes(handlerParam.getUserCodes());
            dayAttendanceHandlerDTO.setAttendanceDayId(tempDayId);
            attendanceGenerateService.dayAttendanceHandler(dayAttendanceHandlerDTO);
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
        }

        return ReturnT.SUCCESS;
    }


    @Data
    private static class AttendanceGenerateTestHandlerParam {
        /**
         * 入参:国家
         */
        private String countryList;

        /**
         * 入参:用户编码   考勤审批通过后会通过这个字段来
         */
        private String userCodes;

        /**
         * 周期的开始时间的long
         */
        private Long startDayId;

        /**
         * 周期的结束时间的long
         */
        private Long endDayId;


    }
}
