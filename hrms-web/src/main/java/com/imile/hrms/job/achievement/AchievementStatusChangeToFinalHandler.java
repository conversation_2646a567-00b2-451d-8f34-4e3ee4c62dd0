package com.imile.hrms.job.achievement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.achievement.AchievementsEmployeeAppraisalNodeTypeEnum;
import com.imile.hrms.common.enums.achievement.AchievementsEmployeeAppraisalStatusEnum;
import com.imile.hrms.dao.achievement.model.AchievementEmployeeConclusionDO;
import com.imile.hrms.dao.achievement.model.AchievementsEmployeeAppraisalDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.service.achievement.AchievementEmployeeConclusionService;
import com.imile.hrms.service.achievement.AchievementsEmployeeAppraisalService;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绩效执行人维护
 *
 * <AUTHOR>
 * @date 2023/04/19
 */
@Slf4j
@Component
public class AchievementStatusChangeToFinalHandler {


    @Autowired
    private AchievementsEmployeeAppraisalService achievementsEmployeeAppraisalService;

    @Autowired
    private AchievementEmployeeConclusionService achievementEmployeeConclusionService;

    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;

    @XxlJob(BusinessConstant.JobHandler.ACHIEVEMENT_STATUS_CHANGE_TO_FINAL)
    public ReturnT<String> AchievementStatusChangeToFinalHandler(String param) {
        List<AchievementsEmployeeAppraisalDO> list = achievementsEmployeeAppraisalService.list(new LambdaQueryWrapper<AchievementsEmployeeAppraisalDO>()
                .eq(AchievementsEmployeeAppraisalDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(AchievementsEmployeeAppraisalDO::getEventId, param)
                .ge(AchievementsEmployeeAppraisalDO::getAppraisalStatus, AchievementsEmployeeAppraisalStatusEnum.WAIT_ASSESS_RESULT.getStatus())
        );

        List<Long> ids = list.stream().map(AchievementsEmployeeAppraisalDO::getId).collect(Collectors.toList());

        List<AchievementEmployeeConclusionDO> achievementEmployeeConclusionList = achievementEmployeeConclusionService.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementEmployeeConclusionDO>()
                .in(AchievementEmployeeConclusionDO::getEmployeeAppraisalId, ids)
                .eq(AchievementEmployeeConclusionDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );

        Map<Long,AchievementEmployeeConclusionDO> map = achievementEmployeeConclusionList.stream().collect(Collectors.toMap(AchievementEmployeeConclusionDO::getEmployeeAppraisalId, AchievementEmployeeConclusionDO -> AchievementEmployeeConclusionDO));

        List<AchievementEmployeeConclusionDO> insertList = new ArrayList<>();

        List<AchievementEmployeeConclusionDO> updateList = new ArrayList<>();

        //获取key为员工考核id value为leaderCode的map
        Map<Long, Long> deptMap = new HashMap<>();

        List<Long> appraisalExecutorIdList = new ArrayList<>();

        List<Long> deptIds = list.stream().map(AchievementsEmployeeAppraisalDO::getAppraiserDeptId).distinct().collect(Collectors.toList());

        for (Long deptId : deptIds){
            HrmsEntDeptDO hrmsEntDeptDO = getDeptByDeptIdAndLever(deptId, 0);
            deptMap.put(deptId, hrmsEntDeptDO.getLeaderCode());
            appraisalExecutorIdList.add(hrmsEntDeptDO.getLeaderCode());
        }

        List<HrmsUserInfoDO> ExecutorList = hrmsUserInfoDao.getBaseMapper().selectList(new LambdaQueryWrapper<HrmsUserInfoDO>()
                .in(HrmsUserInfoDO::getId, appraisalExecutorIdList)
                .eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );

        Map<Long, HrmsUserInfoDO> executorMap = ExecutorList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity()));

        for (AchievementsEmployeeAppraisalDO achievementsEmployeeAppraisalDO : list) {
            if (map.containsKey(achievementsEmployeeAppraisalDO.getId())) {
                AchievementEmployeeConclusionDO achievementEmployeeConclusionDO = map.get(achievementsEmployeeAppraisalDO.getId());
                if (StringUtils.isEmpty(achievementEmployeeConclusionDO.getLeaderRate())){
                    achievementsEmployeeAppraisalDO.setAppraisalRemark("Personal performance evaluation delay leads to default B rating.");
                    achievementEmployeeConclusionDO.setLeaderRate("B");
                    achievementEmployeeConclusionDO.setLeaderRemark("Not filled in");
                    if (!Objects.equals(0, achievementsEmployeeAppraisalDO.getExerciserUserId())){
                        achievementEmployeeConclusionDO.setExerciserConclusion("01");
                    }
                    achievementsEmployeeAppraisalDO.setLastUpdUserName("BrushData");
                    achievementsEmployeeAppraisalDO.setAppraisalExecutorName("system");
                }else{
                    if (!Objects.equals(0, achievementsEmployeeAppraisalDO.getExerciserUserId())){
                        if (StringUtils.isEmpty(achievementEmployeeConclusionDO.getExerciserConclusion())){
                            achievementEmployeeConclusionDO.setExerciserConclusion("01");
                        }
                    }
                }
                updateList.add(achievementEmployeeConclusionDO);
            } else {
                AchievementEmployeeConclusionDO achievementEmployeeConclusionDO = new AchievementEmployeeConclusionDO();
                achievementEmployeeConclusionDO.setId(iHrmsIdWorker.nextId());
                achievementEmployeeConclusionDO.setEmployeeAppraisalId(achievementsEmployeeAppraisalDO.getId());
                achievementEmployeeConclusionDO.setLeaderRate("B");
                achievementEmployeeConclusionDO.setLeaderRemark("Not filled in");
                achievementsEmployeeAppraisalDO.setAppraisalRemark("Personal performance evaluation delay leads to default B rating.");
                if (!Objects.equals(0, achievementsEmployeeAppraisalDO.getExerciserUserId())){
                    achievementEmployeeConclusionDO.setExerciserConclusion("01");
                }
                insertList.add(achievementEmployeeConclusionDO);
                achievementsEmployeeAppraisalDO.setLastUpdUserName("BrushData");
                achievementsEmployeeAppraisalDO.setAppraisalExecutorName("system");
            }

            if (StringUtils.isEmpty(achievementsEmployeeAppraisalDO.getAppraisalRemark()) && 1!=achievementsEmployeeAppraisalDO.getAppraisalDeptLevel()){
                achievementsEmployeeAppraisalDO.setAppraisalRemark("Performance review delay defaults to Level 1 supervisor approval pending.");
                achievementsEmployeeAppraisalDO.setLastUpdUserName("BrushData");
                achievementsEmployeeAppraisalDO.setAppraisalExecutorName("system");
            }

            Long  appraisalExecutorId = deptMap.get(achievementsEmployeeAppraisalDO.getAppraiserDeptId());
            //获取当前人要开启N级考核的部门
            achievementsEmployeeAppraisalDO.setAppraisalExecutorId(appraisalExecutorId);
            HrmsUserInfoDO appraisalExecutor = executorMap.get(appraisalExecutorId);
            achievementsEmployeeAppraisalDO.setAppraisalExecutorName(getName(appraisalExecutor.getUserName(), appraisalExecutor.getUserNameEn()));

            achievementsEmployeeAppraisalDO.setAppraisalStatus(AchievementsEmployeeAppraisalStatusEnum.AUDITED_RESULT.getStatus());
            achievementsEmployeeAppraisalDO.setCurrentAppraisalNode(AchievementsEmployeeAppraisalNodeTypeEnum.RESULT_FIRST.getType());
            achievementsEmployeeAppraisalDO.setAppraisalDeptLevel(1);

        }

        achievementsEmployeeAppraisalService.updateBatchById(list);
        // todo 绩效加密
        achievementEmployeeConclusionService.updateBatchById(updateList);
        achievementEmployeeConclusionService.saveBatch(insertList);

        return ReturnT.SUCCESS;
    }

    /**
     * 根据部门id字段和lever等级字段递归查询对应等级的单个部门
     */
    public HrmsEntDeptDO getDeptByDeptIdAndLever(Long deptId, Integer lever) {
        HrmsEntDeptDO dept = hrmsEntDeptDao.getBaseMapper().selectById(deptId);
        if (dept.getLevel().equals(lever)) {
            return dept;
        } else {
            dept = getDeptByDeptIdAndLever(dept.getParentId(), lever);
        }
        return dept;
    }

    /**
     * 获取用户姓名
     *
     * @param name
     * @param nameEn
     * @return
     */
    private String getName(String name, String nameEn) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        Boolean checkName = hrmsUserInfoService.checkName(name);
        String realName = StringUtils.isBlank(nameEn) ? name.trim() : StringUtils.capitalize(nameEn.trim()) + name.trim();
        String userName = checkName ? realName : name.trim();
        return userName;
    }


}
