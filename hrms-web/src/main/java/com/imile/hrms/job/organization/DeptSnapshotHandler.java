package com.imile.hrms.job.organization;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.common.util.IdWorkUtils;
import com.imile.hrms.dao.bussiness.area.HrmsBizeAreaBaseConfigDO;
import com.imile.hrms.dao.organization.dao.HrmsBizModelDao;
import com.imile.hrms.dao.organization.dao.HrmsDeptSnapshotDao;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.dao.HrmsOrgBusinessConfigDao;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.dao.organization.model.HrmsDeptSnapshotDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsOrgBusinessConfigDO;
import com.imile.hrms.dao.organization.model.HrmsOrgBusinessConfigPlusDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.dict.service.DictService;
import com.imile.hrms.integration.dict.vo.DictVO;
import com.imile.hrms.service.business.area.HrmsBizeAreaBaseConfigService;
import com.imile.hrms.service.organization.param.DeptSnapshotParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.hrms.common.entity.BaseDOUtil.fillDOInsert;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@Component
public class DeptSnapshotHandler {

    @Resource
    private HrmsDeptSnapshotDao hrmsDeptSnapshotDao;
    @Resource
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Resource
    private HrmsBizModelDao hrmsBizModelDao;
    @Resource
    private HrmsBizeAreaBaseConfigService hrmsBizAreaDao;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsOrgBusinessConfigDao hrmsOrgBusinessConfigDao;
    @Resource
    private DictService dictService;
    @Resource
    private IdWorkUtils idWorkUtils;

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String JOB_NAME = BusinessConstant.JobHandler.DEPT_SNAPSHOT_HANDLER;

    @XxlJob(BusinessConstant.JobHandler.DEPT_SNAPSHOT_HANDLER)
    public ReturnT<String> deptSnapshotHandler(String param) {
        XxlJobLogger.log("{} 开始执行,param:{}", JOB_NAME, param);
        DeptSnapshotParam snapshotParam = StringUtils.isBlank(param) ? null : JSON.parseObject(param, DeptSnapshotParam.class);
        long startTime = System.currentTimeMillis();
        // 设置登录信息
        setLoginInfo();
        // 设置版本号
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        String snapshotVersion = snapshotParam != null && StringUtils.isNotBlank(snapshotParam.getSnapshotVersion()) ?
                snapshotParam.getSnapshotVersion() : LocalDate.now().format(formatter);
        // 快照版本号唯一，不重复生成同一快照版本
        int count = getSnapShotCount(snapshotVersion);
        if (count > 0) {
            XxlJobLogger.log("{} 该快照版本已存在, snapshotVersion: {}", JOB_NAME, snapshotVersion);
            return returnSuccess(startTime);
        }
        List<Long> deptIds = snapshotParam != null ? snapshotParam.getDeptIds() : Lists.newArrayList();
        List<HrmsEntDeptDO> deptList = getDeptList(deptIds);
        if (CollectionUtils.isEmpty(deptList)) {
            XxlJobLogger.log("{} 没有部门可生成快照", JOB_NAME);
            return returnSuccess(startTime);
        }
        Set<Long> bizAreaSet = Sets.newHashSet();
        Set<Long> bizModelIdSet = Sets.newHashSet();
        Set<Long> leaderSet = Sets.newHashSet();
        List<Long> allDeptIdList = Lists.newArrayList();
        deptList.forEach(e -> {
            bizAreaSet.addAll(Splitter.on(HrmsStringUtil.COMMA).splitToList(e.getBizArea()).stream().filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList()));
            bizModelIdSet.addAll(Splitter.on(HrmsStringUtil.COMMA).splitToList(e.getBizModelId()).stream().filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList()));
            leaderSet.add(e.getLeaderCode());
            allDeptIdList.add(e.getId());
        });
        // 获取业务领域
        Map<String, HrmsBizeAreaBaseConfigDO> bizAreaMap = getBizAreaMap(bizAreaSet);
        // 获取业务节点
        Map<String, HrmsBizModelDO> bizModelMap = getBizModelMap(bizModelIdSet);
        // 获取人员
        List<HrmsOrgBusinessConfigDO> bizOwnerConfigList = hrmsOrgBusinessConfigDao.getByDeptIdList(allDeptIdList);
        List<Long> bizOwnerUserIdList = bizOwnerConfigList.stream().
                filter(s -> StringUtils.isNotBlank(s.getLeaderId())).
                map(s -> splitIds2List(s.getLeaderId())).
                flatMap(Collection::stream).distinct().collect(Collectors.toList());
        leaderSet.addAll(bizOwnerUserIdList);
        Map<String, HrmsUserInfoDO> leaderMap = getLeaderMap(leaderSet);

        // 获取业务负责人
        Map<String, DictVO> bizOwnerTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.LEADER_PROPERTY);
        Map<Long, List<HrmsOrgBusinessConfigDO>> deptBizOwnerConfigListMap = CollectionUtils.isEmpty(bizOwnerConfigList) ? Maps.newHashMap() :
                bizOwnerConfigList.stream().collect(Collectors.groupingBy(HrmsOrgBusinessConfigDO::getDeptId));

        // 生成快照
        List<HrmsDeptSnapshotDO> records = deptList.stream().map(e -> {
            List<HrmsBizeAreaBaseConfigDO> bizAreaList = Splitter.on(HrmsStringUtil.COMMA).splitToList(e.getBizArea()).stream().map(x -> bizAreaMap.getOrDefault(x, null)).filter(Objects::nonNull).collect(Collectors.toList());
            List<HrmsBizModelDO> bizModelList = Splitter.on(HrmsStringUtil.COMMA).splitToList(e.getBizModelId()).stream().map(x -> bizModelMap.getOrDefault(x, null)).filter(Objects::nonNull).collect(Collectors.toList());
            HrmsDeptSnapshotDO snapshotDO = generateSnapshot(e, snapshotVersion, bizAreaList, bizModelList, leaderMap);
            // 处理业务负责人
            List<HrmsOrgBusinessConfigDO> deptBizOwnerConfigList = deptBizOwnerConfigListMap.getOrDefault(e.getId(), Collections.emptyList());
            if (CollectionUtils.isNotEmpty(deptBizOwnerConfigList)) {
                List<HrmsOrgBusinessConfigPlusDO> list = getHrmsOrgBusinessConfigPlusList(deptBizOwnerConfigList, bizOwnerTypeMap, leaderMap);
                snapshotDO.setDeptBizLeaderConfig(JSON.toJSONString(list));
            }

            return snapshotDO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(records)) {
            hrmsDeptSnapshotDao.saveBatch(records);
        }
        return returnSuccess(startTime);
    }

    @NotNull
    private List<HrmsOrgBusinessConfigPlusDO> getHrmsOrgBusinessConfigPlusList(List<HrmsOrgBusinessConfigDO> deptBizOwnerConfigList, Map<String, DictVO> bizOwnerTypeMap, Map<String, HrmsUserInfoDO> leaderMap) {
        return deptBizOwnerConfigList.stream()
                .filter(s -> bizOwnerTypeMap.containsKey(s.getLeaderProperty().toString()))
                .map(config -> {
                    HrmsOrgBusinessConfigPlusDO configPlus = new HrmsOrgBusinessConfigPlusDO();
                    BeanUtils.copyProperties(config, configPlus);
                    configPlus.setLeaderPropertyStr(bizOwnerTypeMap.get(config.getLeaderProperty().toString()).getDataValue());
                    List<Long> leaderIds = splitIds2List(config.getLeaderId());
                    String leaderUserNameCn = leaderIds.stream()
                            .map(userId -> {
                                if (!leaderMap.containsKey(userId.toString())) {
                                    return "";
                                }
                                HrmsUserInfoDO ownerUser = leaderMap.get(userId.toString());
                                return ownerUser.getUserName() + "(" + ownerUser.getUserCode() + ")";
                            })
                            .collect(Collectors.joining(HrmsStringUtil.COMMA));
                    configPlus.setLeaderUserNameCn(leaderUserNameCn);
                    String leaderUserNameEn = leaderIds.stream()
                            .map(userId -> {
                                if (!leaderMap.containsKey(userId.toString())) {
                                    return "";
                                }
                                HrmsUserInfoDO ownerUser = leaderMap.get(userId.toString());
                                return ownerUser.getUserNameEn() + "(" + ownerUser.getUserCode() + ")";
                            })
                            .collect(Collectors.joining(HrmsStringUtil.COMMA));
                    configPlus.setLeaderUserNameEn(leaderUserNameEn);
                    return configPlus;
                })
                .collect(Collectors.toList());
    }

    private List<Long> splitIds2List(String ids) {
        if (StringUtils.isBlank(ids)) {
            return Collections.emptyList();
        }
        return Splitter.on(HrmsStringUtil.COMMA).splitToList(ids).stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    private int getSnapShotCount(String snapshotVersion) {
        QueryWrapper<HrmsDeptSnapshotDO> snapshotQuery = new QueryWrapper<>();
        snapshotQuery.lambda().eq(HrmsDeptSnapshotDO::getIsDelete, BusinessConstant.N).eq(HrmsDeptSnapshotDO::getSnapshotVersion, snapshotVersion);
        return hrmsDeptSnapshotDao.count(snapshotQuery);
    }

    @NotNull
    private HrmsDeptSnapshotDO generateSnapshot(HrmsEntDeptDO dept, String snapshotVersion,
                                                List<HrmsBizeAreaBaseConfigDO> bizAreaList, List<HrmsBizModelDO> bizModelList, Map<String, HrmsUserInfoDO> leaderMap) {
        HrmsDeptSnapshotDO snapshotDO = new HrmsDeptSnapshotDO();
        // 设置snapshot所有字段
        BeanUtils.copyProperties(dept, snapshotDO);
        snapshotDO.setId(idWorkUtils.nextId());
        snapshotDO.setSnapshotVersion(snapshotVersion);
        snapshotDO.setDeptId(dept.getId());
        if (CollectionUtils.isNotEmpty(bizAreaList)) {
            HrmsBizeAreaBaseConfigDO bizArea = bizAreaList.get(0);
            snapshotDO.setBizAreaId(bizArea.getId());
            snapshotDO.setBizAreaNameEn(bizArea.getBusinessAreaNameEn());
            snapshotDO.setBizAreaNameCn(bizArea.getBusinessAreaNameCn());
        }
        snapshotDO.setEnabledDate(dept.getRecentActiveTime());
        snapshotDO.setDisabledDate(dept.getRecentDisabledTime());
        snapshotDO.setDeptLevel(dept.getLevel());
        if (CollectionUtils.isNotEmpty(bizModelList)) {
            StringBuilder bizModelIds = new StringBuilder();
            StringBuilder bizModelNameCns = new StringBuilder();
            StringBuilder bizModelNameEns = new StringBuilder();
            Iterator<HrmsBizModelDO> iterator = bizModelList.iterator();
            if (iterator.hasNext()) {
                HrmsBizModelDO hrmsBizModelDO = iterator.next();
                joinBizModel(bizModelIds, bizModelNameCns, bizModelNameEns, hrmsBizModelDO, null);
                while (iterator.hasNext()) {
                    hrmsBizModelDO = iterator.next();
                    joinBizModel(bizModelIds, bizModelNameCns, bizModelNameEns, hrmsBizModelDO, HrmsStringUtil.COMMA);
                }
                snapshotDO.setBizModelIds(bizModelIds.toString());
                snapshotDO.setBizModelNamesCn(bizModelNameCns.toString());
                snapshotDO.setBizModelNamesEn(bizModelNameEns.toString());
            }
        }
        if (dept.getLeaderCode() != null && leaderMap.containsKey(dept.getLeaderCode().toString())) {
            HrmsUserInfoDO leader = leaderMap.get(dept.getLeaderCode().toString());
            snapshotDO.setLeaderCode(leader.getId().toString());
            snapshotDO.setLeaderName(leader.getUserName() + "(" + leader.getUserCode() + ")");
        }
        snapshotDO.setOcCode(dept.getOcCode());
        fillDOInsert(snapshotDO);
        return snapshotDO;
    }

    private void joinBizModel(StringBuilder bizModelIds, StringBuilder bizModelNameCns, StringBuilder bizModelNameEns, HrmsBizModelDO hrmsBizModelDO, String split) {
        if (StringUtils.isNotBlank(split)) {
            bizModelIds.append(split);
            bizModelNameCns.append(split);
            bizModelNameEns.append(split);
        }
        bizModelIds.append(hrmsBizModelDO.getId().toString());
        bizModelNameCns.append(hrmsBizModelDO.getBizModelNameCn());
        if (StringUtils.isNotBlank(hrmsBizModelDO.getDescribeCn())) {
            bizModelNameCns.append("(").append(hrmsBizModelDO.getDescribeCn().trim()).append(")");
        }
        bizModelNameEns.append(hrmsBizModelDO.getBizModelNameEn());
        if (StringUtils.isNotBlank(hrmsBizModelDO.getDescribeEn())) {
            bizModelNameEns.append("(").append(hrmsBizModelDO.getDescribeEn().trim()).append(")");
        }
    }

    private List<HrmsEntDeptDO> getDeptList(List<Long> ids) {
        QueryWrapper<HrmsEntDeptDO> deptQuery = new QueryWrapper<>();
        deptQuery.lambda().eq(HrmsEntDeptDO::getIsDelete, BusinessConstant.N).
                in(CollectionUtils.isNotEmpty(ids), HrmsEntDeptDO::getId, ids);
        return hrmsEntDeptDao.list(deptQuery);
    }

    private Map<String, HrmsUserInfoDO> getLeaderMap(Set<Long> leaderSet) {
        if (CollectionUtils.isEmpty(leaderSet)) {
            return Maps.newHashMap();
        }
        QueryWrapper<HrmsUserInfoDO> leaderQuery = new QueryWrapper<>();
        leaderQuery.lambda().eq(HrmsUserInfoDO::getIsDelete, BusinessConstant.N).
                in(CollectionUtils.isNotEmpty(leaderSet), HrmsUserInfoDO::getId, leaderSet);
        List<HrmsUserInfoDO> leaderList = hrmsUserInfoDao.list(leaderQuery);
        return CollectionUtils.isNotEmpty(leaderList) ?
                leaderList.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity(), (k1, k2) -> k1)) : Maps.newHashMap();
    }

    private Map<String, HrmsBizModelDO> getBizModelMap(Set<Long> bizModelIdSet) {
        if (CollectionUtils.isEmpty(bizModelIdSet)) {
            return Maps.newHashMap();
        }
        QueryWrapper<HrmsBizModelDO> bizModelQuery = new QueryWrapper<>();
        bizModelQuery.lambda().eq(HrmsBizModelDO::getIsDelete, BusinessConstant.N).
                in(CollectionUtils.isNotEmpty(bizModelIdSet), HrmsBizModelDO::getId, bizModelIdSet);
        List<HrmsBizModelDO> bizModelList = hrmsBizModelDao.list(bizModelQuery);
        return CollectionUtils.isNotEmpty(bizModelList) ?
                bizModelList.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity(), (k1, k2) -> k1)) : Maps.newHashMap();
    }

    private Map<String, HrmsBizeAreaBaseConfigDO> getBizAreaMap(Set<Long> bizAreaSet) {
        if (CollectionUtils.isEmpty(bizAreaSet)) {
            return Maps.newHashMap();
        }
        QueryWrapper<HrmsBizeAreaBaseConfigDO> bizAreaQuery = new QueryWrapper<>();
        bizAreaQuery.lambda().eq(HrmsBizeAreaBaseConfigDO::getIsDelete, BusinessConstant.N).
                in(CollectionUtils.isNotEmpty(bizAreaSet), HrmsBizeAreaBaseConfigDO::getId, bizAreaSet);
        List<HrmsBizeAreaBaseConfigDO> bizAreaList = hrmsBizAreaDao.list(bizAreaQuery);
        return CollectionUtils.isNotEmpty(bizAreaList) ?
                bizAreaList.stream().collect(Collectors.toMap(e -> e.getId().toString(), Function.identity(), (k1, k2) -> k1)) : Maps.newHashMap();
    }

    private static void setLoginInfo() {
        UserContext userContext = new UserContext();
        userContext.setUserCode("XXL-JOB");
        userContext.setUserName("XXL-JOB");
        userContext.setOrgId(10L);
        RequestInfoHolder.setLoginInfo(userContext);
    }

    private ReturnT<String> returnSuccess(long startTime) {
        long endTime = System.currentTimeMillis();
        XxlJobLogger.log("{} 执行完毕,总耗时:{}ms", JOB_NAME, endTime - startTime);
        return ReturnT.SUCCESS;
    }
}
