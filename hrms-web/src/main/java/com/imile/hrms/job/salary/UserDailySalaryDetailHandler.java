package com.imile.hrms.job.salary;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.util.DateConvertUtils;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 员工日薪定时计算
 * 目前只计算带薪节假日薪资
 * <AUTHOR>
 * @date 2022/05/26
 */
@Component
public class UserDailySalaryDetailHandler {
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @XxlJob(BusinessConstant.JobHandler.USER_DAILY_SALARY_DETAIL_HANDLER)
    public ReturnT<String> userDailySalaryDetailHandler(String param) throws ParseException {
        UserDailySalaryDetailHandler.UserDailySalaryDetailParam detailParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, UserDailySalaryDetailHandler.UserDailySalaryDetailParam.class) : new UserDailySalaryDetailHandler.UserDailySalaryDetailParam();
        if (detailParam.getCountry() == null) {
            return ReturnT.FAIL;
        }
        Date salaryDate = null;
        if (detailParam.getDate() == null) {
            // todo 可能需要往前推一天
            salaryDate = DateConvertUtils.getMinTime(new Date());
        } else {
            salaryDate = DateConvertUtils.getMinTimeByDate(detailParam.getDate());
        }
        //查询所有在职并且是正式的员工、司机信息
        LambdaQueryWrapper<HrmsUserInfoDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(HrmsUserInfoDO::getCompanyId, detailParam.getCompanyId());
        if (StringUtils.isNotBlank(detailParam.getUserCodes())) {
            List<String> userCodes = Arrays.asList(detailParam.getUserCodes().split(","));
            queryWrapper.in(HrmsUserInfoDO::getUserCode, userCodes);
        }
        queryWrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserInfoDO::getEmployeeType, Arrays.asList(EmploymentTypeEnum.EMPLOYEE.getCode(),EmploymentTypeEnum.SUB_EMPLOYEE.getCode()));
        List<HrmsUserInfoDO> userInfoList = hrmsUserInfoDao.list(queryWrapper);

        //查到用户所属的出勤日历规则

        return ReturnT.SUCCESS;
    }


    @Data
    private static class UserDailySalaryDetailParam {

        /**
         * 国家
         */
        private String country;
        /**
         * 用户编码
         */
        private String userCodes;

        /**
         * 计薪日期
         */
        private String date;
    }
}
