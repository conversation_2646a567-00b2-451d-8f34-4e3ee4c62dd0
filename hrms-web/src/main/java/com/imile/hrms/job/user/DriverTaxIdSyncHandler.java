package com.imile.hrms.job.user;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.freelancer.FreelancerBasicInfoEnum;
import com.imile.hrms.common.enums.user.DriverTaxIdSourceEnum;
import com.imile.hrms.dao.freelancer.dao.FreelancerDriverInfoDao;
import com.imile.hrms.dao.freelancer.model.FreelancerDriverInfoDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.user.dao.HrmsUserExtendAttrDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.DriverZipCodeDTO;
import com.imile.hrms.dao.user.model.HrmsUserExtendAttrDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.NeedUpdateTaxIdUserDO;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.waukeen.api.gt.GtApi;
import com.imile.waukeen.api.gt.dto.GtDriverDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-1-5
 * @version: 1.0
 */
@Slf4j
@Component
public class DriverTaxIdSyncHandler {

    @Reference(version = "1.0.0", check = false, timeout = 20000)
    private GtApi gtApi;

    @Autowired
    private FreelancerDriverInfoDao freelancerDriverInfoDao;

    @Autowired
    private HrmsUserExtendAttrDao hrmsUserExtendAttrDao;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private IHrmsIdWorker iHrmsIdWorker;
    @Resource
    private UserCertificateService userCertificateService;


    @XxlJob(BusinessConstant.JobHandler.DRIVER_TAX_ID_SYNC_HANDLER)
    public ReturnT<String> driverTaxIdSyncHandler(String param) {
        XxlJobLogger.log("入参值为:{}", BusinessConstant.JobHandler.DRIVER_TAX_ID_SYNC_HANDLER, param);

        // 查询常驻国BRA、CPF不为空、taxIdSource != FINANCE的用户
        List<NeedUpdateTaxIdUserDO> userIdList = hrmsUserInfoDao.selectNeedUpdateTaxIdUser();
        if (CollectionUtils.isEmpty(userIdList)) {
            XxlJobLogger.log("无人员需要更新");
            return ReturnT.SUCCESS;
        }
        Map<Long, NeedUpdateTaxIdUserDO> userAttrMap = userIdList.stream().collect(Collectors.toMap(NeedUpdateTaxIdUserDO::getUserId, o -> o, (k1, k2) -> k2));
        List<Long> userIds = new ArrayList<>(userAttrMap.keySet());
        if (CollectionUtils.isEmpty(userIds)) {
            XxlJobLogger.log("无人员需要更新");
            return ReturnT.SUCCESS;
        }
        // 获取需要更新的实体类
        List<String> attrKeyItemList = Lists.newArrayList(FreelancerBasicInfoEnum.taxId.getCode(), FreelancerBasicInfoEnum.taxIdSource.getCode());
        List<HrmsUserExtendAttrDO> needUpdateRecords = hrmsUserExtendAttrDao.listByAttrKeyAndUserIds(attrKeyItemList, userIds);
        if (CollectionUtils.isEmpty(needUpdateRecords)) {
            XxlJobLogger.log("无需要更新扩展字段");
            return ReturnT.SUCCESS;
        }
        XxlJobLogger.log("待更新人员数量:{}", userIds.size());
        Map<Long, List<HrmsUserExtendAttrDO>> needUpdateRecordMap = needUpdateRecords.stream().collect(Collectors.groupingBy(HrmsUserExtendAttrDO::getUserId));

        List<HrmsUserInfoDO> users = hrmsUserInfoDao.getByUserIds(userIds);
        List<FreelancerDriverInfoDO> freelancers = freelancerDriverInfoDao.getByUserIds(userIds);
        List<HrmsUserExtendAttrDO> resList = new ArrayList<>();
        Integer successCount = 0;
        for (HrmsUserInfoDO userInfoDO : users) {
            if (!CountryCodeEnum.BRA.getCode().equals(userInfoDO.getLocationCountry())) {
                XxlJobLogger.log("更新失败，不是BRA员工:{}", userInfoDO.getUserCode());
                continue;
            }
            NeedUpdateTaxIdUserDO tax = userAttrMap.get(userInfoDO.getId());
            String taxId = getTaxIdFromGt(userInfoDO.getUserName(), tax);
            if (StringUtils.isBlank(taxId)) {
                XxlJobLogger.log("更新失败，查询taxid失败:{}, taxid: {}", userInfoDO.getUserCode(), taxId);
                continue;
            }
            List<HrmsUserExtendAttrDO> needUpdateItem = needUpdateRecordMap.get(userInfoDO.getId());
            if (CollectionUtils.isEmpty(needUpdateItem)) {
                XxlJobLogger.log("更新失败，不存在需要更新的扩展字段:{}", userInfoDO.getUserCode());
                continue;
            }
            buildNeedUpdate(needUpdateItem, taxId, resList);
            successCount++;
        }
        for (FreelancerDriverInfoDO userInfoDO : freelancers) {
            if (!CountryCodeEnum.BRA.getCode().equals(userInfoDO.getCountry())) {
                XxlJobLogger.log("更新失败，不是BRA员工:{}", userInfoDO.getUserCode());
                continue;
            }
            NeedUpdateTaxIdUserDO tax = userAttrMap.get(userInfoDO.getId());
            String taxId = getTaxIdFromGt(userInfoDO.getUserName(), tax);
            if (StringUtils.isBlank(taxId)) {
                XxlJobLogger.log("更新失败，查询taxid失败:{}", userInfoDO.getUserCode());
                continue;
            }
            List<HrmsUserExtendAttrDO> needUpdateItem = needUpdateRecordMap.get(userInfoDO.getId());
            if (CollectionUtils.isEmpty(needUpdateItem)) {
                XxlJobLogger.log("更新失败，不存在需要更新的扩展字段:{}", userInfoDO.getUserCode());
                continue;
            }
            buildNeedUpdate(needUpdateItem, taxId, resList);
            successCount++;
        }

        hrmsUserExtendAttrDao.saveOrUpdateBatch(resList);
        XxlJobLogger.log("总数量:{}", userIds.size());
        XxlJobLogger.log("成功更新人员数量:{}", successCount);
        XxlJobLogger.log("失败数量:{}", userIds.size() - successCount);
        return ReturnT.SUCCESS;
    }

    private void buildNeedUpdate(List<HrmsUserExtendAttrDO> needUpdateItem, String taxId, List<HrmsUserExtendAttrDO> resList) {
        Map<String, HrmsUserExtendAttrDO> map = needUpdateItem.stream().collect(Collectors.toMap(HrmsUserExtendAttrDO::getAttrKey, o -> o, (k1, k2) -> k2));
        Long userId = needUpdateItem.get(0).getUserId();
        if (map.containsKey(FreelancerBasicInfoEnum.taxId.getCode())) {
            HrmsUserExtendAttrDO taxIdAttr = map.get(FreelancerBasicInfoEnum.taxId.getCode());
            taxIdAttr.setAttrValue(taxId);
            BaseDOUtil.fillDOUpdate(taxIdAttr);
            resList.add(taxIdAttr);
        } else {
            HrmsUserExtendAttrDO model = new HrmsUserExtendAttrDO();
            model.setAttrKey(FreelancerBasicInfoEnum.taxId.getCode());
            model.setAttrValue(taxId);
            model.setId(iHrmsIdWorker.nextId());
            model.setUserId(userId);
            BaseDOUtil.fillDOInsert(model);
            resList.add(model);
        }
        if (map.containsKey(FreelancerBasicInfoEnum.taxIdSource.getCode())) {
            HrmsUserExtendAttrDO taxIdSourceAttr = map.get(FreelancerBasicInfoEnum.taxIdSource.getCode());
            taxIdSourceAttr.setAttrValue(DriverTaxIdSourceEnum.FINANCE.name());
            BaseDOUtil.fillDOUpdate(taxIdSourceAttr);
            resList.add(taxIdSourceAttr);
        } else {
            HrmsUserExtendAttrDO model = new HrmsUserExtendAttrDO();
            model.setAttrKey(FreelancerBasicInfoEnum.taxIdSource.getCode());
            model.setAttrValue(DriverTaxIdSourceEnum.FINANCE.name());
            model.setId(iHrmsIdWorker.nextId());
            model.setUserId(userId);
            BaseDOUtil.fillDOInsert(model);
            resList.add(model);
        }
    }

    private String getTaxIdFromGt(String userName, NeedUpdateTaxIdUserDO tax) {
        if (StringUtils.isBlank(userName) || tax == null || StringUtils.isBlank(tax.getCpfNumber()) || StringUtils.isBlank(tax.getZipCodeList())) {
            return null;
        }
        GtDriverDTO gtDriverDTO = new GtDriverDTO();
        gtDriverDTO.setDriverName(userName);
        List<DriverZipCodeDTO> driverZipCodeDTOList = JSONObject.parseArray(tax.getZipCodeList(), DriverZipCodeDTO.class);
        gtDriverDTO.setZipCode(driverZipCodeDTOList.get(0).getZipCode());
        gtDriverDTO.setTaxCode(tax.getCpfNumber());
        return gtApi.registerDriver(gtDriverDTO).getResult();
    }

}
