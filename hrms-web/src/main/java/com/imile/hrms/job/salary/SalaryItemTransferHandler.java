package com.imile.hrms.job.salary;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.salary.dao.HrmsCompanyOtherSalaryConfigDao;
import com.imile.hrms.dao.salary.dao.HrmsEmployeeSalaryInfoItemDao;
import com.imile.hrms.dao.salary.model.HrmsCompanyOtherSalaryConfigDO;
import com.imile.hrms.dao.salary.model.HrmsEmployeeSalaryInfoDO;
import com.imile.hrms.dao.salary.model.HrmsEmployeeSalaryInfoItemDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigDO;
import com.imile.hrms.dao.salary.query.HrmsEmployeeSalaryInfoQuery;
import com.imile.hrms.dao.salary.query.SalaryItemConfigQuery;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.salary.HrmsEmployeeSalaryInfoManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemConfigManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 薪资增减项迁移
 * @author: taokang
 * @createDate: 2024/1/8 13:59
 * @version: 1.0
 */
@Slf4j
@Component
public class SalaryItemTransferHandler {

    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsCompanyOtherSalaryConfigDao hrmsCompanyOtherSalaryConfigDao;
    @Autowired
    private HrmsEmployeeSalaryInfoManage hrmsEmployeeSalaryInfoManage;
    @Autowired
    private HrmsEmployeeSalaryInfoItemDao hrmsEmployeeSalaryInfoItemDao;
    @Autowired
    private HrmsSalaryItemConfigManage hrmsSalaryItemConfigManage;

    @XxlJob(BusinessConstant.JobHandler.SALARY_ITEM_TRANSFER_HANDLER)
    public ReturnT<String> salaryItemTransferHandler(String content) {

        SalaryItemTransferHandler.SalaryItemTransferHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, SalaryItemTransferHandler.SalaryItemTransferHandlerParam.class) : new SalaryItemTransferHandler.SalaryItemTransferHandlerParam();
        if (StringUtils.isNotBlank(param.getUserCodeList()) && (StringUtils.isBlank(param.getCountry()) || param.getSettlementDate() == null)) {
            return ReturnT.SUCCESS;
        }
        List<HrmsEmployeeSalaryInfoDO> employeeSalaryInfoDOList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodeList())) {
            List<String> countryList = Arrays.asList(param.getUserCodeList().split(","));
            List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(countryList);
            List<Long> userIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
            HrmsEmployeeSalaryInfoQuery hrmsEmployeeSalaryInfoQuery = new HrmsEmployeeSalaryInfoQuery();
            hrmsEmployeeSalaryInfoQuery.setPayrollCountry(param.getCountry());
            hrmsEmployeeSalaryInfoQuery.setSettlementDate(param.getSettlementDate());
            hrmsEmployeeSalaryInfoQuery.setUserIdList(userIdList);
            employeeSalaryInfoDOList = hrmsEmployeeSalaryInfoManage.selectList(hrmsEmployeeSalaryInfoQuery);
        } else {
            HrmsEmployeeSalaryInfoQuery hrmsEmployeeSalaryInfoQuery = new HrmsEmployeeSalaryInfoQuery();
            hrmsEmployeeSalaryInfoQuery.setPayrollCountry(param.getCountry());
            hrmsEmployeeSalaryInfoQuery.setSettlementDate(param.getSettlementDate());
            employeeSalaryInfoDOList = hrmsEmployeeSalaryInfoManage.selectList(hrmsEmployeeSalaryInfoQuery);
        }

        List<HrmsSalaryItemConfigDO> allItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(new SalaryItemConfigQuery());
        Map<String, List<HrmsSalaryItemConfigDO>> allItemConfigMap = allItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getItemNo));

        List<Long> employeeSalaryInfoIds = employeeSalaryInfoDOList.stream().map(HrmsEmployeeSalaryInfoDO::getId).collect(Collectors.toList());
        List<HrmsEmployeeSalaryInfoItemDO> hrmsEmployeeSalaryInfoItemDOList = hrmsEmployeeSalaryInfoItemDao.listInfo(employeeSalaryInfoIds);
        Map<Long, List<HrmsEmployeeSalaryInfoItemDO>> employeeItemMap = hrmsEmployeeSalaryInfoItemDOList.stream().collect(Collectors.groupingBy(HrmsEmployeeSalaryInfoItemDO::getEmployeeSalaryInfoId));

        List<HrmsCompanyOtherSalaryConfigDO> allCompanyOtherSalaryConfigDOS = hrmsCompanyOtherSalaryConfigDao.selectAllOtherSalaryConfig();

        List<HrmsEmployeeSalaryInfoItemDO> updateList = new ArrayList<>();
        List<Long> noReflectEmployeeItemIdList = new ArrayList<>();
        List<Long> noItemIdList = new ArrayList<>();

        for (HrmsEmployeeSalaryInfoDO employeeSalaryInfoDO : employeeSalaryInfoDOList) {
            List<HrmsEmployeeSalaryInfoItemDO> existEmployeeItemList = employeeItemMap.get(employeeSalaryInfoDO.getId());
            for (HrmsEmployeeSalaryInfoItemDO employeeSalaryInfoItemDO : existEmployeeItemList) {
                //同一个国家的优先
                List<HrmsCompanyOtherSalaryConfigDO> existCompanyOtherSalaryConfigDOS = allCompanyOtherSalaryConfigDOS.stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getCountry(), employeeSalaryInfoDO.getPayrollCountry())
                                && StringUtils.equalsIgnoreCase(item.getItemKey().trim(), employeeSalaryInfoItemDO.getItemKey().trim()))
                        .collect(Collectors.toList());
                List<HrmsCompanyOtherSalaryConfigDO> existNoCompanyOtherSalaryConfigDOS = allCompanyOtherSalaryConfigDOS.stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getItemKey().trim(), employeeSalaryInfoItemDO.getItemKey().trim()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existCompanyOtherSalaryConfigDOS) && CollectionUtils.isEmpty(existNoCompanyOtherSalaryConfigDOS)) {
                    noReflectEmployeeItemIdList.add(employeeSalaryInfoItemDO.getId());
                    continue;
                }
                String itemNo = "";
                if (CollectionUtils.isNotEmpty(existCompanyOtherSalaryConfigDOS)) {
                    itemNo = existCompanyOtherSalaryConfigDOS.get(0).getItemNo();
                } else {
                    itemNo = existNoCompanyOtherSalaryConfigDOS.get(0).getItemNo();
                }
                List<HrmsSalaryItemConfigDO> existItemList = allItemConfigMap.get(itemNo);
                if (CollectionUtils.isEmpty(existItemList)) {
                    noItemIdList.add(employeeSalaryInfoItemDO.getId());
                    continue;
                }
                employeeSalaryInfoItemDO.setItemKey(existItemList.get(0).getId().toString());
                updateList.add(employeeSalaryInfoItemDO);
            }
        }
        if (CollectionUtils.isNotEmpty(noReflectEmployeeItemIdList) || CollectionUtils.isNotEmpty(noItemIdList)) {
            XxlJobLogger.log("noReflectEmployeeItemIdList:{}", JSON.toJSONString(noReflectEmployeeItemIdList));
            XxlJobLogger.log("noItemIdList:{}", JSON.toJSONString(noItemIdList));
           // return ReturnT.SUCCESS;
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            hrmsEmployeeSalaryInfoItemDao.updateBatchById(updateList);
        }
        return ReturnT.SUCCESS;

    }


    @Data
    private static class SalaryItemTransferHandlerParam {

        /**
         * 申请国
         */
        private String country;

        /**
         * 结算时间-年月(202308)
         */
        private Long settlementDate;


        private String userCodeList;
    }
}
