package com.imile.hrms.job.account;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.model.component.Certificate;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.hermes.enterprise.dto.EntEmpDriverApiDTO;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hermes.enterprise.dto.UpdateHrUserIdParamDTO;
import com.imile.hermes.enterprise.query.SynHermes2HrmsApiPageQuery;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoBasicApiDTO;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CertificateTypeEnum;
import com.imile.hrms.common.enums.DataSourceEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.account.AccountBizTypeEnum;
import com.imile.hrms.common.enums.account.AccountStatusEnum;
import com.imile.hrms.common.enums.account.AccountTypeEnum;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.account.dao.HrmsAccountRecordDao;
import com.imile.hrms.dao.account.model.HrmsAccountRecordDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.sys.dao.HrmsCompanyCertificateConfigDao;
import com.imile.hrms.dao.sys.dao.ScriptErrorRecordDao;
import com.imile.hrms.dao.sys.dto.ScriptErrorRecordDO;
import com.imile.hrms.dao.sys.model.HrmsCompanyCertificateConfigDO;
import com.imile.hrms.dao.user.model.HrmsLaborContractInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserCertificateDO;
import com.imile.hrms.dao.user.model.HrmsUserEducationInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserExtendInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.CertificateConfigQuery;
import com.imile.hrms.dao.user.query.VendorQuery;
import com.imile.hrms.integration.hermes.service.EntEmpDriverService;
import com.imile.hrms.integration.hermes.service.EntEmployeeService;
import com.imile.hrms.integration.hermes.service.VendorService;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.user.HrmsUserLeaveService;
import com.imile.hrms.service.user.dto.UserLeaveAddDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 同步hermes账号到hrms
 *
 * <AUTHOR>
 * @date 2024/01/12
 */
@Slf4j
@Component
public class SynTms2HrmsAccountHandler {

    @Resource
    protected IHrmsIdWorker iHrmsIdWorker;
    @Resource
    private HrmsCompanyCertificateConfigDao hrmsCompanyCertificateConfigDao;
    @Resource
    private EntEmployeeService entEmployeeService;
    @Resource
    private EntEmpDriverService entEmpDriverService;
    @Resource
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Resource
    private HrmsAccountRecordDao hrmsAccountRecordDao;
    @Resource
    private HrmsProperties hrmsProperties;
    @Resource
    private VendorService vendorService;
    @Resource
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Resource
    private UserCertificateService userCertificateService;
    @Resource
    private HrmsUserLeaveService userLeaveService;
    @Resource
    private ScriptErrorRecordDao scriptErrorRecordDao;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public static final String JOB_NAME = BusinessConstant.JobHandler.SYN_TMS_2_HRMS_ACCOUNT;

    @XxlJob(BusinessConstant.JobHandler.SYN_TMS_2_HRMS_ACCOUNT)
    public ReturnT<String> synTms2HrmsAccountHandler(String param) {
        XxlJobLogger.log("开始执行,param:{}", param);
        log.info("{} 开始执行,param:{}", JOB_NAME, param);
        List<ScriptErrorRecordDO> errorRecords = Lists.newArrayList();
        UserContext userContext = new UserContext();
        userContext.setUserCode("XXL-JOB");
        userContext.setUserName("XXL-JOB");
        userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        RequestInfoHolder.setLoginInfo(userContext);
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("开始执行, param: %s", param)));
        Integer countLimit = 3;
        try {
            if (Strings.isEmpty(param)) {
                // 默认无参数，分页查询tms中hrUserId is null的账号，每次处理1000条
                // 先查司机
                List<String> allDriverUserCode = Lists.newArrayList();
                processDriver(allDriverUserCode, false, errorRecords, countLimit, Boolean.TRUE, Boolean.TRUE, null);
                // 再查员工
                processUser(allDriverUserCode, errorRecords, countLimit, Boolean.TRUE, Boolean.TRUE, null);
            } else {
                // 有参数，控制台手动触发
                SynAccountParam synAccountParam = JSON.parseObject(param, SynAccountParam.class);
                if (synAccountParam == null) {
                    return ReturnT.SUCCESS;
                }
                // 手动调用时，默认false
                Boolean doSql = synAccountParam.getDoSql() != null && synAccountParam.getDoSql();
                // 手动调用时，默认true
                Boolean isCheck = synAccountParam.getIsCheck() != null ? synAccountParam.getIsCheck() : true;
                // 手动调用时，最大countLimit为50
                if (synAccountParam.getCountLimit() != null) {
                    countLimit = Math.min(synAccountParam.getCountLimit(), 50);
                }
                List<String> allDriverUserCode = Lists.newArrayList();
                if (synAccountParam.getIsOnlyLineHaul() != null && synAccountParam.getIsOnlyLineHaul()) {
                    processDriver(allDriverUserCode, true, errorRecords, countLimit, doSql, isCheck, synAccountParam.getBlackList());
                } else if (synAccountParam.getCountLimit() != null) {
                    processDriver(allDriverUserCode, false, errorRecords, countLimit, doSql, isCheck, synAccountParam.getBlackList());
                    // 再查员工
                    processUser(allDriverUserCode, errorRecords, countLimit, doSql, isCheck, synAccountParam.getBlackList());
                } else {
                    if (!CollectionUtil.isEmpty(synAccountParam.getDriverCodeList())) {
                        SynHermes2HrmsApiPageQuery query = new SynHermes2HrmsApiPageQuery();
                        query.setPageSize(1000);
                        query.setCurrentPage(1);
                        query.setUserCodes(synAccountParam.getDriverCodeList());
                        PaginationResult<EntEmpDriverApiDTO> page = entEmpDriverService.getSynHermes2HrmsDriver(query);
                        List<EntEmpDriverApiDTO> list = page.getResults();
                        if (!CollectionUtil.isEmpty(list)) {
                            List<EntEmpDriverApiDTO> filter = list.stream().
                                    filter(x -> CollectionUtils.isEmpty(synAccountParam.getBlackList()) || !synAccountParam.getBlackList().contains(x.getDriverCode())).
                                    collect(Collectors.toList());
                            doProcessDriver(filter, allDriverUserCode, errorRecords, doSql, isCheck);
                        }
                    }
                    if (!CollectionUtil.isEmpty(synAccountParam.getUserCodeList())) {
                        SynHermes2HrmsApiPageQuery employeeQuery = new SynHermes2HrmsApiPageQuery();
                        employeeQuery.setPageSize(1000);
                        employeeQuery.setCurrentPage(1);
                        employeeQuery.setUserCodes(synAccountParam.getUserCodeList());
                        PaginationResult<EntEmployeeApiDTO> page = entEmployeeService.getSynHermes2HrmsEmployee(employeeQuery);
                        List<EntEmployeeApiDTO> list = page.getResults();
                        if (!CollectionUtil.isEmpty(list)) {
                            List<EntEmployeeApiDTO> filter = list.stream().
                                    filter(e -> !allDriverUserCode.contains(e.getUserCode())).
                                    filter(x -> CollectionUtils.isEmpty(synAccountParam.getBlackList()) || !synAccountParam.getBlackList().contains(x.getUserCode())).
                                    collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(filter)) {
                                doProcessEmployee(filter, errorRecords, doSql, isCheck);
                            }
                        }
                    }
                }
            }
            errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("执行结束, param:%s", param)));
            XxlJobLogger.log("执行结束, param:{}", param);
            log.info("{} 执行结束, param:{}", JOB_NAME, param);
        } catch (Exception ignored) {
        } finally {
            if (!CollectionUtil.isEmpty(errorRecords)) {
                try {
                    Lists.partition(errorRecords, 2000).forEach(e -> scriptErrorRecordDao.saveBatch(e));
                } catch (Exception e) {
                    log.info("{} insert error record failed, reason: {}", JOB_NAME, e.getMessage());
                }
            }
        }
        return ReturnT.SUCCESS;
    }

    private void processDriver(List<String> allDriverUserCode, Boolean isOnlyLineHaul, List<ScriptErrorRecordDO> errorRecords, Integer countLimit, Boolean doSql, Boolean isCheck, List<String> blackList) {
        int currentPage = 1;
        int pageSize = 1000;
        int totalRowsFetched = 0;
        int total = 0;
        int count = 0;
        while (count < countLimit) {
            count += 1;
            SynHermes2HrmsApiPageQuery query = new SynHermes2HrmsApiPageQuery();
            query.setPageSize(pageSize);
            query.setCurrentPage(currentPage);
            if (isOnlyLineHaul) {
                query.setFunctional("LineHaul");
            }
            PaginationResult<EntEmpDriverApiDTO> page = entEmpDriverService.getSynHermes2HrmsDriver(query);
            // 第一次查询记录total数量
            if (total == 0) {
                total = page.getPagination().getTotalResult();
            }
            List<EntEmpDriverApiDTO> list = page.getResults();
            // 数据为空时跳出循环
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            // 去除黑名单
            List<EntEmpDriverApiDTO> filter = list.stream().filter(x -> CollectionUtils.isEmpty(blackList) || !blackList.contains(x.getDriverCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filter)) {
                doProcessDriver(filter, allDriverUserCode, errorRecords, doSql, isCheck);
            }
            // 处理完成时跳出循环
            totalRowsFetched += list.size();
            if (totalRowsFetched >= total) {
                break;
            }
            currentPage++;
        }
    }

    private void doProcessDriver(List<EntEmpDriverApiDTO> list, List<String> allDriverUserCode, List<ScriptErrorRecordDO> errorRecords, Boolean doSql, Boolean isCheck) {
        List<String> driverCodeList = list.stream().map(EntEmpDriverApiDTO::getDriverCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        allDriverUserCode.addAll(driverCodeList);
        List<String> dtlUserCodeList = list.stream().map(EntEmpDriverApiDTO::getDtlUserCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 查询对应hrms账号
        List<HrmsUserInfoDO> userInfoList = hrmsUserInfoManage.selectUserInfoByCodes(driverCodeList);
        Map<String, HrmsUserInfoDO> userMap = CollectionUtil.isEmpty(userInfoList) ? Maps.newHashMap() : userInfoList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, item -> item, (k1, k2) -> k1));
        // 查询上级hrms账号
        List<HrmsUserInfoDO> dtlUserInfoList = hrmsUserInfoManage.selectUserInfoByCodes(dtlUserCodeList);
        Map<String, HrmsUserInfoDO> dtlUserMap = CollectionUtil.isEmpty(dtlUserInfoList) ? Maps.newHashMap() : dtlUserInfoList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, item -> item, (k1, k2) -> k1));
        // 生成对应hrms账号
        AtomicInteger driverCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(item -> threadPoolTaskExecutor.execute(() -> {
            try {
                UserContext userContext = new UserContext();
                userContext.setUserCode("XXL-JOB");
                userContext.setUserName("XXL-JOB");
                userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
                RequestInfoHolder.setLoginInfo(userContext);
                HrmsUserInfoDO userInfo = userMap.get(item.getDriverCode());
                HrmsUserInfoDO dtl = dtlUserMap.get(item.getDtlUserCode());
                if (userInfo == null) {
                    // 不存在则创建
                    List<HrmsEntDeptDO> deptList = hrmsEntDeptDao.getDeptByOcIdWithAllStatus(item.getOcId());
                    HrmsEntDeptDO oc = CollectionUtil.isEmpty(deptList) ? null : deptList.get(0);
                    // 校验网点
                    if (isCheck && oc == null) {
//                        XxlJobLogger.log("check failed, driverCode: {}, reason: {}", item.getDriverCode(), HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc());
                        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(item.getDriverCode(), String.format("check failed, driverCode: %s, reason: %s", item.getDriverCode(), HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc())));
                        failCount.incrementAndGet();
                        return;
                    }
                    List<HrmsCompanyCertificateConfigDO> certificateConfigDOList = getDriverCompanyCertificateConfig(item, oc);
                    if (isCheck && !checkDriver(item, dtl, oc, errorRecords)) {
                        failCount.incrementAndGet();
                        return;
                    }
                    // 创建账号 写入hrms
                    driverEntry(item, dtl, oc, certificateConfigDOList, doSql);
                    driverCount.incrementAndGet();
                    return;
                } else if (doSql) {
                    // 同步hr_user_id至tms侧
                    updateUserIdToTms(userInfo);
                }
                driverCount.incrementAndGet();
            } catch (Exception e) {
                failCount.incrementAndGet();
//                XxlJobLogger.log("check failed, driverCode: {}, reason: {}", item.getDriverCode(), e);
                errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(item.getDriverCode(), String.format("check failed, driverCode: %s, reason: %s", item.getDriverCode(), Arrays.toString(e.getStackTrace()))));
            } finally {
                countDownLatch.countDown();
            }
        }));
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            XxlJobLogger.log("CountDownLatch wait InterruptedException");
            Thread.currentThread().interrupt();
        }
        XxlJobLogger.log("total driver count in the cycle: {}", list.size());
        XxlJobLogger.log("success driver count in the cycle: {}", driverCount);
        XxlJobLogger.log("fail driver count in the cycle: {}", failCount);
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("total driver count in the cycle %s", list.size())));
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("success driver count in the cycle %s", driverCount)));
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("fail driver count in the cycle %s", failCount)));
    }

    private void processUser(List<String> allDriverUserCode, List<ScriptErrorRecordDO> errorRecords, Integer countLimit, Boolean doSql, Boolean isCheck, List<String> blackList) {
        int currentPage = 1;
        int pageSize = 1000;
        int totalRowsFetched = 0;
        int total = 0;
        int count = 0;
        // 防止死循环
        while (count < countLimit) {
            count += 1;
            SynHermes2HrmsApiPageQuery employeeQuery = new SynHermes2HrmsApiPageQuery();
            employeeQuery.setPageSize(pageSize);
            employeeQuery.setCurrentPage(currentPage);
            PaginationResult<EntEmployeeApiDTO> page = entEmployeeService.getSynHermes2HrmsEmployee(employeeQuery);
            // 第一次查询记录total数量
            if (total == 0) {
                total = page.getPagination().getTotalResult();
            }
            List<EntEmployeeApiDTO> list = page.getResults();
            // 数据为空时跳出循环
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            List<EntEmployeeApiDTO> filter = list.stream().filter(e -> !allDriverUserCode.contains(e.getUserCode())).
                    filter(x -> CollectionUtils.isEmpty(blackList) || !blackList.contains(x.getUserCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filter)) {
                doProcessEmployee(filter, errorRecords, doSql, isCheck);
            }
            // 处理完成时跳出循环
            totalRowsFetched += list.size();
            if (totalRowsFetched >= total) {
                break;
            }
            currentPage++;
        }
    }

    private void doProcessEmployee(List<EntEmployeeApiDTO> list, List<ScriptErrorRecordDO> errorRecords, Boolean doSql, Boolean isCheck) {
        // 查询对应hrms账号
        List<String> userCodeList = list.stream().map(EntEmployeeApiDTO::getUserCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        Map<String, HrmsUserInfoDO> userMap = CollectionUtil.isEmpty(userInfoList) ? Maps.newHashMap() : userInfoList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, item -> item, (k1, k2) -> k1));
        // 生成对应hrms账号
        AtomicInteger userCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(item -> threadPoolTaskExecutor.execute(() -> {
            try {
                UserContext userContext = new UserContext();
                userContext.setUserCode("XXL-JOB");
                userContext.setUserName("XXL-JOB");
                userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
                RequestInfoHolder.setLoginInfo(userContext);
                HrmsUserInfoDO userInfo = userMap.get(item.getUserCode());
                if (userInfo == null) {
                    // 不存在则创建
                    List<HrmsEntDeptDO> deptList = hrmsEntDeptDao.getDeptByOcIdWithAllStatus(item.getOcId());
                    HrmsEntDeptDO oc = CollectionUtil.isEmpty(deptList) ? null : deptList.get(0);
                    if (isCheck && oc == null) {
//                        XxlJobLogger.log("check failed, userCode: {}, reason: {}", item.getUserCode(), HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc());
                        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(item.getUserCode(), String.format("check failed, userCode: %s, reason: %s", item.getUserCode(), HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc())));
                        failCount.incrementAndGet();
                        return;
                    }
                    List<HrmsCompanyCertificateConfigDO> certificateConfigDOList = getUserCompanyCertificateConfig(oc);
                    if (isCheck && !checkUser(item, errorRecords)) {
                        failCount.incrementAndGet();
                        return;
                    }
                    // 创建账号 写入hrms
                    employeeEntry(item, oc, certificateConfigDOList, doSql);
                    userCount.incrementAndGet();
                    return;
                } else if (doSql) {
                    // 同步hr_user_id至tms侧
                    updateUserIdToTms(userInfo);
                }
                userCount.incrementAndGet();
            } catch (Exception e) {
                failCount.incrementAndGet();
//                XxlJobLogger.log("check failed, userCode: {}, reason: {}", item.getUserCode(), e);
                errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(item.getUserCode(), String.format("%check failed, userCode: %s, reason: %s", item.getUserCode(), Arrays.toString(e.getStackTrace()))));
            } finally {
                countDownLatch.countDown();
            }
        }));
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            XxlJobLogger.log("CountDownLatch wait InterruptedException");
            Thread.currentThread().interrupt();
        }
        XxlJobLogger.log("total user count in the cycle: {}", list.size());
        XxlJobLogger.log("success user count in the cycle: {}", userCount);
        XxlJobLogger.log("failed user count in the cycle: {}", failCount);
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("total user count in the cycle %s", list.size())));
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("success user count in the cycle %s", userCount)));
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(null, String.format("fail user count in the cycle %s", failCount)));
    }

    public void driverEntry(EntEmpDriverApiDTO driver, HrmsUserInfoDO dtl, HrmsEntDeptDO oc, List<HrmsCompanyCertificateConfigDO> certificateConfigDOList, Boolean doSql) {
        // 基础信息处理 注意空指针
        HrmsUserInfoDO userInfoDO = handleDriverUserInfo(driver, dtl, oc);
        // 证件信息处理 注意空指针
        List<HrmsUserCertificateDO> userCertificateInfoDOList = handleDriverCertificateInfo(userInfoDO, driver, certificateConfigDOList);
        // 拓展信息处理 注意空指针
        HrmsUserExtendInfoDO userExtendInfoDO = hrmsUserInfoManage.handleUserExtendInfo(userInfoDO, driver.getEmail());
        // 教育信息处理 注意空指针
        HrmsUserEducationInfoDO userEducationInfoDO = hrmsUserInfoManage.handlerUserEducationInfo(userInfoDO);
        // 入职信息处理 注意空指针
        HrmsUserEntryRecordDO userEntryRecordDO = hrmsUserInfoManage.handleUserEntryRecord(userInfoDO);
        userEntryRecordDO.setEntryDate(driver.getCreateDate());
        userEntryRecordDO.setConfirmDate(driver.getCreateDate());
        // 合同信息 注意空指针
        HrmsLaborContractInfoDO laborContractInfoDO = hrmsUserInfoManage.handlerLaborContractInfo(userInfoDO);
        // 用户角色
        userInfoDO.setWorkNo(iHrmsIdWorker.nextWorkNo());
        if (doSql) {
            hrmsUserInfoManage.save(userInfoDO, userExtendInfoDO, userEntryRecordDO, userCertificateInfoDOList, userEducationInfoDO, laborContractInfoDO, null);
            // 个人假期设置
            UserLeaveAddDTO addDTO = new UserLeaveAddDTO();
            addDTO.setUserId(userInfoDO.getId());
            addDTO.setCountry(userInfoDO.getLocationCountry());
            userLeaveService.userEntryAddLeaveInfo(addDTO);
            // 工号，tms账号开通情况
            accountOpenRecord(userInfoDO);
            // 同步hr_user_id至tms侧
            updateUserIdToTms(driver.getStatus(), userInfoDO);
        }
    }

    public void employeeEntry(EntEmployeeApiDTO employee, HrmsEntDeptDO oc, List<HrmsCompanyCertificateConfigDO> certificateConfigDOList, Boolean doSql) {
        // 基础信息处理 注意空指针
        HrmsUserInfoDO userInfoDO = handleEmployeeUserInfo(employee, oc);
        // 证件信息处理 注意空指针
        List<HrmsUserCertificateDO> userCertificateInfoDOList = handleEmployeeCertificateInfo(userInfoDO, employee, certificateConfigDOList);
        // 拓展信息处理 注意空指针
        HrmsUserExtendInfoDO userExtendInfoDO = hrmsUserInfoManage.handleUserExtendInfo(userInfoDO, employee.getEmail());
        // 教育信息处理 注意空指针
        HrmsUserEducationInfoDO userEducationInfoDO = hrmsUserInfoManage.handlerUserEducationInfo(userInfoDO);
        // 入职信息处理 注意空指针
        HrmsUserEntryRecordDO userEntryRecordDO = hrmsUserInfoManage.handleUserEntryRecord(userInfoDO);
        userEntryRecordDO.setEntryDate(employee.getCreateDate());
        userEntryRecordDO.setConfirmDate(employee.getCreateDate());
        // 合同信息 注意空指针
        HrmsLaborContractInfoDO laborContractInfoDO = hrmsUserInfoManage.handlerLaborContractInfo(userInfoDO);
        // 用户角色
        userInfoDO.setWorkNo(iHrmsIdWorker.nextWorkNo());
        if (doSql) {
            hrmsUserInfoManage.save(userInfoDO, userExtendInfoDO, userEntryRecordDO, userCertificateInfoDOList, userEducationInfoDO, laborContractInfoDO, null);
            // 个人假期设置
            UserLeaveAddDTO addDTO = new UserLeaveAddDTO();
            addDTO.setUserId(userInfoDO.getId());
            addDTO.setCountry(userInfoDO.getLocationCountry());
            userLeaveService.userEntryAddLeaveInfo(addDTO);
            // 工号，tms账号开通情况
            accountOpenRecord(userInfoDO);
            // 同步hr_user_id至tms侧
            updateUserIdToTms(employee.getStatus(), userInfoDO);
        }
    }

    private HrmsUserInfoDO handleDriverUserInfo(EntEmpDriverApiDTO driver, HrmsUserInfoDO leader, HrmsEntDeptDO oc) {
        HrmsUserInfoDO hrmsUserInfoDO = new HrmsUserInfoDO();
        //员工姓名
        hrmsUserInfoDO.setId(iHrmsIdWorker.nextId());
        hrmsUserInfoDO.setUserName(driver.getDriverName());
        //编码
        hrmsUserInfoDO.setUserCode(driver.getDriverCode());
        hrmsUserInfoDO.setDataSource(DataSourceEnum.SYNC.getCode());
        //部门
        hrmsUserInfoDO.setDeptId(Optional.ofNullable(oc).map(HrmsEntDeptDO::getId).orElse(null));
        hrmsUserInfoDO.setOcId(Optional.ofNullable(oc).map(HrmsEntDeptDO::getId).orElse(null));
        hrmsUserInfoDO.setOcCode(Optional.ofNullable(oc).map(HrmsEntDeptDO::getOcCode).orElse(""));
        //岗位
        hrmsUserInfoDO.setPostId(hrmsProperties.getEntry().getPostId());
        //上级
        if (leader != null) {
            hrmsUserInfoDO.setLeaderId(leader.getId());
            hrmsUserInfoDO.setLeaderName(leader.getUserName());
        }
        //供应商信息
        VendorInfoApiDTO vendor = null;
        try {
            vendor = vendorService.synTms2HrmsAccountVendor(driver.getVendorCode());
        } catch (Exception e) {
            log.error("rpc-获取供应商信息失败, driverCode: {}, vendorCode: {}", driver.getDriverCode(), driver.getVendorCode(), e);
        }
        hrmsUserInfoDO.setVendorId(Optional.ofNullable(vendor).map(VendorInfoApiDTO::getVendorId).orElse(null));
        hrmsUserInfoDO.setVendorOrgId(Optional.ofNullable(vendor).map(VendorInfoApiDTO::getOrgId).orElse(null));
        hrmsUserInfoDO.setVendorCode(driver.getVendorCode());
        hrmsUserInfoDO.setVendorName(Optional.ofNullable(vendor).map(VendorInfoApiDTO::getVendorShortName).orElse(null));
        //是否仓内工作人员
        hrmsUserInfoDO.setIsWarehouseStaff(BusinessConstant.N);
        hrmsUserInfoDO.setEmployeeType(driver.getType() != null ? driver.getType() : EmploymentTypeEnum.OS_PER_DELIVERED.getCode());
        // 公司, oc已校验不会为空
        hrmsUserInfoDO.setOriginCountry(oc == null ? null : oc.getCountry());
        //获取公司对应的国家，拿到国家编码，拿到区号
        hrmsUserInfoManage.getPhone(driver.getMobile(), hrmsUserInfoDO.getOriginCountry(), hrmsUserInfoDO);
        hrmsUserInfoDO.setSex(driver.getSex());
        //状态
        hrmsUserInfoDO.setStatus(driver.getStatus());
        hrmsUserInfoDO.setWorkStatus(WorkStatusEnum.ON_JOB.getCode());
        hrmsUserInfoDO.setSysAccountName(driver.getDriverName());
        hrmsUserInfoDO.setIsDriver(BusinessConstant.Y);
        hrmsUserInfoDO.setIsFinish(BusinessConstant.Y);
        hrmsUserInfoDO.setFunctional(driver.getFunctional());
        // 是否虚拟账号
        hrmsUserInfoDO.setIsVirtual(BusinessConstant.N);
        hrmsUserInfoDO.setLocationCity(driver.getLocationCity());
        hrmsUserInfoDO.setLocationProvince(driver.getLocationProvince());
        hrmsUserInfoDO.setLocationCountry(driver.getLocationCountry());
        if (StringUtils.isBlank(hrmsUserInfoDO.getLocationCountry()) && oc != null) {
            hrmsUserInfoDO.setLocationCountry(oc.getCountry());
        }
        BaseDOUtil.fillDOInsert(hrmsUserInfoDO);
        return hrmsUserInfoDO;

    }


    private HrmsUserInfoDO handleEmployeeUserInfo(EntEmployeeApiDTO employee, HrmsEntDeptDO oc) {
        HrmsUserInfoDO hrmsUserInfoDO = new HrmsUserInfoDO();
        //员工姓名
        hrmsUserInfoDO.setId(iHrmsIdWorker.nextId());
        hrmsUserInfoDO.setUserName(employee.getUserName());
        //编码
        hrmsUserInfoDO.setUserCode(employee.getUserCode());
        hrmsUserInfoDO.setDataSource(DataSourceEnum.SYNC.getCode());
        //部门
        hrmsUserInfoDO.setDeptId(Optional.ofNullable(oc).map(HrmsEntDeptDO::getId).orElse(null));
        hrmsUserInfoDO.setOcId(Optional.ofNullable(oc).map(HrmsEntDeptDO::getId).orElse(null));
        hrmsUserInfoDO.setOcCode(Optional.ofNullable(oc).map(HrmsEntDeptDO::getOcCode).orElse(""));
        //岗位
        hrmsUserInfoDO.setIsWarehouseStaff(BusinessConstant.Y);
        hrmsUserInfoDO.setEmployeeType(employee.getEmploymentType());
        VendorInfoBasicApiDTO vendor = null;
        try {
            if (employee.getVendorId() != null) {
                VendorQuery vendorQuery = VendorQuery.builder().orgId(employee.getVendorId()).build();
                List<VendorInfoBasicApiDTO> list = vendorService.getBaseVendorList(vendorQuery);
                if (!CollectionUtils.isEmpty(list)) {
                    vendor = list.get(0);
                }
            }
        } catch (Exception e) {
            log.error("rpc-获取供应商信息失败, userCode: {}, vendorId: {}", employee.getUserCode(), employee.getVendorId(), e);
        }
        hrmsUserInfoDO.setVendorId(Optional.ofNullable(vendor).map(VendorInfoBasicApiDTO::getVendorId).orElse(null));
        hrmsUserInfoDO.setVendorOrgId(Optional.ofNullable(vendor).map(VendorInfoBasicApiDTO::getOrgId).orElse(null));
        hrmsUserInfoDO.setVendorCode(Optional.ofNullable(vendor).map(VendorInfoBasicApiDTO::getVendorCode).orElse(null));
        hrmsUserInfoDO.setVendorName(Optional.ofNullable(vendor).map(VendorInfoBasicApiDTO::getVendorShortName).orElse(null));
        // 公司
        hrmsUserInfoDO.setOriginCountry(oc == null ? null : oc.getCountry());
        //获取公司对应的国家，拿到国家编码，拿到区号
        hrmsUserInfoManage.getPhone(employee.getMobile(), hrmsUserInfoDO.getOriginCountry(), hrmsUserInfoDO);
        hrmsUserInfoDO.setSex(employee.getSex());
        //状态
        hrmsUserInfoDO.setStatus(employee.getStatus());
        hrmsUserInfoDO.setWorkStatus(WorkStatusEnum.ON_JOB.getCode());
        hrmsUserInfoDO.setSysAccountName(employee.getUserName());
        hrmsUserInfoDO.setIsDriver(BusinessConstant.N);
        hrmsUserInfoDO.setIsFinish(BusinessConstant.Y);
        hrmsUserInfoDO.setFunctional(null);
        // 是否虚拟账号
        hrmsUserInfoDO.setIsVirtual(BusinessConstant.N);
        hrmsUserInfoDO.setLocationCity(employee.getLocationCity());
        hrmsUserInfoDO.setLocationProvince(employee.getLocationProvince());
        hrmsUserInfoDO.setLocationCountry(employee.getLocationCountry());
        if (StringUtils.isBlank(hrmsUserInfoDO.getLocationCountry()) && oc != null) {
            hrmsUserInfoDO.setLocationCountry(oc.getCountry());
        }
        hrmsUserInfoDO.setIsDtl(employee.getIsDtl() != null && employee.getIsDtl() ? BusinessConstant.Y : BusinessConstant.N);
        BaseDOUtil.fillDOInsert(hrmsUserInfoDO);
        return hrmsUserInfoDO;

    }


    private List<HrmsUserCertificateDO> handleDriverCertificateInfo(HrmsUserInfoDO userInfoDO, EntEmpDriverApiDTO driver, List<HrmsCompanyCertificateConfigDO> certificateConfigDOList) {
        Map<String, HrmsCompanyCertificateConfigDO> companyCertificateConfigMap = CollectionUtils.isEmpty(certificateConfigDOList) ? Maps.newHashMap() :
                certificateConfigDOList.stream().collect(Collectors.toMap(HrmsCompanyCertificateConfigDO::getCertificateTypeCode, item -> item, (oldValue, newValue) -> oldValue));
        List<HrmsUserCertificateDO> res = Lists.newArrayList();
        //身份证
        processIdCard(userInfoDO, driver.getIdCard(), companyCertificateConfigMap, res);
        // 驾照
        if (!StringUtils.isEmpty(driver.getDriverLicense())) {
            HrmsCompanyCertificateConfigDO hrmsCompanyCertificateConfigDO = companyCertificateConfigMap.get(CertificateTypeEnum.DRIVING_LICENSE.getCode());
            HrmsUserCertificateDO userCertificate = getUserCertificate(userInfoDO, driver.getDriverLicense(), driver.getLicenseBegin(), hrmsCompanyCertificateConfigDO, CertificateTypeEnum.DRIVING_LICENSE);
            if (userCertificate.getCertificateExpireDate() != null) {
                userCertificate.setCertificateExpireDate(driver.getLicenseEnd());
            }
            res.add(userCertificate);
        }
        return res;
    }

    private void processIdCard(HrmsUserInfoDO userInfoDO, String idCard, Map<String, HrmsCompanyCertificateConfigDO> companyCertificateConfigMap, List<HrmsUserCertificateDO> res) {
        if (!StringUtils.isEmpty(idCard)) {
            // 优先级：身份证-居住证-护照
            CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.ID_CARD;
            HrmsCompanyCertificateConfigDO hrmsCompanyCertificateConfigDO = companyCertificateConfigMap.get(CertificateTypeEnum.ID_CARD.getCode());
            if (hrmsCompanyCertificateConfigDO == null) {
                certificateTypeEnum = CertificateTypeEnum.RESIDENCY_PERMIT;
                hrmsCompanyCertificateConfigDO = companyCertificateConfigMap.get(CertificateTypeEnum.RESIDENCY_PERMIT.getCode());
            }
            if (hrmsCompanyCertificateConfigDO == null) {
                certificateTypeEnum = CertificateTypeEnum.PASS_PORT;
                hrmsCompanyCertificateConfigDO = companyCertificateConfigMap.get(CertificateTypeEnum.PASS_PORT.getCode());
            }
            HrmsUserCertificateDO userCertificate = getUserCertificate(userInfoDO, idCard, null, hrmsCompanyCertificateConfigDO, certificateTypeEnum);
            res.add(userCertificate);
        }
    }

    private List<HrmsUserCertificateDO> handleEmployeeCertificateInfo(HrmsUserInfoDO userInfoDO, EntEmployeeApiDTO employee, List<HrmsCompanyCertificateConfigDO> certificateConfigDOList) {
        Map<String, HrmsCompanyCertificateConfigDO> companyCertificateConfigMap = CollectionUtils.isEmpty(certificateConfigDOList) ? Maps.newHashMap() :
                certificateConfigDOList.stream().collect(Collectors.toMap(HrmsCompanyCertificateConfigDO::getCertificateTypeCode, item -> item, (oldValue, newValue) -> oldValue));
        List<HrmsUserCertificateDO> res = Lists.newArrayList();
        // 身份证
        processIdCard(userInfoDO, employee.getIdCard(), companyCertificateConfigMap, res);
        return res;
    }

    private void accountOpenRecord(HrmsUserInfoDO userInfoDO) {
        List<HrmsAccountRecordDO> res = new ArrayList<>();
        // 工号开通成功
        res.add(getAccountOpenRecord(userInfoDO, AccountTypeEnum.WORK_NO, userInfoDO.getWorkNo()));
        //账号开通成功
        res.add(getAccountOpenRecord(userInfoDO, AccountTypeEnum.TMS_ACCOUNT, userInfoDO.getUserCode()));
        hrmsAccountRecordDao.saveBatch(res);
    }

    private HrmsAccountRecordDO getAccountOpenRecord(HrmsUserInfoDO userInfoDO, AccountTypeEnum accountTypeEnum, String account) {
        HrmsAccountRecordDO hrmsAccountRecordDO = new HrmsAccountRecordDO();
        hrmsAccountRecordDO.setId(iHrmsIdWorker.nextId());
        hrmsAccountRecordDO.setBizId(String.valueOf(userInfoDO.getId()));
        hrmsAccountRecordDO.setBizType(AccountBizTypeEnum.USER_ENTRY_OPEN.name());
        hrmsAccountRecordDO.setAccountTypeCode(accountTypeEnum.name());
        hrmsAccountRecordDO.setAccountStatus(AccountStatusEnum.CREATED.name());
        hrmsAccountRecordDO.setAccount(account);
        hrmsAccountRecordDO.setIsDelete(IsDeleteEnum.NO.getCode());
        hrmsAccountRecordDO.setIsLatest(BusinessConstant.Y);
        hrmsAccountRecordDO.setIsNeedHandle(BusinessConstant.Y);
        hrmsAccountRecordDO.setIsSuccess(BusinessConstant.Y);
        hrmsAccountRecordDO.setRecordVersion(1L);
        BaseDOUtil.fillDOInsert(hrmsAccountRecordDO);
        return hrmsAccountRecordDO;

    }


    /**
     * 获取证件类
     *
     * @param userInfoDO
     * @param
     * @param
     * @param
     */
    private HrmsUserCertificateDO getUserCertificate(HrmsUserInfoDO userInfoDO, String certificateCode, Date date, HrmsCompanyCertificateConfigDO hrmsCompanyCertificateConfigDO, CertificateTypeEnum certificateTypeEnum) {
        HrmsUserCertificateDO certificateInfoDO = BeanUtil.copyProperties(userInfoDO, HrmsUserCertificateDO.class);
        certificateInfoDO.setCertificateTypeCode(certificateTypeEnum.getCode());
        certificateInfoDO.setCertificateCode(certificateCode);
        certificateInfoDO.setCertificateExpireDate(date);
        certificateInfoDO.setId(iHrmsIdWorker.nextId());
        certificateInfoDO.setUserId(userInfoDO.getId());
        certificateInfoDO.setCountry(StringUtils.isBlank(userInfoDO.getOriginCountry()) ? "" : userInfoDO.getOriginCountry());
        certificateInfoDO.setHandlerMethod(Optional.ofNullable(hrmsCompanyCertificateConfigDO).map(HrmsCompanyCertificateConfigDO::getHandlerMethod).orElse(null));
        BaseDOUtil.fillDOInsert(certificateInfoDO);
        return certificateInfoDO;
    }

    private void updateUserIdToTms(String hermesStatus, HrmsUserInfoDO userInfo) {
        UpdateHrUserIdParamDTO updateHrUserIdParamDTO = new UpdateHrUserIdParamDTO();
        updateHrUserIdParamDTO.setUserCode(userInfo.getUserCode());
        updateHrUserIdParamDTO.setHrUserId(userInfo.getId());
        updateHrUserIdParamDTO.setHrWorkStatus(hermesStatus != null && hermesStatus.equals(StatusEnum.ACTIVE.getCode()) ? WorkStatusEnum.ON_JOB.getCode() : "DISABLED");
        List<UpdateHrUserIdParamDTO> userIdParams = new ArrayList<>();
        userIdParams.add(updateHrUserIdParamDTO);
        entEmpDriverService.updateHrUserId(userIdParams);
    }

    private void updateUserIdToTms(HrmsUserInfoDO userInfo) {
        UpdateHrUserIdParamDTO updateHrUserIdParamDTO = new UpdateHrUserIdParamDTO();
        updateHrUserIdParamDTO.setUserCode(userInfo.getUserCode());
        updateHrUserIdParamDTO.setHrUserId(userInfo.getId());
        updateHrUserIdParamDTO.setHrWorkStatus(userInfo.getWorkStatus());
        List<UpdateHrUserIdParamDTO> userIdParams = new ArrayList<>();
        userIdParams.add(updateHrUserIdParamDTO);
        entEmpDriverService.updateHrUserId(userIdParams);
    }

    /**
     * 检查司机
     */
    private Boolean checkDriver(EntEmpDriverApiDTO driver, HrmsUserInfoDO leader, HrmsEntDeptDO oc, List<ScriptErrorRecordDO> errorRecords) {
        // 检验汇报上级
        String employmentType = driver.getType() != null ? driver.getType() : EmploymentTypeEnum.OS_PER_DELIVERED.getCode();
//        if (leader == null && !EmploymentTypeEnum.OS_PER_DELIVERED.getCode().equals(employmentType)) {
//            XxlJobLogger.log("check failed, driverCode: {}, reason: {}", driver.getDriverCode(), "汇报上级信息不存在");
//            errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(driver.getDriverCode(), String.format("check failed, driverCode: %s, reason: %s", driver.getDriverCode(), "汇报上级信息不存在")));
//            return false;
//        }
        //  只校验启用账号的证件
        if (!StatusEnum.ACTIVE.getCode().equals(driver.getStatus())) {
            return true;
        }
        List<Certificate> certificateList = Lists.newArrayList(Certificate.builder()
                        .certificateTypeCode(CertificateTypeEnum.ID_CARD.getCode())
                        .certificateCode(driver.getIdCard())
                        .build(),
                Certificate.builder()
                        .certificateTypeCode(CertificateTypeEnum.DRIVING_LICENSE.getCode()).
                        certificateCode(driver.getDriverLicense())
                        .build());
        UserCertificateCheckResultDTO resultDTO = userCertificateService.checkUserCertificateDuplicate(UserCertificateDuplicateCheckParam.builder()
                .certificateList(certificateList)
                .build());

        // 查询必填证件类型
        if (!resultDTO.getIsRepeat()) {
            return true;
        }
        UserCertificateCheckResultDTO.RepeatCertificate repeatCertificate = resultDTO.getRepeatCertificateList().get(0);
//        XxlJobLogger.log("check failed, driverCode: {}, reason: {}, duplicate certificate: {} {}, ownerUserCode: {}",
//                driver.getDriverCode(), HrmsErrorCodeEnums.DUPLICATE_CERTIFICATE_ERROR.getDesc(),
//                repeatCertificate.getCertificate().getCertificateTypeCode(), repeatCertificate.getCertificate().getCertificateCode(), repeatCertificate.getOwnerUserCode());
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(driver.getDriverCode(), String.format("check failed, driverCode: %s, reason: %s, duplicate certificate: %s %s, ownerUserCode: %s",
                driver.getDriverCode(), HrmsErrorCodeEnums.DUPLICATE_CERTIFICATE_ERROR.getDesc(),
                repeatCertificate.getCertificate().getCertificateTypeCode(), repeatCertificate.getCertificate().getCertificateCode(), repeatCertificate.getOwnerUserCode())));
        return false;
    }

    private List<HrmsCompanyCertificateConfigDO> getDriverCompanyCertificateConfig(EntEmpDriverApiDTO driver, HrmsEntDeptDO oc) {
        List<HrmsCompanyCertificateConfigDO> certificateConfigDOList = Lists.newArrayList();
        CertificateConfigQuery configQuery = new CertificateConfigQuery();
        configQuery.setDriver(1);
        // 用工类型标准化 hrms_company_certificate_config的employee_type并不是标准的用工类型，而是聚合后的类型等同于枚举类中的nature
        configQuery.setEmployeeType(EmploymentTypeEnum.getByCode(driver.getType()).getNature());
        if (oc != null) {
            configQuery.setCountry(oc.getCountry());
            certificateConfigDOList = hrmsCompanyCertificateConfigDao.listConfigByCompanyId(configQuery);
        }
        if (CollectionUtils.isEmpty(certificateConfigDOList)) {
            configQuery.setCountry(BusinessConstant.DEFAULT_COUNTRY);
            certificateConfigDOList = hrmsCompanyCertificateConfigDao.listConfigByCompanyId(configQuery);
            if (CollectionUtils.isEmpty(certificateConfigDOList)) {
                return Lists.newArrayList();
            }
        }
        return certificateConfigDOList;
    }

    /**
     * 检查用户
     */
    private Boolean checkUser(EntEmployeeApiDTO employee, List<ScriptErrorRecordDO> errorRecords) {
        //  只校验启用账号的证件
        if (!StatusEnum.ACTIVE.getCode().equals(employee.getStatus()) || StringUtils.isBlank(employee.getIdCard())) {
            return true;
        }
        List<Certificate> certificateList = Lists.newArrayList(Certificate.builder()
                .certificateTypeCode(CertificateTypeEnum.ID_CARD.getCode())
                .certificateCode(employee.getIdCard())
                .build());
        UserCertificateCheckResultDTO resultDTO = userCertificateService.checkUserCertificateDuplicate(UserCertificateDuplicateCheckParam.builder()
                .certificateList(certificateList)
                .build());
        // 查询必填证件类型
        if (!resultDTO.getIsRepeat()) {
            return true;
        }
        UserCertificateCheckResultDTO.RepeatCertificate repeatCertificate = resultDTO.getRepeatCertificateList().get(0);
//        XxlJobLogger.log("check failed, userCode: {}, reason: {}, duplicate certificate: {} {}, ownerUserCode: {}",
//                employee.getUserCode(), HrmsErrorCodeEnums.DUPLICATE_CERTIFICATE_ERROR.getDesc(),
//                repeatCertificate.getCertificate().getCertificateTypeCode(), repeatCertificate.getCertificate().getCertificateCode(), repeatCertificate.getOwnerUserCode());
        errorRecords.add(ScriptErrorRecordDO.getSynTms2Hrms(employee.getUserCode(), String.format("check failed, userCode: %s, reason: %s, duplicate certificate: %s %s, ownerUserCode: %s",
                employee.getUserCode(), HrmsErrorCodeEnums.DUPLICATE_CERTIFICATE_ERROR.getDesc(),
                repeatCertificate.getCertificate().getCertificateTypeCode(), repeatCertificate.getCertificate().getCertificateCode(), repeatCertificate.getOwnerUserCode())));
        return false;
    }

    @Nullable
    private List<HrmsCompanyCertificateConfigDO> getUserCompanyCertificateConfig(HrmsEntDeptDO oc) {
        CertificateConfigQuery configQuery = new CertificateConfigQuery();
        List<HrmsCompanyCertificateConfigDO> certificateConfigDOList = Lists.newArrayList();
        configQuery.setDriver(0);
        if (oc != null) {
            configQuery.setCountry(oc.getCountry());
            certificateConfigDOList = hrmsCompanyCertificateConfigDao.listConfigByCompanyId(configQuery);
        }
        if (CollectionUtils.isEmpty(certificateConfigDOList)) {
            configQuery.setCountry(BusinessConstant.DEFAULT_COUNTRY);
            certificateConfigDOList = hrmsCompanyCertificateConfigDao.listConfigByCompanyId(configQuery);
            if (CollectionUtils.isEmpty(certificateConfigDOList)) {
                return null;
            }
        }
        return certificateConfigDOList;
    }

    @Data
    public static class SynAccountParam {

        /**
         * 手动调用时的循环次数上线，小于等于50，与其他参数不兼容
         */
        private Integer countLimit;
        /**
         * 只同步干线司机
         */
        private Boolean isOnlyLineHaul;

        private Boolean doSql;

        /**
         * 是否校验
         */
        private Boolean isCheck;

        /**
         * 用户编码
         */
        private List<String> userCodeList;

        private List<String> driverCodeList;

        /**
         * 黑名单
         */
        private List<String> blackList;

    }
}
