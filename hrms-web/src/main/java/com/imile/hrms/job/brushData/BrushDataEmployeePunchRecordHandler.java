package com.imile.hrms.job.brushData;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.punch.dao.EmployeePunchRecordDao;
import com.imile.hrms.dao.punch.model.EmployeePunchRecordDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} BrushDataEmployeePunchRecordHandler
 * {@code @since:} 2024-06-04 18:24
 * {@code @description:}
 */
@Component
@Slf4j
public class BrushDataEmployeePunchRecordHandler {
    @Autowired
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;

    @XxlJob(BusinessConstant.JobHandler.BRUSH_DATA_EMPLOYEE_PUNCH_RECORD_HANDLER)
    public ReturnT<String> brushDataEmployeePunchRecordHandler(String content) {
        BrushDataEmployeePunchRecordHandler.BrushDataEmployeePunchRecordHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, BrushDataEmployeePunchRecordHandler.BrushDataEmployeePunchRecordHandlerParam.class) : new BrushDataEmployeePunchRecordHandler.BrushDataEmployeePunchRecordHandlerParam();
        log.info("刷数据-员工打卡记录处理器开始执行 参数：{}", JSON.toJSONString(param));
        List<String> countryList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(param.getCountry())) {
            countryList = Arrays.asList(param.getCountry().split(","));
        }
        if (ObjectUtil.isEmpty(param.getStartDate())) {
            param.setStartDate("2022-01-01 00:00:00");
        }
        if (ObjectUtil.isEmpty(param.getEndDate())) {
            param.setEndDate("2022-01-01 00:00:00");
        }
        DateTime startDateTime = DateUtil.beginOfDay(DateUtil.parse(param.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
        DateTime endDateTime = DateUtil.beginOfDay(DateUtil.parse(param.getEndDate(), "yyyy-MM-dd HH:mm:ss"));

        log.info("刷数据-员工打卡记录处理器开始执行 参数：{}", JSON.toJSONString(param));

        // 统计需要修改国家的数据
        List<PunchRecordInfoParam> punchRecordInfoList = Lists.newArrayList();

        int currentPage = 1;
        int pageSize = 1000;
        Page<EmployeePunchRecordDO> page = PageHelper.startPage(currentPage, pageSize, true);
        List<String> finalCountryList = countryList;
        PageInfo<EmployeePunchRecordDO> pageInfo = page.doSelectPageInfo(() -> employeePunchRecordDao.selectListByCondition(finalCountryList, startDateTime, endDateTime));
        // 总记录数
        List<EmployeePunchRecordDO> pageEmployeePunchRecordList = pageInfo.getList();
        if (CollUtil.isNotEmpty(pageEmployeePunchRecordList)) {
            log.info("brushDataApplicationFormHandler currentPage：{} totalPages：{} totalRecords：{}", currentPage, pageInfo.getPages(), pageInfo.getTotal());
            handlerEmployeePunchRecord(pageEmployeePunchRecordList, param, punchRecordInfoList);
        }
        log.info("brushDataApplicationFormHandler | currentPage:{},pageSize:{},total:{}", currentPage, pageSize, pageInfo.getTotal());

        log.info("brushDataApplicationFormHandler currentPage {} pages：{}", currentPage, pageInfo.getPages());


        while (currentPage < pageInfo.getPages()) {
            log.info("进入while循环");
            currentPage++;
            log.info("currentPage：{} pages：{}", currentPage, pageInfo.getPages());

            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> employeePunchRecordDao.selectListByCondition(finalCountryList, startDateTime, endDateTime));
            pageEmployeePunchRecordList = pageInfo.getList();
            if (CollUtil.isNotEmpty(pageEmployeePunchRecordList)) {
                log.info("while循环 pageApplicationForm size：{} pageApplicationForm：{}", pageEmployeePunchRecordList.size(), JSON.toJSONString(pageEmployeePunchRecordList));
                handlerEmployeePunchRecord(pageEmployeePunchRecordList, param, punchRecordInfoList);
            }
            log.info("while循环：brushDataApplicationFormHandler | currentPage:{},pageSize:{},total:{}", currentPage, pageSize, pageInfo.getTotal());
            log.info("brushDataApplicationFormHandler,currentPage {} while循环结束", currentPage);
        }

        log.info("punchRecordInfoList size {}", punchRecordInfoList.size());

        return ReturnT.SUCCESS;
    }

    /**
     * 处理员工打卡记录
     *
     * @param pageEmployeePunchRecordList 打卡记录
     * @param param                       参数
     */
    private void handlerEmployeePunchRecord(List<EmployeePunchRecordDO> pageEmployeePunchRecordList, BrushDataEmployeePunchRecordHandlerParam param, List<PunchRecordInfoParam> punchRecordInfoList) {
        if (CollUtil.isEmpty(pageEmployeePunchRecordList)) {
            log.info("handlerApplicationForm pageApplicationForm empty");
            return;
        }
        List<String> userCodeList = pageEmployeePunchRecordList.stream().filter(e -> ObjectUtil.isNotNull(e.getUserCode())).map(EmployeePunchRecordDO::getUserCode).collect(Collectors.toList());

        UserDaoQuery query = UserDaoQuery.builder().userCodes(userCodeList).build();

        List<HrmsUserInfoDO> userInfoList = hrmsUserInfoDao.userList(query);
        // 将userInfoList转换为Map
        Map<String, HrmsUserInfoDO> userInfoMap = userInfoList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, Function.identity()));

        List<EmployeePunchRecordDO> targetEmployeePunchRecordList = Lists.newArrayList();
        List<PunchRecordInfoParam> infoList = Lists.newArrayList();

        for (EmployeePunchRecordDO punchRecord : pageEmployeePunchRecordList) {
            String userCode = punchRecord.getUserCode();
            String country = punchRecord.getCountry();
            if (ObjectUtil.isNull(country)) {
                log.info("punchRecord country is null, punchRecord:{}", JSON.toJSONString(punchRecord));
                continue;
            }
            log.info("punchRecord userCode:{},country:{}", userCode, country);
            HrmsUserInfoDO userInfo = userInfoMap.get(userCode);
            if (ObjectUtil.isNull(userInfo)) {
                log.info("punchRecord userInfo is null userCode：{}", userCode);
                continue;
            }
            log.info("punchRecord userInfo：userCode：{}, origin_country ：{}  location_country ：{}", userCode, ObjectUtil.isNull(userInfo.getOriginCountry()) ? "null" : userInfo.getOriginCountry(), userInfo.getLocationCountry());
            if (ObjectUtil.equal(country, userInfo.getLocationCountry())) {
                log.info("punchRecord userInfo location_country equal punchRecord country userCode：{}", userCode);
                continue;
            }
            log.info("punchRecord userInfo location_country not equal punchRecord country userCode：{} from country：{}, userInfo location_country：{}", userCode, country, userInfo.getLocationCountry());

            PunchRecordInfoParam infoParam = new PunchRecordInfoParam();
            infoParam.setUserCode(userCode);
            infoParam.setFormCountry(country);
            infoParam.setUserInfoOriginCountry(userInfo.getOriginCountry());
            infoParam.setUserInfoLocationCountry(userInfo.getLocationCountry());
            infoList.add(infoParam);
            log.info("punchRecord infoParam：{}", JSON.toJSONString(infoParam));
            // 更新申请单据的国家
            punchRecord.setCountry(userInfo.getLocationCountry());
            targetEmployeePunchRecordList.add(punchRecord);
        }
        punchRecordInfoList.addAll(infoList);
        log.info("punchRecord targetEmployeePunchRecordList size：{}", targetEmployeePunchRecordList.size());
        log.info("punchRecord infoList size：{} infoList：{}", infoList.size(), JSON.toJSONString(infoList));
        if (CollUtil.isNotEmpty(targetEmployeePunchRecordList)) {
            log.info("targetEmployeePunchRecordList start update targetEmployeePunchRecordList size：{} targetEmployeePunchRecordList：{}", targetEmployeePunchRecordList.size(), JSON.toJSONString(targetEmployeePunchRecordList));
            if (ObjectUtil.equal(param.getFlag(), "true")) {
                employeePunchRecordDao.updateBatchById(targetEmployeePunchRecordList);
            }
        }
    }

    @Data
    private static class PunchRecordInfoParam {
        /**
         * 用户id
         */
        private String userCode;
        /**
         * 申请单国家
         */
        private String formCountry;
        /**
         * 用户信息核算组织
         */
        private String userInfoOriginCountry;
        /**
         * 用户信息常驻国
         */
        private String userInfoLocationCountry;
    }

    /**
     * 刷数据-申请单据处理器参数
     */
    @Data
    private static class BrushDataEmployeePunchRecordHandlerParam {
        /**
         * 是否落库标识：默认false 不落库
         */
        private String flag = "false";

        /**
         * 国家
         */
        private String country;

        /**
         * 开始时间
         */
        private String startDate;

        /**
         * 结束时间
         */
        private String endDate;
    }
}
