package com.imile.hrms.job.leave;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.service.attendance.UserLeaveInspectionService;
import com.imile.hrms.service.attendance.param.UserLeaveInspectionNewHandlerParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * {@code @author:} allen
 * {@code @className:} userLeaveInspectionNewHandler
 * {@code @since:} 2023-12-21 17:53
 * {@code @description:} 人员假期巡检处理器
 */
@Slf4j
@Component
public class UserLeaveInspectionNewHandler {
    @Autowired
    private UserLeaveInspectionService userLeaveInspectionService;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_INSPECTION_NEW_HANDLER)
    public ReturnT<String> userLeaveInspectionNewHandler(String content) {
        UserLeaveInspectionNewHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, UserLeaveInspectionNewHandlerParam.class) : new UserLeaveInspectionNewHandlerParam();
        userLeaveInspectionService.userLeaveInspectionHandler(param);
        return ReturnT.SUCCESS;
    }
}
