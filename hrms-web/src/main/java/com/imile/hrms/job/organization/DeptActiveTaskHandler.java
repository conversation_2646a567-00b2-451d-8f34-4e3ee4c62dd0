package com.imile.hrms.job.organization;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.organization.OrganizationStatusSwitchParam;
import com.imile.hermes.enterprise.query.SyncHrDeptInfoApiQuery;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.constants.KingdeeConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.enums.RelationBizTypeEnum;
import com.imile.hrms.common.enums.organization.DeptActiveStatusEnum;
import com.imile.hrms.common.enums.organization.DeptActiveTypeEnum;
import com.imile.hrms.common.enums.organization.DeptStatusEnum;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.organization.dao.HrmsDeptActiveTaskDao;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsDeptActiveTaskDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.query.EntDeptQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.genesis.RpcOrganizationService;
import com.imile.hrms.integration.hermes.service.EntDeptService;
import com.imile.hrms.integration.kingdee.KingdeeIntegration;
import com.imile.hrms.manage.base.HrmsPlatformRelationManage;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.mq.HrMqEventConstant;
import com.imile.hrms.mq.basic.ProducerBasicService;
import com.imile.hrms.service.organization.param.HrmsEntDeptSyncParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/3
 */
@Component
@Slf4j
public class DeptActiveTaskHandler {

    @Resource
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsDeptNewManage hrmsDeptNewManage;
    @Resource
    private HrmsDeptActiveTaskDao hrmsDeptActiveTaskDao;
    @Resource
    protected LogRecord logRecord;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private ProducerBasicService producerBasicService;
    @Resource
    private EntDeptService entDeptService;
    @Resource
    private KingdeeIntegration kingdeeIntegration;
    @Resource
    private HrmsPlatformRelationManage hrmsPlatformRelationManage;
    @Resource
    private RpcOrganizationService rpcOrganizationService;

    @Value("${rocket.mq.hr.topic}")
    private String hrProducerTopic;

    public static final String JOB_NAME = BusinessConstant.JobHandler.DEPT_SNAPSHOT_HANDLER;

    @XxlJob(BusinessConstant.JobHandler.DEPT_ACTIVE_TASK_HANDLER)
    public ReturnT<String> deptActiveTaskHandler(String param) {
        XxlJobLogger.log("{} 开始执行,param:{}", JOB_NAME, param);

        long startTime = System.currentTimeMillis();
        // 设置登录信息
        setLoginInfo();

        if (!StringUtils.isBlank(param)) {
            DeptActiveTaskHandler.deptActiveTaskHandlerParam activeTaskHandlerParam = JSON.parseObject(param, DeptActiveTaskHandler.deptActiveTaskHandlerParam.class);
            // 需要生效的部门
            List<Long> taskIds = activeTaskHandlerParam.getTaskIds();
            if (CollectionUtil.isEmpty(taskIds)) {
                return ReturnT.SUCCESS;
            }
            LambdaQueryWrapper<HrmsDeptActiveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HrmsDeptActiveTaskDO::getIsDelete, BusinessConstant.N);
            queryWrapper.in(HrmsDeptActiveTaskDO::getId, taskIds);
            List<HrmsDeptActiveTaskDO> taskList = hrmsDeptActiveTaskDao.list(queryWrapper);
            if (CollectionUtil.isEmpty(taskList)) {
                return ReturnT.SUCCESS;
            }
            doProcess(taskList);
        } else {
            // 自动查询
            // 获取待生效任务: activeStatus = waitActive, activeType = all，triggerTime <= currentTime
            LambdaQueryWrapper<HrmsDeptActiveTaskDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HrmsDeptActiveTaskDO::getIsDelete, BusinessConstant.N);
            queryWrapper.le(HrmsDeptActiveTaskDO::getTriggerTime, new Date());
            queryWrapper.eq(HrmsDeptActiveTaskDO::getActiveStatus, DeptActiveStatusEnum.WAIT_ACTIVE.getCode());
            // 按触发时间排序
            queryWrapper.orderByAsc(HrmsDeptActiveTaskDO::getTriggerTime);
            queryWrapper.last("limit 20");
            List<HrmsDeptActiveTaskDO> taskList = hrmsDeptActiveTaskDao.list(queryWrapper);
            if (CollectionUtil.isEmpty(taskList)) {
                return ReturnT.SUCCESS;
            }
            doProcess(taskList);
        }
        return returnSuccess(startTime);
    }

    private void doProcess(List<HrmsDeptActiveTaskDO> taskList) {
        // 根据deptIds，获取部门
        Date now = new Date();
        List<Long> deptIds = taskList.stream().map(HrmsDeptActiveTaskDO::getDeptId).collect(Collectors.toList());
        List<HrmsEntDeptDO> deptList = hrmsDeptNewManage.getDeptListByIdList(deptIds);
        if (CollectionUtil.isEmpty(deptList)) {
            return;
        }
        Map<Long, HrmsEntDeptDO> deptMap = deptList.stream().collect(Collectors.toMap(HrmsEntDeptDO::getId, Function.identity(), (o1, o2) -> o1));
        // 下级部门
        EntDeptQuery query = new EntDeptQuery();
        query.setParentIdList(deptIds);
        query.setStatus(DeptStatusEnum.ACTIVE.getCode());
        List<HrmsEntDeptDO> childrenDeptList = hrmsDeptNewManage.getSubDeptFromParentIdList(query);
        Map<Long, List<HrmsEntDeptDO>> childrenDeptMap = childrenDeptList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getParentId));
        List<HrmsUserInfoDO> userList = hrmsUserInfoDao.getOnJobUserByDeptIdList(deptIds);
        Map<Long, List<HrmsUserInfoDO>> userMap = userList.stream().collect(Collectors.groupingBy(HrmsUserInfoDO::getDeptId));
        // 更新生效记录, 不满足的任务记录失败原因
        List<HrmsDeptActiveTaskDO> failedList = Lists.newArrayList();
        for (HrmsDeptActiveTaskDO task : taskList) {
            setLoginInfoByCreateUser(task);
            // 判断部门是否存在、是否满足生效要求
            // activeType = active、add: 满足启用条件，disabled: 满足停用条件
            if (!deptMap.containsKey(task.getDeptId())) {
                addCommonErrorRecord(task.getId());
                continue;
            }
            HrmsEntDeptDO dept = deptMap.get(task.getDeptId());
            if (DeptActiveTypeEnum.DISABLED.getCode().equals(task.getActiveType())) {
                // 停用
                // 不满足下级组织
                Boolean hasChildren = childrenDeptMap.containsKey(dept.getId()) && CollectionUtils.isNotEmpty(childrenDeptMap.get(dept.getId()));
                // 不满足人员
                Boolean hasUser = userMap.containsKey(dept.getId()) && CollectionUtils.isNotEmpty(userMap.get(dept.getId()));
                HrmsErrorCodeEnums errCode = getDisabledTypeErrorCode(hasChildren, hasUser);
                if (errCode != null) {
                    addCheckDisabledFailedRecord(task.getId(), errCode);
                    continue;
                }
                // 满足：更新部门状态、生效时间/停用时间、同步下游系统
                doSwitchStatus(task.getId(), dept, DeptStatusEnum.DISABLED.getCode(), now, failedList);
            } else if (DeptActiveTypeEnum.ACTIVE.getCode().equals(task.getActiveType())) {
                // 启用
                // 不满足上级组织,
                List<HrmsEntDeptDO> parentDept = hrmsDeptNewManage.getDeptListByIdList(Lists.newArrayList(dept.getParentId()));
                if (CollectionUtils.isNotEmpty(parentDept)) {
                    HrmsEntDeptDO parent = parentDept.get(0);
                    if (!DeptStatusEnum.ACTIVE.getCode().equals(parent.getStatus())) {
                        addCheckActiveOrAddFailedRecord(task.getId());
                        continue;
                    }
                }
                // 满足：更新部门状态、生效时间/停用时间、同步下游系统
                doSwitchStatus(task.getId(), dept, DeptStatusEnum.ACTIVE.getCode(), now, failedList);
            } else if (DeptActiveTypeEnum.ADD.getCode().equals(task.getActiveType())) {
                // 新增
                // 不满足上级组织，必须现查，由于新增时支持选择未生效部门作为上级，这里触发时需要保证上级部门已启用
                List<HrmsEntDeptDO> parentDept = hrmsDeptNewManage.getDeptListByIdList(Lists.newArrayList(dept.getParentId()));
                if (CollectionUtils.isNotEmpty(parentDept)) {
                    HrmsEntDeptDO parent = parentDept.get(0);
                    if (!DeptStatusEnum.ACTIVE.getCode().equals(parent.getStatus())) {
                        addCheckActiveOrAddFailedRecord(task.getId());
                        continue;
                    }
                }
                doAddDept(task.getId(), dept, now, failedList);
            }
        }
        // 统一处理sql执行或其他原因造成的触发失败
        if (CollectionUtils.isNotEmpty(failedList)) {
            setLoginInfo();
            hrmsDeptActiveTaskDao.updateBatchById(failedList);
        }
    }

    private static HrmsErrorCodeEnums getDisabledTypeErrorCode(Boolean hasChildren, Boolean hasUser) {
        HrmsErrorCodeEnums errCode;
        if (hasChildren && hasUser) {
            errCode = HrmsErrorCodeEnums.DEPT_CANT_DISABLED_1;
        } else if (hasChildren) {
            errCode = HrmsErrorCodeEnums.DEPT_CANT_DISABLED_2;
        } else if (hasUser){
            errCode = HrmsErrorCodeEnums.DEPT_CANT_DISABLED_3;
        } else {
            errCode = null;
        }
        return errCode;
    }

    private void addCheckActiveOrAddFailedRecord(Long taskId) {
        HrmsDeptActiveTaskDO taskRecord = new HrmsDeptActiveTaskDO();
        taskRecord.setId(taskId);
        BaseDOUtil.fillDOUpdate(taskRecord);
        taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE_ERROR.getCode());
        taskRecord.setErrMsg(HrmsErrorCodeEnums.DEPT_CANT_ACTIVE.getCode());
        hrmsDeptActiveTaskDao.updateById(taskRecord);
    }

    private void addCheckDisabledFailedRecord(Long taskId, HrmsErrorCodeEnums errorCode) {
        HrmsDeptActiveTaskDO taskRecord = new HrmsDeptActiveTaskDO();
        taskRecord.setId(taskId);
        BaseDOUtil.fillDOUpdate(taskRecord);
        taskRecord.setErrMsg(errorCode.getCode());
        taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE_ERROR.getCode());
        hrmsDeptActiveTaskDao.updateById(taskRecord);
    }

    private void addCommonErrorRecord(Long taskId) {
        HrmsDeptActiveTaskDO taskRecord = new HrmsDeptActiveTaskDO();
        taskRecord.setId(taskId);
        BaseDOUtil.fillDOUpdate(taskRecord);
        taskRecord.setErrMsg(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode());
        taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE_ERROR.getCode());
        hrmsDeptActiveTaskDao.updateById(taskRecord);
    }

    private void doAddDept(Long taskId, HrmsEntDeptDO dept, Date now, List<HrmsDeptActiveTaskDO> failedList) {
        HrmsEntDeptDO record = new HrmsEntDeptDO();
        record.setId(dept.getId());
        record.setStatus(DeptStatusEnum.ACTIVE.getCode());
        record.setRecentActiveTime(now);
        BaseDOUtil.fillDOUpdate(record);
        HrmsDeptActiveTaskDO taskRecord = new HrmsDeptActiveTaskDO();
        taskRecord.setId(taskId);
        BaseDOUtil.fillDOUpdate(taskRecord);
        taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE.getCode());
        transactionTemplate.executeWithoutResult(status -> {
            try {
                // 更新记录表
                hrmsDeptActiveTaskDao.updateById(taskRecord);
                // 在更新业务库部门状态
                hrmsEntDeptDao.updateById(record);
                // 将部门同步至下游系统
                producerBasicService.sendMessage(hrProducerTopic, HrMqEventConstant.SYNC_DEPT_TO_DOWNSTREAM,
                        dept.getId().toString(),
                        HrmsEntDeptSyncParam.builder().id(dept.getId()).build());
                // 日志记录
            } catch (Exception e) {
                log.error("deptActiveTaskHandler 停启用部门失败", e);
                status.setRollbackOnly();
                taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE_ERROR.getCode());
                taskRecord.setErrMsg(JSON.toJSONString(e));
                BaseDOUtil.fillDOUpdate(taskRecord);
                failedList.add(taskRecord);
            }
        });

    }

    private void doSwitchStatus(Long taskId, HrmsEntDeptDO dept, String deptStatus, Date now, List<HrmsDeptActiveTaskDO> failedList) {
        HrmsEntDeptDO record = new HrmsEntDeptDO();
        record.setId(dept.getId());
        record.setStatus(deptStatus);
        if (DeptStatusEnum.ACTIVE.getCode().equals(deptStatus)) {
            record.setRecentActiveTime(now);
        } else if (DeptStatusEnum.DISABLED.getCode().equals(deptStatus)) {
            record.setRecentDisabledTime(now);
        }
        BaseDOUtil.fillDOUpdate(record);
        HrmsDeptActiveTaskDO taskRecord = new HrmsDeptActiveTaskDO();
        taskRecord.setId(taskId);
        BaseDOUtil.fillDOUpdate(taskRecord);
        taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE.getCode());
        transactionTemplate.executeWithoutResult(status -> {
            try {
                // 更新记录表
                hrmsDeptActiveTaskDao.updateById(taskRecord);
                // 先更新主数据组织状态
                switchOrganizationStatus(dept.getDeptCode(), deptStatus);
                // 在更新业务库部门状态
                hrmsEntDeptDao.updateById(record);
                doBusinessAfterDeptStatusSwitch(deptStatus, dept.getId(), dept.getDeptCode());
                // 日志记录
                logRecord.diffObj(record, dept, OperationTypeEnum.DEPT_SWITCH_STATUS.getCode());
            } catch (Exception e) {
                log.error("deptActiveTaskHandler 停启用部门失败", e);
                status.setRollbackOnly();
                taskRecord.setActiveStatus(DeptActiveStatusEnum.ACTIVE_ERROR.getCode());
                taskRecord.setErrMsg(JSON.toJSONString(e));
                BaseDOUtil.fillDOUpdate(taskRecord);
                failedList.add(taskRecord);
            }
        });
    }


    private void doBusinessAfterDeptStatusSwitch(String status, Long deptId, String deptCode) {
        // 同步hermes及金蝶
        this.syncDeptStatus2Hermes(deptCode, status);
        kingdeeIntegration.noAuditAndStatusSwitch(String.valueOf(deptId),
                RelationBizTypeEnum.KINGDEE_DEPT.getCode(), "",
                BusinessConstant.PLAT_FORM, status, KingdeeConstant.BusKey.DEPT_KEY);
        hrmsPlatformRelationManage.syncDeptStatus2Wecom(deptId, status);
    }

    private void syncDeptStatus2Hermes(String deptCode, String status) {
        SyncHrDeptInfoApiQuery apiQuery = new SyncHrDeptInfoApiQuery();
        apiQuery.setDepCode(deptCode);
        apiQuery.setStatus(status);
        entDeptService.syncHrDeptStatus(Lists.newArrayList(apiQuery));
    }

    private void switchOrganizationStatus(String deptCode, String status) {
        OrganizationStatusSwitchParam param = new OrganizationStatusSwitchParam();
        param.setOrganizationCode(deptCode);
        param.setOrganizationStatus(status);
        param.setOperationScene(OperationSceneEnum.HRMS_DEPT_MANAGE);
        try {
            rpcOrganizationService.switchOrganizationStatus(param);
        } catch (Exception e) {
            log.error("停启用组织失败,deptCode:{},status:{}", deptCode, status, e);
        }
    }

    private static void setLoginInfo() {
        UserContext userContext = new UserContext();
        userContext.setUserCode("XXL-JOB");
        userContext.setUserName("XXL-JOB");
        userContext.setOrgId(10L);
        RequestInfoHolder.setLoginInfo(userContext);
    }

    private static void setLoginInfoByCreateUser(HrmsDeptActiveTaskDO task) {
        if (task == null) {
            setLoginInfo();
            return;
        }
        UserContext userContext = new UserContext();
        userContext.setUserCode(task.getCreateUserCode());
        userContext.setUserName(task.getCreateUserName());
        userContext.setOrgId(10L);
        RequestInfoHolder.setLoginInfo(userContext);
    }

    private ReturnT<String> returnSuccess(long startTime) {
        long endTime = System.currentTimeMillis();
        XxlJobLogger.log("{} 执行完毕,总耗时:{}ms", JOB_NAME, endTime - startTime);
        return ReturnT.SUCCESS;
    }

    @Data
    public static class deptActiveTaskHandlerParam {

        /**
         * 任务 ids
         */
        private List<Long> taskIds;

    }
}
