package com.imile.hrms.job.salary;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.salary.SalaryReportInfoEnum;
import com.imile.hrms.dao.salary.model.HrmsSalaryReportConfigDO;
import com.imile.hrms.dao.salary.query.SalaryReportConfigQuery;
import com.imile.hrms.manage.salary.HrmsSalaryReportConfigManage;
import com.imile.hrms.service.salary.dto.SalaryReportConfigInfoDTO;
import com.imile.util.BeanUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/4/10 17:20
 * @version: 1.0
 */
@Slf4j
@Component
public class SalaryReportResidentLocationHandler {

    @Autowired
    private HrmsSalaryReportConfigManage hrmsSalaryReportConfigManage;

    @XxlJob(BusinessConstant.JobHandler.SALARY_REPORT_RESIDENT_LOCATION_HANDLER)
    public ReturnT<String> SalaryReportResidentLocationHandler(String content) {
        SalaryReportResidentLocationHandler.SalaryReportResidentLocationHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, SalaryReportResidentLocationHandler.SalaryReportResidentLocationHandlerParam.class) : new SalaryReportResidentLocationHandler.SalaryReportResidentLocationHandlerParam();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            paramCountryList = Arrays.asList(param.getCountryList().split(","));
        }

        SalaryReportConfigQuery salaryReportConfigQuery = new SalaryReportConfigQuery();
        salaryReportConfigQuery.setIsLatest(BusinessConstant.Y);
        salaryReportConfigQuery.setType(param.getType());
        salaryReportConfigQuery.setCountryList(paramCountryList);
        List<HrmsSalaryReportConfigDO> salaryReportConfigDOList = hrmsSalaryReportConfigManage.selectReportConfigList(salaryReportConfigQuery);
        List<HrmsSalaryReportConfigDO> updateList = new ArrayList<>();
        for (HrmsSalaryReportConfigDO reportConfigDO : salaryReportConfigDOList) {
            if (StringUtils.isBlank(reportConfigDO.getReportInfo())) {
                continue;
            }
            List<SalaryReportConfigInfoDTO> salaryReportConfigInfoDTOList = JSON.parseArray(reportConfigDO.getReportInfo(), SalaryReportConfigInfoDTO.class);
            List<String> codeList = salaryReportConfigInfoDTOList.stream()
                    .map(SalaryReportConfigInfoDTO::getCode)
                    .collect(Collectors.toList());
            if (codeList.contains(SalaryReportInfoEnum.RESIDENT_LOCATION.getCode())) {
                continue;
            }
            List<SalaryReportConfigInfoDTO> filterSalaryReportConfigInfoDTOList = new ArrayList<>();
            for (SalaryReportConfigInfoDTO configInfoDTO : salaryReportConfigInfoDTOList) {
                SalaryReportConfigInfoDTO reportConfigInfoDTO = BeanUtils.convert(configInfoDTO, SalaryReportConfigInfoDTO.class);
                filterSalaryReportConfigInfoDTOList.add(reportConfigInfoDTO);
                if (StringUtils.equalsIgnoreCase(configInfoDTO.getCode(), SalaryReportInfoEnum.ORIGIN_COUNTRY.getCode())) {
                    SalaryReportConfigInfoDTO residentLocationDTO = BeanUtils.convert(configInfoDTO, SalaryReportConfigInfoDTO.class);
                    residentLocationDTO.setCode(SalaryReportInfoEnum.RESIDENT_LOCATION.getCode());
                    filterSalaryReportConfigInfoDTOList.add(residentLocationDTO);
                }
            }
            int sort = 1;
            for (SalaryReportConfigInfoDTO reportConfigInfoDTO : filterSalaryReportConfigInfoDTOList) {
                reportConfigInfoDTO.setSort(sort);
                sort++;
            }
            reportConfigDO.setReportInfo(JSON.toJSONString(filterSalaryReportConfigInfoDTOList));
            BaseDOUtil.fillDOUpdate(reportConfigDO);
            updateList.add(reportConfigDO);
        }
        hrmsSalaryReportConfigManage.updateBatch(updateList);
        return ReturnT.SUCCESS;
    }


    @Data
    private static class SalaryReportResidentLocationHandlerParam {
        /**
         * 国家
         */
        private String countryList;

        /**
         * 报表类型
         */
        private String type;
    }
}
