package com.imile.hrms.job.leave;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.leave.CompanyLeaveTypeEnum;
import com.imile.hrms.common.enums.leave.LeaveTypeEnum;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntCompanyDao;
import com.imile.hrms.dao.user.dao.HrmsUserEntryRecordDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.mapper.HrmsUserLeaveRecordMapper;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveItemConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveDetailDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.dao.user.query.CompanyLeaveQuery;
import com.imile.hrms.dao.user.query.UserLeaveDetailQuery;
import com.imile.hrms.manage.company.HrmsCompanyLeaveConfigManage;
import com.imile.hrms.manage.user.HrmsUserLeaveDetailManage;
import com.imile.hrms.manage.user.HrmsUserLeaveStageDetailManage;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.hrms.common.entity.BaseDOUtil.fillDOInsert;
import static com.imile.hrms.common.entity.BaseDOUtil.fillDOUpdate;

/**
 * 员工年假处理类
 *
 * <AUTHOR>
 * @date 2022/05/30
 */
@Slf4j
@Component
public class UserAnnualLeaveHandler {

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsEntCompanyDao hrmsEntCompanyDao;
    @Autowired
    private HrmsUserEntryRecordDao userEntryRecordDao;
    @Autowired
    private HrmsUserLeaveDetailManage userLeaveDetailManage;
    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;
    @Autowired
    private HrmsUserLeaveRecordMapper hrmsUserLeaveRecordMapper;
    @Autowired
    private HrmsCompanyLeaveConfigManage companyLeaveConfigManage;
    @Autowired
    private HrmsUserLeaveStageDetailManage hrmsUserLeaveStageDetailManage;
    @Resource
    private EntDeptNewService entDeptNewService;
    private static Date DEFAULT_DATE;

    @XxlJob(BusinessConstant.JobHandler.USER_ANNUAL_LEAVE_HANDLER)
    public ReturnT<String> userAnnualLeaveHandler(String content) throws ParseException {
        UserAnnualLeaveHandler.UserAnnualLeaveParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, UserAnnualLeaveHandler.UserAnnualLeaveParam.class) : new UserAnnualLeaveHandler.UserAnnualLeaveParam();

        DEFAULT_DATE = DateUtil.beginOfDay(new Date());
        if (StringUtils.isNotBlank(param.getDateNow())) {
            DEFAULT_DATE = DateUtil.beginOfDay(DateUtil.parse(param.getDateNow(), "yyyy-MM-dd HH:mm:ss"));
        }
        XxlJobLogger.log("当前时间:{}", DEFAULT_DATE);

        List<String> userCodeList = new ArrayList<>();
        List<String> employeeTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            userCodeList = Arrays.asList(param.getUserCodes().split(","));
        }
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            employeeTypeList = Arrays.asList(param.getEmployeeType().split(","));
        }
        List<String> countryList = entDeptNewService.getCountryList(param.getCountryList());

        // 用户code 与 特殊国家绑定，必须都不为空，才能走测试逻辑
        if (CollUtil.isNotEmpty(userCodeList) && CollUtil.isNotEmpty(countryList)) {
            //只有正式员工，正式司机才发送年假
            LambdaQueryWrapper<HrmsUserInfoDO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
            queryWrapper.in(HrmsUserInfoDO::getUserCode, userCodeList);
            queryWrapper.in(HrmsUserInfoDO::getWorkStatus, Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()));
            queryWrapper.eq(HrmsUserInfoDO::getStatus, StatusEnum.ACTIVE.getCode());
            //queryWrapper.in(HrmsUserInfoDO::getEmployeeType, Arrays.asList(EmployeeTypeEnum.INTERNAL.getCode(), EmployeeTypeEnum.Sub_Formal.getCode(), DriverTypeEnum.EMPLOYEE.getCode(), DriverTypeEnum.SUB_EMPLOYEE.getCode()));
            queryWrapper.in(HrmsUserInfoDO::getEmployeeType, employeeTypeList);
            List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.list(queryWrapper);
            XxlJobLogger.log("查询到的员工个数为:{}", userInfoDOList.size());
            // 该方法只给配置国家：KSA、UAE、HQ国家员工发年假，其他国家员工不发年假 【KSA：入职日发放2.5年假。HQ、UAE：每月1号发放2.5年假】
            userLeaveHandler(userInfoDOList, param);

            // 每年一月一号执行下面的逻辑，给非KSA、UAE、HQ国家员工发年假
            if (DateUtil.month(DEFAULT_DATE) + 1 == 1 && DateUtil.dayOfMonth(DEFAULT_DATE) == 1) {
                otherUserLeaveHandler(countryList, employeeTypeList, userCodeList);

            }
            return ReturnT.SUCCESS;
        }

        //if (CollectionUtils.isEmpty(countryList)) {
        //    return ReturnT.SUCCESS;
        //}
        // countryList不为空：说明有特殊国家
        if (CollUtil.isNotEmpty(countryList)) {
            // 特殊国家：KSA、UAE、HQ
            for (String country : countryList) {
                LambdaQueryWrapper<HrmsUserInfoDO> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(HrmsUserInfoDO::getLocationCountry, country);
                queryWrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
                queryWrapper.in(HrmsUserInfoDO::getWorkStatus, Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()));
                queryWrapper.eq(HrmsUserInfoDO::getStatus, StatusEnum.ACTIVE.getCode());
                queryWrapper.in(HrmsUserInfoDO::getEmployeeType, employeeTypeList);
                List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.list(queryWrapper);
                XxlJobLogger.log("查询到的员工个数为:{}", userInfoDOList.size());
                userLeaveHandler(userInfoDOList, param);
            }
        }

        // countryList为空：说明没有特殊国家，所有国家都按照下面逻辑执行

        // 每年一月一号执行下面的逻辑，给非KSA、UAE、HQ国家员工发年假
        if (DateUtil.month(DEFAULT_DATE) + 1 == 1 && DateUtil.dayOfMonth(DEFAULT_DATE) == 1) {
            XxlJobLogger.log("当前时间:{},开始执行非KSA、UAE、HQ国家员工年假逻辑", DEFAULT_DATE);
            // 创建userCodeList空集合
            List<String> userCodeInfoList = Lists.newArrayList();
            otherUserLeaveHandler(countryList, employeeTypeList, userCodeInfoList);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 每年一月一号执行下面的逻辑，给非KSA、UAE、HQ国家员工发年假
     *
     * @param countryList      需要排除的国家--->KSA、UAE、HQ【通过xxljob配置获取】
     * @param employeeTypeList 需要查询人员的用工类型【通过xxljob配置获取】
     * @param userCodeList   需要查询人员的userCode【通过xxljob配置获取】
     */
    private void otherUserLeaveHandler(List<String> countryList, List<String> employeeTypeList, List<String> userCodeList) {
        // 查询非KSA、UAE、HQ国家所有员工，根据国家假期设置配置发年假
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());
        companyLeaveQuery.setLeaveType(CompanyLeaveTypeEnum.ANNUAL_LEAVE.getCode());
        // 获取有年假类型的国家的假期配置
        List<HrmsCompanyLeaveConfigDO> allCompanyLeaveConfigList = companyLeaveConfigManage.selectCompanyLeave(companyLeaveQuery);
        // 获取非KSA、UAE、HQ国家的年假类型的假期配置
        List<HrmsCompanyLeaveConfigDO> allCompanyLeaveConfigFilterList =
                allCompanyLeaveConfigList.stream().filter(o -> !countryList.contains(o.getCountry())).collect(Collectors.toList());

        // 如果存在有年假类型的非KSA、UAE、HQ国家的假期配置
        if (CollUtil.isNotEmpty(allCompanyLeaveConfigFilterList)) {
            // 获取所有国家【非KSA、UAE、HQ国家】假期配置的主键id
            List<Long> allCompanyLeaveConfigIdList = allCompanyLeaveConfigFilterList.stream().map(HrmsCompanyLeaveConfigDO::getId).collect(Collectors.toList());

            // 获取所有国家【非KSA、UAE、HQ国家】假期配置下的假期详情
            List<HrmsCompanyLeaveItemConfigDO> allCompanyLeaveItemConfigList = companyLeaveConfigManage.selectItemByConfigId(allCompanyLeaveConfigIdList);

            // 将所有国家【非KSA、UAE、HQ国家】假期配置根据国家分组
            Map<String, List<HrmsCompanyLeaveConfigDO>> allCountryToCompanyLeaveConfigMap = allCompanyLeaveConfigFilterList.stream().collect(Collectors.groupingBy(HrmsCompanyLeaveConfigDO::getCountry));

            // 新增用户假期集合
            List<HrmsUserLeaveDetailDO> addUserLeaveDetailList = Lists.newArrayList();
            // 新增用户假期详情集合
            List<HrmsUserLeaveStageDetailDO> addUserLeaveStageDetailList = Lists.newArrayList();
            // 新增用户假期操作记录集合
            List<HrmsUserLeaveRecordDO> addUserLeaveRecordList = Lists.newArrayList();
            // 更新用户假期详情集合
            List<HrmsUserLeaveStageDetailDO> updateUserLeaveStageDetailList = Lists.newArrayList();
            // 更新用户假期集合
            List<HrmsUserLeaveDetailDO> updateUserLeaveDetailDOList = new ArrayList<>();
            // 遍历每一个国家的假期方案
            allCountryToCompanyLeaveConfigMap.forEach((country, companyLeaveConfigList) -> {

                // 获取指定国家下所有人员应该获取的假期
                List<HrmsCompanyLeaveConfigDO> allGetLeave = companyLeaveConfigList.stream().filter(o -> o.getUseSex() == 0).collect(Collectors.toList());
                // 获取指定国家下男性人员应该获取的假期
                List<HrmsCompanyLeaveConfigDO> maleGetLeave = companyLeaveConfigList.stream().filter(o -> o.getUseSex() == 1).collect(Collectors.toList());
                // 获取指定国家下女性人员应该获取的假期
                List<HrmsCompanyLeaveConfigDO> femaleGetLeave = companyLeaveConfigList.stream().filter(o -> o.getUseSex() == 2).collect(Collectors.toList());

                // 获取该国家假期配置id集合
                List<Long> leaveConfigIdList = companyLeaveConfigList.stream().map(HrmsCompanyLeaveConfigDO::getId).collect(Collectors.toList());
                // 获取该国家下的假期配置详情
                List<HrmsCompanyLeaveItemConfigDO> companyLeaveItemConfigInfo = allCompanyLeaveItemConfigList.stream().filter(o -> leaveConfigIdList.contains(o.getLeaveId())).collect(Collectors.toList());
                // 将该国家下假期详情表按照leaveId分组
                Map<Long, List<HrmsCompanyLeaveItemConfigDO>> leaveIdToItemConfigMap = companyLeaveItemConfigInfo.stream().collect(Collectors.groupingBy(HrmsCompanyLeaveItemConfigDO::getLeaveId));

                // 查询该国家所有在职的员工、雇员
                LambdaQueryWrapper<HrmsUserInfoDO> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(HrmsUserInfoDO::getLocationCountry, country);
                queryWrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
                queryWrapper.in(HrmsUserInfoDO::getWorkStatus, Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()));
                queryWrapper.eq(HrmsUserInfoDO::getStatus, StatusEnum.ACTIVE.getCode());
                queryWrapper.in(HrmsUserInfoDO::getEmployeeType, employeeTypeList);
                List<HrmsUserInfoDO> userInfoList = hrmsUserInfoDao.list(queryWrapper);
                XxlJobLogger.log("查询到的国家：{}下，员工个数为:{}", country, userInfoList.size());

                if (CollUtil.isNotEmpty(userCodeList)) {
                    userInfoList = userInfoList.stream().filter(o -> userCodeList.contains(o.getUserCode())).collect(Collectors.toList());
                    XxlJobLogger.log("xxl-job【userAnnualLeaveHandler】配置了userCodeList，userCodeList数据为：{}", userCodeList.toString());
                    XxlJobLogger.log("xxl-job【userAnnualLeaveHandler】配置了userCodeList，过滤userCodeList之后查询到的国家：{}下，员工个数为:{}", country, userInfoList.size());
                }

                // 获取该国家下所有用户主键id
                List<Long> userIdList = userInfoList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());

                UserLeaveDetailQuery userLeaveDetailQuery = new UserLeaveDetailQuery();
                userLeaveDetailQuery.setUserIds(userIdList);
                // userLeaveDetailQuery.setStatus(StatusEnum.ACTIVE.getCode());
                userLeaveDetailQuery.setLeaveType(CompanyLeaveTypeEnum.ANNUAL_LEAVE.getCode());
                // 获取该国家所有员工拥有的年假假期
                List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailManage.selectUserLeaveDetail(userLeaveDetailQuery);
                // 该员工所有假期根据用户id分组
                Map<Long, List<HrmsUserLeaveDetailDO>> userIdToLeaveDetailMap = userLeaveDetailDOList.stream().collect(Collectors.groupingBy(HrmsUserLeaveDetailDO::getUserId));

                // 获取该国家所有员工拥有的年假假期的主键id
                List<Long> leaveIdList = userLeaveDetailDOList.stream().map(HrmsUserLeaveDetailDO::getId).collect(Collectors.toList());
                // 获取该国家所有员工的拥有假期的详情
                List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = hrmsUserLeaveStageDetailManage.selectByLeaveId(leaveIdList);
                // 获取该国家所有员工的拥有假期的详情map 按照leaveId分组
                Map<Long, List<HrmsUserLeaveStageDetailDO>> leaveIdToStageDetailMap = userLeaveStageDetailList.stream().collect(Collectors.groupingBy(HrmsUserLeaveStageDetailDO::getLeaveId));

                for (HrmsUserInfoDO userInfo : userInfoList) {
                    // 该国家下该人员应该有的假期集合
                    List<HrmsCompanyLeaveConfigDO> userShouldLeaveList = Lists.newArrayList();
                    userShouldLeaveList.addAll(allGetLeave);
                    // 如果是男性，获取男性的假期
                    if (userInfo.getSex() == 1) {
                        userShouldLeaveList.addAll(maleGetLeave);
                    }
                    // 如果是女性，获取女性的假期
                    if (userInfo.getSex() == 2) {
                        userShouldLeaveList.addAll(femaleGetLeave);
                    }
                    // 获取指定国家假期配置按照假期类型分组
                    Map<String, HrmsCompanyLeaveConfigDO> leaveTypeToCompanyLeaveConfigMap = userShouldLeaveList.stream().collect(Collectors.toMap(HrmsCompanyLeaveConfigDO::getLeaveType, Function.identity()));

                    // 获取该员工拥有的所有假期
                    List<HrmsUserLeaveDetailDO> userNowLeaveDetailList = userIdToLeaveDetailMap.get(userInfo.getId());
                    //防止空指针
                    userNowLeaveDetailList = userNowLeaveDetailList == null ? Lists.newArrayList() : userNowLeaveDetailList;
                    // 将该员工所有假期 变成假期类型为key的map
                    Map<String, HrmsUserLeaveDetailDO> leaveTypeToUserLeaveDetailMap = userNowLeaveDetailList.stream().collect(Collectors.toMap(HrmsUserLeaveDetailDO::getLeaveType, Function.identity()));

                    // 遍历国家下应该有的假期
                    leaveTypeToCompanyLeaveConfigMap.forEach((leaveType, companyLeaveConfig) -> {
                        // 获取该假期类型的详情
                        List<HrmsCompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig = leaveIdToItemConfigMap.get(companyLeaveConfig.getId());

                        // 获取该员工该假期类型的假期数据
                        HrmsUserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveType);
                        if (ObjectUtil.isNull(userLeaveDetailDO)) {
                            // 新增该员工该假期类型
                            HrmsUserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveType, companyLeaveConfig, addUserLeaveDetailList);
                            // 获取该假期总的分钟数
                            BigDecimal totalMinutes = BigDecimal.ZERO;
                            for (HrmsCompanyLeaveItemConfigDO companyLeaveItemConfigDO : hrmsCompanyLeaveItemConfig) {
                                BigDecimal leaveResidueDay = companyLeaveItemConfigDO.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfigDO.getStartDay());
                                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfigDO, userLeaveDetail, leaveResidueMinutes, addUserLeaveStageDetailList);
                            }
                            // 新增操作记录
                            otherAddLeaveRecord(userInfo, leaveType, totalMinutes, addUserLeaveRecordList, "true");
                        } else {
                            // 如果之前假期记录是禁用状态，修改该假期记录为启用状态
                            if (userLeaveDetailDO.getStatus().equals(StatusEnum.DISABLED.getCode())) {
                                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                                fillDOUpdate(userLeaveDetailDO);
                                updateUserLeaveDetailDOList.add(userLeaveDetailDO);

                                // 删除该假期类型的所有假期详情
                                List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                                HrmsUserLeaveStageDetailDO stageDetailInfo = userLeaveStageDetailInfoList.get(0);
                                stageDetailInfo.setIsDelete(IsDeleteEnum.YES.getCode());
                                fillDOUpdate(stageDetailInfo);
                                updateUserLeaveStageDetailList.add(stageDetailInfo);
                                if (stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) != 0) {
                                    otherAddLeaveRecord(userInfo, leaveType, stageDetailInfo.getLeaveResidueMinutes(), addUserLeaveRecordList, "false");
                                }

                                // 获取该假期总的分钟数
                                BigDecimal totalMinutes = BigDecimal.ZERO;
                                // 遍历该国家下该假期类型的详情
                                for (HrmsCompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                                    // 获取该阶段假期天数
                                    BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                    BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                    totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                    addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig, userLeaveDetailDO, leaveResidueMinutes, addUserLeaveStageDetailList);
                                }
                                // 新增操作记录
                                otherAddLeaveRecord(userInfo, leaveType, totalMinutes, addUserLeaveRecordList, "true");
                            } else {
                                // 累加该员工该假期类型
                                List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                                // 获取该假期总的分钟数
                                BigDecimal totalMinutes = BigDecimal.ZERO;
                                // 遍历该国家下该假期类型的详情
                                for (HrmsCompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                                    // 获取该阶段假期天数
                                    BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                    BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                    totalMinutes = totalMinutes.add(leaveResidueMinutes);

                                    // 年假由于不会存在阶梯假，所以这边特殊处理了，不是按照先删除，在新增的方式，而是直接累加。【就算后面有的国家配置了多个阶梯，只要这个人之前有年假，那么都会累加到这一个年假记录余额上面，也不会有问题】
                                    HrmsUserLeaveStageDetailDO stageDetailDO = userLeaveStageDetailInfoList.get(0);
                                    stageDetailDO.setLeaveResidueMinutes(userLeaveStageDetailInfoList.get(0).getLeaveResidueMinutes().add(leaveResidueMinutes));
                                    fillDOUpdate(stageDetailDO);
                                    updateUserLeaveStageDetailList.add(stageDetailDO);
                                }
                                // 新增操作记录
                                otherAddLeaveRecord(userInfo, leaveType, totalMinutes, addUserLeaveRecordList, "true");
                            }
                        }
                    });
                }
            });

            //落库
            userLeaveDetailManage.userLeaveBalanceDaysUpdate(addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveStageDetailList, updateUserLeaveDetailDOList);
        }
    }

    /**
     * 构建用户假期类型数据
     * @param userInfo 用户信息
     * @param leaveType 假期类型
     * @param companyLeaveConfig 假期配置
     * @param addUserLeaveDetailList 用户假期集合
     * @return HrmsUserLeaveDetailDO
     */
    @NotNull
    private HrmsUserLeaveDetailDO addLeaveDetail(HrmsUserInfoDO userInfo, String leaveType, HrmsCompanyLeaveConfigDO companyLeaveConfig, List<HrmsUserLeaveDetailDO> addUserLeaveDetailList) {
        HrmsUserLeaveDetailDO userLeaveDetail = new HrmsUserLeaveDetailDO();
        userLeaveDetail.setId(iHrmsIdWorker.nextId());
        userLeaveDetail.setUserId(userInfo.getId());
        userLeaveDetail.setUserCode(userInfo.getUserCode());
        userLeaveDetail.setLeaveType(leaveType);
        userLeaveDetail.setStatus(companyLeaveConfig.getStatus());
        fillDOInsert(userLeaveDetail);
        addUserLeaveDetailList.add(userLeaveDetail);
        return userLeaveDetail;
    }

    /**
     * 增加用户假期详情表数据
     * @param companyLeaveConfig 假期配置
     * @param companyLeaveItemConfigDO 假期详情配置
     * @param userLeaveDetail 用户假期数据
     * @param leaveResidueMinutes 假期剩余分钟数
     * @param addUserLeaveStageDetailList 用户假期详情集合
     */
    private void addLeaveStageDetail(HrmsCompanyLeaveConfigDO companyLeaveConfig, HrmsCompanyLeaveItemConfigDO companyLeaveItemConfigDO, HrmsUserLeaveDetailDO userLeaveDetail, BigDecimal leaveResidueMinutes, List<HrmsUserLeaveStageDetailDO> addUserLeaveStageDetailList) {
        HrmsUserLeaveStageDetailDO detailDO = new HrmsUserLeaveStageDetailDO();
        detailDO.setLeaveId(userLeaveDetail.getId());
        detailDO.setLeaveResidueMinutes(leaveResidueMinutes);
        detailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
        detailDO.setPercentSalary(companyLeaveItemConfigDO.getPercentSalary());
        detailDO.setStage(companyLeaveItemConfigDO.getStage());
        detailDO.setStatus(companyLeaveConfig.getStatus());
        fillDOInsert(detailDO);
        addUserLeaveStageDetailList.add(detailDO);
    }

    /**
     * 增加假期调整操作记录
     *
     * @param userInfo               用户信息
     * @param leaveType              假期类型
     * @param totalMinutes           假期总分钟数
     * @param addUserLeaveRecordList 假期操作记录集合
     * @param flag
     */
    private void otherAddLeaveRecord(HrmsUserInfoDO userInfo, String leaveType, BigDecimal totalMinutes, List<HrmsUserLeaveRecordDO> addUserLeaveRecordList, String flag) {
        HrmsUserLeaveRecordDO recordDO = new HrmsUserLeaveRecordDO();
        recordDO.setId(iHrmsIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(new Date());
        recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        recordDO.setLeaveType(leaveType);
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
        }else {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
        }
        recordDO.setLeaveMinutes(totalMinutes);
        recordDO.setRemark("");
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setRemark("新年初始化/New year initialization");
        } else {
            recordDO.setRemark("过期清零/Overdue clearing");
        }
        fillDOInsert(recordDO);
        // 防止查询时间相同，导致根据创建时间排序出现先后顺序问题【根据创建时间倒序之后，过期清零在新年初始化上面就比较奇怪】，所以这边新年初始化创建时间需要在过期清零后面，
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setCreateDate(DateUtil.offsetSecond(new Date(), 1));
            recordDO.setLastUpdDate(DateUtil.offsetSecond(new Date(), 1));
        }
        recordDO.setOperationUserCode("xxl-job");
        recordDO.setOperationUserName("xxl-job");
        addUserLeaveRecordList.add(recordDO);
    }


    private void userLeaveHandler(List<HrmsUserInfoDO> userInfoDOList, UserAnnualLeaveHandler.UserAnnualLeaveParam param) {
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return;
        }
        List<Long> userIds = userInfoDOList.stream().map(o -> o.getId()).collect(Collectors.toList());
        //查询用户的入职日期
        List<HrmsUserEntryRecordDO> entryRecordDOList = userEntryRecordDao.listByUserIds(userIds);
        //Key为userId
        Map<Long, HrmsUserEntryRecordDO> entryRecordMap = entryRecordDOList.stream().filter(o -> o.getEntryDate() != null).collect(Collectors.toMap(o -> o.getUserId(), o -> o, (v1, v2) -> v1));

        //查询这批用户的所有年假时间
        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        query.setUserIds(userIds);
        query.setLeaveType(CompanyLeaveTypeEnum.ANNUAL_LEAVE.getCode());
        // 这边注释掉查询激活的用户假期数据：原因：对于其他国家过来的，如果用户年假是DISABLED，则1. 先ACTIVE，2. 删除之前年假详情信息，3.新增2.5天用户年假详情记录
        // query.setStatus(StatusEnum.ACTIVE.getCode());
        List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailManage.selectUserLeaveDetail(query);
        XxlJobLogger.log("查询到的用户年假记录个数为:{}", userLeaveDetailDOList.size());

        //Key为userId
        Map<Long, HrmsUserLeaveDetailDO> userLeaveMap = userLeaveDetailDOList.stream().collect(Collectors.toMap(o -> o.getUserId(), o -> o, (v1, v2) -> v1));

        List<Long> userLeaveIds = userLeaveDetailDOList.stream().map(o -> o.getId()).collect(Collectors.toList());
        List<HrmsUserLeaveStageDetailDO> stageDetailDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userLeaveIds)) {
            stageDetailDOList = userLeaveDetailManage.selectUserLeaveStageByLeaveIds(userLeaveIds);
        }
        Map<Long, List<HrmsUserLeaveStageDetailDO>> userLeaveStageMap = stageDetailDOList.stream().collect(Collectors.groupingBy(o -> o.getLeaveId()));


        List<HrmsUserLeaveDetailDO> addList = new ArrayList<>();
        List<HrmsUserLeaveDetailDO> updateList = new ArrayList<>();
        List<HrmsUserLeaveStageDetailDO> stageAddList = new ArrayList<>();
        List<HrmsUserLeaveStageDetailDO> stageUpdateList = new ArrayList<>();
        List<HrmsUserLeaveRecordDO> recordDOList = new ArrayList<>();
        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
            HrmsUserEntryRecordDO entryRecordDO = entryRecordMap.get(userInfoDO.getId());
            if (entryRecordDO == null) {
                continue;
            }
            if (!sendLeaveHandler(entryRecordDO, param, userInfoDO)) {
                continue;
            }

            HrmsUserLeaveDetailDO userLeaveDetailDO = userLeaveMap.get(userInfoDO.getId());
            //新入职的员工，满1个月,员工没有年假，要新增一条记录
            if (userLeaveDetailDO == null) {
                userLeaveDetailDO = new HrmsUserLeaveDetailDO();
                Long userLeaveId = iHrmsIdWorker.nextId();
                userLeaveDetailDO.setId(userLeaveId);
                userLeaveDetailDO.setUserId(userInfoDO.getId());
                userLeaveDetailDO.setUserCode(userInfoDO.getUserCode());
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                userLeaveDetailDO.setLeaveType(CompanyLeaveTypeEnum.ANNUAL_LEAVE.getCode());
                userLeaveDetailDO.setCreateDate(new Date());
                userLeaveDetailDO.setCreateUserCode("210871");
                userLeaveDetailDO.setCreateUserName("test-max");
                userLeaveDetailDO.setLastUpdDate(new Date());
                userLeaveDetailDO.setLastUpdUserCode("210871");
                userLeaveDetailDO.setLastUpdUserName("test-max");
                addList.add(userLeaveDetailDO);

                HrmsUserLeaveStageDetailDO stageDetailDO = new HrmsUserLeaveStageDetailDO();
                stageDetailDO.setLeaveId(userLeaveId);
                BigDecimal leaveResidueMinutes = BigDecimal.valueOf(2.5).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                stageDetailDO.setLeaveResidueMinutes(leaveResidueMinutes);
                stageDetailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
                stageDetailDO.setPercentSalary(BigDecimal.ONE);
                stageDetailDO.setStage(BusinessConstant.Y);
                stageDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                stageDetailDO.setIsDelete(IsDeleteEnum.NO.getCode());
                stageDetailDO.setCreateDate(new Date());
                stageDetailDO.setCreateUserCode("210871");
                stageDetailDO.setCreateUserName("test-max");
                stageDetailDO.setLastUpdDate(new Date());
                stageDetailDO.setLastUpdUserCode("210871");
                stageDetailDO.setLastUpdUserName("test-max");
                stageAddList.add(stageDetailDO);

                leaveRecordHandler(recordDOList, userInfoDO, "true", null);
                continue;
            }
            // 如果之前假期记录是禁用状态，修改该假期记录为启用状态
            if (userLeaveDetailDO.getStatus().equals(StatusEnum.DISABLED.getCode())) {
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                //正式员工每月给年假
                userLeaveDetailDO.setLastUpdDate(new Date());
                userLeaveDetailDO.setLastUpdUserCode("210871");
                userLeaveDetailDO.setLastUpdUserName("test-max");
                updateList.add(userLeaveDetailDO);

                // 先删除之前的假期详情
                HrmsUserLeaveStageDetailDO stageDetailDO = userLeaveStageMap.get(userLeaveDetailDO.getId()).get(0);
                if (stageDetailDO == null) {
                    continue;
                }
                stageDetailDO.setIsDelete(IsDeleteEnum.NO.getCode());
                stageDetailDO.setLastUpdDate(new Date());
                stageDetailDO.setLastUpdUserCode("210871");
                stageDetailDO.setLastUpdUserName("test-max");
                stageUpdateList.add(stageDetailDO);
                // 如果该假期记录详情余额为0，则不生成请假操作记录
                if (stageDetailDO.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) != 0) {
                    leaveRecordHandler(recordDOList, userInfoDO, "false", stageDetailDO.getLeaveResidueMinutes());
                }

                // 新增假期详情--->初始化2.5天假期详情
                HrmsUserLeaveStageDetailDO detailDO = new HrmsUserLeaveStageDetailDO();
                detailDO.setLeaveId(userLeaveDetailDO.getId());
                BigDecimal leaveResidueMinutes = BigDecimal.valueOf(2.5).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                detailDO.setLeaveResidueMinutes(leaveResidueMinutes);
                detailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
                detailDO.setPercentSalary(BigDecimal.ONE);
                detailDO.setStage(BusinessConstant.Y);
                detailDO.setStatus(StatusEnum.ACTIVE.getCode());
                detailDO.setLastUpdDate(new Date());
                detailDO.setLastUpdUserCode("210871");
                detailDO.setLastUpdUserName("test-max");
                stageAddList.add(detailDO);

                leaveRecordHandler(recordDOList, userInfoDO, "true", null);
                continue;
            }

            HrmsUserLeaveStageDetailDO stageDetailDO = userLeaveStageMap.get(userLeaveDetailDO.getId()).get(0);
            if (stageDetailDO == null) {
                continue;
            }
            BigDecimal oldLeaveResidueMinutes = stageDetailDO.getLeaveResidueMinutes();
            if (oldLeaveResidueMinutes == null) {
                oldLeaveResidueMinutes = BigDecimal.ZERO;
            }
            BigDecimal leaveResidueMinutes = oldLeaveResidueMinutes.add(BigDecimal.valueOf(2.5).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));
            stageDetailDO.setLeaveResidueMinutes(leaveResidueMinutes);
            stageUpdateList.add(stageDetailDO);

            leaveRecordHandler(recordDOList, userInfoDO, "true", null);
        }

        //if (CollectionUtils.isNotEmpty(recordDOList)) {
        //    hrmsUserLeaveRecordMapper.insertBatchSomeColumn(recordDOList);
        //}
        //if (CollectionUtils.isNotEmpty(addList)) {
        //    userLeaveDetailManage.batchSave(addList);
        //}
        //if (CollectionUtils.isNotEmpty(updateList)) {
        //    userLeaveDetailManage.batchUpdate(updateList);
        //}
        //if (CollectionUtils.isNotEmpty(stageAddList)) {
        //    userLeaveDetailManage.batchSaveStageDetail(stageAddList);
        //}
        //if (CollectionUtils.isNotEmpty(stageUpdateList)) {
        //    userLeaveDetailManage.batchUpdateStageDetail(stageUpdateList);
        //}

        // 落库
        userLeaveDetailManage.userLeaveBalanceDaysUpdate(addList, stageAddList, recordDOList, stageUpdateList, updateList);

    }

    /**
     * 增加假期调整操作记录
     *
     * @param userInfo               用户信息
     * @param leaveType              假期类型
     * @param totalMinutes           假期总分钟数
     * @param addUserLeaveRecordList 假期操作记录集合
     * @param flag                   是否销假【true:销假，false:请假】
     */
    private void addLeaveRecord(HrmsUserInfoDO userInfo, String leaveType, BigDecimal totalMinutes, List<HrmsUserLeaveRecordDO> addUserLeaveRecordList, String flag) {
        HrmsUserLeaveRecordDO recordDO = new HrmsUserLeaveRecordDO();
        recordDO.setId(iHrmsIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(new Date());
        recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        recordDO.setLeaveType(leaveType);
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
        }else {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
        }
        recordDO.setLeaveMinutes(totalMinutes);
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setRemark("定时任务，每月给予2.5天年假");
        } else {
            recordDO.setRemark("定时任务，每月给予2.5天年假【用户年假DISABLED，清零】");
        }
        fillDOInsert(recordDO);
        // 防止查询时间相同，导致根据创建时间排序出现先后顺序问题【根据创建时间倒序之后，过期清零在新年初始化上面就比较奇怪】，所以这边新年初始化创建时间需要在过期清零后面，
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setCreateDate(DateUtil.offsetSecond(new Date(), 1));
            recordDO.setLastUpdDate(DateUtil.offsetSecond(new Date(), 1));
        }
        recordDO.setOperationUserCode("xxl-job");
        recordDO.setOperationUserName("xxl-job");
        addUserLeaveRecordList.add(recordDO);
    }

    /**
     * 流水记录添加
     */
    private void leaveRecordHandler(List<HrmsUserLeaveRecordDO> recordDOList, HrmsUserInfoDO userInfoDO, String flag, BigDecimal leaveMinutes) {
        HrmsUserLeaveRecordDO recordDO = new HrmsUserLeaveRecordDO();
        recordDO.setId(iHrmsIdWorker.nextId());
        recordDO.setUserId(userInfoDO.getId());
        recordDO.setUserCode(userInfoDO.getUserCode());
        recordDO.setDate(new Date());
        recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        recordDO.setLeaveStartDay(new Date());
        recordDO.setLeaveEndDay(new Date());
        recordDO.setLeaveType(CompanyLeaveTypeEnum.ANNUAL_LEAVE.getCode());
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
        } else {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
        }
        //BigDecimal reduceDay = BigDecimal.valueOf(2.5);
        //recordDO.setReduceDay(reduceDay);
        //recordDO.setHours(BigDecimal.valueOf(8).multiply(reduceDay).abs());
        if (ObjectUtil.isNull(leaveMinutes)) {
            recordDO.setLeaveMinutes(BigDecimal.valueOf(2.5).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));
        } else {
            recordDO.setLeaveMinutes(leaveMinutes);
        }
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setRemark("定时任务，每月给予2.5天年假");
        } else {
            recordDO.setRemark("定时任务，每月给予2.5天年假【用户年假DISABLED，清零】");
        }
        recordDO.setIsDelete(IsDeleteEnum.NO.getCode());
        recordDO.setCreateDate(new Date());
        recordDO.setCreateUserCode("210871");
        recordDO.setCreateUserName("test-max");
        recordDO.setLastUpdDate(new Date());
        recordDO.setLastUpdUserCode("210871");
        recordDO.setLastUpdUserName("test-max");
        // 防止查询时间相同，导致根据创建时间排序出现先后顺序问题【根据创建时间倒序之后，过期清零在新年初始化上面就比较奇怪】，所以这边新年初始化创建时间需要在过期清零后面，
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setCreateDate(DateUtil.offsetSecond(new Date(), 1));
            recordDO.setLastUpdDate(DateUtil.offsetSecond(new Date(), 1));
        }
        recordDO.setOperationUserCode("xxl-job");
        recordDO.setOperationUserName("xxl-job");
        recordDOList.add(recordDO);
    }

    /**
     * 判断是否需要发送年假
     */
    private boolean sendLeaveHandler(HrmsUserEntryRecordDO entryRecordDO, UserAnnualLeaveHandler.UserAnnualLeaveParam param, HrmsUserInfoDO userInfo) {
        Date confirmDate = DateUtil.beginOfDay(entryRecordDO.getEntryDate());
        Date dateNow = DateUtil.beginOfDay(DEFAULT_DATE);
        //当前日期是这个日期所在月份的第几天
        int dayNow = DateUtil.dayOfMonth(dateNow);
        //获取当前月的最后一天
        int endDayNow = DateUtil.endOfMonth(dateNow).dayOfMonth();
        //确认入职时间是改月的第几天
        int dayConfirm = DateUtil.dayOfMonth(confirmDate);

        // 员工所属国 是KSA国家 ，每月的入职日发2.5天年假
        if (userInfo.getLocationCountry().equals("KSA")) {
            //同一个月，不发放年假：其实就是【不同月或不同年的入职那一天】或【当天是当月最后一天】发2.5天年假
            if ((DateUtil.year(dateNow) == DateUtil.year(confirmDate)) && (DateUtil.month(dateNow) == DateUtil.month(confirmDate))) {
                return false;
            }
            //同一天，发年假
            if (dayNow == dayConfirm) {
                return true;
            }
            // 如果 dayConfirm = 30号，我传入的日期是28号，则28<30
            if (dayNow < dayConfirm) {
                // 如果28号是当月的最后一天，则发年假
                if (endDayNow == dayNow) {
                    return true;
                }
                return false;
            }
        }
        // 如果是HQ或UAE国家，每月1号发2.5天年假
        if ((userInfo.getLocationCountry().equals("HQ") || userInfo.getLocationCountry().equals("UAE")) && dayNow == 1) {
            return true;
        }

        return false;




/*

        //获取确认入职时间,转为DateTime格式
        DateTime confirmDate = DateUtil.date(entryRecordDO.getConfirmDate());
        //获取当前时间
        DateTime dateNow = DEFAULT_DATE;

        //当前日期是这个日期所在月份的第几天
        int dayNow = dateNow.dayOfMonth();
        //确认入职时间是改月的第几天
        int dayConfirm = confirmDate.dayOfMonth();

        if (param.isInitLeave) {
            //只会执行一次，初始化数据的时候，后续每天跑，不会调用
            if (dateNow.year() == confirmDate.year()) {
                //上个月的数据
                if (dateNow.month() == confirmDate.month() + 1) {
                    if (dayConfirm <= dayNow) {
                        return true;
                    }
                    return false;
                }
                //当前月的数据
                if (dateNow.month() == confirmDate.month()) {
                    return false;
                }
                //上个月之前的数据
                return true;
            }
            return true;
        }

        //当天入职的员工不发年假
        if (DateUtil.betweenDay(dateNow, confirmDate, false) == 0) {
            return false;
        }

        //同一天，发年假
        if (dayNow == dayConfirm) {
            return true;
        }

        //不是同一天时，看是否是改月的最后一天
        //获取入职时间的改月的最后一天
        int endDayConfirm = DateUtil.endOfMonth(confirmDate).dayOfMonth();
        //获取当前月的最后一天
        int endDayNow = DateUtil.endOfMonth(dateNow).dayOfMonth();

        //入职的时间的月份天数小于当前月的天数的时候，一定会在上面同一天，发年假这里被判断住
        //当前时间是改月的最后一天，并且入职时间大于当前月的最后一天
        if (dayNow == endDayNow && dayConfirm > endDayNow) {
            return true;
        }
        return false;*/
    }


    @Data
    private static class UserAnnualLeaveParam {
        /**
         * 所属国
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;

        /**
         * 指定当天时间
         */
        private String dateNow;

        /**
         * 用工类型
         */
        private String employeeType;
    }
}
