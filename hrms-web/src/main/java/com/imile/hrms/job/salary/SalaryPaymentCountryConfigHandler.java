package com.imile.hrms.job.salary;

import com.imile.common.enums.StatusEnum;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.salary.model.HrmsSalaryPaymentCountryConfigDO;
import com.imile.hrms.dao.salary.query.SalaryPaymentCountryQuery;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.manage.salary.HrmsSalaryPaymentCountryConfigManage;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/4/8 14:16
 * @version: 1.0
 */
@Slf4j
@Component
public class SalaryPaymentCountryConfigHandler {

    @Autowired
    private CountryService countryService;
    @Autowired
    private HrmsSalaryPaymentCountryConfigManage hrmsSalaryPaymentCountryConfigManage;
    @Autowired
    private IHrmsIdWorker hrmsIdWorker;

    @XxlJob(BusinessConstant.JobHandler.SALARY_PAYMENT_COUNTRY_CONFIG_HANDLER)
    public ReturnT<String> salaryPaymentCountryConfigHandler(String content) {
        List<CountryConfigDTO> countryConfigList = countryService.queryAllCountryConfigList();
        Map<String, List<CountryConfigDTO>> countryConfigMap = countryConfigList.stream().collect(Collectors.groupingBy(CountryConfigDTO::getCountryName));

        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(SalaryPaymentCountryQuery.builder().build());
        Map<String, List<HrmsSalaryPaymentCountryConfigDO>> paymentCountryMap = paymentCountryConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryPaymentCountryConfigDO::getPaymentCountry));

        List<HrmsSalaryPaymentCountryConfigDO> addList = new ArrayList<>();
        for (Map.Entry<String, List<CountryConfigDTO>> entry : countryConfigMap.entrySet()) {
            List<HrmsSalaryPaymentCountryConfigDO> existCountryConfigList = paymentCountryMap.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(existCountryConfigList)) {
                continue;
            }
            HrmsSalaryPaymentCountryConfigDO countryConfigDO = new HrmsSalaryPaymentCountryConfigDO();
            countryConfigDO.setId(hrmsIdWorker.nextId());
            countryConfigDO.setPaymentCountry(entry.getKey());
            countryConfigDO.setStatus(StatusEnum.DISABLED.getCode());
            countryConfigDO.setCountryCreateDate(entry.getValue().get(0).getCreateDate());
            BaseDOUtil.fillDOInsert(countryConfigDO);
            addList.add(countryConfigDO);
        }
        hrmsSalaryPaymentCountryConfigManage.saveBatch(addList);
        return ReturnT.SUCCESS;
    }
}
