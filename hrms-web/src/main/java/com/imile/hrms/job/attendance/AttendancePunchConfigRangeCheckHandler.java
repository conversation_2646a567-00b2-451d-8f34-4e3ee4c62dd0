package com.imile.hrms.job.attendance;

import com.alibaba.fastjson.JSON;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.common.enums.attendance.AttendanceTypeEnum;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigQuery;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDaoFacade;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarManageAdapter;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigManageAdapter;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigRangeAdapter;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.mq.HrMqEventConstant;
import com.imile.hrms.mq.basic.ProducerBasicService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 用户打卡规则/日历范围检查
 * @author: taokang
 * @createDate: 2023/10/24 19:57
 * @version: 1.0
 */
@Slf4j
@Component
public class AttendancePunchConfigRangeCheckHandler {

    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Resource
    private CalendarConfigDaoFacade calendarConfigDaoFacade;
    @Autowired
    protected IHrmsIdWorker iHrmsIdWorker;
    @Autowired
    private ProducerBasicService producerBasicService;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Resource
    private PunchConfigDaoFacade punchConfigDaoFacade;
    @Resource
    private PunchConfigRangeAdapter punchConfigRangeAdapter;
    @Resource
    private PunchConfigManageAdapter punchConfigManageAdapter;


    @Resource
    private CalendarManageAdapter calendarManageAdapter;
    @Value("${rocket.mq.hr.topic}")
    private String hrProducerTopic;

    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_PUNCH_CONFIG_RANGE_CHECK_HANDLER)
    public ReturnT<String> attendancePunchConfigRangeCheckHandler(String content) {
        AttendancePunchConfigRangeCheckHandler.AttendancePunchConfigRangeCheckHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, AttendancePunchConfigRangeCheckHandler.AttendancePunchConfigRangeCheckHandlerParam.class) : new AttendancePunchConfigRangeCheckHandler.AttendancePunchConfigRangeCheckHandlerParam();

        List<HrmsUserInfoDO> userInfoDOList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            List<String> originCountryList = Arrays.asList(param.getCountryList().split(","));
            UserDaoQuery query = UserDaoQuery.builder().locationCountryList(originCountryList).build();
            userInfoDOList = hrmsUserInfoDao.userList(query);
            //userInfoDOList = hrmsUserInfoManage.selectUserInfoByCompanyIdList(originCountryList);
        }
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodes().split(","));
            userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        }
        List<Long> userIdList = userInfoDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<HrmsAttendancePunchConfigRangeDO> hrmsAttendancePunchConfigRangeDOList = punchConfigRangeAdapter.selectConfigRangeByBizId(userIdList);
        List<HrmsAttendanceConfigRangeDO> hrmsAttendanceConfigRangeDOList = calendarManageAdapter.selectConfigRange(userIdList);

        //查询改所有的日历和打卡规则配置
        AttendancePunchConfigQuery attendancePunchConfigQuery = new AttendancePunchConfigQuery();
//        List<HrmsAttendancePunchConfigDO> hrmsAttendancePunchConfigDOList = hrmsAttendancePunchConfigDao.list(attendancePunchConfigQuery).stream().filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode())).collect(Collectors.toList());
        List<HrmsAttendancePunchConfigDO> hrmsAttendancePunchConfigDOList = punchConfigDaoFacade.getConfigAdapter().list(attendancePunchConfigQuery)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()))
                .collect(Collectors.toList());

        AttendanceConfigQuery attendanceConfigQuery = new AttendanceConfigQuery();
//        List<HrmsAttendanceConfigDO> hrmsAttendanceConfigDOList = hrmsAttendanceConfigDao.list(attendanceConfigQuery).stream().filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode())).collect(Collectors.toList());
        List<HrmsAttendanceConfigDO> hrmsAttendanceConfigDOList = calendarConfigDaoFacade.getCalendarConfigAdapter().list(attendanceConfigQuery)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()))
                .collect(Collectors.toList());


        List<HrmsAttendancePunchConfigRangeDO> addPunchConfigRangeList = new ArrayList<>();
        List<HrmsAttendanceConfigRangeDO> addConfigRangeList = new ArrayList<>();
        Date date = new Date();
        Set<Long> updateUserIdList = new HashSet<>();

        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
            List<HrmsAttendancePunchConfigRangeDO> userAttendancePunchConfigRangeDOList = hrmsAttendancePunchConfigRangeDOList.stream().filter(item -> item.getBizId().equals(userInfoDO.getId())).collect(Collectors.toList());
            List<HrmsAttendanceConfigRangeDO> userHrmsAttendanceConfigRangeDOList = hrmsAttendanceConfigRangeDOList.stream().filter(item -> item.getBizId().equals(userInfoDO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userAttendancePunchConfigRangeDOList) && CollectionUtils.isNotEmpty(userHrmsAttendanceConfigRangeDOList)) {
                continue;
            }
            //没有打卡规则
            if (CollectionUtils.isEmpty(userAttendancePunchConfigRangeDOList)) {
                //查询改国家下的默认打卡规则
                List<HrmsAttendancePunchConfigDO> defaultAttendancePunchConfigDOList = hrmsAttendancePunchConfigDOList.stream()
                        .filter(item -> item.getIsDefault().equals(1) && StringUtils.equalsIgnoreCase(item.getCountry(), userInfoDO.getLocationCountry())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(defaultAttendancePunchConfigDOList) || defaultAttendancePunchConfigDOList.size() > 1) {
                    continue;
                }
                boolean attendancePunchConfigTag = false;
                HrmsAttendancePunchConfigRangeDO addConfigRangeDO = new HrmsAttendancePunchConfigRangeDO();
                updateUserIdList.add(userInfoDO.getId());
                //先找部门存不存在打卡规则，没有就默认
                for (HrmsAttendancePunchConfigDO attendancePunchConfigDO : hrmsAttendancePunchConfigDOList) {
                    if (StringUtils.isBlank(attendancePunchConfigDO.getDeptIds())) {
                        continue;
                    }
                    List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(attendancePunchConfigDO.getDeptIds().split(","), Long.class));
                    if (deptIds.contains(userInfoDO.getDeptId())) {
                        attendancePunchConfigRangeBuild(userInfoDO.getId(), RangeTypeEnum.DEPT.getCode(), date, addConfigRangeDO, attendancePunchConfigDO);
                        addPunchConfigRangeList.add(addConfigRangeDO);
                        attendancePunchConfigTag = true;
                        break;
                    }
                }
                if (!attendancePunchConfigTag) {
                    //直接默认
                    attendancePunchConfigRangeBuild(userInfoDO.getId(), RangeTypeEnum.DEFAULT.getCode(), date, addConfigRangeDO, defaultAttendancePunchConfigDOList.get(0));
                    addPunchConfigRangeList.add(addConfigRangeDO);
                }
            }

            //没有日历
            if (CollectionUtils.isEmpty(userHrmsAttendanceConfigRangeDOList)) {
                //查询改国家下的默认日历
                List<HrmsAttendanceConfigDO> defaultAttendanceConfigDOList = hrmsAttendanceConfigDOList.stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getType(), AttendanceTypeEnum.DEFAULT.name()) && StringUtils.equalsIgnoreCase(item.getCountry(), userInfoDO.getLocationCountry())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(defaultAttendanceConfigDOList) || defaultAttendanceConfigDOList.size() > 1) {
                    continue;
                }
                boolean attendanceConfigTag = false;
                HrmsAttendanceConfigRangeDO addConfigRangeDO = new HrmsAttendanceConfigRangeDO();
                updateUserIdList.add(userInfoDO.getId());
                //先找部门存不存在打卡规则，没有就默认
                for (HrmsAttendanceConfigDO attendanceConfigDO : hrmsAttendanceConfigDOList) {
                    if (StringUtils.isBlank(attendanceConfigDO.getDeptIds())) {
                        continue;
                    }
                    List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(attendanceConfigDO.getDeptIds().split(","), Long.class));
                    if (deptIds.contains(userInfoDO.getDeptId())) {
                        attendanceConfigRangeBuild(userInfoDO.getId(), RangeTypeEnum.DEPT.getCode(), date, addConfigRangeDO, attendanceConfigDO);
                        addConfigRangeList.add(addConfigRangeDO);
                        attendanceConfigTag = true;
                        break;
                    }
                }
                if (!attendanceConfigTag) {
                    //直接默认
                    attendanceConfigRangeBuild(userInfoDO.getId(), RangeTypeEnum.DEFAULT.getCode(), date, addConfigRangeDO, defaultAttendanceConfigDOList.get(0));
                    addConfigRangeList.add(addConfigRangeDO);
                }
            }
        }
//        hrmsAttendancePunchConfigRangeManage.attendancePunchRangeCheckUpdate(addPunchConfigRangeList, addConfigRangeList);
        punchConfigManageAdapter.attendancePunchRangeCheckUpdate(addPunchConfigRangeList, addConfigRangeList);
        log.info("attendancePunchConfigRangeCheckHandler | updateUserIdList:{}", JSON.toJSON(updateUserIdList));
        for (Long userId : updateUserIdList) {
            producerBasicService.sendMessage(hrProducerTopic, HrMqEventConstant.ATTENDANCE_AND_PUNCH_UPDATE_TAG, userId.toString(), null);
        }
        return ReturnT.SUCCESS;
    }

    private void attendancePunchConfigRangeBuild(Long userId, String attendanceRangeType, Date date, HrmsAttendancePunchConfigRangeDO addConfigRangeDO, HrmsAttendancePunchConfigDO attendancePunchConfigDO) {
        addConfigRangeDO.setId(iHrmsIdWorker.nextId());
        addConfigRangeDO.setBizId(userId);
        addConfigRangeDO.setPunchConfigId(attendancePunchConfigDO.getId());
        addConfigRangeDO.setPunchConfigNo(attendancePunchConfigDO.getPunchConfigNo());
        addConfigRangeDO.setRangeType(attendanceRangeType);
        addConfigRangeDO.setIsNeedPunch(1);
        if (StringUtils.equalsIgnoreCase(attendanceRangeType, RangeTypeEnum.DEPT.getCode()) && attendancePunchConfigDO.getIsDefault().equals(1)) {
            addConfigRangeDO.setIsNeedPunch(0);
        }
        addConfigRangeDO.setEffectTime(date);
        addConfigRangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        addConfigRangeDO.setIsLatest(BusinessConstant.Y);
        addConfigRangeDO.setRemark("定时任务检查打卡规则是否缺失生成");
        BaseDOUtil.fillDOInsert(addConfigRangeDO);
    }

    private void attendanceConfigRangeBuild(Long userId, String attendanceRangeType, Date date, HrmsAttendanceConfigRangeDO addConfigRangeDO, HrmsAttendanceConfigDO attendanceConfigDO) {
        addConfigRangeDO.setId(iHrmsIdWorker.nextId());
        addConfigRangeDO.setBizId(userId);
        addConfigRangeDO.setAttendanceConfigId(attendanceConfigDO.getId());
        addConfigRangeDO.setAttendanceConfigNo(attendanceConfigDO.getAttendanceConfigNo());
        addConfigRangeDO.setRangeType(attendanceRangeType);
        addConfigRangeDO.setStartDate(date);
        addConfigRangeDO.setEndDate(BusinessConstant.DEFAULT_END_TIME);
        addConfigRangeDO.setIsLatest(BusinessConstant.Y);
        addConfigRangeDO.setRemark("定时任务检查日历是否缺失生成");
        BaseDOUtil.fillDOInsert(addConfigRangeDO);
    }

    @Data
    private static class AttendancePunchConfigRangeCheckHandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;
    }
}
