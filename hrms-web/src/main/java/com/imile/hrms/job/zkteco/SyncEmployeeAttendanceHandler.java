package com.imile.hrms.job.zkteco;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.punch.PunchTypeEnum;
import com.imile.hrms.common.enums.punch.SourceTypeEnum;
import com.imile.hrms.common.util.CommonUtil;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.dao.EmployeePunchRecordDao;
import com.imile.hrms.dao.punch.model.EmployeePunchRecordDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassConfigDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.manage.attendance.HrmsAttendanceEmployeeDetailManage;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigManageAdapter;
import com.imile.hrms.manage.punch.HrmsAttendanceClassEmployeeConfigManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.attendance.AttendanceGenerateService;
import com.imile.hrms.service.attendance.dto.DayAttendanceHandlerDTO;
import com.imile.hrms.service.attendance.dto.WarehouseAttendanceHandlerDTO;
import com.imile.hrms.service.punch.WarehouseAttendanceCalculateService;
import com.imile.hrms.service.punch.WarehouseService;
import com.imile.hrms.service.zkteco.ZKTecoUtils;
import com.imile.hrms.service.zkteco.ZktecoService;
import com.imile.hrms.service.zkteco.dto.ZKTecoAttendanceDTO;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 同步中控考勤打卡记录
 * @author: Max
 * @createDate: 2022-07-21
 * @version: 1.0
 */
@Slf4j
@Component
public class SyncEmployeeAttendanceHandler {

    @Autowired
    private ZKTecoUtils zkTecoUtils;
    @Autowired
    private HrmsUserInfoDao userInfoDao;
    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Autowired
    private EmployeePunchRecordDao punchRecordDao;
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsAttendanceEmployeeDetailManage hrmsAttendanceEmployeeDetailManage;
    @Autowired
    private HrmsAttendanceClassEmployeeConfigManage hrmsAttendanceClassEmployeeConfigManage;
    @Autowired
    private AttendanceGenerateService attendanceGenerateService;
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private CountryService countryService;
    @Resource
    private ZktecoService zktecoService;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private PunchConfigManageAdapter punchConfigManageAdapter;
    @Resource
    private WarehouseAttendanceCalculateService warehouseAttendanceCalculateService;


    @XxlJob(BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER)
    public ReturnT<String> syncEmployeeAttendanceHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER, param);
        SyncEmployeeAttendanceHandler.SyncEmployeeAttendanceHandlerParam handlerParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, SyncEmployeeAttendanceHandler.SyncEmployeeAttendanceHandlerParam.class) : new SyncEmployeeAttendanceHandler.SyncEmployeeAttendanceHandlerParam();

        //String token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserName(), hrmsProperties.getZkteco().getPassword());
        String token80 = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserNameVersion8(), hrmsProperties.getZkteco().getPasswordVersion8(), hrmsProperties.getZkteco().getSERVER_URL_VERSION_8());
        String token85 = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserName(), hrmsProperties.getZkteco().getPassword(), hrmsProperties.getZkteco().getSERVER_URL());


        List<String> userCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
            userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
        }
        if (handlerParam.getStartDate() == null || handlerParam.getEndDate() == null) {
            handlerParam.setStartDate(HrmsDateUtil.pushDate(DateUtils.dayBegin(new Date()), -1));
            handlerParam.setEndDate(new Date());
        }
        List<ZKTecoAttendanceDTO> attendanceList = new ArrayList<>();
        // 获取本次拉取zkt的所有打卡记录
        List<EmployeePunchRecordDO> allPunchRecordDOList = new ArrayList<>();

        //查询用户信息
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        if (CollectionUtils.isNotEmpty(userCodeList)) {
            log.info("syncEmployeeAttendanceHandler specify user userCodeList:{}", JSON.toJSONString(userCodeList));
            for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
                String attendances = "";
                if (zktecoService.isCountryVersion8(userInfoDO.getLocationCountry())) {
                    //走8.0版本
                    attendances = zkTecoUtils.listAttendances(token80, handlerParam.getStartDate(), handlerParam.getEndDate(), null, null, handlerParam.getTerminalSn(), userInfoDO.getUserCode(), hrmsProperties.getZkteco().getSERVER_URL_VERSION_8());
                } else {
                    attendances = zkTecoUtils.listAttendances(token85, handlerParam.getStartDate(), handlerParam.getEndDate(), null, null, handlerParam.getTerminalSn(), userInfoDO.getUserCode(), hrmsProperties.getZkteco().getSERVER_URL());
                }
                JSONObject attendancesJson = JSONObject.parseObject(attendances);
                attendanceList = JSONObject.parseArray(attendancesJson.get("data").toString(), ZKTecoAttendanceDTO.class);
                punchCardHandler(attendanceList, handlerParam, allPunchRecordDOList);
            }
            // 处理拉取打卡记录信息，并计算考勤
            handlerPunchRecordCalculateAttendance(allPunchRecordDOList);

            return ReturnT.SUCCESS;
        }

        int index = 1;
        int pageSize = 1000;

        String attendances80 = zkTecoUtils.listAttendances(token80, handlerParam.getStartDate(), handlerParam.getEndDate(), index, pageSize, handlerParam.getTerminalSn(), null, hrmsProperties.getZkteco().getSERVER_URL_VERSION_8());
        String attendances85 = zkTecoUtils.listAttendances(token85, handlerParam.getStartDate(), handlerParam.getEndDate(), index, pageSize, handlerParam.getTerminalSn(), null, hrmsProperties.getZkteco().getSERVER_URL());

        if (StringUtils.isNotBlank(attendances80)) {
            allPunchCardHandler(token80, hrmsProperties.getZkteco().getSERVER_URL_VERSION_8(), attendances80, handlerParam, allPunchRecordDOList);
        }
        if (StringUtils.isNotBlank(attendances85)) {
            allPunchCardHandler(token85, hrmsProperties.getZkteco().getSERVER_URL(), attendances85, handlerParam, allPunchRecordDOList);
        }

        // 处理拉取打卡记录信息，并计算考勤
        handlerPunchRecordCalculateAttendance(allPunchRecordDOList);

        return ReturnT.SUCCESS;
    }


    private void allPunchCardHandler(String token, String serverUrl, String attendances, SyncEmployeeAttendanceHandlerParam handlerParam, List<EmployeePunchRecordDO> allPunchRecordDOList) {
        int count = 0;
        int index = 1;
        int pageSize = 1000;

        JSONObject attendancesJson = JSONObject.parseObject(attendances);
        count = (int) attendancesJson.get("count");
        List<ZKTecoAttendanceDTO> attendanceList = JSONObject.parseArray(attendancesJson.get("data").toString(), ZKTecoAttendanceDTO.class);
        punchCardHandler(attendanceList, handlerParam, allPunchRecordDOList);

        if (count <= pageSize) {
            return;
        }

        index = count / pageSize;
        if (count % pageSize > 0) {
            index += 1;
        }

        for (int i = 1; i <= index; i++) {
            String attendancesForEach = zkTecoUtils.listAttendances(token, handlerParam.getStartDate(), handlerParam.getEndDate(), i + 1, pageSize, handlerParam.getTerminalSn(), null, serverUrl);
            JSONObject attendancesJsonForEach = JSONObject.parseObject(attendancesForEach);
            List<ZKTecoAttendanceDTO> attendanceListForEach = JSONObject.parseArray(attendancesJsonForEach.get("data").toString(), ZKTecoAttendanceDTO.class);
            punchCardHandler(attendanceListForEach, handlerParam, allPunchRecordDOList);
        }
    }

    private void punchCardHandler(List<ZKTecoAttendanceDTO> attendanceList, SyncEmployeeAttendanceHandlerParam handlerParam, List<EmployeePunchRecordDO> allPunchRecordDOList) {
        if (CollectionUtils.isEmpty(attendanceList)) {
            return;
        }
        log.info("punchCardHandler attendanceList size:{}", attendanceList.size());
        List<String> userCodes = attendanceList.stream().map(o -> o.getEmp_code()).collect(Collectors.toList());
        //查询员工是否在hr系统中
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.userListByUserCodes(userCodes);
        Map<String, HrmsUserInfoDO> userMap = userInfoDOList.stream().collect(Collectors.toMap(o -> o.getUserCode(), o -> o, (v1, v2) -> v1));

        //查询部门信息
        List<Long> deptIds = userInfoDOList.stream().map(o -> o.getDeptId()).collect(Collectors.toList());
        List<HrmsEntDeptDO> deptDOList = hrmsEntDeptDao.listByDeptIds(deptIds);
        //Map<Long, HrmsEntDeptDO> deptMap = deptDOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));

        List<String> dayIdStr = attendanceList.stream().map(o -> DateUtil.format(DateUtils.str2Date(o.getPunch_time(), "yyyy-MM-dd HH:mm:ss"), DatePattern.PURE_DATE_PATTERN)).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(EmployeePunchRecordDO::getDayId, dayIdStr);
        queryWrapper.eq(EmployeePunchRecordDO::getSourceType, SourceTypeEnum.ZKTECO.name());
        List<EmployeePunchRecordDO> userPunchRecordList = punchRecordDao.list(queryWrapper);
        Map<Integer, List<EmployeePunchRecordDO>> userPunchRecordMap = userPunchRecordList.stream().collect(Collectors.groupingBy(o -> o.getRelationId()));

        Map<String, List<ZKTecoAttendanceDTO>> userPunchCardGroup = attendanceList.stream().collect(Collectors.groupingBy(o -> o.getEmp_code()));

        List<EmployeePunchRecordDO> punchRecordDOList = new ArrayList<>();

        for (Map.Entry<String, List<ZKTecoAttendanceDTO>> entry : userPunchCardGroup.entrySet()) {
            HrmsUserInfoDO userInfoDO = userMap.get(entry.getKey());
            if (userInfoDO == null) {
                continue;
            }
            for (ZKTecoAttendanceDTO attendanceDTO : entry.getValue()) {
                String punchType = attendanceDTO.getPunch_state() == 1 ? PunchTypeEnum.ON_DUTY.getCode() : PunchTypeEnum.OUT_DUTY.getCode();
                Date punchTime = DateUtils.str2Date(attendanceDTO.getPunch_time(), "yyyy-MM-dd HH:mm:ss");
                String dayId = DateUtil.format(punchTime, DatePattern.PURE_DATE_PATTERN);

                List<EmployeePunchRecordDO> punchRecords = userPunchRecordMap.get(attendanceDTO.getId());
                if (CollectionUtils.isNotEmpty(punchRecords)) {
                    List<EmployeePunchRecordDO> dayPunchRecords = punchRecords.stream().filter(o -> StringUtils.equalsIgnoreCase(dayId, o.getDayId())).collect(Collectors.toList());
                    boolean has = dayPunchRecords.stream().anyMatch(o -> o.getPunchTime().compareTo(punchTime) == 0);
                    if (has) {
                        log.info("punchCardHandler punchRecord has exist,attendanceDTO:{}", JSON.toJSONString(attendanceDTO));
                        continue;
                    }
                }

                EmployeePunchRecordDO punchRecordDO = new EmployeePunchRecordDO();
                punchRecordDO.setUserCode(userInfoDO.getUserCode());
                punchRecordDO.setCountry(userInfoDO.getLocationCountry());
                //HrmsEntDeptDO deptDO = deptMap.get(userInfoDO.getDeptId());
                punchRecordDO.setDeptId(userInfoDO.getDeptId());
                punchRecordDO.setRelationId(attendanceDTO.getId());
                punchRecordDO.setPunchCardType(attendanceDTO.getVerify_type_display());
                punchRecordDO.setPunchArea(attendanceDTO.getArea_alias());
                punchRecordDO.setLatitude(BigDecimal.ZERO);
                punchRecordDO.setLongitude(BigDecimal.ZERO);
                punchRecordDO.setPunchTime(punchTime);
                punchRecordDO.setDayId(dayId);
                punchRecordDO.setPunchType(punchType);
                punchRecordDO.setSourceType(SourceTypeEnum.ZKTECO.name());
                punchRecordDO.setEmployeeType(userInfoDO.getEmployeeType());
                punchRecordDOList.add(punchRecordDO);
            }
        }
        // 将封装好的打卡记录放到总的打卡记录中
        allPunchRecordDOList.addAll(punchRecordDOList);

        if (CollectionUtils.isNotEmpty(punchRecordDOList)) {
            punchRecordDao.saveBatch(punchRecordDOList);
        }
    }

    /**
     * 根据每一个小时拉过来的打卡记录，实时计算这些人的考勤
     *
     * @param allPunchRecordDOList 打卡记录
     */
    private void handlerPunchRecordCalculateAttendance(List<EmployeePunchRecordDO> allPunchRecordDOList) {
        if (CollUtil.isEmpty(allPunchRecordDOList)) {
            log.info("handlerPunchRecordCalculateAttendance allPunchRecordDOList is empty，无需计算考勤");
            return;
        }
        log.info("handlerPunchRecordCalculateAttendance allPunchRecordDOList size:{}", allPunchRecordDOList.size());
        log.info("handlerPunchRecordCalculateAttendance allPunchRecordDOList:{}", JSON.toJSONString(allPunchRecordDOList));

        // 获取用户code集合
        List<String> userCodeList = allPunchRecordDOList.stream().map(EmployeePunchRecordDO::getUserCode).distinct().collect(Collectors.toList());
        // 获取dayId集合
        List<String> dayIdList = allPunchRecordDOList.stream().map(EmployeePunchRecordDO::getDayId).distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(userCodeList) || CollUtil.isEmpty(dayIdList)) {
            log.info("handlerPunchRecordCalculateAttendance userCodeList or dayIdList is empty");
            log.info("handlerPunchRecordCalculateAttendance userCodeList:{}", JSON.toJSONString(userCodeList));
            log.info("handlerPunchRecordCalculateAttendance dayIdList:{}", JSON.toJSONString(dayIdList));
            return;
        }
        log.info("handlerPunchRecordCalculateAttendance zkt sync userCodeList size:{}", userCodeList.size());
        log.info("handlerPunchRecordCalculateAttendance zkt sync userCodeList:{}", JSON.toJSONString(userCodeList));

        //查询员工是否在hr系统中
        List<HrmsUserInfoDO> userInfoList = userInfoDao.userListByUserCodes(userCodeList);

        log.info("handlerPunchRecordCalculateAttendance hrms userInfoList size:{}", userInfoList.size());

        // 将userInfoList按照userCode转为map
        Map<String, HrmsUserInfoDO> userInfoMap = userInfoList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, Function.identity()));

        // 获取员工id集合
        List<Long> userIdList = userInfoList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());

        // 获取员工常驻国集合
        List<String> locationCountryList = userInfoList.stream().map(HrmsUserInfoDO::getLocationCountry).collect(Collectors.toList());

        if (CollUtil.isEmpty(locationCountryList)) {
            log.info("handlerPunchRecordCalculateAttendance locationCountryList is empty");
            return;
        }
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(locationCountryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));

        // 封装dayId集合，将dayIdList转为Long的dayIdList，并且加一天减一天
        List<Long> dayIdLongList = getDayIdLongList(dayIdList);
        log.info("handlerPunchRecordCalculateAttendance dayIdLongList:{}", JSON.toJSONString(dayIdLongList));
        // 通过userIdList + dayIdLongList 查询员工的正常考勤表记录
        List<HrmsAttendanceEmployeeDetailDO> attendanceEmployeeList = hrmsAttendanceEmployeeDetailManage.selectByUserIdListAndDayIdList(userIdList, dayIdLongList);
        Map<Long, List<HrmsAttendanceEmployeeDetailDO>> userAttendanceMap = attendanceEmployeeList.stream().collect(Collectors.groupingBy(HrmsAttendanceEmployeeDetailDO::getUserId));

        // 通过userIdList + dayIdLongList 查询员工的排班信息，【如果userIdList为空，上面已经拦截了，下面这个就不会执行了】
        List<HrmsAttendanceClassEmployeeConfigDO> hrmsAttendanceClassEmployeeConfig = hrmsAttendanceClassEmployeeConfigManage.selectBatchUserRecord(userIdList, dayIdLongList);
        Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> attendanceClassEmployeeConfigMap = hrmsAttendanceClassEmployeeConfig.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getUserId));

        // 获取排班班次id
        List<Long> classIdList = hrmsAttendanceClassEmployeeConfig.stream().filter(item -> ObjectUtil.isNotNull(item.getClassId())).map(HrmsAttendanceClassEmployeeConfigDO::getClassId).collect(Collectors.toList());
        // 根据班次id获取排班班次配置信息
//        List<HrmsAttendancePunchClassConfigDO> classConfig = hrmsAttendancePunchClassConfigManage.selectClassByIdList(classIdList);
        List<HrmsAttendancePunchClassConfigDO> classConfig = punchConfigManageAdapter.selectClassByIdList(classIdList);
        // 将classConfig按照classId转为map
        Map<Long, HrmsAttendancePunchClassConfigDO> classConfigMap = classConfig.stream().collect(Collectors.toMap(HrmsAttendancePunchClassConfigDO::getId, Function.identity()));

        // 获取当前时间
        DateTime nowDate = DateUtil.date();

        // 需要计算考勤的数据
        List<DayAttendanceHandlerDTO> dayAttendanceHandlerList = Lists.newArrayList();
        // 1. 按照员工编号分组
        Map<String, List<EmployeePunchRecordDO>> userPunchRecordMap = allPunchRecordDOList.stream().collect(Collectors.groupingBy(EmployeePunchRecordDO::getUserCode));
        // 2. 按照员工编号分组，计算考勤
        for (Map.Entry<String, List<EmployeePunchRecordDO>> entry : userPunchRecordMap.entrySet()) {
            // 用户code
            String userCode = entry.getKey();
            HrmsUserInfoDO hrmsUserInfo = userInfoMap.get(userCode);
            if (ObjectUtil.isNull(hrmsUserInfo)) {
                log.info("handlerPunchRecordCalculateAttendance hrmsUserInfo is null，userCode：{}", userCode);
                continue;
            }
            log.info("handlerPunchRecordCalculateAttendance hrmsUserInfo：{}", JSON.toJSONString(hrmsUserInfo));
            String locationCountry = hrmsUserInfo.getLocationCountry();
            if (ObjectUtil.equal(locationCountry, "")) {
                XxlJobLogger.log("userCode：{},常驻国为空", userCode);
                continue;
            }
            String timeZone = countryConfigMap.getOrDefault(locationCountry, "");
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("userCode：{},该国家:{},不存在国家时区", userCode, locationCountry);
                continue;
            }
            // 将当前时间，转换为国家本地时间
            Date localDateTime = CommonUtil.convertDateByTimeZone(timeZone, nowDate);
            Long localDayId = Long.valueOf(DateUtil.format(localDateTime, DatePattern.PURE_DATE_PATTERN));
            int localYear = DateUtil.year(localDateTime);
            int localMonth = DateUtil.month(localDateTime) + 1;
            int localDay = DateUtil.dayOfMonth(localDateTime);
            // 校验当前时间dateTime是不是凌晨四点钟
            int localHour = DateUtil.hour(localDateTime, true);

            log.info("handlerPunchRecordCalculateAttendance userCode：{}，localYear：{}，localMonth：{}，localDay：{}，localHour：{},serverDate：{}", userCode, localYear, localMonth, localDay, localHour, nowDate);

            // 获取该人员的正常考勤数据
            List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetail = userAttendanceMap.get(hrmsUserInfo.getId());
            //防止空指针
            hrmsAttendanceEmployeeDetail = CollUtil.isEmpty(hrmsAttendanceEmployeeDetail) ? Lists.newArrayList() : hrmsAttendanceEmployeeDetail;
            // 将hrmsAttendanceEmployeeDetail按照dayId分组
            Map<Long, List<HrmsAttendanceEmployeeDetailDO>> dayAttendanceMap = hrmsAttendanceEmployeeDetail.stream().collect(Collectors.groupingBy(HrmsAttendanceEmployeeDetailDO::getDayId));

            // 获取该人员的排班信息
            List<HrmsAttendanceClassEmployeeConfigDO> hrmsAttendanceClassEmployeeConfigInfo = attendanceClassEmployeeConfigMap.get(hrmsUserInfo.getId());
            //防止空指针
            hrmsAttendanceClassEmployeeConfigInfo = CollUtil.isEmpty(hrmsAttendanceClassEmployeeConfigInfo) ? Lists.newArrayList() : hrmsAttendanceClassEmployeeConfigInfo;
            // 将hrmsAttendanceClassEmployeeConfigInfo按照dayId分组
            Map<Long, List<HrmsAttendanceClassEmployeeConfigDO>> dayAttendanceClassEmployeeConfigMap = hrmsAttendanceClassEmployeeConfigInfo.stream().collect(Collectors.groupingBy(HrmsAttendanceClassEmployeeConfigDO::getDayId));

            // 用户对应的本次拉取的所有打卡记录
            List<EmployeePunchRecordDO> value = entry.getValue();
            log.info("handlerPunchRecordCalculateAttendance userCode：{}，value：{}", userCode, JSON.toJSONString(value));
            // 获取该人员的dayId集合
            List<String> dayIdListByUserCode = value.stream().map(EmployeePunchRecordDO::getDayId).distinct().collect(Collectors.toList());
            // 封装dayId数据，该dayIdByUserCodeLongList是该用户本次同步过来，需要遍历的dayId
            List<Long> dayIdByUserCodeLongList = getDayIdLongList(dayIdListByUserCode);
            log.info("handlerPunchRecordCalculateAttendance userCode：{}，dayIdByUserCodeLongList：{}", userCode, JSON.toJSONString(dayIdByUserCodeLongList));

            // 获取用户这些天的打卡记录
            //List<EmployeePunchRecordDO> employeePunchRecord = Lists.newArrayList();
            //if (CollUtil.isNotEmpty(dayIdByUserCodeLongList)) {
            //    List<String> dayIdByUserCodeStringList = Lists.newArrayList();
            //    for (Long param : dayIdByUserCodeLongList) {
            //        dayIdByUserCodeStringList.add(String.valueOf(param));
            //    }
            //    EmployeePunchCardRecordQuery employeePunchCardRecordQuery = new EmployeePunchCardRecordQuery();
            //    employeePunchCardRecordQuery.setUserCode(userCode);
            //    employeePunchCardRecordQuery.setDayIds(dayIdByUserCodeStringList);
            //    employeePunchRecord = punchRecordDao.listRecords(employeePunchCardRecordQuery);
            //}
            // 获取dayIdByUserCodeLongList的最大的dayId
            Long maxDayId = dayIdByUserCodeLongList.stream().max(Long::compareTo).orElse(0L);
            log.info("handlerPunchRecordCalculateAttendance userCode：{}，maxDayId：{}，localDayId：{}", userCode, maxDayId, localDayId);
            /*
                如果最大计算考勤日的时间 == 当地当前时间，则需要判断该用户当天是否存在打卡记录，不存在打卡记录，则不需要计算考勤，因为有可能人员在国外，但是人员的打卡规则是在国内，所以获取localDayId就==计算考勤日了
                比如人员在MEX（人员的打卡规则是CHN，常驻地是CHN），通过考勤机器打卡时间是 2024-07-15 09:09:27，中国时间2024-07-16 00:00:00 会去拉打卡记录，找到这个人，然后计算 20240714 20240715 20240716三天的考勤，所以20240716就等当前时间了，就不满足上面大于的条件，就计算考勤了，然后是上下班缺卡
             */
            // 如果最大计算考勤日的时间 == 当地当前时间，则需要判断该用户当天是否存在打卡记录，不存在打卡记录，则不需要计算考勤
            if (maxDayId.equals(localDayId)) {
                List<EmployeePunchRecordDO> punchList = punchRecordDao.getPunchList(maxDayId, userCode, null);
                log.info("handlerPunchRecordCalculateAttendance maxDayId == localDay count，userCode：{}，maxDayId：{}，localDay：{}，punchList：{}", userCode, maxDayId, localDayId, JSON.toJSONString(punchList));
                if (CollUtil.isEmpty(punchList)) {
                    log.info("handlerPunchRecordCalculateAttendance maxDayId == localDayId and employeePunchRecord is empty，userCode：{}，locationCountry：{}，maxDayId：{}，localDayId：{}", userCode, locationCountry, maxDayId, localDayId);
                    // dayIdByUserCodeLongList去除掉最大的dayId
                    log.info("handlerPunchRecordCalculateAttendance dayIdByUserCodeLongList remove maxDayId，userCode：{}，locationCountry：{}，maxDayId：{}，localDayId：{}", userCode, locationCountry, maxDayId, localDayId);
                    dayIdByUserCodeLongList.removeIf(element -> element.equals(maxDayId));
                }
            }
            // 遍历dayIdLongList，所有的dayId
            for (Long dayId : dayIdByUserCodeLongList) {

                // 如果需要计算考勤日的时间大于当前时间，则不进行考勤计算，只有小于等于当天的才计算
                if (dayId > localDayId) {
                    log.info("handlerPunchRecordCalculateAttendance dayId > localDayId，userCode：{}，locationCountry：{}，attendanceDayId：{}，localDayId：{}", userCode, locationCountry, dayId, localDayId);
                    continue;
                }

                // 获取该员工的当天的正常考勤数据
                List<HrmsAttendanceEmployeeDetailDO> attendanceEmployeeDetail = dayAttendanceMap.get(dayId);
                // 防止空指针
                attendanceEmployeeDetail = CollUtil.isEmpty(attendanceEmployeeDetail) ? Lists.newArrayList() : attendanceEmployeeDetail;

                // 如果attendanceEmployeeDetail不为空且存在考勤类型为P的数据，则不进行考勤计算,[这边只过滤了为P的数据，来判断是否不需要计算，这样会存在一天都请假或都外勤的情况也需要重新计算考勤，这多计算也没有问题。不能少计算了]
                //if (CollUtil.isNotEmpty(attendanceEmployeeDetail) && attendanceEmployeeDetail.stream().anyMatch(o -> StringUtils.equalsIgnoreCase(o.getConcreteType(), AttendanceConcreteTypeEnum.P.getCode()))) {
                //    log.info("handlerPunchRecordCalculateAttendance hrmsAttendanceEmployeeDetail is not null and attendanceType has P record，userCode：{}，hrmsUserInfo：{}，不进行考勤计算逻辑处理", userCode, JSON.toJSONString(hrmsUserInfo));
                //    continue;
                //}

                BigDecimal attendanceMinutes = BigDecimal.ZERO;
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                //先看该用户当天正常考勤表的数据是不是正常，统计正常出勤时长以及请假时长
                for (HrmsAttendanceEmployeeDetailDO detailDO : attendanceEmployeeDetail) {
                    if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                    }
                    if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
                    }
                }
                log.info("handlerPunchRecordCalculateAttendance userCode：{}，dayId：{}, attendanceMinutes：{}，leaveMinutes：{}", userCode, dayId, attendanceMinutes, leaveMinutes);

                // 获取当天排班信息
                List<HrmsAttendanceClassEmployeeConfigDO> hrmsAttendanceClassEmployeeConfigInfoList = dayAttendanceClassEmployeeConfigMap.get(dayId);
                // 防止空指针
                hrmsAttendanceClassEmployeeConfigInfoList = CollUtil.isEmpty(hrmsAttendanceClassEmployeeConfigInfoList) ? Lists.newArrayList() : hrmsAttendanceClassEmployeeConfigInfoList;

                if (CollUtil.isNotEmpty(hrmsAttendanceClassEmployeeConfigInfoList)) {
                    if(hrmsAttendanceClassEmployeeConfigInfoList.size() > 1) {
                        log.info("handlerPunchRecordCalculateAttendance hrmsAttendanceClassEmployeeConfigInfoList size > 1，userCode：{}，dayId：{}, hrmsAttendanceClassEmployeeConfigInfoList：{}", userCode, dayId, JSON.toJSONString(hrmsAttendanceClassEmployeeConfigInfoList));
                        continue;
                    }
                    HrmsAttendanceClassEmployeeConfigDO hrmsAttendanceClassEmployeeConfigDO = hrmsAttendanceClassEmployeeConfigInfoList.get(0);
                    Long classId = hrmsAttendanceClassEmployeeConfigDO.getClassId();
                    if (ObjectUtil.isNotNull(classId)) {
                        log.info("handlerPunchRecordCalculateAttendance classId is not null，userCode：{}，hrmsUserInfo：{}，classId：{}", userCode, JSON.toJSONString(hrmsUserInfo), classId);
                        // 获取班次信息
                        HrmsAttendancePunchClassConfigDO hrmsAttendancePunchClassConfig = classConfigMap.get(classId);
                        if(ObjectUtil.isNotNull(hrmsAttendancePunchClassConfig)) {
                            // 获取班次信息
                            BigDecimal legalWorkingHours = hrmsAttendancePunchClassConfig.getLegalWorkingHours();
                            if (ObjectUtil.isNotNull(legalWorkingHours)) {
                                BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);
                                log.info("handlerPunchRecordCalculateAttendance legalWorkingHours：{}，totalMinutes：{}", legalWorkingHours, totalMinutes);
                                // 判断该人员考勤正常表数据，该天的考勤时长是否已经达到了规定的工作时长，已经达到了则不进行考勤计算逻辑处理
                                if(attendanceMinutes.add(leaveMinutes).compareTo(totalMinutes) >= 0) {
                                    log.info("handlerPunchRecordCalculateAttendance attendanceMinutes.add(leaveMinutes).compareTo(totalMinutes) >= 0 不进行考勤计算逻辑处理，userCode：{}，hrmsUserInfo：{}，attendanceMinutes：{}，leaveMinutes：{}，totalMinutes：{}", userCode, JSON.toJSONString(hrmsUserInfo), attendanceMinutes, leaveMinutes, totalMinutes);
                                    continue;
                                }
                            }
                        }
                    }
                }

                // 否则是需要计算考勤的，这边记录下来
                DayAttendanceHandlerDTO dayAttendanceHandlerDTO = new DayAttendanceHandlerDTO();
                dayAttendanceHandlerDTO.setUserCodes(userCode);
                dayAttendanceHandlerDTO.setAttendanceDayId(dayId);
                dayAttendanceHandlerList.add(dayAttendanceHandlerDTO);
            }
        }

        if (CollUtil.isEmpty(dayAttendanceHandlerList)) {
            XxlJobLogger.log("{} 执行完毕,没有需要计算考勤的数据", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);
            return;
        }
        // 将dayAttendanceHandlerList按照dayId分组，并将userCodes拼接成字符串，逗号分隔
        Map<Long, List<DayAttendanceHandlerDTO>> dayAttendanceHandlerMap = dayAttendanceHandlerList.stream().collect(Collectors.groupingBy(DayAttendanceHandlerDTO::getAttendanceDayId));
        // 遍历dayAttendanceHandlerMap，将userCodes拼接成字符串，逗号分隔
        List<DayAttendanceHandlerDTO> targetDayAttendanceHandlerList = Lists.newArrayList();
        for (Map.Entry<Long, List<DayAttendanceHandlerDTO>> entry : dayAttendanceHandlerMap.entrySet()) {
            Long key = entry.getKey();
            List<DayAttendanceHandlerDTO> dayAttendanceHandlerDTOList = entry.getValue();
            String userCodes = dayAttendanceHandlerDTOList.stream().map(DayAttendanceHandlerDTO::getUserCodes).collect(Collectors.joining(","));
            DayAttendanceHandlerDTO targetDayAttendanceHandlerDTO = new DayAttendanceHandlerDTO();
            targetDayAttendanceHandlerDTO.setUserCodes(userCodes);
            targetDayAttendanceHandlerDTO.setAttendanceDayId(key);
            targetDayAttendanceHandlerList.add(targetDayAttendanceHandlerDTO);
        }

        log.info("handlerPunchRecordCalculateAttendance targetDayAttendanceHandlerList size:{}", targetDayAttendanceHandlerList.size());
        log.info("handlerPunchRecordCalculateAttendance targetDayAttendanceHandlerList:{}", JSON.toJSONString(targetDayAttendanceHandlerList));

        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("XXL-JOB, {} " + "同步打卡记录，计算考勤" + "--->开始",
                BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);
        log.info("XXL-JOB, {} " + "同步打卡记录，计算考勤" + "--->开始",
                BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);

        AtomicInteger successCount = new AtomicInteger();
        List<DayAttendanceHandlerDTO> failAttendanceHandlerList = Lists.newArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(targetDayAttendanceHandlerList.size());
        // 3. 调用考勤计算接口
        targetDayAttendanceHandlerList.forEach(targetDayAttendanceHandlerDTO -> {
            threadPoolTaskExecutor.execute(() -> {
                try {
                    // 计算考勤
                    attendanceGenerateService.dayAttendanceHandler(targetDayAttendanceHandlerDTO);

                    //仓内计算考勤 当前存在自有员工混打场景
                    WarehouseAttendanceHandlerDTO attendanceHandlerDTO = WarehouseAttendanceHandlerDTO.builder().attendanceDayId(targetDayAttendanceHandlerDTO.getAttendanceDayId()).userCodes(targetDayAttendanceHandlerDTO.getUserCodes()).build();
                    warehouseAttendanceCalculateService.warehouseAttendanceCalculate(attendanceHandlerDTO);

                    successCount.getAndIncrement();
                } catch (Exception e) {
                    failAttendanceHandlerList.add(targetDayAttendanceHandlerDTO);
                    XxlJobLogger.log("{} 同步打卡记录，计算考勤任务失败,targetDayAttendanceHandlerDTO:{},message:{}", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER, JSON.toJSONString(targetDayAttendanceHandlerDTO), e.getMessage());
                    log.info("{} 同步打卡记录，计算考勤失败，targetDayAttendanceHandlerDTO:{},异常信息为:{}", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER, JSON.toJSONString(targetDayAttendanceHandlerDTO), e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            XxlJobLogger.log("{} CountDownLatch wait InterruptedException", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);
            Thread.currentThread().interrupt();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        XxlJobLogger.log("XXL-JOB, {} , " + "同步打卡记录，计算考勤" + "--->结束，耗时 {} 毫秒，其中成功{}个,失败{}个,失败的信息为:{}",
                BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_REMINDER_HANDLER, duration, successCount.get(), failAttendanceHandlerList.size(), failAttendanceHandlerList);

    }

    /**
     * 封装dayIdList，将dayIdList转为Long的dayIdList，并且加一天减一天
     *
     * @param dayIdList dayIdList
     * @return List<Long>
     */
    private List<Long> getDayIdLongList(List<String> dayIdList) {
        log.info("getDayIdLongList dayIdList:{}，dayIdList size {}", JSON.toJSONString(dayIdList), dayIdList.size());
        if (CollUtil.isEmpty(dayIdList)) {
            return Lists.newArrayList();
        }
        // 防止并发修改异常
        List<DateTime> dayIdToDayDate = Lists.newCopyOnWriteArrayList();
        // 将dayIdLongList转为date格式
        for (String dayId : dayIdList) {
            DateTime dateTime = DateUtil.parse(dayId, DatePattern.PURE_DATE_PATTERN);
            dayIdToDayDate.add(dateTime);
        }
        log.info("getDayIdLongList dayIdToDayDate:{}，dayIdToDayDate size {}", JSON.toJSONString(dayIdToDayDate), dayIdToDayDate.size());

        // dayIdToDayDate加一天减一天添加到list里面
        for (Date date : dayIdToDayDate) {
            dayIdToDayDate.add(DateUtil.offsetDay(date, 1));
            dayIdToDayDate.add(DateUtil.offsetDay(date, -1));
        }
        log.info("getDayIdLongList dayIdToDayDate:{}，dayIdToDayDate size {}", JSON.toJSONString(dayIdToDayDate), dayIdToDayDate.size());

        List<Long> dayIdLongList = Lists.newArrayList();
        // 将dayIdToDayDate转为Long的dayIdList
        for (DateTime dateTime : dayIdToDayDate) {
            dayIdLongList.add(Long.parseLong(DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN)));
        }
        log.info("getDayIdLongList dayIdLongList:{}，dayIdLongList size {}", JSON.toJSONString(dayIdLongList), dayIdLongList.size());
        // 去重
        dayIdLongList = dayIdLongList.stream().distinct().collect(Collectors.toList());

        return dayIdLongList;
    }

    @Data
    private static class SyncEmployeeAttendanceHandlerParam {
        /**
         * 员工编号
         */
        private String userCodes;
        /**
         * 终端sn
         */
        private String terminalSn;
        /**
         * 开始时间
         */
        private Date startDate;
        /**
         * 结束时间
         */
        private Date endDate;
    }
}
