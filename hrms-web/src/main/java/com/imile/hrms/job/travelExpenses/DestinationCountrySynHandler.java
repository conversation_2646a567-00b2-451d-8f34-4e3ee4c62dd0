package com.imile.hrms.job.travelExpenses;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hermes.business.api.BusZoneListApi;
import com.imile.hermes.business.dto.BusZoneListDTO;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.travelExpenses.model.HrmsTravelExpensesDO;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.hrms.service.travelExpenses.HrmsTravelExpensesService;
import com.imile.rpc.common.RpcResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自有司机账号状态巡检处理类
 *
 * <AUTHOR>
 * @date 2023/04/19
 */
@Slf4j
@Component
public class DestinationCountrySynHandler {

    @Autowired
    private CountryService countryService;

    @Autowired
    private IHrmsIdWorker hrmsIdWorker;

    @Reference(version = "1.0.0")
    private BusZoneListApi busZoneListApi;

    @Autowired
    private HrmsTravelExpensesService hrmsTravelExpensesService;

    @XxlJob(BusinessConstant.JobHandler.DESTINATION_COUNTRY_SYNCHRONIZATION)
    public ReturnT<String> DestinationCountrySynHandler(String param) {

        CountryApiQuery query = new CountryApiQuery();
        query.setOrgId(10L);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(query);

        //根据国家code唯一分组
        Map<String, CountryConfigDTO> map = countryConfigList.stream().collect(Collectors.toMap(CountryConfigDTO::getCountryName, Function.identity(), (key1, key2) -> key2));


        UserContext userContext = new UserContext();
        userContext.setUserCode("XXL-JOB");
        userContext.setUserName("XXL-JOB");
        userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        RequestInfoHolder.setLoginInfo(userContext);

        //根源国家
        RpcResult<List<BusZoneListDTO>> rpcList=busZoneListApi.queryBusZoneByRegionLevel(1,10L);
        List<BusZoneListDTO> list=HrmsCollectionUtils.convert(RpcResultProcessor.process(rpcList), BusZoneListDTO.class);

        log.info("差旅管理同步国家信息，差旅管理国家信息：{}", JSON.toJSONString(list));

        List<HrmsTravelExpensesDO> travelExpensesList=hrmsTravelExpensesService.getBaseMapper().selectList(new LambdaQueryWrapper<HrmsTravelExpensesDO>()
                .eq(HrmsTravelExpensesDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .groupBy(HrmsTravelExpensesDO::getCountry)
        );

        //获取差旅管理所有国家
        List<String> countryList=travelExpensesList.stream().map(HrmsTravelExpensesDO::getCountry).collect(Collectors.toList());


        //根据国家分组去重
        Map<String,BusZoneListDTO> rootMap=list.stream().collect(Collectors.toMap(BusZoneListDTO::getZoneName, Function.identity(),(k1,k2)->k1));

        List<HrmsTravelExpensesDO> insertList=new ArrayList<>();
        rootMap.forEach((k,v)->{
            if(!countryList.contains(k)){
                log.info("国家不存在，需要同步");
                HrmsTravelExpensesDO hrmsTravelExpensesDO=new HrmsTravelExpensesDO();
                hrmsTravelExpensesDO.setCountry(k);
                //格式化当前时间成年月日
                //hrmsTravelExpensesDO.setEffectTime(DateFormatUtils.parseDateTimeYYDDMM(DateFormatUtils.formatDate(new Date())));
                hrmsTravelExpensesDO.setId(hrmsIdWorker.nextId());
                hrmsTravelExpensesDO.setCode(v.getZoneName());
                if(map.containsKey(k)){
                    hrmsTravelExpensesDO.setCurrency(map.get(k).getCurrency());
                }
                BaseDOUtil.fillDOInsert(hrmsTravelExpensesDO);
                insertList.add(hrmsTravelExpensesDO);
            }
        });

        hrmsTravelExpensesService.saveBatch(insertList);

        return ReturnT.SUCCESS;
    }
}
