package com.imile.hrms.job.blacklist;

import com.imile.hrms.api.blacklist.dto.BanInfoDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.blacklist.BlacklistBanStatusEnum;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.blacklist.dao.HrmsBlacklistDao;
import com.imile.hrms.dao.blacklist.model.HrmsBlacklistDO;
import com.imile.hrms.service.blacklist.HrmsBlacklistService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时刷新黑名单缓存
 *
 * @ClassName RefreshBlacklistCacheHandler
 * <AUTHOR>
 * @Date 2023/5/23 19:01
 */
@Slf4j
@Component
public class RefreshBlacklistCacheHandler {

    @Resource
    private HrmsBlacklistDao hrmsBlacklistDao;

    @Resource
    private HrmsBlacklistService hrmsBlacklistService;

    @XxlJob(BusinessConstant.JobHandler.REFRESH_BLACKLIST_CACHE_HANDLER)
    public ReturnT<String> refreshBlacklistCacheHandler(String param) {
        XxlJobLogger.log("refreshBlacklistCacheHandler | start |  param={}", param);
        List<HrmsBlacklistDO> blacklistList;
        if (Strings.isNotEmpty(param)) {
            List<Long> idList = Arrays.stream(param.split(HrmsStringUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
            blacklistList = hrmsBlacklistDao.getByIdList(idList);
            dealBlacklistList(blacklistList);
        } else {
            boolean isRemainder = true;
            Long beginId = 0L;
            while (isRemainder) {
                blacklistList = hrmsBlacklistDao.getByStatusList(BlacklistBanStatusEnum.getBanStatusList(), beginId, 1000);
                if (CollectionUtil.isEmpty(blacklistList)) {
                    isRemainder = false;
                    continue;
                }
                beginId = blacklistList.get(blacklistList.size() - 1).getId();
                dealBlacklistList(blacklistList);
            }
        }
        XxlJobLogger.log("refreshBlacklistCacheHandler | end |  param={}", param);
        return ReturnT.SUCCESS;
    }

    private void dealBlacklistList(List<HrmsBlacklistDO> blacklistList) {
        blacklistList.forEach(o -> {
            BanInfoDTO banInfoDTO = hrmsBlacklistService.convertBanInfo(o);
            XxlJobLogger.log("refreshBlacklistCacheHandler | o={} |  banInfoDTO={}", o, banInfoDTO);
        });
    }

}
