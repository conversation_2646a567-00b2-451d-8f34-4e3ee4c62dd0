package com.imile.hrms.job.salary;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.hrms.api.wechat.api.WechatSendTextApi;
import com.imile.hrms.api.wechat.query.WeChatSendTestQuery;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.EnvEnum;
import com.imile.hrms.common.enums.salary.SalarySettlementUserDetailDataSourceEnum;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementAgentRecordDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserDetailDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserInfoDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateItemConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementAgentRecordQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementUserDetailQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementUserInfoQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateItemConfigQuery;
import com.imile.hrms.manage.salary.HrmsSalaryAttendanceItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySchemeConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementAgentRecordManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementUserDetailManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementUserInfoManage;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryTaskConfigManage;
import com.imile.hrms.service.attendance.HrmsAttendanceEmployeeDetailService;
import com.imile.hrms.service.salary.HrmsSalaryBaseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 代办记录提示
 * @author: taokang
 * @createDate: 2024/4/1 17:11
 * @version: 1.0
 */
@Slf4j
@Component
public class SalarySettlementAgentNoticeHandler {

    @Autowired
    private HrmsSalaryTaskConfigManage hrmsSalaryTaskConfigManage;
    @Autowired
    private HrmsSalarySchemeConfigManage hrmsSalarySchemeConfigManage;
    @Autowired
    private HrmsSalaryBaseService hrmsSalaryBaseService;
    @Autowired
    private HrmsSalaryItemConfigManage hrmsSalaryItemConfigManage;
    @Autowired
    private HrmsSalaryAttendanceItemConfigManage hrmsSalaryAttendanceItemConfigManage;
    @Autowired
    private HrmsSalarySettlementUserInfoManage hrmsSalarySettlementUserInfoManage;
    @Autowired
    private HrmsAttendanceEmployeeDetailService hrmsAttendanceEmployeeDetailService;
    @Autowired
    private HrmsSalarySettlementAgentRecordManage hrmsSalarySettlementAgentRecordManage;
    @Autowired
    private HrmsSalarySubmitTemplateConfigManage hrmsSalarySubmitTemplateConfigManage;
    @Autowired
    private HrmsSalarySubmitTemplateItemConfigManage hrmsSalarySubmitTemplateItemConfigManage;
    @Autowired
    private HrmsSalarySettlementUserDetailManage hrmsSalarySettlementUserDetailManage;
    @Autowired
    private WechatSendTextApi wechatSendTextApi;

    @Value(value = "${server.env}")
    private String env;
    public static final String HOST = "";

    public static final String TEST_HOST = "https://test-ehr-new.52imile.cn/#/PayrollCalculationCenter/SalaryDataIntegration";

    public static final String UAT_HOST = "https://uat-ehr-new.imile.com/#/PayrollCalculationCenter/SalaryDataIntegration";

    public static final String PROD_HOST = "https://ehr-ehr-new.imile.com/#/PayrollCalculationCenter/SalaryDataIntegration";

    @XxlJob(BusinessConstant.JobHandler.SALARY_SETTLEMENT_AGENT_NOTICE_HANDLER)
    public ReturnT<String> salarySettlementAgentNoticeHandler(String content) {
        SalarySettlementAgentNoticeHandler.SalarySettlementAgentNoticeHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, SalarySettlementAgentNoticeHandler.SalarySettlementAgentNoticeHandlerParam.class) : new SalarySettlementAgentNoticeHandler.SalarySettlementAgentNoticeHandlerParam();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            paramCountryList = Arrays.asList(param.getCountryList().split(","));
        }
        Date date = new Date();
        if (param.getTestDate() != null) {
            date = param.getTestDate();
        }
        //找到所有的代办记录，当前月和前一个月已经后一个月，共3个月的数据
        Long nowMonth = Long.valueOf(DateUtil.format(date, "yyyyMM"));
        Long beforeMonth = Long.valueOf(DateUtil.format(DateUtil.offsetMonth(DateUtil.parse(nowMonth.toString(), "yyyyMM"), -1), "yyyyMM"));
        Long afterMonth = Long.valueOf(DateUtil.format(DateUtil.offsetMonth(DateUtil.parse(nowMonth.toString(), "yyyyMM"), 1), "yyyyMM"));

        HrmsSalarySettlementAgentRecordQuery agentRecordQuery = HrmsSalarySettlementAgentRecordQuery.builder()
                .paymentCountryList(paramCountryList)
                .paymentMonthList(Arrays.asList(nowMonth.toString(), beforeMonth.toString(), afterMonth.toString()))
                .build();
        List<HrmsSalarySettlementAgentRecordDO> settlementAgentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(agentRecordQuery).stream()
                .filter(item -> item.getIsNeedNotice() != null && item.getIsNeedNotice().equals(BusinessConstant.Y))
                .collect(Collectors.toList());

        List<Long> salarySubmitTemplateConfigIdList = settlementAgentRecordDOList.stream()
                .map(HrmsSalarySettlementAgentRecordDO::getSalarySubmitTemplateConfigId)
                .collect(Collectors.toList());
        List<HrmsSalarySubmitTemplateConfigDO> submitTemplateConfigDOList = hrmsSalarySubmitTemplateConfigManage.listByIdList(salarySubmitTemplateConfigIdList);
        Map<Long, List<HrmsSalarySubmitTemplateConfigDO>> submitMap = submitTemplateConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateConfigDO::getId));

        //查提报配置中的科目信息(总科目)
        List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(salarySubmitTemplateConfigIdList)) {
            HrmsSalarySubmitTemplateItemConfigQuery templateItemConfigQuery = HrmsSalarySubmitTemplateItemConfigQuery.builder()
                    .submitTemplateConfigIdList(salarySubmitTemplateConfigIdList)
                    .build();
            submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(templateItemConfigQuery);
        }
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> submitItemMap = submitTemplateItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getSubmitTemplateConfigId));

        List<HrmsSalarySettlementAgentRecordDO> updateList = new ArrayList<>();
        for (HrmsSalarySettlementAgentRecordDO agentRecordDO : settlementAgentRecordDOList) {
            List<HrmsSalarySubmitTemplateConfigDO> existTemplateList = submitMap.get(agentRecordDO.getSalarySubmitTemplateConfigId());
            if (CollectionUtils.isEmpty(existTemplateList)) {
                continue;
            }
            List<HrmsSalarySubmitTemplateItemConfigDO> existTemplateItemList = submitItemMap.get(existTemplateList.get(0).getId());
            if (CollectionUtils.isEmpty(existTemplateItemList)) {
                continue;
            }
            if (agentRecordDO.getSubmitEndDate().before(date)) {
                continue;
            }
            //还没到提报时间
            Date noticeDate = DateUtil.offsetHour(agentRecordDO.getSubmitEndDate(), existTemplateList.get(0).getRemindHours());
            if (noticeDate.before(date)) {
                continue;
            }
            //看改单据的提报数据是否都提报了
            //总结薪人员
            HrmsSalarySettlementUserInfoQuery settlementUserInfoQuery = new HrmsSalarySettlementUserInfoQuery();
            settlementUserInfoQuery.setPaymentCountry(agentRecordDO.getPaymentCountry());
            settlementUserInfoQuery.setPaymentMonth(agentRecordDO.getPaymentMonth());
            settlementUserInfoQuery.setSalarySchemeConfigNo(agentRecordDO.getSalarySchemeConfigNo());
            settlementUserInfoQuery.setIsLatest(BusinessConstant.Y);
            List<HrmsSalarySettlementUserInfoDO> allSettlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.listByQuery(settlementUserInfoQuery);

            //获取已经提报的人员科目信息
            List<Long> allSalarySettlementUserInfoIdList = allSettlementUserInfoDOList.stream()
                    .map(HrmsSalarySettlementUserInfoDO::getId)
                    .collect(Collectors.toList());
            List<HrmsSalarySettlementUserDetailDO> allSettlementUserDetailDOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(allSalarySettlementUserInfoIdList)) {
                HrmsSalarySettlementUserDetailQuery settlementUserDetailQuery = HrmsSalarySettlementUserDetailQuery.builder()
                        .salarySettlementUserInfoIdList(allSalarySettlementUserInfoIdList)
                        .salarySubmitTemplateConfigId(agentRecordDO.getSalarySubmitTemplateConfigId())
                        .dataSource(SalarySettlementUserDetailDataSourceEnum.SETTLEMENT_APPROVAL.getCode())
                        .isLatest(BusinessConstant.Y)
                        .build();
                allSettlementUserDetailDOList = hrmsSalarySettlementUserDetailManage.listByQuery(settlementUserDetailQuery);
            }
            Set<Long> agentRecordSettlementUserIdSet = allSettlementUserInfoDOList.stream()
                    .map(HrmsSalarySettlementUserInfoDO::getId)
                    .collect(Collectors.toSet());
            Map<String, List<HrmsSalarySettlementUserDetailDO>> userDetailMap = allSettlementUserDetailDOList.stream().collect(Collectors.groupingBy(item -> item.getSalarySettlementUserInfoId().toString() + item.getItemId().toString()));

            //都提报完成了，没必须发
            if (agentRecordSettlementUserIdSet.size() * existTemplateItemList.size() <= userDetailMap.size()) {
                agentRecordDO.setIsNeedNotice(BusinessConstant.N);
                BaseDOUtil.fillDOUpdate(agentRecordDO);
                updateList.add(agentRecordDO);
                continue;
            }
            int dataNumber = agentRecordSettlementUserIdSet.size() * existTemplateItemList.size() - userDetailMap.size();
            try {
                //给提报人发企业微信消息，clover发送
                WeChatSendTestQuery weChatSendTestQuery = new WeChatSendTestQuery();
                weChatSendTestQuery.setUserCodes(JSONObject.parseArray(existTemplateList.get(0).getReporterList(), String.class));
                String desc = "待办任务手动催办：您好，您有" + dataNumber + "条薪资数据提报待办，时间窗口期" + DateUtil.format(agentRecordDO.getSubmitStartDate(), "yyyy-MM-dd") + "-" + DateUtil.format(agentRecordDO.getSubmitEndDate(), "yyyy-MM-dd") + "快截止了，请前往薪资系统" + "(" + getHost() + ")" + "抓紧提报";
                String descEn = "Manual Reminder of Pending Tasks: Hello, your " + dataNumber + " payroll data submission is pending, the time window " + DateUtil.format(agentRecordDO.getSubmitStartDate(), "yyyy-MM-dd") + "-" + DateUtil.format(agentRecordDO.getSubmitEndDate(), "yyyy-MM-dd") + " is about to expire, please go to the payroll system" + "(" + getHost() + ")" + " to catch up with the submission";
                weChatSendTestQuery.setContent(RequestInfoHolder.isChinese() ? desc : descEn);
                wechatSendTextApi.weChatTextPublicSend(weChatSendTestQuery);
            } catch (Exception e) {
                log.error("clover发送企业微信消息失败", e);
            }
            agentRecordDO.setIsNeedNotice(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(agentRecordDO);
            updateList.add(agentRecordDO);
        }
        hrmsSalarySettlementAgentRecordManage.batchUpdateAgentRecord(updateList);
        return ReturnT.SUCCESS;
    }


    public String getHost() {
        if (EnvEnum.DEV.getCode().equals(env) || EnvEnum.TEST.getCode().equals(env)) {
            return TEST_HOST;
        }
        if (EnvEnum.UAT.getCode().equals(env)) {
            return UAT_HOST;
        }
        if (EnvEnum.PROD.getCode().equals(env)) {
            return PROD_HOST;
        }
        return HOST;
    }


    @Data
    private static class SalarySettlementAgentNoticeHandlerParam {
        /**
         * 国家
         */
        private String countryList;

        /**
         * 测试使用，模拟执行时间
         */
        private Date testDate;
    }
}
