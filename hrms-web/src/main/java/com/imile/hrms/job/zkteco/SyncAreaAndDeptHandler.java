//package com.imile.hrms.job.zkteco;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.imile.common.enums.IsDeleteEnum;
//import com.imile.hrms.common.config.HrmsProperties;
//import com.imile.hrms.common.constants.BusinessConstant;
//import com.imile.hrms.common.context.RequestInfoHolder;
//import com.imile.hrms.common.enums.ZktVersionEnum;
//import com.imile.hrms.dao.organization.dao.HrmsEntCompanyDao;
//import com.imile.hrms.dao.organization.model.HrmsEntCompanyDO;
//import com.imile.hrms.service.zkteco.ZKTecoUtils;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import com.xxl.job.core.log.XxlJobLogger;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.beanutils.ConvertUtils;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * @description: 同步区域和部门信息到中控考勤
// * @author: Max
// * @createDate: 2022-07-21
// * @version: 1.0
// */
//@Slf4j
//@Component
//public class SyncAreaAndDeptHandler {
//
//    @Autowired
//    private HrmsEntCompanyDao hrmsEntCompanyDao;
//    @Autowired
//    private ZKTecoUtils zkTecoUtils;
//    @Autowired
//    private HrmsProperties hrmsProperties;
//
//    @XxlJob(BusinessConstant.JobHandler.SYNC_AREA_DEPT_ZKTECO_HANDLER)
//    public ReturnT<String> syncAreaAndDeptHandler(String param) {
//        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.SYNC_AREA_DEPT_ZKTECO_HANDLER, param);
//        SyncAreaAndDeptHandler.SyncAreaAndDeptHandlerParam handlerParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, SyncAreaAndDeptHandler.SyncAreaAndDeptHandlerParam.class) : new SyncAreaAndDeptHandler.SyncAreaAndDeptHandlerParam();
//
//
//        List<HrmsEntCompanyDO> companyDOList = getCompanyNames(handlerParam.companyIds);
//
//        for (HrmsEntCompanyDO companyDO : companyDOList) {
//            String areaJson = "";
//            if ( StringUtils.isNotBlank(hrmsProperties.getZkteco().getCompanyIdVersion8())
//                    && Arrays.asList(hrmsProperties.getZkteco().getCompanyIdVersion8().split(",")).contains(companyDO.getId().toString())) {
//                //走8.0版本
//                String token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserName(), hrmsProperties.getZkteco().getPassword(), hrmsProperties.getZkteco().getSERVER_URL_VERSION_8());
//                areaJson = zkTecoUtils.createArea(token, companyDO.getCountry(), companyDO.getCountry(), hrmsProperties.getZkteco().getSERVER_URL_VERSION_8());
//            } else {
//                String token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserName(), hrmsProperties.getZkteco().getPassword(), hrmsProperties.getZkteco().getSERVER_URL());
//                areaJson = zkTecoUtils.createArea(token, companyDO.getCountry(), companyDO.getCountry(), hrmsProperties.getZkteco().getSERVER_URL());
//            }
//            System.out.println("areaJson======" + areaJson);
//            JSONObject jsonObject = JSONObject.parseObject(areaJson);
//            String areaId = jsonObject.get("id").toString();
//        }
//
//       /* if (StringUtils.isNotBlank(handlerParam.getDeptNames())) {
//            List<String> deptNames = Arrays.asList(handlerParam.getDeptNames().split(","));
//            for (String deptName : deptNames) {
//                String deptJson="";
//                if (StringUtils.isNotBlank(hrmsProperties.getZkteco().getCompanyIdVersion8())
//                        && Arrays.asList(hrmsProperties.getZkteco().getCompanyIdVersion8().split(",")).contains(companyDO.getId().toString())) {
//                    //走8.0版本
//                    deptJson = zkTecoUtils.createDept(token, deptName, deptName);
//                }else {
//                    deptJson = zkTecoUtils.createDept(token, deptName, deptName);
//                }
//                JSONObject jsonObject = JSONObject.parseObject(deptJson);
//                String deptId = jsonObject.get("id").toString();
//                System.out.println("deptId======"+deptId);
//            }
//
//        }*/
//
//        return ReturnT.SUCCESS;
//    }
//
//
//    /**
//     * 获取要处理的公司信息
//     *
//     * @param companyIdStr
//     * @return
//     */
//    private List<HrmsEntCompanyDO> getCompanyNames(String companyIdStr) {
//        List<Long> list = new ArrayList<>();
//        if (StringUtils.isNotBlank(companyIdStr)) {
//            Long[] arr = (Long[]) ConvertUtils.convert(companyIdStr.split(","), Long.class);
//            list = Arrays.asList(arr);
//        }
//
//        LambdaQueryWrapper<HrmsEntCompanyDO> queryWrapper = Wrappers.lambdaQuery();
//        queryWrapper.eq(HrmsEntCompanyDO::getIsDelete, IsDeleteEnum.NO.getCode());
//        if (CollectionUtils.isNotEmpty(list)) {
//            queryWrapper.in(HrmsEntCompanyDO::getId, list);
//        }
//        List<HrmsEntCompanyDO> companyDOList = hrmsEntCompanyDao.list(queryWrapper);
//        //List<String> countryList = companyDOList.stream().map(o -> o.getCountry()).collect(Collectors.toList());
//        return companyDOList;
//    }
//
//
//    @Data
//    private static class SyncAreaAndDeptHandlerParam {
//        /**
//         * 公司ID
//         */
//        private String companyIds;
//
//        /**
//         * 部门名称
//         */
//        private String deptNames;
//
//    }
//}
