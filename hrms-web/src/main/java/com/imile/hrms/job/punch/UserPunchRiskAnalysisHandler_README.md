# 用户打卡风险分析定时任务

## 功能说明

该定时任务用于分析用户的打卡风险行为，主要检测同经纬度且时间间隔超过10分钟的打卡记录。

## 任务处理器

- **处理器名称**: `userPunchRiskAnalysisHandler`
- **处理器类**: `UserPunchRiskAnalysisHandler`
- **常量定义**: `BusinessConstant.JobHandler.USER_PUNCH_RISK_ANALYSIS_HANDLER`

## 参数说明

任务参数为 JSON 格式，包含以下字段：

```json
{
  "startDayId": 20250605,
  "endDayId": 20250605,
  "userCodes": ["user001", "user002"]
}
```

### 参数详细说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDayId | Long | 是 | 开始日期，格式：YYYYMMDD |
| endDayId | Long | 是 | 结束日期，格式：YYYYMMDD |
| userCodes | List<String> | 否 | 用户编码列表，为空则分析所有用户 |

## 使用示例

### 1. 分析指定日期的所有用户

```json
{
  "startDayId": 20250605,
  "endDayId": 20250605
}
```

### 2. 分析指定用户的风险

```json
{
  "startDayId": 20250601,
  "endDayId": 20250605,
  "userCodes": ["user001", "user002", "user003"]
}
```

### 3. 分析一周的数据

```json
{
  "startDayId": 20250601,
  "endDayId": 20250607
}
```

### 4. 无参数执行（默认分析前一天）

如果不传递任何参数，任务会自动分析前一天的数据。

## 风险检测逻辑

1. **数据查询**: 根据时间范围和用户条件查询打卡记录
2. **分组处理**: 按用户分组，再按经纬度分组
3. **时间检测**: 对同一经纬度的打卡记录按时间排序，检测相邻记录的时间间隔
4. **风险标记**: 如果时间间隔超过10分钟，标记为风险记录
5. **数据保存**: 保存风险汇总和明细数据

## 输出结果

### 风险汇总表 (attendance_user_punch_risk)

- 用户基本信息
- 分析时间范围
- 风险总次数
- 风险发生日期
- 风险等级 (high/medium/low)

### 风险明细表 (attendance_user_punch_risk_detail)

- 每条风险打卡的详细信息
- 打卡时间、经纬度
- GPS配置信息

## 风险等级规则

- **high**: 风险次数 >= 10
- **medium**: 风险次数 >= 5
- **low**: 风险次数 < 5

## 注意事项

1. 任务会自动删除已存在的同用户同周期的风险数据，避免重复
2. 只分析有效的、包含经纬度信息的打卡记录
3. 记录的是"后续发生的打卡"作为风险明细
4. 支持事务管理，确保数据一致性

## 定时任务配置建议

- **执行频率**: 建议每日凌晨执行，分析前一天的数据
- **超时时间**: 根据数据量设置，建议30分钟以上
- **失败重试**: 建议设置重试机制

## 监控和日志

任务执行过程中会输出详细的日志信息，包括：

- 参数解析结果
- 数据查询统计
- 风险检测结果
- 异常信息

可通过日志监控任务执行状态和结果。
