package com.imile.hrms.job.salary;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.salary.SalaryHandlerExecultionSourceEnum;
import com.imile.hrms.common.enums.salary.SalaryTaskConfigHandlerTypeEnum;
import com.imile.hrms.common.util.CommonUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.salary.model.HrmsSalaryHandlerExecutionRecordDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySchemeConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementAgentRecordDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryTaskConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryHandlerExecutionRecordQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateConfigQuery;
import com.imile.hrms.dao.salary.query.SalarySchemeConfigQuery;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.integration.hermes.vo.CountryDTO;
import com.imile.hrms.manage.salary.HrmsSalaryAttendanceItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryHandlerExecutionRecordManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySchemeConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementAgentRecordManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementUserInfoManage;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryTaskConfigManage;
import com.imile.hrms.service.attendance.HrmsAttendanceEmployeeDetailService;
import com.imile.hrms.service.salary.HrmsSalaryBaseService;
import com.imile.hrms.service.salary.dto.SalarySchemeCycleDetailDTO;
import com.imile.hrms.service.salary.dto.SalarySchemeCycleInfoDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 薪资结算员工代办单据生成
 * @author: taokang
 * @createDate: 2024/3/1 15:08
 * @version: 1.0
 */
@Slf4j
@Component
public class SalarySettlementAgentRecordHandler {

    @Autowired
    private HrmsSalaryTaskConfigManage hrmsSalaryTaskConfigManage;
    @Autowired
    private HrmsSalarySchemeConfigManage hrmsSalarySchemeConfigManage;
    @Autowired
    private HrmsSalaryBaseService hrmsSalaryBaseService;
    @Autowired
    private HrmsSalaryItemConfigManage hrmsSalaryItemConfigManage;
    @Autowired
    private HrmsSalaryAttendanceItemConfigManage hrmsSalaryAttendanceItemConfigManage;
    @Autowired
    private HrmsSalarySettlementUserInfoManage hrmsSalarySettlementUserInfoManage;
    @Autowired
    private HrmsAttendanceEmployeeDetailService hrmsAttendanceEmployeeDetailService;
    @Autowired
    private HrmsSalarySettlementAgentRecordManage hrmsSalarySettlementAgentRecordManage;
    @Autowired
    private HrmsSalarySubmitTemplateConfigManage hrmsSalarySubmitTemplateConfigManage;
    @Autowired
    private HrmsSalarySubmitTemplateItemConfigManage hrmsSalarySubmitTemplateItemConfigManage;
    @Autowired
    private HrmsSalaryHandlerExecutionRecordManage hrmsSalaryHandlerExecutionRecordManage;
    @Autowired
    private IHrmsIdWorker hrmsIdWorker;
    @Autowired
    private CountryService countryService;

    @XxlJob(BusinessConstant.JobHandler.SALARY_SETTLEMENT_AGENT_RECORD_HANDLER)
    public ReturnT<String> salarySettlementAgentRecordHandler(String content) {
        SalarySettlementAgentRecordHandler.SalarySettlementAgentRecordHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, SalarySettlementAgentRecordHandler.SalarySettlementAgentRecordHandlerParam.class) : new SalarySettlementAgentRecordHandler.SalarySettlementAgentRecordHandlerParam();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            paramCountryList = Arrays.asList(param.getCountryList().split(","));
        }
        //找出所有的薪资提报配置(无需最新的，在遍历的时候会去当前执行时间的计薪月记录)
        List<HrmsSalarySubmitTemplateConfigDO> allSalarySubmitTemplateConfigDOList = hrmsSalarySubmitTemplateConfigManage.listByQuery(HrmsSalarySubmitTemplateConfigQuery.builder().build());

        //查询所有薪资提报配置的时间窗口类型的执行配置
        List<HrmsSalaryTaskConfigDO> allSalaryTaskConfigDOList = hrmsSalaryTaskConfigManage.selectByAllCountryList().stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getHandlerType(), SalaryTaskConfigHandlerTypeEnum.TIME_WINDOW.getCode()))
                .collect(Collectors.toList());
        List<Long> taskConfigIdList = allSalaryTaskConfigDOList.stream().map(HrmsSalaryTaskConfigDO::getId).collect(Collectors.toList());
        //获取配置的执行记录
        HrmsSalaryHandlerExecutionRecordQuery executionRecordQuery = HrmsSalaryHandlerExecutionRecordQuery.builder().taskConfigIdList(taskConfigIdList).build();
        List<HrmsSalaryHandlerExecutionRecordDO> allSalaryHandlerExecutionRecordDOList = hrmsSalaryHandlerExecutionRecordManage.listByQuery(executionRecordQuery);

        //获取所有的计薪方案，需要找到每个计薪方案的周期
        SalarySchemeConfigQuery salarySchemeConfigQuery = new SalarySchemeConfigQuery();
        salarySchemeConfigQuery.setStatus(StatusEnum.ACTIVE.getCode());
        List<HrmsSalarySchemeConfigDO> allSalarySchemeConfigDOList = hrmsSalarySchemeConfigManage.selectSchemeConfigList(salarySchemeConfigQuery);

        //薪资提报配置根据国家分组进行遍历执行  颗粒度为国家&月份&计薪方案
        Map<String, List<HrmsSalarySubmitTemplateConfigDO>> submitTemplateConfigMap = allSalarySubmitTemplateConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateConfigDO::getPaymentCountry));

        List<HrmsSalarySettlementAgentRecordDO> addAgentRecordList = new ArrayList<>();
        List<HrmsSalaryHandlerExecutionRecordDO> addHandlerExecutionRecordList = new ArrayList<>();
        for (Map.Entry<String, List<HrmsSalarySubmitTemplateConfigDO>> submitTemplateEntry : submitTemplateConfigMap.entrySet()) {
            if (CollectionUtils.isNotEmpty(paramCountryList) && !paramCountryList.contains(submitTemplateEntry.getKey())) {
                continue;
            }
            CountryDTO countryDTO = countryService.queryCountry(submitTemplateEntry.getKey());
            if (countryDTO == null || StringUtils.isBlank(countryDTO.getTimeZone())) {
                countryDTO = countryService.queryCountry("CHN");
            }
            Date date = CommonUtil.convertDateByTimeZone(countryDTO.getTimeZone(), new Date());
            if (param.getTestDate() != null) {
                date = param.getTestDate();
            }
            Long dayId = Long.valueOf(DateUtil.format(date, "yyyyMMdd"));
            //获取当前国家下所有计薪方案
            List<HrmsSalarySchemeConfigDO> countrySalarySchemeConfigDOList = allSalarySchemeConfigDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getCountry(), submitTemplateEntry.getKey()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countrySalarySchemeConfigDOList)) {
                continue;
            }
            Map<String, List<HrmsSalarySchemeConfigDO>> schemeConfigMap = countrySalarySchemeConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySchemeConfigDO::getSchemeNo));
            //对计薪方案进行分组遍历处理
            for (Map.Entry<String, List<HrmsSalarySchemeConfigDO>> schemeEntry : schemeConfigMap.entrySet()) {
                List<HrmsSalarySchemeConfigDO> salarySchemeConfigDOList = schemeEntry.getValue().stream()
                        .sorted(Comparator.comparing(HrmsSalarySchemeConfigDO::getExpireTime).reversed())
                        .collect(Collectors.toList());
                //计薪方案的计薪周期开始结束时间转为当前时间对应的月份的具体时间
                SalarySchemeCycleInfoDTO salarySchemeCycleInfoDTO = JSON.parseObject(salarySchemeConfigDOList.get(0).getCycleInfo(), SalarySchemeCycleInfoDTO.class);
                SalarySchemeCycleDetailDTO salarySchemeCycleDetailDTO = hrmsSalaryBaseService.getSalarySchemeCycleDetailByDate(date, salarySchemeCycleInfoDTO.getCycleStart(), salarySchemeCycleInfoDTO.getCycleEnd());
                if (salarySchemeCycleDetailDTO == null) {
                    continue;
                }
                //判断计薪方案在当前周期有没有生效，可能知道生效时间在未来，可能在历史就停用了，可以停用又启用，正好当前月是停启用的空窗期
                boolean schemeTempTag = false;
                for (HrmsSalarySchemeConfigDO schemeConfigDO : salarySchemeConfigDOList) {
                    if (schemeConfigDO.getEffectTime().after(salarySchemeCycleDetailDTO.getCycleEndDate())
                            || schemeConfigDO.getExpireTime().before(salarySchemeCycleDetailDTO.getCycleStartDate())) {
                        continue;
                    }
                    schemeTempTag = true;
                    break;
                }
                if (!schemeTempTag) {
                    continue;
                }
                //看该计薪方案在今天是否满足配置表的执行规则
                Long settlementDate = Long.valueOf(DateUtil.format(salarySchemeCycleDetailDTO.getCycleEndDate(), "yyyyMM"));
                //注意:这里是一个国家下的所有薪资提报，即国家&月份&计薪方案确定一条提报记录
                for (HrmsSalarySubmitTemplateConfigDO salarySubmitTemplateConfigDO : submitTemplateEntry.getValue()) {
                    //找到薪资提报配置表当前月份符合的配置
                    if (salarySubmitTemplateConfigDO.getEffectDate().compareTo(settlementDate) > 0
                            || salarySubmitTemplateConfigDO.getExpireDate().compareTo(settlementDate) < 1) {
                        continue;
                    }
                    List<String> schemeNoList = Arrays.asList(salarySubmitTemplateConfigDO.getSchemeNoListString().split(","));
                    if (!schemeNoList.contains(schemeEntry.getKey())) {
                        continue;
                    }

                    //找到该月的报表配置，在查询窗口配置时间，看今天是否满足配置 并且看该配置有没有执行记录，如果有，无需执行
                    List<HrmsSalaryTaskConfigDO> existSalaryTaskConfigDOList = allSalaryTaskConfigDOList.stream()
                            .filter(item -> item.getId().equals(salarySubmitTemplateConfigDO.getSalaryTaskConfigId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(existSalaryTaskConfigDOList)) {
                        continue;
                    }
                    List<HrmsSalaryHandlerExecutionRecordDO> existSalaryHandlerExecutionRecordDOList = allSalaryHandlerExecutionRecordDOList.stream()
                            .filter(item -> item.getSalaryTaskConfigId().equals(existSalaryTaskConfigDOList.get(0).getId())
                                    && StringUtils.equalsIgnoreCase(item.getSchemeNo(), schemeEntry.getKey())
                                    && StringUtils.equalsIgnoreCase(item.getPaymentMonth(), settlementDate.toString()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(existSalaryHandlerExecutionRecordDOList)) {
                        continue;
                    }
                    //生成真正的窗口执行时间
                    Date timeWindowStartDate = DateUtil.beginOfDay(DateUtil.offsetDay(salarySchemeCycleDetailDTO.getCycleEndDate(), existSalaryTaskConfigDOList.get(0).getStartDiff()));
                    Date timeWindowEndDate = DateUtil.endOfDay(DateUtil.offsetDay(salarySchemeCycleDetailDTO.getCycleEndDate(), existSalaryTaskConfigDOList.get(0).getEndDiff()));
                    //看当前时间和执行时间是不是同一天，如果是，就执行
                    Long initDayId = Long.valueOf(DateUtil.format(timeWindowStartDate, "yyyyMMdd"));
                    if (initDayId.compareTo(dayId) != 0) {
                        continue;
                    }
                    //生成代办记录
                    salarySettlementAgentRecordBuild(submitTemplateEntry.getKey(), salarySchemeCycleDetailDTO, settlementDate.toString(), schemeEntry.getKey(),
                            timeWindowStartDate, timeWindowEndDate, salarySubmitTemplateConfigDO, addAgentRecordList);
                    //生成配置执行记录
                    salaryHandlerExecutionRecordBuild(existSalaryTaskConfigDOList.get(0), settlementDate.toString(), schemeEntry.getKey(), addHandlerExecutionRecordList);
                }
            }
        }

        hrmsSalarySettlementAgentRecordManage.settlementAgentRecordScheduledBatchSave(addAgentRecordList, addHandlerExecutionRecordList);

        return ReturnT.SUCCESS;
    }

    private void salarySettlementAgentRecordBuild(String paymentCountry, SalarySchemeCycleDetailDTO salarySchemeCycleDetailDTO,
                                                  String paymentMonth, String schemeNo,
                                                  Date submitStartDate, Date submitEndDate,
                                                  HrmsSalarySubmitTemplateConfigDO salarySubmitTemplateConfigDO,
                                                  List<HrmsSalarySettlementAgentRecordDO> addAgentRecordList) {
        HrmsSalarySettlementAgentRecordDO agentRecordDO = new HrmsSalarySettlementAgentRecordDO();
        agentRecordDO.setId(hrmsIdWorker.nextId());
        agentRecordDO.setName(paymentCountry + "_" + paymentMonth + "_" + salarySubmitTemplateConfigDO.getTemplateName());
        agentRecordDO.setPaymentCountry(paymentCountry);
        agentRecordDO.setPaymentMonth(paymentMonth);
        agentRecordDO.setSalarySchemeConfigNo(schemeNo);
        agentRecordDO.setSalarySubmitTemplateConfigId(salarySubmitTemplateConfigDO.getId());
        agentRecordDO.setSalarySubmitTemplateConfigNo(salarySubmitTemplateConfigDO.getTemplateNo());
        agentRecordDO.setSubmitStartDate(submitStartDate);
        agentRecordDO.setSubmitEndDate(submitEndDate);
        agentRecordDO.setDataCollectStartDate(salarySchemeCycleDetailDTO.getCycleStartDate());
        agentRecordDO.setDataCollectEndDate(salarySchemeCycleDetailDTO.getCycleEndDate());
        agentRecordDO.setNoticeNumber(BusinessConstant.N);
        agentRecordDO.setIsNeedNotice(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(agentRecordDO);
        addAgentRecordList.add(agentRecordDO);
    }

    private void salaryHandlerExecutionRecordBuild(HrmsSalaryTaskConfigDO salaryTaskConfigDO, String paymentMonth, String schemeNo, List<HrmsSalaryHandlerExecutionRecordDO> addHandlerExecutionRecordList) {
        HrmsSalaryHandlerExecutionRecordDO executionRecordDO = new HrmsSalaryHandlerExecutionRecordDO();
        executionRecordDO.setId(hrmsIdWorker.nextId());
        executionRecordDO.setSalaryTaskConfigId(salaryTaskConfigDO.getId());
        executionRecordDO.setPaymentCountry(salaryTaskConfigDO.getCountry());
        executionRecordDO.setPaymentMonth(paymentMonth);
        executionRecordDO.setSchemeNo(schemeNo);
        executionRecordDO.setSource(SalaryHandlerExecultionSourceEnum.AUTO.getCode());
        BaseDOUtil.fillDOInsert(executionRecordDO);
        addHandlerExecutionRecordList.add(executionRecordDO);
    }

    @Data
    private static class SalarySettlementAgentRecordHandlerParam {
        /**
         * 国家
         */
        private String countryList;

        /**
         * 测试使用，模拟执行时间
         */
        private Date testDate;
    }
}
