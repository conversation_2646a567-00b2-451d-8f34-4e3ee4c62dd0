package com.imile.hrms.job.calendar;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.attendance.AttendanceDayTypeEnum;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigDao;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigDetailDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.leave.HrmsCompanyLegalLeaveConfigDao;
import com.imile.hrms.dao.leave.model.HrmsCompanyLegalLeaveConfigDO;
import com.imile.hrms.dao.leave.query.LegalLeaveConfigQuery;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} LegalLeaveSetAttendanceConfigIdHandler
 * {@code @since:} 2025-02-18 10:11
 * {@code @description:}查询日历详情中法假数据不存在法定假期表的数据
 */
@Slf4j
@Component
public class QueryAttendanceConfigNotExistLegalLeaveDataHandler {
    @Resource
    private HrmsAttendanceConfigDao attendanceConfigDao;
    @Resource
    private HrmsCompanyLegalLeaveConfigDao legalLeaveConfigDao;
    @Resource
    private HrmsAttendanceConfigDetailDao attendanceConfigDetailDao;
    @Resource
    private IHrmsIdWorker iHrmsIdWorker;

    @XxlJob(BusinessConstant.JobHandler.QUERY_ATTENDANCE_CONFIG_NOT_EXIST_LEGAL_LEAVE_DATA_HANDLER)
    public ReturnT<String> queryAttendanceConfigNotExistLegalLeaveDataHandler(String content) {
        QueryAttendanceConfigNotExistLegalLeaveDataHandler.QueryAttendanceConfigNotExistLegalLeaveDataParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, QueryAttendanceConfigNotExistLegalLeaveDataHandler.QueryAttendanceConfigNotExistLegalLeaveDataParam.class) : new QueryAttendanceConfigNotExistLegalLeaveDataHandler.QueryAttendanceConfigNotExistLegalLeaveDataParam();
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("param is null");
            log.info("param is null");
            return ReturnT.FAIL;
        }

        if (ObjectUtil.isEmpty(param.getCountryList())) {
            XxlJobLogger.log("countryList param is empty");
            log.info("countryList param is empty");
            return ReturnT.FAIL;
        }
        if (ObjectUtil.isNull(param.getIsSave())) {
            XxlJobLogger.log("isSave param is null");
            log.info("isSave param is null");
            return ReturnT.FAIL;
        }
        if (ObjectUtil.isNull(param.getYear())) {
            XxlJobLogger.log("year param is null");
            log.info("year param is null");
            return ReturnT.FAIL;
        }

        // 获取国家列表
        List<String> countryList = Arrays.asList(param.getCountryList().split(","));
        // 通过国家查询日历数据
        List<HrmsAttendanceConfigDO> attendanceConfigList = attendanceConfigDao.selectByCountryList(countryList);
        // 获取日历数据id集合
        List<Long> attendanceConfigIdList = attendanceConfigList.stream().map(HrmsAttendanceConfigDO::getId).collect(Collectors.toList());
        // 将attendanceConfigList按照国家分组
        Map<String, List<HrmsAttendanceConfigDO>> countryToAttendanceConfig = attendanceConfigList.stream().collect(Collectors.groupingBy(HrmsAttendanceConfigDO::getCountry));

        // 获取日历详情表数据
        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailList = attendanceConfigDetailDao.selectListByConfigIds(attendanceConfigIdList);
        // 过滤详情数据，只需要2024年之后的法定假期，然后按照日历id分组
        Map<Long, List<HrmsAttendanceConfigDetailDO>> attendanceConfigIdToDetail = attendanceConfigDetailList.stream().filter(item -> {
            String dayType = item.getDayType();
            Integer year = item.getYear();
            return (ObjectUtil.isNotEmpty(dayType) && ObjectUtil.equal(dayType, AttendanceDayTypeEnum.HOLIDAY.name())) && (ObjectUtil.isNotEmpty(year) && year > param.getYear());
        }).collect(Collectors.groupingBy(HrmsAttendanceConfigDetailDO::getAttendanceConfigId));

        // 查询国家的法定假期
        LegalLeaveConfigQuery query = new LegalLeaveConfigQuery();
        query.setLocationCountryList(countryList);
        List<HrmsCompanyLegalLeaveConfigDO> leaveConfigList = legalLeaveConfigDao.queryByCondition(query);
        // 过滤leaveConfigList获取指定年份以后的
        leaveConfigList = leaveConfigList.stream().filter(item -> {
            Integer year = item.getYear();
            return ObjectUtil.isNotEmpty(year) && year > param.getYear();
        }).collect(Collectors.toList());

        // 将leaveConfigList按照国家分组
        Map<String, List<HrmsCompanyLegalLeaveConfigDO>> countryToLegalLeaveConfig = leaveConfigList.stream().collect(Collectors.groupingBy(HrmsCompanyLegalLeaveConfigDO::getLocationCountry));

        // 处理数据:给每一个日历设置一份法定假期
        for (Map.Entry<String, List<HrmsAttendanceConfigDO>> entry : countryToAttendanceConfig.entrySet()) {
            // 日历详情不存在法定假期，但是法定假期表存在数据，value是法定假期的主键id集合
            Map<String, List<Long>> attendanceConfigIdToDayIdOne = Maps.newHashMap();
            // 日历详情存在法定假期，但是法定假期表不存在数据，value是日历详情表中对应的dayId集合
            Map<String, List<Long>> attendanceConfigIdToDayIdTwo = Maps.newHashMap();
            // 日历详情存在法定假期，法定假期表也存在数据，需要比对：获取法定假期中不存在日历详情中的数据，value是法定假期中对应的dayId集合
            Map<String, List<Long>> attendanceConfigIdToDayIdThree = Maps.newHashMap();
            // 日历详情存在法定假期，法定假期表也存在数据，需要比对：获取日历中不存在法定假期的表中的数据，value是日历详情中对应的dayId集合
            Map<String, List<Long>> attendanceConfigIdToDayId = Maps.newHashMap();

            String country = entry.getKey();
            List<HrmsAttendanceConfigDO> value = entry.getValue();
            // 获取该国家下面法定假期数据
            List<HrmsCompanyLegalLeaveConfigDO> legalLeaveConfigList = countryToLegalLeaveConfig.get(country);
            // 防止空指针
            legalLeaveConfigList = CollUtil.isEmpty(legalLeaveConfigList) ? Lists.newArrayList() : legalLeaveConfigList;
            // 获取法定假期id集合
            List<Long> legalLeaveConfigIds = legalLeaveConfigList.stream().map(HrmsCompanyLegalLeaveConfigDO::getId).collect(Collectors.toList());
            if (ObjectUtil.isEmpty(country)) {
                XxlJobLogger.log("country is empty");
                log.info("country is empty");
                continue;
            }
            if (CollUtil.isEmpty(value)) {
                XxlJobLogger.log("country:{} 下面没有设置日历，肯定不存在日历详情数据", country);
                log.info("country:{} 下面没有设置日历，肯定不存在日历详情数据", country);

                if (CollUtil.isNotEmpty(legalLeaveConfigList)) {
                    XxlJobLogger.log("当前国家:{},日历不存在，存在法定假期", country);
                    log.info("当前国家:{},日历不存在，存在法定假期", country);
                }
                continue;
            }

            // 存在日历
            for (HrmsAttendanceConfigDO attendanceConfigDO : value) {
                Long attendanceConfigId = attendanceConfigDO.getId();
                String attendanceConfigName = attendanceConfigDO.getAttendanceConfigName();
                String mapKey = attendanceConfigId + ":" + attendanceConfigName;

                // 获取该日历的日历详情数据
                List<HrmsAttendanceConfigDetailDO> attendanceConfigDetails = attendanceConfigIdToDetail.get(attendanceConfigId);
                // 防止空指针
                attendanceConfigDetails = CollUtil.isEmpty(attendanceConfigDetails) ? Lists.newArrayList() : attendanceConfigDetails;
                // 获取attendanceConfigDetails的dayId集合
                List<Long> dayIdListBak = attendanceConfigDetails.stream().map(HrmsAttendanceConfigDetailDO::getDayId).collect(Collectors.toList());
                List<Long> dayIdList = attendanceConfigDetails.stream().map(HrmsAttendanceConfigDetailDO::getDayId).collect(Collectors.toList());
                if (CollUtil.isEmpty(attendanceConfigDetails)) {
                    XxlJobLogger.log("country:{} 日历id:{} 不存在法假日历详情数据", country, attendanceConfigId);
                    log.info("country:{} 日历id:{} 不存在法假日历详情数据", country, attendanceConfigId);
                    if (CollUtil.isNotEmpty(legalLeaveConfigList)) {
                        // 基本上这种不太可能存在，存在的话打印日志看下。
                        attendanceConfigIdToDayIdOne.put(mapKey, legalLeaveConfigIds);
                        XxlJobLogger.log("当前国家:{} 日历id:{} 不存在日历详情数据,存在法定假期", country, attendanceConfigId);
                        log.info("当前国家:{} 日历id:{} 不存在日历详情数据,存在法定假期", country, attendanceConfigId);
                    }
                    continue;
                }
                // 日历详情不为空，但是法定假期不存在，则需要记录下来：需要业务方新增法定假期信息
                if(CollUtil.isEmpty(legalLeaveConfigList)){
                    attendanceConfigIdToDayIdTwo.put(mapKey, dayIdList);
                    XxlJobLogger.log("country:{} 日历id:{} 存在日历详情数据,但是法定假期不存在。【目标数据：需要捞出来，给业务方看】", country, attendanceConfigId);
                    log.info("country:{} 日历id:{} 存在日历详情数据,但是法定假期不存在。【目标数据：需要捞出来，给业务方看】", country, attendanceConfigId);
                    continue;
                }

                // 日历以及法定假期都存在数据的情况下：分为下面两种情况

                // 情况1：获取法定假期中不存在日历详情中的数据
                List<Long> missingLegalLeaveDayIds = Lists.newArrayList();
                for (HrmsCompanyLegalLeaveConfigDO legalLeaveConfig : legalLeaveConfigList) {
                    Long legalLeaveStartDayId = legalLeaveConfig.getLegalLeaveStartDayId();
                    Long legalLeaveEndDayId = legalLeaveConfig.getLegalLeaveEndDayId();

                    List<Long> legalLeaveDayIdRange = new ArrayList<>();
                    for (long dayId = legalLeaveStartDayId; dayId <= legalLeaveEndDayId; dayId++) {
                        legalLeaveDayIdRange.add(dayId);
                    }
                    missingLegalLeaveDayIds = checkContainment(legalLeaveDayIdRange, dayIdListBak);
                }
                if (CollUtil.isNotEmpty(missingLegalLeaveDayIds)) {
                    attendanceConfigIdToDayIdThree.put(mapKey, missingLegalLeaveDayIds);
                }


                // 情况2：日历详情存在的情况，法定假期也存在，则需要对比日历的dayId是否已经在法定假期里面存在了，存在，则不需处理，不存在，则需要记录下来：需要业务方新增法定假期信息
                for (HrmsAttendanceConfigDetailDO attendanceConfigDetail : attendanceConfigDetails) {
                    Long dayId = attendanceConfigDetail.getDayId();
                    for (HrmsCompanyLegalLeaveConfigDO legalLeaveConfig : legalLeaveConfigList) {
                        Long legalLeaveStartDayId = legalLeaveConfig.getLegalLeaveStartDayId();
                        Long legalLeaveEndDayId = legalLeaveConfig.getLegalLeaveEndDayId();
                        // 判断dayId是否与legalLeaveStartDayId、legalLeaveEndDayId存在交集，不在则需要记录下来：需要业务方新增法定假期信息
                        if (dayId >= legalLeaveStartDayId && dayId <= legalLeaveEndDayId) {
                            // 将dayIdList去掉dayId
                            dayIdList.remove(dayId);
                            XxlJobLogger.log("country:{} 日历id:{} 日历详情dayId:{} 已经在法定假期里面存在了，不需要处理", country, attendanceConfigId, dayId);
                            log.info("country:{} 日历id:{} 日历详情dayId:{} 已经在法定假期里面存在了，不需要处理", country, attendanceConfigId, dayId);
                            break;
                        }
                    }
                }

                // 如果处理之后的dayIdList不为空，该日历存在日历详情不在法定假期中的数据
                if (CollUtil.isNotEmpty(dayIdList)) {
                    attendanceConfigIdToDayId.put(mapKey, dayIdList);
                }

            }

            // 打印日志
            log.info("country:{},attendanceConfigIdToDayIdOne:{},attendanceConfigIdToDayIdTwo:{},attendanceConfigIdToDayIdThree:{},attendanceConfigIdToDayId:{}",
                    country, JSON.toJSONString(attendanceConfigIdToDayIdOne), JSON.toJSONString(attendanceConfigIdToDayIdTwo), JSON.toJSONString(attendanceConfigIdToDayIdThree), JSON.toJSONString(attendanceConfigIdToDayId));
        }

        //XxlJobLogger.log("attendanceConfigIdToDayIdOne:{}", JSON.toJSONString(attendanceConfigIdToDayIdOne));
        //XxlJobLogger.log("attendanceConfigIdToDayIdTwo:{}", JSON.toJSONString(attendanceConfigIdToDayIdTwo));
        //XxlJobLogger.log("attendanceConfigIdToDayIdThree:{}", JSON.toJSONString(attendanceConfigIdToDayIdThree));
        //XxlJobLogger.log("attendanceConfigIdToDayId:{}", JSON.toJSONString(attendanceConfigIdToDayId));
        //
        //log.info("attendanceConfigIdToDayIdOne:{}", JSON.toJSONString(attendanceConfigIdToDayIdOne));
        //log.info("attendanceConfigIdToDayIdTwo:{}", JSON.toJSONString(attendanceConfigIdToDayIdTwo));
        //log.info("attendanceConfigIdToDayIdThree:{}", JSON.toJSONString(attendanceConfigIdToDayIdThree));
        //log.info("attendanceConfigIdToDayId:{}", JSON.toJSONString(attendanceConfigIdToDayId));

        // 落库法定假期数据
        List<HrmsCompanyLegalLeaveConfigDO> targetLegalLeaveConfigList = Lists.newArrayList();
        // 处理attendanceConfigIdToDayId，应该不需要代码处理，需要业务方处理
        //for (Map.Entry<Long, List<Long>> entry : attendanceConfigIdToDayId.entrySet()) {
        //    Long attendanceConfigId = entry.getKey();
        //    List<Long> dayIdList = entry.getValue();
        //
        //    // 获取日历数据
        //    List<HrmsAttendanceConfigDO> attendanceConfigInfoList = attendanceConfigList.stream().filter(attendanceConfigDO -> attendanceConfigDO.getId().equals(attendanceConfigId)).collect(Collectors.toList());
        //    if (CollUtil.isEmpty(attendanceConfigInfoList)) {
        //        XxlJobLogger.log("attendanceConfigId:{} 不存在日历数据", attendanceConfigId);
        //        continue;
        //    }
        //    HrmsAttendanceConfigDO attendanceConfig = attendanceConfigInfoList.get(0);
        //    Collections.sort(dayIdList);
        //    Long start = dayIdList.get(0);
        //    Long end = dayIdList.get(0);
        //    // 遍历集合
        //    for (int i = 1; i < dayIdList.size(); i++) {
        //        Long current = dayIdList.get(i);
        //        if (current == end + 1) {
        //            // 当前日期与前一个日期连续
        //            end = current;
        //        } else {
        //            // 当前日期不连续，保存当前的连续段并计算差值
        //            buildLegalLeaveConfigList(attendanceConfigId, end, start, attendanceConfig, targetLegalLeaveConfigList);
        //            // 更新start和end
        //            start = current;
        //            end = current;
        //        }
        //    }
        //
        //    // 添加最后一段连续日期并计算差值
        //    buildLegalLeaveConfigList(attendanceConfigId, end, start, attendanceConfig, targetLegalLeaveConfigList);
        //}

        XxlJobLogger.log("targetLegalLeaveConfigList:{}", JSON.toJSONString(targetLegalLeaveConfigList));
        log.info("targetLegalLeaveConfigList:{}", JSON.toJSONString(targetLegalLeaveConfigList));

        // 落库
        if (Boolean.TRUE.equals(param.getIsSave())) {
            XxlJobLogger.log("开始落库");
            log.info("开始落库");
            //legalLeaveConfigDao.saveBatch(targetLegalLeaveConfigList);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 获取法定假期中不存在日历详情中的数据
     *
     * @param legalLeaveDayIdRange 法定假期范围日期数据
     * @param dayIdListBak         日历详情数据
     * @return List<Long>
     */
    private List<Long> checkContainment(List<Long> legalLeaveDayIdRange, List<Long> dayIdListBak) {
        List<Long> missingDates = new ArrayList<>();
        for (Long date : legalLeaveDayIdRange) {
            if (!dayIdListBak.contains(date)) {
                missingDates.add(date);
            }
        }
        return missingDates;
    }

    /**
     * 构建法定假期集合
     *
     * @param attendanceConfigId         日历id
     * @param end                        尾部日期
     * @param start                      开头日期
     * @param attendanceConfig           日历数据
     * @param targetLegalLeaveConfigList 目标集合
     */
    private void buildLegalLeaveConfigList(Long attendanceConfigId, Long end, Long start, HrmsAttendanceConfigDO attendanceConfig, List<HrmsCompanyLegalLeaveConfigDO> targetLegalLeaveConfigList) {
        long legalLeaveDuration = end - start + 1;
        HrmsCompanyLegalLeaveConfigDO legalLeaveConfig = new HrmsCompanyLegalLeaveConfigDO();
        legalLeaveConfig.setId(iHrmsIdWorker.nextId());
        // 先注释
        //legalLeaveConfig.setAttendanceConfigId(attendanceConfigId);
        legalLeaveConfig.setLocationCountry(attendanceConfig.getCountry());
        legalLeaveConfig.setLegalLeaveStartDayId(start);
        legalLeaveConfig.setLegalLeaveEndDayId(end);
        legalLeaveConfig.setLegalLeaveDuration(Integer.parseInt(String.valueOf(legalLeaveDuration)));
        targetLegalLeaveConfigList.add(legalLeaveConfig);
    }

    @Data
    private static class QueryAttendanceConfigNotExistLegalLeaveDataParam {
        /**
         * 所属国
         */
        private String countryList;

        /**
         * 处理哪一年及以后的数据
         */
        private Integer year = 2024;

        /**
         * 标志
         */
        private Boolean isSave = true;
    }


}
