package com.imile.hrms.job.punch;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceCycleConfigDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.punch.model.HrmsAttendanceUserCardConfigDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.manage.punch.HrmsAttendanceUserCardConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryEmployeeConfigManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.approval.dto.AttendanceDayCycleDTO;
import com.imile.hrms.service.attendance.HrmsAttendanceBaseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 用户考勤周期补卡次数配置
 * @author: taokang
 * @createDate: 2023-7-22
 * @version: 1.0
 */
@Slf4j
@Component
public class UserCardConfigHandler {
    @Autowired
    private HrmsSalaryEmployeeConfigManage hrmsSalaryEmployeeConfigManage;
    @Autowired
    private HrmsSalaryConfigManage hrmsSalaryConfigManage;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsAttendanceUserCardConfigManage hrmsAttendanceUserCardConfigManage;
    @Autowired
    private HrmsAttendanceBaseService hrmsAttendanceBaseService;
    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @XxlJob(BusinessConstant.JobHandler.USER_CARD_CONFIG_HANDLER)
    public ReturnT<String> userCardConfigHandler(String content) {
        UserCardConfigHandler.UserCardConfigHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, UserCardConfigHandler.UserCardConfigHandlerParam.class) : new UserCardConfigHandler.UserCardConfigHandlerParam();

        List<HrmsAttendanceUserCardConfigDO> addUserCardConfigDOList = new ArrayList<>();
        if (param.getInitDayId() == null) {
            param.setInitDayId(Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd")));
        }
        List<HrmsUserInfoDO> userInfoDOList;
        if (StringUtils.isNotBlank(param.getUserCodeList())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodeList().split(","));
            userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList).stream()
                    .filter(item -> StringUtils.isNotBlank(item.getUserCode()) && StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()) && StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())).collect(Collectors.toList());
        } else {
            if (StringUtils.isEmpty(param.getCountryList())) {
                XxlJobLogger.log("公司不能为空,content:{}", content);
                return ReturnT.SUCCESS;
            }

            List<String> countryList = Arrays.asList(param.getCountryList().split(","));
            UserDaoQuery query = UserDaoQuery.builder().locationCountryList(countryList).status(StatusEnum.ACTIVE.getCode()).workStatus(WorkStatusEnum.ON_JOB.getCode()).build();
            userInfoDOList = hrmsUserInfoDao.userList(query).stream()
                    .filter(item -> StringUtils.isNotBlank(item.getUserCode())).collect(Collectors.toList());
            //userInfoDOList = hrmsUserInfoManage.selectUserInfoByCompanyIdList(countryList).stream()
            //        .filter(item -> StringUtils.isNotBlank(item.getUserCode()) && StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()) && StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())).collect(Collectors.toList());
        }
        List<Long> userIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
        //查询员工所有的记录
        List<HrmsAttendanceUserCardConfigDO> userCardConfigDOS = hrmsAttendanceUserCardConfigManage.selectByUserIdList(userIdList).stream()
                .sorted(Comparator.comparing(HrmsAttendanceUserCardConfigDO::getCycleStartDate)).collect(Collectors.toList());
        Map<Long, List<HrmsAttendanceUserCardConfigDO>> userCardConfigMap = userCardConfigDOS.stream().collect(Collectors.groupingBy(HrmsAttendanceUserCardConfigDO::getUserId));

        //List<HrmsSalaryEmployeeConfigDO> salaryEmployeeConfigDOS = hrmsSalaryEmployeeConfigManage.listSalaryEmployeeConfigByUserIdList(userIdList);
        //List<Long> salaryIdList = salaryEmployeeConfigDOS.stream().map(item -> item.getSalaryConfigId()).collect(Collectors.toList());
        //List<HrmsSalaryConfigDO> salaryConfigDOS = hrmsSalaryConfigManage.selectSalaryConfigByIdList(salaryIdList);


        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
            //HrmsSalaryConfigDO salaryConfigDO = null;
            //List<HrmsSalaryEmployeeConfigDO> userSalaryEmployeeConfigDOS = salaryEmployeeConfigDOS.stream().filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());
            //if (CollectionUtils.isEmpty(userSalaryEmployeeConfigDOS)) {
            //    salaryConfigDO = new HrmsSalaryConfigDO();
            //    salaryConfigDO.setCycleStart(BusinessConstant.ONE.toString());
            //    salaryConfigDO.setCycleEnd(CycleTypeEnum.END_OF_MONTH_CODE);
            //} else {
            //    List<HrmsSalaryConfigDO> userSalaryConfigDOS = salaryConfigDOS.stream().filter(item -> item.getId().equals(userSalaryEmployeeConfigDOS.get(0).getSalaryConfigId())).collect(Collectors.toList());
            //    if (CollectionUtils.isEmpty(userSalaryConfigDOS)) {
            //        salaryConfigDO = new HrmsSalaryConfigDO();
            //        salaryConfigDO.setCycleStart(BusinessConstant.ONE.toString());
            //        salaryConfigDO.setCycleEnd(CycleTypeEnum.END_OF_MONTH_CODE);
            //    } else {
            //        salaryConfigDO = userSalaryConfigDOS.get(0);
            //    }
            //}
            // 考勤周期
            //HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfig(userInfoDO.getId());
            HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfigUserCard(userInfoDO.getId());
            AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceCycleConfigDay(param.getInitDayId(), userAttendanceCycleConfig);

            //AttendanceDayCycleDTO attendanceDayCycleDTO = hrmsAttendanceBaseService.getUserAttendanceDayCycle(param.getInitDayId(), salaryConfigDO);
            if (attendanceDayCycleDTO == null) {
                continue;
            }
            //新入职的用户，之前可能没有
            List<HrmsAttendanceUserCardConfigDO> userCardList = userCardConfigMap.get(userInfoDO.getId());
            if (CollectionUtils.isEmpty(userCardList)) {
                addCardConfigBuild(userInfoDO.getId(), attendanceDayCycleDTO, addUserCardConfigDOList);
                continue;
            }
            //存在历史数据，看有没有相同周期的
            //看本周期是否以及存在了，存在了不做任务操作
            List<HrmsAttendanceUserCardConfigDO> existList = userCardList.stream().filter(item -> DateUtil.format(item.getCycleStartDate(), "yyyyMMdd").equals(DateUtil.format(attendanceDayCycleDTO.getAttendanceStartDate(), "yyyyMMdd"))).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(existList)) {
                //存在数据了
                continue;
            }
            //没存在
            addCardConfigBuild(userInfoDO.getId(), attendanceDayCycleDTO, addUserCardConfigDOList);
        }
        hrmsAttendanceUserCardConfigManage.batchSave(addUserCardConfigDOList);
        return ReturnT.SUCCESS;

    }


    private void addCardConfigBuild(Long userId, AttendanceDayCycleDTO attendanceDayCycleDTO, List<HrmsAttendanceUserCardConfigDO> addUserCardConfigDOList) {
        HrmsAttendanceUserCardConfigDO addDO = new HrmsAttendanceUserCardConfigDO();
        addDO.setId(iHrmsIdWorker.nextId());
        addDO.setUserId(userId);
        addDO.setUsedCardCount(BusinessConstant.ZERO);
        addDO.setCycleStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
        addDO.setCycleEndDate(DateUtil.offset(attendanceDayCycleDTO.getAttendanceEndDate(), DateField.MILLISECOND, -999));
        BaseDOUtil.fillDOInsert(addDO);
        addUserCardConfigDOList.add(addDO);
    }

    @Data
    private static class UserCardConfigHandlerParam {
        /**
         * 初始化哪个月份的考勤周期  默认今天所属的周期
         */
        private Long initDayId;

        /**
         * 国家
         */
        private String countryList;

        /**
         * 定时任务每天执行：哪些用户
         */
        private String userCodeList;
    }
}
