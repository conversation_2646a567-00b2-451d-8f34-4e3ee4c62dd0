package com.imile.hrms.punch;

import com.baidu.aip.imageprocess.AipImageProcess;
import org.json.JSONObject;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;

public class Sample {
    //设置APPID/AK/SK
    public static final String APP_ID = "103650971";
    public static final String API_KEY = "9HWSyrBcSig3UmR170Xg5KqC";
    public static final String SECRET_KEY = "GLkKtxXH07RNX290JuioXqnKHonyriNk";

    public static void main(String[] args) {
        // 初始化一个AipImageProcess
        AipImageProcess client = new AipImageProcess(APP_ID, API_KEY, SECRET_KEY);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);

        HashMap<String, String> options = new HashMap<>();
        // 调用接口
        String image = "D:\\doc\\test2.png";
        JSONObject res = client.imageDefinitionEnhance(image, options);
        String imageStr = res.getString("image");
        byte[] decodedBytes = Base64.getDecoder().decode(imageStr);
        try (FileOutputStream fos = new FileOutputStream("D:\\doc\\output2.png")) {
            fos.write(decodedBytes);
            System.out.println("文件已成功写入。");
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}