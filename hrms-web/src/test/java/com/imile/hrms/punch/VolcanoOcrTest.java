package com.imile.hrms.punch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.io.BaseEncoding;
import com.google.common.io.Files;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.imile.hrms.manage.ocr.volcano.entity.MultiLanguageOcrRequest;
import com.imile.hrms.manage.ocr.volcano.entity.MultiLanguageOcrResponseData;
import com.imile.hrms.manage.ocr.volcano.entity.ValcanoResponse;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;

import java.io.File;
import java.lang.reflect.Type;

/**
 * 火山引擎OCR
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
public class VolcanoOcrTest {
    //设置AK/SK
    public static final String ACCESS_KEY_ID = "AKLTODAzOThmYzNjOGVhNDE2MGFhZWNkZjgwNGEyMmMxYWQ";
    public static final String SECRET_ACCESS_KEY = "TURCaE9EUmtOVGc0WldGa05HWXhNR0prWkdGaFlqUTFaVGxsTURBNFlqaw==";
    public static final String OCR_URL = "https://visual.volcengineapi.com?Action=MultiLanguageOCR&Version=2022-08-31";

    public static void main(String[] args) throws Exception {
        String filePath = "/Users/<USER>/Desktop/ocr/1.jpg";
        byte[] fileBytes = Files.toByteArray(new File(filePath));
        String base64String = BaseEncoding.base64().encode(fileBytes);

//        Credentials credentials = new Credentials();
//        credentials.setAccessKeyId(ACCESS_KEY_ID);
//        credentials.setSecretAccessKey(SECRET_ACCESS_KEY);
//        credentials.setRegion("cn-north-1");
//        credentials.setService("cv");
//
//        /* create http client */
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        /* prepare request */
//        HttpPost request = new HttpPost();
//        request.setURI(new URI(OCR_URL));
//        List<NameValuePair> nameValuePairList = Lists.newArrayList();
//        nameValuePairList.add(new BasicNameValuePair("image_base64", base64String));
//        request.setEntity(new UrlEncodedFormEntity(nameValuePairList, Consts.UTF_8));
//        Signer signer = new Signer();
//        signer.sign(request, credentials);
//        /* launch request */
//        CloseableHttpResponse response = httpClient.execute(request);
//        /* status code */
//        System.out.println(response.getStatusLine().getStatusCode());   // 200
//        /* get response body */
//        HttpEntity entity = response.getEntity();
//        if (entity != null) {
//            String result = EntityUtils.toString(entity);
//            System.out.println(result);
//        }
//        /* close resources */
//        response.close();
//        httpClient.close();

        IVisualService visualService = VisualServiceImpl.getInstance();
        visualService.setAccessKey(ACCESS_KEY_ID);
        visualService.setSecretKey(SECRET_ACCESS_KEY);
        MultiLanguageOcrRequest request = new MultiLanguageOcrRequest();
        request.setImageBase64(base64String);
        String action = "MultiLanguageOCR";
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(request));
        try {
            String jsonStr = visualService.ocrApi(action, jsonObject);
            Gson gson =  new Gson();
            Type type = new TypeToken<ValcanoResponse<MultiLanguageOcrRequest>>(){}.getType();
            ValcanoResponse<MultiLanguageOcrResponseData> multiLanguageOcrRequestBody = gson.fromJson(jsonStr, type);
            System.out.println(multiLanguageOcrRequestBody.getData().getOcr_infos());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
