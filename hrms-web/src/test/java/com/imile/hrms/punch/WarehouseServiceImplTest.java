/*package com.imile.hrms.punch;

import cn.hutool.core.bean.BeanUtil;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.FaceErrorCodeEnum;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.ipep.IpepIntegration;
import com.imile.hrms.integration.ipep.OssApiVo;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.face.FaceEngineService;
import com.imile.hrms.service.punch.WarehouseService;
import com.imile.hrms.service.punch.param.warehouse.AddUserParam;
import com.imile.hrms.service.punch.param.warehouse.FaceCheckParam;
import com.imile.hrms.service.punch.param.warehouse.FaceSearchParam;
import com.imile.hrms.service.punch.param.warehouse.InOrOutParam;
import com.imile.hrms.service.punch.param.warehouse.OcUserParam;
import com.imile.hrms.service.punch.param.warehouse.OutParam;
import com.imile.hrms.service.punch.param.warehouse.ReportParam;
import com.imile.hrms.service.punch.vo.warehouse.CertificatesVO;
import com.imile.hrms.service.punch.vo.warehouse.DateReportVO;
import com.imile.hrms.service.punch.vo.warehouse.MonthReportVO;
import com.imile.hrms.service.punch.vo.warehouse.RecordListVO;
import com.imile.hrms.service.punch.vo.warehouse.UserFaceSearchVO;
import com.imile.hrms.service.punch.vo.warehouse.UserVO;
import com.imile.hrms.service.punch.vo.warehouse.VendorVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehouseOcUserVO;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;*/

/**
 * <AUTHOR>
 * @project hrms
 * @description 测试
 * @date 2024/7/6 10:44:09
 */
/*
public class WarehouseServiceImplTest extends ApplicationTest {
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Test
    public void getVendorListByOcList() {
        List<VendorVO> vendorListByOcList = warehouseService.getVendorListByOcList(new ArrayList<>());
        System.out.println(vendorListByOcList);
    }

    @Resource
    private ConverterService converterService;

    @Test
    public void getUser() {
        RecordListVO user = warehouseService.getUser(1105894103515664384L);
        converterService.withAnnotationForSingle(user);
        System.out.println(user);
    }

    @Test
    public void saveOrUpdateUser() {
        AddUserParam addUserParam = new AddUserParam();
        addUserParam.setUserId(1105894103515664384L);
        addUserParam.setUserName("xxx");
        addUserParam.setSex(0);
*/
/*        addUserParam.setCertificatesCode("12232343345t3424f");
        addUserParam.setCertificatesPhoto("23421");
        addUserParam.setExpirationDate(new Date());*//*

        addUserParam.setOcId(81L);
        addUserParam.setVendorId(14L);
        UserVO l = warehouseService.saveOrUpdateUser(addUserParam);
        System.out.println(l);
    }


    @Test
    public void in() {
        InOrOutParam inOrOutParam = new InOrOutParam();
        inOrOutParam.setOcId(81L);
        inOrOutParam.setUserIdList(Collections.singletonList(1075438809920831488L));
        warehouseService.in(inOrOutParam);
    }

    @Test
    public void inFor() {
        List<HrmsUserInfoDO> onJobUserByDeptIdList = hrmsUserInfoDao.getOnJobUserByDeptIdList(Collections.singletonList(1097580729203687807L));
        List<Long> collect = onJobUserByDeptIdList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
        InOrOutParam inOrOutParam = new InOrOutParam();
        inOrOutParam.setOcId(81L);
        inOrOutParam.setUserIdList(collect);
        warehouseService.in(inOrOutParam);
    }

    @Test
    public void out() {
        OutParam inOrOutParam = new OutParam();
        inOrOutParam.setOcId(81L);
        inOrOutParam.setUserIdList(Collections.singletonList(1105192866805841921L));
        warehouseService.out(inOrOutParam);
    }

    @Test
    public void inThenOut() throws InterruptedException {
        InOrOutParam inOrOutParam = new InOrOutParam();
        inOrOutParam.setOcId(87L);
        inOrOutParam.setUserIdList(Collections.singletonList(1075438809920831488L));
        warehouseService.in(inOrOutParam);
        Thread.sleep(60000);

        OutParam outParam = new OutParam();
        outParam.setOcId(87L);
        outParam.setUserIdList(Collections.singletonList(1075438809920831488L));
        warehouseService.out(outParam);
    }


    @Test
    public void dateReport() {
        HrmsUserInfoDO userByUserCode = hrmsUserInfoDao.getByUserCode("2103410601");
        RequestInfoHolder.setLoginInfo(BeanUtil.copyProperties(userByUserCode, UserContext.class));
        ReportParam reportParam = new ReportParam();
        reportParam.setStartDate(new Date());
        reportParam.setEndDate(new Date());
        reportParam.setCurrentPage(1);
        reportParam.setShowCount(100);
        PaginationResult<DateReportVO> dateReportVOPaginationResult = warehouseService.dateReport(reportParam);
        System.out.println(dateReportVOPaginationResult);
    }

    @Test
    public void monthReport() {
        HrmsUserInfoDO userByUserCode = hrmsUserInfoDao.getByUserCode("2103410601");
        RequestInfoHolder.setLoginInfo(BeanUtil.copyProperties(userByUserCode, UserContext.class));
        ReportParam reportParam = new ReportParam();
        reportParam.setOcIdList(new ArrayList<>());
        reportParam.setVendorIdList(new ArrayList<>());
        reportParam.setStartDate(new Date());
        reportParam.setEndDate(new Date());
        reportParam.setCurrentPage(1);
        reportParam.setShowCount(100);
        PaginationResult<MonthReportVO> dateReportVOPaginationResult = warehouseService.monthReport(reportParam);
        System.out.println(dateReportVOPaginationResult);
    }


    @Test
    public void ocUserList() {
       */
/* HrmsUserInfoDO userByUserCode = hrmsUserInfoDao.getByUserCode("2103410601");
        RequestInfoHolder.setLoginInfo(BeanUtil.copyProperties(userByUserCode, UserContext.class));
        OcUserParam ocUserParam = new OcUserParam();
        ocUserParam.setCertificatesCode("");
        ocUserParam.setSearchUserKey("Charles");
        PaginationResult<WarehouseOcUserVO> reportVOPaginationResult = warehouseService.ocUserList(ocUserParam);
        System.out.println(reportVOPaginationResult);*//*

    }

    @Resource
    private IpepIntegration ipepIntegration;

    @Test
    public void certificatesUpload() throws IOException {
     */
/*   MultipartFile multipartFile = getMultipartFile("hermes/ent/image/default/2024/7/202407081234254488437760001.png");
        CertificatesVO certificatesVO = warehouseService.certificatesUpload(multipartFile, BusinessConstant.LABOR_DISPATCH, CountryCodeEnum.BRA.getCode());
        System.out.println(certificatesVO);*//*

    }

    private @NotNull MultipartFile getMultipartFile(String fileKey) throws IOException {
        OssApiVo ossApiVo = ipepIntegration.getUrlByFileKey(fileKey, 1);
        if (Objects.isNull(ossApiVo)) {
            throw BusinessException.get(FaceErrorCodeEnum.FACE_QUERY_OSS_FILE_URL_FAIL.getCode(), FaceErrorCodeEnum.FACE_QUERY_OSS_FILE_URL_FAIL.getDesc());
        }
        URL url = new URL(ossApiVo.getFileUrl());
        URLConnection connection = url.openConnection();
        // 获取输入流
        InputStream inputStream = connection.getInputStream();
        // 读取输入流中的数据，并保存到字节数组中
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        // 将字节数组转换为字节数组
        byte[] bytes = byteArrayOutputStream.toByteArray();
        // 创建ByteArrayInputStream对象，将字节数组传递给它
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        // 创建MultipartFile对象，将ByteArrayInputStream对象作为构造函数的参数
        return new MockMultipartFile("file", "filename.jpg", "image/jpg", byteArrayInputStream);
    }

    @Resource
    private FaceEngineService faceEngineService;

    @Test
    public void faceInput() {
//        faceEngineService.faceInput("hermes/ent/image/default/2024/7/202407081234210527547461633.jpg", "2103410601", null, true);
        System.out.println();
    }


    @Test
    public void faceCheck1() throws IOException {
        MultipartFile multipartFile = getMultipartFile("hermes/ent/image/default/2024/7/202407081234210527547461633.jpg");
        UserFaceSearchVO userFaceSearchVO = faceEngineService.faceCheck(new FaceCheckParam());
        System.out.println(userFaceSearchVO);
    }

    @Test
    public void faceCheck2() throws IOException {
        MultipartFile multipartFile = getMultipartFile("hermes/ent/image/default/2024/7/202407081234254488437760001.png");
        FaceCheckParam faceCheckParam = new FaceCheckParam();
        UserFaceSearchVO userFaceSearchVO = faceEngineService.faceCheck(faceCheckParam);
        System.out.println(userFaceSearchVO);
    }


    @Test
    public void faceSearch1() throws IOException {
        MultipartFile multipartFile = getMultipartFile("hermes/ent/image/default/2024/7/202407081234254488437760001.png");
        FaceSearchParam faceSearchParam = new FaceSearchParam();
        faceSearchParam.setFile(multipartFile);
        faceSearchParam.setEmployeeType(BusinessConstant.LABOR_DISPATCH);
        faceSearchParam.setFaceTime(new Date());
        UserFaceSearchVO userFaceSearchVO = faceEngineService.faceRecognition(faceSearchParam);
        System.out.println(userFaceSearchVO);
    }

    @Test
    public void faceSearch2() throws IOException {
        MultipartFile multipartFile = getMultipartFile("hermes/ent/image/default/2024/7/202407081234210527547461633.jpg");
        FaceSearchParam faceSearchParam = new FaceSearchParam();
        faceSearchParam.setFile(multipartFile);
        faceSearchParam.setEmployeeType(BusinessConstant.LABOR_DISPATCH);
        faceSearchParam.setFaceTime(new Date());
        UserFaceSearchVO userFaceSearchVO = faceEngineService.faceRecognition(faceSearchParam);
        System.out.println(userFaceSearchVO);
    }


}
*/
