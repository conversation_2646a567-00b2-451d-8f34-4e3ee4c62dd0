package com.imile.hrms.punch;

import com.imile.hrms.dao.punch.param.HrmsAttendancePunchInEncryptParam;
import com.imile.hrms.dao.punch.param.HrmsAttendancePunchInParam;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.punch.HrmsAttendanceMobilePunchService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/1/8 
 * @Description
 */
public class PunchInDecryptTest extends ApplicationTest {

    @Resource
    private HrmsAttendanceMobilePunchService mobilePunchService;


    @Test
    public void testpunchInDecrypt(){
        HrmsAttendancePunchInEncryptParam encryptParam = new HrmsAttendancePunchInEncryptParam();
        encryptParam.setIvStr("uigAse0clYPyTITbJYlpfA==");
        encryptParam.setContent("Ll4CqKGO4XC6aXst/yHpbZe2EUyMuuxrlF3p+u//2J+0wuCFswMw1IIISwNot80LivjigcHzWPFknrY/2cXdgexRlT45bbksbidhfCvFRCsn06vMkC9PdT8oLvLKjse9tUmab1/k8EcL2g+An3KaVN7LuC4Pft7GoaLEDHFa3MAV/pC5nO2uP20Pks83HAAy");

        HrmsAttendancePunchInParam punchInParam = mobilePunchService.punchInDecrypt(encryptParam);
        System.out.println(punchInParam);

    }

}
