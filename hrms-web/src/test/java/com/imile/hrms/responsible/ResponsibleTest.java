package com.imile.hrms.responsible;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.punch.PunchTypeEnum;
import com.imile.hrms.common.enums.responible.CompanyStatusEnum;
import com.imile.hrms.dao.punch.dto.AttendancePunchConfigDTO;
import com.imile.hrms.dao.punch.dto.UserPunchInfoDTO;
import com.imile.hrms.dao.punch.param.CheckUserPunchParam;
import com.imile.hrms.dao.punch.param.UserPunchParam;
import com.imile.hrms.dao.punch.query.UserPunchConfigQuery;
import com.imile.hrms.dao.responsible.mapper.HrmsResponsibleSettlementSubjectMapper;
import com.imile.hrms.dao.responsible.model.HrmsResponsibleSettlementSubjectDO;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.punch.EmployeePunchCardService;
import com.imile.hrms.service.punch.HrmsAttendanceEmployeePunchRecordService;
import com.imile.hrms.service.punch.dto.PunchCardRecordQueryDTO;
import com.imile.hrms.service.responsible.HrmsResponsibleService;
import com.imile.hrms.service.responsible.dto.*;
import org.junit.After;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * HrmsAttendanceEmployeePunchRecordService Tester.
 * {@link HrmsAttendanceEmployeePunchRecordService}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 12/30/2021
 */
public class ResponsibleTest extends ApplicationTest {

    @Autowired
    private HrmsResponsibleService hrmsResponsibleService;


    @Autowired
    private HrmsResponsibleSettlementSubjectMapper hrmsResponsibleSettlementSubjectMapper;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }


    @Test
    void init(){
        HrmsResponsibleSettlementSubjectDO add = new HrmsResponsibleSettlementSubjectDO();
        add.setIsDelete(IsDeleteEnum.NO.getCode());
        add.setStatus(CompanyStatusEnum.WORKING.getCode());
        add.setSettlementShortName("");
        add.setSettlementCenterName("默认设置");
        add.setCompanyOrgId(null);
        add.setRegisterCountry("all");
        BaseDOUtil.fillDOInsert(add);

        hrmsResponsibleSettlementSubjectMapper.insert(add);
    }


    @Test
    void syncTest() {
        hrmsResponsibleService.sync();
    }

    @Test
    void editTest(){
        ResponsibleUpdateDTO updateDTO = new ResponsibleUpdateDTO();
        updateDTO.setId(38L);
        updateDTO.setFinanceManagerPostIds(Arrays.asList(1233L ));
        updateDTO.setAccountingPostIds( Arrays.asList(1234L,1035L,1047L ));
        hrmsResponsibleService.edit(updateDTO);
    }

    @Test
    void pageListTest(){
//        ResponsibleSettlementSubjectQueryDTO queryDTO = new ResponsibleSettlementSubjectQueryDTO();
//        queryDTO.setAccountPostId(1234L);
//        PaginationResult<ResponsibleSettlementSubjectListDTO> result =
//                hrmsResponsibleService.pageList(queryDTO);
//        result.getResults().forEach(System.out::println);

        ResponsibleSettlementSubjectQueryDTO queryDTO1 = new ResponsibleSettlementSubjectQueryDTO();
//        queryDTO1.setAccountPostName("AM");

//        queryDTO1.setCompanyOrgId(null);
//        queryDTO1.setSettlementShortName("imile");
//        queryDTO1.setSettlementCenterName("imile");
        PaginationResult<ResponsibleSettlementSubjectListDTO> result2 =
                hrmsResponsibleService.pageList(queryDTO1);
        result2.getResults().forEach(System.out::println);


//        queryDTO1.setCompanyOrgId();
//        queryDTO1.setSettlementShortName("123");
//        queryDTO1.setSettlementCenterName("123");
//        PaginationResult<ResponsibleSettlementSubjectListDTO> result2 =
//                hrmsResponsibleService.pageList(queryDTO1);
//        result2.getResults().forEach(System.out::println);
    }

    @Test
    void  detailTest(){
        ResponsibleSettlementDetailDTO detailDTO = hrmsResponsibleService.detail(19L);
        System.out.println("detailDTO = " + detailDTO);

        ResponsibleSettlementDetailDTO detail = hrmsResponsibleService.detail(38L);
        System.out.println("detail = " + detail);
    }

    @Test
    void  importTest(){
        List<ResponsibleSettlementImportDTO> list = Lists.newArrayList();
        for (int i = 0; i < 1; i++) {
            ResponsibleSettlementImportDTO dto = genDto();
            list.add(dto);
        }
        List<ResponsibleSettlementImportDTO> failList = hrmsResponsibleService.importData(list);
        failList.forEach(System.out::println);
    }

    private ResponsibleSettlementImportDTO genDto() {
        ResponsibleSettlementImportDTO dto = new ResponsibleSettlementImportDTO();
        dto.setCompanyOrgId(34806L);
        dto.setAccountPostIds("1001，1007");
        dto.setFinancePostIds("10,011,007");
        return dto;
    }


} 
