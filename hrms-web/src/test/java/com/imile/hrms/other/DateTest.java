package com.imile.hrms.other;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * 日期测试
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/9
 */
public class DateTest {

    public static void main(String[] args) {
        Date startTime = DateUtil.parse("2021-09-01");
        Date endTime = DateUtil.parse("2021-12-30");
        while (startTime.before(endTime)) {
            startTime = DateUtil.offsetDay(startTime, 1);
            DateObj dateObj = convert(startTime);
            System.out.println(dateObj);
        }
    }

    /**
     * 输入 2021-12-09,则返回对应的年份、月份、季度等信息
     *
     * @param date
     * @return
     */
    private static DateObj convert(Date date) {
        DateObj dateObj = new DateObj();
        // 2021
        dateObj.setYear(DateUtil.year(date));
        // 4
        dateObj.setQuarter(DateUtil.quarter(date));
        // 12
        dateObj.setMonth(DateUtil.month(date) + 1);
        // 50
        dateObj.setWeek(DateUtil.weekOfYear(date));
        // 9
        dateObj.setDay(DateUtil.dayOfMonth(date));
        // 星期四/THURSDAY/THURSDAY
        dateObj.setDayOfWeek(DateUtil.dayOfWeekEnum(date).toChinese() + "/" + DateUtil.dayOfWeekEnum(date).getValue() + "/" + DateUtil.dayOfWeekEnum(date).name());
        // 20211209
        dateObj.setId(Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN)));
        return dateObj;
    }

    @Data
    public static class DateObj {

        private Integer year;
        private Integer quarter;
        private Integer month;
        private Integer week;
        private Integer day;
        private String dayOfWeek;
        private Long id;

        @Override
        public String toString() {
            return "DateObj{" +
                    "year=" + year +
                    ", quarter=" + quarter +
                    ", month=" + month +
                    ", week=" + week +
                    ", day=" + day +
                    ", dayOfWeek='" + dayOfWeek + '\'' +
                    ", id=" + id +
                    '}';
        }
    }
}
