package com.imile.hrms.user;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.dao.user.dto.UserDTO;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.user.HrmsUserInfoService;
import org.junit.jupiter.api.Test;
import org.junit.Before;
import org.junit.After;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * HrmsUserInfoServiceImpl Tester.
 * {@link com.imile.hrms.service.user.impl.HrmsUserInfoServiceImpl}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 03/16/2022
 */
public class HrmsPlatFormUserInfoServiceImplTest extends ApplicationTest {

    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }


    /**
     * Method: getCrmUserByUserCode(String userCode)
     * {@link com.imile.hrms.service.user.impl.HrmsUserInfoServiceImpl#getCrmUserByUserCode}
     */
    @Test
    public void testGetCrmUserByUserCode() throws Exception {
        String userCode = "ray";
        UserDTO crmUserByUserCode = hrmsUserInfoService.getCrmUserByUserCode(userCode);
        System.out.println(crmUserByUserCode);

    }

    @Test
    public void testListUserInfoByUserCode() throws Exception {
        List<String> userCodes = Arrays.asList("ray","noah");
        List<UserDTO> userDTOS = hrmsUserInfoService.listUserInfoByUserCode(userCodes);
        String s = JSON.toJSONString(userDTOS);
        System.out.println(s);
        System.out.println(userDTOS);

    }


}
