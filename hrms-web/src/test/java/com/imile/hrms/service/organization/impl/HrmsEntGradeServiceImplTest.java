package com.imile.hrms.service.organization.impl;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.common.StatusSwitchParamDTO;
import com.imile.hrms.dao.organization.dto.EntGradeDTO;
import com.imile.hrms.dao.organization.query.GradeQuery;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.organization.HrmsEntGradeService;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

class HrmsEntGradeServiceImplTest extends ApplicationTest {

    @Autowired
    HrmsEntGradeService service;


    @Test
    void gradeList() {
        GradeQuery gradeQuery = new GradeQuery();
        gradeQuery.setGradeType("ray 的高管类");
        gradeQuery.setRankSequence("s");
        PaginationResult<EntGradeDTO> entGradeDTOPaginationResult = service.gradeList(gradeQuery);
        List<EntGradeDTO> results = entGradeDTOPaginationResult.getResults();
        System.out.println(results);
    }

    @Test
    void addGrade() {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserCode("Ray");
        userInfoDTO.setUserName("Ray");

        UcenterUtils.setUserInfo(new UserInfoDTO());
        EntGradeDTO entGradeDTO = new EntGradeDTO();

        entGradeDTO.setGradeTypeCn("前端");
        entGradeDTO.setGradeTypeEn("frontEnd");
        entGradeDTO.setRankSequence("H");
        entGradeDTO.setMaxRank(5);
        entGradeDTO.setMinRank(60);
        Boolean aBoolean = service.addGrade(entGradeDTO);
        Assert.assertTrue(aBoolean);
    }

    @Test
    void statusSwitch() {
        StatusSwitchParamDTO statusSwitchParam = new StatusSwitchParamDTO();
        statusSwitchParam.setStatus("DISABLE");
        statusSwitchParam.setId(881687726481481728L);
        service.statusSwitch(statusSwitchParam);
    }
}