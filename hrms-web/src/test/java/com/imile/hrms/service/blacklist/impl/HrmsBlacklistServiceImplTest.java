package com.imile.hrms.service.blacklist.impl;

import com.alibaba.fastjson.JSON;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.api.blacklist.dto.BanInfoDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.blacklist.BlacklistBanStatusEnum;
import com.imile.hrms.common.util.DateFormatterUtil;
import com.imile.hrms.dao.blacklist.dao.HrmsBlacklistDao;
import com.imile.hrms.dao.blacklist.dao.HrmsBlacklistLogDao;
import com.imile.hrms.dao.blacklist.dto.ImportBlacklistParamDTO;
import com.imile.hrms.dao.blacklist.dto.LockdownInfoDTO;
import com.imile.hrms.dao.blacklist.model.HrmsBlacklistDO;
import com.imile.hrms.dao.blacklist.model.HrmsBlacklistLogDO;
import com.imile.hrms.dao.blacklist.query.BlacklistPageQuery;
import com.imile.hrms.dao.user.dao.HrmsUserCertificateDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.SelectUserDTO;
import com.imile.hrms.dao.user.dto.UserCertificateInfoParamDTO;
import com.imile.hrms.dao.user.model.HrmsUserCertificateDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.CertificateConfigQuery;
import com.imile.hrms.job.blacklist.RefreshBlacklistCacheHandler;
import com.imile.hrms.job.blacklist.UpdateBanStatusHandler;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.blacklist.HrmsBlacklistService;
import com.imile.hrms.service.blacklist.param.BlacklistAddParam;
import com.imile.hrms.service.blacklist.param.BlacklistEditParam;
import com.imile.hrms.service.blacklist.vo.BlacklistDetailVO;
import com.imile.hrms.service.blacklist.vo.BlacklistLogVO;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.hrms.web.blacklist.controller.HrmsBlacklistController;
import com.imile.hrms.web.blacklist.vo.BlacklistPageVO;
import com.imile.hrms.web.sys.HrmsCompanyCertificateConfigController;
import com.imile.hrms.web.sys.vo.CertificateConfigVO;
import com.imile.hrms.web.user.vo.UserCertificateCheckParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Objects;


@Slf4j
class HrmsBlacklistServiceImplTest extends ApplicationTest {

    @Resource
    private HrmsBlacklistService hrmsBlacklistService;

    @Resource
    private UpdateBanStatusHandler updateBanStatusHandler;
    @Resource
    private RefreshBlacklistCacheHandler refreshBlacklistCacheHandler;



    @Resource
    private HrmsBlacklistDao hrmsBlacklistDao;

    @Resource
    private HrmsBlacklistLogDao hrmsBlacklistLogDao;

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Resource
    private HrmsUserCertificateDao hrmsUserCertificateDao;

    @Resource
    private HrmsBlacklistController hrmsBlacklistController;

    @Resource
    private HrmsCompanyCertificateConfigController companyCertificateConfigController;

    @Test
    void page() {
        // Setup
        final BlacklistPageQuery query = new BlacklistPageQuery();
        query.setCurrentPage(1);
        query.setShowCount(20);
        query.setCount(true);
        query.setResourceType("resourceType");
        query.setOrganizationIds(Collections.singletonList(0L));
        query.setBanStatusList(Arrays.asList(1, 2, 3));
        query.setUserNameOrCertificateCode("401");
        // Run the test
        Result<PaginationResult<BlacklistPageVO>> page = hrmsBlacklistController.page(query);
        System.out.println(JSON.toJSONString(page));
    }

    @Test
    void addTest() {
        Date now = new Date();
        //准备数据
        final BlacklistAddParam addParam = new BlacklistAddParam();
        addParam.setUserId(892878755771453441L);
        addParam.setEndDate(new GregorianCalendar(2023, Calendar.MAY, 29).getTime());
        addParam.setReason("add-20230523");
        addParam.setSourceSystem("TMS");
        //操作
        hrmsBlacklistService.add(addParam);
        //检验
        HrmsBlacklistDO hrmsBlacklistDO = hrmsBlacklistDao.selectByUserId(addParam.getUserId());
        HrmsUserInfoDO userInfo = hrmsUserInfoDao.getById(addParam.getUserId());
        List<HrmsBlacklistLogDO> blacklistLogList = hrmsBlacklistLogDao.getByblacklistId(hrmsBlacklistDO.getId());
        List<HrmsUserCertificateDO> userCertificateList = hrmsUserCertificateDao.listCertificateInfo(addParam.getUserId(), null);
        log.info("hrmsBlacklistDO={}", hrmsBlacklistDO);
        log.info("userInfo={}", userInfo);
        log.info("blacklistLogList={}", blacklistLogList);
        log.info("userCertificateList={}", userCertificateList);

        Assertions.assertNotNull(userInfo);
        Assertions.assertEquals(addParam.getUserId(), hrmsBlacklistDO.getUserId());
        Assertions.assertEquals(userInfo.getUserCode(), hrmsBlacklistDO.getUserCode());
        Assertions.assertEquals(addParam.getSourceSystem(), hrmsBlacklistDO.getSourceSystem());
        Assertions.assertEquals(DateFormatterUtil.getEndOfDay(addParam.getEndDate()), hrmsBlacklistDO.getEndDate());
        Assertions.assertEquals(addParam.getReason(), hrmsBlacklistDO.getReason());
        Assertions.assertTrue(Arrays.stream(BlacklistBanStatusEnum.values()).anyMatch(e -> Objects.equals(e.getValue(), hrmsBlacklistDO.getBanStatus())));
        Assertions.assertEquals("admin", hrmsBlacklistDO.getCreateUserName());
        Assertions.assertEquals("徐谢靠", hrmsBlacklistDO.getRecordUserName());
    }

    /**
     * 新增操作-id重复
     */
    @Test
    void addTest_IdDuplicate() {
        //准备数据
        BlacklistAddParam addParam1 = new BlacklistAddParam();
        addParam1.setUserId(968683948471222272L);
        addParam1.setEndDate(new GregorianCalendar(2023, Calendar.MAY, 21).getTime());
        addParam1.setReason("gal-test");
        addParam1.setSourceSystem("tms");
        //检验
        Assertions.assertThrows(BusinessException.class, () -> hrmsBlacklistService.add(addParam1));
    }

    @Test
    void editTest() {
        Date now = new Date();
        BlacklistEditParam editParam = new BlacklistEditParam();
        editParam.setBlacklistId(1085657757337649152L);
        ArrayList<UserCertificateInfoParamDTO> list = new ArrayList<>();
        UserCertificateInfoParamDTO dto1 = new UserCertificateInfoParamDTO();
        dto1.setCertificateTypeCode("RESIDENCY_PERMIT");
        dto1.setCertificateCode("RESIDENCY_PERMITinsert");
        dto1.setHandlerMethod("MUST_HANDLER");
        dto1.setId(null);
        dto1.setCertificateExpireDate(now);
        UserCertificateInfoParamDTO dto2 = new UserCertificateInfoParamDTO();
        dto2.setCertificateTypeCode("ID_CARD");
        dto2.setCertificateCode("ID_CARDupdate");
        dto2.setCertificateExpireDate(now);
        dto2.setHandlerMethod("24222");
        dto2.setId(983885163329093633L);
        list.add(dto1);
        list.add(dto2);
        editParam.setEndDate(new GregorianCalendar(2023, Calendar.MAY, 21).getTime());
        editParam.setReason("editTest-20230524-894259589631971329L");
        editParam.setSourceSystem("HRMS");
        hrmsBlacklistService.edit(editParam);

        HrmsBlacklistDO blacklistDO = hrmsBlacklistDao.getById(editParam.getBlacklistId());
        System.out.println(blacklistDO);
        List<HrmsBlacklistLogDO> blacklistLogList = hrmsBlacklistLogDao.getByblacklistId(editParam.getBlacklistId());
        System.out.println(blacklistLogList.get(0));
    }


    @Test
    void getDetailTest() {
        BlacklistDetailVO detail = hrmsBlacklistService.getDetail(1083393116649693184L);
        System.out.println(detail);
    }

    @Resource
    private ConverterService converterService;
    @Test
    void logTest() {
        List<BlacklistLogVO> logList = hrmsBlacklistService.getLogList(1087143398509117440L);
        System.out.println(JSON.toJSONString(logList));
    }
    @Test
   void importBlacklistTest(){
        ImportBlacklistParamDTO dto1 = new ImportBlacklistParamDTO();
        dto1.setEmployeeId("968683948471222272");
        dto1.setEndDate("2023/5/29");
        dto1.setReason("gsl-import2-968683948471222272");
        dto1.setRowNum(1);
        ImportBlacklistParamDTO dto2 = new ImportBlacklistParamDTO();
        dto2.setEmployeeId("935690521731399681");
        dto2.setEndDate("2023/5/21");
        dto2.setReason("gsl-import2-935690521731399681");
        dto2.setRowNum(2);
        String str ="[{\"employeeId\":\"*********\",\"endDate\":\"Tue May 29 00:00:00 CST 2023\",\"reason\":\"20230529test0gsl\",\"rowNum\":\"2\"}]";
        List<ImportBlacklistParamDTO> importList = JSON.parseArray(str, ImportBlacklistParamDTO.class);

        List<ImportBlacklistParamDTO> importBlacklistParamDTOS = hrmsBlacklistService.importBlacklist(importList,"2103410601","TMS","BRA");
        System.out.println(importBlacklistParamDTOS);

    }


    @Test
    void getBanInfoTest() {
        BanInfoDTO banInfo = hrmsBlacklistService.getBanInfo("D2101520601");
        System.out.println(banInfo);
    }

    @Test
    void checkExitBlacklistTest() {
        Date now = new Date();
        final UserCertificateInfoParamDTO dto1 = new UserCertificateInfoParamDTO();
        dto1.setCertificateTypeCode("RESIDENCY_PERMIT");
        dto1.setCertificateCode("213241");
        dto1.setCertificateExpireDate(now);
        final UserCertificateInfoParamDTO dto2 = new UserCertificateInfoParamDTO();
        dto2.setCertificateTypeCode("ID_CARD");
        dto2.setCertificateCode("345355");
        dto2.setCertificateExpireDate(now);
        List<UserCertificateInfoParamDTO> list = Arrays.asList(dto1, dto2);
        LockdownInfoDTO lockdownInfoDTO = hrmsBlacklistService.checkExitBlacklist(null, list);
        System.out.println(lockdownInfoDTO);
    }


    @Test
    void updateBanStatusHandlerTest() {
        updateBanStatusHandler.updateBanStatusHandler(null);
    }
    @Test
    void refreshBlacklistCacheHandlerTest() {
        refreshBlacklistCacheHandler.refreshBlacklistCacheHandler("");
    }

    @Test
    void getConfigTest() {
        CertificateConfigQuery query = new CertificateConfigQuery();
        query.setDriver(1);
        query.setDriverType(EmploymentTypeEnum.EMPLOYEE.getCode());
        query.setEmployeeType(EmploymentTypeEnum.EMPLOYEE.getCode());
        query.setCountry(BusinessConstant.DEFAULT_COUNTRY);
        Result<List<CertificateConfigVO>> config = companyCertificateConfigController.getConfig(query);
        System.out.println(JSON.toJSONString(config.getResultObject()));
    }
    @Test
    void isExitBlacklistTest(){
//        HrmsBlacklistDO o = hrmsBlacklistService.isExitBlacklist("*********");
//        System.out.println(o);
    }
    @Resource
    private HrmsUserInfoService hrmsUserInfoService;


    @Test
   void  getDriverListTest(){
        List<SelectUserDTO> driverList = hrmsUserInfoService.getDriverList();
        System.out.println(JSON.toJSONString(driverList));
   }

    @Test
    void checkBlacklistCertificateTest(){
        UserCertificateCheckParam userCertificateCheckParam = new UserCertificateCheckParam();
        userCertificateCheckParam.setCertificateType("ID_CARD");
        userCertificateCheckParam.setCertificateCode("ewrwqe");
        Result<LockdownInfoDTO> lockdownInfoDTOResult = hrmsBlacklistController.checkBlacklistCertificate(userCertificateCheckParam);
        System.out.println(JSON.toJSONString(lockdownInfoDTOResult));
    }

}
