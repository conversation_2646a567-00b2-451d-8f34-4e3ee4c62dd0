package com.imile.hrms.service.user;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.enums.CertificateTypeEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.dao.user.dto.OutSourceDriverRegisterParam;
import com.imile.hrms.dao.user.dto.UserCertificateParamDTO;
import com.imile.hrms.dao.user.dto.UserDTO;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.user.driver.HrmsDriverService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class HrmsAttendanceUserServiceImplTest extends ApplicationTest {
    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;
    /**
     * Method: addOutSourceDriver(OutSourceDriverRegisterParam driverRegisterParam)
     * {@link  com.imile.hrms.service.user.impl.HrmsUserInfoServiceImpl#addOutSourceDriver}
     */
    @Test
    public void testAddOutSourceDriver() throws Exception {
        UserContext user = new UserContext();
        user.setUserName("ray");
        user.setUserCode("ray");
        RequestInfoHolder.setLoginInfo(user);
        RequestInfoHolder.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        OutSourceDriverRegisterParam driverRegisterParam = new OutSourceDriverRegisterParam();
        driverRegisterParam.setUserName("小盒落地成司机");
        driverRegisterParam.setId(943993605691801600L);
        driverRegisterParam.setUserCode("210606");
        driverRegisterParam.setPersonalEmail("<EMAIL>");
        driverRegisterParam.setSex(BusinessConstant.Y);
        driverRegisterParam.setOcCode("S210153701");
        driverRegisterParam.setEmployeeType(EmploymentTypeEnum.OS_PER_DELIVERED.getCode());
        driverRegisterParam.setVendorCode("SRM123");
        driverRegisterParam.setLeaderUserCode("210606");
        driverRegisterParam.setPhone("88888");
        List<UserCertificateParamDTO> certificateInfoDTOList = new ArrayList<>();
        UserCertificateParamDTO needHandler = new UserCertificateParamDTO();
        needHandler.setCertificateTypeCode(CertificateTypeEnum.RESIDENCY_PERMIT.getCode());
        needHandler.setId(943993605771493377L);
        needHandler.setCertificateCode("ray_jzz_11111111");
        UserCertificateParamDTO mustHandler = new UserCertificateParamDTO();
        mustHandler.setCertificateTypeCode(CertificateTypeEnum.DRIVING_LICENSE.getCode());
        mustHandler.setCertificateCode("ray_driver_11111111");
        certificateInfoDTOList.add(needHandler);
        certificateInfoDTOList.add(mustHandler);
        driverRegisterParam.setUserCertificateList(certificateInfoDTOList);
        hrmsUserInfoService.addOutSourceDriver(driverRegisterParam);
    }

    @Test
    void dtlListTest(){
        Long vendor = 952944319252140033L;
        Long ocId = 804073902908243969L;
        List<UserDTO> dto = hrmsUserInfoService.selectDtlListByOcId( ocId);
        dto.stream().forEach(System.out::println);
    }

    @Test
    void  editTest(){
        String json = "{\"isLineHaul\":false,\"userName\":\"Charles888999\",\"sex\":\"2\",\"vendorId\":\"1051594226707734529\",\"employeeType\":\"OSFixedsalary\",\"phone\":\"**********\",\"alternativePhone\":\"**********\",\"personalEmail\":\"<EMAIL>\",\"functional\":\"Dispatch,Pickup\",\"leaderUserCode\":\"**********\",\"leaderId\":\"1022603464850546689\",\"vehicleModel\":\"bike\",\"deviceIds\":\"777888\",\"ocId\":\"84\",\"hrUserId\":\"1073351552355405825\",\"userCode\":\"D2101631501\",\"certificateInfoDTOList\":[{\"certificateTypeCode\":\"ID_CARD\",\"certificateCode\":\"1qaz12345\",\"certificateExpireDate\":\"2023-04-30\"},{\"certificateTypeCode\":\"DRIVING_LICENSE\",\"certificateCode\":\"1233edc\",\"certificateExpireDate\":\"2023-04-27\"}]}";
        OutSourceDriverRegisterParam param = JSON.parseObject(json, OutSourceDriverRegisterParam.class);
        System.out.println("param = " + param);
        hrmsDriverService.edit( param );
    }

    @Autowired
    HrmsDriverService hrmsDriverService;


//    @Test
//    void testDtlList(){
//        Long vendorId = 952944319252140033L;
//        Long ocId = 804073902908243969L;
//        hrmsUserInfoService.selectDtlList(vendorId, ocId);
//    }



    @Test
    void testSyncLineHual(){
        OutSourceDriverRegisterParam param = genLineHaulDriver();

    }

    public static void main(String[] args) {

    }

    @Test
    void testNotLineHaul(){
        OutSourceDriverRegisterParam param = genLineHaulDriver();
        hrmsUserInfoService.addOutSourceDriver(param);
    }




    public static OutSourceDriverRegisterParam genLineHaulDriver() {

        String liwei = "{\n" +
                "    \"isLineHaul\": false,\n" +
                "    \"userName\": \"CharlesLlw\",\n" +
                "    \"sex\": \"1\",\n" +
                "    \"vendorId\": \"749979487365771265\",\n" +
                "    \"employeeType\": \"OSPerdelivered\",\n" +
                "    \"phone\": \"13245678987\",\n" +
                "    \"alternativePhone\": \"777888\",\n" +
                "    \"personalEmail\": \"<EMAIL>\",\n" +
                "    \"functional\": \"Dispatch,Pickup\",\n" +
                "    \"vehicleModel\": \"MOD2006\",\n" +
                "    \"deviceIds\": \"111,222\",\n" +
                "    \"deptId\": \"1032363243689213952\",\n" +
                "    \"idCard\": \"1qasz1234\",\n" +
                "    \"idCardDate\": \"2023-04-28\",\n" +
                "    \"driverLicense\": \"43212wsx\",\n" +
                "    \"driverLicenseDate\": \"2023-04-30\"\n" +
                "}";


        String json = "{\n" +
                "    \"isLineHaul\": true,\n" +
                "    \"userName\": \"John Doe\",\n" +
                "    \"sex\": 1,\n" +
                "    \"phone\": \"1313313\",\n" +
                "    \"functional\": \"Line Haul\",\n" +
                "    \"ocId\": \"1031084\",\n" +
                "    \"employeeType\": \"OS_FIXED_SALARY\",\n" +
                "    \"leaderId\": 887387791321731072,\n" +
                "    \"leaderUserCode\": \"21032382\",\n" +
                "    \"phone\": 15551234567,\n" +
                "    \"deviceIds\": \"ABC123DEF456\",\n" +
                "     \"certificateInfoDTOList\":\n" +
                "    [\n" +
                "        {\n" +
                "            \"certificateCode\": \"123456789012345678\",\n" +
                "            \"certificateExpireDate\": \"2025-12-31T23:59:59.999Z\",\n" +
                "            \"certificateReceiptDate\": \"2023-04-18T07:29:46.213Z\",\n" +
                "            \"certificateTypeCode\": \"DRIVER_LICENSE\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certificateCode\": \"1234567890123\",\n" +
                "            \"certificateExpireDate\": \"2025-12-31T23:59:59.999Z\",\n" +
                "            \"certificateReceiptDate\": \"2023-04-18T07:29:46.213Z\",\n" +
                "            \"certificateTypeCode\": \"ID_CARD\"\n" +
                "        }\n" +
                "    ]\n" +
                "\n" +
                "}";
        OutSourceDriverRegisterParam param = JSON.parseObject(liwei, OutSourceDriverRegisterParam.class);
        System.out.println("param = " + param);
        return param;
    }
}
