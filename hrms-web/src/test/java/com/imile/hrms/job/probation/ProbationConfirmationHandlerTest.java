package com.imile.hrms.job.probation;

import com.imile.hrms.service.ApplicationTest;
import org.junit.After;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/10
 */
public class ProbationConfirmationHandlerTest extends ApplicationTest {
    @Autowired
    private ProbationConfirmationHandler probationConfirmationHandler;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    @Test
    public void testSalaryEmployeeDetailHandler() throws Exception {

        probationConfirmationHandler.probationConfirmationHandler("");
    }
}
