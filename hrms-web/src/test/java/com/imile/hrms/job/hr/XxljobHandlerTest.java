package com.imile.hrms.job.hr;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.hrms.Application;
import com.imile.hrms.job.account.SynTms2HrmsAccountHandler;
import com.imile.hrms.job.organization.DeptActiveTaskHandler;
import com.imile.hrms.job.organization.DeptSnapshotHandler;
import com.imile.hrms.job.user.DriverTaxIdSyncHandler;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.organization.param.DeptSnapshotParam;
import org.junit.After;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * DateInitHandler Tester.
 * {@link DateInitHandler}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/14
 */
@SpringBootTest(classes = Application.class, properties = {"apollo.meta=http://10.20.0.1:8081"})
public class XxljobHandlerTest extends ApplicationTest {
    @Resource
    private SynTms2HrmsAccountHandler synTms2HrmsAccountHandler;
    @Resource
    private DriverTaxIdSyncHandler driverTaxIdSyncHandler;
    @Resource
    private DeptSnapshotHandler deptSnapshotHandler;

    @Resource
    private DeptActiveTaskHandler deptActiveTaskHandler;
    @Before
    public void before() throws Exception {
    }

    @After
    public void after() {
    }

    /**
     * Method: txCheck(String param)
     * {@link DateInitHandler#dateInit(String)}
     */
    @Test
    public void synTms2HrmsAccountHandler() {
        SynTms2HrmsAccountHandler.SynAccountParam synAccountParam = new SynTms2HrmsAccountHandler.SynAccountParam();
        synAccountParam.setIsCheck(false);
        synAccountParam.setCountLimit(3);
        synAccountParam.setDoSql(Boolean.TRUE);
        synAccountParam.setBlackList(Lists.newArrayList("D2102255301"));
        synTms2HrmsAccountHandler.synTms2HrmsAccountHandler(JSONObject.toJSONString(synAccountParam));
        System.out.println("============>");
    }

    @Test
    public void driverTaxIdSyncHandler() {
        driverTaxIdSyncHandler.driverTaxIdSyncHandler("");
        System.out.println("============>");
    }

    public static void main(String[] args) {
        SynTms2HrmsAccountHandler.SynAccountParam synAccountParam = new SynTms2HrmsAccountHandler.SynAccountParam();
        synAccountParam.setIsCheck(false);
        synAccountParam.setCountLimit(3);
        synAccountParam.setDoSql(Boolean.FALSE);
        synAccountParam.setBlackList(Lists.newArrayList("123"));
        System.out.println(JSONObject.toJSONString(synAccountParam));
    }

    @Test
    public void deptSnapshotHandler() {
        deptSnapshotHandler.deptSnapshotHandler("");
        System.out.println("============>");
    }

    @Test
    public void deptActiveTaskHandler() {
        DeptActiveTaskHandler.deptActiveTaskHandlerParam activeTaskHandlerParam = new DeptActiveTaskHandler.deptActiveTaskHandlerParam();
        activeTaskHandlerParam.setTaskIds(Lists.newArrayList(1097580729203689299L));
        deptActiveTaskHandler.deptActiveTaskHandler(JSONObject.toJSONString(activeTaskHandlerParam));
        System.out.println("============>");
    }
} 
