package com.imile.hrms.common.tempalte;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.imile.hrms.common.template.GeneratorUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/8
 */
public class TemplateTest {

    public static void main(String[] args) throws Exception {

        Map<String, Object> data = new HashMap<>();
        JSONObject param = new JSONObject();


        // 账号
        JSONArray accounts = new JSONArray();
        JSONObject tmsAccount = new JSONObject();
        tmsAccount.fluentPut("accountTypeCode", "TMS_ACCOUNT")
                .fluentPut("accountTypeNameByLang", "系统账号")
                .fluentPut("account", "noah")
                .fluentPut("accountStatus", "CREATED")
                .fluentPut("initialPassword", "123456");
        JSONObject email = new JSONObject();
        email.fluentPut("accountTypeCode", "ENTERPRISE_EMAIL")
                .fluentPut("accountTypeNameByLang", "企业邮箱")
                .fluentPut("account", "<EMAIL>")
                .fluentPut("accountStatus", "NOT_CREATE")
                .fluentPut("initialPassword", "imile123456");

        accounts.add(tmsAccount);
        accounts.add(email);

        param.fluentPut("userName", "Noah")
                .fluentPut("workNo", "20001")
                .fluentPut("deptName", "测试部门")
                .fluentPut("postName", "测试岗位")
                .put("accounts", accounts);

        data.put("param", param);
        String tempName = "/user/entry_email_template.ftl";
        String content = GeneratorUtil.tempFactory(tempName, data);
        System.out.println("==================模板内容===============\n" + content);
    }
}
