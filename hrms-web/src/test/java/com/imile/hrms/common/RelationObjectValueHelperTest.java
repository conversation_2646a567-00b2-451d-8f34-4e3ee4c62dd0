package com.imile.hrms.common;

import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.user.UserTransformService;
import com.imile.hrms.service.user.result.transform.UserTransformBeforeBO;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
public class RelationObjectValueHelperTest extends ApplicationTest {


    @Resource
    private UserTransformService transformService;


    //
    @Test
    public void test() {

        UserTransformBeforeBO transformUserDisplayInfo = transformService.getTransformUserDisplayInfo(1229077995914928129L);

    }
}
