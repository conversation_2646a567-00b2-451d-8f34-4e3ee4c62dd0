package com.imile.hrms.newAttendance.calendar;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigAdapter;
import com.imile.hrms.service.ApplicationTest;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/11 
 * @Description
 */
public class CalendarConfigAdapterTest extends ApplicationTest {

    @Resource
    private CalendarConfigAdapter calendarConfigAdapter;

    @Before
    public void before() {
        Boolean doubleWriteMode = calendarConfigAdapter.isDoubleWriteMode();
        Boolean enableNewModule = calendarConfigAdapter.isEnableNewModule();
        System.out.println("doubleWriteMode = " + doubleWriteMode);
        System.out.println("enableNewModule = " + enableNewModule);
    }

    @Test
    public void testgetById() {
        Long id = 1021168293031051264L;
        HrmsAttendanceConfigDO hrmsAttendanceConfigDO = calendarConfigAdapter.getById(id);
        System.out.println(hrmsAttendanceConfigDO);
    }

    @Test
    public void testlistByIds() {
        Long id = 1021168293031051264L;
        List<HrmsAttendanceConfigDO> hrmsAttendanceConfigDOS = calendarConfigAdapter.listByIds(
                Collections.singletonList(id));
        System.out.println(hrmsAttendanceConfigDOS);
    }

    @Test
    public void testselectByCountryList(){
        List<HrmsAttendanceConfigDO> hrmsAttendanceConfigDOS = calendarConfigAdapter.selectByCountryList(
                Collections.singletonList("KSA"));
        System.out.println(hrmsAttendanceConfigDOS);
    }

    @Test
    public void testgetActiveById(){
        HrmsAttendanceConfigDO hrmsAttendanceConfigDO = calendarConfigAdapter.getActiveById(0L);
        System.out.println(hrmsAttendanceConfigDO);
    }


}
