package com.imile.hrms.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.user.api.DriverApi;
import com.imile.hrms.api.user.enums.DriverDynamicFieldEnum;
import com.imile.hrms.api.user.param.DriverFilterParam;
import com.imile.hrms.api.user.param.SupplierDriverAuditParam;
import com.imile.hrms.api.user.result.DriverDynamicInfoDTO;
import com.imile.hrms.api.user.result.UserCertificateDTO;
import com.imile.hrms.service.ApplicationTest;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Slf4j
public class DriverApiTest extends ApplicationTest {

    @Resource
    private DriverApi driverApi;

    @Test
    public void testAuditSupplierDriver() {
        SupplierDriverAuditParam param = new SupplierDriverAuditParam();
        param.setId(1334248917172342785L);
        param.setAuditStatus("NO_THROUGH");
        param.setRejectReason("这是驳回原因");
        RpcResult<Boolean> result = driverApi.auditSupplierDriver(param);
        log.info("result:{}", JSON.toJSONString(result.getResult()));
    }

    @Test
    public void testGetDriverIdPageList() {
        DriverFilterParam param = new DriverFilterParam();
        param.setDriverName("symon");
        param.setAuditStatus("WAIT_AUDIT");
        RpcResult<PaginationResult<Long>> result = driverApi.getDriverIdPageList(param);
        log.info("result:{}", JSON.toJSONString(result.getResult()));
    }

    @Test
    public void testListDriverDynamicInfo() {
        RpcResult<List<DriverDynamicInfoDTO>> result = driverApi.listDriverDynamicInfo(Lists.newArrayList(1333144801499893761L, 1333144612059959297L), Lists.newArrayList(DriverDynamicFieldEnum.values()));
        log.info("result:{}", JSON.toJSONString(result.getResult()));
    }

    @Test
    public void testGetDriverCertificateList() {
        RpcResult<List<UserCertificateDTO>> result = driverApi.getDriverCertificateList(1336790023613480961L);
        log.info("result:{}", JSON.toJSONString(result.getResult()));
    }
}
