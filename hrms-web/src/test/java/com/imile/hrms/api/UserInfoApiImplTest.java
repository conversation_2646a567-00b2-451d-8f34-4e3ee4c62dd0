package com.imile.hrms.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.Application;
import com.imile.hrms.api.user.api.UserApi;
import com.imile.hrms.api.user.dto.UserQueryParam;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.user.UserService;
import com.imile.rpc.common.RpcResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(classes = Application.class, properties = {"apollo.meta=http://10.20.0.1:8081"})
class UserInfoApiImplTest extends ApplicationTest {

    @Resource
    private UserService userService;

    @Resource
    private UserApi userApi;
    @Test
    public void testInit() {
        long start = System.currentTimeMillis();
        System.out.println("test" + start);
        ArrayList<Integer> integers = Lists.newArrayList(1133, 1134, 1138, 1139, 1140, 1142, 1143, 1144, 1145, 1146, 1149, 1156, 1157, 1031050, 1031111, 1031196, 1031256, 1031655, 1031701, 1032442, 1032478, 1032514, 1032550, 1032552, 1032553, 1032555, 1032568, 1032569, 1032604, 1032607, 1032608, 1032643, 1032645, 1032646, 1032651, 1032652, 1032653, 1032654, 1032655, 1032656, 1032682, 1032698, 1032714, 1032730, 1032746, 1032747, 1032762, 1032763, 1032778, 1032779, 1032794, 1032795, 1032810, 1032982, 1032999, 1033016, 1033033, 1033050, 1033067, 1033084, 1033101, 1033118, 1033135, 1033152, 1033169, 1033170, 1033186, 1033203, 1033204, 1033220, 1033221, 1033237, 1033238, 1033254, 1033255, 1033271, 1033272, 1033288, 1033289, 1033305, 1033306, 1033322, 1033323, 1033339, 1033340, 1033356, 1033357, 1033373, 1033374, 1033390, 1033391, 1033407, 1033408, 1033424, 1033425, 1033441, 1033458, 1033459, 1033475, 1033491, 1033507, 1033572, 1033589, 1033606, 1033743, 1033744, 1033745, 1033746, 1033747, 1033748, 1033749, 1033750, 1033751, 1033752, 1033753, 1033754, 1033755, 1033756, 1033757, 1033762, 1033763, 1033764, 1033765, 1033766, 1033767, 1033768, 1033769, 1033770, 1033771, 1033772, 1033773, 1033774, 1033775, 1033776, 1033777, 1033778, 1033779, 1033780, 1033781, 1033782, 1033783, 1033784, 1033785, 1033798, 1033799, 1033839, 1033840, 1033884, 1033885, 1033894, 1033895, 1033896, 1033897, 1033898, 1033899, 1033900, 1033901, 1033902, 1033903, 1033904, 1033905, 1033906, 1033908, 1033909, 1033910, 1033911, 1033920, 1033921, 1033922, 1033923, 1033924, 1033927, 1033928, 1033929, 1033930, 1033931, 1033933, 1033935, 1033936, 1033940, 1033941, 1033942, 1033943, 1033944, 1033945, 1033948, 1033949, 1033950, 1033951, 1033952, 1033953, 1033954, 1033955, 1033956, 1033957, 1033958, 1033959, 1033960, 1034113, 1034114, 1034115, 1034116, 1034120, 1034121, 1034122, 1034123, 1034124, 1034125, 1034126, 1034127, 1034128, 1034129, 1034130, 1034131, 1034132, 1034258, 1034263, 1034266, 1034268, 1034269, 1034271, 1034272, 1034273, 1034327, 1034328, 1034364, 1034365, 1034373, 1034450, 1034451, 1034452, 1034453, 1034454, 1034455, 1034456, 1034457, 1034458, 1034459, 1034460, 1034461, 1034462, 1034463, 1034464, 1034465, 1034466, 1034467, 1034468, 1034469, 1034470, 1034471, 1034472, 1034473, 1034474, 1034475, 1034476, 1034477, 1034478, 1034479, 1034480, 1034481, 1034482, 1034483, 1034484, 1034485, 1034486, 1034490, 1034491, 1034492, 1034493, 1034494, 1034495, 1034496, 1034497, 1034498, 1034499, 1034500, 1034501, 1034502, 1034503, 1034532, 1034568, 1034569, 1034577, 1034585, 1034596, 1034597, 1034598, 1034599, 1034600, 1034601, 1034602, 1034603, 1034604, 1034605, 1034606, 1034607, 1034608, 1034609, 1034610, 1034611, 1034612, 1034613, 1034614, 1034615, 1034616, 1034617, 1034618, 1034619, 1034620, 1034621, 1034622, 1034623, 1034625, 1034627, 1034633, 1034634, 1034635, 1034636, 1034637, 1034645, 1034699, 1034700, 1034701, 1034702, 1034703, 1034704, 1034705, 1034706, 1034707, 1034708, 1034709, 1034711, 1034712, 1034713, 1034714, 1034715, 1034716, 1034717, 1034718, 1034719, 1034720, 1034721, 1034722, 1034723, 1034724, 1034725, 1034726, 1034727, 1034728, 1034729, 1034730, 1034731, 1034732, 1034733, 1034734, 1034735, 1034739, 1034740, 1034741, 1034742, 1034743, 1034744, 1034745, 1034746, 1034747, 1034748, 1034749, 1034750, 1034751, 1034752, 1034753, 1034755, 1034756, 1034757, 1034758, 1034759, 1034760, 1034761, 1034762, 1034763, 1034767, 1034773, 1034774, 1034776, 1034777, 1034778, 1034779, 1034782, 1034783, 1034786, 1034802, 1034803, 1034804, 1034808, 1034809, 1034810, 1034811, 1034812, 1034813, 1034814, 1034815, 1034816, 1034817, 1034818, 1034819, 1034820, 1034821, 1034822, 1034823, 1034824, 1034825, 1034826, 1034827, 1034828, 1034829, 1034830, 1034831, 1034832, 1034833, 1034834, 1034835, 1034836, 1034837, 1034838, 1034839, 1034840, 1034841, 1034842, 1034843, 1034844, 1034845, 1034846, 1034847, 1034848, 1034849, 1034850, 1034851, 1034852, 1034853, 1034854, 1034855, 1034856, 1034857, 1034858, 1034859, 1034860, 1034861, 1034862, 1034863, 1034864, 1034875, 1034911, 1034912, 1034920, 1034928, 1034931, 1034932, 1034933, 1034935, 1034936, 1034937, 1034938, 1034939, 1034940, 1034941, 1034942, 1034943, 1034944, 1034945, 1034946, 1034947, 1034948, 1034949, 1034950, 1034951, 1034952, 1034953, 1034954, 1034955, 1034956, 1034957, 1034958, 1034959, 1034960, 1034961, 1034962, 1034963, 1034964, 1034965, 1034966, 1034967, 1034968, 1034969, 1034970, 1034971, 1034972, 1034973, 1034974, 1034975, 1034976);
        List<Long> list = integers.stream().map(Integer::longValue).collect(Collectors.toList());
        UserQueryParam userQueryParam = new UserQueryParam();
        userQueryParam.setShowCount(20);
        userQueryParam.setPageNum(1);
        userQueryParam.setDeptIdList(list);
        PaginationResult<String> userCodesByService = userService.getUserCodesByDeptIds(userQueryParam);
        System.out.printf(JSON.toJSONString(userCodesByService));
        System.out.println("test end" + (System.currentTimeMillis() - start));

        long start2 = System.currentTimeMillis();
        System.out.println("test" + start);
        ArrayList<Integer> integers2 = Lists.newArrayList(1133, 1134, 1138, 1139, 1140, 1142, 1143, 1144, 1145, 1146, 1149, 1156, 1157, 1031050, 1031111, 1031196, 1031256, 1031655, 1031701, 1032442, 1032478, 1032514, 1032550, 1032552, 1032553, 1032555, 1032568, 1032569, 1032604, 1032607, 1032608, 1032643, 1032645, 1032646, 1032651, 1032652, 1032653, 1032654, 1032655, 1032656, 1032682, 1032698, 1032714, 1032730, 1032746, 1032747, 1032762, 1032763, 1032778, 1032779, 1032794, 1032795, 1032810, 1032982, 1032999, 1033016, 1033033, 1033050, 1033067, 1033084, 1033101, 1033118, 1033135, 1033152, 1033169, 1033170, 1033186, 1033203, 1033204, 1033220, 1033221, 1033237, 1033238, 1033254, 1033255, 1033271, 1033272, 1033288, 1033289, 1033305, 1033306, 1033322, 1033323, 1033339, 1033340, 1033356, 1033357, 1033373, 1033374, 1033390, 1033391, 1033407, 1033408, 1033424, 1033425, 1033441, 1033458, 1033459, 1033475, 1033491, 1033507, 1033572, 1033589, 1033606, 1033743, 1033744, 1033745, 1033746, 1033747, 1033748, 1033749, 1033750, 1033751, 1033752, 1033753, 1033754, 1033755, 1033756, 1033757, 1033762, 1033763, 1033764, 1033765, 1033766, 1033767, 1033768, 1033769, 1033770, 1033771, 1033772, 1033773, 1033774, 1033775, 1033776, 1033777, 1033778, 1033779, 1033780, 1033781, 1033782, 1033783, 1033784, 1033785, 1033798, 1033799, 1033839, 1033840, 1033884, 1033885, 1033894, 1033895, 1033896, 1033897, 1033898, 1033899, 1033900, 1033901, 1033902, 1033903, 1033904, 1033905, 1033906, 1033908, 1033909, 1033910, 1033911, 1033920, 1033921, 1033922, 1033923, 1033924, 1033927, 1033928, 1033929, 1033930, 1033931, 1033933, 1033935, 1033936, 1033940, 1033941, 1033942, 1033943, 1033944, 1033945, 1033948, 1033949, 1033950, 1033951, 1033952, 1033953, 1033954, 1033955, 1033956, 1033957, 1033958, 1033959, 1033960, 1034113, 1034114, 1034115, 1034116, 1034120, 1034121, 1034122, 1034123, 1034124, 1034125, 1034126, 1034127, 1034128, 1034129, 1034130, 1034131, 1034132, 1034258, 1034263, 1034266, 1034268, 1034269, 1034271, 1034272, 1034273, 1034327, 1034328, 1034364, 1034365, 1034373, 1034450, 1034451, 1034452, 1034453, 1034454, 1034455, 1034456, 1034457, 1034458, 1034459, 1034460, 1034461, 1034462, 1034463, 1034464, 1034465, 1034466, 1034467, 1034468, 1034469, 1034470, 1034471, 1034472, 1034473, 1034474, 1034475, 1034476, 1034477, 1034478, 1034479, 1034480, 1034481, 1034482, 1034483, 1034484, 1034485, 1034486, 1034490, 1034491, 1034492, 1034493, 1034494, 1034495, 1034496, 1034497, 1034498, 1034499, 1034500, 1034501, 1034502, 1034503, 1034532, 1034568, 1034569, 1034577, 1034585, 1034596, 1034597, 1034598, 1034599, 1034600, 1034601, 1034602, 1034603, 1034604, 1034605, 1034606, 1034607, 1034608, 1034609, 1034610, 1034611, 1034612, 1034613, 1034614, 1034615, 1034616, 1034617, 1034618, 1034619, 1034620, 1034621, 1034622, 1034623, 1034625, 1034627, 1034633, 1034634, 1034635, 1034636, 1034637, 1034645, 1034699, 1034700, 1034701, 1034702, 1034703, 1034704, 1034705, 1034706, 1034707, 1034708, 1034709, 1034711, 1034712, 1034713, 1034714, 1034715, 1034716, 1034717, 1034718, 1034719, 1034720, 1034721, 1034722, 1034723, 1034724, 1034725, 1034726, 1034727, 1034728, 1034729, 1034730, 1034731, 1034732, 1034733, 1034734, 1034735, 1034739, 1034740, 1034741, 1034742, 1034743, 1034744, 1034745, 1034746, 1034747, 1034748, 1034749, 1034750, 1034751, 1034752, 1034753, 1034755, 1034756, 1034757, 1034758, 1034759, 1034760, 1034761, 1034762, 1034763, 1034767, 1034773, 1034774, 1034776, 1034777, 1034778, 1034779, 1034782, 1034783, 1034786, 1034802, 1034803, 1034804, 1034808, 1034809, 1034810, 1034811, 1034812, 1034813, 1034814, 1034815, 1034816, 1034817, 1034818, 1034819, 1034820, 1034821, 1034822, 1034823, 1034824, 1034825, 1034826, 1034827, 1034828, 1034829, 1034830, 1034831, 1034832, 1034833, 1034834, 1034835, 1034836, 1034837, 1034838, 1034839, 1034840, 1034841, 1034842, 1034843, 1034844, 1034845, 1034846, 1034847, 1034848, 1034849, 1034850, 1034851, 1034852, 1034853, 1034854, 1034855, 1034856, 1034857, 1034858, 1034859, 1034860, 1034861, 1034862, 1034863, 1034864, 1034875, 1034911, 1034912, 1034920, 1034928, 1034931, 1034932, 1034933, 1034935, 1034936, 1034937, 1034938, 1034939, 1034940, 1034941, 1034942, 1034943, 1034944, 1034945, 1034946, 1034947, 1034948, 1034949, 1034950, 1034951, 1034952, 1034953, 1034954, 1034955, 1034956, 1034957, 1034958, 1034959, 1034960, 1034961, 1034962, 1034963, 1034964, 1034965, 1034966, 1034967, 1034968, 1034969, 1034970, 1034971, 1034972, 1034973, 1034974, 1034975, 1034976);
        List<Long> list2 = integers2.stream().map(Integer::longValue).collect(Collectors.toList());
        UserQueryParam userQueryParam2 = new UserQueryParam();
        userQueryParam2.setShowCount(20);
        userQueryParam2.setPageNum(64);
        userQueryParam2.setDeptIdList(list2);
        RpcResult<PaginationResult<String>> userCodesByApi = userApi.getUserCodes(userQueryParam2);
        System.out.printf(JSON.toJSONString(userCodesByApi));
        System.out.println("test end" + (System.currentTimeMillis() - start));
    }
}