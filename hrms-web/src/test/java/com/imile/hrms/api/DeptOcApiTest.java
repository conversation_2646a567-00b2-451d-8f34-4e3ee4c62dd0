package com.imile.hrms.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.hrms.api.enums.DeptDynamicFieldEnum;
import com.imile.hrms.api.organization.api.DeptApi;
import com.imile.hrms.api.organization.api.DeptOcApi;
import com.imile.hrms.api.organization.dto.DeptChainNodeDTO;
import com.imile.hrms.api.organization.dto.DeptDTO;
import com.imile.hrms.api.organization.dto.DeptDynamicInfoDTO;
import com.imile.hrms.api.organization.dto.DeptOwnerDTO;
import com.imile.hrms.api.organization.dto.DeptTreeDTO;
import com.imile.hrms.api.organization.dto.DeptTreeNodeDTO;
import com.imile.hrms.api.organization.dto.WarehouseOcDTO;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import com.imile.hrms.api.organization.query.DeptTreeConditionParam;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.service.ApplicationTest;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/22
 */
@Slf4j
public class DeptOcApiTest extends ApplicationTest {

    @Resource
    private DeptOcApi deptOcApi;

    @Test
    public void getWarehouseOcList() {
        List<String> ocList = Lists.newArrayList("S2101456201");
        RpcResult<List<WarehouseOcDTO>> result = deptOcApi.getWarehouseOcList(ocList);
        log.info("result:{}", JSON.toJSONString(result.getResult()));
    }
}
