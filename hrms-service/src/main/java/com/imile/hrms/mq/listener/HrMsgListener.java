package com.imile.hrms.mq.listener;

import com.imile.hrms.mq.basic.BusinessProcessorRegistry;
import com.imile.hrms.mq.basic.MqMsgBusinessProcessor;
import com.imile.hrms.mq.basic.mapping.HrTagMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:  监听HR相关的消息
 * @author: taokang
 * @createDate: 2022-10-8
 * @version: 1.0
 */
@Service
@Slf4j
public class HrMsgListener implements MessageListenerConcurrently {
    @Autowired
    private BusinessProcessorRegistry businessProcessorRegistry;

    @Autowired
    private HrTagMapping hrTagMapping;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        MessageExt messageExt = list.get(0);
        try {
            log.info("收到HR的消息，msgId=" +messageExt.getMsgId() + "msgKey="+messageExt.getKeys());
            MqMsgBusinessProcessor executor = businessProcessorRegistry.findProcessor(hrTagMapping.get(messageExt.getTags()));
            executor.execute(messageExt);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("接收HR的mq消息发生异常，msgId="+messageExt.getMsgId(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
}
