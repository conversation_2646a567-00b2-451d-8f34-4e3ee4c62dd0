package com.imile.hrms.mq.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.dtrack.api.dto.event.DtrackEventConst;
import com.imile.hrms.mq.basic.BusinessProcessorRegistry;
import com.imile.hrms.mq.basic.mapping.DtrackHrTagMapping;
import com.imile.hrms.service.driver.attendance.HrmsDriverPunchRecordService;
import com.imile.saas.tms.api.event.EventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} TmsHrMsgListener
 * {@code @since:} 2024-01-18 19:03
 * {@code @description:}
 */
@Service
@Slf4j
public class DtrackHrMsgListener implements MessageListenerConcurrently {
    @Autowired
    private BusinessProcessorRegistry businessProcessorRegistry;

    @Autowired
    private DtrackHrTagMapping dtrackHrTagMapping;

    @Autowired
    private HrmsDriverPunchRecordService hrmsDriverPunchRecordService;
    //@Override
    //public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
    //    MessageExt messageExt = list.get(0);
    //    try {
    //        log.info("收到DA->HR的消息，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
    //        MqMsgBusinessProcessor executor = businessProcessorRegistry.findProcessor(dtrackHrTagMapping.get(messageExt.getTags()));
    //        executor.execute(messageExt);
    //        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    //    } catch (Exception e) {
    //        log.error("接收DA->HR的mq消息发生异常，msgId=" + messageExt.getMsgId(), e);
    //        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
    //    }
    //}

    /**
     * 由于DA轨迹打卡的topic，在生成者那边没有设置tag，所以这边不能根据tag获取具体处理类，只能在这边处理业务逻辑，不是在处理类那边处理业务逻辑：【如果说后面设置了tag，就可以使用上面注释的代码了】
     * @param list msgs.size() >= 1<br> DefaultMQPushConsumer.consumeMessageBatchMaxSize=1,you can modify here
     * @param consumeConcurrentlyContext 消费上下文
     * @return The consume status
     */
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        MessageExt message = list.get(0);
        try {
            log.info("收到DA->HR的消息，msgId=" + message.getMsgId() + "msgKey=" + message.getKeys());
            log.info("DtrackHrMsgListener|consumeMessage:{}", JSON.toJSON(message));
            EventDTO eventDTO =  JSON.parseObject(new String(message.getBody()), EventDTO.class);
            if(ObjectUtil.isNull(eventDTO)){
                log.info("DtrackHrMsgListener|consumeMessage｜eventDTO为null");
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            log.info("DtrackHrMsgListener|consumeMessage｜eventDTO:{}", JSON.toJSON(eventDTO));
            String operateTags = message.getTags();
            log.info("监听DA轨迹数据tag：" + operateTags);
            // 业务数据：DtrackHubEventPayload
            if (ObjectUtil.equal(eventDTO.getBizType(), DtrackEventConst.BizType.EVENT) &&
                    ObjectUtil.equal(eventDTO.getOperateType(), DtrackEventConst.OperateType.EVENT_HUB_IN)) {
                log.info("EVENT_HUB_IN｜DA轨迹入口｜满足条件进入if");
                hrmsDriverPunchRecordService.handleDtrackHubEventPayload(eventDTO);
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("接收DA->HR的mq消息发生异常，msgId=" + message.getMsgId(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
}
