package com.imile.hrms.mq.processor;

import com.alibaba.fastjson.JSON;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.hrms.common.constants.BpmMqTagsConstant;
import com.imile.hrms.mq.HrMqEventConstant;
import com.imile.hrms.mq.basic.MqMsgBusinessProcessor;
import com.imile.hrms.mq.basic.MsgType;
import com.imile.hrms.service.zkteco.ZktecoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;

/**
 * @description: 同步员工到中控的事件处理器
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncEmployeeToZkEventProcessor implements MqMsgBusinessProcessor {

    @Autowired
    private ZktecoService zktecoService;
    @Override
    public MsgType getMsgType() {
        return MsgType.SYNC_EMPLOYEE_DRIVER_TO_ZK_EVENT;
    }

    @Override
    public void execute(MessageExt message) throws ParseException {
        Long userId = Long.valueOf(message.getKeys());
        String operateTags = message.getTags();
        switch (operateTags) {
            case HrMqEventConstant.SYNC_EMPLOYEE_DRIVER_TO_ZK_TAG:
                zktecoService.syncEmployee2Zkteco(userId);
                return;
            default:
                //其他事件我们不关注
                return;
        }
    }
}
