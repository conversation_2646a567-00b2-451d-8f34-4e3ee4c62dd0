package com.imile.hrms.mq.listener;

import com.imile.hrms.mq.basic.BusinessProcessorRegistry;
import com.imile.hrms.mq.basic.MqMsgBusinessProcessor;
import com.imile.hrms.mq.basic.mapping.TmsHrTagMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} TmsHrMsgListener
 * {@code @since:} 2024-01-18 19:03
 * {@code @description:}
 */
@Service
@Slf4j
public class TmsHrMsgListener implements MessageListenerConcurrently {
    @Autowired
    private BusinessProcessorRegistry businessProcessorRegistry;

    @Autowired
    private TmsHrTagMapping tmsHrTagMapping;
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        MessageExt messageExt = list.get(0);
        try {
            log.info("收到TMS->HR的消息，msgId=" + messageExt.getMsgId() + "msgKey=" + messageExt.getKeys());
            MqMsgBusinessProcessor executor = businessProcessorRegistry.findProcessor(tmsHrTagMapping.get(messageExt.getTags()));
            executor.execute(messageExt);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("接收TMS->HR的mq消息发生异常，msgId=" + messageExt.getMsgId(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
}
