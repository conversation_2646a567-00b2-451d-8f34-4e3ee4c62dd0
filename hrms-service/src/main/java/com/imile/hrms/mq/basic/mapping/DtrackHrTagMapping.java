package com.imile.hrms.mq.basic.mapping;

import com.imile.dtrack.api.dto.event.DtrackEventConst;
import com.imile.hrms.mq.basic.MsgType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * {@code @author:} allen
 * {@code @className:} DtrackHrTagMapping
 * {@code @since:} 2024-01-18 20:01
 * {@code @description:}
 */
@Service
public class DtrackHrTagMapping {
    private Map<String, MsgType> tag2MsgTypeMap;

    public DtrackHrTagMapping() {
        tag2MsgTypeMap = new HashMap<>();

        initBusinessMapper();
    }

    private void initBusinessMapper() {
        tag2MsgTypeMap.put(DtrackEventConst.BizType.EVENT, MsgType.MONITOR_DA_TRACK_EVENT);
    }
    public MsgType get(String tag) {
        return tag2MsgTypeMap.get(tag);
    }
}
