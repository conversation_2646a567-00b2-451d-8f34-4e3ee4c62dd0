package com.imile.hrms.mq.processor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.hrms.common.enums.organization.PostFamilyTypeEnum;
import com.imile.hrms.common.enums.organization.PostFieldEnum;
import com.imile.hrms.dao.organization.query.PostConditionBuilder;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.primary.entity.condition.UserConditionBuilder;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.mq.HrMqEventConstant;
import com.imile.hrms.mq.basic.MqMsgBusinessProcessor;
import com.imile.hrms.mq.basic.MsgType;
import com.imile.hrms.mq.param.PostEventParam;
import com.imile.hrms.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024//29
 */
@Slf4j
@Service
public class PostEventProcessor implements MqMsgBusinessProcessor {

    @Resource
    private PostManage postManage;
    @Resource
    private UserManage userManage;
    @Resource
    private UserService userService;

    @Override
    public MsgType getMsgType() {
        return MsgType.POST_EVENT;
    }

    @Override
    public void execute(MessageExt message) {
        String operateTags = message.getTags();
        if (HrMqEventConstant.POST_INFO_CHANGE.equals(operateTags)) {
            PostEventParam.PostInfoChange param
                    = JSON.parseObject(new String(message.getBody()), PostEventParam.PostInfoChange.class);
            log.info("收到岗位信息变更事件消息,msgId:{},topic:{},tags:{},param:{}",
                    message.getMsgId(), message.getTopic(), message.getTags(), JSON.toJSONString(param));
            // 判断岗位关联的职位族类是否变更
            List<String> postFamilyFieldList = Lists.newArrayList(PostFieldEnum.JOB_FAMILY_ID.getKey(),
                    PostFieldEnum.JOB_CATEGORY_ID.getKey(),
                    PostFieldEnum.JOB_SUB_CATEGORY_ID.getKey());
            boolean isPostFamilyChanged = param.getChangeFieldList().stream()
                    .anyMatch(s -> postFamilyFieldList.contains(s.getFieldName()));
            if (!isPostFamilyChanged) {
                return;
            }
            // 变更过则再根据岗位查询人员
            List<String> userCodeList = userManage.getUserCodeByCondition(UserConditionBuilder.builder()
                    .postId(param.getId())
                    .build());
            userService.syncUser2Wecom(userCodeList);
        } else if (HrMqEventConstant.POST_FAMILY_INFO_CHANGE.equals(operateTags)) {
            PostEventParam.PostFamilyInfoChange param
                    = JSON.parseObject(new String(message.getBody()), PostEventParam.PostFamilyInfoChange.class);
            log.info("收到职位族类信息变更事件消息,msgId:{},topic:{},tags:{},param:{}",
                    message.getMsgId(), message.getTopic(), message.getTags(), JSON.toJSONString(param));
            // 根据职位族类ID及层级查询涉及的岗位
            PostConditionBuilder condition = PostConditionBuilder.builder()
                    .build();
            if (param.getLevel().equals(PostFamilyTypeEnum.FAMILY.getLevel())) {
                condition.setFamilyIdList(Lists.newArrayList(param.getId()));
            } else if (param.getLevel().equals(PostFamilyTypeEnum.CATEGORY.getLevel())) {
                condition.setCategoryIdList(Lists.newArrayList(param.getId()));
            } else if (param.getLevel().equals(PostFamilyTypeEnum.SUB_CATEGORY.getLevel())) {
                condition.setSubCategoryIdList(Lists.newArrayList(param.getId()));
            }
            List<PostDO> postList = postManage.getPostByCondition(condition);
            if (postList.isEmpty()) {
                return;
            }
            List<Long> postIdList = postList.stream()
                    .map(PostDO::getId)
                    .collect(Collectors.toList());
            // 再根据涉及岗位查询人员
            List<String> userCodeList = userManage.getUserCodeByCondition(UserConditionBuilder.builder()
                    .postIdList(postIdList)
                    .build());
            userService.syncUser2Wecom(userCodeList);
        }
    }
}