package com.imile.hrms.mq.param;

import com.imile.hrms.service.log.component.OperationFieldDiffer;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserEventParam<T> implements Serializable {

    private static final long serialVersionUID = -4697417453800042435L;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 操作人
     */
    private UserInfoDTO operator;

    /**
     * 消息体
     */
    private T body;

    @Data
    public static class Default implements Serializable {

        private static final long serialVersionUID = -4151887183028375660L;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Entry implements Serializable {

        private static final long serialVersionUID = 6452555323612274940L;

        /**
         * 人员编码
         */
        private String userCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseInfoChange implements Serializable {

        private static final long serialVersionUID = 3592000820884555482L;

        /**
         * 变动字段比对列表
         */
        private List<OperationFieldDiffer> changeFieldList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Transform implements Serializable {

        private static final long serialVersionUID = 3592000820884555482L;

        /**
         * 调动ID
         */
        private Long transformId;

        /**
         * 变动字段比对列表
         */
        private List<OperationFieldDiffer> changeFieldList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Dimission implements Serializable {

        private static final long serialVersionUID = -4077362467095328861L;
        // 历史字段 字段名沿用之前的
        /**
         * 离职人员ID
         */
        private Long dimissionUserId;

        /**
         * 离职人员编码
         */
        private String dimissionUserCode;

        /**
         * 交接人人员ID
         */
        private Long transfereeId;

        /**
         * 交接人人员编码
         */
        private String transfereeUserCode;
    }
}
