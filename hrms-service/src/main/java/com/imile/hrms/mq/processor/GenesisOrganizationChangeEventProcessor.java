package com.imile.hrms.mq.processor;

import com.alibaba.fastjson.JSON;
import com.imile.genesis.api.enums.DatabaseEnum;
import com.imile.genesis.api.enums.GenesisProducerTagsEnum;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.hrms.mq.basic.MqMsgBusinessProcessor;
import com.imile.hrms.mq.basic.MsgType;
import com.imile.hrms.mq.helper.OperatorHelper;
import com.imile.hrms.mq.param.GenesisDataSyncParam;
import com.imile.hrms.service.organization.EntDeptNewService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/26
 */
@Slf4j
@Service
public class GenesisOrganizationChangeEventProcessor implements MqMsgBusinessProcessor {

    @Autowired
    private EntDeptNewService entDeptNewService;

    @Override
    public MsgType getMsgType() {
        return MsgType.GENESIS_ORGANIZATION_CHANGE_EVENT;
    }

    @Override
    public void execute(MessageExt message) {
        GenesisDataSyncParam param = JSON.parseObject(new String(message.getBody()), GenesisDataSyncParam.class);
        log.info("收到主数据组织事件消息,msgId:{},topic:{},tags:{},param:{}",
                message.getMsgId(), message.getTopic(), message.getTags(), JSON.toJSONString(param));
        // 由于Jar包版本落后无法识别新增场景时默认设为未知
        if (Objects.isNull(param.getOperationScene())) {
            param.setOperationScene(OperationSceneEnum.UNKNOWN);
        }
        // 操作场景对应hrms数据库的无需处理
        if (DatabaseEnum.HRMS.equals(param.getOperationScene().getDatabase())) {
            log.info("操作场景{}对应的数据源为hrms,无需处理", param.getOperationScene().getCode());
            return;
        }
        OperatorHelper.setOperatorInfo(param.getOperator());
        String operateTags = message.getTags();
        if (GenesisProducerTagsEnum.ORGANIZATION_SAVE.getCode().equals(operateTags)) {
            entDeptNewService.handleCustomDept(param.getDataCode());
        } else if (GenesisProducerTagsEnum.ORGANIZATION_STATUS_SWITCH.getCode().equals(operateTags)) {
            // todo 其他业务系统对接后补充
        }
    }
}
