package com.imile.hrms.config;

import com.imile.hrms.mq.listener.GenesisMsgListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/5/26
 */
@Slf4j
@Configuration
public class RocketMQGenesisConsumerConf {

    @Value("${rocket.mq.nameSrvAddr}")
    private String nameSrvAddr;
    @Value("${rocketmq.genesis.consumer.group}")
    private String consumerGroupName;
    @Value("${rocketmq.genesis.user.change.topic}")
    private String userChangeTopic;
    @Value("${rocketmq.genesis.organization.change.topic}")
    private String organizationChangeTopic;
    @Value("${rocketmq.genesis.user.change.tags}")
    private String userChangeTags;
    @Value("${rocketmq.genesis.organization.change.tags}")
    private String organizationChangeTags;
    @Resource
    private GenesisMsgListener genesisMsgListener;

    @Bean(initMethod = "start")
    public DefaultMQPushConsumer genesisConsumer() throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(this.consumerGroupName);
        consumer.setNamesrvAddr(this.nameSrvAddr);
        consumer.setMessageModel(MessageModel.CLUSTERING);
        consumer.setConsumeMessageBatchMaxSize(1);
        consumer.subscribe(this.userChangeTopic, this.userChangeTags);
        consumer.subscribe(this.organizationChangeTopic, this.organizationChangeTags);
        consumer.registerMessageListener(genesisMsgListener);
        return consumer;
    }
}
