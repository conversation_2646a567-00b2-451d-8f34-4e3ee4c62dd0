package com.imile.hrms.service.user.result.transform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/9/10
 */
@Data
@Builder
public class UserTransformListBO {

    /**
     * 调动ID
     */
    private Long id;

    /**
     * 人员ID
     */
    private Long userId;

    /*=======================================人员快照 start=========================================*/
    /**
     * 姓名
     */
    private String userName;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 调动状态（1:调动中 3:调动驳回 5:调动取消 7:调动完成）
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.USER_TRANSFORM_STATUS, ref = "transformStatusDesc")
    private Integer transformStatus;
    private String transformStatusDesc;

    /**
     * 调动类型（1:员工调动 2:组织调整调动 3:司机切换 4:特殊调动）
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.USER_TRANSFORM_TYPE, ref = "transformTypeDesc")
    private Integer transformType;
    private String transformTypeDesc;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    private String employeeTypeDesc;

    /**
     * 所属部门id
     */
    @RelationObjectValue(beanId = "hrmsDeptNewManageImpl", fieldId = "deptName", extendFieldIdList = "deptNamePath",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门链
     */
    private String deptNamePath;

    /**
     * 工作岗位
     */
    @RelationObjectValue(beanId = "postManageImpl", fieldId = "postName", fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗职体系id
     */
    @RelationObjectValue(beanId = "gradeManageImpl", fieldId = "jobSequence")
    private Long gradeId;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 职级序列
     */
    private String jobSequence;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;


    /*=======================================人员快照 end=========================================*/
    /**
     * 审批单据ID
     */
    private Long approvalId;

    /**
     * 调动时间（生效时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transformTime;

    /**
     * 是否全球派遣（0:否 1:是）
     */
    private Integer isGlobalRelocation;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

}
