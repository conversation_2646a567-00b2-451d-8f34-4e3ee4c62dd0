package com.imile.hrms.service.user.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
@Data
public class UserVisaSaveParam {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 签证类型（1:旅游签证 2:商务签证 3:落地签证 4:工作签证）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer visaType;

    /**
     * 签证号
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String visaNo;

    /**
     * 生效日期
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 失效日期
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 签发组织
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String issueOrganization;

    /**
     * 签发地点
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String issuePlace;

    /**
     * 签证正面照路径
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String visaFrontPath;
}
