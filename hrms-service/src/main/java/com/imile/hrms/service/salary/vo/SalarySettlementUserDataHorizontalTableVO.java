package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.service.salary.dto.SalarySettlementUserDataDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/12 10:45
 * @version: 1.0
 */
@Data
public class SalarySettlementUserDataHorizontalTableVO {

    /**
     * 结薪员工ID
     */
    private Long settlementUserInfoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户姓名
     */
    private String userNameEn;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 离职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dimissionDate;

    /**
     * 计薪方案名称
     */
    private String schemeName;

    /**
     * 计薪方案编码
     */
    private String schemeNo;

    /**
     * 计薪方案对应计薪国下的币种
     */
    private String currency;

    /**
     * 数据统计周期(计薪方案周期)
     */
    private String dataCollectCycle;

    /**
     * 计薪月份(202308)
     */
    private String paymentMonth;

    /**
     * 员工薪资档案生效时间
     */
    private Date userSchemeEffectTime;

    /**
     * 员工薪资档案生效时间
     */
    private Date userSchemeExpireTime;

    /**
     * 改用户科目信息
     */
    private List<SalarySettlementUserDataDTO> settlementUserDataDTOList;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String createUserCode;

    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
}
