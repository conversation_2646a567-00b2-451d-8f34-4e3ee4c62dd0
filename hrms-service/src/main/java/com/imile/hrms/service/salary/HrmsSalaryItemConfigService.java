package com.imile.hrms.service.salary;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.service.salary.param.SalaryAttendanceItemListParam;
import com.imile.hrms.service.salary.param.SalaryAttendanceItemUpdateParam;
import com.imile.hrms.service.salary.param.SalaryItemConfigAddParam;
import com.imile.hrms.service.salary.param.SalaryItemConfigCountryQueryParam;
import com.imile.hrms.service.salary.param.SalaryItemConfigCountryUpdateParam;
import com.imile.hrms.service.salary.param.SalaryItemConfigDetailParam;
import com.imile.hrms.service.salary.param.SalaryItemConfigInfoParam;
import com.imile.hrms.service.salary.param.SalaryItemConfigStatusSwitchParam;
import com.imile.hrms.service.salary.vo.SalaryAttendanceItemListVO;
import com.imile.hrms.service.salary.vo.SalaryItemConfigCountryDetailVO;
import com.imile.hrms.service.salary.vo.SalaryItemConfigDetailVO;
import com.imile.hrms.service.salary.vo.SalaryItemConfigVO;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/24 10:55
 * @version: 1.0
 */
public interface HrmsSalaryItemConfigService {

    /**
     * 列表查询
     */
    PaginationResult<SalaryItemConfigVO> list(SalaryItemConfigInfoParam param);

    /**
     * 列表导出
     */
    PaginationResult<SalaryItemConfigVO> listExport(SalaryItemConfigInfoParam param);

    /**
     * 薪资项新增
     */
    void salaryItemAdd(SalaryItemConfigAddParam param);

    /**
     * 薪资项适用国家编辑
     */
    void salaryItemCountryUpdate(SalaryItemConfigCountryUpdateParam param);

    /**
     * 详情
     */
    SalaryItemConfigDetailVO detail(SalaryItemConfigDetailParam param);

    /**
     * 薪资项停启用
     */
    void statusSwitch(SalaryItemConfigStatusSwitchParam param);

    /**
     * 获取所有国家下的薪资项
     */
    List<SalaryItemConfigCountryDetailVO> countryList(SalaryItemConfigCountryQueryParam param);

    /**
     * 获取所有国家下的薪资项 并集
     */
    List<SalaryItemConfigDetailVO> countryItemList(SalaryItemConfigCountryQueryParam param);

    /**
     * 考勤项目映射更新
     */
    void attendanceItemUpdate(SalaryAttendanceItemUpdateParam param);


    /**
     * 考勤项目映射查询
     */
    PaginationResult<SalaryAttendanceItemListVO> attendanceItemList(SalaryAttendanceItemListParam param);


}
