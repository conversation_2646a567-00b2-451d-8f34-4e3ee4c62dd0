package com.imile.hrms.service.punch.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.fms.api.bill.dto.BillingRecordDTO;
import com.imile.fms.api.bill.dto.FeeBillingRecordQuery;
import com.imile.fms.api.bill.enums.BusinessNoTypeApiEnum;
import com.imile.fms.api.bill.enums.SettlementObjectApiEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.punch.WarehouseAttendanceStatusEnum;
import com.imile.hrms.common.util.DateFormatterUtil;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailDao;
import com.imile.hrms.dao.punch.dto.WarehouseAttendanceConfigDetailDTO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.query.WarehouseAttendanceConfigQuery;
import com.imile.hrms.integration.fms.FmsService;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.mq.producer.RocketMessage;
import com.imile.hrms.service.newAttendance.punchConfig.adapter.PunchConfigServiceAdapter;
import com.imile.hrms.service.punch.WarehouseAttendancePushFinService;
import com.imile.hrms.service.punch.dto.WarehouseAttendancePushFinDTO;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓内考勤数据推送财务系统
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
@Slf4j
@Service
public class WarehouseAttendancePushFinServiceImpl implements WarehouseAttendancePushFinService {

    @Resource
    private FmsService fmsService;

    @Resource
    private EntOcService entOcService;

//    @Resource
//    private HrmsAttendancePunchConfigService punchConfigService;

    @Resource
    private PunchConfigServiceAdapter punchConfigServiceAdapter;

    @Resource
    private HrmsWarehouseDetailDao warehouseDetailDao;

    @Resource
    private HrmsEntDeptDao entDeptDao;

    @Resource
    private ApplicationEventPublisher publisher;

    @Value("${rocket.mq.push.waukeen.attendance.topic:SAAS-WAUKEEN-BILL-TEST}")
    private String pushFinAttendanceTopic;

    @Async("taskExecutor")
    @Override
    public void asyncPushFin(List<Long> warehouseDetailIds, String stateType) {
        if (CollectionUtils.isEmpty(warehouseDetailIds)) {
            return;
        }
        List<HrmsWarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(warehouseDetailIds);
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            return;
        }

        //过滤得到所有劳务派遣且有考勤时长的记录
        warehouseDetailList = warehouseDetailList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())
                        && Lists.newArrayList(WarehouseAttendanceStatusEnum.NORMAL.getCode(), WarehouseAttendanceStatusEnum.ABNORMAL.getCode()).contains(warehouse.getAttendanceStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.info("无劳务派遣仓内考勤推送财务记录");
            return;
        }

        List<Long> ocIdList = warehouseDetailList.stream()
                .map(HrmsWarehouseDetailDO::getOcId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, Long> deptMap = entDeptDao.selectByIdList(ocIdList).stream().collect(Collectors.toMap(HrmsEntDeptDO::getId, HrmsEntDeptDO::getOcId));

        Map<Long, String> ocMap = entOcService.getOcByIds(BusinessConstant.DEFAULT_ORG_ID, new ArrayList<>(deptMap.values()))
                .stream()
                .collect(Collectors.toMap(EntOcApiDTO::getId, EntOcApiDTO::getOcCode, (v1, v2) -> v1));

        Date now = new Date();
        warehouseDetailList.forEach(warehouse -> {
            Long tmsOcId = deptMap.get(warehouse.getOcId());
            String warehouseAttendanceCode = buildWarehouseAttendanceCode(warehouse, ocMap.get(tmsOcId));

            //查询账单是否已锁定
            FeeBillingRecordQuery query = new FeeBillingRecordQuery();
            query.setBusinessNo(warehouseAttendanceCode);
            query.setSourceSettlementType(SettlementObjectApiEnum.SETTLEMENT_CENTER.getCode());
            query.setTargetSettlementType(SettlementObjectApiEnum.VENDOR.getCode());
            query.setBusinessNoType(BusinessNoTypeApiEnum.LABOUR_PUNCH_NO.getCode());
            List<BillingRecordDTO> billingRecords = fmsService.queryFeeBillingRecords(query);
            if (CollectionUtils.isNotEmpty(billingRecords)) {
                boolean immutable = billingRecords.stream().anyMatch(BillingRecordDTO::getImmutable);
                if (immutable) {
                    log.info("仓内考勤流水号: {} ,推送财务账单已锁定", warehouseAttendanceCode);
                    return;
                }
            }
            WarehouseAttendancePushFinDTO.AttendanceRecord attendanceRecord = new WarehouseAttendancePushFinDTO.AttendanceRecord();
            attendanceRecord.setWarehouseAttendanceCode(warehouseAttendanceCode);
            attendanceRecord.setBusinessCountry(warehouse.getCountry());
            attendanceRecord.setOcCode(ocMap.get(tmsOcId));
            attendanceRecord.setVendorCode(warehouse.getVendorCode());
            attendanceRecord.setUserCode(warehouse.getUserCode());
            attendanceRecord.setClassesType(warehouse.getClassesType());
            attendanceRecord.setClassesId(warehouse.getClassesId());
            attendanceRecord.setClassesName(warehouse.getClassesName());
            attendanceRecord.setEmploymentForm(warehouse.getEmploymentForm());
            attendanceRecord.setSalaryDate(warehouse.getSalaryDate());
            if (Objects.equals(warehouse.getWarehouseDate().getTime(), warehouse.getSalaryDate().getTime())) {
                attendanceRecord.setAttendanceType(warehouse.getAttendanceType());
            } else {
                attendanceRecord.setAttendanceType(getCurrentDayType(warehouse.getSalaryDate(), warehouse));
            }

            if (warehouse.getRequiredAttendanceTime().compareTo(BigDecimal.ZERO) > 0) {
                if (warehouse.getActualAttendanceTime().compareTo(BigDecimal.ZERO) < 1) {
                    attendanceRecord.setActualAttendanceDay(BigDecimal.ZERO);
                } else if (warehouse.getActualAttendanceTime().compareTo(warehouse.getRequiredAttendanceTime()) > -1) {
                    attendanceRecord.setActualAttendanceDay(BigDecimal.ONE);
                } else {
                    attendanceRecord.setActualAttendanceDay(warehouse.getActualAttendanceTime().divide(warehouse.getRequiredAttendanceTime(), 3, RoundingMode.HALF_UP));
                }
            }

            if (warehouse.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0) {
                if (warehouse.getActualWorkingHours().compareTo(BigDecimal.ZERO) < 1) {
                    attendanceRecord.setActualWorkingDay(BigDecimal.ZERO);
                } else if (warehouse.getActualWorkingHours().compareTo(warehouse.getLegalWorkingHours()) > -1) {
                    attendanceRecord.setActualWorkingDay(BigDecimal.ONE);
                } else {
                    attendanceRecord.setActualWorkingDay(warehouse.getActualWorkingHours().divide(warehouse.getLegalWorkingHours(), 3, RoundingMode.HALF_UP));
                }
            }

            attendanceRecord.setActualAttendanceTime(warehouse.getActualAttendanceTime());
            attendanceRecord.setActualWorkingHours(warehouse.getActualWorkingHours());
            attendanceRecord.setOvertimeHours(BigDecimal.ZERO);
            attendanceRecord.setWeek(getWeek(warehouse.getSalaryDate()));

            if (StringUtils.isNotEmpty(warehouse.getWarehouseAttendanceCode()) && !Objects.equals(warehouse.getWarehouseAttendanceCode(), warehouseAttendanceCode)) {
                attendanceRecord.setExpiredWarehouseAttendanceCode(warehouse.getWarehouseAttendanceCode());
                attendanceRecord.setLastSalaryDate(warehouse.getSalaryDate());
            }
            warehouse.setWarehouseAttendanceCode(warehouseAttendanceCode);
            BaseDOUtil.fillDOUpdateFromUCenter(warehouse);

            WarehouseAttendancePushFinDTO attendancePushFin = new WarehouseAttendancePushFinDTO();
            attendancePushFin.setStateType(stateType);
            attendancePushFin.setStateDate(now);
            attendancePushFin.setPayload(attendanceRecord);

            log.info("推送财务仓内考勤数据：{}", JSON.toJSONString(attendancePushFin));
            publisher.publishEvent(RocketMessage.<WarehouseAttendancePushFinDTO>builder()
                    .topic(pushFinAttendanceTopic)
                    .tag(stateType)
                    .key(attendanceRecord.getWarehouseAttendanceCode())
                    .data(attendancePushFin)
                    .build());
        });

        warehouseDetailDao.updateBatchById(warehouseDetailList);
    }

    private String buildWarehouseAttendanceCode(HrmsWarehouseDetailDO warehouseDetailDO, String ocCode) {
        return DateUtil.format(warehouseDetailDO.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD) +
                BusinessConstant.RUNG + "0" + warehouseDetailDO.getClassesType() +
                BusinessConstant.RUNG + warehouseDetailDO.getClassesId() +
                BusinessConstant.RUNG + ocCode +
                BusinessConstant.RUNG + warehouseDetailDO.getVendorCode() +
                BusinessConstant.RUNG + warehouseDetailDO.getUserCode();
    }

    private Integer getWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK) - 1;
    }

    private String getCurrentDayType(Date attendanceTime, HrmsWarehouseDetailDO warehouseDetailDO) {
        WarehouseAttendanceConfigQuery query = new WarehouseAttendanceConfigQuery();
        query.setUserId(warehouseDetailDO.getUserId());
        query.setLocationCountry(warehouseDetailDO.getCountry());
        query.setNow(attendanceTime);
//        WarehouseAttendanceConfigDetailDTO warehouseAttendanceConfigDetail = punchConfigService.selectNowAttendanceConfigDetail(query);
        WarehouseAttendanceConfigDetailDTO warehouseAttendanceConfigDetail = punchConfigServiceAdapter.selectNowAttendanceConfigDetail(query);
        return Objects.nonNull(warehouseAttendanceConfigDetail) ? warehouseAttendanceConfigDetail.getDayType() : null;
    }


}
