package com.imile.hrms.service.log.result;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
public class OperationRecordBO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用编码
     */
    private String applicationCode;

    /**
     * 操作人编码
     */
    private String operatorUserCode;

    /**
     * 操作人名称
     */
    private String operatorUserName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作场景编码
     */
    private String operationSceneCode;

    /**
     * 操作场景名称
     */
    private String operationSceneName;

    /**
     * 操作编码
     */
    private String operationCode;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 操作对象编码
     */
    private String operationTargetCode;

    /**
     * 操作对象描述
     */
    private String operationTargetDesc;

    /**
     * 操作内容
     */
    private String operationContent;
}
