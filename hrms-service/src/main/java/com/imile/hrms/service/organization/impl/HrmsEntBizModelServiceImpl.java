package com.imile.hrms.service.organization.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.dao.organization.dto.BizModelListDTO;
import com.imile.hrms.dao.organization.dto.EntBizModelDTO;
import com.imile.hrms.dao.organization.dto.EntBizModelListDTO;
import com.imile.hrms.dao.organization.dto.EntBizModelSelectDTO;
import com.imile.hrms.dao.organization.dto.EntBizModelUpdateStatusDTO;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.dao.organization.query.BizModelQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.manage.organization.HrmsBizModelManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.imile.hrms.service.organization.HrmsEntBizModelService;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Service
@Slf4j
public class HrmsEntBizModelServiceImpl extends BaseService implements HrmsEntBizModelService {

    @Autowired
    private HrmsBizModelManage hrmsBizModelManage;

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Override
    public PaginationResult<EntBizModelSelectDTO> selectBizModel(BizModelQuery query) {
        Page<HrmsBizModelDO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<HrmsBizModelDO> pageInfo = page.doSelectPageInfo(() -> hrmsBizModelManage.selectBizModelList(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return getPageResult(new ArrayList<>(), query, (int) pageInfo.getTotal(), pageInfo.getPages());
        }
        List<EntBizModelSelectDTO> bizModelSelectDTOS = new ArrayList<>();

        pageInfo.getList().forEach(b -> {
            EntBizModelSelectDTO dto = BeanUtils.convert(b, EntBizModelSelectDTO.class);
            dto.setBizModelName(RequestInfoHolder.isChinese() ? b.getBizModelNameCn() : b.getBizModelNameEn());
            dto.setDescribe(RequestInfoHolder.isChinese() ? b.getDescribeCn() : b.getDescribeEn());
            bizModelSelectDTOS.add(dto);
        });

        return getPageResult(bizModelSelectDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public Boolean addBizModel(EntBizModelDTO dto) {
        //校验
        List<HrmsBizModelDO> bizModelDOS = hrmsBizModelManage.checkName(dto.getBizModelNameCn(), dto.getBizModelNameEn());
        if (CollectionUtils.isNotEmpty(bizModelDOS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.BIZ_MODEL_NAME_EXISTS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.BIZ_MODEL_NAME_EXISTS.getDesc()));
        }
        //新增
        HrmsBizModelDO hrmsBizModelDO = BeanUtils.convert(dto, HrmsBizModelDO.class);
        buildBizModelDO(hrmsBizModelDO);
        return hrmsBizModelManage.saveOrUpdateDO(hrmsBizModelDO);
    }

    @Override
    public Boolean updateBizModel(EntBizModelDTO dto) {
        //校验
        List<HrmsBizModelDO> bizModelDOS = hrmsBizModelManage.checkName(dto.getBizModelNameCn(), dto.getBizModelNameEn());
        if (CollectionUtils.isNotEmpty(bizModelDOS) && bizModelDOS.size() > 1) {
            throw BusinessException.get(HrmsErrorCodeEnums.BIZ_MODEL_NAME_EXISTS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.BIZ_MODEL_NAME_EXISTS.getDesc()));
        }
        //编辑
        HrmsBizModelDO hrmsBizModelDO = BeanUtils.convert(dto, HrmsBizModelDO.class);
        this.fillDOUpdate(hrmsBizModelDO);
        return hrmsBizModelManage.saveOrUpdateDO(hrmsBizModelDO);
    }

    @Override
    public Boolean updateBizModelStatus(EntBizModelUpdateStatusDTO dto) {

        //校验
        HrmsBizModelDO bizModelDO = hrmsBizModelManage.getBizModeById(dto.getId());
        if (bizModelDO.getStatus().equals(dto.getStatus())) {
            return Boolean.TRUE;
        }
        if (dto.getStatus().equals(StatusEnum.DISABLED.getCode())) {
            UserDaoQuery build = UserDaoQuery.builder()
                    .bizModelId(dto.getId())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .build();
            List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.userList(build);
            if (CollectionUtils.isNotEmpty(userInfoDOList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.BIZ_MODEL_STILL_USE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.BIZ_MODEL_STILL_USE.getDesc()));
            }
        }
        return hrmsBizModelManage.updateStatus(dto.getId(), dto.getStatus());
    }

    @Override
    public List<EntBizModelListDTO> getList(BizModelListDTO dto) {
        List<Long> idList = Lists.newArrayList();
        // 优先找部门配置的业务节点 如果没配置则返回全量业务节点
        if (Objects.nonNull(dto.getDeptId())) {
            idList = hrmsDeptManage.getBizModelIdList(dto.getDeptId());
        }
        List<HrmsBizModelDO> list = hrmsBizModelManage.getList(idList);
        return list.stream()
                .map(o -> {
                    EntBizModelListDTO item = BeanUtils.convert(o, EntBizModelListDTO.class);
                    item.setBizModelName(RequestInfoHolder.isChinese() ? o.getBizModelNameCn() : o.getBizModelNameEn());
                    item.setDescribe(RequestInfoHolder.isChinese() ? o.getDescribeCn() : o.getDescribeEn());
                    return item;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, String> getBizModelNameMap(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<HrmsBizModelDO> bizModelList = hrmsBizModelManage.getList(idList);
        return bizModelList.stream()
                .collect(Collectors.toMap(HrmsBizModelDO::getId,
                        s -> RequestInfoHolder.isChinese() ? s.getBizModelNameCn() : s.getBizModelNameEn()));
    }

    /**
     * 构建新增数据
     *
     * @param hrmsBizModelDO
     * @return
     */
    private void buildBizModelDO(HrmsBizModelDO hrmsBizModelDO) {
        hrmsBizModelDO.setId(iHrmsIdWorker.nextId());
        hrmsBizModelDO.setStatus(StatusEnum.ACTIVE.getCode());
        hrmsBizModelDO.setOrgId(RequestInfoHolder.getOrgId());
        this.fillDOInsert(hrmsBizModelDO);
    }
}
