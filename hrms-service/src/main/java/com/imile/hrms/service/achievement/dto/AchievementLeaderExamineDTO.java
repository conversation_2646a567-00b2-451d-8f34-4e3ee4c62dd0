package com.imile.hrms.service.achievement.dto;

import lombok.Data;

/**
 * <p>
 * 绩效领导审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class AchievementLeaderExamineDTO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 审核顺序
     */
    private Integer examineOrder;

    /**
     * 状态：0不是最终审核人 1最终审核人
     */
    private Integer isFinalUser;

    /**
     * 下一个审核人姓名
     */
    private String nextUserName;

}
