package com.imile.hrms.service.refactor.user.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Data
public class UserOutsourceExportParam extends ResourceQuery {

    /**
     * 工号
     */
    private String workNo;

    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 岗位id
     */
    private String postId;

    /**
     * 入职起始时间
     */
    private String entryStartTime;

    /**
     * 入职结束时间
     */
    private String entryEndTime;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 用工类型
     */
    private String employeeTypeList;

    /**
     * 部门
     */
    private String deptIdList;

    /**
     * 是否是司机
     */
    private String isDriver;

    /**
     * 入职渠道
     */
    private String onboardingChannel;
}
