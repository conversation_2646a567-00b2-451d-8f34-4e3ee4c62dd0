
package com.imile.hrms.service.ocr.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */

@Data
public class OcrCertificateRequestParam {

    /**
     * 文件
     */
    private String fileKey;

    /**
     * 文件
     */
    @JsonIgnore
    private MultipartFile file;

    /**
     * 常驻国
     */
    private String country;

    /**
     * 证件类型
     */
    private String certificateType;
}
