package com.imile.hrms.service.user;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.user.model.UserEducationDO;
import com.imile.hrms.service.user.param.UserEducationSaveParam;
import com.imile.hrms.service.user.result.UserEducationBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3
 */
public interface UserEducationService {

    /**
     * 获取人员教育经历差异
     *
     * @param userId    人员ID
     * @param paramList 人员教育经历列表
     * @return DataDifferHolder<UserEducationDO>
     */
    DataDifferHolder<UserEducationDO> differ(Long userId, List<UserEducationSaveParam> paramList);

    /**
     * 获取人员教育经历列表
     *
     * @param userId 人员ID
     * @return List<UserEducationBO>
     */
    List<UserEducationBO> getUserEducationList(Long userId);
}
