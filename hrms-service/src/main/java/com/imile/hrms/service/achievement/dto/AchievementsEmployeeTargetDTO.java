package com.imile.hrms.service.achievement.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AchievementsEmployeeTargetDTO implements Serializable {
    private static final long serialVersionUID = 3749391621199402536L;


    /**
     * 状态(0:未提交,1:已提交)
     */
    private String status;

    /**
     * 活动名称
     */
    private String eventName;

    /**
     * 员工考核ID
     */
    private Long employeeAppraisalId;

    /**
     * 考核责任人code
     */
    private String userCode;

    /**
     * 定性权重
     */
    private BigDecimal qualitativeWeight;

    /**
     * 定量权重
     */
    private BigDecimal quantifyWeight;

    /**
     * 指标明细
     */
    private List<AchievementsOrgTargetItemAddDTO> itemList;



}
