package com.imile.hrms.service.achievement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 绩效活动用户关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
public class AchievementsEventsUserDTO {

    /**
     * 用户id(后期扩展成其他id)
     */
    private Long useId;

    /**
     * 考核类型(1组织 2员工)
     */
    private String scopeType;

    /**
     * useId的类型（1 userId 2 roleId）
     */
    private String idType;

    /**
     * 流程id
     */
    private Long processId;


}
