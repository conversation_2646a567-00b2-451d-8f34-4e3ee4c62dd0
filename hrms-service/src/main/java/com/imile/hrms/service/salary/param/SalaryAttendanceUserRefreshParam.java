package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/18 16:21
 * @version: 1.0
 */
@Data
public class SalaryAttendanceUserRefreshParam {

    /**
     * 薪资每月计薪方案信息表ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long salaryAttendanceSchemeId;

    /**
     * 消息提示类别(为空表示就是页面的普通刷新，有值表示是消息提示中的刷新)
     */
    private String messageType;
}
