//package com.imile.hrms.service.sys.impl;
//
//import com.imile.hrms.common.enums.AttributeTypeEnum;
//import com.imile.hrms.common.enums.WorkStatusEnum;
//import com.imile.hrms.common.enums.audit.ApprovalRoleEnum;
//import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
//import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
//import com.imile.hrms.dao.user.dao.HrmsUserRoleDao;
//import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
//import com.imile.hrms.manage.user.HrmsUserInfoManage;
//import com.imile.hrms.service.sys.HrmsUserRoleService;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * @description:
// * @author: taokang
// * @createDate: 2022-9-29
// * @version: 1.0
// */
//@Service
//public class HrmsUserRoleServiceImpl implements HrmsUserRoleService {
//
//    @Autowired
//    private HrmsUserInfoManage hrmsUserInfoManage;
//    @Autowired
//    private HrmsUserRoleDao hrmsUserRoleDao;
//    @Autowired
//    private HrmsEntDeptDao hrmsEntDeptDao;
//
//    @Override
//    public List<String> getRoleUser(List<String> roleList, HrmsUserInfoDO userInfoDO) {
//        if (CollectionUtils.isEmpty(roleList)) {
//            return new ArrayList<>();
//        }
//        List<String> resultUserCodeList = new ArrayList<>();
//        for (String role : roleList) {
//            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COMPANY_ADMIN.getCode())) {
//                List<Long> userIdList = hrmsUserRoleDao.selectUserByRole(AttributeTypeEnum.SUBSIDIARY.getCode())
//                        .stream().map(item -> item.getUserId()).collect(Collectors.toList());
//                List<String> userCodeList = hrmsUserInfoManage.selectUserInfoByIds(userIdList)
//                        .stream().filter(item -> item.getCompanyId() != null && item.getCompanyId().equals(userInfoDO.getCompanyId()) && StringUtils.isNotBlank(item.getUserCode()) && StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()))
//                        .map(item -> item.getUserCode()).collect(Collectors.toList());
//                resultUserCodeList.addAll(userCodeList);
//            }
//            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_ADMIN.getCode())) {
//                List<Long> userIdList = hrmsUserRoleDao.selectUserByRole(AttributeTypeEnum.DEPT_SYS.getCode())
//                        .stream().map(item -> item.getUserId()).collect(Collectors.toList());
//                List<Long> hoUserIdList = hrmsUserRoleDao.selectUserByRole(AttributeTypeEnum.HO_DEPT_SYS.getCode())
//                        .stream().map(item -> item.getUserId()).collect(Collectors.toList());
//                userIdList.addAll(hoUserIdList);
//
//                List<String> userCodeList = hrmsUserInfoManage.selectUserInfoByIds(userIdList)
//                        .stream().filter(item -> item.getDeptId() != null && item.getDeptId().equals(userInfoDO.getDeptId()) && item.getCompanyId() != null && item.getCompanyId().equals(userInfoDO.getCompanyId()) && StringUtils.isNotBlank(item.getUserCode()) && StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()))
//                        .map(item -> item.getUserCode()).collect(Collectors.toList());
//                resultUserCodeList.addAll(userCodeList);
//            }
//            if (userInfoDO.getLeaderId() != null && StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.REPORT_LEADER.getCode())) {
//                HrmsUserInfoDO reportLeaderUserInfoDO = hrmsUserInfoManage.getUserInfoById(userInfoDO.getLeaderId());
//                if (reportLeaderUserInfoDO != null && StringUtils.isNotBlank(reportLeaderUserInfoDO.getUserCode()) && StringUtils.equalsIgnoreCase(reportLeaderUserInfoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
//                    resultUserCodeList.add(reportLeaderUserInfoDO.getUserCode());
//                }
//            }
//            if (userInfoDO.getDeptId() != null && StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_LEADER.getCode())) {
//                List<HrmsEntDeptDO> entDeptDOS = hrmsEntDeptDao.listByDeptIds(Arrays.asList(userInfoDO.getDeptId()));
//                if (CollectionUtils.isEmpty(entDeptDOS)) {
//                    continue;
//                }
//                HrmsUserInfoDO deptLeaderUserInfoDO = hrmsUserInfoManage.getUserInfoById(entDeptDOS.get(0).getLeaderCode());
//                if (deptLeaderUserInfoDO != null && StringUtils.isNotBlank(deptLeaderUserInfoDO.getUserCode()) && StringUtils.equalsIgnoreCase(deptLeaderUserInfoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
//                    resultUserCodeList.add(deptLeaderUserInfoDO.getUserCode());
//                }
//            }
//        }
//        return resultUserCodeList;
//    }
//}
