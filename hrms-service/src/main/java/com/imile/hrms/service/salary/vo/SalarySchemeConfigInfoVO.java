package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/26 15:01
 * @version: 1.0
 */
@Data
public class SalarySchemeConfigInfoVO {

    private Long id;

    /**
     * 计薪方案编码
     */
    private String schemeNo;

    /**
     * 计薪方案名称
     */
    private String schemeName;

    /**
     * 计薪国
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 计薪周期类型(月/单周/双周)
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_SCHEME_CYCLE_TYPE, ref = "cycleTypeDesc")
    private String cycleType;
    private String cycleTypeDesc;

    /**
     * 计薪周期-开始时间
     */
    private String cycleStart;

    /**
     * 计薪周期-结束时间
     */
    private String cycleEnd;

    /**
     * 发薪日期(依据计薪周期类型有不同类型的值)
     */
    private String payDateValue;

    /**
     * 关联员工
     */
    private Integer relationUserCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String createUserCode;

    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
}
