package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/29 17:53
 * @version: 1.0
 */
@Data
public class SalaryItemConfigDetailParam {

    /**
     * 薪资项ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long itemConfigId;
}
