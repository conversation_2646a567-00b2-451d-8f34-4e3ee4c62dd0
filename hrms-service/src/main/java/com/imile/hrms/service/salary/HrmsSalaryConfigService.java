package com.imile.hrms.service.salary;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.common.ConfigNoStatusSwitchParamDTO;
import com.imile.hrms.dao.salary.dto.ConfigSelectDTO;
import com.imile.hrms.dao.salary.dto.SalaryConfigDTO;
import com.imile.hrms.dao.salary.dto.SalaryConfigDetailDTO;
import com.imile.hrms.dao.salary.param.SalaryConfigAddParam;
import com.imile.hrms.dao.salary.param.SalaryConfigUpdateParam;
import com.imile.hrms.dao.salary.query.SalaryConfigQuery;

import java.util.List;
import java.util.Map;

/**
 * 计薪方案管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/29
 */
public interface HrmsSalaryConfigService {


    /**
     * 列表条件查询
     *
     * @param query
     * @return
     */
    PaginationResult<SalaryConfigDTO> list(SalaryConfigQuery query);

    /**
     * 详情查询
     *
     * @param salaryConfigNo
     * @return
     */
    SalaryConfigDetailDTO detail(String salaryConfigNo);

    /**
     * 新增
     *
     * @param salaryConfigAddParam
     */
    void add(SalaryConfigAddParam salaryConfigAddParam);

    /**
     * 修改
     *
     * @param salaryConfigUpdateParam
     */
    void update(SalaryConfigUpdateParam salaryConfigUpdateParam);

    /**
     * 状态切换
     *
     * @param statusSwitchParamDTO
     */
    void statusSwitch(ConfigNoStatusSwitchParamDTO statusSwitchParamDTO);

    /**
     * 下拉
     *
     * @param query
     * @return
     */
    List<ConfigSelectDTO> selectList(SalaryConfigQuery query);

}
