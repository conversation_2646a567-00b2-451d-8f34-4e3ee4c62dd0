package com.imile.hrms.service.achievement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 绩效评价规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
public class AchievementEvaluateRulePageDTO {

    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则名称(英文)
     */
    private String ruleNameEn;

    /**
     * 状态:1禁用 0启用
     */
    private Integer status;


    /**
     * 绩效评价规则id
     */
    private Long achievementEvaluateRuleId;

    /**
     * 职级名称
     */
    private String gradeName;

    /**
     * 职级id，隔开
     */
    private String gradeIds;

    /**
     * A权重
     */
    private BigDecimal aWeigh;

    /**
     * B+权重
     */
    private BigDecimal bPlusWeight;

    /**
     * B权重
     */
    private BigDecimal bWeight;

    /**
     * C权重
     */
    private BigDecimal cWeight;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdDate;

    /**
     * 更新人
     */
    private String lastUpdUserName;

    /**
     * 配置强制比例人数少于x人，不做校验
     */
    private Integer ruleNum;


}
