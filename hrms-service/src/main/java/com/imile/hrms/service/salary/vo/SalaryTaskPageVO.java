package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 计算任务列表
 *
 * <AUTHOR>
 * @since 2023/12/15
 */
@Data
public class SalaryTaskPageVO {

    private Long id;

    /**
     * 任务编码
     */
    private String taskNo;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务类型
     */
    private String taskTypeDesc;

    /**
     * 计薪方案id
     */
    private Long salarySchemeConfigId;

    /**
     * 计薪方案编码
     */
    private String salarySchemeConfigNo;

    /**
     * 计薪方案名称
     */
    private String salarySchemeConfigName;

    /**
     * 记薪周期
     */
    private String salaryConfigCycle;

    /**
     * 计薪周期类型(月/单周/双周)
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_SCHEME_CYCLE_TYPE, ref = "cycleTypeDesc")
    private String cycleType;

    /**
     * 计薪周期类型(月/单周/双周)
     */
    private String cycleTypeDesc;

    /**
     * 发薪日期
     */
    private String payDate;

    /**
     * 关联人数
     */
    private Integer associatedNumber;

    /**
     * 计算人数
     */
    private Integer calculateNumber;

    /**
     * 计算次数
     */
    private Integer calculateCount;

    /**
     * 任务状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_CALCULATE_TASK_STATUS, ref = "statusDesc")
    private String status;

    /**
     * 任务状态描述
     */
    private String statusDesc;

    /**
     * 计算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date calculateDate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 更新人
     */
    private String lastUpdUserName;

    /**
     * 添加类型
     */
    private String addType;

    /**
     * 实发工资总额
     */
    private BigDecimal totalSalary;

    /**
     * 币种
     */
    private String currency;

    /**
     * 是否暂存，如果字段不为空并且为1，点击查看的时候，不用调用calculate/halfWayEnd接口
     */
    private Integer isStaging;

    /**
     * 薪资数据类型(模板 + 是否选择)
     */
    private List<SalaryTaskSalaryDataTypePageVO> salaryDataTypeList;

    /**
     * 上期间参与算薪人数
     */
    private Integer lastCalculateNumber;

    /**
     * 算薪人数同上区间比的比例
     */
    private String calculateNumberRate;

    /**
     * 人员详情
     */
    private List<SalaryTaskSalaryCalculateUserPageVO> calculateUserList;
}
