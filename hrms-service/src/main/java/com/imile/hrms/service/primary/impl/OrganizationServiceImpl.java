package com.imile.hrms.service.primary.impl;

import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.organization.OrganizationAddParam;
import com.imile.genesis.api.model.param.organization.OrganizationUpdateParam;
import com.imile.hrms.api.primary.model.param.organization.OrganizationConditionParam;
import com.imile.hrms.api.primary.model.result.organization.OrganizationDTO;
import com.imile.hrms.dao.primary.dao.OrganizationDao;
import com.imile.hrms.dao.primary.entity.OrganizationDO;
import com.imile.hrms.dao.primary.entity.condition.OrganizationConditionBuilder;
import com.imile.hrms.integration.genesis.RpcOrganizationService;
import com.imile.hrms.manage.primary.OrganizationManage;
import com.imile.hrms.service.primary.OrganizationService;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Slf4j
@Service
public class OrganizationServiceImpl implements OrganizationService {

    @Autowired
    private OrganizationDao organizationDao;

    @Autowired
    private OrganizationManage organizationManage;

    @Autowired
    private RpcOrganizationService rpcOrganizationService;

    @Override
    public String addOrganization(OrganizationAddParam param) {
        param.setOperationScene(OperationSceneEnum.OPERATION);
        return rpcOrganizationService.addOrganization(param);
    }

    @Override
    public Boolean updateOrganization(OrganizationUpdateParam param) {
        param.setOperationScene(OperationSceneEnum.OPERATION);
        return rpcOrganizationService.updateOrganization(param);
    }

    @Override
    public OrganizationDTO getOrganizationByCode(String organizationCode) {
        OrganizationDO organization = organizationManage.getOrganizationByCode(organizationCode);
        return BeanUtils.convert(organization, OrganizationDTO.class);
    }

    @Override
    public List<OrganizationDTO> listOrganizationByCondition(OrganizationConditionParam param) {
        OrganizationConditionBuilder condition = BeanUtils.convert(param, OrganizationConditionBuilder.class);
        List<OrganizationDO> organizationList = organizationDao.selectByCondition(condition);
        if (organizationList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtils.convert(OrganizationDTO.class, organizationList);
    }
}
