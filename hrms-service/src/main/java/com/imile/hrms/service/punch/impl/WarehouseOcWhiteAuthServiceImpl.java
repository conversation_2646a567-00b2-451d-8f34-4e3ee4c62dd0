package com.imile.hrms.service.punch.impl;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseOcWhiteListDao;
import com.imile.hrms.dao.punch.model.HrmsWarehouseOcWhiteListDO;
import com.imile.hrms.dao.punch.param.WarehouseOcWhiteListParam;
import com.imile.hrms.dao.punch.param.WarehouseOcWhiteListSaveParam;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.service.punch.WarehouseOcWhiteAuthService;
import com.imile.hrms.service.punch.vo.warehouse.WarehouseOcWhiteListVO;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内考勤白名单服务
 *
 * <AUTHOR>
 * @since 2025/1/22
 */
@Slf4j
@Service
public class WarehouseOcWhiteAuthServiceImpl implements WarehouseOcWhiteAuthService {
    @Resource
    private HrmsWarehouseOcWhiteListDao warehouseOcWhiteListDao;
    @Resource
    private HrmsUserInfoDao userInfoDao;

    @Override
    public List<WarehouseOcWhiteListVO> warehouseOcWhiteList(WarehouseOcWhiteListParam param) {
        param.setCreateUserCode(RequestInfoHolder.getUserCode());
        param.setSuperAdmin(BusinessConstant.N);
        List<HrmsWarehouseOcWhiteListDO> warehouseOcWhiteList = warehouseOcWhiteListDao.selectByCondition(param);
        if (CollectionUtils.isEmpty(warehouseOcWhiteList)) {
            return Collections.emptyList();
        }
        List<Long> userIds = warehouseOcWhiteList.stream().map(HrmsWarehouseOcWhiteListDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userInfoMap = userInfoDao.getByUserIds(userIds).stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        return warehouseOcWhiteList.stream().map(data -> {
            WarehouseOcWhiteListVO whiteListVO = new WarehouseOcWhiteListVO();
            whiteListVO.setId(data.getId());
            whiteListVO.setUserId(data.getUserId());
            HrmsUserInfoDO user = userInfoMap.get(data.getUserId());
            if (Objects.nonNull(user)) {
                whiteListVO.setUserCode(user.getUserCode());
                whiteListVO.setUserName(user.getUserName());
                whiteListVO.setProfilePhotoKey(user.getProfilePhotoUrl());
            }
            whiteListVO.setCreateDate(data.getCreateDate());
            return whiteListVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean checkOcWhiteListConfigPermission() {
        WarehouseOcWhiteListParam param = new WarehouseOcWhiteListParam();
        param.setUserId(RequestInfoHolder.getUserId());
        param.setSuperAdmin(BusinessConstant.Y);
        List<HrmsWarehouseOcWhiteListDO> warehouseOcWhiteList = warehouseOcWhiteListDao.selectByCondition(param);
        if (CollectionUtils.isEmpty(warehouseOcWhiteList)) {
            return Boolean.FALSE;
        }
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.getByUserIds(warehouseOcWhiteList.stream().map(HrmsWarehouseOcWhiteListDO::getUserId).distinct().collect(Collectors.toList()))
                .stream()
                .filter(user -> Objects.equals(user.getStatus(), StatusEnum.ACTIVE.getCode())
                        && Objects.equals(user.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()))
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(userInfoDOList);
    }

    @Override
    public Boolean checkOcWhiteListPermission() {
        WarehouseOcWhiteListParam param = new WarehouseOcWhiteListParam();
        param.setUserId(RequestInfoHolder.getUserId());
        List<HrmsWarehouseOcWhiteListDO> warehouseOcWhiteList = warehouseOcWhiteListDao.selectByCondition(param);
        if (CollectionUtils.isEmpty(warehouseOcWhiteList)) {
            return Boolean.FALSE;
        }
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.getByUserIds(warehouseOcWhiteList.stream().map(HrmsWarehouseOcWhiteListDO::getUserId).distinct().collect(Collectors.toList()))
                .stream()
                .filter(user -> Objects.equals(user.getStatus(), StatusEnum.ACTIVE.getCode())
                        && Objects.equals(user.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()))
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(userInfoDOList);
    }

    @Override
    public Boolean saveOcWhiteList(WarehouseOcWhiteListSaveParam param) {
        HrmsUserInfoDO userInfoDO = Optional.ofNullable(userInfoDao.getById(param.getUserId()))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc())));
        if (!StringUtils.equalsIgnoreCase(userInfoDO.getWorkStatus(), BusinessConstant.WORK_STATUS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.PERSON_HAS_RESIGNED.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PERSON_HAS_RESIGNED.getDesc()));
        }
        if (!StringUtils.equalsIgnoreCase(userInfoDO.getStatus(), BusinessConstant.STATUS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getDesc()));
        }
        WarehouseOcWhiteListParam ocWhiteListParam = new WarehouseOcWhiteListParam();
        ocWhiteListParam.setUserId(userInfoDO.getId());
        List<HrmsWarehouseOcWhiteListDO> warehouseOcWhiteList = warehouseOcWhiteListDao.selectByCondition(ocWhiteListParam);
        if (CollectionUtils.isNotEmpty(warehouseOcWhiteList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.THE_OC_ADMIN_ALREADY_EXISTS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.THE_OC_ADMIN_ALREADY_EXISTS.getDesc()));
        }
        HrmsWarehouseOcWhiteListDO warehouseOcWhiteListDO = new HrmsWarehouseOcWhiteListDO();
        warehouseOcWhiteListDO.setUserId(param.getUserId());
        warehouseOcWhiteListDO.setSuperAdmin(param.getSuperAdmin());
        if (Objects.nonNull(param.getCreateUserId())) {
            HrmsUserInfoDO userInfo = Optional.ofNullable(userInfoDao.getById(param.getCreateUserId()))
                    .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc())));
            warehouseOcWhiteListDO.setCreateDate(new Date());
            warehouseOcWhiteListDO.setCreateUserCode(userInfo.getUserCode());
            warehouseOcWhiteListDO.setCreateUserName(userInfo.getUserName());
            warehouseOcWhiteListDO.setIsDelete(IsDeleteEnum.NO.getCode());
            warehouseOcWhiteListDO.setRecordVersion(BusinessConstant.LONG_ONE);
        } else {
            BaseDOUtil.fillDOInsert(warehouseOcWhiteListDO);
        }
        return warehouseOcWhiteListDao.save(warehouseOcWhiteListDO);
    }

    @Override
    public void deleteOcWhiteList(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        HrmsWarehouseOcWhiteListDO warehouseOcWhiteListDO = warehouseOcWhiteListDao.getById(id);
        if (Objects.isNull(warehouseOcWhiteListDO)) {
            return;
        }
        warehouseOcWhiteListDO.setIsDelete(IsDeleteEnum.YES.getCode());
        BaseDOUtil.fillDOUpdate(warehouseOcWhiteListDO);
        warehouseOcWhiteListDao.updateById(warehouseOcWhiteListDO);
    }
}
