package com.imile.hrms.service.organization.vo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.annotation.WithDictSeparator;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.organization.dto.EntDeptDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
@Data
public class DeptSnapshotDetailVO {
    /**
     * 部门id
     */
    private Long id;

    /**
     * 父级组织id
     */
    private Long parentId;

    private String parentName;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 负责人
     */
    private Long leaderCode;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 名称+编码
     */
    private String leaderNameAndCode;
    /**
     * 是否作业部门
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SYS_BOOLEAN, ref = "isOperationDeptDesc")
    private Integer isOperationDept;


    private String isOperationDeptDesc;

    /**
     * 状态
     */
    private String status;
    /**
     * 员工人数
     */
    private Integer employeeNum;
    private Long ocId;

    /**
     * 网点简称
     */
    private String deptShortName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门类型
     */
    private String type;

    /**
     * 部门级数
     */
    private Integer level;

    /**
     * 是否模板
     */
    private Integer isTemplate;

    private Integer isCostSettlement;

    private Integer isOrgManagement;

    /**
     * 部门属性
     */
    private String deptType;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;


    /**
     * 组织类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.DEPT_TYPE, ref = "deptOrgTypeDesc")
    private String deptOrgType;

    /**
     * 组织类型描述
     */
    private String deptOrgTypeDesc;

    /**
     * 区域
     */
    private String region;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份
     */
    private String province;

    /**
     * 国家
     */
    private String country;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 业务领域列表
     */
    private List<String> bizAreaList;

    /**
     * 业务领域描述列表
     */
    private List<String> bizAreaNameList;


    /**
     * 组织定位
     */
    private String deptPosition;

    /**
     * 组织职责
     */
    private String deptDuty;

    /**
     * 业务节点id列表
     */
    private List<String> bizModelIdList;

    /**
     * 业务节点描述列表
     */
    private List<String> bizModelNamesList;

    /**
     * 业务负责人信息
     */
    private List<EntDeptDTO.OrgBusinessConfigVO> orgBusinessConfigVOList;

    /**
     * 业务国家
     */
    private List<String> bizCountryList;

    /**
     * 生效时间
     */
    private Date activeTime;

    /**
     * 停用时间
     */
    private Date disabledTime;

    /**
     * 网点类型
     */
    @WithDictSeparator(typeCode = BusinessConstant.SysDictDataTypeConstant.OC_TYPE, tSeparator = "|", ref = "ocTypeNames")
    private String ocType;

    /**
     * 网点类型名称
     */
    private String ocTypeNames;
}
