package com.imile.hrms.service.excel;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.accounting.center.dto.ProjectPageResponseDto;
import com.imile.common.enums.ApprovalEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.excel.ExcelImport;
import com.imile.genesis.api.enums.CommonStatusEnum;
import com.imile.hermes.business.dto.BusZoneListDTO;
import com.imile.hermes.business.enums.RegionLevelEnums;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hermes.enterprise.dto.EntOcQueryApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.DeptTypeEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.IEnum;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.organization.DeptStatusEnum;
import com.imile.hrms.common.enums.probation.ProbationStatusEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.dao.organization.enums.DeptOrgTypeEnum;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntGradeDO;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.probation.model.HrmsUserProbationDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.integration.hermes.service.ZoneService;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.organization.ProjectManage;
import com.imile.hrms.service.excel.field.UserImportFieldEnum;
import com.imile.hrms.service.user.context.UserImportGlobalContext;
import com.imile.hrms.service.user.context.UserImportTempContext;
import com.imile.hrms.service.user.helper.UserCheckHelper;
import com.imile.util.date.DateUtils;
import com.imile.waukeen.api.base.enums.VendorTypeApiEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@Slf4j
@Component
public abstract class UserBaseImportProcessor<T extends ExcelImport> extends AbstractImportProcessor<T,
        UserImportGlobalContext, UserImportTempContext> {

    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private ProjectManage projectManage;
    @Resource
    private EntOcService entOcService;
    @Resource
    private ZoneService zoneService;

    protected static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2]\\d|3[0-1])$");

    protected Map<String, List<HrmsEntDeptDO>> getOcName2DeptListMap(List<String> ocNameList) {
        if (ocNameList.isEmpty()) {
            return Collections.emptyMap();
        }
        EntOcQueryApiDTO ocQuery = new EntOcQueryApiDTO();
        ocQuery.setOcNames(ocNameList);
        List<EntOcApiDTO> ocList = entOcService.getOcList(BusinessConstant.DEFAULT_ORG_ID, ocQuery);
        List<Long> ocIdList = ocList.stream()
                .map(EntOcApiDTO::getId)
                .collect(Collectors.toList());
        List<HrmsEntDeptDO> ocDeptList = deptManage.getDeptListByOcIdList(ocIdList);
        Map<Long, HrmsEntDeptDO> ocId2DeptMap = ocDeptList.stream()
                .collect(Collectors.toMap(HrmsEntDeptDO::getOcId, Function.identity()));
        return ocList.stream()
                .filter(s -> ocId2DeptMap.containsKey(s.getId()))
                .collect(Collectors.groupingBy(EntOcApiDTO::getOcName, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream()
                                .map(item -> ocId2DeptMap.get(item.getId()))
                                .collect(Collectors.toList()))));
    }

    protected Map<String, Long> getZoneKey2IdMap(List<String> countryNameList, List<String> provinceNameList, List<String> cityNameList) {
        List<BusZoneListDTO> countryList
                = zoneService.getZoneListByNameAndLevel(countryNameList, RegionLevelEnums.COUNTRY.getCode());
        Map<String, Long> countryKey2IdMap = countryList.stream()
                .collect(Collectors.toMap(BusZoneListDTO::getZoneName, BusZoneListDTO::getId));
        List<BusZoneListDTO> provinceList
                = zoneService.getZoneListByNameAndLevel(provinceNameList, RegionLevelEnums.PROVINCE.getCode());
        Map<String, Long> provinceKey2IdMap = provinceList.stream()
                .collect(Collectors.toMap(s ->
                        this.buildZoneKey(s.getParentId(), s.getZoneName()), BusZoneListDTO::getId));
        List<BusZoneListDTO> cityList
                = zoneService.getZoneListByNameAndLevel(cityNameList, RegionLevelEnums.CITY.getCode());
        Map<String, Long> cityKey2IdMap = cityList.stream()
                .collect(Collectors.toMap(s ->
                        this.buildZoneKey(s.getParentId(), s.getZoneName()), BusZoneListDTO::getId));
        Map<String, Long> zoneKey2IdMap = Maps.newHashMap();
        zoneKey2IdMap.putAll(countryKey2IdMap);
        zoneKey2IdMap.putAll(provinceKey2IdMap);
        zoneKey2IdMap.putAll(cityKey2IdMap);
        return zoneKey2IdMap;
    }

    protected void doOrganizationCheck(String deptCode, String ocName,
                                       UserImportGlobalContext globalContext,
                                       UserImportTempContext tempContext, List<String> errorTipList) {
        if (StringUtils.isBlank(deptCode) && StringUtils.isBlank(ocName)) {
            return;
        }
        if (StringUtils.isNotBlank(deptCode) && StringUtils.isNotBlank(ocName)) {
            // 校验部门
            this.doFieldDataExistCheck(UserImportFieldEnum.DEPT_ID, deptCode, deptCode,
                    globalContext.getDeptMap(), tempContext, errorTipList);
            // 校验网点
            this.doFieldDataExistCheck(UserImportFieldEnum.OC_NAME, ocName, ocName,
                    globalContext.getOcName2DeptListMap(), tempContext, errorTipList);
            // 部门及网点前置校验未通过则跳过匹配校验
            Long deptId = tempContext.getUser().getDeptId();
            Long ocDeptId = tempContext.getUser().getOcId();
            if (Objects.isNull(deptId) || Objects.isNull(ocDeptId)) {
                return;
            }
            // 校验部门和网点是否匹配（司机时所属部门及所属网点需一致 非司机时 所属部门为网点型部门时需与所属网点一致 非网点型部门时所属网点地理国需在所属部门业务覆盖国中）
            Integer isDriver = tempContext.getOriginUser().getIsDriver();
            boolean isDriverMatchFlag = WhetherEnum.YES.getKey().equals(isDriver)
                    && deptId.equals(ocDeptId);
            boolean notDriverDeptMatchFlag = WhetherEnum.NO.getKey().equals(isDriver)
                    && !DeptOrgTypeEnum.STATION.getValue().equals(tempContext.getDept().getDeptOrgType())
                    && Splitter.on(HrmsStringUtil.COMMA).splitToList(tempContext.getDept().getBizCountry()).contains(tempContext.getOcDept().getCountry());
            boolean notDriverOcDeptMatchFlag = WhetherEnum.NO.getKey().equals(isDriver)
                    && DeptOrgTypeEnum.STATION.getValue().equals(tempContext.getDept().getDeptOrgType())
                    && deptId.equals(ocDeptId);
            log.info("校验部门和网点是否匹配,isDriverMatchFlag:{},notDriverMatchFlag:{},notDriverOcDeptMatchFlag:{},isDriver:{},deptId:{},ocDeptId:{}",
                    isDriverMatchFlag, notDriverDeptMatchFlag, notDriverOcDeptMatchFlag, isDriver, deptId, ocDeptId);
            if (!isDriverMatchFlag && !notDriverDeptMatchFlag && !notDriverOcDeptMatchFlag) {
                errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.STATION_DEPARTMENT_NOT_MATCH,
                        ocName, deptCode));
            }
        } else {
            String fieldNames = String.join("、", Lists.newArrayList(UserImportFieldEnum.DEPT_ID.getDesc(),
                    UserImportFieldEnum.OC_NAME.getDesc()));
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_ASSOCIATED_REQUIRED, fieldNames));
        }
    }

    protected void doStationCheck(String ocName, UserImportGlobalContext globalContext,
                                  UserImportTempContext tempContext, List<String> errorTipList) {
        if (StringUtils.isBlank(ocName)) {
            return;
        }
        this.doFieldDataExistCheck(UserImportFieldEnum.OC_NAME, ocName, ocName,
                globalContext.getOcName2DeptListMap(), tempContext, errorTipList);
        // 网点前置校验未通过则跳过匹配校验
        if (Objects.isNull(tempContext.getOcDept())) {
            return;
        }
        // 改之后网点地理国与改之前须一致
        if (!tempContext.getOcDept().getCountry().equals(tempContext.getOriginOcDept().getCountry())) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.STATION_SELECTION_RANGE_EXCEED, ocName));
            return;
        }
        // 劳务派遣用工类型只能选直营网点
        if (EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(tempContext.getOriginUser().getEmployeeType())
                && tempContext.getOcDept().getType().equals(DeptTypeEnum.JOIN_STATION.getCode())) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.STATION_SELECTION_RANGE_EXCEED, ocName));
            return;
        }
        // 外包司机所属部门=所属网点
        tempContext.getUser().setDeptId(tempContext.getOcDept().getId());
        // 用于后续校验汇报上级
        tempContext.setDept(tempContext.getOcDept());
    }

    protected void doLocationCheck(String locationCountry, String locationProvince, String locationCity,
                                   UserImportGlobalContext globalContext,
                                   UserImportTempContext tempContext, List<String> errorTipList) {
        if (StringUtils.isBlank(locationCountry) && StringUtils.isBlank(locationProvince) && StringUtils.isBlank(locationCity)) {
            return;
        }
        if (StringUtils.isNotBlank(locationCountry) && StringUtils.isNotBlank(locationProvince)
                && StringUtils.isNotBlank(locationCity)) {
            String provinceKey = this.buildZoneKey(globalContext.getZoneKey2IdMap().get(locationCountry), locationProvince);
            String cityKey = this.buildZoneKey(globalContext.getZoneKey2IdMap().get(provinceKey), locationCity);
            this.doFieldDataExistCheck(UserImportFieldEnum.LOCATION_COUNTRY, locationCountry,
                    locationCountry, globalContext.getZoneKey2IdMap(), tempContext, errorTipList);
            this.doFieldDataExistCheck(UserImportFieldEnum.LOCATION_PROVINCE, locationProvince,
                    provinceKey, globalContext.getZoneKey2IdMap(), tempContext, errorTipList);
            this.doFieldDataExistCheck(UserImportFieldEnum.LOCATION_CITY, locationCity,
                    cityKey, globalContext.getZoneKey2IdMap(), tempContext, errorTipList);
        } else {
            String fieldNames = String.join("、", Lists.newArrayList(UserImportFieldEnum.LOCATION_COUNTRY.getDesc(),
                    UserImportFieldEnum.LOCATION_PROVINCE.getDesc(),
                    UserImportFieldEnum.LOCATION_CITY.getDesc()));
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_ASSOCIATED_REQUIRED, fieldNames));
        }
    }

    protected String buildZoneKey(Long parentId, String zoneName) {
        return parentId + "_" + zoneName;
    }

    protected void doFieldLengthCheck(UserImportFieldEnum field, String fieldValue,
                                      int maxLength, UserImportTempContext tempContext, List<String> errorTipList) {
        if (StringUtils.isBlank(fieldValue)) {
            return;
        }
        if (StringUtils.isNotBlank(fieldValue) && fieldValue.length() > maxLength) {
            errorTipList.add(this.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_LENGTH_LIMIT,
                    field.getDesc(tempContext.getImportFields()), maxLength));
            return;
        }
        switch (field) {
            case USER_NAME:
                tempContext.getUser().setUserName(fieldValue);
                break;
            case USER_NAME_EN:
                tempContext.getUser().setUserNameEn(fieldValue);
                break;
            case WAGES_CARD_BANK:
                tempContext.getUserWagesCard().setWagesCardBank(fieldValue);
                break;
            case WAGES_CARD_BANK_BRANCH:
                tempContext.getUserWagesCard().setWagesCardBankBranch(fieldValue);
                break;
            case WAGES_CARD_NO:
                tempContext.getUserWagesCard().setWagesCardNo(fieldValue);
                break;
            case CARDHOLDER_NAME:
                tempContext.getUserWagesCard().setCardholderName(fieldValue);
                break;
            case SWIFT_CODE:
                tempContext.getUserWagesCard().setSwiftCode(fieldValue);
                break;
            default:
        }
    }

    protected void doFieldPatternCheck(UserImportFieldEnum field, String fieldValue,
                                       Pattern pattern, String patternDesc,
                                       UserImportGlobalContext globalContext,
                                       UserImportTempContext tempContext,
                                       List<String> errorTipList) {
        if (StringUtils.isBlank(fieldValue)) {
            return;
        }
        String fieldDesc = field.getDesc(tempContext.getImportFields());
        if (StringUtils.isNotBlank(fieldValue) && !pattern.matcher(fieldValue).matches()) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_PATTERN_ERROR, fieldDesc, patternDesc));
            return;
        }
        switch (field) {
            case CONFIRM_ENTRY_DATE:
                HrmsUserEntryRecordDO originUserEntryRecord
                        = globalContext.getOriginUserEntryRecordMap().get(tempContext.getUser().getId());
                // 无入职记录 则忽略
                if (Objects.isNull(originUserEntryRecord)) {
                    return;
                }
                HrmsUserEntryRecordDO userEntryRecord = new HrmsUserEntryRecordDO();
                userEntryRecord.setId(originUserEntryRecord.getId());
                userEntryRecord.setConfirmDate(DateUtils.str2Date(fieldValue));
                tempContext.setUserEntryRecord(userEntryRecord);
                break;
            case PROBATION_MONTH:
                HrmsUserProbationDO originUserProbation
                        = globalContext.getOriginUserProbationMap().get(tempContext.getUser().getId());
                // 无试用期记录则忽略
                if (Objects.isNull(originUserProbation)) {
                    return;
                }
                // 试用期状态为待反馈结果之后的状态 则报错
                if (originUserProbation.getProbationStatus() > ProbationStatusEnum.WAIT_FEEDBACK_RESULT.getStatus()) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_UNMODIFIABLE, fieldDesc));
                    return;
                }
                HrmsUserProbationDO userProbation = new HrmsUserProbationDO();
                userProbation.setId(originUserProbation.getId());
                userProbation.setProbationMonths(Integer.parseInt(fieldValue));
                tempContext.setUserProbation(userProbation);
                break;
            case JOB_LEVEL:
                tempContext.getUser().setGradeNo(BusinessFieldUtils.encryption(fieldValue, tempContext.getUser().getId().toString()));
                break;
            case JOB_GRADE:
                tempContext.getUser().setJobGrade(BusinessFieldUtils.encryption(fieldValue, tempContext.getUser().getId().toString()));
                break;
            case DISPATCH_START_DATE:
                tempContext.getExtraFieldMap().put(field.getKey(), fieldValue);
                break;
            default:
        }
    }

    protected void doFieldEnumCheck(UserImportFieldEnum field, String fieldValue,
                                    IEnum[] enums, UserImportTempContext tempContext, List<String> errorTipList) {
        if (StringUtils.isBlank(fieldValue)) {
            return;
        }
        // 组装描述与键值映射 支持中文、英文及中英文组合
        Map<String, IEnum> fieldDescEnumMap = Maps.newHashMap();
        Arrays.stream(enums).forEach(s -> {
            fieldDescEnumMap.put(s.getDescCn(), s);
            fieldDescEnumMap.put(s.getDescEn(), s);
            fieldDescEnumMap.put(s.getDescCn() + "/" + s.getDescEn(), s);
        });
        IEnum fieldEnum = fieldDescEnumMap.get(fieldValue);
        String fieldDesc = field.getDesc(tempContext.getImportFields());
        String patternDesc = Arrays.stream(enums)
                .map(s -> RequestInfoHolder.isChinese() ? s.getDescCn() : s.getDescEn())
                .collect(Collectors.joining("/"));
        if (StringUtils.isNotBlank(fieldValue) && Objects.isNull(fieldEnum)) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_PATTERN_ERROR, fieldDesc, patternDesc));
            return;
        }
        switch (field) {
            case IS_GLOBAL_RELOCATION:
                tempContext.getUser().setIsGlobalRelocation((Integer) fieldEnum.getKey());
                break;
            case VISA_TYPE:
                tempContext.getExtraFieldMap().put(field.getKey(), fieldEnum.getKey());
                tempContext.getExtraFieldMap().put(field.getKey() + BusinessConstant.CN, fieldEnum.getDescCn());
                tempContext.getExtraFieldMap().put(field.getKey() + BusinessConstant.EN, fieldEnum.getDescEn());
                break;
            default:
        }
    }

    @SuppressWarnings("unchecked")
    protected void doFieldDataExistCheck(UserImportFieldEnum field, String fieldValue, String key,
                                         Map<String, ?> dataMap,
                                         UserImportTempContext tempContext, List<String> errorTipList) {
        if (StringUtils.isBlank(fieldValue)) {
            return;
        }
        String fieldDesc = field.getDesc(tempContext.getImportFields());
        // 由于部分场景key是基于fieldValue加工后得到的 如常驻地 因此传参中需要传key及fieldValue
        if (StringUtils.isNotBlank(fieldValue) && !dataMap.containsKey(key)) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_NOT_EXIST, fieldDesc, fieldValue));
            return;
        }
        switch (field) {
            case NATIONALITY:
                tempContext.getUser().setCountryCode((String) dataMap.get(fieldValue));
                break;
            case DEPT_ID:
                HrmsEntDeptDO dept = (HrmsEntDeptDO) dataMap.get(fieldValue);
                if (!DeptStatusEnum.ACTIVE.getCode().equals(dept.getStatus())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_UNAVAILABLE,
                            fieldDesc, fieldValue));
                    return;
                }
                if (WhetherEnum.YES.getKey().equals(tempContext.getOriginUser().getIsDriver())
                        && !DeptOrgTypeEnum.STATION.getValue().equals(dept.getDeptOrgType())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.DRIVER_DEPARTMENT_NOT_MATCH));
                    return;
                }
                tempContext.getUser().setDeptId(dept.getId());
                tempContext.setDept(dept);
                break;
            case OC_NAME:
                List<HrmsEntDeptDO> ocDeptList = (List<HrmsEntDeptDO>) dataMap.get(fieldValue);
                Predicate<HrmsEntDeptDO> ocDeptPredicate
                        = CommonUtils.customEqualFilter(HrmsEntDeptDO::getStatus, StatusEnum.ACTIVE.getCode());
                HrmsEntDeptDO ocDept = super.getMappingData(ocDeptList, ocDeptPredicate, fieldDesc, fieldValue, errorTipList);
                if (Objects.isNull(ocDept)) {
                    return;
                }
                if (WhetherEnum.YES.getKey().equals(tempContext.getOriginUser().getIsDriver())
                        && DeptTypeEnum.CENTER_STATION.getCode().equals(ocDept.getType())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.DRIVER_STATION_ILLEGAL));
                    return;
                }
                tempContext.getUser().setOcId(ocDept.getId());
                tempContext.getUser().setOcCode(ocDept.getOcCode());
                tempContext.getUser().setOriginCountry(ocDept.getCountry());
                tempContext.setOcDept(ocDept);
                break;
            case BIZ_MODEL_NAME:
                List<HrmsBizModelDO> bizModelList = (List<HrmsBizModelDO>) dataMap.get(fieldValue);
                Predicate<HrmsBizModelDO> bizModelPredicate
                        = CommonUtils.customEqualFilter(HrmsBizModelDO::getStatus, StatusEnum.ACTIVE.getCode());
                HrmsBizModelDO bizModel = super.getMappingData(bizModelList, bizModelPredicate, fieldDesc, fieldValue, errorTipList);
                if (Objects.isNull(bizModel)) {
                    return;
                }
                tempContext.getUser().setBizModelId(bizModel.getId());
                tempContext.setBizModel(bizModel);
                break;
            case PROJECT_CODE:
                ProjectPageResponseDto project = (ProjectPageResponseDto) dataMap.get(fieldValue);
                if (!BusinessConstant.ZERO.toString().equals(project.getProjectStatus())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_UNAVAILABLE,
                            fieldDesc, fieldValue));
                    return;
                }
                tempContext.getUser().setProjectCode(project.getProjectCode());
                tempContext.getUser().setProjectId(projectManage.convertProjectCodeToId(project.getProjectCode()));
                tempContext.setProject(project);
                break;
            case POST_ID:
                PostDO post = (PostDO) dataMap.get(fieldValue);
                if (!CommonStatusEnum.ACTIVE.getCode().equals(post.getStatus())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_UNAVAILABLE,
                            fieldDesc, fieldValue));
                    return;
                }
                tempContext.getUser().setPostId(post.getId());
                break;
            case LEADER_CODE:
                if (fieldValue.equals(tempContext.getOriginUser().getUserCode())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.PARENT_ACCOUNT_CAN_NOT_BE_ITSELF,
                            fieldDesc, fieldValue));
                    return;
                }
                HrmsUserInfoDO leader = (HrmsUserInfoDO) dataMap.get(fieldValue);
                String country = Objects.isNull(tempContext.getOcDept())
                        ? tempContext.getOriginUser().getOriginCountry()
                        : tempContext.getOcDept().getCountry();
                String ocCode = Objects.isNull(tempContext.getOcDept())
                        ? tempContext.getOriginUser().getOcCode()
                        : tempContext.getOcDept().getOcCode();
                boolean isMatch = UserCheckHelper.isLeaderMatch(leader, tempContext.getOriginUser().getIsDriver(),
                        tempContext.getOriginUser().getEmployeeType(), country, ocCode);
                if (!isMatch) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_UNAVAILABLE,
                            fieldDesc, fieldValue));
                    return;
                }
                tempContext.getUser().setLeaderId(leader.getId());
                tempContext.getUser().setLeaderName(leader.getUserName());
                tempContext.setLeader(leader);
                break;
            case VENDOR_CODE:
                VendorInfoApiDTO vendor = (VendorInfoApiDTO) dataMap.get(fieldValue);
                if (StatusEnum.DISABLED.getCode().equals(vendor.getStatus())
                        || !ApprovalEnum.APPROVAL_SUCCESS.getCode().equals(vendor.getApprovalStatus())) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_UNAVAILABLE,
                            fieldDesc, fieldValue));
                    return;
                }
                // 所属部门为网点型部门时需判断网点与供应商是否匹配
                if (!this.isVendorStationMatch(fieldValue, tempContext, errorTipList, vendor)) {
                    return;
                }
                tempContext.getUser().setVendorCode(vendor.getVendorCode());
                tempContext.getUser().setVendorId(vendor.getVendorId());
                tempContext.getUser().setVendorName(vendor.getVendorShortName());
                tempContext.getUser().setVendorOrgId(vendor.getOrgId());
                break;
            case JOB_SEQUENCE:
                List<HrmsEntGradeDO> gradeList = (List<HrmsEntGradeDO>) dataMap.get(fieldValue);
                Predicate<HrmsEntGradeDO> gradePredicate
                        = CommonUtils.customEqualFilter(HrmsEntGradeDO::getStatus, StatusEnum.ACTIVE.getCode());
                HrmsEntGradeDO grade = super.getMappingData(gradeList, gradePredicate, fieldDesc, fieldValue, errorTipList);
                if (Objects.isNull(grade)) {
                    return;
                }
                tempContext.getUser().setGradeId(grade.getId());
                tempContext.setGrade(grade);
                break;
            case LOCATION_COUNTRY:
                // 账号为司机时 判断本次提交或数据库中的网点所在国是否为BRA 是的话常驻地国家也需要限制只允许填写BRA
                boolean isStationAtBra = (Objects.nonNull(tempContext.getOcDept())
                        && CountryCodeEnum.BRA.getCode().equals(tempContext.getOcDept().getCountry()))
                        || (Objects.isNull(tempContext.getOcDept()) && Objects.nonNull(tempContext.getOriginOcDept())
                        && CountryCodeEnum.BRA.getCode().equals(tempContext.getOriginOcDept().getCountry()));
                if (WhetherEnum.YES.getKey().equals(tempContext.getOriginUser().getIsDriver())
                        && isStationAtBra && !CountryCodeEnum.BRA.getCode().equals(fieldValue)) {
                    errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.STATION_LOCATION_NOT_MATCH,
                            CountryCodeEnum.BRA.getCode(), CountryCodeEnum.BRA.getCode()));
                    return;
                }
                tempContext.getUser().setLocationCountry(fieldValue);
                break;
            case LOCATION_PROVINCE:
                tempContext.getUser().setLocationProvince(fieldValue);
                break;
            case LOCATION_CITY:
                tempContext.getUser().setLocationCity(fieldValue);
                break;
            case PAYMENT_COUNTRY:
                tempContext.getUserWagesCard().setPaymentCountry(fieldValue);
                break;
            default:
        }
    }

    private boolean isVendorStationMatch(String fieldValue, UserImportTempContext tempContext, List<String> errorTipList,
                                         VendorInfoApiDTO vendor) {
        HrmsEntDeptDO tempDept = Objects.nonNull(tempContext.getDept())
                ? tempContext.getDept()
                : tempContext.getOriginDept();
        if (Objects.isNull(tempDept)) {
            return false;
        }
        if (!DeptOrgTypeEnum.STATION.getValue().equals(tempDept.getDeptOrgType())) {
            return true;
        }
        // 供应商所属中心网点需包含网点所属中心网点
        List<String> ocCenterCodeList = Splitter.on(HrmsStringUtil.COMMA).splitToList(vendor.getOcCenterCode());
        if (!ocCenterCodeList.contains(tempDept.getOcCenterCode())) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.STATION_VENDOR_NOT_MATCH,
                    fieldValue, tempDept.getDeptNameEn()));
            return false;
        }
        // 直营网点只能选非加盟供应商 加盟网点只能选加盟供应商
        List<String> vendorTypeList = Splitter.on(HrmsStringUtil.COMMA).splitToList(vendor.getVendorType());
        boolean isDirectOcVendorError = DeptTypeEnum.STATION.getCode().equals(tempDept.getType())
                && vendorTypeList.contains(VendorTypeApiEnum.HUB.getCode());
        boolean isJoinOcVendorError = DeptTypeEnum.JOIN_STATION.getCode().equals(tempDept.getType())
                && !vendorTypeList.contains(VendorTypeApiEnum.HUB.getCode());
        if (isDirectOcVendorError || isJoinOcVendorError) {
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.STATION_VENDOR_NOT_MATCH,
                    fieldValue, tempDept.getDeptNameEn()));
            return false;
        }
        return true;
    }
}
