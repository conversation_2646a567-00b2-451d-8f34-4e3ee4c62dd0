package com.imile.hrms.service.business.area.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class HrmsBizeAreaBathEditBizeDeptParam {
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private List<HrmsBizeAreaEditBizeDeptListParam> list;

    /**
     * 停用的业务领域id
     */
    private Long id;
}
