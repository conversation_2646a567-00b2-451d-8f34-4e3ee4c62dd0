package com.imile.hrms.service.approval.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/13 17:01
 * @version: 1.0
 */
@Data
public class LeaveFormInfoExportVO implements Serializable {
    private String applyUserName;
    private String applyUserCode;
    private String applyUserWorkNo;
    private String userName;
    private String userCode;
    private String workNo;
    private String country;
    private String postName;
    private String deptName;
    private String applicationCode;
    private String leaveName;
    private String leaveHours;
    private String startDate;
    private String endDate;
    private String formStatus;
    private Date createDate;
}