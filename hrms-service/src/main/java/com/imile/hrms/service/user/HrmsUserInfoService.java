package com.imile.hrms.service.user;


import com.imile.common.page.PaginationResult;
import com.imile.hermes.enterprise.dto.EntEmpDriverApiDTO;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hrms.api.salary.query.UserApiQuery;
import com.imile.hrms.api.user.dto.UserPmsPageDTO;
import com.imile.hrms.api.user.param.PmsPageParam;
import com.imile.hrms.dao.user.dto.ApprovalUserInfoDTO;
import com.imile.hrms.dao.user.dto.CrmUserInfoDTO;
import com.imile.hrms.dao.user.dto.CurrentUserDTO;
import com.imile.hrms.dao.user.dto.DtlSelectListDTO;
import com.imile.hrms.dao.user.dto.OutSourceDriverRegisterParam;
import com.imile.hrms.dao.user.dto.SelectUserDTO;
import com.imile.hrms.dao.user.dto.UpdateUserParam;
import com.imile.hrms.dao.user.dto.UserCompanyParam;
import com.imile.hrms.dao.user.dto.UserDTO;
import com.imile.hrms.dao.user.dto.UserInfoByDeptIdDTO;
import com.imile.hrms.dao.user.dto.UserInfoExpandDTO;
import com.imile.hrms.dao.user.dto.UserInfoExpandNoLoginParamDTO;
import com.imile.hrms.dao.user.dto.UserInfoUpdatePhotoParamDTO;
import com.imile.hrms.dao.user.dto.UserListInfoDTO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.CheckUserNameQuery;
import com.imile.hrms.dao.user.query.SelectUserPageQuery;
import com.imile.hrms.dao.user.query.SelectUserQuery;
import com.imile.hrms.dao.user.query.UserQuery;
import com.imile.hrms.dao.user.query.UserSyncQuery;
import com.imile.hrms.integration.hermes.vo.HrUserInfoDTO;
import com.imile.hrms.service.organization.result.HrmsCountryVO;
import com.imile.hrms.service.user.dto.ApprovalUserInfoQueryDTO;
import com.imile.hrms.service.user.dto.CheckUserCertificateDTO;
import com.imile.hrms.service.user.dto.CheckUserInfoEffectiveDTO;
import com.imile.hrms.service.user.dto.CrmUserInfoQueryDTO;
import com.imile.hrms.service.user.dto.HrmsUserInfoByOaDTO;
import com.imile.hrms.service.user.dto.JobLevelDecryption;
import com.imile.hrms.service.user.dto.JobLevelSecret;
import com.imile.hrms.service.user.dto.UserBaseInfoDTO;
import com.imile.hrms.service.user.dto.UserCorrectImportDTO;
import com.imile.hrms.service.user.dto.UserDetailByUserCodeDTO;
import com.imile.hrms.service.user.dto.UserInfoQueryDTO;
import com.imile.hrms.service.user.dto.UserInfoResultDTO;
import com.imile.hrms.service.user.param.UserAssociateQueryParam;
import com.imile.permission.api.dto.BusinessBasicDataApiDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
public interface HrmsUserInfoService {

    /**
     * 员工基础信息订正(不订正姓名，打印出来，不订正状态)
     */
    List<UserCorrectImportDTO> userCorrectImport(List<UserCorrectImportDTO> importDTOList);


    /**
     * 查找未同步到TMS用户列表
     *
     * @return
     */
    List<HrmsUserInfoDO> queryUnSyncUserInfoList(UserSyncQuery userSyncQuery);


    /**
     * 用户信息
     *
     * @param userId
     * @return
     * @throws Exception
     */
    UserInfoExpandDTO userInfo(Long userId) throws Exception;

    /**
     * 用户信息h5
     *
     * @param userId
     * @return
     * @throws Exception
     */
    UserInfoExpandDTO userInfoNoLogin(Long userId) throws Exception;

    /**
     * 员工信息分页查询
     *
     * @param handlerUserQuery
     * @return
     */
    PaginationResult<UserListInfoDTO> userList(UserQuery handlerUserQuery);


    /**
     * 员工信息分页查询
     *
     * @param handlerUserQuery
     * @return
     */
    PaginationResult<UserListInfoDTO> userExportList(UserQuery handlerUserQuery);

//    /**
//     * 员工下拉列表
//     *
//     * @return
//     */
//    List<HrmsUserInfoDO> selectList(String status, boolean isNeedSeparate);


    /**
     * 分页下拉接口
     *
     * @param userQuery
     * @return
     */
    List<SelectUserDTO> selectUserList(SelectUserQuery userQuery);

    List<SelectUserDTO> selectListNoAuth(SelectUserQuery userQuery);

    /**
     * h5页面保存方法
     *
     * @param userInfoExpandDTO
     */
    void addUserInfoOpen(UserInfoExpandNoLoginParamDTO userInfoExpandDTO);


    /**
     * 校验英文名
     *
     * @param query
     * @return
     */
    Boolean checkUserNameEn(CheckUserNameQuery query);


    /**
     * 从TMS同步用户到HR系统
     *
     * @param userInfoDTO
     */
    void syncUserInfo(HrUserInfoDTO userInfoDTO);

    /**
     * 获取当前用户
     *
     * @return
     */
    CurrentUserDTO getCurrentUser();

    /**
     * 获取下级user
     *
     * @param userCode
     * @return
     */
    List<UserDTO> listSubUsers(String userCode);

    /**
     * 获取当前userCode具体信息
     *
     * @param userCode
     * @return
     */
    UserDTO getCrmUserByUserCode(String userCode);

    /**
     * CRM获取员工信息(模糊查询)
     *
     * @return
     */
    PaginationResult<CrmUserInfoDTO> selectCrmUserInfo(CrmUserInfoQueryDTO queryDTO);

    /**
     * 查询crm要的员工信息
     *
     * @return
     */
    List<BusinessBasicDataApiDTO> selectCrmUserToPermission();

    /**
     * 审批流获取HR员工信息
     */
    List<ApprovalUserInfoDTO> selectApprovalUserInfo(ApprovalUserInfoQueryDTO queryDTO);

    /**
     * 根据userCodeList获取信息
     *
     * @param userCode
     * @return
     */
    List<UserDTO> listUserInfoByUserCode(List<String> userCode);

    /**
     * 根据userCodeList获取信息
     *
     * @param userCode
     * @return
     */
    List<UserDTO> attendanceListUserInfoByUserCode(List<String> userCode);

    /**
     * 根据userCodeList获取信息
     *
     * @param userApiQuery
     * @return
     */
    List<UserDTO> listUserInfo(UserApiQuery userApiQuery);

    /**
     * 员工信息查询
     *
     * @param userId
     * @return
     */
    HrmsUserInfoDO getByUserId(Long userId);

    /**
     * 判断当前登录人是否一键关联用户(把tms账号直接关联进来)
     *
     * @param userId
     * @return
     */
    boolean checkStaffCorrelationUserCode(Long userId);

    /**
     * 判断当前登录人能否进行用户基础信息订正导入
     *
     * @param userId
     * @return
     */
    boolean checkUserInfoCorrectUserCode(Long userId);

    /**
     * 判断当前登录人能否进行司机TMS关联到HR
     *
     * @param userId
     * @return
     */
    boolean checkAuthSyncDriver(Long userId);

    /**
     * 更新员工头像
     *
     * @param updateUserParam
     */
    void updatePhoto(UpdateUserParam updateUserParam);

    /**
     * 查询员工信息，包括证件信息
     */
    List<UserInfoResultDTO> listUserInfo(UserInfoQueryDTO queryDTO);

    /**
     * 同步账号基本信息，两边建立关联
     *
     * @param userParam
     */
    void syncUserCode(UserCompanyParam userParam);

    void syncDriverCode(UserCompanyParam userParam);


    /**
     * 新增外包司机
     *
     * @param driverRegisterParam
     */
    HrmsUserInfoDO addOutSourceDriver(OutSourceDriverRegisterParam driverRegisterParam);


    /**
     * 编辑外包 非干线司机
     *
     * @param param
     */
    void editOutSourceDriver(OutSourceDriverRegisterParam param);


    List<String> queryDriverType(String employeeType, String country);

    /**
     * 根据证件号查询用户信息
     *
     * @param certificateCode
     * @return
     */
    CheckUserCertificateDTO selectUserByCertificateCode(String certificateCode);

    /**
     * 导出用户信息
     *
     * @param query
     * @return
     */
    PaginationResult<UserListInfoDTO> export(UserQuery query);

    /**
     * 编辑用户头像
     *
     * @param updatePhotoParamDTO
     * @return
     */
    void updatePhoto(UserInfoUpdatePhotoParamDTO updatePhotoParamDTO);

    /**
     * 内部调用，外部慎重调用
     *
     * @param driver
     * @param employee
     * @param userParam
     */
    void entry(EntEmpDriverApiDTO driver, EntEmployeeApiDTO employee, UserCompanyParam userParam);

    CheckUserInfoEffectiveDTO checkEffective(Long userId);

    /**
     * 获取所有用户
     */
    List<HrmsUserInfoByOaDTO> getAllUser();

    /**
     * 获取用户基础信息
     *
     * @param userCode
     * @return
     */
    UserBaseInfoDTO userBaseInfo(String userCode);

    /**
     * 获取用户基础信息
     *
     * @param userCode
     * @return
     */
    UserDetailByUserCodeDTO userDetailByUserCode(String userCode);

    PaginationResult<SelectUserDTO> selectUserList(SelectUserPageQuery userQuery);

    /**
     * 根据供应商 获取 del 下拉列表
     *
     * @param vendorId
     * @return
     */
    DtlSelectListDTO selectDtlList(Long vendorId, Long ocId);


    /**
     * 根据ocId 获取 本级 + 上级   下拉列表
     *
     * @param ocId
     * @deprecated replaced by HrmsDriverService#getDriverLeaderList(DriverLeaderQueryParam)
     * @return
     */
    @Deprecated
    List<UserDTO> selectDtlListByOcId(Long ocId);


    /**
     * 根据userIdList获取信息
     *
     * @param userIds
     * @return
     */
    List<UserDTO> listUserInfoByUserIds(List<Long> userIds);

    /**
     * 校验用户名是否全部是中文
     */
    Boolean checkName(String userName);

    /**
     * 职级加密
     *
     * @deprecated replaced by {@link com.imile.hrms.common.util.BusinessFieldUtils#encryption}
     */
    @Deprecated
    JobLevelSecret encryption(Long gradeId, String gradeNo, String jobGrade, Long id);

    /**
     * 职级解密
     *
     * @deprecated replaced by {@link com.imile.hrms.common.util.BusinessFieldUtils#decryption}
     */
    @Deprecated
    JobLevelDecryption decryption(Long gradeId, String gradeNo, String jobGrade, Long id);

    /**
     * 根据部门id查员工
     */
    List<UserInfoByDeptIdDTO> listUserInfoByDeptId(Long deptId);

    /**
     * 根据部门id查员工
     */
    List<UserInfoByDeptIdDTO> listUserInfoByDeptIds(List<Long> deptIds);

    /**
     * 根据id查询，查不到会报业务异常
     *
     * @param userId userId
     * @return HrmsUserInfoDO
     */
    HrmsUserInfoDO getHrmsUserInfoDO(Long userId);

    /**
     * 得到司机列表
     *
     * @return List<SelectUserDTO>
     */
    List<SelectUserDTO> getDriverList();

    /**
     * 获取用户联想列表（最多返回满足条件的前50条）
     *
     * @param param UserAssociateQueryParam
     * @return List<SelectUserDTO>
     */
    @Deprecated
    List<SelectUserDTO> getUserAssociateList(UserAssociateQueryParam param);

    PaginationResult<UserPmsPageDTO> getPaginationResult(PmsPageParam pmsPageParam);

    /**
     * 根据员工ID列表获取部门ID映射
     *
     * @param userIdList 员工ID列表
     * @return Map
     */
    Map<Long, Long> getUserDeptIdMap(List<Long> userIdList);

    /**
     * 根据员工ID列表获取员工名称映射
     *
     * @param userIdList 员工ID列表
     * @return Map
     */
    Map<Long, String> getUserNameMap(List<Long> userIdList);

    /**
     * 根据员工ID列表获取员工映射
     *
     * @param userIdList 员工ID列表
     * @return Map
     */
    Map<Long, HrmsUserInfoDO> getUserMap(List<Long> userIdList);

    /**
     * 司机管理NEW新增众包司机
     *
     * @param driverRegisterParam
     */
    void addFreelancer(OutSourceDriverRegisterParam driverRegisterParam);

    /**
     * 司机管理NEW编辑众包司机
     *
     * @param driverRegisterParam
     */
    void editFreelancer(OutSourceDriverRegisterParam driverRegisterParam);

    /**
     * 司机管理NEW编辑自主注册众包司机
     *
     * @param driverRegisterParam
     */
    void editRegistrationFreelancer(OutSourceDriverRegisterParam driverRegisterParam);


    HrmsUserInfoDO getByUserCode(String userCode);

    /**
     * 获取用户有权限的业务覆盖国列表
     *
     * @param id 用户ID
     * @return List<HrmsCountryVO>
     */
    List<HrmsCountryVO> getUserBizCountryList(Long id);


    PaginationResult<UserListInfoDTO> selectUserInfoPage(UserQuery query);

    /**
     * 获取全球业务覆盖国列表
     *
     * @return List<HrmsCountryVO>
     */
    List<HrmsCountryVO> getAllBizCountryList();

    void updateAttendanceAndPunchConfigHandler(HrmsUserInfoDO userInfoDO, HrmsUserInfoDO oldRecord);
}
