package com.imile.hrms.service.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
@Data
public class UserEntryWorkInfoSaveParam {

    /**
     * 是否司机（0:否 1:是）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer isDriver;

    /**
     * 是否是DTL（0:否 1:是）
     */
    private Integer isDtl;

    /**
     * 所属部门ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long deptId;

    /**
     * 所属网点编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String ocCode;

    /**
     * 用工类型
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String employeeType;

    /**
     * 所属供应商编码
     */
    private String vendorCode;

    /**
     * 岗位ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long postId;

    /**
     * 汇报上级ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long leaderId;

    /**
     * 职级序列ID
     */
    private Long gradeId;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 职等
     */
    private String jobGrade;

    /**
     * 业务节点ID
     */
    private Long bizModelId;

    /**
     * 项目编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String projectCode;

    /**
     * 常驻地国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCountry;

    /**
     * 常驻地省份
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationProvince;

    /**
     * 常驻地城市
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCity;
}
