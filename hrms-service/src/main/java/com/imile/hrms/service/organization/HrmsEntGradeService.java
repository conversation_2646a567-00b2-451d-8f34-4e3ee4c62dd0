package com.imile.hrms.service.organization;


import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.common.StatusSwitchParamDTO;
import com.imile.hrms.dao.organization.dto.EntGradeDTO;
import com.imile.hrms.dao.organization.dto.GradeDTO;
import com.imile.hrms.dao.organization.query.GradeQuery;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
public interface HrmsEntGradeService {
    /**
     * 岗位列表查询
     *
     * @param query
     * @return
     */
    PaginationResult<EntGradeDTO> gradeList(GradeQuery query);

    /**
     * 添加grade
     *
     * @param entGradeDTO
     * @return
     */
    Boolean addGrade(EntGradeDTO entGradeDTO);

    /**
     * 修改grade
     *
     * @param entGradeDTO
     * @return
     */
    Boolean updateGrade(EntGradeDTO entGradeDTO);

    /**
     * 状态切换
     *
     * @param paramDTO
     * @return
     */
    Boolean statusSwitch(StatusSwitchParamDTO paramDTO);

    /**
     * 下拉列表
     *
     * @return
     */
    List<GradeDTO> selectList();

    /**
     * 获取启用中职级列表
     *
     * @return List<GradeDTO>
     */
    List<com.imile.hrms.api.base.result.GradeDTO> getGradeActiveList();
}
