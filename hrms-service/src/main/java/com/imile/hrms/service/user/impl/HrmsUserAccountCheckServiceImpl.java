package com.imile.hrms.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.EmployeeNatureEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.OperationModuleEnum;
import com.imile.hrms.common.enums.account.AccountDisabledTypeEnum;
import com.imile.hrms.common.enums.account.AccountStatusEnum;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.dao.attendance.query.DeliveryCountSyncQuery;
import com.imile.hrms.dao.user.dao.HrmsUserEntryRecordDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.prism.DeliveryCountService;
import com.imile.hrms.integration.prism.dto.DeliveryAndPickUpCountDTO;
import com.imile.hrms.service.tx.HrmsTxManageFactory;
import com.imile.hrms.service.user.HrmsUserAccountCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-04-12
 */
@Slf4j
@Service
public class HrmsUserAccountCheckServiceImpl implements HrmsUserAccountCheckService {

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private DeliveryCountService deliveryCountService;
    @Autowired
    private HrmsUserEntryRecordDao entryRecordDao;
    @Autowired
    private HrmsProperties hrmsProperties;

    @Override
    public void disabledUserAccount(List<String> countryList, Integer isDriver, List<String> employeeTypes) {

        List<HrmsUserInfoDO> updateUserList = new ArrayList<>();
        for (String country : countryList) {
            // 查询所有的外包司机信息
            List<HrmsUserInfoDO> userInfoDOList = getExternalDriverInfoByCompanyId(country, isDriver, employeeTypes);
            List<Long> userIds = userInfoDOList.stream().map(o -> o.getId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                continue;
            }
            // 查询入职信息
            List<HrmsUserEntryRecordDO> entryRecordDOList = entryRecordDao.listByUserIds(userIds);
            // 过滤入职时间在巡检时间之前的
            List<HrmsUserEntryRecordDO> filterList = entryRecordDOList.stream().filter(o -> compareEntryDate(o.getEntryDate(), isDriver, employeeTypes)).collect(Collectors.toList());
            Map<Long, HrmsUserInfoDO> userIdMap = userInfoDOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
            List<String> userCodes = filterList.stream().map(o -> userIdMap.get(o.getUserId()).getUserCode()).collect(Collectors.toList());
            // 查询司机近X天内的派件信息
            DeliveryCountSyncQuery query = DeliveryCountSyncQuery.builder().driverCodes(userCodes).startTime(pushDate(isDriver, employeeTypes)).endTime(new Date()).timeZone(8).build();
            List<DeliveryAndPickUpCountDTO> countDTOList = deliveryCountService.statisticalDeliveryAndPickUpData(query);
            // 过滤除未派件的司机信息
            Map<String, DeliveryAndPickUpCountDTO> map = countDTOList.stream().collect(Collectors.toMap(o -> o.getDriverCode(), o -> o, (v1, v2) -> v1));
            Map<String, HrmsUserInfoDO> userMap = userInfoDOList.stream().collect(Collectors.toMap(o -> o.getUserCode(), o -> o, (v1, v2) -> v1));

            for (String userCode : userCodes) {
                DeliveryAndPickUpCountDTO countDTO = map.get(userCode);
                if (countDTO == null || (countDTO.getDeliveredCount() == 0 && countDTO.getDeliveryCount() == 0)) {
                    HrmsUserInfoDO userInfoDO = userMap.get(userCode);
                    userInfoDO.setStatus(AccountStatusEnum.DISABLED.name());
                    userInfoDO.setDisabledDate(new Date());
                    userInfoDO.setDisabledReason(AccountDisabledTypeEnum.NOT_DELIVERY.name());
                    updateUserList.add(userInfoDO);
                    continue;
                }
            }
            //4、冻结司机账号
            hrmsUserInfoDao.updateBatchById(updateUserList);

            // 事务处理
            updateUserList.forEach(item -> {
                HrmsTxManageFactory.getTxManage(OperationModuleEnum.USER_EDIT).handle(String.valueOf(item.getId()));
            });
            //5、发送消息通知hubLeaber、hr(邮件、企业微信)

        }

    }


    /**
     * 根据公司id获取外包司机信息
     *
     * @param country
     * @return
     */
    private List<HrmsUserInfoDO> getExternalDriverInfoByCompanyId(String country, Integer isDriver, List<String> employeeTypes) {
        LambdaQueryWrapper<HrmsUserInfoDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(HrmsUserInfoDO::getOriginCountry, country);
        wrapper.eq(HrmsUserInfoDO::getIsDriver, isDriver);
        wrapper.in(HrmsUserInfoDO::getEmployeeType, employeeTypes);
        wrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return hrmsUserInfoDao.list(wrapper);
    }


    private Date pushDate(Integer isDriver, List<String> employeeTypes) {
        Integer pushDay = 0;
        boolean isInternal = employeeTypes.stream()
                .allMatch(o -> EmployeeNatureEnum.INTERNAL.getCode().equalsIgnoreCase(EmploymentTypeEnum.getByCode(o).getNature()));
        if (isDriver.equals(BusinessConstant.Y) && isInternal) {
            pushDay = hrmsProperties.getCheckAccount().getInternalDriver();
        }else if (isDriver.equals(BusinessConstant.Y) && !isInternal) {
            pushDay = hrmsProperties.getCheckAccount().getExternalDriver();
        }

        return HrmsDateUtil.pushDate(new Date(), pushDay);
    }

    private boolean compareEntryDate(Date entryDate, Integer isDriver, List<String> employeeTypes) {
        Date date = pushDate(isDriver, employeeTypes);
        if (entryDate.compareTo(date) <= 0) {
            return true;
        }
        return false;
    }
}
