package com.imile.hrms.service.attendance.impl;

import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceAbnormalRemindRecordDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceAbnormalRemindRecordDO;
import com.imile.hrms.dao.attendance.param.AbnormalRemindRecordAddParam;
import com.imile.hrms.dao.attendance.query.AbnormalRemindRecordQuery;
import com.imile.hrms.service.attendance.HrmsAttendanceAbnormalRemindRecordService;
import com.imile.hrms.service.base.BaseService;
import com.imile.util.BeanUtils;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 异常发送提醒记录 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2024/10/22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class HrmsAttendanceAbnormalRemindRecordServiceImpl extends BaseService implements HrmsAttendanceAbnormalRemindRecordService {

    private final HrmsAttendanceAbnormalRemindRecordDao abnormalRemindRecordDao;

    @Override
    public List<HrmsAttendanceAbnormalRemindRecordDO> listOnly(AbnormalRemindRecordQuery query) {
        List<HrmsAttendanceAbnormalRemindRecordDO> abnormalRemindRecordList = abnormalRemindRecordDao.listOnly(query);
        return abnormalRemindRecordList;
    }

    @Override
    public void add(AbnormalRemindRecordAddParam param) {
        HrmsAttendanceAbnormalRemindRecordDO abnormalRemindRecordDO = BeanUtils.convert(param, HrmsAttendanceAbnormalRemindRecordDO.class);
        BaseDOUtil.fillDOInsert(abnormalRemindRecordDO);
        abnormalRemindRecordDao.save(abnormalRemindRecordDO);
    }
}
