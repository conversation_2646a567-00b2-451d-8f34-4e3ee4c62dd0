package com.imile.hrms.service.user.dto;

import com.imile.hrms.dao.user.dto.UserCertificateInfoParamDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HrmsApprovalUserInfoEditDTO implements Serializable {
    private static final long serialVersionUID = -4675640448931783095L;

    private Long id;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 所属国家名称
     */
    private String countryName;

    /**
     * 所属国家编码
     */
    private String countryCode;

    /**
     * 性别 性别(1:男,2:女)
     */
    private Integer sex;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 供应商企业id
     */
    private Long vendorOrgId;
    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 工作岗位
     */
    private Long postId;


    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 汇报上级编码
     */
    private Long leaderId;

    /**
     * 汇报上级名称
     */
    private String leaderName;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 员工头像地址
     */
    private String profilePhotoUrl;

    /**
     * 是否为司机 是否为司机
     */
    private Integer isDriver;

    /**
     * 是否与UCenter已同步 是否与UCenter已同步,即UCenter当中是否有该用户的信息
     */
    private Integer isSync;

    /**
     * 数据来源 入职添加、数据导入、其他系统同步
     */
    private String dataSource;

    /**
     * 是否司机leader
     */
    private Integer isDtl;

    /**
     * 系统账号名称
     */
    private String sysAccountName;

    /**
     * 是否虚拟员工 0:否  1:是
     */
    private Integer isVirtual;

    /**
     * 职能
     */
    private String functional;

    /**
     * 是否仓内作业员工
     */
    private Integer isWarehouseStaff;

    /**
     * 车辆类别
     */
    private String vehicleModel;

    /**
     * 员工证件信息
     */
    private List<UserCertificateInfoParamDTO> userCertificateInfos;
}
