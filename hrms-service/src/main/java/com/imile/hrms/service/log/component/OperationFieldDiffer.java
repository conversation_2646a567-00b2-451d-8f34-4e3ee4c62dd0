package com.imile.hrms.service.log.component;

import com.alibaba.fastjson.annotation.JSONField;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
@Data
@AllArgsConstructor
public class OperationFieldDiffer {

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 改之前值
     */
    private Object beforeValue;

    /**
     * 改之后值
     */
    private Object afterValue;

    /**
     * 是否核心字段
     */
    @JSONField(serialize = false)
    private Boolean isCore;

    /**
     * 排序值
     */
    @JSONField(serialize = false)
    private Integer order;

    public static OperationFieldDiffer of(String fieldName, Object beforeValue, Object afterValue) {
        return new OperationFieldDiffer(fieldName, beforeValue, afterValue, Boolean.TRUE, BusinessConstant.ZERO);
    }

    public static OperationFieldDiffer of(String fieldName, Object beforeValue, Object afterValue, Boolean isCore, Integer order) {
        return new OperationFieldDiffer(fieldName, beforeValue, afterValue, isCore, order);
    }
}
