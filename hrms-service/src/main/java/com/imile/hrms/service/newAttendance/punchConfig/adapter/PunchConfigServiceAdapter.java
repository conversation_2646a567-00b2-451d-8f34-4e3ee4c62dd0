package com.imile.hrms.service.newAttendance.punchConfig.adapter;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.adapter.AbstractAdapter;
import com.imile.hrms.common.adapter.ServiceAdapter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchConfigQuery;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigDetailDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigExportConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigRangeDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigSelectDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchDayTypeDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchUserInfoDTO;
import com.imile.hrms.dao.punch.dto.WarehouseAttendanceConfigDetailDTO;
import com.imile.hrms.dao.punch.dto.WarehouseAttendancePunchClassItemConfigDTO;
import com.imile.hrms.dao.punch.dto.WarehousePunchClassConfigDTO;
import com.imile.hrms.dao.punch.dto.WarehousePunchConfigDTO;
import com.imile.hrms.dao.punch.dto.WarehousePunchConfigInTimeDTO;
import com.imile.hrms.dao.punch.param.ConfigNoStatusSwitchParam;
import com.imile.hrms.dao.punch.param.HrmsAttendancePunchConfigAddParam;
import com.imile.hrms.dao.punch.param.HrmsAttendancePunchConfigUpdateParam;
import com.imile.hrms.dao.punch.query.AttendancePunchClassExportQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigDetailQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigUserQuery;
import com.imile.hrms.dao.punch.query.WarehouseAttendanceConfigQuery;
import com.imile.hrms.dao.punch.query.WarehouseAttendanceDayByClassIdQuery;
import com.imile.hrms.dao.punch.query.WarehouseAttendanceDayQuery;
import com.imile.hrms.dao.punch.query.WarehousePunchClassConfigQuery;
import com.imile.hrms.dao.punch.query.WarehousePunchConfigQuery;
import com.imile.hrms.dao.punch.query.WarehousePunchInTimeQuery;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.punch.HrmsAttendancePunchConfigManage;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceUser;
import com.imile.hrms.service.newAttendance.common.mapstruct.CommonMapstruct;
import com.imile.hrms.service.newAttendance.punchConfig.PunchService;
import com.imile.hrms.service.newAttendance.punchConfig.application.PunchConfigApplicationService;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigAddOrUpdateCommand;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigAuthUpdateCommand;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigStatusSwitchCommand;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchConfigDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchConfigDetailDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchConfigExportConfigDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchConfigRangeDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchConfigSelectDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchDayTypeDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchUserInfoDTO;
import com.imile.hrms.service.newAttendance.punchConfig.mapstruct.PunchConfigMapstruct;
import com.imile.hrms.service.newAttendance.punchConfig.query.PunchClassExportQuery;
import com.imile.hrms.service.newAttendance.punchConfig.query.PunchConfigUserQuery;
import com.imile.hrms.service.punch.HrmsAttendancePunchConfigService;
import com.imile.hrms.service.punch.dto.PunchTemplateJudgeDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/17
 * @Description
 */
@Slf4j
@Component
public class PunchConfigServiceAdapter extends AbstractAdapter implements ServiceAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    @Resource
    private PunchService punchService;
    @Resource
    private PunchConfigApplicationService punchConfigApplicationService;

    @Resource
    private HrmsAttendancePunchConfigManage attendancePunchConfigManage;
    @Resource
    private HrmsAttendancePunchConfigService attendancePunchConfigService;

    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }


    /**
     * 获取某一用户打卡规则明细,新模块在service实现
     */
    public HrmsAttendancePunchConfigDetailDTO detailUserPunchConfig(Long userId, Date punchTime) {
        return commonQuery(
                () -> {
                    PunchConfigDetailDTO punchConfigDetailDTO = punchService.detailUserPunchConfig(userId, punchTime);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchConfigDetailDTO(punchConfigDetailDTO);
                },
                () -> attendancePunchConfigManage.detailUserPunchConfig(userId, punchTime)
        );
    }


    /**
     * 判断新增打卡规则是否为默认打卡规则
     */
    public PunchTemplateJudgeDTO punchTemplateJudge(String country) {
        return commonQuery(
                () -> {
                    var punchAndCalendarTemplateJudgeDTO = punchConfigApplicationService.punchTemplateJudge(country);
                    return PunchConfigMapstruct.INSTANCE.toTemplateJudgeDTO(punchAndCalendarTemplateJudgeDTO);
                },
                () -> attendancePunchConfigService.punchTemplateJudge(country)
        );
    }

    /**
     * 新增打卡规则,转发到新模块方法
     */
    public List<HrmsAttendancePunchConfigRangeDTO> addPunchConfig(HrmsAttendancePunchConfigAddParam addParam) {
        PunchConfigAddOrUpdateCommand addOrUpdateCommand = PunchConfigMapstruct.INSTANCE.toAddOrUpdateCommand(addParam);
        List<PunchConfigRangeDTO> punchConfigRangeDTOS = punchConfigApplicationService.addPunchConfig(addOrUpdateCommand);
        return PunchConfigMapstruct.INSTANCE.toOldPunchConfigRangeDTO(punchConfigRangeDTOS);
    }

    /**
     * 打卡规则列表
     */
    public PaginationResult<HrmsAttendancePunchConfigDTO> listPunchList(AttendancePunchConfigQuery query) {
        return commonQuery(
                () -> {
                    PunchConfigQuery punchConfigQuery = PunchConfigMapstruct.INSTANCE.toNewPunchConfigQuery(query);
                    PaginationResult<PunchConfigDTO> paginationResult = punchConfigApplicationService.listPunchList(punchConfigQuery);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchConfigPage(paginationResult);
                },
                () -> attendancePunchConfigService.listPunchList(query)
        );
    }

    /**
     * 更新打卡规则，转发到新模块方法
     */
    public List<HrmsAttendancePunchConfigRangeDTO> updatePunchConfig(HrmsAttendancePunchConfigAddParam addParam) {
        PunchConfigAddOrUpdateCommand updateCommand = PunchConfigMapstruct.INSTANCE.toAddOrUpdateCommand(addParam);
        List<PunchConfigRangeDTO> punchConfigRangeDTOS = punchConfigApplicationService.updatePunchConfig(updateCommand);
        return PunchConfigMapstruct.INSTANCE.toOldPunchConfigRangeDTO(punchConfigRangeDTOS);
    }

    /**
     * 更新打卡规则权限,转发到新模块方法
     */
    public List<HrmsAttendancePunchConfigRangeDTO> updateAuthPunchConfig(HrmsAttendancePunchConfigUpdateParam updateParam) {
        PunchConfigAuthUpdateCommand authUpdateCommand = PunchConfigMapstruct.INSTANCE.toAuthUpdateCommand(updateParam);
        List<PunchConfigRangeDTO> punchConfigRangeDTOS = punchConfigApplicationService.updateAuthPunchConfig(authUpdateCommand);
        return PunchConfigMapstruct.INSTANCE.toOldPunchConfigRangeDTO(punchConfigRangeDTOS);
    }

    /**
     * 规则详情
     */
    public HrmsAttendancePunchConfigDetailDTO detailPunchConfig(AttendancePunchConfigDetailQuery query) {
        return commonQuery(
                () -> {
                    PunchConfigDetailQuery punchConfigDetailQuery = PunchConfigMapstruct.INSTANCE.toNewPunchConfigDetailQuery(query);
                    PunchConfigDetailDTO punchConfigDetailDTO = punchConfigApplicationService.detailPunchConfig(punchConfigDetailQuery);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchConfigDetailDTO(punchConfigDetailDTO);
                },
                () -> attendancePunchConfigService.detailPunchConfig(query)
        );
    }

    /**
     * 关闭开启打卡规则,转发到新模块方法
     */
    public List<HrmsAttendancePunchConfigRangeDTO> stateSwitch(ConfigNoStatusSwitchParam statusSwitchParam) {
        PunchConfigStatusSwitchCommand switchCommand = PunchConfigMapstruct.INSTANCE.toSwitchCommand(statusSwitchParam);
        List<PunchConfigRangeDTO> punchConfigRangeDTOS = punchConfigApplicationService.stateSwitch(switchCommand);
        return PunchConfigMapstruct.INSTANCE.toOldPunchConfigRangeDTO(punchConfigRangeDTOS);
    }

    /**
     * 打卡规则下拉框
     */
    public List<HrmsAttendancePunchConfigSelectDTO> selectList(String country) {
        return commonQuery(
                () -> {
                    List<PunchConfigSelectDTO> punchConfigSelectDTOS = punchConfigApplicationService.selectList(country);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchConfigSelect(punchConfigSelectDTOS);
                },
                () -> attendancePunchConfigService.selectList(country)
        );
    }

    /**
     * 该打卡规则可以选择的排班类型
     */
    public List<HrmsAttendancePunchDayTypeDTO> selectDayTypeList(Long punchConfigId, Long userId) {
        return commonQuery(
                () -> {
                    List<PunchDayTypeDTO> punchDayTypeDTOList = punchConfigApplicationService.selectDayTypeList(punchConfigId, userId);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchDayTypeDTO(punchDayTypeDTOList);
                },
                () -> attendancePunchConfigService.selectDayTypeList(punchConfigId, userId)
        );
    }


    /**
     * 打卡规则导出
     */
    public PaginationResult<HrmsAttendancePunchConfigExportConfigDTO> export(AttendancePunchClassExportQuery query) {
        return commonQuery(
                () -> {
                    PunchClassExportQuery punchClassExportQuery = PunchConfigMapstruct.INSTANCE.toNewPunchClassExportQuery(query);
                    PaginationResult<PunchConfigExportConfigDTO> paginationResult = punchConfigApplicationService.export(punchClassExportQuery);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchConfigExportConfigPage(paginationResult);
                },
                () -> attendancePunchConfigService.export(query)
        );
    }

    /**
     * 打卡规则人员列表
     */
    public PaginationResult<HrmsAttendancePunchUserInfoDTO> selectPunchUserList(AttendancePunchConfigUserQuery query) {
        return commonQuery(
                () -> {
                    PunchConfigUserQuery punchConfigUserQuery = PunchConfigMapstruct.INSTANCE.toNewPunchConfigUserQuery(query);
                    PaginationResult<PunchUserInfoDTO> paginationResult = punchConfigApplicationService.selectPunchUserList(punchConfigUserQuery);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchUserInfoPage(paginationResult);
                },
                () -> attendancePunchConfigService.selectPunchUserList(query)
        );
    }


    //=================================以下为仓内打卡配置内部方法=================================================

    /**
     * 根据国家+部门id+打卡类型查找打卡规则以及班次和班次详情：done
     *
     * @param query 参数
     * @return List<WarehousePunchConfigDTO>
     */
    public List<WarehousePunchConfigDTO> selectPunchConfigByWarehouseCondition(WarehousePunchConfigQuery query) {
        return commonQuery(
                () -> punchService.selectPunchConfigByWarehouseCondition(query),
                () -> attendancePunchConfigService.selectPunchConfigByWarehouseCondition(query)
        );
    }

    /**
     * 获取考勤日：仓内出仓根据当前时间 + userId获取考勤日（因为需要知道当前时间打出仓卡，所属考勤日，才能计算考勤）。入仓的考勤日就是当前打卡时间的年月日（班次选错也没关系，因为班次只是表示几点上下班，dayId考勤日不会错）：done
     * 如果获取到的考勤日是未来时间，则仓内不计算考勤
     *
     * @param query 参数
     * @return Long 返回值 ：返回null表示参数为null或用户不存在
     */
    public Long getAttendanceDay(WarehouseAttendanceDayQuery query) {
        return commonQuery(
                () -> punchService.getAttendanceDay(query),
                () -> attendancePunchConfigService.getAttendanceDay(query)
        );
    }

    /**
     * 获取指定班次，当前时间所属考勤日
     *
     * @param query 参数
     * @return Long
     */
    public Long getAttendanceDayByOneClassId(WarehouseAttendanceDayByClassIdQuery query) {
        return commonQuery(
                () -> punchService.getAttendanceDayByOneClassId(query),
                () -> attendancePunchConfigService.getAttendanceDayByOneClassId(query)
        );
    }

    /**
     * 获取该人员当天的日历：根据userId + 当前时间（dayId）获取当天日历情况（这里不需要获取考勤日了，直接取当前时间年月日即可，因为这个是在仓内入仓的时候需要的接口，考勤日就是当前时间）：done
     *
     * @param query 参数
     * @return WarehouseAttendanceConfigDetailDTO
     */
    public WarehouseAttendanceConfigDetailDTO selectNowAttendanceConfigDetail(WarehouseAttendanceConfigQuery query) {
        return commonQuery(
                () -> punchService.selectNowAttendanceConfigDetail(query),
                () -> attendancePunchConfigService.selectNowAttendanceConfigDetail(query)
        );
    }

    /**
     * 根据班次id获取班次详情信息：done
     *
     * @param punchClassId 班次id
     * @return List<WarehouseAttendancePunchClassItemConfigDTO>
     */
    public List<WarehouseAttendancePunchClassItemConfigDTO> selectPunchClassItemDetail(Long punchClassId) {
        return commonQuery(
                () -> punchService.selectPunchClassItemDetail(punchClassId),
                () -> attendancePunchConfigService.selectPunchClassItemDetail(punchClassId)
        );
    }

    /**
     * 获取班次的最早上班时间、最晚上班时间：done
     *
     * @param query 参数
     * @return WarehousePunchConfigInTimeDTO
     */
    public WarehousePunchConfigInTimeDTO selectPunchInTime(WarehousePunchInTimeQuery query) {
        return commonQuery(
                () -> punchService.selectPunchInTime(query),
                () -> attendancePunchConfigService.selectPunchInTime(query)
        );
    }

    /**
     * 根据国家+部门id列表+打卡类型查找班次信息：done
     *
     * @param query 参数
     * @return List<WarehousePunchClassConfigDTO>
     */
    public List<WarehousePunchClassConfigDTO> selectPunchClassConfigByWarehouseCondition(WarehousePunchClassConfigQuery query) {
        return commonQuery(
                () -> punchService.selectPunchClassConfigByWarehouseCondition(query),
                () -> attendancePunchConfigService.selectPunchClassConfigByWarehouseCondition(query)
        );
    }


    //=========================HrmsAttendancePunchConfigRangeService====================================================

    /**
     * 转发到新模块方法
     */
    public boolean addPunchConfigRange(HrmsUserInfoDO hrmsUserInfoDO) {
//        return commonQuery(
//                () -> {
//                    AttendanceUser attendanceUser = CommonMapstruct.INSTANCE.mapToUser(hrmsUserInfoDO);
//                    return punchService.addPunchConfigRange(attendanceUser);
//                },
//                () -> attendancePunchConfigRangeService.addAttendancePunchConfigRange(hrmsUserInfoDO)
//        );
        AttendanceUser attendanceUser = CommonMapstruct.INSTANCE.mapToUser(hrmsUserInfoDO);
        return punchService.addPunchConfigRange(attendanceUser);
    }

    /**
     * 转发到新模块方法
     */
    public boolean addWarehousePunchConfigRange(HrmsUserInfoDO hrmsUserInfoDO) {
//        return commonQuery(
//                () -> {
//                    AttendanceUser attendanceUser = CommonMapstruct.INSTANCE.mapToUser(hrmsUserInfoDO);
//                    return punchService.addWarehousePunchConfigRange(attendanceUser);
//                },
//                () -> attendancePunchConfigRangeService.addWarehouseAttendancePunchConfigRange(hrmsUserInfoDO)
//        );
        AttendanceUser attendanceUser = CommonMapstruct.INSTANCE.mapToUser(hrmsUserInfoDO);
        return punchService.addWarehousePunchConfigRange(attendanceUser);
    }


}
