package com.imile.hrms.service.leave.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigInvalidTypeEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigIssueFrequencyEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigIssueRoundingRuleEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigIssueTimeEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigIssueTypeEnum;
import com.imile.hrms.common.enums.leave.LeaveConfigRangRangeTypeEnum;
import com.imile.hrms.common.enums.leave.LeaveConsumeTypeEnum;
import com.imile.hrms.common.enums.punch.PunchDayTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.leave.HrmsCompanyLeaveConfigRangDao;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigCarryOverDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigCarryOverRangeDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigIssueRuleDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigIssueRuleRangeDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigRangDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveJourneyConfigDO;
import com.imile.hrms.dao.leave.query.LeaveConfigRangQuery;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveItemConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveDetailDO;
import com.imile.hrms.dao.user.query.CompanyLeaveQuery;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.dao.user.query.UserLeaveDetailQuery;
import com.imile.hrms.manage.company.HrmsCompanyLeaveConfigManage;
import com.imile.hrms.manage.leave.HrmsCompanyWelfareLeaveConfigManage;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.manage.user.HrmsUserLeaveDetailManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.leave.HrmsCompanyWelfareLeaveConfigService;
import com.imile.hrms.service.leave.param.LeaveItemConfigSaveParam;
import com.imile.hrms.service.leave.param.WelfareLeaveConfigDetailParam;
import com.imile.hrms.service.leave.param.WelfareLeaveConfigImportParam;
import com.imile.hrms.service.leave.param.WelfareLeaveConfigSaveParam;
import com.imile.hrms.service.leave.param.WelfareLeaveConfigStatusUpdateParam;
import com.imile.hrms.service.leave.param.WelfareLeaveConfigUpdateParam;
import com.imile.hrms.service.leave.vo.LeaveConfigCarryOverRangeVO;
import com.imile.hrms.service.leave.vo.LeaveConfigCarryOverVO;
import com.imile.hrms.service.leave.vo.LeaveConfigIssueRuleRangeVO;
import com.imile.hrms.service.leave.vo.LeaveConfigIssueRuleVO;
import com.imile.hrms.service.leave.vo.LeaveItemConfigVO;
import com.imile.hrms.service.leave.vo.LeaveJourneyConfigVO;
import com.imile.hrms.service.leave.vo.WelfareLeaveConfigVO;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceUser;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.dto.CompanyLeaveConfigDetailDTO;
import com.imile.hrms.service.user.dto.CompanyLeaveConfigQueryDTO;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyWelfareLeaveConfigServiceimpl
 * {@code @since:} 2024-04-08 15:30
 * {@code @description:}
 */
@Service
@Slf4j
public class HrmsCompanyWelfareLeaveConfigServiceImpl extends BaseService implements HrmsCompanyWelfareLeaveConfigService {

    @Autowired
    private UserResourceService userResourceService;
    @Autowired
    private HrmsCompanyLeaveConfigManage companyLeaveConfigManage;
    @Autowired
    private HrmsCompanyWelfareLeaveConfigManage hrmsCompanyWelfareLeaveConfigManage;
    @Autowired
    protected IHrmsIdWorker iHrmsIdWorker;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsUserLeaveDetailManage hrmsUserLeaveDetailManage;
    @Autowired
    private HrmsDeptNewManage deptManage;
    @Autowired
    private HrmsCompanyLeaveConfigRangDao hrmsCompanyLeaveConfigRangDao;
    @Autowired
    private HrmsUserEntryRecordManage userEntryRecordManage;

    @Override
    public PaginationResult<CompanyLeaveConfigDetailDTO> list(CompanyLeaveConfigQueryDTO queryDTO) {
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        List<String> countryList = userResourceService.getAuthorizedBizCountryList(RequestInfoHolder.getUserId());
        //没有权限
        if (CollectionUtils.isEmpty(countryList)) {
            PageInfo<CompanyLeaveConfigDetailDTO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, queryDTO);
        }
        companyLeaveQuery.setCountryList(countryList);
        if (StringUtils.isNotBlank(queryDTO.getCountry())) {
            if (countryList.contains(queryDTO.getCountry())) {
                companyLeaveQuery.setCountryList(Arrays.asList(queryDTO.getCountry()));
            } else {
                PageInfo<CompanyLeaveConfigDetailDTO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
                return getPageResult(pageInfoResult, queryDTO);
            }
        }
        companyLeaveQuery.setLeaveName(queryDTO.getLeaveName());
        companyLeaveQuery.setLeaveType(queryDTO.getLeaveType());
        companyLeaveQuery.setStatus(queryDTO.getStatus());
        companyLeaveQuery.setIsDispatch(queryDTO.getIsDispatch());
        Page<HrmsCompanyLeaveConfigDO> page = PageHelper.startPage(queryDTO.getCurrentPage(), queryDTO.getShowCount(), queryDTO.getShowCount() > 0);
        PageInfo<HrmsCompanyLeaveConfigDO> pageInfo = page.doSelectPageInfo(() -> companyLeaveConfigManage.selectCompanyLeave(companyLeaveQuery));
        List<HrmsCompanyLeaveConfigDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            PageInfo<CompanyLeaveConfigDetailDTO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, queryDTO);
        }

        List<CompanyLeaveConfigDetailDTO> resultList = new ArrayList<>();
        for (HrmsCompanyLeaveConfigDO leaveConfigDO : list) {
            CompanyLeaveConfigDetailDTO resultDto = new CompanyLeaveConfigDetailDTO();
            resultDto.setId(leaveConfigDO.getId());
            resultDto.setCountry(leaveConfigDO.getCountry());
            resultDto.setCreateUser(leaveConfigDO.getCreateUserName());
            resultDto.setCreateDate(leaveConfigDO.getCreateDate());
            resultDto.setLastUpdateUser(leaveConfigDO.getLastUpdUserName());
            resultDto.setLastUpdateDate(leaveConfigDO.getLastUpdDate());
            resultDto.setLeaveType(leaveConfigDO.getLeaveType());
            resultDto.setStatus(leaveConfigDO.getStatus());
            resultDto.setLeaveName(leaveConfigDO.getLeaveName());
            resultDto.setLeaveShortName(leaveConfigDO.getLeaveShortName());
            resultDto.setConsumeLeaveType(leaveConfigDO.getConsumeLeaveType());
            resultDto.setIsDispatch(leaveConfigDO.getIsDispatch());
            resultList.add(resultDto);
        }
        PageInfo<CompanyLeaveConfigDetailDTO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(resultList);
        return getPageResult(pageInfoResult, queryDTO);
    }

    /**
     * 新增福利假期配置：只保存配置信息，不分配假期，分配假期使用定时任务
     *
     * @param param 入参
     * @return 是否成功
     */
    @Override
    public boolean add(WelfareLeaveConfigSaveParam param) {
        log.info("新增福利假期配置入参：{}", param);
        checkWelfareLeaveConfigParam(param);
        // 校验假期名称是否重复
        param.setLeaveName(param.getLeaveName().trim());
        HrmsCompanyLeaveConfigDO configDO = new HrmsCompanyLeaveConfigDO();
        configDO.setLeaveName(param.getLeaveName());
        configDO.setCountry(param.getCountry());
        List<HrmsCompanyLeaveConfigDO> leaveConfigDOList = companyLeaveConfigManage.selectLeaveConfig(configDO);
        if (CollectionUtils.isNotEmpty(leaveConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.COMPANY_LEAVE_REPEAT_ERROR.getDesc(), "company leave repeat");
        }
        // 1. 构建国家假期主表信息
        HrmsCompanyLeaveConfigDO leaveConfig = BeanUtils.convert(param, HrmsCompanyLeaveConfigDO.class);
        leaveConfig.setId(iHrmsIdWorker.nextId());
        String employeeType = String.join(",", param.getEmployeeTypeList());
        String deptId = param.getDeptIdList().stream().map(Convert::toStr).collect(Collectors.joining(","));
        leaveConfig.setEmployeeType(employeeType);
        leaveConfig.setDeptIds(deptId);
        // 设置派遣国
        if (CollectionUtils.isNotEmpty(param.getDispatchCountry())) {
            String dispatchCountry = StringUtils.join(param.getDispatchCountry(), ",");
            leaveConfig.setDispatchCountry(dispatchCountry);
        }
        BaseDOUtil.fillDOInsert(leaveConfig);

        // 2. 构建福利假期使用范围表
        List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangList = Lists.newArrayList();
        setLeaveConfigRange(param, leaveConfig, leaveConfigRangList);

        // 3. 构建福利假发放规则数据
        HrmsCompanyLeaveConfigIssueRuleDO leaveConfigIssueRule = BeanUtils.convert(param.getLeaveConfigIssueRuleSaveParam(), HrmsCompanyLeaveConfigIssueRuleDO.class);
        leaveConfigIssueRule.setId(iHrmsIdWorker.nextId());
        leaveConfigIssueRule.setLeaveId(leaveConfig.getId());
        BaseDOUtil.fillDOInsert(leaveConfigIssueRule);

        // 构建发放规则阶梯范围数据（含司龄递增、年龄递增、工龄递增、常驻省市）
        List<HrmsCompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = Lists.newArrayList();
        // 构建路途假数据
        List<HrmsCompanyLeaveJourneyConfigDO> leaveJourneyConfigList = Lists.newArrayList();
        // 构建假期详情数据
        List<HrmsCompanyLeaveItemConfigDO> leaveItemConfigList = Lists.newArrayList();

        // 如果发放类型是司龄递增/年龄递增/工龄递增，那么需要构建发放范围数据
        if (LeaveConfigIssueTypeEnum.getRangeTypeList().contains(param.getLeaveConfigIssueRuleSaveParam().getIssueType())) {
            leaveConfigIssueRuleRangeList = BeanUtils.convert(HrmsCompanyLeaveConfigIssueRuleRangeDO.class, param.getLeaveConfigIssueRuleSaveParam().getLeaveConfigIssueRuleRangeList());
            leaveConfigIssueRuleRangeList.forEach(leaveConfigIssueRuleRange -> {
                leaveConfigIssueRuleRange.setId(iHrmsIdWorker.nextId());
                leaveConfigIssueRuleRange.setIssueRuleId(leaveConfigIssueRule.getId());
                BaseDOUtil.fillDOInsert(leaveConfigIssueRuleRange);
            });
        }
        // 如果发放类型是派遣国远近，那么需要构建路途假数据
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueType()
                , LeaveConfigIssueTypeEnum.DISPATCH_COUNTRY_DISTANCE.getType())) {
            leaveJourneyConfigList = BeanUtils.convert(HrmsCompanyLeaveJourneyConfigDO.class, param.getLeaveConfigIssueRuleSaveParam().getLeaveJourneyConfigList());
            leaveJourneyConfigList.forEach(leaveJourneyConfig -> {
                leaveJourneyConfig.setId(iHrmsIdWorker.nextId());
                leaveJourneyConfig.setIssueRuleId(leaveConfigIssueRule.getId());
                BaseDOUtil.fillDOInsert(leaveJourneyConfig);
            });
        }
        // 其他情况，需要构建假期详情数据
        if (CollectionUtils.isNotEmpty(param.getLeaveItemConfigSaveParamList())) {
            // 假期百分比处理
            // 根据阶段从低到高排序
            List<LeaveItemConfigSaveParam> itemList = param.getLeaveItemConfigSaveParamList().stream().sorted(Comparator.comparing(LeaveItemConfigSaveParam::getStage)).collect(Collectors.toList());
            itemList.forEach(item -> {
                // 百分比不为0的时候，需要除以100
                if (item.getPercentSalary().compareTo(BigDecimal.ZERO) > 0) {
                    item.setPercentSalary(item.getPercentSalary().divide(new BigDecimal(100)));
                }
            });

            BigDecimal startDayTemp = BigDecimal.ONE;
            BigDecimal endDayTemp = BigDecimal.ZERO;
            for (LeaveItemConfigSaveParam itemConfigParan : itemList) {
                endDayTemp = endDayTemp.add(itemConfigParan.getLeaveDay());
                HrmsCompanyLeaveItemConfigDO itemConfigDO = new HrmsCompanyLeaveItemConfigDO();
                itemConfigDO.setId(iHrmsIdWorker.nextId());
                itemConfigDO.setLeaveId(leaveConfig.getId());
                itemConfigDO.setStage(itemConfigParan.getStage());
                itemConfigDO.setPercentSalary(itemConfigParan.getPercentSalary());
                itemConfigDO.setStartDay(startDayTemp);
                itemConfigDO.setEndDay(endDayTemp);
                startDayTemp = startDayTemp.add(itemConfigParan.getLeaveDay());
                BaseDOUtil.fillDOInsert(itemConfigDO);
                leaveItemConfigList.add(itemConfigDO);
            }
        }

        // 4. 构建结转规则数据
        HrmsCompanyLeaveConfigCarryOverDO leaveConfigCarryOver = BeanUtils.convert(param.getLeaveConfigCarryOverSaveParam(), HrmsCompanyLeaveConfigCarryOverDO.class);
        leaveConfigCarryOver.setId(iHrmsIdWorker.nextId());
        leaveConfigCarryOver.setLeaveId(leaveConfig.getId());
        BaseDOUtil.fillDOInsert(leaveConfigCarryOver);
        // 构建结转规则失效范围数据
        List<HrmsCompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList = Lists.newArrayList();
        // 如果结转失效类型是按入职日期设置，那么需要构建失效范围数据
        if (LeaveConfigInvalidTypeEnum.EXPIRES_ON_THE_DATE_OF_ENTRY.getType()
                .equals(param.getLeaveConfigCarryOverSaveParam().getInvalidType())) {
            leaveConfigCarryOverRangeList = BeanUtils.convert(HrmsCompanyLeaveConfigCarryOverRangeDO.class
                    , param.getLeaveConfigCarryOverSaveParam().getLeaveConfigCarryOverRangeList());
            leaveConfigCarryOverRangeList.forEach(leaveConfigCarryOverRange -> {
                leaveConfigCarryOverRange.setId(iHrmsIdWorker.nextId());
                leaveConfigCarryOverRange.setCarryOverId(leaveConfigCarryOver.getId());
                BaseDOUtil.fillDOInsert(leaveConfigCarryOverRange);
            });
        }

        hrmsCompanyWelfareLeaveConfigManage.addWelfareLeaveConfig(leaveConfig, leaveConfigRangList, leaveConfigIssueRule
                , leaveConfigIssueRuleRangeList, leaveItemConfigList, leaveConfigCarryOver
                , leaveConfigCarryOverRangeList, leaveJourneyConfigList);
        return true;
    }

    /**
     * 福利假期详情：TODO：枚举值处理
     *
     * @param param 入参
     * @return 详情
     */
    @Override
    public WelfareLeaveConfigVO detail(WelfareLeaveConfigDetailParam param) {
        log.info("福利假期详情入参：{}", param);
        Long leaveId = param.getId();
        // 1. 福利假期主表信息
        HrmsCompanyLeaveConfigDO leaveConfig = companyLeaveConfigManage.getById(leaveId);
        if (ObjectUtil.isNull(leaveConfig)) {
            throw BusinessException.get(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc());
        }
        // 2. 福利假期使用范围表
        LeaveConfigRangQuery leaveConfigRangQuery = new LeaveConfigRangQuery();
        leaveConfigRangQuery.setLeaveId(leaveId);
        // 查询用户级别的数据
        leaveConfigRangQuery.setRangeType(LeaveConfigRangRangeTypeEnum.USER.getType());
        List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangList = hrmsCompanyWelfareLeaveConfigManage.getLeaveConfigRangList(leaveConfigRangQuery);
        // 获取leaveConfigRangList的userId
        List<String> userCodeList = leaveConfigRangList.stream().map(HrmsCompanyLeaveConfigRangDO::getUserCode).collect(Collectors.toList());
        // 3. 福利假发放规则数据
        List<HrmsCompanyLeaveConfigIssueRuleDO> leaveConfigIssueRule = hrmsCompanyWelfareLeaveConfigManage.getLeaveConfigIssueRuleListById(leaveId);
        List<HrmsCompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = Lists.newArrayList();
        List<HrmsCompanyLeaveJourneyConfigDO> leaveJourneyConfigList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(leaveConfigIssueRule)) {
            // TODO：需要不要判断是否有多个发放规则
            Long issueRuleId = leaveConfigIssueRule.get(0).getId();
            // 4. 福利假发放规则：范围表
            leaveConfigIssueRuleRangeList = hrmsCompanyWelfareLeaveConfigManage.getLeaveConfigIssueRuleRangeList(issueRuleId);
            // 5. 福利假发放规则：路途假规则表
            leaveJourneyConfigList = hrmsCompanyWelfareLeaveConfigManage.getLeaveJourneyConfigList(issueRuleId);
        }
        List<HrmsCompanyLeaveItemConfigDO> itemConfigDOList = companyLeaveConfigManage.selectItemByConfigId(Collections.singletonList(leaveId));
        List<LeaveItemConfigVO> leaveItemConfiList = new ArrayList<>();
        BigDecimal lastLeaveDay = BigDecimal.ZERO;
        for (HrmsCompanyLeaveItemConfigDO itemConfigDO : itemConfigDOList) {
            LeaveItemConfigVO leaveItemConfig = new LeaveItemConfigVO();
            leaveItemConfig.setId(itemConfigDO.getId());
            leaveItemConfig.setLeaveDay(itemConfigDO.getEndDay().subtract(lastLeaveDay));
            leaveItemConfig.setPercentSalary(itemConfigDO.getPercentSalary().multiply(new BigDecimal("100")));
            leaveItemConfig.setStage(itemConfigDO.getStage());
            leaveItemConfiList.add(leaveItemConfig);
            lastLeaveDay = itemConfigDO.getEndDay();
        }
        LeaveConfigIssueRuleVO targetLeaveConfigIssueRule = null;
        if (CollUtil.isNotEmpty(leaveConfigIssueRule)) {
            targetLeaveConfigIssueRule = BeanUtils.convert(leaveConfigIssueRule.get(0), LeaveConfigIssueRuleVO.class);
            List<LeaveConfigIssueRuleRangeVO> targetLeaveConfigIssueRuleRange = BeanUtils.convert(LeaveConfigIssueRuleRangeVO.class, leaveConfigIssueRuleRangeList);
            targetLeaveConfigIssueRule.setLeaveConfigIssueRuleRangeList(targetLeaveConfigIssueRuleRange);
            List<LeaveJourneyConfigVO> targetLeaveJourneyConfig = BeanUtils.convert(LeaveJourneyConfigVO.class, leaveJourneyConfigList);
            targetLeaveConfigIssueRule.setLeaveJourneyConfigList(targetLeaveJourneyConfig);
        }

        // 6. 福利假期结转规则数据
        List<HrmsCompanyLeaveConfigCarryOverDO> leaveConfigCarryOverList = hrmsCompanyWelfareLeaveConfigManage.getCarryOverByConfigId(leaveId);
        LeaveConfigCarryOverVO targetLeaveConfigCarryOver = null;
        if (CollUtil.isNotEmpty(leaveConfigCarryOverList)) {
            HrmsCompanyLeaveConfigCarryOverDO leaveConfigCarryOver = leaveConfigCarryOverList.get(0);
            targetLeaveConfigCarryOver = BeanUtils.convert(leaveConfigCarryOver, LeaveConfigCarryOverVO.class);
            List<HrmsCompanyLeaveConfigCarryOverRangeDO> LeaveConfigCarryOverRangeList = hrmsCompanyWelfareLeaveConfigManage.getLeaveConfigCarryOverRangeList(leaveConfigCarryOver.getId());
            List<LeaveConfigCarryOverRangeVO> targetLeaveConfigCarryOverRangeConfig = BeanUtils.convert(LeaveConfigCarryOverRangeVO.class, LeaveConfigCarryOverRangeList);
            targetLeaveConfigCarryOver.setLeaveConfigCarryOverRangeList(targetLeaveConfigCarryOverRangeConfig);
        }


        // 福利假期主表信息
        WelfareLeaveConfigVO leaveConfigVO = BeanUtils.convert(leaveConfig, WelfareLeaveConfigVO.class);
        leaveConfigVO.setUserCodeList(userCodeList);
        List<String> userNameList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList).stream()
                .map(RequestInfoHolder.isChinese() ? HrmsUserInfoDO::getUserName : HrmsUserInfoDO::getUserNameEn)
                .collect(Collectors.toList());
        leaveConfigVO.setUserNameList(userNameList);
        List<Long> deptIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(leaveConfig.getDeptIds())) {
            deptIdList = Arrays.stream(leaveConfig.getDeptIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        List<String> deptNameList = deptManage.getDeptListByIdList(deptIdList).stream()
                .map(RequestInfoHolder.isChinese() ? HrmsEntDeptDO::getDeptNameCn : HrmsEntDeptDO::getDeptNameEn)
                .collect(Collectors.toList());
        leaveConfigVO.setDeptIdList(deptIdList);
        leaveConfigVO.setDeptNameList(deptNameList);
        leaveConfigVO.setEmployeeTypeList(Arrays.asList(leaveConfig.getEmployeeType().split(",")));
        // 发放规则数据，包含发放规则范围数据
        leaveConfigVO.setLeaveConfigIssueRule(targetLeaveConfigIssueRule);
        // 阶梯假期数据
        leaveConfigVO.setLeaveItemConfigList(leaveItemConfiList);
        // 结转规则数据
        leaveConfigVO.setLeaveConfigCarryOver(targetLeaveConfigCarryOver);

        return leaveConfigVO;
    }

    @Override
    public boolean update(WelfareLeaveConfigUpdateParam param) {
        // 查询假期信息
        HrmsCompanyLeaveConfigDO leaveConfig = companyLeaveConfigManage.getById(param.getId());
        if (ObjectUtil.isNull(leaveConfig)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR);
        }
        // 更新假期表
        updateLeaveConfig(leaveConfig, param);
        // 3. 新增范围表
        List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangList = Lists.newArrayList();
        // 需要删除的范围数据
        List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangDeleteList = Lists.newArrayList();
        List<HrmsCompanyLeaveConfigRangDO> hrmsCompanyLeaveConfigRangDOS = hrmsCompanyLeaveConfigRangDao.selectByLeaveId(Arrays.asList(leaveConfig.getId()));
        if (CollectionUtils.isNotEmpty(hrmsCompanyLeaveConfigRangDOS)) {
            for (HrmsCompanyLeaveConfigRangDO leaveConfigRangDO : hrmsCompanyLeaveConfigRangDOS) {
                leaveConfigRangDO.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(leaveConfigRangDO);
                leaveConfigRangDeleteList.add(leaveConfigRangDO);
            }
        }
        WelfareLeaveConfigSaveParam saveParam = BeanUtils.convert(param, WelfareLeaveConfigSaveParam.class);
        setLeaveConfigRange(saveParam, leaveConfig, leaveConfigRangList);
        hrmsCompanyWelfareLeaveConfigManage.updateWelfareLeaveConfig(leaveConfig, leaveConfigRangDeleteList, leaveConfigRangList);
        return true;
    }

    /**
     * 修改国家假期状态
     *
     * @param param 入参
     * @return 是否成功
     */
    @Override
    public boolean updateStatus(WelfareLeaveConfigStatusUpdateParam param) {
        log.info("修改国家假期状态入参：{}", param);
        // 1. 查询国家假期信息
        HrmsCompanyLeaveConfigDO leaveConfig = companyLeaveConfigManage.getById(param.getId());
        if (ObjectUtil.isNull(leaveConfig)) {
            throw BusinessException.get(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc(), "no company leave exists");
        }
        if (StringUtils.equals(leaveConfig.getStatus(), param.getStatus())) {
            return true;
        }
        leaveConfig.setStatus(param.getStatus());
        fillDOUpdate(leaveConfig);

        // 2. 修改国家假期状态，需要修改用户假期表状态
        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        //List<HrmsUserInfoDO> userList = hrmsUserInfoDao.selectUserInfoByOriginCountryList(Arrays.asList(leaveConfig.getCountry()));
        UserDaoQuery userDaoQuery = UserDaoQuery.builder().locationCountry(leaveConfig.getCountry()).build();
        List<HrmsUserInfoDO> userList = hrmsUserInfoDao.userList(userDaoQuery);
        List<Long> userIdList = userList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
        query.setUserIds(userIdList);
        query.setConfigId(leaveConfig.getId());
//        query.setLeaveType(leaveConfig.getLeaveType());
        query.setStatus(StatusEnum.ACTIVE.getCode());
        if (StringUtils.equalsIgnoreCase(StatusEnum.ACTIVE.getCode(), param.getStatus())) {
            query.setStatus(StatusEnum.DISABLED.getCode());
        }
        List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = hrmsUserLeaveDetailManage.selectUserLeaveDetail(query);
        userLeaveDetailDOList.forEach(item -> {
            item.setStatus(leaveConfig.getStatus());
            fillDOUpdate(item);
        });
        hrmsCompanyWelfareLeaveConfigManage.updateStatus(leaveConfig, userLeaveDetailDOList);
        return true;
    }


    /**
     * 导入福利假期
     *
     * @param param 入参
     * @return 错误信息
     */
    @Override
    public List<WelfareLeaveConfigImportParam> importWelfareLeaveConfig(List<WelfareLeaveConfigImportParam> param) {
        List<WelfareLeaveConfigImportParam> failImportList = new ArrayList<>();
        List<WelfareLeaveConfigImportParam> params = checkWelfareLeaveConfigImportParam(param, failImportList);
        if (CollectionUtils.isEmpty(params)) return failImportList;
        out:
        for (WelfareLeaveConfigImportParam item : params) {
            try {
                // 1. 更新主表
                HrmsCompanyLeaveConfigDO configDO = new HrmsCompanyLeaveConfigDO();
                configDO.setLeaveType(item.getLeaveType());
                configDO.setCountry(item.getCountry());
                List<HrmsCompanyLeaveConfigDO> leaveConfigDOList = companyLeaveConfigManage.selectLeaveConfig(configDO);
                if (CollectionUtils.isEmpty(leaveConfigDOList)) {
                    setFailInfo(item, failImportList, HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR);
                    continue;
                }
                HrmsCompanyLeaveConfigDO leaveConfig = BeanUtils.convert(item, HrmsCompanyLeaveConfigDO.class);
                transferEmptyLeaveConfigDO(leaveConfig);
                leaveConfig.setId(leaveConfigDOList.get(0).getId());
                BaseDOUtil.fillDOUpdate(leaveConfig);
                // 2. 新增范围表
                List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangList = Lists.newArrayList();
                // 需要删除的范围数据
                List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangDeleteList = Lists.newArrayList();
                List<HrmsCompanyLeaveConfigRangDO> hrmsCompanyLeaveConfigRangDOS = hrmsCompanyLeaveConfigRangDao.selectByLeaveId(Arrays.asList(leaveConfig.getId()));
                if (CollectionUtils.isNotEmpty(hrmsCompanyLeaveConfigRangDOS)) {
                    for (HrmsCompanyLeaveConfigRangDO leaveConfigRangDO : hrmsCompanyLeaveConfigRangDOS) {
                        leaveConfigRangDO.setIsDelete(IsDeleteEnum.YES.getCode());
                        fillDOUpdate(leaveConfigRangDO);
                        leaveConfigRangDeleteList.add(leaveConfigRangDO);
                    }
                }
                WelfareLeaveConfigSaveParam saveParam = BeanUtils.convert(item, WelfareLeaveConfigSaveParam.class);
                if (StringUtils.isNotBlank(item.getEmployeeType())) {
                    List<String> employeeTypeList = Arrays.asList(item.getEmployeeType().split(","));
                    saveParam.setEmployeeTypeList(employeeTypeList);
                }
                if (StringUtils.isNotBlank(item.getUserCodes())) {
                    List<String> userCodeList = Arrays.asList(item.getUserCodes().split(","));
                    saveParam.setUserCodeList(userCodeList);
                }
                if (StringUtils.isNotBlank(item.getDeptIds())) {
                    List<Long> deptIdList = Arrays.asList(item.getDeptIds().split(",")).stream().map(e -> Long.parseLong(e)).collect(Collectors.toList());
                    saveParam.setDeptIdList(deptIdList);
                }
                setLeaveConfigRange(saveParam, leaveConfig, leaveConfigRangList);
                //查询目前已经有的范围
                LeaveConfigRangQuery leaveConfigRangQuery = new LeaveConfigRangQuery();
                leaveConfigRangQuery.setLeaveId(leaveConfig.getId());
                // 3. 构建福利假发放规则数据
                List<HrmsCompanyLeaveConfigIssueRuleDO> localLeaveConfigIssueRule = hrmsCompanyWelfareLeaveConfigManage.getLeaveConfigIssueRuleListById(leaveConfig.getId());
                HrmsCompanyLeaveConfigIssueRuleDO leaveConfigIssueRule = BeanUtils.convert(item, HrmsCompanyLeaveConfigIssueRuleDO.class);
                if (Objects.isNull(leaveConfigIssueRule.getIssueRoundingRule()))
                    leaveConfigIssueRule.setIssueRoundingRule(LeaveConfigIssueRoundingRuleEnum.ROUND.getType());
                if (CollectionUtils.isEmpty(localLeaveConfigIssueRule)) {
                    leaveConfigIssueRule.setId(iHrmsIdWorker.nextId());
                    BaseDOUtil.fillDOInsert(leaveConfigIssueRule);
                } else {
                    leaveConfigIssueRule.setId(localLeaveConfigIssueRule.get(0).getId());
                    BaseDOUtil.fillDOUpdate(leaveConfigIssueRule);
                }
                leaveConfigIssueRule.setLeaveId(leaveConfig.getId());

                //4. 构建司龄递增数据
                List<HrmsCompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = Lists.newArrayList();
                // 构建假期详情数据(导入不做假期详情数据导入)
                // List<HrmsCompanyLeaveItemConfigDO> leaveItemConfigList = Lists.newArrayList();

                // 如果发放类型是司龄递增，那么需要构建司龄递增数据
                if (ObjectUtil.equal(item.getIssueType(), LeaveConfigIssueTypeEnum.INCREASING_NUMBER_OF_LEADERS.getType())) {
                    String leaveConfigIssueRuleRangeImport = item.getLeaveConfigIssueRuleRange();
                    if (StringUtils.isBlank(leaveConfigIssueRuleRangeImport)) {
                        setFailInfo(item, failImportList, HrmsErrorCodeEnums.NO_COMPANY_LEAVE_STAGE_ERROR);

                        continue;
                    }
                    List<String> listRangeCharacter = Arrays.asList(leaveConfigIssueRuleRangeImport.split("_"));
                    //查询本地司龄发放规则是否存在，存在则需要把原先数据删除后再新增
                    List<HrmsCompanyLeaveConfigIssueRuleRangeDO> localLeaveConfigIssueRuleRangeList = hrmsCompanyWelfareLeaveConfigManage.getLeaveConfigIssueRuleRangeList(leaveConfigIssueRule.getId());
                    if (CollectionUtils.isNotEmpty(localLeaveConfigIssueRuleRangeList)) {
                        for (HrmsCompanyLeaveConfigIssueRuleRangeDO hrmsCompanyLeaveConfigIssueRuleRangeDO : localLeaveConfigIssueRuleRangeList) {
                            hrmsCompanyLeaveConfigIssueRuleRangeDO.setIsDelete(IsDeleteEnum.YES.getCode());
                            BaseDOUtil.fillDOUpdate(hrmsCompanyLeaveConfigIssueRuleRangeDO);
                            leaveConfigIssueRuleRangeList.add(hrmsCompanyLeaveConfigIssueRuleRangeDO);
                        }
                    }
                    //按照下划线分割字符构建司龄发放范围数据
                    for (String character : listRangeCharacter) {
                        if (!character.contains("-")) {
                            setFailInfo(item, failImportList, HrmsErrorCodeEnums.NO_COMPANY_LEAVE_STAGE_ERROR);
                            continue out;
                        }
                        HrmsCompanyLeaveConfigIssueRuleRangeDO leaveConfigIssueRuleRange = new HrmsCompanyLeaveConfigIssueRuleRangeDO();
                        String[] charArray = character.split("\\-");
                        if (charArray.length != 5) {
                            setFailInfo(item, failImportList, HrmsErrorCodeEnums.NO_COMPANY_LEAVE_STAGE_ERROR);
                            continue out;
                        }
                        leaveConfigIssueRuleRange.setId(iHrmsIdWorker.nextId());
                        leaveConfigIssueRuleRange.setSymbolLeft(Integer.valueOf(charArray[0]));
                        leaveConfigIssueRuleRange.setYearLeft(Integer.valueOf(charArray[1]));
                        leaveConfigIssueRuleRange.setSymbolRight(Integer.valueOf(charArray[2]));
                        leaveConfigIssueRuleRange.setYearRight(Integer.valueOf(charArray[3]));
                        leaveConfigIssueRuleRange.setIssueRuleId(leaveConfigIssueRule.getId());
                        leaveConfigIssueRuleRange.setIssueQuota(BigDecimal.valueOf(Long.parseLong(charArray[4])));
                        BaseDOUtil.fillDOInsert(leaveConfigIssueRuleRange);
                        leaveConfigIssueRuleRangeList.add(leaveConfigIssueRuleRange);
                    }
                } else {
                    // 假期详情数据不做导入处理，暂时不导入
                    //TODO
                }
                // 4. 构建结转规则数据
                //查询当前结转规则是否存在
                HrmsCompanyLeaveConfigCarryOverDO leaveConfigCarryOver;
                List<HrmsCompanyLeaveConfigCarryOverDO> leaveConfigCarryOverList = hrmsCompanyWelfareLeaveConfigManage.getCarryOverByConfigId(leaveConfig.getId());
                if (CollectionUtils.isEmpty(leaveConfigCarryOverList)) {
                    leaveConfigCarryOver = BeanUtils.convert(item, HrmsCompanyLeaveConfigCarryOverDO.class);
                    leaveConfigCarryOver.setId(iHrmsIdWorker.nextId());
                    leaveConfigCarryOver.setLeaveId(leaveConfig.getId());
                    BaseDOUtil.fillDOInsert(leaveConfigCarryOver);
                } else {
                    leaveConfigCarryOver = leaveConfigCarryOverList.get(0);
                    leaveConfigCarryOver.setIsCarryOver(item.getIsCarryOver());
                    leaveConfigCarryOver.setMaxCarryOverDay(item.getMaxCarryOverDay());
                    leaveConfigCarryOver.setIsInvalid(item.getIsInvalid());
                    leaveConfigCarryOver.setInvalidDate(item.getInvalidDate());
                    BaseDOUtil.fillDOUpdate(leaveConfigCarryOver);
                }

                hrmsCompanyWelfareLeaveConfigManage.importWelfareLeaveConfig(leaveConfig, leaveConfigRangDeleteList, leaveConfigRangList, leaveConfigIssueRule, leaveConfigIssueRuleRangeList, leaveConfigCarryOver);
            } catch (Exception e) {
                IpepUtils.putFail(item, e.getMessage());
                failImportList.add(item);
            }
        }


        return failImportList;
    }

    @Override
    public boolean isConsumeLeave(String consumeTypeStr, String dayPunchType) {
        if (StringUtils.isEmpty(consumeTypeStr) || StringUtils.isEmpty(dayPunchType)) {
            return true;
        }
        List<String> consumeType = Arrays.asList(consumeTypeStr.split(BusinessConstant.DEFAULT_DELIMITER));
        if (PunchDayTypeEnum.PH.getCode().equals(dayPunchType) && !consumeType.contains(LeaveConsumeTypeEnum.PH.getType())) {
            return false;
        }
        if (PunchDayTypeEnum.OFF.getCode().equals(dayPunchType) && !consumeType.contains(LeaveConsumeTypeEnum.OFF.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 判断用户是否满足绑定条件
     * @param leaveConfig 假期规则
     * @param userInfoDO 用户
     * @return
     */
    @Override
    public boolean checkInCondition(HrmsCompanyLeaveConfigDO leaveConfig, HrmsUserInfoDO userInfoDO) {
        // 重构拷贝保留原先判断逻辑，代码未做更改
        Integer useSex = leaveConfig.getUseSex();
        String deptIds = leaveConfig.getDeptIds();
        String employeeType = leaveConfig.getEmployeeType();
        String startEntryDate = leaveConfig.getStartEntryDate();
        String endEntryDate = leaveConfig.getEndEntryDate();
        List<String> employeeTypeList = Arrays.asList(employeeType.split(","));

        // 默认发放的五个用户类型：如果假期没有选择用工类型，就是全部的用工类型，就是下面的五个
        List<String> defaultEmployeeType = Arrays.asList(EmploymentTypeEnum.EMPLOYEE.getCode(),
                EmploymentTypeEnum.SUB_EMPLOYEE.getCode(),
                EmploymentTypeEnum.INTERN.getCode(),
                EmploymentTypeEnum.PART_TIMER.getCode(),
                EmploymentTypeEnum.CONSULTANT.getCode());
        // 如果假期绑定的用工类型为空，就是全部的用户类型
        List<String> targetEmployeeTypeList = StringUtils.isBlank(employeeType)
                ? defaultEmployeeType : employeeTypeList;
        log.info("国家假期配置：性别：{}，部门：{}，员工类型：{}", useSex, deptIds, employeeTypeList);
        if (StringUtils.isBlank(deptIds)
                && targetEmployeeTypeList.contains(userInfoDO.getEmployeeType())
                && (ObjectUtil.isNotNull(useSex)
                && (useSex == 0 || useSex.equals(userInfoDO.getSex())))
                && (this.checkInEntryDate(startEntryDate, endEntryDate, userInfoDO))) {
            log.info("部门范围为空，用户：{}，满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户绑定该假期范围表",
                    userInfoDO.getUserCode(), userInfoDO.getLocationCountry(),
                    leaveConfig.getId(), leaveConfig.getLeaveType());
            return true;
        }
        if (StringUtils.isNotBlank(deptIds)) {
            String[] split = deptIds.split(",");
            if (split.length > 0) {
                log.info("假期id：{}，假期类型：{}，部门范围：{}",
                        leaveConfig.getId(), leaveConfig.getLeaveType(), deptIds);
                List<Long> deptIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
                if (deptIdList.contains(userInfoDO.getDeptId())
                        && targetEmployeeTypeList.contains(userInfoDO.getEmployeeType())
                        && (ObjectUtil.isNotNull(useSex)
                        && (useSex == 0 || useSex.equals(userInfoDO.getSex())))
                        && (this.checkInEntryDate(startEntryDate, endEntryDate, userInfoDO))) {
                    log.info("部门范围不为空，用户：{}，满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户绑定该假期范围表",
                            userInfoDO.getUserCode(), userInfoDO.getLocationCountry(), leaveConfig.getId(), leaveConfig.getLeaveType());
                    return true;
                }
            }
        }
        log.info("部门范围不为空，用户：{}，不满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户不绑定该假期范围表",
                userInfoDO.getUserCode(), userInfoDO.getLocationCountry(), leaveConfig.getId(), leaveConfig.getLeaveType());
        return false;
    }

    @Override
    public boolean checkInCondition(HrmsCompanyLeaveConfigDO leaveConfig, AttendanceUser userInfo) {
        // 重构拷贝保留原先判断逻辑，代码未做更改
        Integer useSex = leaveConfig.getUseSex();
        String deptIds = leaveConfig.getDeptIds();
        String employeeType = leaveConfig.getEmployeeType();
        String startEntryDate = leaveConfig.getStartEntryDate();
        String endEntryDate = leaveConfig.getEndEntryDate();
        List<String> employeeTypeList = Arrays.asList(employeeType.split(","));

        // 默认发放的五个用户类型：如果假期没有选择用工类型，就是全部的用工类型，就是下面的五个
        List<String> defaultEmployeeType = Arrays.asList(EmploymentTypeEnum.EMPLOYEE.getCode(),
                EmploymentTypeEnum.SUB_EMPLOYEE.getCode(),
                EmploymentTypeEnum.INTERN.getCode(),
                EmploymentTypeEnum.PART_TIMER.getCode(),
                EmploymentTypeEnum.CONSULTANT.getCode());
        // 如果假期绑定的用工类型为空，就是全部的用户类型
        List<String> targetEmployeeTypeList = StringUtils.isBlank(employeeType)
                ? defaultEmployeeType : employeeTypeList;
        log.info("国家假期配置：性别：{}，部门：{}，员工类型：{}", useSex, deptIds, employeeTypeList);
        if (StringUtils.isBlank(deptIds)
                && targetEmployeeTypeList.contains(userInfo.getEmployeeType())
                && (ObjectUtil.isNotNull(useSex)
                && (useSex == 0 || useSex.equals(userInfo.getSex())))
                && (this.checkInEntryDate(startEntryDate, endEntryDate, userInfo))) {
            log.info("部门范围为空，用户：{}，满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户绑定该假期范围表",
                    userInfo.getUserCode(), userInfo.getLocationCountry(),
                    leaveConfig.getId(), leaveConfig.getLeaveType());
            return true;
        }
        if (StringUtils.isNotBlank(deptIds)) {
            String[] split = deptIds.split(",");
            if (split.length > 0) {
                log.info("假期id：{}，假期类型：{}，部门范围：{}",
                        leaveConfig.getId(), leaveConfig.getLeaveType(), deptIds);
                List<Long> deptIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
                if (deptIdList.contains(userInfo.getDeptId())
                        && targetEmployeeTypeList.contains(userInfo.getEmployeeType())
                        && (ObjectUtil.isNotNull(useSex)
                        && (useSex == 0 || useSex.equals(userInfo.getSex())))
                        && (this.checkInEntryDate(startEntryDate, endEntryDate, userInfo))) {
                    log.info("部门范围不为空，用户：{}，满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户绑定该假期范围表",
                            userInfo.getUserCode(), userInfo.getLocationCountry(), leaveConfig.getId(), leaveConfig.getLeaveType());
                    return true;
                }
            }
        }
        log.info("部门范围不为空，用户：{}，不满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户不绑定该假期范围表",
                userInfo.getUserCode(), userInfo.getLocationCountry(), leaveConfig.getId(), leaveConfig.getLeaveType());
        return false;
    }

    private Boolean checkInEntryDate(String startEntryDateStr,
                                     String endEntryDateStr,
                                     HrmsUserInfoDO userInfo) {
        if (StringUtils.isBlank(startEntryDateStr) || StringUtils.isBlank(endEntryDateStr)) {
            return true;
        }
        // 获取人员入职信息
        HrmsUserEntryRecordDO userEntryInfo = userEntryRecordManage.getByUserId(userInfo.getId());
        if (Objects.isNull(userEntryInfo) || Objects.isNull(userEntryInfo.getConfirmDate())) {
            log.info("userCode：{}，未获取到人员入职信息", userInfo.getUserCode());
            return false;
        }
        Date startEntryDate = DateUtils.str2Date(startEntryDateStr);
        Date endEntryDate = DateUtils.str2Date(endEntryDateStr);
        if (userEntryInfo.getConfirmDate().compareTo(startEntryDate) >= 0
                && userEntryInfo.getConfirmDate().compareTo(endEntryDate) <= 0) {
            return true;
        }
        return false;
    }

    private Boolean checkInEntryDate(String startEntryDateStr,
                                     String endEntryDateStr,
                                     AttendanceUser userInfo) {
        if (StringUtils.isBlank(startEntryDateStr) || StringUtils.isBlank(endEntryDateStr)) {
            return true;
        }
        // 获取人员入职信息
        HrmsUserEntryRecordDO userEntryInfo = userEntryRecordManage.getByUserId(userInfo.getId());
        if (Objects.isNull(userEntryInfo) || Objects.isNull(userEntryInfo.getConfirmDate())) {
            log.info("userCode：{}，未获取到人员入职信息", userInfo.getUserCode());
            return false;
        }
        Date startEntryDate = DateUtils.str2Date(startEntryDateStr);
        Date endEntryDate = DateUtils.str2Date(endEntryDateStr);
        if (userEntryInfo.getConfirmDate().compareTo(startEntryDate) >= 0
                && userEntryInfo.getConfirmDate().compareTo(endEntryDate) <= 0) {
            return true;
        }
        return false;
    }

    /**
     * 构建福利假期配置范围表
     *
     * @param leaveConfig         福利假期配置
     * @param rangeType           范围类型：1-用户级别，2-部门级别
     * @param userInfo            部门用户
     * @param leaveConfigRangList 福利假期配置范围表
     */
    private void buildLeaveConfigRang(HrmsCompanyLeaveConfigDO leaveConfig, int rangeType, HrmsUserInfoDO userInfo, List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangList) {
        HrmsCompanyLeaveConfigRangDO leaveConfigRang = new HrmsCompanyLeaveConfigRangDO();
        leaveConfigRang.setId(iHrmsIdWorker.nextId());
        leaveConfigRang.setLeaveId(leaveConfig.getId());
        leaveConfigRang.setRangeType(rangeType);
        leaveConfigRang.setUserCode(userInfo.getUserCode());
        BaseDOUtil.fillDOInsert(leaveConfigRang);
        leaveConfigRangList.add(leaveConfigRang);
    }

    /**
     * 校验入参
     *
     * @param param 入参
     */
    private void checkWelfareLeaveConfigParam(WelfareLeaveConfigSaveParam param) {
        if (ObjectUtil.isNull(param)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
        }
        // 主表字段校验
        if (ObjectUtil.equal(param.getIsUploadAttachment(), 1)) {
            if (ObjectUtil.isNull(param.getUploadAttachmentCondition())) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_LEAVE_CONDITION_ERROR);
            }
        }
        // 发放范围校验
        if (BusinessConstant.Y.equals(param.getIsDispatch()) && CollectionUtils.isEmpty(param.getDispatchCountry())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_LEAVE_DISPATCH_COUNTRY_ERROR);
        }

        // 发放规则校验
        if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
        }
        // 如果发放频次是一次性发放，那么发放时间必须是员工入职日，并且没有发放日期
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueFrequency(), 2)) {
            if (!ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueTime(), 3)) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
            }
            if (ObjectUtil.isNotNull(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth())
                    && param.getLeaveConfigIssueRuleSaveParam().getIssueMonth() != 0) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
            }
            if (ObjectUtil.isNotNull(param.getLeaveConfigIssueRuleSaveParam().getIssueDay())
                    && param.getLeaveConfigIssueRuleSaveParam().getIssueDay() != 0) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
            }
        }
        // 发放频次是周期性发放,如果发放时间是每年固定日，那么发放日期的月份不能为null，发放日期的日不能为null，如果发放时间是每月固定日，那么发放日期的月份必须为null，发放日期的日不能为null
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueFrequency(), 1)) {
            if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueTime(), 1)) {
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth())
                        || Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
                }
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueDay())
                        || Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueDay(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
                }
            }
            if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueTime(), 2)) {
                if (ObjectUtil.isNotNull(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth())
                        && !Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
                }
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueDay())
                        || Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueDay(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
                }
            }
        }

        // 如果额度类型是司领递增，则范围数据必填。其他的额度类型，阶段信息必填，如果是固定额度，那么发放额度必填。
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueType(), 2)) {
            if (CollectionUtils.isEmpty(param.getLeaveConfigIssueRuleSaveParam().getLeaveConfigIssueRuleRangeList())) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
            }
        } else {
            if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueType(), 1)) {
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueQuota())) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
                }
            }
        }

        // 结转规则校验
        if (ObjectUtil.isNull(param.getLeaveConfigCarryOverSaveParam())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
        }
        // 如果假期是否永久有效为否时，那么有效期必填，更新周期必填
        if (ObjectUtil.equal(param.getLeaveConfigCarryOverSaveParam().getIsInvalid(), 2)) {
            if (ObjectUtil.isNull(param.getUseCycle()) || ObjectUtil.isNull(param.getUseStartDate()) || ObjectUtil.isNull(param.getUseEndDate())) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
            }
        }
    }

    /**
     * 校验导入入参
     *
     * @param importParams 入参
     */
    private List<WelfareLeaveConfigImportParam> checkWelfareLeaveConfigImportParam(List<WelfareLeaveConfigImportParam> importParams, List<WelfareLeaveConfigImportParam> failImportList) {
        List<WelfareLeaveConfigImportParam> checkSuccessList = Lists.newArrayList();
        out:
        for (WelfareLeaveConfigImportParam param : importParams) {
            if (ObjectUtil.isNull(param)) {
                setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                continue;
            }

            if (StringUtils.isNotBlank(param.getEmployeeType())) {
                List<String> employeeTypeList = Arrays.asList(param.getEmployeeType().split(","));
                if (CollectionUtils.isNotEmpty(employeeTypeList)) {
                    for (String type : employeeTypeList) {
                        if (!EmploymentTypeEnum.TYPE_OF_DEFAULT_WELFARE_LEAVE.contains(type)) {
                            setFailInfo(param, failImportList, HrmsErrorCodeEnums.EMPLOYEE_TYPE_ERROR);
                            continue out;
                        }
                    }
                }
            }

            if (ObjectUtil.equal(param.getIsUploadAttachment(), 1)) {
                if (ObjectUtil.isNull(param.getUploadAttachmentCondition())) {
                    setFailInfo(param, failImportList, HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_LEAVE_CONDITION_ERROR);
                    continue;
                }
            }

            // 如果发放频次是一次性发放，那么发放时间必须是员工入职日，并且没有发放日期
            if (ObjectUtil.equal(param.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())) {
                if (!ObjectUtil.equal(param.getIssueTime(), LeaveConfigIssueTimeEnum.EMPLOYEE_ONBOARDING_DAY.getType())) {
                    setFailInfo(param, failImportList, HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_ONE_TIME_ISSUANCE_ERROR);
                    continue;
                }
                if (ObjectUtil.isNotNull(param.getIssueMonth())
                        && param.getIssueMonth() != BusinessConstant.ZERO) {
                    setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                    continue;
                }
                if (ObjectUtil.isNotNull(param.getIssueDay())
                        && param.getIssueDay() != BusinessConstant.ZERO) {
                    setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                    continue;
                }
            }
            // 如果发放频次是周期性发放，那么发放时间必须是每年固定日或者每月固定日，如果发放时间是每年固定日，那么发放日期的月份不能为null，发放日期的日不能为null，如果发放时间是每月固定日，那么发放日期的月份必须为null，发放日期的日不能为null
            if (ObjectUtil.equal(param.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType())) {
                if (!ObjectUtil.equal(param.getIssueTime(), LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType())
                        && !ObjectUtil.equal(param.getIssueTime(), LeaveConfigIssueTimeEnum.FIXED_DAY_OF_THE_MONTH.getType())) {
                    setFailInfo(param, failImportList, HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_PERIODICAL_ISSUANCE_ERROR);
                    continue;
                }
                if (ObjectUtil.equal(param.getIssueTime(), LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType())) {
                    if (ObjectUtil.isNull(param.getIssueMonth())) {
                        setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                        continue;
                    }
                    if (ObjectUtil.isNull(param.getIssueDay())) {
                        setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                        continue;
                    }
                }
                if (ObjectUtil.equal(param.getIssueTime(), LeaveConfigIssueTimeEnum.FIXED_DAY_OF_THE_MONTH.getType())) {
                    if (ObjectUtil.isNotNull(param.getIssueMonth())) {
                        setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                        continue;
                    }
                    if (ObjectUtil.isNull(param.getIssueDay())) {
                        setFailInfo(param, failImportList, HrmsErrorCodeEnums.PARAM_NOT_NULL);
                        continue;
                    }
                }
            }

            // 如果额度类型是司领递增，则范围数据必填。其他的额度类型，阶段信息必填，如果是固定额度，那么发放额度必填。
            if (ObjectUtil.equal(param.getIssueType(), LeaveConfigIssueTypeEnum.INCREASING_NUMBER_OF_LEADERS.getType())) {
                if (StringUtils.isEmpty(param.getLeaveConfigIssueRuleRange())) {
                    setFailInfo(param, failImportList, HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_INCREASING_NUMBER_OF_LEADERS_ERROR);
                    continue;
                }
            } else {
                if (ObjectUtil.equal(param.getIssueType(), LeaveConfigIssueTypeEnum.FIXED_AMOUNT.getType())) {
                    if (ObjectUtil.isNull(param.getIssueQuota())) {
                        setFailInfo(param, failImportList, HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_FIXED_AMOUNT_ERROR);
                        continue;
                    }
                }
            }

            // 如果假期是否永久有效为否时，那么有效期必填，更新周期必填，假期是否可结转一定是 是
//            if (ObjectUtil.equal(param.getIsInvalid(), LeaveConfigIsInvalidEnum.NO.getType())) {
//                if (ObjectUtil.equal(param.getIsCarryOver(), LeaveConfigIsCarryOverEnum.NO.getType())) {
//                    setFailInfo(param,failImportList, HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_LEAVE_CONFIG_INVALID_ERROR);
//                    continue;
//                }
//            }
            checkSuccessList.add(param);
        }
        return checkSuccessList;
    }

    private void setLeaveConfigRange(WelfareLeaveConfigSaveParam param, HrmsCompanyLeaveConfigDO leaveConfig, List<HrmsCompanyLeaveConfigRangDO> leaveConfigRangList) {
        // 根据userIdList查询用户信息
        UserDaoQuery userDaoQuery = buildUserQuery();
        if (CollectionUtils.isNotEmpty(param.getEmployeeTypeList())) {
            userDaoQuery.setEmployeeTypes(param.getEmployeeTypeList());
        }
        if (Objects.nonNull(param.getUseSex()) && param.getUseSex() != BusinessConstant.ZERO) {
            userDaoQuery.setSex(param.getUseSex());
        }
        // 如果有员工，只查询员工
        UserDaoQuery userOnlyQuery = buildUserQuery();
        List<HrmsUserInfoDO> userInfoList = Lists.newArrayList();
        List<HrmsUserInfoDO> deptUserInfoList;
        // 设置派遣人员查询条件
        setDispatchUserQuery(userDaoQuery, param);
        setDispatchUserQuery(userOnlyQuery, param);
        // 改动点：如果部门为空，员工不为空，那就只查询员工,并且和用工类型、性别、地区查出来得数据取并集
        if (CollectionUtils.isNotEmpty(param.getUserCodeList()) && CollectionUtils.isEmpty(param.getDeptIdList())) {
            userOnlyQuery.setUserCodes(param.getUserCodeList());
            userInfoList = hrmsUserInfoDao.userList(userOnlyQuery);
            //如果只选了用工类型、性别、地区，也录成dept类型得，这样返回展示员工不会多
            deptUserInfoList = hrmsUserInfoDao.userList(userDaoQuery);
        } else if (CollectionUtils.isNotEmpty(param.getDeptIdList()) && CollectionUtils.isEmpty(param.getUserCodeList())) {
            //如果部门不为空，员工为空，那就只交集部门
            userDaoQuery.setDeptIds(param.getDeptIdList());
            deptUserInfoList = hrmsUserInfoDao.userList(userDaoQuery);
        } else if (CollectionUtils.isEmpty(param.getUserCodeList()) && CollectionUtils.isEmpty(param.getDeptIdList())) {
            //如果部门和员工都为空，那就取所有交集的员工
            deptUserInfoList = hrmsUserInfoDao.userList(userDaoQuery);
        } else {
            //如果部门和员工都不为空，需要取员工和部门的并集
            userDaoQuery.setDeptIds(param.getDeptIdList());
            deptUserInfoList = hrmsUserInfoDao.userList(userDaoQuery);

            userOnlyQuery.setUserCodes(param.getUserCodeList());
            userInfoList = hrmsUserInfoDao.userList(userOnlyQuery);
        }
        // 获取targetDeptUserInfoList的主键id
        List<Long> targetUserInfoIdList = userInfoList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());

        List<HrmsUserInfoDO> targetDeptUserInfoList = Lists.newArrayList();
        // 遍历部门信息，与查出来的员工取并集
        for (HrmsUserInfoDO deptUserInfo : deptUserInfoList) {
            // 如果已经有用户级别的，那么不再添加为部门级别的
            if (targetUserInfoIdList.contains(deptUserInfo.getId())) {
                continue;
            }
            targetDeptUserInfoList.add(deptUserInfo);
        }
        // 过滤不满足入职日期范围人员
        targetUserInfoIdList.addAll(targetDeptUserInfoList
                .stream()
                .map(HrmsUserInfoDO::getId)
                .collect(Collectors.toList()));
        this.filterUserEntryDate(param, targetDeptUserInfoList, userInfoList, targetUserInfoIdList);
        // 构造假期范围数据
        targetDeptUserInfoList.forEach(deptUser -> buildLeaveConfigRang(leaveConfig, LeaveConfigRangRangeTypeEnum.DEPT.getType(), deptUser, leaveConfigRangList));
        userInfoList.forEach(user -> buildLeaveConfigRang(leaveConfig, LeaveConfigRangRangeTypeEnum.USER.getType(), user, leaveConfigRangList));
    }

    private void setFailInfo(WelfareLeaveConfigImportParam param, List<WelfareLeaveConfigImportParam> failImportList, HrmsErrorCodeEnums errorType) {
        IpepUtils.putFail(param, RequestInfoHolder.isChinese() ?
                errorType.getMessage() : errorType.getDesc());
        failImportList.add(param);
    }

    private void transferEmptyLeaveConfigDO(HrmsCompanyLeaveConfigDO leaveConfigDO) {
        if (Objects.isNull(leaveConfigDO.getUseSex())) {
            leaveConfigDO.setUseSex(BusinessConstant.ZERO);
        }

        if (Objects.isNull(leaveConfigDO.getEmployeeType())) {
            leaveConfigDO.setEmployeeType("");
        }

        if (Objects.isNull(leaveConfigDO.getDeptIds())) {
            leaveConfigDO.setDeptIds("");
        }
    }

    private void updateLeaveConfig(HrmsCompanyLeaveConfigDO leaveConfig, WelfareLeaveConfigUpdateParam param) {
        // 判断员工类型
        List<String> employeeTypeList = param.getEmployeeTypeList();
        if (CollectionUtils.isNotEmpty(employeeTypeList)) {
            for (String type : employeeTypeList) {
                if (!EmploymentTypeEnum.TYPE_OF_DEFAULT_WELFARE_LEAVE.contains(type)) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.EMPLOYEE_TYPE_ERROR);
                }
            }
            String employeeType_Str = StringUtils.join(employeeTypeList, ",");
            leaveConfig.setEmployeeType(employeeType_Str);
        } else {
            leaveConfig.setEmployeeType(StringUtils.EMPTY);
        }
        // 更新性别
        if (Objects.nonNull(param.getUseSex())) {
            leaveConfig.setUseSex(param.getUseSex());
        } else {
            leaveConfig.setUseSex(BusinessConstant.ZERO);
        }
        // 更新部门
        List<Long> deptIdList = param.getDeptIdList();
        if (CollectionUtils.isNotEmpty(deptIdList)) {
            String deptId_Str = StringUtils.join(deptIdList, ",");
            leaveConfig.setDeptIds(deptId_Str);
        } else {
            leaveConfig.setDeptIds(StringUtils.EMPTY);
        }
        fillDOUpdate(leaveConfig);
    }

    private UserDaoQuery buildUserQuery() {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .isDelete(IsDeleteEnum.NO.getCode())
                .status(StatusEnum.ACTIVE.getCode())
                // 用工类型默认只有员工、挂靠、实习生、兼职、顾问才发放假期
                .employeeTypes(EmploymentTypeEnum.TYPE_OF_DEFAULT_WELFARE_LEAVE)
                .workStatusList(Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()))
                .build();
        return userDaoQuery;
    }

    private void setDispatchUserQuery(UserDaoQuery userDaoQuery, WelfareLeaveConfigSaveParam param) {
        //如果是派遣假，则通过是否派遣标识+派遣地+国籍 找员工
        if (BusinessConstant.Y.equals(param.getIsDispatch())) {
            userDaoQuery.setIsGlobalRelocation(BusinessConstant.Y);
            // 添加国籍查询条件
            String countryCode = CountryCodeEnum.convert2StandardCountryCode(param.getCountry());
            if (StringUtils.isBlank(countryCode)) {
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_LEAVE_TYPE_ERROR);
            }
            // CHN特殊判断 (人员中国籍存在TW)
            if (CountryCodeEnum.CHN.getStandardCode().equals(countryCode)) {
                userDaoQuery.setCountryCodeList(Arrays.asList(countryCode, "TW", "HK", "MO"));
            } else {
                userDaoQuery.setCountryCodeList(Arrays.asList(countryCode));
            }
            if (CollectionUtils.isNotEmpty(param.getDispatchCountry()) && !param.getDispatchCountry().contains(BusinessConstant.GLOBAL_FLAG)) {
                //需要派遣地条件
                userDaoQuery.setLocationCountryList(param.getDispatchCountry());
            }
        } else {
            userDaoQuery.setLocationCountry(param.getCountry());
            userDaoQuery.setIsGlobalRelocation(BusinessConstant.N);
        }
    }

    private void filterUserEntryDate(WelfareLeaveConfigSaveParam param
            , List<HrmsUserInfoDO> deptUserInfoList, List<HrmsUserInfoDO> userInfoList
            , List<Long> userIdList) {
        // 过滤入职日期范围为空及人员范围为空的情况
        if (StringUtils.isBlank(param.getStartEntryDate()) || StringUtils.isBlank(param.getEndEntryDate())) {
            return;
        }
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        Date startEntryDate = DateUtils.str2Date(param.getStartEntryDate());
        Date endEntryDate = DateUtils.str2Date(param.getEndEntryDate());
        // 查询人员入职日期
        List<HrmsUserEntryRecordDO> userEntryRecordList = userEntryRecordManage.selectUserEntryByUserIds(userIdList);
        if (CollectionUtils.isEmpty(userEntryRecordList)) {
            return;
        }
        // 过滤出入职日期范围内的员工
        List<Long> userIdsForCondition = userEntryRecordList.stream()
                .filter(item -> Objects.nonNull(item.getUserId())
                        && Objects.nonNull(item.getConfirmDate())
                        && item.getConfirmDate().compareTo(startEntryDate) >= 0
                        && item.getConfirmDate().compareTo(endEntryDate) <= 0)
                .map(item -> item.getUserId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdsForCondition)) {
            deptUserInfoList.clear();
            userInfoList.clear();
            return;
        }
        deptUserInfoList.removeIf(item -> !userIdsForCondition.contains(item.getId()));
        userInfoList.removeIf(item -> !userIdsForCondition.contains(item.getId()));
    }
}
