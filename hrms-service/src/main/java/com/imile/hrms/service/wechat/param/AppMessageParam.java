package com.imile.hrms.service.wechat.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description: 应用消息入参(避免jar冲突 ， 暂时自己定义参数)
 * @Author: han.wang
 * @Date: 2024/12/09 14:50
 * @Version:1.0
 */
@Data
public class AppMessageParam {

    /**
     * 认证用入参（必传）
     * 应用id（使用方提供appId）
     */
    private String appId;

    /**
     * 认证用入参（必传）
     * secret（可在iTop手动生成,详情见附录《手动生成secret》）
     */
    private String secret;

    /**
     * 消息类型（必传）
     * 支持 markdown、 file、 image、 text
     */
    @NotBlank(message = "消息类型不能为空")
    private String msgtype;

    /**
     * 发送人（必传）
     */
    @Size(min = 1, message = "发送人不能为空")
    private List<String> toUser;

    /**
     * 应用id（必传）
     */
    @NotBlank(message = "应用id不能为空")
    private String agentId;

    /**
     * 发送内容（必传）
     * markdown传map的字符串，其他传base64
     */
    @NotBlank(message = "发送内容不能为空")
    private String content;

    /**
     * 文件名称（markdown、text不需要设置该参数，该类型需要带上文件后缀名，eg：xxx.png）
     */
    private String fileName;

    /**
     * 是否需要模板（只有markdown消息类型才需要传，并且是在需要模板的情况下）
     */
    private Boolean needTemplate = false;

    /**
     * 模板名称（只有markdown消息类型需要模板的情况下才需要传）
     */
    private String templateName;

    /**
     * 表示是否开启重复消息检查
     * 0表示否，1表示是，默认0
     */
    private Integer enableDuplicateCheck = 0;

    /**
     * 表示是否重复消息检查的时间间隔
     * 默认1800s，最大不超过4小时
     */
    private Integer duplicateCheckinterval = 1800;

    /**
     * 表示是否是保密消息（markdown消息类型不需要传）
     * 0表示否，1表示是，默认0
     */
    private Integer safe = 0;
}
