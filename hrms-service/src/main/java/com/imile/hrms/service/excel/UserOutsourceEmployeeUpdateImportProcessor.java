package com.imile.hrms.service.excel;

import com.google.common.collect.Lists;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.user.UserIdentityTypeEnum;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.excel.field.UserImportFieldEnum;
import com.imile.hrms.service.excel.field.UserUpdate4OutsourcingEmployeeImportFieldEnum;
import com.imile.hrms.service.log.component.OperationFieldDiffer;
import com.imile.hrms.service.refactor.user.param.UserDifferParam;
import com.imile.hrms.service.refactor.user.param.UserOutsourceEmployeeUpdateImportParam;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.context.UserImportGlobalContext;
import com.imile.hrms.service.user.context.UserImportTempContext;
import com.imile.hrms.service.user.helper.ExternalDependencyHelper;
import com.imile.hrms.service.user.helper.UserHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/26
 */
@Slf4j
@Service
public class UserOutsourceEmployeeUpdateImportProcessor extends UserBaseImportProcessor<UserOutsourceEmployeeUpdateImportParam> {

    @Resource
    private UserManage userManage;
    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private PostManage postManage;
    @Resource
    private UserService userService;
    @Resource
    private ExternalDependencyHelper externalDependencyHelper;
    @Resource
    private LogRecord logRecord;

    @Override
    protected UserImportGlobalContext buildGlobalContext(List<UserOutsourceEmployeeUpdateImportParam> dataList) {
        UserImportGlobalContext context = new UserImportGlobalContext();

        List<String> duplicatedUserCodeList = dataList.stream()
                .filter(param -> StringUtils.isNotBlank(param.getUserCode()))
                .collect(Collectors.groupingBy(UserOutsourceEmployeeUpdateImportParam::getUserCode, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        context.setDuplicatedUserCodeList(duplicatedUserCodeList);

        List<String> userCodeList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getUserCode);
        context.setUserMap(userManage.getUserMapByCode(userCodeList));

        List<HrmsUserInfoDO> userList = Lists.newArrayList(context.getUserMap().values());

        List<Long> originOcDeptIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getOcId);
        context.setOriginOcDeptMap(deptManage.getDeptMap(originOcDeptIdList));

        List<String> ocNameList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getOcName);
        context.setOcName2DeptListMap(super.getOcName2DeptListMap(ocNameList));

        List<String> projectCodeList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getProjectCode);
        context.setProjectMap(externalDependencyHelper.getProjectMap(projectCodeList));

        List<Long> postIdList = super.getIdList(dataList, UserOutsourceEmployeeUpdateImportParam::getPostId);
        Map<Long, PostDO> postMap = postManage.getPostMap(postIdList);
        context.setPostMap(postMap.entrySet().stream()
                .collect(Collectors.toMap(s -> s.getKey().toString(), Map.Entry::getValue)));

        List<String> leaderCodeList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getLeaderCode);
        context.setLeaderMap(userManage.getUserMapByCode(leaderCodeList));

        List<String> vendorCodeList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getVendorCode);
        context.setVendorMap(externalDependencyHelper.getVendorMap(vendorCodeList));

        List<String> countryNameList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getLocationCountry);
        List<String> provinceNameList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getLocationProvince);
        List<String> cityNameList = CommonUtils.getFieldValueList(dataList, UserOutsourceEmployeeUpdateImportParam::getLocationCity);
        context.setZoneKey2IdMap(super.getZoneKey2IdMap(countryNameList, provinceNameList, cityNameList));
        return context;
    }

    @Override
    protected UserImportTempContext buildTempContext(UserOutsourceEmployeeUpdateImportParam data,
                                                     UserImportGlobalContext globalContext) {
        UserImportTempContext context = new UserImportTempContext();
        HrmsUserInfoDO originUser = globalContext.getUserMap().get(data.getUserCode());
        if (Objects.isNull(originUser)) {
            return context;
        }
        context.setOriginUser(originUser);
        context.setOriginOcDept(globalContext.getOriginOcDeptMap().get(originUser.getOcId()));
        context.setImportFields(UserUpdate4OutsourcingEmployeeImportFieldEnum.values());
        HrmsUserInfoDO user = new HrmsUserInfoDO();
        user.setId(originUser.getId());
        context.setUser(user);
        return context;
    }

    @Override
    protected String checkFailFastField(UserOutsourceEmployeeUpdateImportParam data, UserImportGlobalContext globalContext) {
        if (StringUtils.isBlank(data.getUserCode())) {
            return super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_REQUIRED,
                    UserUpdate4OutsourcingEmployeeImportFieldEnum.USER_CODE.getDesc());
        }
        if (globalContext.getDuplicatedUserCodeList().contains(data.getUserCode())) {
            return super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_VALUE_DUPLICATED,
                    UserUpdate4OutsourcingEmployeeImportFieldEnum.USER_CODE.getDesc(), data.getUserCode());
        }
        HrmsUserInfoDO user = globalContext.getUserMap().get(data.getUserCode());
        if (Objects.isNull(user)) {
            return super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_NOT_EXIST,
                    UserUpdate4OutsourcingEmployeeImportFieldEnum.USER_CODE.getDesc(), data.getUserCode());
        }
        if (!WorkStatusEnum.ON_JOB.getCode().equals(user.getWorkStatus())) {
            return super.getErrorTip(HrmsErrorCodeEnums.USER_WORK_STATUS_UNAVAILABLE, user.getUserCode());
        }
        if (WhetherEnum.YES.getKey().equals(user.getIsDriver())) {
            return super.getErrorTip(HrmsErrorCodeEnums.USER_IDENTITY_TYPE_UNAVAILABLE,
                    UserIdentityTypeEnum.DRIVER.getDesc());
        }
        if (!EmploymentTypeEnum.TYPE_OF_OS.contains(user.getEmployeeType())) {
            return super.getErrorTip(HrmsErrorCodeEnums.USER_EMPLOYEE_TYPE_UNAVAILABLE,
                    EmploymentTypeEnum.descOfCode(user.getEmployeeType()));
        }
        return "";
    }

    @Override
    protected List<String> checkFailSafeField(UserOutsourceEmployeeUpdateImportParam data,
                                              UserImportGlobalContext globalContext,
                                              UserImportTempContext tempContext) {
        List<String> errorTipList = Lists.newArrayList();
        // 校验姓名全称
        super.doFieldLengthCheck(UserImportFieldEnum.USER_NAME, data.getUserName(),
                50, tempContext, errorTipList);
        // 校验网点
        super.doStationCheck(data.getOcName(), globalContext, tempContext, errorTipList);
        // 校验项目
        super.doFieldDataExistCheck(UserImportFieldEnum.PROJECT_CODE, data.getProjectCode(),
                data.getProjectCode(), globalContext.getProjectMap(), tempContext, errorTipList);
        // 校验岗位
        super.doFieldDataExistCheck(UserImportFieldEnum.POST_ID, data.getPostId(),
                data.getPostId(), globalContext.getPostMap(), tempContext, errorTipList);
        // 校验汇报上级
        super.doFieldDataExistCheck(UserImportFieldEnum.LEADER_CODE, data.getLeaderCode(),
                data.getLeaderCode(), globalContext.getLeaderMap(), tempContext, errorTipList);
        // 校验供应商
        super.doFieldDataExistCheck(UserImportFieldEnum.VENDOR_CODE, data.getVendorCode(),
                data.getVendorCode(), globalContext.getVendorMap(), tempContext, errorTipList);
        // 校验常驻地
        super.doLocationCheck(data.getLocationCountry(), data.getLocationProvince(), data.getLocationCity(),
                globalContext, tempContext, errorTipList);
        return errorTipList;
    }

    @Override
    protected void processData(UserOutsourceEmployeeUpdateImportParam data, UserImportTempContext tempContext) {
        // 数据入库并记录操作日志
        userManage.doUpdateImportSave(tempContext.getUser(), null, null, null, null, null);
        logRecord.diffObj(tempContext.getUser(), tempContext.getOriginUser(),
                OperationTypeEnum.EMPLOYEE_OUTSOURCING_IMPORT_UPDATE.getCode());
        List<OperationFieldDiffer> fieldDiffList = UserHelper.saveLog(UserDifferParam.builder()
                .id(tempContext.getOriginUser().getId())
                .operationCode(OperationCodeEnum.HRMS_OUTSOURCE_EMPLOYEE_IMPORT_UPDATE)
                .beforeUser(tempContext.getOriginUser())
                .afterUser(tempContext.getUser())
                .build());
        // 发送数据变动通知
        userService.doUserBaseInfoChangeNotice(tempContext.getOriginUser(), fieldDiffList);
    }
}
