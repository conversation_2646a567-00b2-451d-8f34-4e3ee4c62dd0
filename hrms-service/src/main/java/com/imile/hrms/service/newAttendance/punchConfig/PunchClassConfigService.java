package com.imile.hrms.service.newAttendance.punchConfig;

import cn.hutool.core.date.DateUtil;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchClassConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchConfigDao;
import com.imile.hrms.common.util.PunchTimeCalculator;
import com.imile.hrms.service.newAttendance.punchConfig.helper.PunchTimeHelper;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassItemConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigDO;
import com.imile.hrms.service.approval.dto.DayNormalPunchTimeDTO;
import com.imile.hrms.service.approval.dto.DayPunchTimeDTO;
import com.imile.hrms.common.util.DateHelper;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchClassConfigSelectDTO;
import com.imile.hrms.service.newAttendance.punchConfig.mapstruct.PunchConfigMapstruct;
import com.imile.hrms.service.newAttendance.punchConfig.query.PunchClassNameQuery;
import com.imile.util.date.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/2/16
 * @Description
 */
@Slf4j
@Service
public class PunchClassConfigService {

    @Autowired
    private PunchConfigDao punchConfigDao;
    @Autowired
    private PunchClassConfigDao classConfigDao;

    /**
     * 班次列表查询
     */
    public List<PunchClassConfigSelectDTO> selectList(PunchClassNameQuery query) {
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.listByConfigNo(query.getConfigNo());
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.DATA_NOT_EXITS);
        }
        List<PunchClassConfigDO> punchClassConfigDOList = classConfigDao.listPunchClassConfigByConfigId(
                punchConfigDOList.get(0).getId());
        return PunchConfigMapstruct.INSTANCE.toPunchClassConfigSelectDTO(punchClassConfigDOList);
    }


    /**
     * 获取用户指定天的打卡规则的最早开始最晚截止时间(改天必须是有班次，不能是PH/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     */
    public DayPunchTimeDTO getUserPunchDayTime(Long dayId, List<PunchClassItemConfigDO> itemConfigDOList) {
        if (dayId == null || CollectionUtils.isEmpty(itemConfigDOList) ||
                itemConfigDOList.get(0).notHasValidPunchClassId()) {
            return null;
        }
        // 获取排序后的配置列表
        List<PunchClassItemConfigDO> sortedConfigs = PunchTimeHelper.getSortedConfigs(itemConfigDOList);
        PunchClassItemConfigDO firstConfig = PunchTimeHelper.getFirstConfig(sortedConfigs);
        PunchClassItemConfigDO lastConfig = PunchTimeHelper.getLastConfig(sortedConfigs);
        // 检查配置是否有效（是否包含必要的时间字段）
        if (firstConfig.notHasValidTimes() || lastConfig.notHasValidTimes()) {
            return null;
        }
        //获取用户考勤日的开始截止时间
        DayPunchTimeDTO dayPunchTimeDTO = new DayPunchTimeDTO();
        Date dayDate = DateHelper.transferDayIdToDate(dayId);

        // 判断是否跨天并计算时间
        boolean isPunchInAcrossDay = firstConfig.isPunchInAcrossDay();

        // 设置当日最早打卡时间
        dayPunchTimeDTO.setDayPunchStartTime(
                firstConfig.calculateEarliestPunchInDateTime(dayDate, isPunchInAcrossDay));

        // 设置当日最晚打卡时间（如果上班不跨天，则下班通常跨天）
        dayPunchTimeDTO.setDayPunchEndTime(
                lastConfig.calculateLatestPunchOutDateTime(dayDate, isPunchInAcrossDay));

        dayPunchTimeDTO.logTimePeriod("getUserPunchDayTime");
        return dayPunchTimeDTO;
    }

    /**
     * 获取用户指定天的打卡规则的最早开始最晚截止时间(改天必须是有班次，不能是PH/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     */
    public DayPunchTimeDTO getUserPunchClassItemDayTime(Long dayId,
                                                        Long classItemId,
                                                        List<PunchClassItemConfigDO> itemConfigDOList) {
        if (dayId == null || classItemId == null ||
                CollectionUtils.isEmpty(itemConfigDOList) ||
                itemConfigDOList.get(0).notHasValidPunchClassId()) {
            return null;
        }

        //2.查找并验证目标班次配置
        PunchClassItemConfigDO itemConfigDO = itemConfigDOList.stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .filter(item -> item.getId().equals(classItemId))
                .findFirst()
                .orElse(null);

        if (itemConfigDO == null || itemConfigDO.notHasValidTimes()) {
            return null;
        }

        // 3. 获取整天的排班时间
        DayPunchTimeDTO dayPunchTimeDTO = getUserPunchDayTime(dayId, itemConfigDOList);
        if (dayPunchTimeDTO == null) {
            return null;
        }

        // 4. 根据班次是否跨天，计算打卡时间范围
        DayPunchTimeDTO result;

        if (itemConfigDO.isTimeCrossingDay()) {
            // 跨天情况
            result = DayPunchTimeDTO.create(
                    PunchTimeCalculator.getDateFromDateAndTime(dayPunchTimeDTO.getDayPunchStartTime(), itemConfigDO.getEarliestPunchInTime()),
                    PunchTimeCalculator.getDateFromDateAndTime(dayPunchTimeDTO.getDayPunchEndTime(), itemConfigDO.getLatestPunchOutTime())
            );
        } else {
            // 非跨天情况，需确定是起始日还是结束日
            Date dayPunchStartConvertTime = PunchTimeCalculator.convertToStandardTimeFormat(dayPunchTimeDTO.getDayPunchStartTime());

            if (itemConfigDO.getEarliestPunchInTime().compareTo(dayPunchStartConvertTime) > -1) {
                // 属于起始那天
                result = DayPunchTimeDTO.calculatePunchTimeFrame(dayPunchTimeDTO.getDayPunchStartTime(), itemConfigDO);
            } else {
                // 属于结束那天
                result = DayPunchTimeDTO.calculatePunchTimeFrame(dayPunchTimeDTO.getDayPunchEndTime(), itemConfigDO);
            }
        }

        // 5. 记录日志
        if (null != result) {
            result.logTimePeriod("getUserPunchClassItemDayTime");
        }
        return result;
    }


    /**
     * 获取用户指定天的打卡规则的正常上下班时间(改天必须是有班次，不能是PH/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     */
    public DayNormalPunchTimeDTO getUserPunchClassNormalItemDayTime(Long dayId,
                                                                    Long classItemId,
                                                                    List<PunchClassItemConfigDO> itemConfigDOList) {
        // 输入参数日志记录
        log.info("getUserPunchClassNormalItemDayTime - 开始处理 dayId:{}, classItemId:{}, itemConfigSize:{}",
                dayId, classItemId, itemConfigDOList != null ? itemConfigDOList.size() : 0);
        if (dayId == null || classItemId == null ||
                CollectionUtils.isEmpty(itemConfigDOList) ||
                itemConfigDOList.get(0).notHasValidPunchClassId()) {
            log.info("getUserPunchClassNormalItemDayTime - 参数无效: dayId:{}, classItemId:{}, 班次配置列表为空或无效",
                    dayId, classItemId);
            return null;
        }
        // 排序班次配置
        itemConfigDOList = itemConfigDOList.stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .collect(Collectors.toList());

        // 查找目标班次配置
        PunchClassItemConfigDO targetItemConfig = itemConfigDOList.stream()
                .filter(item -> item.matchesId(classItemId))
                .findFirst()
                .orElse(null);
        if (targetItemConfig == null) {
            log.info("getUserPunchClassNormalItemDayTime - 未找到目标班次配置，classItemId: {}", classItemId);
            return null;
        }
        // 获取整天的排班起始结束时间
        DayPunchTimeDTO dayPunchTimeDTO = getUserPunchDayTime(dayId, itemConfigDOList);
        if (dayPunchTimeDTO == null) {
            return null;
        }
        log.info("getUserPunchClassNormalItemDayTime - 获取整天排班时间成功，开始时间: {}, 结束时间: {}",
                dayPunchTimeDTO.getDayPunchStartTime(), dayPunchTimeDTO.getDayPunchEndTime());
        // 使用班次配置对象计算具体上下班时间
        return DayNormalPunchTimeDTO.calculateNormalPunchTime(dayPunchTimeDTO, targetItemConfig);
    }

    /**
     * 获取用户自由打卡规则指定天的最早开始最晚截止时间
     */
    public DayPunchTimeDTO getUserFreeWorkPunchClassItemDayTime(String dayId, PunchClassItemConfigDO itemConfigDO) {
        if (StringUtils.isEmpty(dayId)) {
            return null;
        }
        String earliestPunchInTimeString = DateHelper.formatHHMMSS(itemConfigDO.getEarliestPunchInTime());
        String latestPunchOutTimeString = DateHelper.formatHHMMSS(itemConfigDO.getLatestPunchOutTime());
        Date date = DateHelper.transferDayIdToDate(dayId);
        String earliestPunchInTimeDayString = DateHelper.formatYYYYMMDD(date);
        String latestPunchOutTimeDayString;
        if (itemConfigDO.isAcrossDay()) {
            latestPunchOutTimeDayString = DateHelper.formatYYYYMMDD(DateUtil.offsetDay(date, 1));
        } else {
            latestPunchOutTimeDayString = earliestPunchInTimeDayString;
        }

        Date dayPunchStartTime = PunchTimeCalculator.getDateFromDateAndTimeStr(
                earliestPunchInTimeDayString,
                earliestPunchInTimeString
        );
        Date dayPunchEndTime = PunchTimeCalculator.getDateFromDateAndTimeStr(
                latestPunchOutTimeDayString,
                latestPunchOutTimeString
        );
        log.info("getUserFreeWorkPunchClassItemDayTime - 计算结果: 开始时间={}, 结束时间={}",
                dayPunchStartTime, dayPunchEndTime);
        return DayPunchTimeDTO.create(dayPunchStartTime, dayPunchEndTime);
    }
}
