package com.imile.hrms.service.promotion.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/2/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPromotionExportBO {

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 年龄
     */
    private Integer userAge;

    /**
     * 性别
     */
    private String sexDesc;

    /**
     * 最高学历
     */
    private String highestEducationLevel;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 司龄（月）
     */
    private Long seniority;

    /**
     * 绩效结果
     */
    private String performanceResult;

    /**
     * 上一次晋升日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastPromotionDate;

    /**
     * 所属部门
     */
    @RelationObjectValue(beanId = "hrmsDeptNewManageImpl", fieldId = "deptName", extendFieldIdList = "deptNamePath",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private Long deptId;
    private String deptName;
    private String deptNamePath;

    /**
     * 一级部门
     */
    private String firstLevelDeptName;

    /**
     * 二级部门
     */
    private String secondLevelDeptName;

    /**
     * 三级部门
     */
    private String thirdLevelDeptName;

    /**
     * 原岗位
     */
    @RelationObjectValue(beanId = "postManageImpl", fieldId = "postName",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long postId;
    private String postName;

    /**
     * 原职级序列
     */
    private String jobSequence;

    /**
     * 原职级
     */
    private String jobLevel;

    /**
     * 原职等
     */
    private String jobGrade;

    /**
     * 晋升类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PROMOTION_TYPE, ref = "promotionTypeDesc")
    private Integer promotionType;
    private String promotionTypeDesc;

    /**
     * 晋升贡献类型
     */
    private String promotionContributeTypeDesc;

    /**
     * 晋升岗位
     */
    @RelationObjectValue(beanId = "postManageImpl", fieldId = "promotionPostName",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long promotionPostId;
    private String promotionPostName;

    /**
     * 晋升职级序列
     */
    private String promotionJobSequence;

    /**
     * 晋升职级
     */
    private String promotionJobLevel;

    /**
     * 晋升职等
     */
    private String promotionJobGrade;

    /**
     * 晋升状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PROMOTION_STATUS, ref = "promotionStatusDesc")
    private Integer promotionStatus;
    private String promotionStatusDesc;

    /**
     * 申请人
     */
    private String createUserName;

    /**
     * 申请日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date effectDate;

}
