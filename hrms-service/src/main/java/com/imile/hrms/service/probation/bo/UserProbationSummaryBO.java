package com.imile.hrms.service.probation.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 员工试用期总结
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
public class UserProbationSummaryBO {

    private Long id;

    /**
     * 试用期id
     */
    private Long userProbationId;

    /**
     * 试用期总结
     */
    private String probationSummary;

    /**
     * 答辩材料（最多支持五个附件）
     */
    private List<AttachmentDTO> defenceFileList;

    /**
     * 员工对业务导师评分
     */
    private Integer bizMentorScore;

    /**
     * 员工对业务导师评价
     */
    private String bizMentorEvaluate;

    /**
     * 是否提交
     */
    private Integer isSubmit;

    /**
     * 最后修改日期(如果提交状态就是提交日期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastUpdDate;
}
