package com.imile.hrms.service.attendance.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description: 派遣消息参数
 * @author: han.wang
 * @createDate: 2024-12-16
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDispatchUserRecordParam {

    /**
     * 派遣开始日期
     */
    private Date dispatchDate;

    /**
     * 派遣地国家
     */
    private String dispatchCountry;

    /**
     * 派遣人员编码
     */
    private String userCode;

    /**
     * 国籍编码
     */
    private String countryCode;

    /**
     * 派遣是否结束标志
     */
    private Integer endFlag;

    /**
     * 调动类型
     */
    private Integer transformType;
}
