package com.imile.hrms.service.achievement.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 指标结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
public class AchievementsTargetResultDetailNewDTO {

    private Long targetItemId;

    /**
     * 指标性质01正向 02反向)
     */
    private String targetProperties;

    /**
     * 计算规则01按比例 02按绝对 03手动填写
     */
    private String calculationRuleType;

    /**
     * 完成值来源(0prism 1手动 2运营)
     */
    private String isComplet;

    /**
     * 完成值（整个周期）
     */
    private BigDecimal completionValue;

    /**
     * 完成率（整个周期）
     */
    private BigDecimal completionRate;

    /**
     * 得分（整个周期）
     */
    private BigDecimal score;

    private Integer cycleNum;

    private Integer cycleType;

    private String cycleNumDesc;

    private String cycleNumDescEn;

    /**
     * 累计规则01平均值 02求和
     */
    private String accumulationRuleType;

}
