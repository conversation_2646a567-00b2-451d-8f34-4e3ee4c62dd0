package com.imile.hrms.service.user.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.api.primary.model.component.Field;
import com.imile.hrms.api.primary.model.result.user.UserBankCardDTO;
import com.imile.hrms.api.user.enums.UserPaymentInfoExtendFieldEnum;
import com.imile.hrms.api.user.result.UserWagesCardDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.common.validator.ParamValidator;
import com.imile.hrms.dao.user.dao.UserWagesCardDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.UserWagesCardDO;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.manage.user.UserWagesCardManage;
import com.imile.hrms.mq.param.UserEventParam;
import com.imile.hrms.service.common.MessageSendService;
import com.imile.hrms.service.user.UserWagesCardService;
import com.imile.hrms.service.user.helper.UserCheckHelper;
import com.imile.hrms.service.user.param.UserWagesCardSaveParam;
import com.imile.hrms.service.user.result.UserWagesCardBO;
import com.imile.hrms.service.user.result.UserWagesCardHistoryBO;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
@Slf4j
@Service
public class UserWagesCardServiceImpl implements UserWagesCardService {

    @Resource
    private UserWagesCardDao userWagesCardDao;
    @Resource
    private UserWagesCardManage userWagesCardManage;
    @Resource
    private UserManage userManage;
    @Resource
    private ConverterService converterService;
    @Resource
    private MessageSendService messageSendService;

    @Override
    public DataDifferHolder<UserWagesCardDO> differ(Long userId, List<UserWagesCardSaveParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return DataDifferHolder.emptyHolder();
        }
        List<UserWagesCardDO> existList = userWagesCardManage.getUserWagesCardList(Lists.newArrayList(userId));
        Map<Long, UserWagesCardDO> existMap = existList.stream()
                .collect(Collectors.toMap(UserWagesCardDO::getId, Function.identity()));
        Map<Long, UserWagesCardSaveParam> updateMap = paramList.stream()
                .filter(s -> Objects.nonNull(s.getId()))
                .collect(Collectors.toMap(UserWagesCardSaveParam::getId, Function.identity()));
        // 存量数据中关键字段字段值发生变化的数据移除最新标识
        List<UserWagesCardDO> updateList = existList.stream()
                .filter(item -> this.isWagesCardNeedHistory(item, updateMap.get(item.getId())))
                .map(item -> {
                    UserWagesCardDO record = new UserWagesCardDO();
                    record.setId(item.getId());
                    record.setIsLatest(BusinessConstant.N);
                    BaseDOUtil.fillDOUpdate(record);
                    return record;
                })
                .collect(Collectors.toList());
        // 传参中的id不为空且关键字段字段值未发生变化的数据正常更新
        List<UserWagesCardDO> notImportantFieldUpdateList = paramList.stream()
                .filter(param -> Objects.nonNull(param.getId())
                        && !this.isWagesCardNeedHistory(existMap.get(param.getId()), param))
                .map(param -> {
                    UserWagesCardDO record = BeanUtils.convert(param, UserWagesCardDO.class);
                    BaseDOUtil.fillDOUpdate(record);
                    return record;
                })
                .collect(Collectors.toList());
        updateList.addAll(notImportantFieldUpdateList);
        // 传参中的id为空或id不为空但关键字段字段值发生变化的数据重新插入
        List<UserWagesCardDO> insertList = paramList.stream()
                .filter(param -> Objects.isNull(param.getId())
                        || this.isWagesCardNeedHistory(existMap.get(param.getId()), param))
                .map(param -> {
                    UserWagesCardDO origin = existMap.get(param.getId());
                    UserWagesCardDO record = BeanUtils.convert(param, UserWagesCardDO.class);
                    record.setId(null);
                    record.setUserId(userId);
                    record.setIsLatest(BusinessConstant.Y);
                    // 存量数据变更时，第一次变更originalId取原数据ID，之后变更都跟第一次变更originalId保持一致
                    if (Objects.nonNull(origin)) {
                        record.setOriginalId(Objects.isNull(origin.getOriginalId())
                                ? origin.getId()
                                : origin.getOriginalId());
                    }
                    BaseDOUtil.fillDOInsert(record);
                    return record;
                })
                .collect(Collectors.toList());
        // 存量数据的 id 不在传参中则代表需删除
        List<UserWagesCardDO> deleteList = existList.stream()
                .filter(item -> !updateMap.containsKey(item.getId()))
                .map(item -> {
                    UserWagesCardDO record = new UserWagesCardDO();
                    record.setId(item.getId());
                    record.setIsDelete(IsDeleteEnum.YES.getCode());
                    BaseDOUtil.fillDOUpdate(record);
                    return record;
                })
                .collect(Collectors.toList());
        return DataDifferHolder.buildHolder(insertList, updateList, deleteList);
    }

    @Override
    public Boolean saveUserWagesCard(Long userId, List<UserWagesCardSaveParam> paramList) {
        HrmsUserInfoDO user = userManage.getUserById(userId);
        DataDifferHolder<UserWagesCardDO> userWagesCardDifferHolder = this.differ(user.getId(), paramList);
        userWagesCardManage.doSave(userWagesCardDifferHolder);
        return Boolean.TRUE;
    }

    @Override
    public void doUserPaymentInfoChangeNotice(String userCode) {
        messageSendService.doNotice4UserPaymentInfoChange(UserEventParam.<UserEventParam.Default>builder()
                .userCode(userCode)
                .operator(UcenterUtils.getUserInfo())
                .build());
    }

    @Override
    public List<UserWagesCardBO> getUserWagesCardList(Long userId) {
        List<UserWagesCardDO> userWagesCardList = userWagesCardManage.getUserWagesCardList(Lists.newArrayList(userId));
        return BeanUtils.convert(UserWagesCardBO.class, userWagesCardList);
    }

    @Override
    public List<UserWagesCardHistoryBO> getUserWagesCardHistoryList(Long originalId) {
        List<UserWagesCardDO> userWagesCardList = userWagesCardDao.selectByOriginalId(originalId);
        return BeanUtils.convert(UserWagesCardHistoryBO.class, userWagesCardList);
    }

    @Override
    public List<UserBankCardDTO> getUserBankCardList(String userCode) {
        HrmsUserInfoDO user = userManage.getUserByUserCode(userCode);
        List<UserWagesCardDO> userWagesCardList = userWagesCardManage.getUserWagesCardList(Lists.newArrayList(user.getId()));
        return userWagesCardList.stream()
                .map(card -> {
                    List<Field> extendFieldList
                            = Collections.singletonList(Field.of(UserPaymentInfoExtendFieldEnum.SWIFT_CODE.getKey(), card.getSwiftCode()));
                    return UserBankCardDTO.builder()
                            .id(card.getId())
                            .bankName(card.getWagesCardBank())
                            .bankBranch(card.getWagesCardBankBranch())
                            .accountName(card.getCardholderName())
                            .accountNumber(card.getWagesCardNo())
                            .extendFieldList(extendFieldList)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<UserWagesCardDTO> getUserWagesCardList(String userCode) {
        HrmsUserInfoDO user = userManage.getUserByUserCode(userCode);
        List<UserWagesCardDO> userWagesCardList = userWagesCardManage.getUserWagesCardList(Lists.newArrayList(user.getId()));
        return BeanUtils.convert(UserWagesCardDTO.class, userWagesCardList);
    }

    @Override
    public Map<String, List<UserWagesCardDTO>> getUserWagesCardMap(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyMap();
        }
        ParamValidator.checkSize(userCodeList);
        Map<String, Long> userCode2IdMap = userManage.getUserCodeIdMap(userCodeList);
        List<Long> userIdList = Lists.newArrayList(userCode2IdMap.values());
        List<UserWagesCardDO> userWagesCardList = userWagesCardManage.getUserWagesCardList(userIdList);
        Map<Long, List<UserWagesCardDO>> userIdGroupMap =
                userWagesCardList.stream().collect(Collectors.groupingBy(UserWagesCardDO::getUserId));
        Map<Long, String> userIdCodeMap = userManage.getUserIdCodeMap(userIdList);
        Map<String, List<UserWagesCardDTO>> result = new HashMap<>();
        userIdGroupMap.forEach((key, value) -> {
            String userCode = userIdCodeMap.get(key);
            result.put(userCode, BeanUtils.convert(UserWagesCardDTO.class, value));
        });
        return result;
    }

    private boolean isWagesCardNeedHistory(UserWagesCardDO before, UserWagesCardSaveParam after) {
        if (Objects.isNull(before) || Objects.isNull(after)) {
            return false;
        }
        UserWagesCardDO afterConvert = BeanUtils.convert(after, UserWagesCardDO.class);
        boolean flag = UserCheckHelper.isWagesCardNeedHistory(before, afterConvert);
        if (flag) {
            log.info("需要存储变更记录,before:{},after:{}", JSON.toJSONString(before), JSON.toJSONString(after));
        }
        return flag;
    }
}
