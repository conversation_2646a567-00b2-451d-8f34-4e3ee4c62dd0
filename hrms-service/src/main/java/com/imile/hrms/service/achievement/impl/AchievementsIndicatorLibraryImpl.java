package com.imile.hrms.service.achievement.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.achievement.KpiTypeSortEnum;
import com.imile.hrms.common.enums.achievement.TargetPropertiesEnum;
import com.imile.hrms.common.util.DateConvertUtils;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.common.util.PageConvertUtil;
import com.imile.hrms.dao.achievement.dto.MapperCodeDTO;
import com.imile.hrms.dao.achievement.mapper.AchievementsIndicatorLibraryMapper;
import com.imile.hrms.dao.achievement.model.AchievementsIndicatorLibraryDO;
import com.imile.hrms.dao.achievement.model.AchievementsOrgTargetItemDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.service.achievement.AchievementsIndicatorLibraryService;
import com.imile.hrms.service.achievement.AchievementsOrgTargetItemService;
import com.imile.hrms.service.achievement.dto.AchievementsIndicatorLibraryAddDTO;
import com.imile.hrms.service.achievement.dto.AchievementsIndicatorLibraryExportDTO;
import com.imile.hrms.service.achievement.dto.AchievementsIndicatorLibraryImportDTO;
import com.imile.hrms.service.achievement.dto.AchievementsIndicatorLibraryListDTO;
import com.imile.hrms.service.achievement.dto.AchievementsIndicatorLibraryListParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsIndicatorLibraryUpdateDTO;
import com.imile.util.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 指标库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Service
public class AchievementsIndicatorLibraryImpl extends ServiceImpl<AchievementsIndicatorLibraryMapper, AchievementsIndicatorLibraryDO> implements AchievementsIndicatorLibraryService {

    @Autowired
    private AchievementsOrgTargetItemService achievementsOrgTargetItemService;

    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    @Override
    public PaginationResult<AchievementsIndicatorLibraryListDTO> pageList(AchievementsIndicatorLibraryListParamDTO dto) {

        try {
            if (ObjectUtil.isNotEmpty(dto.getBeginDate())) {
                dto.setBeginDate(DateConvertUtils.getMinTime(dto.getBeginDate()));
            }
            
            if (ObjectUtil.isNotEmpty(dto.getEndDate())) {
                dto.setEndDate(DateConvertUtils.getMaxTime(dto.getEndDate()));
            }
        } catch (Exception e) {
            throw new RuntimeException("时间转换异常");
        }
        List<String> visibilityList = new ArrayList();
        if (StringUtils.isNotBlank(dto.getVisibility())) {
            List<String> list = Arrays.asList(dto.getVisibility().split(","));
            visibilityList.addAll(list);
        }

        //分页查询
        Page<AchievementsIndicatorLibraryDO> page = PageHelper.startPage(dto.getCurrentPage(), dto.getShowCount(), dto.getShowCount() > 0);
        PageInfo<AchievementsIndicatorLibraryDO> listPage = page.doSelectPageInfo(() -> this.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementsIndicatorLibraryDO>()
                .eq(StringUtils.isNotBlank(dto.getTargetType()), AchievementsIndicatorLibraryDO::getTargetType, dto.getTargetType())
                .eq(StringUtils.isNotBlank(dto.getKpiType()), AchievementsIndicatorLibraryDO::getKpiType, dto.getKpiType())
                .eq(StringUtils.isNotBlank(dto.getIsComplet()), AchievementsIndicatorLibraryDO::getIsComplet, dto.getIsComplet())
                .eq(StringUtils.isNotBlank(dto.getType()), AchievementsIndicatorLibraryDO::getType, dto.getType())
                .eq(StringUtils.isNotBlank(dto.getTargetProperties()), AchievementsIndicatorLibraryDO::getTargetProperties, dto.getTargetProperties())
                .in(StringUtils.isNotBlank(dto.getVisibility()), AchievementsIndicatorLibraryDO::getVisibility, visibilityList)
                .ge(ObjectUtil.isNotEmpty(dto.getBeginDate()), AchievementsIndicatorLibraryDO::getLastUpdDate, dto.getBeginDate())
                .eq(AchievementsIndicatorLibraryDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), AchievementsIndicatorLibraryDO::getLastUpdDate, dto.getEndDate())
                .and(StringUtils.isNotBlank(dto.getTargetName()),wrapper -> wrapper.like(AchievementsIndicatorLibraryDO::getTargetNameEn, dto.getTargetName())
                        .or()
                        .like(AchievementsIndicatorLibraryDO::getTargetName, dto.getTargetName()))//AND (name like '%a%' or name like '%b%')
                .orderByAsc(AchievementsIndicatorLibraryDO::getSort)
        ));

        if (listPage.getList().size() == 0) {
            return PageConvertUtil.getEmptyPageResult(dto);

        }

        //转换为DTO
        List<AchievementsIndicatorLibraryListDTO> listDTO = BeanUtils.convert(AchievementsIndicatorLibraryListDTO.class, listPage.getList());

        List<Long> ids = listDTO.stream().map(AchievementsIndicatorLibraryListDTO::getId).collect(Collectors.toList());

        List<AchievementsOrgTargetItemDO> targetItemList = achievementsOrgTargetItemService.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementsOrgTargetItemDO>()
                .in(AchievementsOrgTargetItemDO::getIndicatorLibraryId, ids)
                .eq(AchievementsOrgTargetItemDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );

        //指标库id分组
        Map<Long, List<AchievementsOrgTargetItemDO>> targetItemGroupMap = targetItemList.stream().collect(Collectors.groupingBy(AchievementsOrgTargetItemDO::getIndicatorLibraryId));

        for (AchievementsIndicatorLibraryListDTO achievementsIndicatorLibraryListDTO : listDTO) {
            KpiTypeSortEnum kpiTypeSortEnum = KpiTypeSortEnum.getInstance(achievementsIndicatorLibraryListDTO.getKpiType());
            if (kpiTypeSortEnum != null) {
                achievementsIndicatorLibraryListDTO.setKpiTypeDesc(RequestInfoHolder.isChinese() ? kpiTypeSortEnum.getDesc() : kpiTypeSortEnum.getDescEn());
            }
            if (targetItemGroupMap.containsKey(achievementsIndicatorLibraryListDTO.getId())) {
                achievementsIndicatorLibraryListDTO.setDeptNum(targetItemGroupMap.get(achievementsIndicatorLibraryListDTO.getId()).size());
            }

        }

        return PageConvertUtil.getPageResult(listDTO, dto, (int) listPage.getTotal(), listPage.getPages());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(AchievementsIndicatorLibraryAddDTO addDTO) {

        // 更新sort字段大于等于当前sort的值+1
        this.getBaseMapper().update(null, new LambdaUpdateWrapper<AchievementsIndicatorLibraryDO>()
                .setSql("sort = sort + 1")
                //大于等于当前sort的值
                .ge(AchievementsIndicatorLibraryDO::getSort, addDTO.getSort())
        );

        AchievementsIndicatorLibraryDO achievementsIndicatorLibraryDO = BeanUtils.convert(addDTO, AchievementsIndicatorLibraryDO.class);
        //设置基础字段
        BaseDOUtil.fillDOInsert(achievementsIndicatorLibraryDO);
        achievementsIndicatorLibraryDO.setId(iHrmsIdWorker.nextId());
        this.getBaseMapper().insert(achievementsIndicatorLibraryDO);

        return achievementsIndicatorLibraryDO.getId();

    }

    @Override
    public Boolean check(Long id) {
        Integer count = achievementsOrgTargetItemService.getBaseMapper().selectCount(new LambdaQueryWrapper<AchievementsOrgTargetItemDO>()
                .eq(AchievementsOrgTargetItemDO::getIndicatorLibraryId, id)
                .eq(AchievementsOrgTargetItemDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );
        if (count > 0) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AchievementsIndicatorLibraryUpdateDTO updateDTO) {
        AchievementsIndicatorLibraryDO achievementsIndicatorLibraryDO = this.getBaseMapper().selectById(updateDTO.getId());

        Integer sortOld = achievementsIndicatorLibraryDO.getSort();

        achievementsIndicatorLibraryDO = BeanUtils.convert(updateDTO, AchievementsIndicatorLibraryDO.class);

        Integer sortNew = achievementsIndicatorLibraryDO.getSort();

        // 查询出sortOld到sortNew之间的数据(sortOld可能比sortNew 大也可能小) 然后+1或者-1

        if (sortOld > sortNew) {
            this.getBaseMapper().update(new AchievementsIndicatorLibraryDO(), new LambdaUpdateWrapper<AchievementsIndicatorLibraryDO>()
                    .setSql("sort = sort + 1")
                    //大于等于
                    .ge(AchievementsIndicatorLibraryDO::getSort, sortNew)
                    .lt(AchievementsIndicatorLibraryDO::getSort, sortOld)
            );
        } else if (sortOld < sortNew) {
            this.getBaseMapper().update(new AchievementsIndicatorLibraryDO(), new LambdaUpdateWrapper<AchievementsIndicatorLibraryDO>()
                    .setSql("sort = sort - 1")
                    //大于
                    .gt(AchievementsIndicatorLibraryDO::getSort, sortOld)
                    .le(AchievementsIndicatorLibraryDO::getSort, sortNew)
            );
        }

        this.getBaseMapper().updateById(achievementsIndicatorLibraryDO);

        List<AchievementsOrgTargetItemDO> list = achievementsOrgTargetItemService.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementsOrgTargetItemDO>()
                .eq(AchievementsOrgTargetItemDO::getIndicatorLibraryId, updateDTO.getId())
                .eq(AchievementsOrgTargetItemDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );
        for (AchievementsOrgTargetItemDO achievementsOrgTargetItemDO : list) {
            achievementsOrgTargetItemDO.setTargetName(updateDTO.getTargetName());
            achievementsOrgTargetItemDO.setTargetNameEn(updateDTO.getTargetNameEn());
            achievementsOrgTargetItemDO.setUnit(updateDTO.getUnit());
            achievementsOrgTargetItemDO.setKpiType(updateDTO.getKpiType());
        }
        achievementsOrgTargetItemService.updateBatchById(list);
    }

    @Override
    public List<AchievementsIndicatorLibraryImportDTO> importExcle(List<AchievementsIndicatorLibraryImportDTO> importList) {

        List<AchievementsIndicatorLibraryImportDTO> errorList = new ArrayList<>();

        List<AchievementsIndicatorLibraryDO> addList = new ArrayList<>();

        for (AchievementsIndicatorLibraryImportDTO importDTO : importList) {
            if (StringUtils.isEmpty(importDTO.getTargetName())) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "指标名称不能为空" : "indicator name cannot be empty");
                errorList.add(importDTO);
            }
            if (StringUtils.isEmpty(importDTO.getKpiType())) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "指标分类不能为空" : "indicator type cannot be empty");
                errorList.add(importDTO);
            }
            if (StringUtils.isNotEmpty(importDTO.getTargetProperties())) {
                if (TargetPropertiesEnum.FORWARD.getDesc().equals(importDTO.getTargetProperties())) {
                    importDTO.setTargetProperties("01");
                } else if (TargetPropertiesEnum.REVERSE.getDesc().equals(importDTO.getTargetProperties())) {
                    importDTO.setTargetProperties("02");
                } else {
                    IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "指标性质只能为正向或反向" : "indicator properties can only be positive or negative");
                    errorList.add(importDTO);
                }
            }
            importDTO.setTargetType("01");
            if (StringUtils.isNotEmpty(importDTO.getIsComplet())) {
                importDTO.setTargetType("02");
                if ("prism".equals(importDTO.getIsComplet())) {
                    importDTO.setIsComplet("1");
                } else if ("fill in byself".equals(importDTO.getIsComplet())) {
                    importDTO.setIsComplet("0");
                } else {
                    IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "KPI classification/完成值来源只能为prism或fill in byself" : "whether to improve can only be prism or fill in byself");
                    errorList.add(importDTO);
                }
            }

            AchievementsIndicatorLibraryDO achievementsIndicatorLibraryDO = BeanUtils.convert(importDTO, AchievementsIndicatorLibraryDO.class);
            achievementsIndicatorLibraryDO.setType("0");
            addList.add(achievementsIndicatorLibraryDO);
        }

        this.saveBatch(addList);

        return errorList;
    }

    @Override
    public PaginationResult<AchievementsIndicatorLibraryExportDTO> listExport(AchievementsIndicatorLibraryListParamDTO exportDto) {
        PaginationResult<AchievementsIndicatorLibraryListDTO> pageDTO = this.pageList(exportDto);

        List<AchievementsIndicatorLibraryListDTO> listDTO = pageDTO.getResults();

        List<AchievementsIndicatorLibraryExportDTO> exportDTOList = BeanUtils.convert(AchievementsIndicatorLibraryExportDTO.class, listDTO);

        for (AchievementsIndicatorLibraryExportDTO dto : exportDTOList) {
            KpiTypeSortEnum kpiTypeSortEnum = KpiTypeSortEnum.getInstance(dto.getKpiType());
            if (kpiTypeSortEnum != null) {
                dto.setKpiType(RequestInfoHolder.isChinese() ? kpiTypeSortEnum.getDesc() : kpiTypeSortEnum.getDescEn());
            }
            if (TargetPropertiesEnum.FORWARD.getCode().equals(dto.getTargetProperties())) {
                dto.setTargetPropertiesName(TargetPropertiesEnum.FORWARD.getDesc());
            } else {
                dto.setTargetPropertiesName(TargetPropertiesEnum.REVERSE.getDesc());
            }
            if ("01".equals(dto.getTargetType())) {
                dto.setTargetType("定性");
            } else {
                 dto.setTargetType("定量");
            }

        }

        return PageConvertUtil.getPageResult(exportDTOList, exportDto, pageDTO.getPagination().getTotalResult(), pageDTO.getPagination().getTotalPage());
    }

    @Override
    public List<MapperCodeDTO> mapperCodes(Long id) {
        return this.getBaseMapper().mapperCodes(id);
    }

    @Override
    public List<MapperCodeDTO> mapperCodesByEmployeeAppraisalId(Long id) {
        return this.getBaseMapper().mapperCodesByEmployeeAppraisalId(id);
    }

    @Override
    public void sortInit() {
        /**
         * 1.查询所有指标
         * 2.设置sort字段从1开始递增
         * 3.更新
         */
        List<AchievementsIndicatorLibraryDO> list = this.getBaseMapper().selectList(new LambdaQueryWrapper<AchievementsIndicatorLibraryDO>()
                .eq(AchievementsIndicatorLibraryDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByDesc(AchievementsIndicatorLibraryDO::getLastUpdDate)
        );

        for (int i = 0; i < list.size(); i++) {
            AchievementsIndicatorLibraryDO achievementsIndicatorLibraryDO = list.get(i);
            achievementsIndicatorLibraryDO.setSort(i + 1);
        }

        this.updateBatchById(list);

    }

}
