package com.imile.hrms.service.tx.impl;

import com.imile.hrms.common.enums.OperationModuleEnum;
import com.imile.hrms.dao.tx.model.HrmsTxLogDO;
import com.imile.hrms.service.tx.AbstractHrmsTxManage;
import com.imile.hrms.service.user.SyncUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户离职事务管理器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/17
 */
@Service
public class UserDimissionTxManage extends AbstractHrmsTxManage {
    @Autowired
    private SyncUserService syncUserService;

    @Override
    protected Boolean doAction(HrmsTxLogDO hrmsTxLogDO) {
        syncUserService.doDelete(Long.parseLong(hrmsTxLogDO.getBizId()));
        return Boolean.TRUE;
    }

    @Override
    public OperationModuleEnum getOperationModule() {
        return OperationModuleEnum.DIMISSION_MODULE;
    }
}
