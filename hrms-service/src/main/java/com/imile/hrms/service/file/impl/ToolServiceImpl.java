package com.imile.hrms.service.file.impl;

import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.integration.ipep.IpepIntegration;
import com.imile.hrms.integration.ipep.OssApiVo;
import com.imile.hrms.service.file.ToolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 工具方法Service
 *
 * <AUTHOR>
 * @since 2024/7/15
 */
@Service
@Slf4j
public class ToolServiceImpl implements ToolService {

    @Resource
    private IpepIntegration ipepIntegration;

    @Override
    public String upload(String directory, MultipartFile file) {
        try {
            return ipepIntegration.upload(directory, file.getOriginalFilename(), file.getBytes(), 1);
        } catch (Exception e) {
            log.error("文件上传错误, cause: ", e);
            throw BusinessException.get(MsgCodeConstant.THIRD_SERVICE_ERROR);
        }
    }

    @Override
    public String getUrlByFileKey(String fileKey) {
        OssApiVo oss = ipepIntegration.getUrlByFileKey(fileKey, 1);
        return oss.getFileUrl();
    }
}
