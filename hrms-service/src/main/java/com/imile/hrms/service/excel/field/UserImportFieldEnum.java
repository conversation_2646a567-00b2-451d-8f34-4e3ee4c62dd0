package com.imile.hrms.service.excel.field;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
@Getter
public enum UserImportFieldEnum {
    /**
     * 人员导入字段枚举类
     */
    USER_CODE("userCode", "账号"),
    USER_NAME("userName", "姓名全称"),
    USER_NAME_EN("userNameEn", "英文名"),
    NATIONALITY("nationality", "国籍"),
    CONFIRM_ENTRY_DATE("confirmEntryDate", "实际入职日期"),
    DEPT_ID("deptId", "部门ID"),
    OC_NAME("ocName", "核算单元"),
    BIZ_MODEL_NAME("bizModelName", "业务节点"),
    PROJECT_CODE("projectCode", "项目编码"),
    POST_ID("postId", "岗位ID"),
    LEADER_CODE("leaderCode", "汇报上级账号"),
    VENDOR_CODE("vendorCode", "供应商编码"),
    PROBATION_MONTH("probationMonth", "试用期（月）"),
    JOB_SEQUENCE("jobSequence", "职级序列"),
    JOB_LEVEL("jobLevel", "职级"),
    JOB_GRADE("jobGrade", "职等"),
    IS_GLOBAL_RELOCATION("isGlobalRelocation", "是否派遣"),
    LOCATION_COUNTRY("locationCountry", "常驻国"),
    LOCATION_PROVINCE("locationProvince", "常驻省"),
    LOCATION_CITY("locationCity", "常驻市"),
    PAYMENT_COUNTRY("paymentCountry", "工资卡支付国家"),
    WAGES_CARD_BANK("wagesCardBank", "工资卡开户行"),
    WAGES_CARD_BANK_BRANCH("wagesCardBankBranch", "工资卡开户行分支行"),
    WAGES_CARD_NO("wagesCardNo", "工资卡卡号"),
    CARDHOLDER_NAME("cardholderName", "持卡人姓名"),
    SWIFT_CODE("swiftCode", "路由代码"),
    VISA_TYPE("visaType", "签证类型"),
    DISPATCH_START_DATE("dispatchStartDate", "本次派遣开始日期"),
    WORK_SENIORITY("workSeniority", "工龄（月）"),
    ;

    private final String key;

    private final String desc;

    UserImportFieldEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getDesc(ImportField[] values) {
        return Arrays.stream(values)
                .filter(item -> item.getKey().equals(key))
                .findAny()
                .map(ImportField::getDesc)
                .orElse("");
    }
}
