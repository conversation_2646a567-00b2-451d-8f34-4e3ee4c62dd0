package com.imile.hrms.service.excel;

import com.google.common.collect.Lists;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.enums.VisaTypeEnum;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.user.transform.TransformTypeEnum;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.common.util.DateFormatUtils;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.UserTransformDO;
import com.imile.hrms.manage.base.CountryManage;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.organization.BizModelManage;
import com.imile.hrms.manage.organization.GradeManage;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.excel.field.UserImportFieldEnum;
import com.imile.hrms.service.excel.field.UserSpecialTransformImportFieldEnum;
import com.imile.hrms.service.log.component.OperationFieldDiffer;
import com.imile.hrms.service.log.helper.OperationRecordHelper;
import com.imile.hrms.service.refactor.user.param.UserDifferParam;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.context.UserImportGlobalContext;
import com.imile.hrms.service.user.context.UserImportTempContext;
import com.imile.hrms.service.user.helper.ExternalDependencyHelper;
import com.imile.hrms.service.user.helper.UserHelper;
import com.imile.hrms.service.user.helper.UserTransformHelper;
import com.imile.hrms.service.user.param.transform.UserSpecialTransformImportParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/12
 */
@Slf4j
@Service
public class UserSpecialTransformImportProcessor extends UserBaseImportProcessor<UserSpecialTransformImportParam> {

    @Resource
    private UserManage userManage;
    @Resource
    private CountryManage countryManage;
    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private BizModelManage bizModelManage;
    @Resource
    private PostManage postManage;
    @Resource
    private GradeManage gradeManage;
    @Resource
    private UserService userService;
    @Resource
    private ExternalDependencyHelper externalDependencyHelper;
    @Resource
    private LogRecord logRecord;

    private static final Pattern WHETHER_PATTERN = Pattern.compile("是/Yes|否/No|Yes|No|是|否");

    private static final Pattern VISATYPE_PATTERN =
            Pattern.compile("旅游签证/Tourist Visa|商务签证/Business Visa|落地签证/Arrival Visa|工作签证/Residence Visa" +
                    "|旅游签证|商务签证|落地签证|工作签证|Tourist Visa|Business Visa|Arrival Visa|Residence Visa");

    @Override
    protected UserImportGlobalContext buildGlobalContext(List<UserSpecialTransformImportParam> dataList) {
        UserImportGlobalContext context = new UserImportGlobalContext();

        List<String> duplicatedUserCodeList = dataList.stream()
                .filter(param -> StringUtils.isNotBlank(param.getUserCode()))
                .collect(Collectors.groupingBy(UserSpecialTransformImportParam::getUserCode, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        context.setDuplicatedUserCodeList(duplicatedUserCodeList);

        List<String> userCodeList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getUserCode);
        context.setUserMap(userManage.getUserMapByCode(userCodeList));

        List<HrmsUserInfoDO> userList = Lists.newArrayList(context.getUserMap().values());

        List<String> nationalityCodeList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getCountryCode);
        context.setOriginNationalityMap(countryManage.getCountryMap(nationalityCodeList));

        List<Long> originDeptIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getDeptId);
        context.setOriginDeptMap(deptManage.getDeptMap(originDeptIdList));

        List<Long> originOcDeptIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getOcId);
        context.setOriginOcDeptMap(deptManage.getDeptMap(originOcDeptIdList));

        List<Long> originBizModelIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getBizModelId);
        context.setOriginBizModelMap(bizModelManage.getBizModelMap(originBizModelIdList));

        List<String> originProjectCodeList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getProjectCode);
        context.setOriginProjectMap(externalDependencyHelper.getProjectMap(originProjectCodeList));

        List<Long> originLeaderIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getLeaderId);
        context.setOriginLeaderMap(userManage.getUserMapById(originLeaderIdList));

        List<Long> originPostIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getPostId);
        context.setOriginPostMap(postManage.getPostMap(originPostIdList));

        List<Long> originGradeIdList = CommonUtils.getFieldValueList(userList, HrmsUserInfoDO::getGradeId);
        context.setOriginGradeMap(gradeManage.getGradeMap(originGradeIdList));

        List<Long> deptIdList = super.getIdList(dataList, UserSpecialTransformImportParam::getDeptId);
        List<HrmsEntDeptDO> deptList = deptManage.getDeptListByIdList(deptIdList);
        context.setDeptMap(deptList.stream()
                .collect(Collectors.toMap(s -> s.getId().toString(), Function.identity())));

        List<String> ocNameList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getOcName);
        context.setOcName2DeptListMap(super.getOcName2DeptListMap(ocNameList));

        List<String> bizModelNameList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getBizModelName);
        context.setBizModelName2DataListMap(bizModelManage.getBizModelName2DataListMap(bizModelNameList));

        List<String> projectCodeList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getProjectCode);
        context.setProjectMap(externalDependencyHelper.getProjectMap(projectCodeList));

        List<String> leaderCodeList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getLeaderCode);
        context.setLeaderMap(userManage.getUserMapByCode(leaderCodeList));

        List<String> countryNameList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getLocationCountry);
        List<String> provinceNameList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getLocationProvince);
        List<String> cityNameList = CommonUtils.getFieldValueList(dataList, UserSpecialTransformImportParam::getLocationCity);
        context.setZoneKey2IdMap(super.getZoneKey2IdMap(countryNameList, provinceNameList, cityNameList));
        return context;
    }

    @Override
    protected UserImportTempContext buildTempContext(UserSpecialTransformImportParam data,
                                                     UserImportGlobalContext globalContext) {
        UserImportTempContext context = new UserImportTempContext();
        HrmsUserInfoDO originUser = globalContext.getUserMap().get(data.getUserCode());
        if (Objects.isNull(originUser)) {
            return context;
        }
        context.setOriginUser(originUser);
        context.setOriginNationality(globalContext.getOriginNationalityMap().get(originUser.getCountryCode()));
        context.setOriginDept(globalContext.getOriginDeptMap().get(originUser.getDeptId()));
        context.setOriginOcDept(globalContext.getOriginOcDeptMap().get(originUser.getOcId()));
        context.setOriginBizModel(globalContext.getOriginBizModelMap().get(originUser.getBizModelId()));
        context.setOriginProject(globalContext.getOriginProjectMap().get(originUser.getProjectCode()));
        context.setOriginLeader(globalContext.getOriginLeaderMap().get(originUser.getLeaderId()));
        context.setOriginPost(globalContext.getOriginPostMap().get(originUser.getPostId()));
        context.setOriginGrade(globalContext.getOriginGradeMap().get(originUser.getGradeId()));
        context.setImportFields(UserSpecialTransformImportFieldEnum.values());
        HrmsUserInfoDO user = new HrmsUserInfoDO();
        user.setId(originUser.getId());
        context.setUser(user);
        return context;
    }

    @Override
    protected String checkFailFastField(UserSpecialTransformImportParam data, UserImportGlobalContext globalContext) {
        if (StringUtils.isBlank(data.getUserCode())) {
            return super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_REQUIRED,
                    UserSpecialTransformImportFieldEnum.USER_CODE.getDesc());
        }
        if (globalContext.getDuplicatedUserCodeList().contains(data.getUserCode())) {
            return super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_VALUE_DUPLICATED,
                    UserSpecialTransformImportFieldEnum.USER_CODE.getDesc(), data.getUserCode());
        }
        HrmsUserInfoDO user = globalContext.getUserMap().get(data.getUserCode());
        if (Objects.isNull(user)) {
            return super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_NOT_EXIST,
                    UserSpecialTransformImportFieldEnum.USER_CODE.getDesc(), data.getUserCode());
        }
        if (!WorkStatusEnum.ON_JOB.getCode().equals(user.getWorkStatus())) {
            return super.getErrorTip(HrmsErrorCodeEnums.USER_WORK_STATUS_UNAVAILABLE, user.getUserCode());
        }
        if (!EmploymentTypeEnum.isOwn(user.getEmployeeType())) {
            return super.getErrorTip(HrmsErrorCodeEnums.USER_EMPLOYEE_TYPE_UNAVAILABLE,
                    EmploymentTypeEnum.descOfCode(user.getEmployeeType()));
        }
        return "";
    }

    @Override
    protected List<String> checkFailSafeField(UserSpecialTransformImportParam data,
                                              UserImportGlobalContext globalContext,
                                              UserImportTempContext tempContext) {
        List<String> errorTipList = Lists.newArrayList();
        // 校验部门及网点
        super.doOrganizationCheck(data.getDeptId(), data.getOcName(), globalContext, tempContext, errorTipList);
        // 校验业务节点
        super.doFieldDataExistCheck(UserImportFieldEnum.BIZ_MODEL_NAME, data.getBizModelName(),
                data.getBizModelName(), globalContext.getBizModelName2DataListMap(), tempContext, errorTipList);
        // 校验项目
        super.doFieldDataExistCheck(UserImportFieldEnum.PROJECT_CODE, data.getProjectCode(),
                data.getProjectCode(), globalContext.getProjectMap(), tempContext, errorTipList);
        // 校验汇报上级
        super.doFieldDataExistCheck(UserImportFieldEnum.LEADER_CODE, data.getLeaderCode(),
                data.getLeaderCode(), globalContext.getLeaderMap(), tempContext, errorTipList);
        // 校验是否派遣、签证、派遣日期
        this.doDispatchCheck(data.getIsGlobalRelocation(), data.getVisaType(), data.getDispatchStartDate(),
                globalContext, tempContext, errorTipList);
        // 校验常驻地
        super.doLocationCheck(data.getLocationCountry(), data.getLocationProvince(), data.getLocationCity(),
                globalContext, tempContext, errorTipList);
        return errorTipList;
    }

    @Override
    protected void processData(UserSpecialTransformImportParam data, UserImportTempContext tempContext) {
        // 组装数据
        UserTransformDO userTransform = UserTransformHelper.buildUserTransform(TransformTypeEnum.SPECIAL_TRANSFER,
                tempContext.getUser(), tempContext, tempContext.getExtraFieldMap());
        // 数据入库并记录操作日志
        userManage.doUserTransformSave(tempContext.getUser(), userTransform);
        logRecord.diffObj(tempContext.getUser(), tempContext.getOriginUser(),
                OperationTypeEnum.SPECIAL_TRANSFORM_IMPORT_UPDATE.getCode());
        List<OperationFieldDiffer> fieldDiffList = UserHelper.saveLog(UserDifferParam.builder()
                .id(tempContext.getOriginUser().getId())
                .operationCode(OperationCodeEnum.HRMS_TRANSFER_SPECIAL)
                .beforeUser(tempContext.getOriginUser())
                .afterUser(tempContext.getUser())
                .build());
        // 发送数据变动通知
        userService.doUserBaseInfoChangeNotice(tempContext.getOriginUser(), fieldDiffList);
        this.sendTransformMq(userTransform, tempContext);
    }

    private void doDispatchCheck(String isGlobalRelocation, String visaType, String dispatchStartDate,
                                 UserImportGlobalContext globalContext, UserImportTempContext tempContext,
                                 List<String> errorTipList) {
        // 校验是否派遣
        super.doFieldEnumCheck(UserImportFieldEnum.IS_GLOBAL_RELOCATION, isGlobalRelocation, WhetherEnum.values(),
                tempContext, errorTipList);
        // 是否派遣未校验通过 或值为否 则不需要校验签证类型及派遣开始日期
        if (Objects.isNull(tempContext.getUser().getIsGlobalRelocation())
                || WhetherEnum.NO.getKey().equals(tempContext.getUser().getIsGlobalRelocation())) {
            return;
        }
        if (StringUtils.isBlank(visaType) || StringUtils.isBlank(dispatchStartDate)) {
            String fieldNames = String.join("、", Lists.newArrayList(
                    UserSpecialTransformImportFieldEnum.VISA_TYPE.getDesc(),
                    UserSpecialTransformImportFieldEnum.DISPATCH_START_DATE.getDesc()));
            errorTipList.add(super.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_ASSOCIATED_LIMIT,
                    UserSpecialTransformImportFieldEnum.IS_GLOBAL_RELOCATION.getDesc(), isGlobalRelocation,
                    fieldNames));
            return;
        }
        // 校验签证类型
        super.doFieldEnumCheck(UserImportFieldEnum.VISA_TYPE, visaType, VisaTypeEnum.values(),
                tempContext, errorTipList);
        // 校验派遣开始日期
        super.doFieldPatternCheck(UserImportFieldEnum.DISPATCH_START_DATE, dispatchStartDate,
                DATE_PATTERN, DateFormatUtils.DATE, globalContext, tempContext, errorTipList);
    }

    private void sendTransformMq(UserTransformDO userTransform, UserImportTempContext tempContext) {
        HrmsUserInfoDO before = tempContext.getOriginUser();
        HrmsUserInfoDO after = tempContext.getUser();
        List<OperationFieldDiffer> fieldDifferList = OperationRecordHelper.getFieldDifferList(before, after);
        userService.doTransformNotice(userTransform.getId(), before.getUserCode(), fieldDifferList);
    }
}
