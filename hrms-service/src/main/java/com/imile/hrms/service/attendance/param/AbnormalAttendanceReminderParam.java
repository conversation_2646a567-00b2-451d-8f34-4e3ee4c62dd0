package com.imile.hrms.service.attendance.param;

import lombok.Builder;
import lombok.Data;

/**
 * {@code @author:} allen
 * {@code @className:} AbnormalAttendanceReminderParam
 * {@code @since:} 2023-11-16 10:53
 * {@code @description:} 异常考勤手动发送企业微信提醒 入参
 */
@Data
public class AbnormalAttendanceReminderParam {
    /**
     * 异常考勤的用户code
     */
    private String userCode;
    /**
     * 该用户对应的考勤异常状态：AbnormalAttendanceStatusEnum
     */
    private String status;
    /**
     * 用户所属国家，用来设置发送企业微信消息语言（非必须，如果该用户没有国家数据，则默认发送英语文案提醒）
     */
    private String country;
    /**
     * 异常考勤日
     */
    private Long dayId;
}
