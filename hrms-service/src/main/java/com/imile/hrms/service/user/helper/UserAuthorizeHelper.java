package com.imile.hrms.service.user.helper;

import com.alibaba.fastjson.JSON;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hermes.enterprise.dto.EntOcSlimpleApiDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.ResourceTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.integration.hermes.service.EntEmployeeService;
import com.imile.hrms.integration.hermes.service.UserAuthorizeService;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.service.organization.helper.OrganizationHelper;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Slf4j
@Component
public class UserAuthorizeHelper {

    @Resource
    private HrmsDeptManage deptManage;
    @Resource
    private OrganizationHelper organizationHelper;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private EntEmployeeService entEmployeeService;
    @Resource
    private UserAuthorizeService userAuthorizeService;

    public List<Long> getFilterOcDeptIdList(String ocCode) {
        List<Long> userOcDeptIdList = this.getUserOcDeptIdList();
        if (StringUtils.isBlank(ocCode)) {
            return userOcDeptIdList;
        }
        Long ocDeptId = organizationHelper.convert2OcDeptId(ocCode);
        // 若该网点不在当前操作人权限范围内 则返回无效部门ID
        if (!userOcDeptIdList.contains(ocDeptId)) {
            return Collections.singletonList(BusinessConstant.INVALID_ID);
        }
        return Collections.singletonList(ocDeptId);
    }

    public List<Long> getDeptIdList() {
        if (StringUtils.isBlank(RequestInfoHolder.getUserCode())) {
            return Collections.singletonList(BusinessConstant.INVALID_ID);
        }
        PermissionDeptVO permissionDept = userResourceService.getPermissionDept();
        log.info("operator:{},permission dept:{}", RequestInfoHolder.getUserCode(), JSON.toJSONString(permissionDept));
        if (permissionDept.getIsSysAdmin()) {
            return permissionDept.getDeptIdList();
        }
        // 当前操作人无部门权限则返回无效部门ID
        if (CollectionUtils.isEmpty(permissionDept.getDeptIdList())) {
            return Collections.singletonList(BusinessConstant.INVALID_ID);
        }
        return permissionDept.getDeptIdList();
    }

    public List<Long> getFilterDeptIdList(List<Long> deptIdList) {
        List<Long> permissionDeptIdList = this.getDeptIdList();
        // permissionDeptIdList为空代表是超管 此时直接返回传参中的deptIdList
        if (CollectionUtils.isEmpty(permissionDeptIdList)) {
            return deptIdList;
        }
        // 非超管且传参为空则返回权限范围内的部门ID
        if (CollectionUtils.isEmpty(deptIdList)) {
            return permissionDeptIdList;
        }
        return deptIdList.stream()
                .filter(permissionDeptIdList::contains)
                .collect(Collectors.toList());
    }

    public boolean hasDeptPermission(Long deptId) {
        if (Objects.isNull(deptId)) {
            return false;
        }
        PermissionDeptVO permissionDept = userResourceService.getPermissionDept();
        log.info("operator:{},permission dept:{}", RequestInfoHolder.getUserCode(), JSON.toJSONString(permissionDept));
        if (permissionDept.getIsSysAdmin()) {
            return true;
        }
        return permissionDept.getDeptIdList().contains(deptId);
    }

    public Boolean hasJobLevelPermission() {
        Map<String, List<String>> dataPermissionMap = RequestInfoHolder.getLoginInfo().getPermissionMap();
        List<String> dataValueList = dataPermissionMap.get("HRMS_JOB_LEVEL_PERMISSION");
        if (CollectionUtils.isEmpty(dataValueList)) {
            return Boolean.FALSE;
        }
        return dataValueList.stream()
                .anyMatch(ResourceTypeEnum.ALL.getCode()::equals);
    }

    /**
     * 获取人员网点权限列表
     *
     * @return List<Long>
     */
    private List<Long> getUserOcDeptIdList() {
        EntEmployeeApiDTO employee = entEmployeeService.getEmployeeByCode(RequestInfoHolder.getUserCode());
        if (Objects.isNull(employee)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS);
        }
        List<EntOcSlimpleApiDTO> ocList = userAuthorizeService.selectOcByUserId(BusinessConstant.DEFAULT_ORG_ID, employee.getId());
        // 若无任何网点权限 则返回无效部门ID
        if (CollectionUtils.isEmpty(ocList)) {
            return Collections.singletonList(BusinessConstant.INVALID_ID);
        }
        List<Long> ocIdList = ocList.stream()
                .map(EntOcSlimpleApiDTO::getId)
                .collect(Collectors.toList());
        List<HrmsEntDeptDO> ocDeptList = deptManage.getOcByIds(ocIdList);
        return ocDeptList.stream()
                .map(HrmsEntDeptDO::getId)
                .collect(Collectors.toList());
    }
}
