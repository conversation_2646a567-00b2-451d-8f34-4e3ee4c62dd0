package com.imile.hrms.service.event.publisher;

import com.imile.hrms.service.attendance.param.EmployeeSchedulingParam;
import com.imile.hrms.service.event.domain.EmployeeSchedulingRegisterEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * @Author: han.wang
 * @Date: Created in 2024/11/19
 * @Description: 人员排班监听事件
 */
@Service
public class EmployeeSchedulingEventPublisher {

    @Autowired
    private ApplicationEventPublisher publisher;

    public void sendSchedulingEvent(EmployeeSchedulingParam param) {
        publisher.publishEvent(new EmployeeSchedulingRegisterEvent(this, param));
    }
}
