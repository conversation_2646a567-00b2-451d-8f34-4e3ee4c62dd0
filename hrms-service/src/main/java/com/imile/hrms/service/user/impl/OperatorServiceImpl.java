package com.imile.hrms.service.user.impl;

import com.github.easylog.service.IOperatorService;
import com.imile.genesis.api.enums.CommonStatusEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.user.UserPermissionTypeEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.user.dto.BaseOptionDTO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.UserAssociateConditionBuilder;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.user.OperatorService;
import com.imile.hrms.service.user.helper.UserAuthorizeHelper;
import com.imile.hrms.service.user.param.OperatorUserAssociateQueryParam;
import com.imile.hrms.service.user.result.UserSelectorBO;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@Service
public class OperatorServiceImpl implements IOperatorService, OperatorService {

    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private UserManage userManage;
    @Resource
    private UserAuthorizeHelper userAuthorizeHelper;
    @Resource
    private HrmsProperties hrmsProperties;

    @Override
    public String getOperator() {
        return RequestInfoHolder.getUserCode() + "|" + RequestInfoHolder.getUserName();
    }

    @Override
    public String getPlatform() {
        return "hrms";
    }

    @Override
    public List<BaseOptionDTO> getVisibleFirstLevelDeptList() {
        List<Long> authDeptIdList = userAuthorizeHelper.getDeptIdList();
        List<HrmsEntDeptDO> firstLevelDeptList = deptManage.getDirectSubDeptListByParentId(hrmsProperties.getDeptTree().getRootId());
        // authDeptIdList为空代表有全量部门权限
        if (authDeptIdList.isEmpty()) {
            return this.convert2Option(firstLevelDeptList);
        }
        // 提取有权限部门的部门路径 去重后得到部门路径中所有部门ID 若一级部门ID在其中存在则代表该一级部门可见
        List<HrmsEntDeptDO> authDeptList = deptManage.getDeptListByIdList(authDeptIdList);
        List<Long> visibleDeptIdList = authDeptList.stream()
                .map(HrmsEntDeptDO::getDeptPath)
                .map(CommonUtils::splitIds2List)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<HrmsEntDeptDO> visibleFirstLevelDeptList = firstLevelDeptList.stream()
                .filter(s -> visibleDeptIdList.contains(s.getId()))
                .collect(Collectors.toList());
        return this.convert2Option(visibleFirstLevelDeptList);
    }

    @Override
    public List<UserSelectorBO> getVisibleUserAssociateList(OperatorUserAssociateQueryParam param) {
        // 处理部门范围
        this.handleDeptRange(param);
        UserAssociateConditionBuilder condition = BeanUtils.convert(param, UserAssociateConditionBuilder.class);
        // 处理数据权限条件
        this.handlePermissionCondition(param.getOperatorPermissionType(), condition);
        List<HrmsUserInfoDO> userList = userManage.getUserByAssociateCondition(condition);
        return userList.stream()
                .map(s -> UserSelectorBO.builder()
                        .id(s.getId())
                        .userCode(s.getUserCode())
                        .userName(BusinessFieldUtils.getUnifiedUserName(s.getUserName(), s.getUserNameEn()))
                        .userAvatar(s.getProfilePhotoUrl())
                        .build())
                .collect(Collectors.toList());
    }

    private List<BaseOptionDTO> convert2Option(List<HrmsEntDeptDO> deptList) {
        return deptList.stream()
                .filter(s -> CommonStatusEnum.ACTIVE.getCode().equals(s.getStatus()))
                .map(s -> BaseOptionDTO.builder()
                        .key(s.getId().toString())
                        .value(s.getDeptCode())
                        .label(BusinessFieldUtils.getUnifiedDeptName(s.getDeptNameCn(), s.getDeptNameEn()))
                        .build())
                .collect(Collectors.toList());
    }

    private void handleDeptRange(OperatorUserAssociateQueryParam param) {
        // 无部门传参或指定主管权限时 无需处理部门范围
        if (CollectionUtils.isEmpty(param.getDeptIdList())
                || UserPermissionTypeEnum.LEADER.getType().equals(param.getOperatorPermissionType())) {
            return;
        }
        // 部门范围是否包含多级子部门选择是时 部门范围=传参部门+全部多级子部门
        if (param.getIsDeptRangeRecursive()) {
            List<HrmsEntDeptDO> recursiveSubDeptList = deptManage.getRecursiveSubDeptListByParentId(param.getDeptIdList());
            List<Long> recursiveSubDeptIdList = recursiveSubDeptList.stream()
                    .map(HrmsEntDeptDO::getId)
                    .collect(Collectors.toList());
            param.getDeptIdList().addAll(recursiveSubDeptIdList);
        }
    }

    private void handlePermissionCondition(Integer operatorPermissionType, UserAssociateConditionBuilder condition) {
        condition.setIsPermissionLimit(Boolean.TRUE);
        condition.setIncludeUserAvatar(Boolean.TRUE);
        if (UserPermissionTypeEnum.DEPT.getType().equals(operatorPermissionType)) {
            // 部门主数据权限时取权限返回内及传参部门的交集
            condition.setDeptIdList(userAuthorizeHelper.getFilterDeptIdList(condition.getDeptIdList()));
        } else if (UserPermissionTypeEnum.LEADER.getType().equals(operatorPermissionType)) {
            // 主管数据权限时 传参的部门无效
            Long leaderId = RequestInfoHolder.getUserId();
            condition.setDeptIdList(deptManage.getDeptIdByLeader(leaderId));
            condition.setLeaderId(leaderId);
        } else {
            // 全量数据权限时取传参部门 为空即全量
            condition.setDeptIdList(condition.getDeptIdList());
        }
    }
}
