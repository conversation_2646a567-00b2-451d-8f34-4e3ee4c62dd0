package com.imile.hrms.service.achievement.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 绩效活动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsEventsNameListDTO {


    /**
     * 活动id
     */
    private Long id;

    /**
     * 活动名称
     */
    private String eventName;

    /**
     * 状态
     */
    private String status;

    /**
     * 年
     */
    private Integer year;

    /**
     * 考核周期类型
     */
    private String cycleType;

    /**
     * 部门负责人是否可填写组织目标
     */
    private Integer isLeaderEditable;

    /**
     * 开启考核(开始自评) 01开启 02未开启
     */
    private String isEvaluation;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastUpdDate;

}
