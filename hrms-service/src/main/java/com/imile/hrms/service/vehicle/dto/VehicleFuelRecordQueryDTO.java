package com.imile.hrms.service.vehicle.dto;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-17
 * @version: 1.0
 */
@Data
public class VehicleFuelRecordQueryDTO extends ResourceQuery {

    /**
     * 车辆所在国
     */
    private String country;


    /**
     * 网点ID（为空全选）
     */
    private Long stationId;

    /**
     * 车牌号
     */
    private String vehicleNo;

    /**
     * 司机编码/司机系统名称
     */
    private String userCodeOrSysName;

    /**
     * 加油开始时间
     */
    private Date refuelingStartTime;

    /**
     * 加油结束时间
     */
    private Date refuelingEndTime;
}
