package com.imile.hrms.service.probation.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/23
 */
@Data
@Builder
public class UserProbationInfoBO {

    /**
     * 人员试用期ID
     */
    private Long id;

    /**
     * 试用月数
     */
    private Integer probationMonths;

    /**
     * 试用期到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date probationEndDate;

    /**
     * 业务导师名称
     */
    private String bizMentorName;

    /**
     * 思想导师名称
     */
    private String mindsetMentorName;

    /**
     * 转正日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmationDate;

    /**
     * 转正结果
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PROBATION_CONFIRMATION_RESULT, ref = "confirmationResultDesc")
    private Integer confirmationResult;

    /**
     * 转正结果描述
     */
    private String confirmationResultDesc;

    /**
     * 试用期状态（1:待配置试用期 4:待提交目标 7:试用考核中 10:待配置答辩 13:待反馈结果 16:待转正 19:已转正 22:转正失败）
     */
    private Integer probationStatus;
}
