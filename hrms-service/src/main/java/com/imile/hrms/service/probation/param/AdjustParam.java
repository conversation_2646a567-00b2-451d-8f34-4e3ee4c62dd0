package com.imile.hrms.service.probation.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class AdjustParam {

    /**
     * 试用期ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * userId 人员档案修改试用期月
     */
    private Long userId;

    /**
     * 试用期月
     */
    private Integer probationMonths;

    /**
     * 入职日期
     */
    private Date entryDate;

    /**
     * 是否来自员工档案
     */
    private Boolean isFromEmployee;
}
