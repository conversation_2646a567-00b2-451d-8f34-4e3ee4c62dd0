package com.imile.hrms.service.newAttendance.punchConfig;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.EntryStatusEnum;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.common.util.DateConvertUtils;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigRangeDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 添加打卡配置范围服务
 *
 * <AUTHOR> chen
 * @Date 2025/2/16
 * @Description
 */
@Slf4j
@Service
public class PunchConfigRangeAddService {

//    private final PunchConfigDao punchConfigDao;
//    private final PunchConfigRangeDao punchConfigRangeDao;

    @Autowired
    private PunchConfigDaoFacade punchConfigDaoFacade;
    @Autowired
    private HrmsUserEntryRecordManage userEntryRecordManage;

    /**
     * 为指定的考勤用户添加打卡配置范围
     *
     * @param attendanceUser 考勤用户
     * @return 添加成功返回true，否则返回false
     */
    public boolean addPunchConfigRange(AttendanceUser attendanceUser) {
        return addPunchConfigRange(attendanceUser, this::getEffectiveTimeForAttendance);
    }

    /**
     * 为指定的考勤用户添加仓库打卡配置范围
     *
     * @param attendanceUser 考勤用户
     * @return 添加成功返回true，否则返回false
     */
    public boolean addWarehousePunchConfigRange(AttendanceUser attendanceUser) {
        return addPunchConfigRange(attendanceUser, this::getEffectiveTimeForWarehouse);
    }


    private boolean addPunchConfigRange(AttendanceUser attendanceUser,
                                        Function<Date, Date> effectiveTimeCalculator) {
//        List<PunchConfigDO> configList = punchConfigDao.listPunchConfigByCountryAndType(
//                attendanceUser.getLocationCountry(), null);
        List<PunchConfigDO> configList = punchConfigDaoFacade.getConfigAdapter().selectConfigByCompanyAndTypeNew(
                attendanceUser.getLocationCountry(), null);
        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }

        PunchConfigDO defaultConfig = getDefaultConfig(configList);
        if (defaultConfig == null) {
            return false;
        }
        Date effectTime = new Date();
        //查询入职确认时间作为日历适用范围生效时间
        Optional<HrmsUserEntryRecordDO> userEntryRecordOptional = userEntryRecordManage.selectUserEntryByUserIds(Collections.singletonList(attendanceUser.getId()))
                .stream().filter(item -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), item.getEntryStatus())).findFirst();
        if (userEntryRecordOptional.isPresent()) {
            HrmsUserEntryRecordDO userEntryRecordDO = userEntryRecordOptional.get();
            if (Objects.nonNull(userEntryRecordDO.getConfirmDate())) {
                effectTime = userEntryRecordDO.getConfirmDate();
            }
        }
        // 先找能匹配到的自定义打卡配置
        Optional<PunchConfigDO> matchingCustomConfig = findMatchingCustomConfig(configList, attendanceUser.getDeptId());
        if (matchingCustomConfig.isPresent()) {
            return addConfigRange(matchingCustomConfig.get(), attendanceUser,
                    RangeTypeEnum.DEPT.getCode(), BusinessConstant.Y, effectiveTimeCalculator.apply(effectTime));
        }

        // 回退到默认打卡配置。看是不是免打卡
        if (isNoPunchDepartment(defaultConfig, attendanceUser.getDeptId())) {
            return addConfigRange(defaultConfig, attendanceUser,
                    RangeTypeEnum.DEPT.getCode(), BusinessConstant.N, effectiveTimeCalculator.apply(effectTime));
        }

        return addConfigRange(defaultConfig, attendanceUser,
                RangeTypeEnum.DEFAULT.getCode(), BusinessConstant.Y, effectiveTimeCalculator.apply(effectTime));
    }

    private PunchConfigDO getDefaultConfig(List<PunchConfigDO> configList) {
        return configList.stream()
                .filter(o -> Objects.equals(o.getIsDefault(), BusinessConstant.Y))
                .findFirst()
                .orElse(null);
    }

    private Optional<PunchConfigDO> findMatchingCustomConfig(List<PunchConfigDO> configList, Long deptId) {
        return configList.stream()
                .filter(PunchConfigDO::areCustom)
                .filter(o -> o.containsDeptId(deptId))
                .findFirst();
    }


//    private boolean isDepartmentInConfig(PunchConfigDO config, Long deptId) {
//        return getDepartmentIds(config.getDeptIds()).contains(deptId);
//    }

    private boolean isNoPunchDepartment(PunchConfigDO config, Long deptId) {
        List<Long> noPunchDeptIds = config.listDeptIds();
        return noPunchDeptIds.contains(deptId);
    }

    private Date getEffectiveTimeForAttendance(Date currentDate) {
        try {
            return DateConvertUtils.getMinTime(currentDate);
        } catch (Exception e) {
            return currentDate;
        }
    }

    private Date getEffectiveTimeForWarehouse(Date currentDate) {
        // 获取当前时间减一天的时间 昨天
        DateTime yesterday = DateUtil.offsetDay(currentDate, -1);
        return DateUtil.beginOfDay(yesterday);
    }

    private boolean addConfigRange(PunchConfigDO punchConfigDO,
                                   AttendanceUser attendanceUser,
                                   String rangeType,
                                   Integer isNeedPunch,
                                   Date effectTime) {
        if (punchConfigDO == null) {
            return false;
        }

        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        rangeDO.setPunchConfigId(punchConfigDO.getId());
        rangeDO.setBizId(attendanceUser.getId());
        rangeDO.setPunchConfigNo(punchConfigDO.getPunchConfigNo());
        rangeDO.setRangeType(rangeType);
        rangeDO.setEffectTime(effectTime);
        rangeDO.setIsNeedPunch(isNeedPunch);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        BaseDOUtil.fillDOInsert(rangeDO);
//        return punchConfigRangeDao.save(rangeDO);
        punchConfigDaoFacade.getRangeAdapter().saveNew(rangeDO);
        return true;
    }


    /*public boolean addAttendancePunchConfigRange(HrmsUserInfoDO userInfoDO) {
        List<PunchConfigDO> configList = punchConfigDao.listPunchConfigByCountryAndType(
                userInfoDO.getLocationCountry(), null);
        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }
        List<PunchConfigDO> defaultPunchConfigDOList = configList.stream()
                .filter(o -> Objects.equals(o.getIsDefault(), BusinessConstant.Y))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(defaultPunchConfigDOList)) {
            return false;
        }
        PunchConfigDO defaultConfig = defaultPunchConfigDOList.get(0);

        List<PunchConfigDO> customList = configList.stream()
                .filter(o -> Objects.equals(o.getIsDefault(), BusinessConstant.N))
                .collect(Collectors.toList());

        for (PunchConfigDO item : customList) {
            if (StringUtils.isBlank(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(item.getDeptIds().split(","), Long.class));
            if (deptIds.contains(userInfoDO.getDeptId())) {
                return addPunchConfigRangeDO(item, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.Y);
            }
        }

        //属于默认打卡规则，看是不是免打卡
        List<Long> deptIds = new ArrayList<>();
        if (StringUtils.isNotBlank(defaultConfig.getDeptIds())) {
            deptIds = Arrays.asList((Long[]) ConvertUtils.convert(defaultConfig.getDeptIds().split(","), Long.class));
        }
        if (deptIds.contains(userInfoDO.getDeptId())) {
            return addPunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.N);
        }
        return addPunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEFAULT.getCode(), BusinessConstant.Y);
    }

    public boolean addWarehouseAttendancePunchConfigRange(HrmsUserInfoDO userInfoDO) {
        List<PunchConfigDO> configList = punchConfigDao.listPunchConfigByCountryAndType(userInfoDO.getLocationCountry(), null);
        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }
        List<PunchConfigDO> defaultPunchConfigDOList = configList.stream()
                .filter(o -> Objects.equals(o.getIsDefault(), BusinessConstant.Y))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultPunchConfigDOList)) {
            return false;
        }
        PunchConfigDO defaultConfig = defaultPunchConfigDOList.get(0);

        List<PunchConfigDO> customList = configList.stream()
                .filter(o -> Objects.equals(o.getIsDefault(), BusinessConstant.N))
                .collect(Collectors.toList());

        for (PunchConfigDO item : customList) {
            if (StringUtils.isBlank(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(item.getDeptIds().split(","), Long.class));
            if (deptIds.contains(userInfoDO.getDeptId())) {
                return addWarehousePunchConfigRangeDO(item, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.Y);
            }
        }

        //属于默认打卡规则，看是不是免打卡
        List<Long> deptIds = new ArrayList<>();
        if (StringUtils.isNotBlank(defaultConfig.getDeptIds())) {
            deptIds = Arrays.asList((Long[]) ConvertUtils.convert(defaultConfig.getDeptIds().split(","), Long.class));
        }
        if (deptIds.contains(userInfoDO.getDeptId())) {
            return addWarehousePunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.N);
        }
        return addWarehousePunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEFAULT.getCode(), BusinessConstant.Y);
    }

    private boolean addPunchConfigRangeDO(PunchConfigDO punchConfigDO,
                                          HrmsUserInfoDO userInfoDO,
                                          String rangeType,
                                          Integer isNeedPunch) {
        if (punchConfigDO == null) {
            return false;
        }
        // 直接新增一条用户的数据
        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        rangeDO.setPunchConfigId(punchConfigDO.getId());
        rangeDO.setBizId(userInfoDO.getId());
        rangeDO.setPunchConfigNo(punchConfigDO.getPunchConfigNo());
        rangeDO.setRangeType(rangeType);
        Date dateMin = new Date();
        try {
            dateMin = DateConvertUtils.getMinTime(new Date());
        } catch (Exception ignored) {

        }
        rangeDO.setEffectTime(dateMin);
        rangeDO.setIsNeedPunch(isNeedPunch);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        BaseDOUtil.fillDOInsert(rangeDO);
        return punchConfigRangeDao.save(rangeDO);
    }

    private boolean addWarehousePunchConfigRangeDO(PunchConfigDO punchConfigDO,
                                                   HrmsUserInfoDO userInfoDO,
                                                   String rangeType,
                                                   Integer isNeedPunch) {
        if (punchConfigDO == null) {
            return false;
        }
        // 直接新增一条用户的数据
        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        rangeDO.setPunchConfigId(punchConfigDO.getId());
        rangeDO.setBizId(userInfoDO.getId());
        rangeDO.setPunchConfigNo(punchConfigDO.getPunchConfigNo());
        rangeDO.setRangeType(rangeType);

        // 获取当前时间减一天的时间 昨天
        Date date = new Date();
        DateTime yesterday = DateUtil.offsetDay(date, -1);
        DateTime effectTime = DateUtil.beginOfDay(yesterday);
        log.info("date : {} yesterday : {} effectTime : {}", date, yesterday, effectTime);

        rangeDO.setEffectTime(effectTime);
        rangeDO.setIsNeedPunch(isNeedPunch);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        BaseDOUtil.fillDOInsert(rangeDO);
        return punchConfigRangeDao.save(rangeDO);
    }*/
}
