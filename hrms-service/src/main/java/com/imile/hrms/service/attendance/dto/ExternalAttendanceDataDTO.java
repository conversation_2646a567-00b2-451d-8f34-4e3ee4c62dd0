package com.imile.hrms.service.attendance.dto;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 外部考勤数据
 * @author: taokang
 * @createDate: 2022-8-3
 * @version: 1.0
 */
@Data
public class ExternalAttendanceDataDTO {

    /**
     * 当前用户id
     */
    private Long userId;

    /**
     * 当前用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 当前所属公司
     */
    private String country;

    /**
     * 当前用户部门ID
     */
    private Long deptId;

    /**
     * 当前用户岗位ID
     */
    private Long postId;


    /**
     * 该员工每日出勤情况
     */
    List<ExternalAttendanceDayDTO> dayDTOList;

}
