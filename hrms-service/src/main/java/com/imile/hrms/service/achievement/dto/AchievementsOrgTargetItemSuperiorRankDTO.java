package com.imile.hrms.service.achievement.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 上级排名vo
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsOrgTargetItemSuperiorRankDTO {


    /**
     * 排名
     */
    private Integer rank;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 部门id
     */
    private Long deptId;


    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门名称-英文
     */
    private String deptNameEn;

    /**
     * 负责人
     */
    private String userName;


    /**
     * 本级列表
     */
    private List<AchievementsOrgTargetItemValueListDTO> list;



}
