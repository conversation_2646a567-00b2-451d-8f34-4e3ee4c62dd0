package com.imile.hrms.service.approval.param;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeAddParam
 * {@code @since:} 2024-06-12 15:12
 * {@code @description:}
 */
@Data
public class OverTimeAddParam {

    /**
     * 申请单id(新增不用传)
     */
    private Long formId;

    /**
     * 申请人用户code
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String applyUserCode;
    /**
     * 被申请人用户code
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String userCode;

    /**
     * 日期：年月日
     */
    @ApiModelProperty(value = "日期：年月日")
    private Long dayId;

    /**
     * 加班开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date overTimeStartDate;

    /**
     * 加班结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date overTimeEndDate;

    /**
     * 预计加班时长(入参分钟数)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private BigDecimal overTimeDuration;

    /**
     * 备注
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String remark;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;

}
