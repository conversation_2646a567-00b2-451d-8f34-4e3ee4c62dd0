package com.imile.hrms.service.probation.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 员工试用期周期总结
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
public class UserProbationCycleSummaryBO {

    /**
     * id
     */
    private Long id;

    /**
     * 试用期id
     */
    private Long userProbationId;

    /**
     * 总结详情
     */
    private String summaryDetail;

    /**
     * 相关附件（最多存储五个附件）
     */
    private List<AttachmentDTO> summaryFileList;

    /**
     * 是否提交
     */
    private Integer isSubmit;

    /**
     * 最后修改日期(如果提交状态就是提交日期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastUpdDate;
}
