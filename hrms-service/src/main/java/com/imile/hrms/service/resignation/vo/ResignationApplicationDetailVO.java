package com.imile.hrms.service.resignation.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.imile.hrms.dao.user.dto.AttachmentParamDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 人事变动表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
@Data
public class ResignationApplicationDetailVO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 审批id
     */
    private Long approvalId;

    /**
     * 单据编号
     */
    private String approvalCode;

    /**
     * 单据状态
     */
    private String approvalStatus;

    /**
     * 离职类型:01离职 02辞退
     */
    private String resignationType;

    /**
     * 员工code
     */
    private String userCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 离职原因/辞退原因
     */
    private String dimissionReason;

    /**
     * 离职原因/辞退原因(code)
     */
    private String dimissionCodeReason;

    /**
     * 计划离职日期
     */
    private Date planDimissionDate;

    /**
     * 工作交接人id
     */
    private Long transfereeId;

    /**
     * 附件
     */
    private List<AttachmentParamDTO> attachments;

}
