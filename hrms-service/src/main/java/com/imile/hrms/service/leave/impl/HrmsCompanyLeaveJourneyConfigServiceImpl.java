package com.imile.hrms.service.leave.impl;

import com.imile.hrms.dao.leave.mapper.HrmsCompanyLeaveJourneyConfigMapper;
import com.imile.hrms.service.leave.HrmsCompanyLeaveJourneyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * {@code @author:} han.wang
 * {@code @className:} HrmsCompanyLeaveJourneyConfigServiceImpl
 * {@code @since:} 2024-12-12 20:52
 * {@code @description:}
 */
@Service
@Slf4j
public class HrmsCompanyLeaveJourneyConfigServiceImpl implements HrmsCompanyLeaveJourneyConfigService {

    @Autowired
    private HrmsCompanyLeaveJourneyConfigMapper hrmsCompanyLeaveJourneyConfigMapper;

}
