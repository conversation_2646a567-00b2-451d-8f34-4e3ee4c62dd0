package com.imile.hrms.service.organization;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.organization.model.HrmsOrgBusinessConfigDO;
import com.imile.hrms.service.organization.param.HrmsOrgBusinessConfigSaveParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/2
 */
public interface HrmsOrgBusinessConfigService {

    /**
     * 获取业务负责人配置差异
     *
     * @param deptId                部门ID
     * @param orgBusinessConfigList 业务负责人配置列表
     * @return DataDifferHolder<HrmsOrgBusinessConfigDO>
     */
    DataDifferHolder<HrmsOrgBusinessConfigDO> differ(Long deptId, List<HrmsOrgBusinessConfigSaveParam> orgBusinessConfigList);

    /**
     * 转换业务负责人配置列表
     *
     * @param deptId                部门ID
     * @param orgBusinessConfigList 业务负责人配置列表
     * @return List<HrmsOrgBusinessConfigDO>
     */
    List<HrmsOrgBusinessConfigDO> convertList(Long deptId, List<HrmsOrgBusinessConfigSaveParam> orgBusinessConfigList);
}
