package com.imile.hrms.service.salary.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 数据看板 -- 人力成本曲线
 *
 * <AUTHOR>
 * @since 2024/4/18
 */
@Data
public class HrmsSalaryManPowerCostCurveVO {

    /**
     * 年月（2024-01）
     */
    private String monitorDate;

    /**
     * 总成本 （当月 -- 总）
     */
    private BigDecimal amount;

    /**
     * 总成本分布
     */
    private List<HrmsSalaryDashboardMonitorItemVO> monitorItemList;

}
