package com.imile.hrms.service.probation.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 试用期目标参数
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class UserProbationTargetParam extends UserProbationBaseParam implements Serializable {
    private static final long serialVersionUID = -1899810468640926391L;


    /**
     * 分类
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = Submit.class)
    @Length(max = 1000, message = ValidCodeConstant.LENGTH)
    private String targetType;

    /**
     * 权重
     */
    @Min(value = 1, message = ValidCodeConstant.MIN, groups = Submit.class)
    @Max(value = 100, message = ValidCodeConstant.MAX, groups = Submit.class)
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = Submit.class)
    private Integer targetWeight;

    /**
     * 详情
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = Submit.class)
    @Length(max = 5000)
    private String targetDetail;

}
