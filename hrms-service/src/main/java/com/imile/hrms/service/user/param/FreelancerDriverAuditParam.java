package com.imile.hrms.service.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class FreelancerDriverAuditParam {

    /**
     * 审核记录ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 审核状态（0:待审核 1:已通过 2:已驳回）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    private String rejectionReason;
}
