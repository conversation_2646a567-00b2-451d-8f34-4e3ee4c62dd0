package com.imile.hrms.service.user.result;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class FreelancerDriverAuditRecordListBO {

    /**
     * 审核记录ID
     */
    private Long id;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 审核类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FREELANCER_DRIVER_AUDIT_TYPE, ref = "auditTypeDesc")
    private Integer auditType;

    /**
     * 审批描述
     */
    private String auditTypeDesc;

    /**
     * 审核状态（0:待审核 1:已通过 2:已驳回）
     */
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    private String rejectionReason;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 国家
     */
    private String country;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    private Date lastUpdDate;
}
