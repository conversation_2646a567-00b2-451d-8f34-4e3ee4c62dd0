package com.imile.hrms.service.punch.param.warehouse;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@Data
public class GetClassListByConditionParam implements Serializable {
    private static final long serialVersionUID = 3664266017099291782L;
    private Long ocId;

    private List<Long> ocIdList;

    private String country;
}
