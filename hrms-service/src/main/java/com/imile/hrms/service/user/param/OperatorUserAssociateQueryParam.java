package com.imile.hrms.service.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Data
public class OperatorUserAssociateQueryParam {

    /**
     * 操作人数据权限类型（0:全量数据权限 1:部门主数据权限 2:主管权限）
     *
     * @see com.imile.hrms.common.enums.user.UserPermissionTypeEnum
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operatorPermissionType;

    /**
     * 人员ID列表（一般回显时使用，回显时仅传该字段即可）
     */
    private List<Long> idList;

    /**
     * 人员编码列表（一般回显时使用，回显时仅传该字段即可）
     */
    private List<String> userCodeList;

    /**
     * 关键字（精准匹配人员编码、模糊匹配人员中文名及英文名）
     */
    private String keyword;

    /**
     * 用工类型列表
     */
    private List<String> employeeTypeList;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 部门ID列表（主管权限时不生效）
     */
    private List<Long> deptIdList;

    /**
     * 部门范围是否包含多级子部门（默认否，即只查当前部门）
     */
    private Boolean isDeptRangeRecursive = Boolean.FALSE;
}
