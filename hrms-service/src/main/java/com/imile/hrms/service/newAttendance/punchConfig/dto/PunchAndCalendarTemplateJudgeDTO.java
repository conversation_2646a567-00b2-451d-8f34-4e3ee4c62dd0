package com.imile.hrms.service.newAttendance.punchConfig.dto;

import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/2/16
 * @Description
 */
@Data
public class PunchAndCalendarTemplateJudgeDTO {

    /**
     * 是否存在默认打卡规则  （0 无  1有）
     */
    private Integer isContainDefaultTemplate;

    /**
     * 是否存在默认日历（0 无  1有）
     */
    private Integer isHaveDefaultAttendance;


    public static PunchAndCalendarTemplateJudgeDTO init() {
        return create(BusinessConstant.N, BusinessConstant.N);
    }


    public static PunchAndCalendarTemplateJudgeDTO create(Integer isContainDefaultTemplate, Integer isHaveDefaultAttendance) {
        PunchAndCalendarTemplateJudgeDTO dto = new PunchAndCalendarTemplateJudgeDTO();
        dto.setIsContainDefaultTemplate(isContainDefaultTemplate);
        dto.setIsHaveDefaultAttendance(isHaveDefaultAttendance);
        return dto;
    }
}
