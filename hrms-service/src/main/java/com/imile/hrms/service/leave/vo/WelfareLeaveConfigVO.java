package com.imile.hrms.service.leave.vo;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} WelfareLeaveConfigVO
 * {@code @since:} 2024-04-13 17:37
 * {@code @description:}
 */
@Data
public class WelfareLeaveConfigVO implements Serializable {
    private static final long serialVersionUID = 7663729649175993473L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 适用国家
     */
    private String country;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期简称
     */
    private String leaveShortName;
    /**
     * 是否派遣假
     */
    private Integer isDispatch;
    /**
     * 派遣地国家
     */
    private String dispatchCountry;

    /**
     * 适用性别
     */
    private Integer useSex;

    /**
     * 接收数据库部门id字符串
     */
    private String deptIds;

    /**
     * 部门id集合
     */
    private List<Long> deptIdList;

    /**
     * 部门的名称集合
     */
    private List<String> deptNameList;

    /**
     * 用户id集合
     */
    private List<String> userCodeList;

    /**
     * 用户姓名集合
     */
    private List<String> userNameList;

    /**
     * 接收数据库用工类型字符串
     */
    private String employeeType;

    /**
     * 用工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 接收数据库地区字符串
     */
    private String locationProvince;

    /**
     * 地区集合
     */
    private List<String> locationProvinceList;

    /**
     * 入职日期范围(开始时间)
     */
    private String startEntryDate;

    /**
     * 入职日期范围(结束时间)
     */
    private String endEntryDate;

    /**
     * 使用限制: 1：入职后，2：试用期转正后， 3：入职一年后，4:入职两年后
     */
    private Integer leaveUsageRestrictions;

    /**
     * 请假单位
     */
    private String leaveUnit;
    /**
     * 最小请假时长
     */
    private Integer miniLeaveDuration;

    /**
     * 节假日是否消耗假期：0-否：表示消耗工作日的时间，1-是：表示自然日的时间
     */
    private String consumeLeaveType;

    /**
     * 是否上传附件：0-否，1-是
     */
    private Integer isUploadAttachment;

    /**
     * 上传附件条件：当isUploadAttachment为1时，此字段必填
     */
    private Long uploadAttachmentCondition;

    /**
     * 上传附件单位：DAYS-天 HOURS-小时 MINUTES-分钟
     */
    private String attachmentUnit;

    /**
     * 更新周期：结转规则中，是否永久有效为否时，必填 为year，是否永久有效为是时，不填，或为“”
     */
    private String useCycle;

    /**
     * 有效期预览：假期可用的开始时间，结转规则中，是否永久有效为否时，必填
     */
    private String useStartDate;

    /**
     * 有效期预览：假期可用的结束时间，结转规则中，是否永久有效为否时，必填
     */
    private String useEndDate;

    /**
     * 状态 ACTIVE/DISABLED
     */
    private String status;

    /**
     * 是否带薪: 1：全薪假 2：无薪假 3：阶梯假
     */
    private Integer isSalary;


    /**
     * 发放假期规则数据
     */
    private LeaveConfigIssueRuleVO leaveConfigIssueRule;

    /**
     * 结转规则
     */
    private LeaveConfigCarryOverVO leaveConfigCarryOver;

    /**
     * 国家假期主表明细表：阶段信息。【额度类型是：固定总额度、不限定额度、初始额度为0才会必填，随司龄递增类型，这张表没数据，数据在hrms_company_leave_config_issue_rule_range表】
     */
    List<LeaveItemConfigVO> leaveItemConfigList;
}
