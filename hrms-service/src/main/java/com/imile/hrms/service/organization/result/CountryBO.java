package com.imile.hrms.service.organization.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CountryBO {

    /**
     * 国家ID
     */
    private Long id;

    /**
     * 国家二字码（字段名歧义 未来废弃 统一使用三字码）
     */
    @Deprecated
    private String countryCode;

    /**
     * 国家三字码
     */
    private String code;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国际区号
     */
    private String countryCallingCode;
}
