package com.imile.hrms.service.account.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
@Data
public class AccountRecordSaveParam {

    /**
     * 账号类型编码（详见枚举类：AccountTypeEnum）
     *
     * @see com.imile.hrms.common.enums.account.AccountTypeEnum
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String accountTypeCode;

    /**
     * 账号
     */
    private String account;
}
