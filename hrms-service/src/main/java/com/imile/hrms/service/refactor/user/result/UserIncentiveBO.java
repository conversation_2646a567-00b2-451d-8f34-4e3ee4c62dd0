package com.imile.hrms.service.refactor.user.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
public class UserIncentiveBO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.HRMS_OWN_INCENTIVE_TYPE, ref = "incentiveTypeDesc")
    private String incentiveType;
    private String incentiveTypeDesc;

    /**
     * 主题
     */
    private String incentiveTopic;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date incentiveTime;

    /**
     * 内容
     */
    private String incentiveContent;
}
