package com.imile.hrms.service.promotion.bo;

import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPromotionAnnualReviewResultDetailBO {

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 一级部门ID
     */
    private Long firstDeptId;

    /**
     * 一级部门总人数
     */
    private Integer firstDeptUserCount;

    /**
     * 候选人列表
     */
    private List<PromotionCandidateListBO> candidateList;

    /**
     * 评议意见
     */
    private String reviewOpinions;

    /**
     * 评议附件
     */
    private List<AttachmentDTO> reviewFileList;

    /**
     * 审批单据ID
     */
    private Long approvalId;

    /**
     * 审批状态（1:审批中 2:通过 -1:审批终止 -2:驳回 -3:撤回）
     * 用于前端展示对应操作按钮
     */
    private Integer approvalStatus;
}



