package com.imile.hrms.service.newAttendance.punchConfig.adapter;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.adapter.AbstractAdapter;
import com.imile.hrms.common.adapter.ServiceAdapter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.dao.punch.dto.HrmsAttendanceGpsConfigDTO;
import com.imile.hrms.dao.punch.param.HrmsAttendanceGpsConfigAddParam;
import com.imile.hrms.dao.punch.param.HrmsAttendanceGpsConfigImportParam;
import com.imile.hrms.dao.punch.param.HrmsAttendanceGpsConfigUpdateParam;
import com.imile.hrms.dao.punch.query.AttendanceGpsConfigQuery;
import com.imile.hrms.service.newAttendance.punchConfig.application.PunchGpsConfigApplicationService;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchGpsConfigDeleteCommand;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchGpsConfigDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchGpsConfigImportDTO;
import com.imile.hrms.service.newAttendance.punchConfig.mapstruct.PunchGpsConfigMapstruct;
import com.imile.hrms.service.punch.HrmsAttendanceGpsConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/19
 * @Description
 */
@Slf4j
@Component
public class PunchGpsConfigServiceAdapter extends AbstractAdapter implements ServiceAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    @Resource
    private PunchGpsConfigApplicationService applicationService;
    @Resource
    private HrmsAttendanceGpsConfigService attendanceGpsConfigService;

    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }


    public PaginationResult<HrmsAttendanceGpsConfigDTO> list(AttendanceGpsConfigQuery query) {
        return commonQuery(
                () -> {
                    PaginationResult<PunchGpsConfigDTO> paginationResult = applicationService.list(
                            PunchGpsConfigMapstruct.INSTANCE.toNewPunchGpsConfigQuery(query));
                    return PunchGpsConfigMapstruct.INSTANCE.toOldGpsConfigPage(paginationResult);
                },
                () -> attendanceGpsConfigService.list(query)
        );
    }

    /**
     * 转发到新模块方法
     */
    public void add(HrmsAttendanceGpsConfigAddParam configAddParam) {
        applicationService.add(
                PunchGpsConfigMapstruct.INSTANCE.toAddCommand(configAddParam));
    }

    /**
     * 转发到新模块方法
     */
    public HrmsAttendanceGpsConfigDTO update(HrmsAttendanceGpsConfigUpdateParam configUpdateParam) {
        PunchGpsConfigDTO punchGpsConfigDTO = applicationService.update(
                PunchGpsConfigMapstruct.INSTANCE.toUpdateCommand(configUpdateParam)
        );
        return PunchGpsConfigMapstruct.INSTANCE.toOldGpsConfigDTO(punchGpsConfigDTO);
    }

    /**
     * 转发到新模块方法
     */
    public HrmsAttendanceGpsConfigDTO delete(Long gpsConfigId) {
        PunchGpsConfigDTO punchGpsConfigDTO = applicationService.delete(PunchGpsConfigDeleteCommand.of(gpsConfigId));
        return PunchGpsConfigMapstruct.INSTANCE.toOldGpsConfigDTO(punchGpsConfigDTO);
    }

    /**
     * todo 转发到新模块方法
     */
    public List<HrmsAttendanceGpsConfigImportParam> importGpsConfig(List<HrmsAttendanceGpsConfigImportParam> param) {
        return commonQuery(
                () -> {
                    List<PunchGpsConfigImportDTO> newParam = PunchGpsConfigMapstruct.INSTANCE.toNewGpsConfigImportParam(param);
                    List<PunchGpsConfigImportDTO> punchGpsConfigImportDTOS = applicationService.importGpsConfig(newParam);
                    return PunchGpsConfigMapstruct.INSTANCE.toOldGpsConfigImportParam(punchGpsConfigImportDTOS);
                },
                () -> attendanceGpsConfigService.importGpsConfig(param)
        );
    }

    public List<HrmsAttendanceGpsConfigDTO> selectList(AttendanceGpsConfigQuery query) {
        return commonQuery(
                () -> {
                    List<PunchGpsConfigDTO> punchGpsConfigDTOS = applicationService.selectList(
                            PunchGpsConfigMapstruct.INSTANCE.toNewPunchGpsConfigQuery(query)
                    );
                    return PunchGpsConfigMapstruct.INSTANCE.toOldGpsConfigDTO(punchGpsConfigDTOS);
                },
                () -> attendanceGpsConfigService.selectList(query)

        );
    }
}
