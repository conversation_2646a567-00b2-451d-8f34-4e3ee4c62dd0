package com.imile.hrms.service.user.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class SyncUserDTO implements Serializable {
    private static final long serialVersionUID = 124558538831719088L;

    /**
     * 司机编码
     */
    private String userCode;

    /**
     * 是否虚拟账号 0:否 1：是
     */
    private Integer isVirtual;
    /**
     * 汇报上级
     */
    private Long leaderId;
    /**
     * 员工类型/司机类型
     */
    private String employeeType;

    /**
     * 所属国
     */
    private String originCountry;
}
