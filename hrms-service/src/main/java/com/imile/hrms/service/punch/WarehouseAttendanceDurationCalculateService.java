package com.imile.hrms.service.punch;

import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.model.HrmsWarehousePunchPeriodDO;

import java.util.List;

/**
 * 仓内出勤时长计算服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
public interface WarehouseAttendanceDurationCalculateService {

    /**
     * 计算仓内考勤时长
     */
    List<HrmsWarehousePunchPeriodDO> calculateAttendanceHours(HrmsWarehouseDetailDO warehouseDetailDO, boolean onlyUpdateWarehouseHours);
}
