package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/9 15:49
 * @version: 1.0
 */
@Data
public class SalarySettlementAgentRecordListParam extends ResourceQuery {

    /**
     * 计薪国
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String paymentCountry;

    /**
     * 计薪月份(202308)
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String paymentMonth;
}
