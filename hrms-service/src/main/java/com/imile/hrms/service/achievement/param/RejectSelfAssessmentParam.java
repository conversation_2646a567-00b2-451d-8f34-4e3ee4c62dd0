package com.imile.hrms.service.achievement.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RejectSelfAssessmentParam {

    @NotNull(message = "员工考核id不能为空")
    private Long employeeAppraisalId;

    @NotBlank(message = "拒绝原因不能为空")
    private String leaderRejectRemark;
}
