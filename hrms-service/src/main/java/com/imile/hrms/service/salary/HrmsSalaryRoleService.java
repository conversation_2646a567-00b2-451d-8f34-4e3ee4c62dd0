package com.imile.hrms.service.salary;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.service.salary.param.SalaryRoleUpdateParam;
import com.imile.hrms.service.salary.param.SalaryRoleUserCountryParam;
import com.imile.hrms.service.salary.param.SalaryRoleUserInfoParam;
import com.imile.hrms.service.salary.vo.SalaryRoleInfoVO;
import com.imile.hrms.service.salary.vo.SalaryRoleUserInfoVO;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/11/15 16:22
 * @version: 1.0
 */
public interface HrmsSalaryRoleService {

    List<SalaryRoleInfoVO> list();

    Boolean check();

    void update(SalaryRoleUpdateParam param);

    PaginationResult<SalaryRoleUserInfoVO> userList(SalaryRoleUserInfoParam param);

    List<String> userCountryList(SalaryRoleUserCountryParam param);
}
