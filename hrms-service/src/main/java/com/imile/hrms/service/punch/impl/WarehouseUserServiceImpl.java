package com.imile.hrms.service.punch.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.CountryEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.component.Certificate;
import com.imile.genesis.api.model.param.user.UserAddParam;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.param.user.UserUpdateParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.genesis.api.model.result.user.UserDTO;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoSimpleApiDTO;
import com.imile.hrms.api.blacklist.api.BlacklistApi;
import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.*;
import com.imile.hrms.common.enums.blacklist.BlacklistBanStatusEnum;
import com.imile.hrms.common.enums.punch.FaceRecordStatusEnum;
import com.imile.hrms.common.enums.punch.WarehouseBlackTypeEnum;
import com.imile.hrms.common.enums.user.UserExtendAttrKeyEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.PageUtil;
import com.imile.hrms.dao.blacklist.dto.LockdownInfoDTO;
import com.imile.hrms.dao.face.dao.FaceFeatureDao;
import com.imile.hrms.dao.punch.dao.HrmsFaceRecordDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseBlackListDao;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.punch.model.HrmsFaceRecordDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseBlackListDO;
import com.imile.hrms.dao.user.dto.UserCertificateInfoParamDTO;
import com.imile.hrms.dao.user.dto.UserExtendAttrDTO;
import com.imile.hrms.dao.user.model.HrmsUserCertificateDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.UserExtendAttrDO;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.dao.user.query.UserExtendAttrQuery;
import com.imile.hrms.integration.genesis.RpcUserService;
import com.imile.hrms.integration.hermes.service.VendorService;
import com.imile.hrms.integration.pcs.PcsService;
import com.imile.hrms.integration.recognition.RecognitionService;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigManageAdapter;
import com.imile.hrms.manage.user.HrmsUserCertificateManage;
import com.imile.hrms.manage.user.UserExtendAttrManage;
import com.imile.hrms.service.blacklist.HrmsBlacklistService;
import com.imile.hrms.service.newAttendance.calendar.adapter.CalendarRangeServiceAdapter;
import com.imile.hrms.service.newAttendance.punchConfig.adapter.PunchConfigServiceAdapter;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.punch.WarehouseBaseService;
import com.imile.hrms.service.punch.WarehouseOcService;
import com.imile.hrms.service.punch.WarehouseSupplierService;
import com.imile.hrms.service.punch.WarehouseUserService;
import com.imile.hrms.service.punch.param.warehouse.*;
import com.imile.hrms.service.punch.vo.warehouse.*;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.hrms.service.user.result.UserCertificateBO;
import com.imile.recognition.api.ocr.model.dto.CertificatesDTO;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内用户服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
@Slf4j
@Service
public class WarehouseUserServiceImpl extends WarehouseBaseService implements WarehouseUserService {
    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private RpcUserService rpcUserService;

    @Resource
    private PcsService pcsService;

    @Resource
    private VendorService vendorService;

    @Resource
    private HrmsUserInfoService userInfoService;

    @Resource
    private UserCertificateService userCertificateService;

    @Resource
    private HrmsBlacklistService blacklistService;

    @Resource
    private RecognitionService recognitionService;

    @Resource
    private HrmsUserCertificateManage certificateManage;

    @Resource
    private UserExtendAttrManage userExtendAttrManage;

//    @Resource
//    private HrmsAttendancePunchConfigRangeManage attendancePunchConfigRangeManage;

    @Resource
    private HrmsFaceRecordDao faceRecordDao;

    @Resource
    private FaceFeatureDao faceFeatureDao;

    @Resource
    private BlacklistApi blacklistApi;

    @Resource
    private CalendarRangeServiceAdapter calendarRangeServiceAdapter;

    @Resource
    private PunchConfigManageAdapter punchConfigManageAdapter;

    @Resource
    private PunchConfigServiceAdapter punchConfigServiceAdapter;

    @Resource
    private HrmsWarehouseBlackListDao hrmsWarehouseBlackListDao;

    @Resource
    private TransactionTemplate transactionTemplate;


    @SneakyThrows
    @Override
    public CertificatesVO certificatesUpload(CertificateUploadParam param) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(param.getFile().getOriginalFilename()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(param.getEmployeeType()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);

        String certificateTypeCode = getCertificateType(param.getCountry());
        String fileUrl = ipepIntegration.upload(BusinessConstant.OCR_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        CertificatesDTO certificates = recognitionService.certificatesRecognition(fileUrl, param.getCountry(), certificateTypeCode);
        CertificatesVO certificatesVO = BeanUtils.convert(certificates, CertificatesVO.class);
        certificatesVO.setUrl(fileUrl);
        certificatesVO.setCertificateTypeCode(certificateTypeCode);
        if (StringUtils.isBlank(certificatesVO.getCertificatesCode())) {
            return certificatesVO;
        }

        //根据证件号查询员工
        List<String> userCodes = new ArrayList<>();
        UserCertificateCheckResultDTO checkResult = checkDuplicateCertificate(certificatesVO.getCertificatesCode(), certificateTypeCode, null);
        if (checkResult.getIsRepeat() && CollectionUtils.isNotEmpty(checkResult.getRepeatCertificateList())) {
            userCodes = checkResult.getRepeatCertificateList().stream().map(UserCertificateCheckResultDTO.RepeatCertificate::getOwnerUserCode).collect(Collectors.toList());
        }
        HrmsUserInfoDO lastExistUser = userCodes.stream()
                .map(userInfoDao::getByUserCode)
                .filter(Objects::nonNull)
                .max(Comparator.comparing(HrmsUserInfoDO::getId)).orElse(null);
        if (Objects.nonNull(lastExistUser)) {
            if (BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType()) && !EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(lastExistUser.getEmployeeType())) {
                throw BusinessException.get(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getDesc()));
            }
            certificatesVO.setUserId(lastExistUser.getId());
        }
        return certificatesVO;
    }

    @Override
    public PaginationResult<ReportVO> ocUserList(OcUserParam param) {
        if (StringUtils.isEmpty(param.getCountry())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        if (CollectionUtils.isEmpty(param.getOcIdList())) {
            GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
            ocListParam.setCountry(param.getCountry());
            ocListParam.setCityList(param.getCityList());
            List<OcVO> ocVOS = warehouseOcService.getOcListByCondition(ocListParam);
            if (CollectionUtils.isNotEmpty(ocVOS)) {
                param.setOcIdList(ocVOS.stream().map(OcVO::getOcId).distinct().collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(param.getVendorIdList())) {
            GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
            ocListParam.setOcIdList(param.getOcIdList());
            List<VendorVO> vendorVOS = warehouseSupplierService.getVendorListByCondition(ocListParam);
            VendorVO vendorVO = new VendorVO();
            vendorVO.setVendorId(BusinessConstant.IMILE_VIRTUAL_VENDOR_ID);
            vendorVOS.add(vendorVO);
            param.setVendorIdList(vendorVOS.stream().map(VendorVO::getVendorId).distinct().collect(Collectors.toList()));
        }

        Set<String> searchKeyUserCodeList = new HashSet<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<HrmsUserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            Set<String> userCodeSet = userInfoDOList.stream().map(HrmsUserInfoDO::getUserCode).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(userCodeSet)) {
                searchKeyUserCodeList.addAll(userCodeSet);
            }
            searchFlag = true;
        }

        if (StringUtils.isNotEmpty(param.getEmploymentForm())) {
            UserExtendAttrQuery query = UserExtendAttrQuery
                    .builder()
                    .attrKey(UserExtendAttrKeyEnum.EMPLOYEE_FORM.getCode())
                    .attrValue(param.getEmploymentForm())
                    .country(param.getCountry())
                    .status(StatusEnum.ACTIVE.getCode())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode()).build();
            Set<String> userCodes = userExtendAttrManage.selectByAttrKeyCondition(query).stream().map(UserExtendAttrDTO::getUserCode).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(userCodes)) {
                searchKeyUserCodeList.addAll(userCodes);
            }
            if (!searchFlag) {
                searchFlag = true;
            }
        }
        //本次页面要查询和展示的证件类型
        String certificateTypeCode = StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.BRA.getCode(), param.getCountry()) ? CertificateTypeEnum.ID_CARD.getCode() : CertificateTypeEnum.INE.getCode();

        if (StringUtils.isNotBlank(param.getCertificatesCode())) {
            Set<String> userCodeList = new HashSet<>();
            UserCertificateCheckResultDTO checkResult = checkDuplicateCertificate(param.getCertificatesCode(), certificateTypeCode, null);
            if (checkResult.getIsRepeat() && CollectionUtils.isNotEmpty(checkResult.getRepeatCertificateList())) {
                userCodeList = checkResult.getRepeatCertificateList().stream().map(UserCertificateCheckResultDTO.RepeatCertificate::getOwnerUserCode).collect(Collectors.toSet());
            }
            if (CollectionUtils.isNotEmpty(userCodeList)) {
                searchKeyUserCodeList.addAll(userCodeList);
            }
            if (!searchFlag) {
                searchFlag = true;
            }
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserCodeList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<String> employeeTypeList = BusinessConstant.OC_EMPLOYEE_TYPE;
        if (CollectionUtils.isNotEmpty(param.getEmployeeTypeList())) {
            employeeTypeList = param.getEmployeeTypeList();
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .locationCountry(param.getCountry())
                .locationCityList(param.getCityList())
                .ocIds(param.getOcIdList())
                .vendorIdList(param.getVendorIdList())
                .userCodes(new ArrayList<>(searchKeyUserCodeList))
                .employeeTypes(employeeTypeList)
                .isDriver(BusinessConstant.N)
                .build();
        Page<HrmsUserInfoDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.userList(userDaoQuery);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<Long> ocIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getDeptId).distinct().collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);
        List<Long> userIdList = userInfoDOList.stream()
                .map(HrmsUserInfoDO::getId)
                .collect(Collectors.toList());

        //获取用户所有的证件类型
        List<UserCertificateBO> userCertificateBOList = userCertificateService.getUserCertificateList(userIdList, certificateTypeCode);

        //查询用工形式
        Map<Long, UserExtendAttrDO> userExtendAttrMap = userExtendAttrManage.getUserExtendAttrList(userIdList, Collections.singletonList(UserExtendAttrKeyEnum.EMPLOYEE_FORM.getCode()))
                .stream().collect(Collectors.toMap(UserExtendAttrDO::getUserId, Function.identity()));

        List<ReportVO> collect = userInfoDOList.stream().map(user -> {
            ReportVO ocUserVO = new ReportVO();
            ocUserVO.setCountry(user.getLocationCountry());
            ocUserVO.setCity(user.getLocationCity());
            ocUserVO.setOcId(user.getDeptId());
            ocUserVO.setOcName(ocMap.get(user.getDeptId()));
            ocUserVO.setVendorId(user.getVendorId());
            ocUserVO.setVendorCode(user.getVendorCode());
            if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(ocUserVO.getVendorId())) {
                ocUserVO.setVendorName(user.getVendorName());
            }
            ocUserVO.setUserId(user.getId());
            ocUserVO.setUserCode(user.getUserCode());
            ocUserVO.setUserName(user.getUserName());
            ocUserVO.setSex(user.getSex());
            ocUserVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
            ocUserVO.setEmployeeType(user.getEmployeeType());
            List<UserCertificateBO> shouCertificateList = userCertificateBOList.stream()
                    .filter(item -> item.getUserId().equals(user.getId())
                            && StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), certificateTypeCode))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shouCertificateList)) {
                ocUserVO.setCertificatesCode(shouCertificateList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getCertificateCode());
            }
            UserExtendAttrDO userExtendAttrDO = userExtendAttrMap.get(user.getId());
            if (Objects.nonNull(userExtendAttrDO)) {
                ocUserVO.setEmploymentForm(userExtendAttrDO.getAttrValue());
            }
            return ocUserVO;
        }).collect(Collectors.toList());
        return PageUtil.get(collect, page, param);
    }

    @Override
    public WarehouseOcUserVO ocUserDetail(Long id) {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .userId(id)
                .isDriver(BusinessConstant.N)
                .build();
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.userList(userDaoQuery);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return new WarehouseOcUserVO();
        }

        HrmsUserInfoDO user = userInfoDOList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
        Map<Long, String> ocMap = getOcMap(Collections.singletonList(user.getDeptId()));
        //获取用户所有的证件类型
        List<UserCertificateBO> userCertificateBOList = userCertificateService.getUserCertificateList(user.getId());
        WarehouseOcUserVO ocUserVO = new WarehouseOcUserVO();
        ocUserVO.setOcId(user.getDeptId());
        ocUserVO.setOcName(ocMap.get(user.getDeptId()));
        ocUserVO.setVendorId(user.getVendorId());
        ocUserVO.setVendorCode(user.getVendorCode());
        if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(ocUserVO.getVendorId())) {
            ocUserVO.setVendorName(user.getVendorName());
        }
        ocUserVO.setUserId(user.getId());
        ocUserVO.setUserCode(user.getUserCode());
        ocUserVO.setUserName(user.getUserName());
        ocUserVO.setSex(user.getSex());
        ocUserVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
        ocUserVO.setEmployeeType(user.getEmployeeType());
        ocUserVO.setProfilePhotoUrlKey(user.getProfilePhotoUrl());
        ocUserVO.setCountry(user.getLocationCountry());
        //证件信息构建
        userCertificateDataBuild(user, user.getLocationCountry(), userCertificateBOList, ocUserVO);
        //巴西查询用工形式
        ocUserVO.setEmploymentForm(getEmploymentForm(user.getId()));
        return ocUserVO;
    }

    @Override
    public RecordListVO getUser(Long userId) {
        if (Objects.isNull(userId)) {
            return new RecordListVO();
        }
        HrmsUserInfoDO user = userInfoDao.getById(userId);
        if (Objects.isNull(user)) {
            return new RecordListVO();
        }
        RecordListVO recordListVO = new RecordListVO();
        recordListVO.setUserId(user.getId());
        recordListVO.setUserCode(user.getUserCode());
        recordListVO.setUserName(user.getUserName());
        recordListVO.setFacePhotoKey(user.getProfilePhotoUrl());
        recordListVO.setVendorId(user.getVendorId());
        recordListVO.setVendorCode(user.getVendorCode());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(user.getVendorCode()));
        recordListVO.setVendorName(vendorMap.get(user.getVendorCode()));
        recordListVO.setHaveFace(StringUtils.isNotBlank(user.getProfilePhotoUrl()));
        HrmsFaceRecordDO faceRecord = faceRecordDao.selectLastOne(userId, null, FaceRecordStatusEnum.EFFECTIVE.getCode());
        recordListVO.setHaveRecognition(Objects.nonNull(faceRecord));
        return recordListVO;
    }

    @Override
    public List<UserVO> getUserListByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return Collections.emptyList();
        }
        List<HrmsUserCertificateDO> certificateDOList = certificateManage.selectCertificateByCode(Collections.singletonList(code));
        //证件信息表userCode字段数据为空
        Set<String> userCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(certificateDOList)) {
            List<Long> userIdList = certificateDOList.stream().map(HrmsUserCertificateDO::getUserId).collect(Collectors.toList());
            userCodes = userInfoDao.getByUserIds(userIdList).stream().map(HrmsUserInfoDO::getUserCode).collect(Collectors.toSet());
        }
        userCodes.add(code);
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.userListByUserCodes(new ArrayList<>(userCodes));

        if (CollectionUtils.isEmpty(userInfoDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_DOES_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_DOES_NOT_EXIST.getDesc()));
        }
        HrmsUserInfoDO userInfoDO = userInfoDOList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
        if (EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(userInfoDO.getEmployeeType())) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_DOES_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_DOES_NOT_EXIST.getDesc()));
        }
        if (!StringUtils.equalsIgnoreCase(userInfoDO.getWorkStatus(), BusinessConstant.WORK_STATUS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.PERSON_HAS_RESIGNED.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PERSON_HAS_RESIGNED.getDesc()));
        }
        if (!StringUtils.equalsIgnoreCase(userInfoDO.getStatus(), BusinessConstant.STATUS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getDesc()));
        }

        UserVO userVO = new UserVO();
        userVO.setUserId(userInfoDO.getId());
        userVO.setUserCode(userInfoDO.getUserCode());
        userVO.setUserName(BusinessFieldUtils.getUnifiedUserName(userInfoDO.getUserName(), userInfoDO.getUserNameEn()));
        return Collections.singletonList(userVO);
    }

    @Override
    public SubmitCheckVO submitCheck(AddUserParam param) {
        saveOrUpdateUserParamCheck(param);
        //获取用户当前证件照片：只有巴西需要,并且返回的是采购那边入职使用的照片，即身份证照片
        SubmitCheckVO resultVO = new SubmitCheckVO();
        if (!StringUtils.equalsAnyIgnoreCase(param.getCountry(), CountryCodeEnum.BRA.getCode())) {
            return resultVO;
        }
        HrmsUserInfoDO user = userInfoDao.getById(param.getUserId());
        resultVO.setUserCode(user.getUserCode());
        List<UserCertificateBO> userCertificateBOList = userCertificateService.getUserCertificateList(user.getId()).stream()
                .filter(item -> StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), CertificateTypeEnum.ID_CARD.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userCertificateBOList)) {
            resultVO.setCertificateFrontPath(userCertificateBOList.get(0).getCertificateFrontPath());
            resultVO.setCertificateFrontUrl(userCertificateBOList.get(0).getCertificateFrontUrl());
            List<WarehouseUserCertificateParam> certificateList = BeanUtils.convert(WarehouseUserCertificateParam.class, userCertificateBOList);
            resultVO.setCertificateList(certificateList);
            return resultVO;
        }
        return resultVO;
    }

    @Override
    public List<UserVO> searchUserList(String searchKey) {
        if (StringUtils.isEmpty(searchKey)) {
            return Collections.emptyList();
        }
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(searchKey);
        return userInfoDOList.stream().map(user -> {
            UserVO userVO = new UserVO();
            userVO.setUserId(user.getId());
            userVO.setUserCode(user.getUserCode());
            userVO.setUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()));
            return userVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean isWarehouseLaborSupport(HrmsUserInfoDO userInfoDO) {
        return Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), userInfoDO.getEmployeeType())
                && Objects.equals(BusinessConstant.Y, userInfoDO.getIsWarehouseStaff())
                && Lists.newArrayList(CountryEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfoDO.getLocationCountry())
                && Objects.equals(BusinessConstant.N, userInfoDO.getIsDriver());
    }

    @Override
    public Boolean isWarehouseSupportUser(HrmsUserInfoDO userInfoDO) {
        return ObjectUtil.equal(userInfoDO.getIsWarehouseStaff(), BusinessConstant.Y)
                && com.imile.hrms.common.enums.EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE.contains(userInfoDO.getEmployeeType())
                && Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfoDO.getLocationCountry())
                && ObjectUtil.notEqual(userInfoDO.getIsDriver(), BusinessConstant.Y);
    }

    @Override
    public Boolean isWarehouseEmployeeSupport(HrmsUserInfoDO userInfoDO) {
        return ObjectUtil.equal(userInfoDO.getIsWarehouseStaff(), BusinessConstant.Y)
                && com.imile.hrms.common.enums.EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(userInfoDO.getEmployeeType())
                && Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfoDO.getLocationCountry())
                && ObjectUtil.notEqual(userInfoDO.getIsDriver(), BusinessConstant.Y);
    }

    @Override
    public UserVO saveOrUpdateUser(AddUserParam param) {
        BusinessLogicException.checkTrue(!StringUtils.equalsIgnoreCase(BusinessConstant.LABOR_DISPATCH, param.getEmployeeType()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), "employeeType");
        //数据校验
        saveOrUpdateUserParamCheck(param);
        HrmsUserInfoDO user = userInfoDao.getById(param.getUserId());
        EntOcApiDTO oc = warehouseOcService.getOc(param.getOcId());
        List<UserCertificateSaveParam> certificateSaveParamList = new ArrayList<>();
        buildUserCertificateSaveParam(param, certificateSaveParamList);
        //巴西劳务派遣，一定是在采购那边先入职成功了，所以一定是走下面的updateUser方法
        if (Objects.isNull(user)) {
            return saveUser(param, oc, certificateSaveParamList);
        } else {
            return updateUser(param, user, oc, certificateSaveParamList);
        }
    }

    @Override
    public void differentNotice(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        //调用采购通知接口
        pcsService.addEmployeeCertificateDiff(userCode);
    }


    @Override
    public void updateUserOcAndVendor(UpdateUserParam param) {
        HrmsUserInfoDO user = Optional.ofNullable(userInfoDao.getById(param.getUserId()))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc())));
        if (!EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType())) {
            throw BusinessException.get(OcrErrorCodeEnum.NON_LABOR_DISPATCH_EMPLOYEE_CANNOT_UPDATE.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.NON_LABOR_DISPATCH_EMPLOYEE_CANNOT_UPDATE.getDesc()));
        }

        UserUpdateParam build = UserUpdateParam.builder()
                .userCode(user.getUserCode())
                .vendorCode(param.getVendorCode())
                .operationScene(OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE)
                .build();
        rpcUserService.updateUser(build);
    }


    @Override
    public void saveUserInfo(HrmsUserInfoDO newUser, List<UserCertificateSaveParam> userCertificateSaveParamList, EntOcApiDTO oc) {
        if (CollectionUtils.isNotEmpty(userCertificateSaveParamList)) {
            userCertificateService.saveUserCertificate(newUser.getId(), userCertificateSaveParamList,
                    OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE);
        }
        HrmsUserInfoDO ocPrincipal = userInfoDao.getByUserCode(oc.getOcPrincipalCode());
        if (Objects.isNull(ocPrincipal)) {
            return;
        }
        newUser.setLeaderId(ocPrincipal.getId());
        newUser.setLeaderName(ocPrincipal.getUserNameEn());
        BaseDOUtil.fillDOUpdate(newUser);
        userInfoDao.saveOrUpdate(newUser);
    }

    @Override
    public String getEmploymentForm(Long userId) {
        List<UserExtendAttrDO> userExtendAttrList = userExtendAttrManage.getUserExtendAttrList(userId);
        if (CollectionUtils.isEmpty(userExtendAttrList)) {
            return null;
        }
        UserExtendAttrDO userExtendAttrDO = userExtendAttrList.stream().filter(userExtend -> Objects.equals(UserExtendAttrKeyEnum.EMPLOYEE_FORM.getCode(), userExtend.getAttrKey())).findFirst().orElse(new UserExtendAttrDO());
        return userExtendAttrDO.getAttrValue();
    }


    @Override
    public void refreshWarehouseEmployeeEmployeeForm() {
        int currentPage = 1;
        int pageSize = 1000;
        UserDaoQuery userQuery = UserDaoQuery
                .builder()
                .locationCountry(CountryCodeEnum.MEX.getCode())
                .employeeTypes(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()))
                .status(StatusEnum.ACTIVE.getCode())
                .isWarehouseStaff(1)
                .isDriver(0).build();
        PageInfo<HrmsUserInfoDO> pageInfo;
        do {
            Page<HrmsUserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
            // 总记录数
            List<HrmsUserInfoDO> pageUserInfoList = pageInfo.getList();
            currentPage++;
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                continue;
            }

            pageUserInfoList.forEach(user -> {
                UserUpdateParam build = UserUpdateParam.builder()
                        .userCode(user.getUserCode())
                        .userName(user.getUserName())
                        .operationScene(OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE)
                        .build();
                Map<String, Object> bizData = new HashMap<>();
                bizData.put(UserExtendAttrKeyEnum.EMPLOYEE_FORM.getCode(), EmployeeFormEnum.SHORT_TERM_WORKER.getCode());
                build.setBizData(bizData);
                rpcUserService.updateUser(build);
            });
            log.info("refreshWarehouseEmployeeEmployeeForm currentPage:{},total:{}", currentPage, pageInfo.getTotal());
        } while (currentPage <= pageInfo.getPages());
    }

    @NotNull
    private UserVO saveUser(AddUserParam param, EntOcApiDTO oc, List<UserCertificateSaveParam> certificateSaveParamList) {
        //判断证件号是否在黑名单中
        checkExistBlackList(param, certificateSaveParamList);

        //证件重复校验
        certificateRepeatCheck(param, certificateSaveParamList, null);

        UserAddParam build = UserAddParam.builder()
                .userName(param.getUserName())
                .userSex(param.getSex())
                .employeeType(EmploymentTypeEnum.OS_FIXED_SALARY.getCode())
                .ocCode(oc.getOcCode())
                .vendorCode(param.getVendorCode())
                .isDriver(BusinessConstant.N)
                .nationalityCode(CountryCodeEnum.convert2StandardCountryCode(oc.getCountry()))
                .locationCountry(oc.getCountry())
                .locationProvince(StringUtils.isBlank(oc.getProvince()) ? BusinessConstant.RUNG : oc.getProvince())
                .locationCity(StringUtils.isBlank(oc.getCity()) ? BusinessConstant.RUNG : oc.getCity())
                .operationScene(OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE)
                .userCertificateList(certificateSaveParamList)
                .build();
        if (Objects.equals(CountryCodeEnum.MEX.getCode(), oc.getCountry())) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(UserExtendAttrKeyEnum.EMPLOYEE_FORM.getCode(), EmployeeFormEnum.SHORT_TERM_WORKER.getCode());
            build.setBizData(paramMap);
        }
        String userCode = rpcUserService.addUser(build);
        HrmsUserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        transactionTemplate.executeWithoutResult(x -> {
            try {
                saveUserInfo(userInfoDO, null, oc);
                //绑定用户日历和打卡规则
//        configRangeService.addWarehouseAttendanceConfigRange(userInfoDO);
                calendarRangeServiceAdapter.addWarehouseCalendarConfigRange(userInfoDO);
                //        punchConfigRangeService.addWarehouseAttendancePunchConfigRange(userInfoDO);
                punchConfigServiceAdapter.addWarehousePunchConfigRange(userInfoDO);
            } catch (Exception e) {
                x.setRollbackOnly();
            }
        });
        UserVO userVO = new UserVO();
        userVO.setUserId(userInfoDO.getId());
        userVO.setUserCode(userInfoDO.getUserCode());
        userVO.setUserName(BusinessFieldUtils.getUnifiedUserName(userInfoDO.getUserName(), userInfoDO.getUserNameEn()));
        userVO.setFirstEnter(Boolean.TRUE);
        return userVO;
    }

    private void checkExistBlackList(AddUserParam param, List<UserCertificateSaveParam> certificateSaveParamList) {
        List<UserCertificateInfoParamDTO> certificateInfoDTOList = certificateSaveParamList.stream().map(certificate -> {
            UserCertificateInfoParamDTO userCertificateInfoParamDTO = new UserCertificateInfoParamDTO();
            userCertificateInfoParamDTO.setCertificateCode(certificate.getCertificateCode());
            userCertificateInfoParamDTO.setCertificateTypeCode(certificate.getCertificateTypeCode());
            return userCertificateInfoParamDTO;
        }).collect(Collectors.toList());
        LockdownInfoDTO lockdownInfo = blacklistService.checkExitBlacklist(null, certificateInfoDTOList);
        if (Objects.nonNull(lockdownInfo)) {
            //判断证件号是否在黑名单中
            addBlackListRecord(param, lockdownInfo);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.BLACKLIST_USER_CANNOT_ENTER, lockdownInfo.getReason());
        }
    }

    @NotNull
    private UserVO updateUser(AddUserParam param, HrmsUserInfoDO user, EntOcApiDTO oc, List<UserCertificateSaveParam> certificateSaveParamList) {
        //判断是否是黑名单用户
        RpcResult<BlacklistInfoDTO> result = blacklistApi.getBlacklistInfo(user.getUserCode());
        if (result.isSuccess() && Objects.nonNull(result.getResult()) && BlacklistBanStatusEnum.getBanStatusList().contains(result.getResult().getBanStatus())) {
            //判断证件号是否在黑名单中
            addBlackListRecord(param, user, result);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.BLACKLIST_USER_CANNOT_ENTER, result.getResult().getReason());
        }
        if (!Objects.equals(BusinessConstant.STATUS, user.getStatus())) {
            //账号停用
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getDesc()));
        }

        //证件重复校验
        certificateRepeatCheck(param, certificateSaveParamList, user.getUserCode());

        UserUpdateParam build = UserUpdateParam.builder()
                .userCode(user.getUserCode())
                .userName(param.getUserName())
                .operationScene(OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE)
                .build();
        if (Objects.equals(CountryCodeEnum.MEX.getCode(), oc.getCountry()) && StringUtils.isNotEmpty(param.getEmploymentForm())) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(UserExtendAttrKeyEnum.EMPLOYEE_FORM.getCode(), param.getEmploymentForm());
            build.setBizData(paramMap);
        }

        if (!StringUtils.equalsAnyIgnoreCase(param.getCountry(), CountryCodeEnum.BRA.getCode()) || Objects.equals(BusinessConstant.HRMS_WEB, param.getSource())) {
            build.setUserSex(param.getSex());
        }
        if (EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType())) {
            build.setOcCode(oc.getOcCode());
            build.setLocationCountry(oc.getCountry());
            build.setLocationCity(oc.getCity());
            build.setLocationProvince(oc.getProvince());
            if (!StringUtils.equalsAnyIgnoreCase(param.getCountry(), CountryCodeEnum.BRA.getCode())) {
                build.setVendorCode(param.getVendorCode());
            }
        }
        rpcUserService.updateUser(build);
        //这里需要查询一次获取最新用户快照信息
        HrmsUserInfoDO updateUser = userInfoDao.getByUserCode(user.getUserCode());
        UserVO userVO = new UserVO();
        transactionTemplate.executeWithoutResult(x -> {
            try {
                saveUserInfo(updateUser, certificateSaveParamList, oc);

                userVO.setUserId(user.getId());
                userVO.setUserCode(user.getUserCode());
                userVO.setUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()));
                userVO.setFirstEnter(Objects.isNull(faceFeatureDao.getByUserCode(user.getUserCode())));

                if (!EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType())) {
                    return;
                }

                //巴西劳务账号在PCS侧创建时未绑定日历和打卡规则
                if (Objects.equals(CountryCodeEnum.BRA.getCode(), user.getLocationCountry())) {
                    //            List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeList = attendancePunchConfigRangeManage.selectConfigRangeByBizId(Collections.singletonList(param.getUserId()));
                    List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeList = punchConfigManageAdapter.selectConfigRangeByBizId(Collections.singletonList(param.getUserId()));
                    if (CollectionUtils.isEmpty(punchConfigRangeList)) {
                        //绑定用户日历和打卡规则
//                configRangeService.addWarehouseAttendanceConfigRange(updateUser);
                        calendarRangeServiceAdapter.addWarehouseCalendarConfigRange(updateUser);
                        //                punchConfigRangeService.addWarehouseAttendancePunchConfigRange(updateUser);
                        punchConfigServiceAdapter.addWarehousePunchConfigRange(updateUser);
                    }
                }

                if (!Objects.equals(user.getDeptId(), param.getOcId())) {
                    userInfoService.updateAttendanceAndPunchConfigHandler(updateUser, user);
                }
            } catch (Exception e) {
                x.setRollbackOnly();
            }
        });
        return userVO;
    }

    private void addBlackListRecord(AddUserParam param, HrmsUserInfoDO user, RpcResult<BlacklistInfoDTO> result) {
        try {
            HrmsWarehouseBlackListDO blackListDO = new HrmsWarehouseBlackListDO();
            blackListDO.setId(iHrmsIdWorker.nextId());
            blackListDO.setUserId(user.getId());
            blackListDO.setUserCode(user.getUserCode());
            blackListDO.setEmployeeType(user.getEmployeeType());
            blackListDO.setOcId(param.getOcId());
            if (StringUtils.isNotBlank(param.getVendorCode())) {
                blackListDO.setVendorCode(param.getVendorCode());
            }
            if (param.getClassId() != null) {
                blackListDO.setClassesId(param.getClassId());
            }
            if (param.getWarehouseDate() != null) {
                blackListDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(param.getWarehouseDate())));
            }
            blackListDO.setType(WarehouseBlackTypeEnum.REGISTER.getCode());
            blackListDO.setReason(result.getResult().getReason());
            hrmsWarehouseBlackListDao.save(blackListDO);
        } catch (Exception ex) {
            log.error("checkIn 添加黑名单记录失败, param: {}", JSONObject.toJSONString(param), ex);
        }
    }

    private void saveOrUpdateUserParamCheck(AddUserParam param) {
        List<VendorInfoSimpleApiDTO> vendorInfoSimpleApiDTOList = vendorService.selectVendorList(Collections.singletonList(param.getVendorId()));
        if (CollectionUtils.isEmpty(vendorInfoSimpleApiDTOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.VENDOR_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.VENDOR_NOT_EXITS.getDesc()));
        }
        if (!StringUtils.equalsAnyIgnoreCase(param.getCountry(), CountryCodeEnum.BRA.getCode())) {
            if (param.getSex() == null) {
                throw BusinessException.get(HrmsErrorCodeEnums.SEX_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SEX_NOT_EMPTY.getDesc()));
            }
            List<WarehouseUserCertificateParam> ineCertificateList = param.getUserCertificateParamList().stream()
                    .filter(item -> StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), CertificateTypeEnum.INE.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ineCertificateList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.CERTIFICATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.CERTIFICATE_NOT_EMPTY.getDesc()));
            }
            if (ineCertificateList.get(0).getCertificateExpireDate() == null) {
                throw BusinessException.get(HrmsErrorCodeEnums.CERTIFICATE_EXPIRE_DATA_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.CERTIFICATE_EXPIRE_DATA_NOT_EMPTY.getDesc()));
            }
            return;
        }

        if (Objects.nonNull(param.getUserId()) || Objects.nonNull(param.getUserCode())) {
            return;
        }

        //巴西校验
        List<WarehouseUserCertificateParam> idCardCertificateList = param.getUserCertificateParamList().stream()
                .filter(item -> StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), CertificateTypeEnum.LABOUR_CARD_NO.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idCardCertificateList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.CERTIFICATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.CERTIFICATE_NOT_EMPTY.getDesc()));
        }

        //判断证件号是否在黑名单中
        UserCertificateInfoParamDTO certificateInfoParamDTO = new UserCertificateInfoParamDTO();
        certificateInfoParamDTO.setCertificateCode(idCardCertificateList.get(0).getCertificateCode());
        certificateInfoParamDTO.setCertificateTypeCode(CertificateTypeEnum.ID_CARD.getCode());
        LockdownInfoDTO lockdownInfo = blacklistService.checkExitBlacklist(null, Collections.singletonList(certificateInfoParamDTO));
        if (Objects.nonNull(lockdownInfo)) {
            addBlackListRecord(param, lockdownInfo);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.BLACKLIST_USER_CANNOT_ENTER, lockdownInfo.getReason());
        }

        //通过证件号找到用户
        UserCertificateCheckResultDTO checkResult = checkDuplicateCertificate(idCardCertificateList.get(0).getCertificateCode(), CertificateTypeEnum.ID_CARD.getCode(), null);
        if (checkResult == null || checkResult.getRepeatCertificateList().size() > BusinessConstant.ONE) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.NOT_VENDOR_USER_NOT_REGISTER, vendorInfoSimpleApiDTOList.get(0).getVendorName());
        }

        UserDTO userDTO = rpcUserService.getUserByCode(checkResult.getRepeatCertificateList().get(0).getOwnerUserCode());
        if (userDTO == null || !StringUtils.equalsAnyIgnoreCase(userDTO.getVendorCode(), param.getVendorCode())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.NOT_VENDOR_USER_NOT_REGISTER, vendorInfoSimpleApiDTOList.get(0).getVendorName());
        }
        param.setUserId(userDTO.getId());
    }

    private void addBlackListRecord(AddUserParam param, LockdownInfoDTO lockdownInfo) {
        try {
            HrmsWarehouseBlackListDO blackListDO = new HrmsWarehouseBlackListDO();
            blackListDO.setId(iHrmsIdWorker.nextId());
            blackListDO.setUserId(lockdownInfo.getUserId());
            blackListDO.setUserCode(lockdownInfo.getUserCode());
            blackListDO.setEmployeeType(EmploymentTypeEnum.OS_FIXED_SALARY.getCode());
            blackListDO.setOcId(param.getOcId());
            if (StringUtils.isNotBlank(param.getVendorCode())) {
                blackListDO.setVendorCode(param.getVendorCode());
            }
            if (param.getClassId() != null) {
                blackListDO.setClassesId(param.getClassId());
            }
            if (param.getWarehouseDate() != null) {
                blackListDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(param.getWarehouseDate())));
            }
            blackListDO.setType(WarehouseBlackTypeEnum.REGISTER.getCode());
            blackListDO.setReason(lockdownInfo.getReason());
            hrmsWarehouseBlackListDao.save(blackListDO);
        } catch (Exception ex) {
            log.error("saveOrUpdateUserParamCheck 添加黑名单记录失败, param: {}", JSONObject.toJSONString(param), ex);
        }
    }

    /**
     * 构造证件保存参数
     *
     * @param param 劳务派遣用户入参
     */
    private void buildUserCertificateSaveParam(AddUserParam param, List<UserCertificateSaveParam> certificateSaveParamList) {

        if (Objects.equals(CountryCodeEnum.BRA.getCode(), param.getCountry())) {
            Set<String> certificateCodes = param.getUserCertificateParamList().stream().map(WarehouseUserCertificateParam::getCertificateCode).collect(Collectors.toSet());
            if (certificateCodes.size() > BusinessConstant.ONE) {
                throw BusinessException.get(HrmsErrorCodeEnums.CERTIFICATE_CODE_MUST_BE_CONSISTENT.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.CERTIFICATE_CODE_MUST_BE_CONSISTENT.getMessage()));
            }
        }
        for (WarehouseUserCertificateParam certificateParam : param.getUserCertificateParamList()) {
            UserCertificateSaveParam saveParam = new UserCertificateSaveParam();
            saveParam.setId(certificateParam.getId());
            saveParam.setCertificateTypeCode(certificateParam.getCertificateTypeCode());
            saveParam.setCertificateCode(certificateParam.getCertificateCode());
            saveParam.setCertificateReceiptDate(certificateParam.getCertificateReceiptDate());
            saveParam.setCertificateExpireDate(certificateParam.getCertificateExpireDate());
            saveParam.setCertificateFrontPath(certificateParam.getCertificateFrontPath());
            saveParam.setCertificateBackPath(certificateParam.getCertificateBackPath());
            certificateSaveParamList.add(saveParam);
        }
    }

    private void certificateRepeatCheck(AddUserParam param, List<UserCertificateSaveParam> certificateSaveParamList, String userCode) {
        String certificateTypeCode = getCertificateType(param.getCountry());
        List<UserCertificateSaveParam> certificateList = certificateSaveParamList.stream()
                .filter(item -> StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), certificateTypeCode))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(certificateList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.CERTIFICATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.CERTIFICATE_NOT_EMPTY.getDesc()));
        }
        UserCertificateCheckResultDTO checkResult = checkDuplicateCertificate(certificateList.get(0).getCertificateCode(), certificateTypeCode, userCode);
        if (checkResult.getIsRepeat()) {
            throw BusinessException.get(OcrErrorCodeEnum.DUPLICATE_ID_CARD.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.DUPLICATE_ID_CARD.getDesc()));
        }
    }


    private UserCertificateCheckResultDTO checkDuplicateCertificate(String certificateCode, String certificateTypeCode, String userCode) {
        UserCertificateDuplicateCheckParam userCertificateDuplicateCheckParam = new UserCertificateDuplicateCheckParam();
        Certificate certificate = new Certificate();
        certificate.setCertificateTypeCode(certificateTypeCode);
        certificate.setCertificateCode(certificateCode);
        userCertificateDuplicateCheckParam.setCertificateList(Collections.singletonList(certificate));
        userCertificateDuplicateCheckParam.setUserCode(userCode);
        return userCertificateService.checkUserCertificateDuplicate(userCertificateDuplicateCheckParam);
    }

    private void userCertificateDataBuild(HrmsUserInfoDO user,
                                          String country,
                                          List<UserCertificateBO> userCertificateBOList,
                                          WarehouseOcUserVO ocUserVO) {
        List<UserCertificateBO> existUserCertificateBOList = new ArrayList<>();
        if (StringUtils.equalsAnyIgnoreCase(country, CountryCodeEnum.BRA.getCode())) {
            existUserCertificateBOList = userCertificateBOList.stream()
                    .filter(item -> item.getUserId().equals(user.getId())
                            && (StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), CertificateTypeEnum.ID_CARD.getCode())
                            || StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), CertificateTypeEnum.LABOUR_CARD_NO.getCode())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserCertificateBOList)) {
                UserCertificateBO rgUserCertificateBO = new UserCertificateBO();
                rgUserCertificateBO.setCertificateTypeCode(CertificateTypeEnum.ID_CARD.getCode());
                existUserCertificateBOList.add(rgUserCertificateBO);
                UserCertificateBO labourUserCertificateBO = new UserCertificateBO();
                labourUserCertificateBO.setCertificateTypeCode(CertificateTypeEnum.LABOUR_CARD_NO.getCode());
                existUserCertificateBOList.add(labourUserCertificateBO);
            }
        }
        if (StringUtils.equalsAnyIgnoreCase(country, CountryCodeEnum.MEX.getCode())) {
            existUserCertificateBOList = userCertificateBOList.stream()
                    .filter(item -> item.getUserId().equals(user.getId())
                            && StringUtils.equalsAnyIgnoreCase(item.getCertificateTypeCode(), CertificateTypeEnum.INE.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserCertificateBOList)) {
                UserCertificateBO mexUserCertificateBO = new UserCertificateBO();
                mexUserCertificateBO.setCertificateTypeCode(CertificateTypeEnum.INE.getCode());
                existUserCertificateBOList.add(mexUserCertificateBO);
            }
        }
        List<WarehouseUserCertificateVO> certificateVOList = new ArrayList<>();
        for (UserCertificateBO userCertificateBO : existUserCertificateBOList) {
            WarehouseUserCertificateVO certificateVO = BeanUtils.convert(userCertificateBO, WarehouseUserCertificateVO.class);
            certificateVOList.add(certificateVO);
        }
        ocUserVO.setCertificateVOList(certificateVOList);
    }
}
