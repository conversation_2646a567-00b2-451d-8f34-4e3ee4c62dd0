package com.imile.hrms.service.blacklist.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlacklistInfo4PhoneVO {

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 电话号码
     */
    private String callingPhone;

    /**
     * 黑名单截止日期
     */
    private Date endDate;

    /**
     * 封禁理由
     */
    private String reason;

    /**
     * 操作用户
     */
    private String recordUserName;
}
