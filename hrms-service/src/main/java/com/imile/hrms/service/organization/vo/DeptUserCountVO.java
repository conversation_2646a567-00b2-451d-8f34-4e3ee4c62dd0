package com.imile.hrms.service.organization.vo;


import lombok.Data;


/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@Data
public class DeptUserCountVO {

    /**
     * 本部门自有人员数量（员工、挂靠、实习生、兼职）
     */
    private Integer selfUserCount;

    /**
     * 本部门外包人员数量（顾问、劳务派遣、合作伙伴）
     */
    private Integer selfOutsourceUserCount;

    /**
     * 本部门及下级部门自有人员数量（员工、挂靠、实习生、兼职）
     */
    private Integer withSubDeptUserCount;

    /**
     * 本部门及下级部门外包人员数量（顾问、劳务派遣、合作伙伴）
     */
    private Integer withSubDeptOutSourceUserCount;

}
