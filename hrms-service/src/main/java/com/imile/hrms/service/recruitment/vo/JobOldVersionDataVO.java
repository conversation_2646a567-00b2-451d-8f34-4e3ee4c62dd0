package com.imile.hrms.service.recruitment.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/2/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobOldVersionDataVO {

    /**
     * 快照数据
     */
    private HcDetailVO data;

    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 修改人
     */
    private String modifiedUserCode;

    private String modifiedUserName;

    private String modifiedUserNameEn;

    /**
     * 对照审批，APPLY = 原单；RESUBMIT = 驳回重提
     */
    private String modificationType;

    private String modificationTypeDesc;


}
