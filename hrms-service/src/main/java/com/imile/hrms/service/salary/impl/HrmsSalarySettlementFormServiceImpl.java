package com.imile.hrms.service.salary.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.FileTemplateApiDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.constants.ExcelUtils;
import com.imile.hrms.common.constants.RedisConstants;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.dto.AttachmentDTO;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.PermissionTypeEnum;
import com.imile.hrms.common.enums.approval.ApprovalNoPrefixEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormTypeEnum;
import com.imile.hrms.common.enums.approval.SalarySettlementCustomFieldEnum;
import com.imile.hrms.common.enums.salary.SalaryItemAttributeEnum;
import com.imile.hrms.common.enums.salary.SalaryItemFormatEnum;
import com.imile.hrms.common.enums.salary.SalaryItemValueTypeEnum;
import com.imile.hrms.common.enums.salary.SalarySettlementFormStatusEnum;
import com.imile.hrms.common.enums.salary.SalarySettlementFormSubmitTypeEnum;
import com.imile.hrms.common.enums.salary.SalarySettlementUserDetailDataSourceEnum;
import com.imile.hrms.common.enums.salary.SalarySettlementUserItemStatusEnum;
import com.imile.hrms.common.enums.salary.SalarySubmitRecordDataStatusEnum;
import com.imile.hrms.common.enums.salary.SalarySubmitRecordOperateTypeEnum;
import com.imile.hrms.common.enums.salary.SalarySubmitRecordStatusUpdateReasonEnum;
import com.imile.hrms.common.enums.salary.SalarySubmitTemplateApprovalMethodEnum;
import com.imile.hrms.common.util.IdWorkUtils;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigSubmitRecordDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryPaymentCountryConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySchemeConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementAgentRecordDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalFormDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalItemInfoDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalUserInfoDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserDetailDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserInfoDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateItemConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryItemConfigSubmitRecordQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementAgentRecordQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementApprovalFormQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementUserDetailQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementUserInfoQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateConfigQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateItemConfigQuery;
import com.imile.hrms.dao.salary.query.SalaryItemConfigQuery;
import com.imile.hrms.dao.salary.query.SalaryPaymentCountryQuery;
import com.imile.hrms.dao.salary.query.SalarySchemeConfigQuery;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.bpm.service.BpmCreateApprovalService;
import com.imile.hrms.integration.ipep.IpepIntegration;
import com.imile.hrms.integration.permission.UserPermissionService;
import com.imile.hrms.integration.permission.dto.DataPermissionDTO;
import com.imile.hrms.integration.permission.dto.PermissionDTO;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemConfigSubmitRecordManage;
import com.imile.hrms.manage.salary.HrmsSalaryPaymentCountryConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySchemeConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementAgentRecordManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementApprovalFormManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementUserDetailManage;
import com.imile.hrms.manage.salary.HrmsSalarySettlementUserInfoManage;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateConfigManage;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateItemConfigManage;
import com.imile.hrms.manage.salary.bo.HrmsSalarySettlementFormDetailBO;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.approval.vo.ApprovalResultVO;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.salary.HrmsSalaryBaseService;
import com.imile.hrms.service.salary.HrmsSalarySettlementBaseService;
import com.imile.hrms.service.salary.HrmsSalarySettlementFormService;
import com.imile.hrms.service.salary.param.SalarySettlementFormAddParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormCancelParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormClosedAccountUserInfoParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormDetailParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormListParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormUserDetailParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormUserInfoParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormUserParam;
import com.imile.hrms.service.salary.param.SalarySettlementFormWithdrawParam;
import com.imile.hrms.service.salary.vo.SalarySettlementFormListCollectTitleVO;
import com.imile.hrms.service.salary.vo.SalarySettlementFormListVO;
import com.imile.hrms.service.salary.vo.SalarySettlementFormUserDetailVO;
import com.imile.hrms.service.salary.vo.SalarySettlementFormUserVO;
import com.imile.hrms.service.salary.vo.SalarySettlementFormUserVerticalTableExportVO;
import com.imile.hrms.service.salary.vo.SalarySettlementFromDetailVO;
import com.imile.hrms.service.salary.vo.SalarySettlementUserDataTitleExportVO;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/13 16:29
 * @version: 1.0
 */
@Service
@Slf4j
public class HrmsSalarySettlementFormServiceImpl extends BaseService implements HrmsSalarySettlementFormService {
    @Autowired
    private HrmsSalarySubmitTemplateConfigManage hrmsSalarySubmitTemplateConfigManage;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private BpmCreateApprovalService bpmCreateApprovalService;
    @Autowired
    private HrmsSalarySettlementAgentRecordManage hrmsSalarySettlementAgentRecordManage;
    @Autowired
    private HrmsSalarySettlementUserInfoManage hrmsSalarySettlementUserInfoManage;
    @Autowired
    private HrmsSalarySettlementUserDetailManage hrmsSalarySettlementUserDetailManage;
    @Autowired
    private HrmsSalarySettlementApprovalFormManage hrmsSalarySettlementApprovalFormManage;
    @Autowired
    private HrmsSalarySchemeConfigManage hrmsSalarySchemeConfigManage;
    @Autowired
    private HrmsSalaryItemConfigManage hrmsSalaryItemConfigManage;
    @Autowired
    private HrmsSalaryBaseService hrmsSalaryBaseService;
    @Autowired
    private HrmsSalarySubmitTemplateItemConfigManage hrmsSalarySubmitTemplateItemConfigManage;
    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;
    @Autowired
    private HrmsDeptManage hrmsDeptManage;
    @Autowired
    private IdWorkUtils idWorkUtils;
    @Autowired
    private IpepIntegration ipepIntegration;
    @Autowired
    private HrmsSalaryItemConfigSubmitRecordManage hrmsSalaryItemConfigSubmitRecordManage;
    @Autowired
    private HrmsSalarySettlementBaseService hrmsSalarySettlementBaseService;
    @Autowired
    private HrmsSalaryPaymentCountryConfigManage hrmsSalaryPaymentCountryConfigManage;
    @Autowired
    private ImileRedisClient redissonClient;

    @Autowired
    private UserPermissionService userPermissionService;

    private static final String salarySettlementImportRedisKey = "HRMS::SALARY:SETTLEMENT:IMPORT";
    public static final long SALARY_SETTLEMENT_IMPORT_REDIS_EXPIRE_TIME = 60;

    @Override
    public ApprovalResultVO formAdd(SalarySettlementFormAddParam param) {
        log.info("formAdd | formAdd :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(RequestInfoHolder.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        HrmsSalarySettlementAgentRecordDO agentRecordDO = hrmsSalarySettlementAgentRecordManage.getById(param.getSalarySettlementAgentRecordId());

        HrmsSalarySettlementApprovalFormDO addSalarySettlementApprovalFormDO = new HrmsSalarySettlementApprovalFormDO();
        List<HrmsSalarySettlementApprovalItemInfoDO> addSalarySettlementApprovalItemInfoDOList = new ArrayList<>();
        List<HrmsSalarySettlementApprovalUserInfoDO> addSalarySettlementApprovalUserInfoDOList = new ArrayList<>();

        //暂存不校验任何信息，直接落库成功，提交时校验
        if (param.getOperationType() == 1) {
            salarySettlementFormAddDataCheck(param, agentRecordDO);
        }
        salarySettlementFormAddDataBuild(param, hrmsUserInfoDO, agentRecordDO,
                addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            hrmsSalarySettlementApprovalFormManage.formAdd(addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
            return resultVO;
        }
        //看提报配置，可能不需要调用bpm，是直接通过
        HrmsSalarySubmitTemplateConfigDO templateConfigDO = hrmsSalarySubmitTemplateConfigManage.getById(agentRecordDO.getSalarySubmitTemplateConfigId());
        if (templateConfigDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
        }
        if (StringUtils.equalsIgnoreCase(templateConfigDO.getApprovalMethod(), SalarySubmitTemplateApprovalMethodEnum.NONE_APPROVAL.getCode())) {
            List<HrmsSalarySettlementUserDetailDO> addSettlementUserDetailDOList = new ArrayList<>();
            List<HrmsSalarySettlementUserDetailDO> updateSettlementUserDetailDOList = new ArrayList<>();
            List<HrmsSalaryItemConfigSubmitRecordDO> addItemRecordList = new ArrayList<>();
            List<HrmsSalaryItemConfigSubmitRecordDO> updateItemRecordList = new ArrayList<>();
            hrmsSalarySettlementBaseService.formAutoOrApprovalPassHandler(addSalarySettlementApprovalFormDO, addSalarySettlementApprovalUserInfoDOList, addSalarySettlementApprovalItemInfoDOList,
                    addSettlementUserDetailDOList, updateSettlementUserDetailDOList, addItemRecordList, updateItemRecordList);
            hrmsSalarySettlementApprovalFormManage.formAutoPassAdd(addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList,
                    addSalarySettlementApprovalUserInfoDOList, addSettlementUserDetailDOList, updateSettlementUserDetailDOList,
                    addItemRecordList, updateItemRecordList);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        salarySettlementFormAddBpmDataBuild(initInfoApiDTO, param, hrmsUserInfoDO, agentRecordDO, addSalarySettlementApprovalFormDO);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        addSalarySettlementApprovalFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsSalarySettlementApprovalFormManage.formAdd(addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public ApprovalResultVO formUpdate(SalarySettlementFormAddParam param) {
        log.info("formUpdate | formUpdate :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(RequestInfoHolder.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        HrmsSalarySettlementAgentRecordDO agentRecordDO = hrmsSalarySettlementAgentRecordManage.getById(param.getSalarySettlementAgentRecordId());
        HrmsSalarySettlementFormDetailBO detailBO = hrmsSalarySettlementApprovalFormManage.getFormDetailById(param.getApplicationFormId());
        if (detailBO == null || detailBO.getSalarySettlementApprovalFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getDesc()));
        }
        HrmsSalarySettlementApprovalFormDO updateSalarySettlementApprovalFormDO = detailBO.getSalarySettlementApprovalFormDO();
        //特殊逻辑 撤回&算薪驳回的单据重新编辑，需要生成新的单据
        if (StringUtils.equalsIgnoreCase(updateSalarySettlementApprovalFormDO.getFormStatus(), SalarySettlementFormStatusEnum.WITHDRAWN.getCode())) {
            //特殊处理，撤回单据只能用一次，需要变回取消状态
            updateSalarySettlementApprovalFormDO.setFormStatus(SalarySettlementFormStatusEnum.CANCEL.getCode());
            this.fillDOUpdate(updateSalarySettlementApprovalFormDO);
            hrmsSalarySettlementApprovalFormManage.batchUpdate(Arrays.asList(updateSalarySettlementApprovalFormDO));
            param.setApplicationFormId(null);
            resultVO = formAdd(param);
            return resultVO;
        }
        List<HrmsSalarySettlementApprovalUserInfoDO> addSalarySettlementApprovalUserInfoDOList = new ArrayList<>();
        List<HrmsSalarySettlementApprovalItemInfoDO> addSalarySettlementApprovalItemInfoDOList = new ArrayList<>();
        //暂存不校验任何信息，直接落库成功，提交时校验
        if (param.getOperationType() == 1) {
            salarySettlementFormAddDataCheck(param, agentRecordDO);
        }
        salarySettlementFormAddDataBuild(param, hrmsUserInfoDO, agentRecordDO,
                updateSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
        List<HrmsSalarySettlementApprovalUserInfoDO> oldSettlementApprovalUserInfoDOList = detailBO.getSalarySettlementApprovalUserInfoDOList();
        oldSettlementApprovalUserInfoDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
        });
        List<HrmsSalarySettlementApprovalItemInfoDO> oldSettlementApprovalItemInfoDOList = detailBO.getSalarySettlementApprovalItemInfoDOList();
        oldSettlementApprovalItemInfoDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
        });
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            hrmsSalarySettlementApprovalFormManage.formUpdate(updateSalarySettlementApprovalFormDO, oldSettlementApprovalItemInfoDOList, oldSettlementApprovalUserInfoDOList,
                    addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
            return resultVO;
        }
        //看提报配置，可能不需要调用bpm，是直接通过
        HrmsSalarySubmitTemplateConfigDO templateConfigDO = hrmsSalarySubmitTemplateConfigManage.getById(agentRecordDO.getSalarySubmitTemplateConfigId());
        if (templateConfigDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
        }
        if (StringUtils.equalsIgnoreCase(templateConfigDO.getApprovalMethod(), SalarySubmitTemplateApprovalMethodEnum.NONE_APPROVAL.getCode())) {
            List<HrmsSalarySettlementUserDetailDO> updateSettlementUserDetailDOList = new ArrayList<>();
            List<HrmsSalarySettlementUserDetailDO> addSettlementUserDetailDOList = new ArrayList<>();
            List<HrmsSalaryItemConfigSubmitRecordDO> addItemRecordList = new ArrayList<>();
            List<HrmsSalaryItemConfigSubmitRecordDO> updateItemRecordList = new ArrayList<>();
            hrmsSalarySettlementBaseService.formAutoOrApprovalPassHandler(updateSalarySettlementApprovalFormDO, addSalarySettlementApprovalUserInfoDOList, addSalarySettlementApprovalItemInfoDOList,
                    addSettlementUserDetailDOList, updateSettlementUserDetailDOList, addItemRecordList, updateItemRecordList);
            hrmsSalarySettlementApprovalFormManage.formAutoPassUpdate(updateSalarySettlementApprovalFormDO, oldSettlementApprovalItemInfoDOList, oldSettlementApprovalUserInfoDOList,
                    addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList, addSettlementUserDetailDOList, updateSettlementUserDetailDOList,
                    addItemRecordList, updateItemRecordList);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        salarySettlementFormAddBpmDataBuild(initInfoApiDTO, param, hrmsUserInfoDO, agentRecordDO, updateSalarySettlementApprovalFormDO);
        //本次保存是否是驳回后重提
        if (updateSalarySettlementApprovalFormDO.getApprovalId() != null && StringUtils.isNotBlank(updateSalarySettlementApprovalFormDO.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(updateSalarySettlementApprovalFormDO.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(updateSalarySettlementApprovalFormDO.getApprovalId());
            }
        }
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
        updateSalarySettlementApprovalFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        hrmsSalarySettlementApprovalFormManage.formUpdate(updateSalarySettlementApprovalFormDO, oldSettlementApprovalItemInfoDOList, oldSettlementApprovalUserInfoDOList,
                addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    @Override
    public HrmsSalarySettlementApprovalFormDO formCalculateSalaryReject(SalarySettlementFormAddParam param) {
        log.info("formCalculateSalaryReject | formCalculateSalaryReject :{}", JSON.toJSONString(param));
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(RequestInfoHolder.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        HrmsSalarySettlementAgentRecordDO agentRecordDO = hrmsSalarySettlementAgentRecordManage.getById(param.getSalarySettlementAgentRecordId());
        HrmsSalarySettlementApprovalFormDO addSalarySettlementApprovalFormDO = new HrmsSalarySettlementApprovalFormDO();
        List<HrmsSalarySettlementApprovalItemInfoDO> addSalarySettlementApprovalItemInfoDOList = new ArrayList<>();
        List<HrmsSalarySettlementApprovalUserInfoDO> addSalarySettlementApprovalUserInfoDOList = new ArrayList<>();
        salarySettlementFormAddDataCheck(param, agentRecordDO);
        salarySettlementFormAddDataBuild(param, hrmsUserInfoDO, agentRecordDO,
                addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        addSalarySettlementApprovalFormDO.setFormStatus(SalarySettlementFormStatusEnum.CALCULATE_SALARY_REJECT.getCode());
        hrmsSalarySettlementApprovalFormManage.formAdd(addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        return addSalarySettlementApprovalFormDO;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> formPreview(SalarySettlementFormAddParam param) {
        log.info("formPreview | formPreview :{}", JSON.toJSONString(param));
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        //暂存就是保存前一步，必填数据都要填写完毕才可以
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(RequestInfoHolder.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        HrmsSalarySettlementAgentRecordDO agentRecordDO = hrmsSalarySettlementAgentRecordManage.getById(param.getSalarySettlementAgentRecordId());
        HrmsSalarySettlementApprovalFormDO addSalarySettlementApprovalFormDO = new HrmsSalarySettlementApprovalFormDO();
        List<HrmsSalarySettlementApprovalItemInfoDO> addSalarySettlementApprovalItemInfoDOList = new ArrayList<>();
        List<HrmsSalarySettlementApprovalUserInfoDO> addSalarySettlementApprovalUserInfoDOList = new ArrayList<>();
        salarySettlementFormAddDataCheck(param, agentRecordDO);
        salarySettlementFormAddDataBuild(param, hrmsUserInfoDO, agentRecordDO,
                addSalarySettlementApprovalFormDO, addSalarySettlementApprovalItemInfoDOList, addSalarySettlementApprovalUserInfoDOList);
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        salarySettlementFormAddBpmDataBuild(initInfoApiDTO, param, hrmsUserInfoDO, agentRecordDO, addSalarySettlementApprovalFormDO);
        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        hrmsSalaryBaseService.previewDTOBuild(recordApiDTOList, resultDTOList);
        return resultDTOList;
    }

    @Override
    public List<SalarySettlementFormListCollectTitleVO> formListCollectTitle(SalarySettlementFormListParam param) {
        PermissionDTO userPermission = userPermissionService.getUserPermission(RequestInfoHolder.getUserCode());
        if (userPermission != null && CollectionUtils.isNotEmpty(userPermission.getDataPermissionDTOList())) {
            List<DataPermissionDTO> collect = userPermission.getDataPermissionDTOList().stream().filter(o -> StringUtils.equalsIgnoreCase(o.getTypeCode(), PermissionTypeEnum.HRMS_IS_SALARY_REPORTER.getTypeCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                //是提报人
                List<HrmsSalarySettlementAgentRecordDO> settlementAgentRecordList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().paymentCountry(param.getPaymentCountry()).build());
                if (CollectionUtils.isEmpty(settlementAgentRecordList)) {
                    return new ArrayList<>();
                }

                //获取所有的提报模板，过滤出当前月份的模版   即该月不同类型的配置只有一份
                List<HrmsSalarySubmitTemplateConfigDO> submitTemplateConfigList = hrmsSalarySubmitTemplateConfigManage.listByQuery(HrmsSalarySubmitTemplateConfigQuery.builder().paymentCountry(param.getPaymentCountry()).build()).stream()
                        .filter(item -> Long.valueOf(param.getPaymentMonth()).compareTo(item.getEffectDate()) > -1
                                && Long.valueOf(param.getPaymentMonth()).compareTo(item.getExpireDate()) < 0
                                && item.getReporterList().contains(RequestInfoHolder.getUserCode()))
                        .collect(Collectors.toList());
                List<Long> submitTemplateConfigIdList = submitTemplateConfigList.stream().map(HrmsSalarySubmitTemplateConfigDO::getId).collect(Collectors.toList());
                settlementAgentRecordList = settlementAgentRecordList.stream().filter(o -> submitTemplateConfigIdList.contains(o.getSalarySubmitTemplateConfigId())).collect(Collectors.toList());
                List<Long> settlementAgentRecordIdList = settlementAgentRecordList.stream().map(HrmsSalarySettlementAgentRecordDO::getId).collect(Collectors.toList());
                param.setSalarySettlementAgentRecordIdList(settlementAgentRecordIdList);
            }
        }

        HrmsSalarySettlementApprovalFormQuery formQuery = HrmsSalarySettlementApprovalFormQuery.builder()
                .paymentCountry(param.getPaymentCountry())
                .paymentMonth(param.getPaymentMonth())
                .submitType(SalarySettlementFormSubmitTypeEnum.NORMAL_MONTH_REPORT.getCode())
                .salarySettlementAgentRecordIdList(param.getSalarySettlementAgentRecordIdList())
                .build();
        List<HrmsSalarySettlementApprovalFormDO> formDOList = hrmsSalarySettlementApprovalFormManage.listByQuery(formQuery);
        List<SalarySettlementFormStatusEnum> formStatusEnumList = Arrays.asList(SalarySettlementFormStatusEnum.values());
        List<SalarySettlementFormListCollectTitleVO> resultList = new ArrayList<>();
        for (SalarySettlementFormStatusEnum formStatusEnum : formStatusEnumList) {
            List<HrmsSalarySettlementApprovalFormDO> formStatusList = formDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), formStatusEnum.getCode()))
                    .collect(Collectors.toList());
            SalarySettlementFormListCollectTitleVO titleVO = new SalarySettlementFormListCollectTitleVO();
            titleVO.setFormStatus(formStatusEnum.getCode());
            titleVO.setFormNumber(formStatusList.size());
            resultList.add(titleVO);
        }
        return resultList;
    }

    @Override
    public PaginationResult<SalarySettlementFormListVO> formList(SalarySettlementFormListParam param) {
        List<Long> roleAgentRecordIdList = new ArrayList<>();
        PermissionDTO userPermission = userPermissionService.getUserPermission(RequestInfoHolder.getUserCode());
        if (userPermission != null && CollectionUtils.isNotEmpty(userPermission.getDataPermissionDTOList())) {
            List<DataPermissionDTO> collect = userPermission.getDataPermissionDTOList().stream().filter(o -> StringUtils.equalsIgnoreCase(o.getTypeCode(), PermissionTypeEnum.HRMS_IS_SALARY_REPORTER.getTypeCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                //是提报人
                List<HrmsSalarySettlementAgentRecordDO> settlementAgentRecordList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().paymentCountry(param.getPaymentCountry()).build());
                if (CollectionUtils.isEmpty(settlementAgentRecordList)) {
                    return getPageResult(BeanUtils.convert(new PageInfo<>(), PageInfo.class), param);
                }

                //获取所有的提报模板，过滤出当前月份的模版   即该月不同类型的配置只有一份
                List<HrmsSalarySubmitTemplateConfigDO> submitTemplateConfigList = hrmsSalarySubmitTemplateConfigManage.listByQuery(HrmsSalarySubmitTemplateConfigQuery.builder().paymentCountry(param.getPaymentCountry()).build()).stream()
                        .filter(item -> Long.valueOf(param.getPaymentMonth()).compareTo(item.getEffectDate()) > -1
                                && Long.valueOf(param.getPaymentMonth()).compareTo(item.getExpireDate()) < 0
                                && item.getReporterList().contains(RequestInfoHolder.getUserCode()))
                        .collect(Collectors.toList());
                List<Long> submitTemplateConfigIdList = submitTemplateConfigList.stream().map(HrmsSalarySubmitTemplateConfigDO::getId).collect(Collectors.toList());
                settlementAgentRecordList = settlementAgentRecordList.stream().filter(o -> submitTemplateConfigIdList.contains(o.getSalarySubmitTemplateConfigId())).collect(Collectors.toList());
                List<Long> settlementAgentRecordIdList = settlementAgentRecordList.stream().map(HrmsSalarySettlementAgentRecordDO::getId).collect(Collectors.toList());
                roleAgentRecordIdList.addAll(settlementAgentRecordIdList);
            }
        }

        HrmsSalarySettlementApprovalFormQuery formQuery = HrmsSalarySettlementApprovalFormQuery.builder()
                .applyUserCodeList(param.getApplyUserCodeList())
                .applicationCode(param.getApplicationCode())
                .formStatus(param.getFormStatus())
                .paymentCountry(param.getPaymentCountry())
                .paymentMonth(param.getPaymentMonth())
                .submitType(SalarySettlementFormSubmitTypeEnum.NORMAL_MONTH_REPORT.getCode())
                .salarySettlementAgentRecordIdList(param.getSalarySettlementAgentRecordIdList())
                .roleAgentRecordIdList(roleAgentRecordIdList)
                .build();
        Page<HrmsSalarySettlementApprovalFormDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PageInfo<HrmsSalarySettlementApprovalFormDO> pageInfo = page.doSelectPageInfo(() -> hrmsSalarySettlementApprovalFormManage.listByQuery(formQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            PageInfo<SalarySettlementFormListVO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, param);
        }

        List<Long> salarySettlementAgentRecordIdList = pageInfo.getList().stream()
                .filter(item -> item.getSalarySettlementAgentRecordId() != null)
                .map(HrmsSalarySettlementApprovalFormDO::getSalarySettlementAgentRecordId)
                .collect(Collectors.toList());
        List<HrmsSalarySettlementAgentRecordDO> settlementAgentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByIdList(salarySettlementAgentRecordIdList);
        Map<Long, List<HrmsSalarySettlementAgentRecordDO>> agentRecordMap = settlementAgentRecordDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementAgentRecordDO::getId));

        List<Long> salarySubmitTemplateConfigIdList = settlementAgentRecordDOList.stream()
                .map(HrmsSalarySettlementAgentRecordDO::getSalarySubmitTemplateConfigId)
                .collect(Collectors.toList());
        List<HrmsSalarySubmitTemplateConfigDO> submitTemplateConfigDOList = hrmsSalarySubmitTemplateConfigManage.listByIdList(salarySubmitTemplateConfigIdList);
        Map<Long, List<HrmsSalarySubmitTemplateConfigDO>> submitTemplateMap = submitTemplateConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateConfigDO::getId));

        List<SalarySettlementFormListVO> formListVOList = new ArrayList<>();
        for (HrmsSalarySettlementApprovalFormDO approvalFormDO : pageInfo.getList()) {
            SalarySettlementFormListVO formListVO = BeanUtils.convert(approvalFormDO, SalarySettlementFormListVO.class);
            List<HrmsSalarySettlementAgentRecordDO> existAgentRecordList = agentRecordMap.get(approvalFormDO.getSalarySettlementAgentRecordId());
            if (CollectionUtils.isNotEmpty(existAgentRecordList)) {
                formListVO.setSalarySettlementAgentRecordName(existAgentRecordList.get(0).getName());
                if (existAgentRecordList.get(0).getDataCollectStartDate() != null && existAgentRecordList.get(0).getDataCollectEndDate() != null) {
                    formListVO.setDataCollectCycle(DateUtil.format(existAgentRecordList.get(0).getDataCollectStartDate(), "yyyy-MM-dd HH:mm:ss") + " - " + DateUtil.format(existAgentRecordList.get(0).getDataCollectEndDate(), "yyyy-MM-dd HH:mm:ss"));
                }
                List<HrmsSalarySubmitTemplateConfigDO> existSubmitTemplateList = submitTemplateMap.get(existAgentRecordList.get(0).getSalarySubmitTemplateConfigId());
                if (CollectionUtils.isNotEmpty(existSubmitTemplateList)) {
                    formListVO.setSalarySubmitTemplateConfigId(existSubmitTemplateList.get(0).getId());
                    formListVO.setSalarySubmitTemplateConfigNo(existSubmitTemplateList.get(0).getTemplateNo());
                    formListVO.setSalarySubmitTemplateConfigName(existSubmitTemplateList.get(0).getTemplateName());
                }
            }
            if (approvalFormDO.getTotalSalaryItemNumber() != null && approvalFormDO.getTotalUserNumber() != null) {
                formListVO.setTotalSubmitNumber(approvalFormDO.getTotalUserNumber() * approvalFormDO.getTotalSalaryItemNumber());
            }
            formListVOList.add(formListVO);
        }
        PageInfo<SalarySettlementFormListVO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(formListVOList);
        return getPageResult(pageInfoResult, param);
    }

    @Override
    public SalarySettlementFromDetailVO formDetail(SalarySettlementFormDetailParam param) {
        HrmsSalarySettlementFormDetailBO formDetailBO = hrmsSalarySettlementApprovalFormManage.getFormDetailById(param.getFormId());
        if (formDetailBO == null || formDetailBO.getSalarySettlementApprovalFormDO() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getDesc()));
        }
        HrmsSalarySettlementApprovalFormDO formDO = formDetailBO.getSalarySettlementApprovalFormDO();
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(formDO.getApplyUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        //获取计薪国币种
        SalaryPaymentCountryQuery salaryPaymentCountryQuery = SalaryPaymentCountryQuery.builder()
                .paymentCountry(formDO.getPaymentCountry())
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(salaryPaymentCountryQuery);
        if (CollectionUtils.isEmpty(paymentCountryConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.PAYMENT_COUNTRY_CONFIG_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PAYMENT_COUNTRY_CONFIG_RECORD_NOT_EXIST.getDesc()));
        }
        //审批单当前勾选的科目
        List<Long> itemDetail = new ArrayList<>();
        if (StringUtils.isNotBlank(formDO.getItemDetail())) {
            itemDetail = JSON.parseArray(formDO.getItemDetail(), Long.class);
        }
        SalarySettlementFromDetailVO detailVO = BeanUtils.convert(formDO, SalarySettlementFromDetailVO.class);
        if (StringUtils.isNotBlank(formDO.getAttachment())) {
            detailVO.setAttachmentList(JSON.parseArray(formDO.getAttachment(), AttachmentDTO.class));
        }
        if (formDO.getTotalSalaryItemNumber() != null && formDO.getTotalUserNumber() != null) {
            detailVO.setTotalSubmitNumber(formDO.getTotalUserNumber() * formDO.getTotalSalaryItemNumber());
        }
        HrmsEntDeptDO deptDO = hrmsDeptManage.selectById(hrmsUserInfoDO.getDeptId());
        if (deptDO != null) {
            detailVO.setApplyUserDeptName(RequestInfoHolder.isChinese() ? deptDO.getDeptNameCn() : deptDO.getDeptNameEn());
        }
        if (hrmsUserInfoDO.getPostId() != null) {
            HrmsEntPostDO entPostDO = hrmsEntPostDao.getById(hrmsUserInfoDO.getPostId());
            if (entPostDO != null) {
                detailVO.setApplyUserPostName(RequestInfoHolder.isChinese() ? entPostDO.getPostNameCn() : entPostDO.getPostNameEn());
            }
        }
        detailVO.setApplyUserOriginCountry(hrmsUserInfoDO.getOriginCountry());

        //注意暂存的单据，可能啥也没选择，连代办记录都没有
        HrmsSalarySettlementAgentRecordDO agentRecordDO = null;
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> submitTemplateItemMap = new HashMap<>();
        if (formDO.getSalarySettlementAgentRecordId() != null) {
            agentRecordDO = hrmsSalarySettlementAgentRecordManage.getById(formDO.getSalarySettlementAgentRecordId());
            if (agentRecordDO == null) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getDesc()));
            }
            SalarySchemeConfigQuery schemeConfigQuery = new SalarySchemeConfigQuery();
            schemeConfigQuery.setSchemeNo(agentRecordDO.getSalarySchemeConfigNo());
            List<HrmsSalarySchemeConfigDO> salarySchemeConfigDOList = hrmsSalarySchemeConfigManage.selectSchemeConfigList(schemeConfigQuery).stream()
                    .sorted(Comparator.comparing(HrmsSalarySchemeConfigDO::getExpireTime).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(salarySchemeConfigDOList)) {
                detailVO.setSalarySchemeConfigNo(salarySchemeConfigDOList.get(0).getSchemeNo());
                detailVO.setSalarySchemeConfigName(salarySchemeConfigDOList.get(0).getSchemeName());
            }
            HrmsSalarySubmitTemplateConfigDO submitTemplateConfigDO = hrmsSalarySubmitTemplateConfigManage.getById(agentRecordDO.getSalarySubmitTemplateConfigId());
            if (submitTemplateConfigDO != null) {
                detailVO.setSalarySubmitTemplateConfigId(submitTemplateConfigDO.getId());
                detailVO.setSalarySubmitTemplateConfigNo(submitTemplateConfigDO.getTemplateNo());
                detailVO.setSalarySubmitTemplateConfigName(submitTemplateConfigDO.getTemplateName());
                detailVO.setSalarySubmitTemplateConfigApprovalMethod(submitTemplateConfigDO.getApprovalMethod());
            }
            detailVO.setSalarySettlementAgentRecordName(agentRecordDO.getName());
            if (agentRecordDO.getDataCollectStartDate() != null && agentRecordDO.getDataCollectEndDate() != null) {
                detailVO.setDataCollectCycle(DateUtil.format(agentRecordDO.getDataCollectStartDate(), "yyyy-MM-dd HH:mm:ss") + " - " + DateUtil.format(agentRecordDO.getDataCollectEndDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (agentRecordDO.getSubmitStartDate() != null && agentRecordDO.getSubmitEndDate() != null) {
                detailVO.setSubmitCycle(DateUtil.format(agentRecordDO.getSubmitStartDate(), "yyyy-MM-dd HH:mm:ss") + " - " + DateUtil.format(agentRecordDO.getSubmitEndDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            //查提报配置中的科目信息
            List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(HrmsSalarySubmitTemplateItemConfigQuery.builder().submitTemplateConfigId(agentRecordDO.getSalarySubmitTemplateConfigId()).build());
            if (CollectionUtils.isEmpty(submitTemplateItemConfigDOList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
            }
            submitTemplateItemConfigDOList = submitTemplateItemConfigDOList.stream()
                    .sorted(Comparator.comparing(HrmsSalarySubmitTemplateItemConfigDO::getSort))
                    .collect(Collectors.toList());
            submitTemplateItemMap = submitTemplateItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId));
        }
        List<SalarySettlementFormUserVO> formUserVOList = new ArrayList<>();
        formUserDetailHandler(BusinessConstant.Y, formDetailBO.getSalarySettlementUserInfoDOList(), formDetailBO.getSalarySettlementApprovalItemInfoDOList(), new ArrayList<>(), itemDetail, agentRecordDO, submitTemplateItemMap, formUserVOList);
        detailVO.setFormUserVOList(formUserVOList);
        return detailVO;
    }

    @Override
    public List<SalarySettlementFormUserVO> formUserInfo(SalarySettlementFormUserInfoParam param) {
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().id(param.getSalarySettlementAgentRecordId()).build());
        if (CollectionUtils.isEmpty(agentRecordDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getDesc()));
        }
        List<Long> salarySettlementFormUserInfoIdList = new ArrayList<>();
        List<Long> salarySettlementFormUserItemIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getSalarySettlementFormUserInfoIdString())) {
            salarySettlementFormUserInfoIdList = Arrays.asList((Long[]) ConvertUtils.convert(param.getSalarySettlementFormUserInfoIdString().split(","), Long.class));
        }
        if (StringUtils.isNotBlank(param.getSalarySettlementFormUserItemIdString())) {
            salarySettlementFormUserItemIdList = Arrays.asList((Long[]) ConvertUtils.convert(param.getSalarySettlementFormUserItemIdString().split(","), Long.class));
        }
        //查提报配置中的科目信息
        List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(HrmsSalarySubmitTemplateItemConfigQuery.builder().submitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId()).build());
        if (CollectionUtils.isEmpty(submitTemplateItemConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
        }
        submitTemplateItemConfigDOList = submitTemplateItemConfigDOList.stream()
                .sorted(Comparator.comparing(HrmsSalarySubmitTemplateItemConfigDO::getSort))
                .collect(Collectors.toList());
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> submitTemplateItemMap = submitTemplateItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId));
        List<Long> itemDetail = submitTemplateItemConfigDOList.stream()
                .map(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId)
                .collect(Collectors.toList());
        //获取该代办记录下的所有结薪用户信息
        HrmsSalarySettlementUserInfoQuery settlementUserInfoQuery = new HrmsSalarySettlementUserInfoQuery();
        settlementUserInfoQuery.setPaymentCountry(agentRecordDOList.get(0).getPaymentCountry());
        settlementUserInfoQuery.setPaymentMonth(agentRecordDOList.get(0).getPaymentMonth());
        settlementUserInfoQuery.setSalarySchemeConfigNo(agentRecordDOList.get(0).getSalarySchemeConfigNo());
        settlementUserInfoQuery.setIdList(salarySettlementFormUserInfoIdList);
        settlementUserInfoQuery.setIsLatest(BusinessConstant.Y);
        List<HrmsSalarySettlementUserInfoDO> allSettlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.listByQuery(settlementUserInfoQuery);
        List<Long> salarySettlementUserInfoIdList = allSettlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getId)
                .collect(Collectors.toList());
        //看看这些计薪用户有没有已经生成数据了(需要传入提报模版ID，一个用户可已有多种提报模版数据，比如这里仅仅需要考勤的数据，不需要绩效等其他数据)
        // 同一个用户的一个科目，可能存在多个数据来源，比如系统获取的考勤，然后提报通过后，现在又进来再次提报，那么应该展示的是提报的数据
        List<HrmsSalarySettlementUserDetailDO> allSettlementUserDetailDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(salarySettlementUserInfoIdList)) {
            HrmsSalarySettlementUserDetailQuery settlementUserDetailQuery = HrmsSalarySettlementUserDetailQuery.builder()
                    .salarySettlementUserInfoIdList(salarySettlementUserInfoIdList)
                    .salarySubmitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId())
                    .isLatest(BusinessConstant.Y)
                    .build();
            allSettlementUserDetailDOList = hrmsSalarySettlementUserDetailManage.listByQuery(settlementUserDetailQuery);
        }
        if (CollectionUtils.isNotEmpty(salarySettlementFormUserItemIdList)) {
            itemDetail = salarySettlementFormUserItemIdList;
            List<Long> finalSalarySettlementFormUserItemIdList = salarySettlementFormUserItemIdList;
            allSettlementUserDetailDOList = allSettlementUserDetailDOList.stream()
                    .filter(item -> finalSalarySettlementFormUserItemIdList.contains(item.getItemId()))
                    .collect(Collectors.toList());
        }
        List<HrmsSalarySettlementUserDetailDO> filterSettlementUserDetailDOList = new ArrayList<>();
        //每个用户的每个科目进行优先级排序
        for (HrmsSalarySettlementUserInfoDO settlementUserInfoDO : allSettlementUserInfoDOList) {
            List<HrmsSalarySettlementUserDetailDO> userDetailDOList = allSettlementUserDetailDOList.stream()
                    .filter(item -> item.getSalarySettlementUserInfoId().equals(settlementUserInfoDO.getId()))
                    .collect(Collectors.toList());
            hrmsSalaryBaseService.getHighestLevelObjectDetail(userDetailDOList, filterSettlementUserDetailDOList, SalarySettlementUserDetailDataSourceEnum.SALARY_CALCULATE);
        }
        List<SalarySettlementFormUserVO> formUserVOList = new ArrayList<>();
        formUserDetailHandler(BusinessConstant.N, allSettlementUserInfoDOList, new ArrayList<>(), filterSettlementUserDetailDOList, itemDetail, agentRecordDOList.get(0), submitTemplateItemMap, formUserVOList);
        return formUserVOList;
    }

    @Override
    public List<SalarySettlementFormUserVO> formClosedAccountUserInfo(SalarySettlementFormClosedAccountUserInfoParam param) {
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().id(param.getSalarySettlementAgentRecordId()).build());
        if (CollectionUtils.isEmpty(agentRecordDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getDesc()));
        }
        //查提报配置中的科目信息
      /*  List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(HrmsSalarySubmitTemplateItemConfigQuery.builder().submitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId()).build());
        if (CollectionUtils.isEmpty(submitTemplateItemConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
        }
        submitTemplateItemConfigDOList = submitTemplateItemConfigDOList.stream()
                .sorted(Comparator.comparing(HrmsSalarySubmitTemplateItemConfigDO::getSort))
                .collect(Collectors.toList());
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> submitTemplateItemMap = submitTemplateItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId));
        List<Long> itemDetail = submitTemplateItemConfigDOList.stream()
                .filter(item -> param.getSalarySettlementFormUserItemIdList().contains(item.getItemConfigId()))
                .map(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId)
                .collect(Collectors.toList());*/

        List<HrmsSalarySettlementUserInfoDO> nowMonthSettlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.selectByIdList(param.getSalarySettlementFormUserInfoIdList());
        List<Long> userIdList = nowMonthSettlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getUserId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        //需要找到上个月给用户的数据
        Long beforeMonth = Long.valueOf(DateUtil.format(DateUtil.offsetMonth(DateUtil.parse(agentRecordDOList.get(0).getPaymentMonth(), "yyyyMM"), -1), "yyyyMM"));
        HrmsSalarySettlementUserInfoQuery beforeMonthSettlementUserInfoQuery = new HrmsSalarySettlementUserInfoQuery();
        beforeMonthSettlementUserInfoQuery.setPaymentCountry(agentRecordDOList.get(0).getPaymentCountry());
        beforeMonthSettlementUserInfoQuery.setPaymentMonth(beforeMonth.toString());
        beforeMonthSettlementUserInfoQuery.setSalarySchemeConfigNo(agentRecordDOList.get(0).getSalarySchemeConfigNo());
        beforeMonthSettlementUserInfoQuery.setUserIdList(userIdList);
        beforeMonthSettlementUserInfoQuery.setIsLatest(BusinessConstant.Y);
        List<HrmsSalarySettlementUserInfoDO> beforeMonthSettlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.listByQuery(beforeMonthSettlementUserInfoQuery);
        List<Long> beforeMonthSalarySettlementUserInfoIdList = beforeMonthSettlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getId)
                .collect(Collectors.toList());
        List<HrmsSalarySettlementUserDetailDO> beforeMonthSettlementUserDetailDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(beforeMonthSalarySettlementUserInfoIdList)) {
            HrmsSalarySettlementUserDetailQuery settlementUserDetailQuery = HrmsSalarySettlementUserDetailQuery.builder()
                    .salarySettlementUserInfoIdList(beforeMonthSalarySettlementUserInfoIdList)
                    .salarySubmitTemplateConfigNo(agentRecordDOList.get(0).getSalarySubmitTemplateConfigNo())
                    .dataSource(SalarySettlementUserDetailDataSourceEnum.SALARY_CALCULATE.getCode())
                    .status(SalarySettlementUserItemStatusEnum.CLOSED_ACCOUNT.getCode())
                    .isLatest(BusinessConstant.Y)
                    .build();
            beforeMonthSettlementUserDetailDOList = hrmsSalarySettlementUserDetailManage.listByQuery(settlementUserDetailQuery).stream()
                    .filter(item -> param.getSalarySettlementFormUserItemIdList().contains(item.getItemId()))
                    .collect(Collectors.toList());
        }
        //获取当前月份的用户信息，在和上个月信息比较
        SalarySettlementFormUserInfoParam userInfoParam = new SalarySettlementFormUserInfoParam();
        userInfoParam.setSalarySettlementAgentRecordId(param.getSalarySettlementAgentRecordId());
        userInfoParam.setSalarySettlementFormUserInfoIdString(StringUtils.join(param.getSalarySettlementFormUserInfoIdList(), ","));
        userInfoParam.setSalarySettlementFormUserItemIdString(StringUtils.join(param.getSalarySettlementFormUserItemIdList(), ","));
        List<SalarySettlementFormUserVO> nowMonthUserDetailList = formUserInfo(userInfoParam);
        for (SalarySettlementFormUserVO formUserVO : nowMonthUserDetailList) {
            for (SalarySettlementFormUserDetailVO detailVO : formUserVO.getFormUserDetailVOList()) {
                detailVO.setItemValue("");
                List<HrmsSalarySettlementUserInfoDO> existBeforeMonthSettlementUserInfoDOList = beforeMonthSettlementUserInfoDOList.stream()
                        .filter(item -> item.getUserId().equals(formUserVO.getUserId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existBeforeMonthSettlementUserInfoDOList)) {
                    continue;
                }
                List<HrmsSalarySettlementUserDetailDO> existBeforeMonthSettlementUserDetailDOList = beforeMonthSettlementUserDetailDOList.stream()
                        .filter(item -> item.getSalarySettlementUserInfoId().equals(existBeforeMonthSettlementUserInfoDOList.get(0).getId())
                                && item.getItemId().equals(detailVO.getItemId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existBeforeMonthSettlementUserDetailDOList)) {
                    continue;
                }
                detailVO.setItemValue(existBeforeMonthSettlementUserDetailDOList.get(0).getItemValue());
            }
        }
        return nowMonthUserDetailList;
    }


    @Override
    public void formCancel(SalarySettlementFormCancelParam param) {
        HrmsSalarySettlementApprovalFormDO formDO = hrmsSalarySettlementApprovalFormManage.getById(param.getFormId());
        if (formDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getDesc()));
        }
        if (!StringUtils.equalsIgnoreCase(formDO.getFormStatus(), SalarySettlementFormStatusEnum.STAGING.getCode())
                && !StringUtils.equalsIgnoreCase(formDO.getFormStatus(), SalarySettlementFormStatusEnum.WITHDRAWN.getCode())) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_ONLY_STAGE_AND_WITHDRAWN_CAN_BE_CANCEL.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_ONLY_STAGE_AND_WITHDRAWN_CAN_BE_CANCEL.getDesc()));
        }
        formDO.setFormStatus(SalarySettlementFormStatusEnum.CANCEL.getCode());
        this.fillDOUpdate(formDO);
        hrmsSalarySettlementApprovalFormManage.formCancel(formDO);
    }

    @Override
    public void formWithdrawn(SalarySettlementFormWithdrawParam param) {
        HrmsSalarySettlementApprovalFormDO formDO = hrmsSalarySettlementApprovalFormManage.getById(param.getFormId());
        if (formDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NOT_EXIST.getDesc()));
        }
        if (!StringUtils.equalsIgnoreCase(formDO.getFormStatus(), SalarySettlementFormStatusEnum.IN_REVIEW.getCode())) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_ONLY_IN_REVIEW_CAN_BE_WITHDRAWN.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_ONLY_IN_REVIEW_CAN_BE_WITHDRAWN.getDesc()));
        }
        formDO.setFormStatus(SalarySettlementFormStatusEnum.WITHDRAWN.getCode());
        this.fillDOUpdate(formDO);
        //将BPM审批中心的单据状态修改
        bpmCreateApprovalService.backApply(formDO.getApprovalId());
        hrmsSalarySettlementApprovalFormManage.formWithdrawn(formDO);
    }

    @Override
    public PaginationResult<SalarySettlementFormUserVerticalTableExportVO> formUserInfoVerticalTableExport(SalarySettlementFormUserInfoParam param) {
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().id(param.getSalarySettlementAgentRecordId()).build());
        if (CollectionUtils.isEmpty(agentRecordDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getDesc()));
        }
        List<SalarySettlementFormUserVO> settlementFormUserVOList = new ArrayList<>();
        if (param.getFormId() != null) {
            SalarySettlementFormDetailParam detailParam = new SalarySettlementFormDetailParam();
            detailParam.setFormId(param.getFormId());
            SalarySettlementFromDetailVO fromDetailVO = formDetail(detailParam);
            settlementFormUserVOList = fromDetailVO.getFormUserVOList();
        } else {
            settlementFormUserVOList = formUserInfo(param);
        }
        List<SalarySettlementFormUserVerticalTableExportVO> exportVOList = new ArrayList<>();
        for (SalarySettlementFormUserVO formUserVO : settlementFormUserVOList) {
            for (SalarySettlementFormUserDetailVO userDetailVO : formUserVO.getFormUserDetailVOList()) {
                SalarySettlementFormUserVerticalTableExportVO exportVO = BeanUtils.convert(userDetailVO, SalarySettlementFormUserVerticalTableExportVO.class);
                exportVO.setPaymentCountry(agentRecordDOList.get(0).getPaymentCountry());
                exportVO.setPaymentMonth(agentRecordDOList.get(0).getPaymentMonth());
                exportVO.setSalarySchemeNo(agentRecordDOList.get(0).getSalarySchemeConfigNo());
                exportVO.setSalarySubmitTemplateConfigNo(agentRecordDOList.get(0).getSalarySubmitTemplateConfigNo());
                exportVO.setUserCode(formUserVO.getUserCode());
                exportVO.setUserName(formUserVO.getUserName());
                exportVO.setUserNameEn(formUserVO.getUserNameEn());
                exportVO.setDeptName(formUserVO.getDeptName());
                exportVO.setOriginCountry(formUserVO.getOriginCountry());
                exportVO.setCountryName(formUserVO.getCountryName());
                exportVO.setLocationCountry(formUserVO.getLocationCountry());
                exportVO.setLocationProvince(formUserVO.getLocationProvince());
                exportVO.setLocationCity(formUserVO.getLocationCity());
                exportVO.setResidentLocation(formUserVO.getResidentLocation());
                exportVOList.add(exportVO);
            }
        }
        PageInfo<SalarySettlementFormUserVerticalTableExportVO> pageInfoResult = new PageInfo<>();
        pageInfoResult.setTotal(exportVOList.size());
        pageInfoResult.setPages(0);
        int startNumber = (param.getCurrentPage() - 1) * param.getShowCount();
        int endNumber = param.getShowCount() + startNumber;
        if (CollectionUtils.isNotEmpty(exportVOList)) {
            if (exportVOList.size() < endNumber) {
                exportVOList = exportVOList.subList(startNumber, exportVOList.size());
            } else {
                exportVOList = exportVOList.subList(startNumber, endNumber);
            }
        }
        if (CollectionUtils.isNotEmpty(exportVOList)) {
            pageInfoResult.setPages(exportVOList.size() % param.getShowCount() == 0 ? exportVOList.size() / param.getShowCount() : exportVOList.size() / param.getShowCount() + 1);
        }
        pageInfoResult.setList(exportVOList);
        return getPageResult(pageInfoResult, param);
    }

    @Override
    public List<SalarySettlementUserDataTitleExportVO> formUserInfoHorizontalTableTitleExport(SalarySettlementFormUserInfoParam param) {
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().id(param.getSalarySettlementAgentRecordId()).build());
        if (CollectionUtils.isEmpty(agentRecordDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getDesc()));
        }
        HrmsSalarySubmitTemplateItemConfigQuery templateItemConfigQuery = HrmsSalarySubmitTemplateItemConfigQuery.builder()
                .submitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId())
                .build();
        //查提报配置中的科目信息
        List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(templateItemConfigQuery).stream()
                .sorted(Comparator.comparing(HrmsSalarySubmitTemplateItemConfigDO::getSort))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(submitTemplateItemConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
        }
        List<Long> itemDetail = submitTemplateItemConfigDOList.stream()
                .map(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId)
                .collect(Collectors.toList());
        if (StringUtils.isNotBlank(param.getSalarySettlementFormUserItemIdString())) {
            itemDetail = Arrays.asList((Long[]) ConvertUtils.convert(param.getSalarySettlementFormUserItemIdString().split(","), Long.class));
        }
        List<SalarySettlementUserDataTitleExportVO> resultList = new ArrayList<>();
        titleExportBuild("userCode", RequestInfoHolder.isChinese() ? "账号" : "Account", resultList);
        titleExportBuild("userName", RequestInfoHolder.isChinese() ? "姓名" : "Legal Name", resultList);
        titleExportBuild("userNameEn", RequestInfoHolder.isChinese() ? "英文名" : "English Name", resultList);
        titleExportBuild("originCountry", RequestInfoHolder.isChinese() ? "核算组织" : "Accounting Organization", resultList);
        titleExportBuild("residentLocation", RequestInfoHolder.isChinese() ? "常驻地" : "Resident Location", resultList);
        titleExportBuild("paymentCountry", RequestInfoHolder.isChinese() ? "计薪国" : "Payroll Country", resultList);
        titleExportBuild("paymentMonth", RequestInfoHolder.isChinese() ? "计薪月" : "Payroll Month", resultList);
        titleExportBuild("schemeNo", RequestInfoHolder.isChinese() ? "计薪方案" : "SchemeNo", resultList);
        titleExportBuild("salarySubmitTemplateConfigNo", RequestInfoHolder.isChinese() ? "薪资数据提报模版配置编码" : "SalarySubmitTemplateConfigNo", resultList);
        titleExportBuild("deptName", RequestInfoHolder.isChinese() ? "部门" : "Department", resultList);
        List<HrmsSalaryItemConfigDO> salaryItemList = hrmsSalaryItemConfigManage.selectItemConfigByIdList(itemDetail);
        for (HrmsSalaryItemConfigDO itemConfigDO : salaryItemList) {
            titleExportBuild(itemConfigDO.getItemNo(), RequestInfoHolder.isChinese() ? itemConfigDO.getItemNameCn() + "(" + itemConfigDO.getItemNo() + ")" : itemConfigDO.getItemNameEn() + "(" + itemConfigDO.getItemNo() + ")", resultList);
        }
        return resultList;
    }

    @Override
    public PaginationResult<Map<String, String>> formUserInfoHorizontalTableExport(SalarySettlementFormUserInfoParam param) {
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(HrmsSalarySettlementAgentRecordQuery.builder().id(param.getSalarySettlementAgentRecordId()).build());
        if (CollectionUtils.isEmpty(agentRecordDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_AGENT_RECORD_NOT_EXIST.getDesc()));
        }
        List<SalarySettlementFormUserVO> settlementFormUserVOList = new ArrayList<>();
        if (param.getFormId() != null) {
            SalarySettlementFormDetailParam detailParam = new SalarySettlementFormDetailParam();
            detailParam.setFormId(param.getFormId());
            SalarySettlementFromDetailVO fromDetailVO = formDetail(detailParam);
            settlementFormUserVOList = fromDetailVO.getFormUserVOList();
        } else {
            settlementFormUserVOList = formUserInfo(param);
        }
        PageInfo<Map<String, String>> pageInfoResult = new PageInfo<>();
        List<Map<String, String>> resList = new ArrayList<>();
        for (SalarySettlementFormUserVO formUserVO : settlementFormUserVOList) {
            Map<String, String> map = new LinkedHashMap<>();
            map.put("userCode", formUserVO.getUserCode());
            map.put("userName", formUserVO.getUserName());
            map.put("userNameEn", formUserVO.getUserNameEn());
            map.put("originCountry", formUserVO.getOriginCountry());
            map.put("residentLocation", formUserVO.getResidentLocation());
            map.put("paymentCountry", formUserVO.getPaymentCountry());
            map.put("paymentMonth", formUserVO.getPaymentMonth());
            map.put("schemeNo", formUserVO.getSalarySchemeConfigNo());
            map.put("salarySubmitTemplateConfigNo", agentRecordDOList.get(0).getSalarySubmitTemplateConfigNo());
            map.put("deptName", formUserVO.getDeptName());
            for (SalarySettlementFormUserDetailVO userDetailVO : formUserVO.getFormUserDetailVOList()) {
                map.put(userDetailVO.getItemNo(), userDetailVO.getItemValue());
            }
            resList.add(map);
        }
        pageInfoResult.setList(resList);
        return getPageResult(pageInfoResult, param);
    }

    @Override
    public String formUserInfoVerticalTableTemplateExport(SalarySettlementFormUserInfoParam param) {
        List<SalarySettlementFormUserVerticalTableExportVO> verticalTableExportVOList = formUserInfoVerticalTableExport(param).getResults();
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (SalarySettlementFormUserVerticalTableExportVO verticalTableExportVO : verticalTableExportVOList) {
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            map.put(RequestInfoHolder.isChinese() ? "账号" : "Account", verticalTableExportVO.getUserCode());
            map.put(RequestInfoHolder.isChinese() ? "姓名" : "Legal Name", verticalTableExportVO.getUserName());
            map.put(RequestInfoHolder.isChinese() ? "核算组织" : "Accounting Organization", verticalTableExportVO.getOriginCountry());
            map.put(RequestInfoHolder.isChinese() ? "常驻地" : "Resident Location", verticalTableExportVO.getResidentLocation());
            map.put(RequestInfoHolder.isChinese() ? "计薪国" : "Payroll Country", verticalTableExportVO.getPaymentCountry());
            map.put(RequestInfoHolder.isChinese() ? "计薪月" : "Payroll Month", verticalTableExportVO.getPaymentMonth());
            map.put(RequestInfoHolder.isChinese() ? "计薪方案" : "SchemeNo", verticalTableExportVO.getSalarySchemeNo());
            map.put(RequestInfoHolder.isChinese() ? "薪资数据提报模版配置编码" : "SalarySubmitTemplateConfigNo", verticalTableExportVO.getSalarySubmitTemplateConfigNo());
            map.put(RequestInfoHolder.isChinese() ? "部门" : "Department", verticalTableExportVO.getDeptName());
            map.put(RequestInfoHolder.isChinese() ? "科目" : "Item", verticalTableExportVO.getItemName());
            map.put(RequestInfoHolder.isChinese() ? "属性" : "Attribute", verticalTableExportVO.getItemAttributeDesc());
            map.put(RequestInfoHolder.isChinese() ? "取值方式" : "Value method", verticalTableExportVO.getItemValueTypeDesc());
            map.put(RequestInfoHolder.isChinese() ? "是否必填" : "Required or not", verticalTableExportVO.getIsRequiredDesc());
            map.put(RequestInfoHolder.isChinese() ? "格式" : "Format", verticalTableExportVO.getItemFormatDesc());
            map.put(RequestInfoHolder.isChinese() ? "币种" : "Currency", verticalTableExportVO.getCurrency());
            map.put(RequestInfoHolder.isChinese() ? "值" : "Value", verticalTableExportVO.getItemValue());
            mapList.add(map);
        }
        if (CollectionUtils.isEmpty(mapList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_USER_INFO_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_USER_INFO_NOT_EXIST.getDesc()));
        }
        String fileName = RequestInfoHolder.isChinese() ? "数据导入模版" + ".xlsx" : "Submission Data Import Template" + ".xlsx";
        File file = ExcelUtils.getInstance().createExcelNoUpload(mapList, fileName, RequestInfoHolder.isChinese() ? "数据导入模版" : "Submission Data Import Template", false, null);
        String url = ipepIntegration.upload("impexp/job_excel/", true, fileName, 1, file);
        return url;
    }

    @Override
    public String formUserInfoHorizontalTableTemplateExport(SalarySettlementFormUserInfoParam param) {
        List<Map<String, String>> horizontalTableExportVOList = formUserInfoHorizontalTableExport(param).getResults();
        List<SalarySettlementUserDataTitleExportVO> dataTitleExportVOList = formUserInfoHorizontalTableTitleExport(param);
        Map<String, String> dataTitleExportVOMap = dataTitleExportVOList.stream().collect(Collectors.toMap(SalarySettlementUserDataTitleExportVO::getKey, SalarySettlementUserDataTitleExportVO::getValue));

        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, String> horizontalTableExportMap : horizontalTableExportVOList) {

            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            for (Map.Entry<String, String> entry : horizontalTableExportMap.entrySet()) {
                String userDataTitleExportValue = dataTitleExportVOMap.get(entry.getKey());
                map.put(userDataTitleExportValue, entry.getValue());
            }

            mapList.add(map);
        }
        String fileName = RequestInfoHolder.isChinese() ? "横表数据导入模版" + ".xlsx" : "Submission Data Import Horizontal Table Template" + ".xlsx";
        File file = ExcelUtils.getInstance().createExcelNoUpload(mapList, fileName, RequestInfoHolder.isChinese() ? "数据导入模版" : "Submission Data Import Horizontal Table Template", false, null);
        return ipepIntegration.upload("impexp/job_excel/", true, fileName, 1, file);
    }

    @Override
    public List<Map<String, String>> formUserInfoVerticalTableImport(String importListStr) {
        List<Map<String, String>> errorMap = new ArrayList<>();
        List<Map<String, String>> listObject = JSONArray.parseObject(importListStr, List.class);
        if (CollectionUtils.isEmpty(listObject)) {
            return errorMap;
        }
        List<HrmsSalarySettlementUserDetailDO> addUserDetailList = new ArrayList<>();
        formUserInfoVerticalTableImportCheck(addUserDetailList, listObject, errorMap);

        //入缓存
        redissonClient.delete(salarySettlementImportRedisKey + RequestInfoHolder.getUserCode());
        redissonClient.set(salarySettlementImportRedisKey + RequestInfoHolder.getUserCode(), JSON.toJSONString(addUserDetailList), SALARY_SETTLEMENT_IMPORT_REDIS_EXPIRE_TIME);

//        hrmsSalarySettlementUserInfoManage.formUserDetailImportUpdate(addUserDetailList, updateUserDetailList, addItemRecordList, updateItemRecordList);
        return errorMap;
    }

    @Override
    public List<Map<String, String>> formUserInfoHorizontalTableImport(String importListStr) {
        List<Map<String, String>> errorMap = new ArrayList<>();
        List<Map<String, String>> listObject = JSONArray.parseObject(importListStr, List.class);
        if (CollectionUtils.isEmpty(listObject)) {
            return errorMap;
        }
        List<HrmsSalarySettlementUserDetailDO> addUserDetailList = new ArrayList<>();
        formUserInfoHorizontalTableImportCheck(addUserDetailList, listObject, errorMap);

        //入缓存
        redissonClient.delete(salarySettlementImportRedisKey + RequestInfoHolder.getUserCode());
        redissonClient.set(salarySettlementImportRedisKey + RequestInfoHolder.getUserCode(), JSON.toJSONString(addUserDetailList), SALARY_SETTLEMENT_IMPORT_REDIS_EXPIRE_TIME);

//        hrmsSalarySettlementUserInfoManage.formUserDetailImportUpdate(addUserDetailList, updateUserDetailList, addItemRecordList, updateItemRecordList);
        return errorMap;
    }

    private void formUserInfoVerticalTableImportCheck(List<HrmsSalarySettlementUserDetailDO> addUserDetailList,
                                                      List<Map<String, String>> listObject,
                                                      List<Map<String, String>> errorMap) {
        String paymentCountryName = RequestInfoHolder.isChinese() ? "计薪国" : "Payroll Country";
        String paymentMonthName = RequestInfoHolder.isChinese() ? "计薪月" : "Payroll Month";
        String schemeNoName = RequestInfoHolder.isChinese() ? "计薪方案" : "SchemeNo";
        String salarySubmitTemplateConfigNoName = RequestInfoHolder.isChinese() ? "薪资数据提报模版配置编码" : "SalarySubmitTemplateConfigNo";
        String userCodeName = RequestInfoHolder.isChinese() ? "账号" : "Account";
        String itemName = RequestInfoHolder.isChinese() ? "科目" : "Item";
        String valueName = RequestInfoHolder.isChinese() ? "值" : "Value";
        //计薪国&计薪月&计薪方案必须都一致，且不能为空
        List<String> paymentCountryList = listObject.stream().filter(m -> m.containsKey(paymentCountryName)).map(m -> m.get(paymentCountryName).trim()).distinct().collect(Collectors.toList());
        List<String> paymentMonthList = listObject.stream().filter(m -> m.containsKey(paymentMonthName)).map(m -> m.get(paymentMonthName).trim()).distinct().collect(Collectors.toList());
        List<String> salarySchemeNoList = listObject.stream().filter(m -> m.containsKey(schemeNoName)).map(m -> m.get(schemeNoName).trim()).distinct().collect(Collectors.toList());
        List<String> salarySubmitTemplateConfigNoList = listObject.stream().filter(m -> m.containsKey(salarySubmitTemplateConfigNoName)).map(m -> m.get(salarySubmitTemplateConfigNoName).trim()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paymentCountryList) || paymentCountryList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪国家不能为空并且必须相同" : "payroll country not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        if (CollectionUtils.isEmpty(paymentMonthList) || paymentMonthList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪月份不能为空并且必须相同" : "payroll month not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        if (CollectionUtils.isEmpty(salarySchemeNoList) || salarySchemeNoList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪方案不能为空并且必须相同" : "salary scheme not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        if (CollectionUtils.isEmpty(salarySubmitTemplateConfigNoList) || salarySubmitTemplateConfigNoList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "薪资数据提报模版配置编码不能为空并且必须相同" : "salary submit template config no not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        SalaryPaymentCountryQuery salaryPaymentCountryQuery = SalaryPaymentCountryQuery.builder()
                .paymentCountry(paymentCountryList.get(0))
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(salaryPaymentCountryQuery);
        if (CollectionUtils.isEmpty(paymentCountryConfigDOList)) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪国配置不存在，请前往【计薪国配置】查看启用中的计薪国." : "Payroll country configuration does not exist, please go to [Payroll Country Configuration] to check the active payroll country.");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        //找到代办单据和结薪用户
        HrmsSalarySettlementAgentRecordQuery agentRecordQuery = HrmsSalarySettlementAgentRecordQuery.builder()
                .paymentCountry(paymentCountryList.get(0))
                .paymentMonth(paymentMonthList.get(0))
                .salarySchemeConfigNo(salarySchemeNoList.get(0))
                .salarySubmitTemplateConfigNo(salarySubmitTemplateConfigNoList.get(0))
                .build();
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(agentRecordQuery);
        if (CollectionUtils.isEmpty(agentRecordDOList) || agentRecordDOList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "代办记录不存在或者不唯一" : "agent record not exist");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }

        HrmsSalarySubmitTemplateItemConfigQuery templateItemConfigQuery = HrmsSalarySubmitTemplateItemConfigQuery.builder()
                .submitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId())
                .build();
        List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(templateItemConfigQuery);
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> templateItemMap = submitTemplateItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId));

        HrmsSalarySettlementUserInfoQuery settlementUserInfoQuery = new HrmsSalarySettlementUserInfoQuery();
        settlementUserInfoQuery.setPaymentCountry(paymentCountryList.get(0));
        settlementUserInfoQuery.setPaymentMonth(paymentMonthList.get(0));
        settlementUserInfoQuery.setSalarySchemeConfigNo(salarySchemeNoList.get(0));
        settlementUserInfoQuery.setIsLatest(BusinessConstant.Y);
        List<HrmsSalarySettlementUserInfoDO> settlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.listByQuery(settlementUserInfoQuery);
        Map<Long, List<HrmsSalarySettlementUserInfoDO>> settlementUserMap = settlementUserInfoDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementUserInfoDO::getUserId));
        List<Long> settlementUserInfoIdList = settlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getId)
                .collect(Collectors.toList());
        //当前结薪用户存在的科目值，与本次导入的科目比较，本次导入的可能是部分科目，所以不能全量删除
        List<HrmsSalarySettlementUserDetailDO> settlementUserDetailDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settlementUserInfoIdList)) {
            HrmsSalarySettlementUserDetailQuery userDetailQuery = HrmsSalarySettlementUserDetailQuery.builder()
                    .salarySettlementUserInfoIdList(settlementUserInfoIdList)
                    .dataSource(SalarySettlementUserDetailDataSourceEnum.SETTLEMENT_APPROVAL.getCode())
                    .salarySubmitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId())
                    .isLatest(BusinessConstant.Y)
                    .build();
            settlementUserDetailDOList = hrmsSalarySettlementUserDetailManage.listByQuery(userDetailQuery);
        }
        List<String> userCodeList = listObject.stream().filter(m -> m.containsKey(userCodeName)).map(m -> m.get(userCodeName).trim()).collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        Map<String, HrmsUserInfoDO> userInfoDOMap = userInfoDOList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, o -> o, (v1, v2) -> v1));

        //获取系统所有科目
        List<HrmsSalaryItemConfigDO> allItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(new SalaryItemConfigQuery());
        Map<Long, List<HrmsSalaryItemConfigDO>> itemMap = allItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        for (Map<String, String> map : listObject) {
            List<String> keyList = new ArrayList<>(map.keySet());
            List<String> valueList = new ArrayList<>(map.values());

            String employeeIdKey = null;
            List<String> employeeIds = keyList.stream().filter(o -> StringUtils.equalsIgnoreCase(o, userCodeName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(employeeIds)) {
                employeeIdKey = employeeIds.get(0);
            }
            String userCode = map.get(employeeIdKey);

            if (StringUtils.isBlank(userCode)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "员工账号不能为空" : "employee id not be empty");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }
            HrmsUserInfoDO userInfoDO = userInfoDOMap.get(userCode);
            if (userInfoDO == null) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "员工不存在" : "employee not exist");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            List<HrmsSalarySettlementUserInfoDO> existSettlementUserList = settlementUserMap.get(userInfoDO.getId());
            if (CollectionUtils.isEmpty(existSettlementUserList)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "结薪用户不存在" : " settlement user not exist");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            String itemNoKey = null;
            List<String> itemNos = keyList.stream().filter(o -> StringUtils.equalsIgnoreCase(o, itemName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemNos)) {
                itemNoKey = itemNos.get(0);
            }
            String itemNo = map.get(itemNoKey);
            if (StringUtils.isBlank(itemNo)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目不能为空" : "item not empty");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            List<HrmsSalaryItemConfigDO> itemList = allItemConfigDOList.stream()
                    .filter(item -> itemNo.contains(item.getItemNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemList)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目在系统中不存在" : "item not exist in system");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            List<HrmsSalarySubmitTemplateItemConfigDO> existSubmitTemplateItemList = templateItemMap.get(itemList.get(0).getId());
            if (CollectionUtils.isEmpty(existSubmitTemplateItemList)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目在报表配置中不存在" : "item not exist in submit template config");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            List<HrmsSalarySettlementUserDetailDO> existUserItemList = settlementUserDetailDOList.stream()
                    .filter(item -> item.getSalarySettlementUserInfoId().equals(existSettlementUserList.get(0).getId())
                            && item.getItemId().equals(itemList.get(0).getId()))
                    .collect(Collectors.toList());

            String itemValueKey = null;
            List<String> itemValues = keyList.stream().filter(o -> StringUtils.equalsIgnoreCase(o, valueName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemValues)) {
                itemValueKey = itemValues.get(0);
            }
            String itemValue = map.get(itemValueKey);

            if (existSubmitTemplateItemList.get(0).getIsRequired().equals(BusinessConstant.Y) && StringUtils.isBlank(itemValue)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目值必填，不能为空" : "item value not empty");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }
            //当科目不为空并且格式是数据或者金额时，要进行大小判断和小数点位数处理
            if (StringUtils.isNotBlank(itemValue)
                    && (StringUtils.equalsIgnoreCase(itemList.get(0).getItemFormat(), SalaryItemFormatEnum.NUMBER.getCode())
                    || StringUtils.equalsIgnoreCase(itemList.get(0).getItemFormat(), SalaryItemFormatEnum.AMOUNT.getCode()))) {
                BigDecimal valueBigdecimal = BigDecimal.ZERO;
                try {
                    valueBigdecimal = new BigDecimal(itemValue);
                } catch (Exception e) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目值格式有误" : "item value format error");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                if (valueBigdecimal.compareTo(BigDecimal.valueOf(existSubmitTemplateItemList.get(0).getMaxValue())) > 0) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目值不能超过最大值:" + existSubmitTemplateItemList.get(0).getMaxValue() : "item value not bigger than" + existSubmitTemplateItemList.get(0).getMaxValue());
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                if (valueBigdecimal.compareTo(BigDecimal.valueOf(existSubmitTemplateItemList.get(0).getMinValue())) < 0) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目值不能小于最小值:" + existSubmitTemplateItemList.get(0).getMinValue() : "item value not less than" + existSubmitTemplateItemList.get(0).getMinValue());
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                List<String> bigdecimalStringList = Arrays.asList(valueBigdecimal.toString().split("\\."));
                if (CollectionUtils.isNotEmpty(bigdecimalStringList) && bigdecimalStringList.size() == 2 && bigdecimalStringList.get(1).length() > paymentCountryConfigDOList.get(0).getCurrencyPrecision()) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "数字小数位不正确，当前国家仅支持" + paymentCountryConfigDOList.get(0).getCurrencyPrecision() + "位小数" : "Decimal places for numbers are incorrect, and only " + paymentCountryConfigDOList.get(0).getCurrencyPrecision() + " decimal places are currently supported in the country");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                itemValue = valueBigdecimal.setScale(paymentCountryConfigDOList.get(0).getCurrencyPrecision(), RoundingMode.HALF_UP).toString();
            }
            if (StringUtils.isNotBlank(itemValue) && StringUtils.equalsIgnoreCase(itemList.get(0).getItemFormat(), SalaryItemFormatEnum.TEXT.getCode())) {
                if (itemValue.length() > existSubmitTemplateItemList.get(0).getMaxCharacter()) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "科目值不能大于最大可输入字符:" + existSubmitTemplateItemList.get(0).getMaxCharacter() : "item value not exceed than" + existSubmitTemplateItemList.get(0).getMaxCharacter());
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
            }
            //日期格式校验
            if (StringUtils.isNotBlank(itemValue) && StringUtils.equalsIgnoreCase(itemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM.getCode())) {
                if (itemValue.contains("-") || itemValue.contains("/")) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "日期格式错误，应为:" + "yyyyMM" : "item value format error,date should be:" + "yyyyMM");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                try {
                    DateUtil.parse(itemValue, "yyyyMM");
                } catch (Exception e) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "日期格式错误，应为:" + "yyyyMM" : "item value format error,date should be:" + "yyyyMM");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                itemValue = DateUtil.format(DateUtil.parse(itemValue, "yyyyMM"), "yyyy-MM");
            }
            if (StringUtils.isNotBlank(itemValue) && StringUtils.equalsIgnoreCase(itemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM_DD.getCode())) {
                if (itemValue.contains("-") || itemValue.contains("/")) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "日期格式错误，应为:" + "yyyyMMdd" : "item value format error,date should be:" + "yyyyMMdd");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                try {
                    DateUtil.parse(itemValue, "yyyyMMdd");
                } catch (Exception e) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "日期格式错误，应为:" + "yyyyMMdd" : "item value format error,date should be:" + "yyyyMMdd");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                itemValue = DateUtil.format(DateUtil.parse(itemValue, "yyyyMMdd"), "yyyy-MM-dd");
            }
            //不允许编辑时，如果历史不存在数据或者与历史数据不一致，报错
            if (existSubmitTemplateItemList.get(0).getIsAllowEdit().equals(BusinessConstant.N)) {
                if (CollectionUtils.isNotEmpty(existUserItemList)
                        && !StringUtils.equalsIgnoreCase(existUserItemList.get(0).getItemValue(), itemValue)) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "该值不能编辑，请保留为空" : "Item value cannot be edited, please leave it empty");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
                if (CollectionUtils.isEmpty(existUserItemList)) {
                    map.put("errorMessage", RequestInfoHolder.isChinese() ? "该值不能编辑，请保留为空" : "Item value cannot be edited, please leave it empty");
                    map.put("success", "false");
                    errorMap.add(map);
                    continue;
                }
            }

            HrmsSalarySettlementUserDetailDO userDetailDO = new HrmsSalarySettlementUserDetailDO();
            userDetailDO.setId(iHrmsIdWorker.nextId());
            userDetailDO.setSalarySettlementUserInfoId(existSettlementUserList.get(0).getId());
            userDetailDO.setSalarySubmitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId());
            userDetailDO.setSalarySubmitTemplateConfigNo(agentRecordDOList.get(0).getSalarySubmitTemplateConfigNo());
            userDetailDO.setItemId(itemList.get(0).getId());
            userDetailDO.setItemNo(itemList.get(0).getItemNo());
            userDetailDO.setItemValue(itemValue);
            userDetailDO.setDataSource(SalarySettlementUserDetailDataSourceEnum.SETTLEMENT_APPROVAL.getCode());
            userDetailDO.setStatus(SalarySettlementUserItemStatusEnum.UNLOCKED.getCode());
            userDetailDO.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsert(userDetailDO);
            addUserDetailList.add(userDetailDO);
        }
    }

    private void formUserInfoHorizontalTableImportCheck(List<HrmsSalarySettlementUserDetailDO> addUserDetailList,
                                                        List<Map<String, String>> listObject,
                                                        List<Map<String, String>> errorMap) {
        String paymentCountryName = RequestInfoHolder.isChinese() ? "计薪国" : "Payroll Country";
        String paymentMonthName = RequestInfoHolder.isChinese() ? "计薪月" : "Payroll Month";
        String schemeNoName = RequestInfoHolder.isChinese() ? "计薪方案" : "SchemeNo";
        String salarySubmitTemplateConfigNoName = RequestInfoHolder.isChinese() ? "薪资数据提报模版配置编码" : "SalarySubmitTemplateConfigNo";
        String userCodeName = RequestInfoHolder.isChinese() ? "账号" : "Account";
        //计薪国&计薪月&计薪方案必须都一致，且不能为空
        List<String> paymentCountryList = listObject.stream().filter(m -> m.containsKey(paymentCountryName)).map(m -> m.get(paymentCountryName).trim()).distinct().collect(Collectors.toList());
        List<String> paymentMonthList = listObject.stream().filter(m -> m.containsKey(paymentMonthName)).map(m -> m.get(paymentMonthName).trim()).distinct().collect(Collectors.toList());
        List<String> salarySchemeNoList = listObject.stream().filter(m -> m.containsKey(schemeNoName)).map(m -> m.get(schemeNoName).trim()).distinct().collect(Collectors.toList());
        List<String> salarySubmitTemplateConfigNoList = listObject.stream().filter(m -> m.containsKey(salarySubmitTemplateConfigNoName)).map(m -> m.get(salarySubmitTemplateConfigNoName).trim()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paymentCountryList) || paymentCountryList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪国家不能为空并且必须相同" : "payroll country not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        if (CollectionUtils.isEmpty(paymentMonthList) || paymentMonthList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪月份不能为空并且必须相同" : "payroll month not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        if (CollectionUtils.isEmpty(salarySchemeNoList) || salarySchemeNoList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪方案不能为空并且必须相同" : "salary scheme not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        if (CollectionUtils.isEmpty(salarySubmitTemplateConfigNoList) || salarySubmitTemplateConfigNoList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "薪资数据提报模版配置编码不能为空并且必须相同" : "salary submit template config no not be empty and must be the same");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        SalaryPaymentCountryQuery salaryPaymentCountryQuery = SalaryPaymentCountryQuery.builder()
                .paymentCountry(paymentCountryList.get(0))
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(salaryPaymentCountryQuery);
        if (CollectionUtils.isEmpty(paymentCountryConfigDOList)) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "计薪国配置不存在，请前往【计薪国配置】查看启用中的计薪国." : "Payroll country configuration does not exist, please go to [Payroll Country Configuration] to check the active payroll country.");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }
        //找到代办单据和结薪用户
        HrmsSalarySettlementAgentRecordQuery agentRecordQuery = HrmsSalarySettlementAgentRecordQuery.builder()
                .paymentCountry(paymentCountryList.get(0))
                .paymentMonth(paymentMonthList.get(0))
                .salarySchemeConfigNo(salarySchemeNoList.get(0))
                .salarySubmitTemplateConfigNo(salarySubmitTemplateConfigNoList.get(0))
                .build();
        List<HrmsSalarySettlementAgentRecordDO> agentRecordDOList = hrmsSalarySettlementAgentRecordManage.listByQuery(agentRecordQuery);
        if (CollectionUtils.isEmpty(agentRecordDOList) || agentRecordDOList.size() > 1) {
            listObject.forEach(item -> {
                item.put("errorMessage", RequestInfoHolder.isChinese() ? "代办记录不存在或者不唯一" : "agent record not exist");
                item.put("success", "false");
                errorMap.add(item);
            });
            return;
        }

        HrmsSalarySubmitTemplateItemConfigQuery templateItemConfigQuery = HrmsSalarySubmitTemplateItemConfigQuery.builder()
                .submitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId())
                .build();
        List<HrmsSalarySubmitTemplateItemConfigDO> submitTemplateItemConfigDOList = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(templateItemConfigQuery);
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> templateItemMap = submitTemplateItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId));

        HrmsSalarySettlementUserInfoQuery settlementUserInfoQuery = new HrmsSalarySettlementUserInfoQuery();
        settlementUserInfoQuery.setPaymentCountry(paymentCountryList.get(0));
        settlementUserInfoQuery.setPaymentMonth(paymentMonthList.get(0));
        settlementUserInfoQuery.setSalarySchemeConfigNo(salarySchemeNoList.get(0));
        settlementUserInfoQuery.setIsLatest(BusinessConstant.Y);
        List<HrmsSalarySettlementUserInfoDO> settlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.listByQuery(settlementUserInfoQuery);
        Map<Long, List<HrmsSalarySettlementUserInfoDO>> settlementUserMap = settlementUserInfoDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementUserInfoDO::getUserId));
        List<Long> settlementUserInfoIdList = settlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getId)
                .collect(Collectors.toList());
        //当前结薪用户存在的科目值，与本次导入的科目比较，本次导入的可能是部分科目，所以不能全量删除
        List<HrmsSalarySettlementUserDetailDO> settlementUserDetailDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settlementUserInfoIdList)) {
            HrmsSalarySettlementUserDetailQuery userDetailQuery = HrmsSalarySettlementUserDetailQuery.builder()
                    .salarySettlementUserInfoIdList(settlementUserInfoIdList)
                    .dataSource(SalarySettlementUserDetailDataSourceEnum.SETTLEMENT_APPROVAL.getCode())
                    .salarySubmitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId())
                    .isLatest(BusinessConstant.Y)
                    .build();
            settlementUserDetailDOList = hrmsSalarySettlementUserDetailManage.listByQuery(userDetailQuery);
        }
        //settlementUserDetailDOList按照salarySettlementUserInfoId分组
        Map<Long, List<HrmsSalarySettlementUserDetailDO>> settlementUserDetailDOMap = settlementUserDetailDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementUserDetailDO::getSalarySettlementUserInfoId));
        List<Long> settlementUserIdList = settlementUserDetailDOList.stream().map(HrmsSalarySettlementUserDetailDO::getSalarySettlementUserInfoId).distinct().collect(Collectors.toList());
        List<HrmsSalarySettlementUserInfoDO> salarySettlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.selectByIdList(settlementUserIdList);
        Map<Long, Long> salarySettlementUserMap = salarySettlementUserInfoDOList.stream().collect(Collectors.toMap(HrmsSalarySettlementUserInfoDO::getUserId, HrmsSalarySettlementUserInfoDO::getId));

        SalaryItemConfigQuery salaryItemConfigQuery = new SalaryItemConfigQuery();
        salaryItemConfigQuery.setStatus(StatusEnum.ACTIVE.getCode());
        List<HrmsSalaryItemConfigDO> salaryItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(salaryItemConfigQuery);
        Map<String, HrmsSalaryItemConfigDO> itemConfigMap = salaryItemConfigDOList.stream().collect(Collectors.toMap(HrmsSalaryItemConfigDO::getItemNo, o -> o, (v1, v2) -> v1));

        List<String> userCodeList = listObject.stream().filter(m -> m.containsKey(userCodeName)).map(m -> m.get(userCodeName).trim()).collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        Map<String, HrmsUserInfoDO> userInfoDOMap = userInfoDOList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, o -> o, (v1, v2) -> v1));

        //获取系统所有科目
        List<HrmsSalaryItemConfigDO> allItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(new SalaryItemConfigQuery());
        Map<Long, List<HrmsSalaryItemConfigDO>> itemMap = allItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        for (Map<String, String> map : listObject) {
            List<String> keyList = new ArrayList<>(map.keySet());
            List<String> valueList = new ArrayList<>(map.values());

            String employeeIdKey = null;
            List<String> employeeIds = keyList.stream().filter(o -> StringUtils.equalsIgnoreCase(o, userCodeName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(employeeIds)) {
                employeeIdKey = employeeIds.get(0);
            }
            String userCode = map.get(employeeIdKey);

            if (StringUtils.isBlank(userCode)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "员工账号不能为空" : "employee id not be empty");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }
            HrmsUserInfoDO userInfoDO = userInfoDOMap.get(userCode);
            if (userInfoDO == null) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "员工不存在" : "employee not exist");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            List<HrmsSalarySettlementUserInfoDO> existSettlementUserList = settlementUserMap.get(userInfoDO.getId());
            if (CollectionUtils.isEmpty(existSettlementUserList)) {
                map.put("errorMessage", RequestInfoHolder.isChinese() ? "结薪用户不存在" : " settlement user not exist");
                map.put("success", "false");
                errorMap.add(map);
                continue;
            }

            Long settlementUserId = salarySettlementUserMap.get(userInfoDO.getId());

            List<HrmsSalarySettlementUserDetailDO> salarySettlementUserDetailDOList = settlementUserDetailDOMap.get(settlementUserId);

            int isGoOn = 1;
            for (String key : keyList) {
                int start = key.indexOf("(");
                int end = key.indexOf(")");
                if (start >= end) {
                    continue;
                }
                String itemConfigNo = key.substring(start + 1, end);
                HrmsSalaryItemConfigDO objectItem = itemConfigMap.get(itemConfigNo);
                if (objectItem == null) {
                    continue;
                }
                String itemKeyName = RequestInfoHolder.isChinese() ? objectItem.getItemNameCn() + "(" + objectItem.getItemNo() + ")" : objectItem.getItemNameEn() + "(" + objectItem.getItemNo() + ")";
                List<String> objectItemName = keyList.stream().filter(o -> StringUtils.equalsIgnoreCase(o, itemKeyName)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(objectItemName)) {
                    continue;
                }
                String objectItemValue = map.get(objectItemName.get(0));
                if (StringUtils.isBlank(objectItemValue)) {
                    continue;
                }

                List<HrmsSalarySettlementUserDetailDO> existUserItemList = CollectionUtils.isEmpty(salarySettlementUserDetailDOList) ?
                        new ArrayList<>() :
                        salarySettlementUserDetailDOList.stream().filter(o -> o.getItemId().equals(objectItem.getId())).collect(Collectors.toList());

                //当科目不为空并且格式是数据或者金额时，要进行大小判断和小数点位数处理
                List<HrmsSalarySubmitTemplateItemConfigDO> existSubmitTemplateItemList = templateItemMap.get(objectItem.getId());

                if (StringUtils.isNotBlank(objectItemValue)
                        && (StringUtils.equalsIgnoreCase(objectItem.getItemFormat(), SalaryItemFormatEnum.NUMBER.getCode())
                        || StringUtils.equalsIgnoreCase(objectItem.getItemFormat(), SalaryItemFormatEnum.AMOUNT.getCode()))) {
                    BigDecimal valueBigdecimal = BigDecimal.ZERO;
                    try {
                        valueBigdecimal = new BigDecimal(objectItemValue);
                    } catch (Exception e) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "科目值格式有误" : itemKeyName + "item value format error");
                        map.put("success", "false");
                        errorMap.add(map);
                        continue;
                    }
                    if (valueBigdecimal.compareTo(BigDecimal.valueOf(existSubmitTemplateItemList.get(0).getMaxValue())) > 0) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "科目值不能超过最大值:" + existSubmitTemplateItemList.get(0).getMaxValue() : itemKeyName + "item value not bigger than" + existSubmitTemplateItemList.get(0).getMaxValue());
                        map.put("success", "false");
                        errorMap.add(map);
                        isGoOn = 0;
                        break;
                    }
                    if (valueBigdecimal.compareTo(BigDecimal.valueOf(existSubmitTemplateItemList.get(0).getMinValue())) < 0) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "科目值不能小于最小值:" + existSubmitTemplateItemList.get(0).getMinValue() : itemKeyName + "item value not less than" + existSubmitTemplateItemList.get(0).getMinValue());
                        map.put("success", "false");
                        errorMap.add(map);
                        isGoOn = 0;
                        break;
                    }
                    List<String> bigdecimalStringList = Arrays.asList(valueBigdecimal.toString().split("\\."));
                    if (CollectionUtils.isNotEmpty(bigdecimalStringList) && bigdecimalStringList.size() == 2 && bigdecimalStringList.get(1).length() > paymentCountryConfigDOList.get(0).getCurrencyPrecision()) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "数字小数位不正确，当前国家仅支持" + paymentCountryConfigDOList.get(0).getCurrencyPrecision() + "位小数" : itemKeyName + "Decimal places for numbers are incorrect, and only " + paymentCountryConfigDOList.get(0).getCurrencyPrecision() + " decimal places are currently supported in the country");
                        map.put("success", "false");
                        errorMap.add(map);
                        break;
                    }
                    objectItemValue = valueBigdecimal.setScale(paymentCountryConfigDOList.get(0).getCurrencyPrecision(), RoundingMode.HALF_UP).toString();
                }
                if (StringUtils.isNotBlank(objectItemValue) && StringUtils.equalsIgnoreCase(objectItem.getItemFormat(), SalaryItemFormatEnum.TEXT.getCode())) {
                    if (objectItemValue.length() > existSubmitTemplateItemList.get(0).getMaxCharacter()) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "科目值不能大于最大可输入字符:" + existSubmitTemplateItemList.get(0).getMaxCharacter() : itemKeyName + "item value not exceed than" + existSubmitTemplateItemList.get(0).getMaxCharacter());
                        map.put("success", "false");
                        errorMap.add(map);
                        break;
                    }
                }
                //日期格式校验
                if (StringUtils.isNotBlank(objectItemValue) && StringUtils.equalsIgnoreCase(objectItem.getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM.getCode())) {
                    if (objectItemValue.contains("-") || objectItemValue.contains("/")) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? "日期格式错误，应为:" + "yyyyMM" : "item value format error,date should be:" + "yyyyMM");
                        map.put("success", "false");
                        errorMap.add(map);
                        continue;
                    }
                    try {
                        DateUtil.parse(objectItemValue, "yyyyMM");
                    } catch (Exception e) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "日期格式错误，应为:" + "yyyyMM" : itemKeyName + "item value format error,date should be:" + "yyyyMM");
                        map.put("success", "false");
                        errorMap.add(map);
                        break;
                    }
                }
                if (StringUtils.isNotBlank(objectItemValue) && StringUtils.equalsIgnoreCase(objectItem.getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM_DD.getCode())) {
                    if (objectItemValue.contains("-") || objectItemValue.contains("/")) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? "日期格式错误，应为:" + "yyyyMMdd" : "item value format error,date should be:" + "yyyyMMdd");
                        map.put("success", "false");
                        errorMap.add(map);
                        continue;
                    }
                    try {
                        DateUtil.parse(objectItemValue, "yyyyMMdd");
                    } catch (Exception e) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "日期格式错误，应为:" + "yyyyMMdd" : itemKeyName + "item value format error,date should be:" + "yyyyMMdd");
                        map.put("success", "false");
                        errorMap.add(map);
                        break;
                    }
                }
                //不允许编辑时，如果历史不存在数据或者与历史数据不一致，报错
                if (existSubmitTemplateItemList.get(0).getIsAllowEdit().equals(BusinessConstant.N)) {
                    if (CollectionUtils.isNotEmpty(existUserItemList)
                            && !StringUtils.equalsIgnoreCase(existUserItemList.get(0).getItemValue(), objectItemValue)) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "该值不能编辑，请保留为空" : itemKeyName + "Item value cannot be edited, please leave it empty");
                        map.put("success", "false");
                        errorMap.add(map);
                        break;
                    }
                    if (CollectionUtils.isEmpty(existUserItemList)) {
                        map.put("errorMessage", RequestInfoHolder.isChinese() ? itemKeyName + "该值不能编辑，请保留为空" : itemKeyName + "Item value cannot be edited, please leave it empty");
                        map.put("success", "false");
                        errorMap.add(map);
                        break;
                    }
                }

                HrmsSalarySettlementUserDetailDO userDetailDO = new HrmsSalarySettlementUserDetailDO();
                userDetailDO.setId(iHrmsIdWorker.nextId());
                userDetailDO.setSalarySettlementUserInfoId(existSettlementUserList.get(0).getId());
                userDetailDO.setSalarySubmitTemplateConfigId(agentRecordDOList.get(0).getSalarySubmitTemplateConfigId());
                userDetailDO.setSalarySubmitTemplateConfigNo(agentRecordDOList.get(0).getSalarySubmitTemplateConfigNo());
                userDetailDO.setItemId(objectItem.getId());
                userDetailDO.setItemNo(objectItem.getItemNo());
                userDetailDO.setItemValue(objectItemValue);
                userDetailDO.setDataSource(SalarySettlementUserDetailDataSourceEnum.SETTLEMENT_APPROVAL.getCode());
                userDetailDO.setStatus(SalarySettlementUserItemStatusEnum.UNLOCKED.getCode());
                userDetailDO.setIsLatest(BusinessConstant.Y);
                BaseDOUtil.fillDOInsert(userDetailDO);
                addUserDetailList.add(userDetailDO);
            }
        }
    }

    private void importItemRecordHandler(Map<Long, List<HrmsSalaryItemConfigDO>> itemMap,
                                         HrmsSalaryPaymentCountryConfigDO salaryPaymentCountryConfigDO,
                                         HrmsSalarySettlementAgentRecordDO agentRecordDO,
                                         List<HrmsSalarySettlementUserDetailDO> settlementUserDetailDOList,
                                         List<HrmsSalaryItemConfigSubmitRecordDO> addItemRecordList,
                                         List<HrmsSalaryItemConfigSubmitRecordDO> updateItemRecordList) {
        Date date = new Date();
        List<Long> salarySettlementUserInfoIdList = settlementUserDetailDOList.stream()
                .map(HrmsSalarySettlementUserDetailDO::getSalarySettlementUserInfoId)
                .collect(Collectors.toList());
        //找出导入用户计算级别的科目信息
        List<HrmsSalarySettlementUserDetailDO> salaryCaculateDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(salarySettlementUserInfoIdList)) {
            HrmsSalarySettlementUserDetailQuery settlementUserDetailQuery = HrmsSalarySettlementUserDetailQuery.builder()
                    .salarySettlementUserInfoIdList(salarySettlementUserInfoIdList)
                    .salarySubmitTemplateConfigId(agentRecordDO.getSalarySubmitTemplateConfigId())
                    .dataSource(SalarySettlementUserDetailDataSourceEnum.SALARY_CALCULATE.getCode())
                    .isLatest(BusinessConstant.Y)
                    .build();
            salaryCaculateDetailList = hrmsSalarySettlementUserDetailManage.listByQuery(settlementUserDetailQuery);
        }
        //找出导入用户所有科目的操作记录
        HrmsSalaryItemConfigSubmitRecordQuery itemConfigSubmitRecordQuery = HrmsSalaryItemConfigSubmitRecordQuery.builder()
                .salarySettlementUserIdList(salarySettlementUserInfoIdList)
                .build();
        List<HrmsSalaryItemConfigSubmitRecordDO> itemConfigSubmitRecordDOList = hrmsSalaryItemConfigSubmitRecordManage.listByQuery(itemConfigSubmitRecordQuery);

        for (HrmsSalarySettlementUserDetailDO userDetailDO : settlementUserDetailDOList) {
            List<HrmsSalaryItemConfigDO> itemList = itemMap.get(userDetailDO.getItemId());
            if (CollectionUtils.isEmpty(itemList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_ITEM_CONFIG_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_ITEM_CONFIG_IS_EMPTY.getDesc()));
            }
            HrmsSalaryItemConfigSubmitRecordDO recordDO = new HrmsSalaryItemConfigSubmitRecordDO();
            recordDO.setId(iHrmsIdWorker.nextId());
            recordDO.setSalarySettlementUserInfoId(userDetailDO.getSalarySettlementUserInfoId());
            recordDO.setItemId(userDetailDO.getItemId());
            recordDO.setItemValue(userDetailDO.getItemValue());
            if (StringUtils.equalsIgnoreCase(itemList.get(0).getItemFormat(), SalaryItemFormatEnum.AMOUNT.getCode())) {
                recordDO.setCurrency(salaryPaymentCountryConfigDO.getCurrency());
            }
            recordDO.setOperateType(SalarySubmitRecordOperateTypeEnum.MANUAL_SUBMISSION.getCode());
            BaseDOUtil.fillDOInsert(recordDO);
            addItemRecordList.add(recordDO);

            List<HrmsSalarySettlementUserDetailDO> existSalaryCaculateDetailList = salaryCaculateDetailList.stream()
                    .filter(item -> item.getSalarySettlementUserInfoId().equals(userDetailDO.getSalarySettlementUserInfoId())
                            && item.getItemId().equals(userDetailDO.getItemId()))
                    .collect(Collectors.toList());
            List<HrmsSalaryItemConfigSubmitRecordDO> existItemConfigSubmitRecordDOList = itemConfigSubmitRecordDOList.stream()
                    .filter(item -> item.getSalarySettlementUserInfoId().equals(userDetailDO.getSalarySettlementUserInfoId())
                            && item.getItemId().equals(userDetailDO.getItemId()))
                    .sorted(Comparator.comparing(HrmsSalaryItemConfigSubmitRecordDO::getCreateDate).reversed())
                    .collect(Collectors.toList());
            //旧的操作记录的状态需要修改
            if (CollectionUtils.isEmpty(existSalaryCaculateDetailList)) {
                recordDO.setStatusUpdateReason(SalarySubmitRecordStatusUpdateReasonEnum.MANUAL_IMPORT.getCode());
                recordDO.setEffectDate(date);
                recordDO.setDataStatus(SalarySubmitRecordDataStatusEnum.EFFECT.getCode());
                if (CollectionUtils.isNotEmpty(existItemConfigSubmitRecordDOList)) {
                    HrmsSalaryItemConfigSubmitRecordDO itemConfigSubmitRecordDO = existItemConfigSubmitRecordDOList.get(0);
                    BaseDOUtil.fillDOUpdate(itemConfigSubmitRecordDO);
                    itemConfigSubmitRecordDO.setDataStatus(SalarySubmitRecordDataStatusEnum.INVALIDATED.getCode());
                    itemConfigSubmitRecordDO.setStatusUpdateReason(SalarySubmitRecordStatusUpdateReasonEnum.NEW_DATA_UPDATE.getCode());
                    updateItemRecordList.add(itemConfigSubmitRecordDO);
                }
                continue;
            }
            //存在计算中的单据，审批通过的不会生效
            recordDO.setStatusUpdateReason(SalarySubmitRecordStatusUpdateReasonEnum.APPROVED_BUT_LAST_DATA_LOCKED.getCode());
            if (StringUtils.equalsIgnoreCase(existSalaryCaculateDetailList.get(0).getStatus(), SalarySettlementUserItemStatusEnum.CLOSED_ACCOUNT.getCode())) {
                recordDO.setStatusUpdateReason(SalarySubmitRecordStatusUpdateReasonEnum.APPROVED_BUT_LAST_DATA_CLOSED.getCode());
            }
            recordDO.setDataStatus(SalarySubmitRecordDataStatusEnum.INEFFECTIVE.getCode());
        }
    }

    private void formUserDetailHandler(Integer isForm, List<HrmsSalarySettlementUserInfoDO> settlementUserInfoDOList, List<HrmsSalarySettlementApprovalItemInfoDO> settlementApprovalItemInfoDOList,
                                       List<HrmsSalarySettlementUserDetailDO> salarySettlementUserDetailDOList, List<Long> itemDetail, HrmsSalarySettlementAgentRecordDO agentRecordDO,
                                       Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> submitTemplateItemMap, List<SalarySettlementFormUserVO> formUserVOList) {
        //本次提报薪资数据的具体用户薪资
        List<Long> userIdList = settlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getUserId)
                .collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByIds(userIdList);
        Map<Long, List<HrmsUserInfoDO>> userMap = userInfoDOList.stream().collect(Collectors.groupingBy(HrmsUserInfoDO::getId));
        List<Long> deptIdList = userInfoDOList.stream()
                .filter(item -> item.getDeptId() != null)
                .map(HrmsUserInfoDO::getDeptId)
                .collect(Collectors.toList());
        List<HrmsEntDeptDO> entDeptDOList = hrmsDeptManage.selectDeptByIds(deptIdList);
        Map<Long, List<HrmsEntDeptDO>> deptMap = entDeptDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));

        //获取系统所有科目
        List<HrmsSalaryItemConfigDO> allItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(new SalaryItemConfigQuery());
        Map<Long, List<HrmsSalaryItemConfigDO>> itemMap = allItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        //获取计薪国币种
        SalaryPaymentCountryQuery salaryPaymentCountryQuery = SalaryPaymentCountryQuery.builder()
                .paymentCountry(agentRecordDO.getPaymentCountry())
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(salaryPaymentCountryQuery);
        if (CollectionUtils.isEmpty(paymentCountryConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.PAYMENT_COUNTRY_CONFIG_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PAYMENT_COUNTRY_CONFIG_RECORD_NOT_EXIST.getDesc()));
        }

        Map<Long, List<HrmsSalarySettlementUserDetailDO>> importMap = new HashMap<>();
        Object json = redissonClient.get(salarySettlementImportRedisKey + RequestInfoHolder.getUserCode());
        if (Objects.nonNull(json)) {
            redissonClient.delete(salarySettlementImportRedisKey + RequestInfoHolder.getUserCode());
            List<HrmsSalarySettlementUserDetailDO> importUserDetailDOList = JSONObject.parseArray((String) json, HrmsSalarySettlementUserDetailDO.class);
            importMap = importUserDetailDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementUserDetailDO::getSalarySettlementUserInfoId));
        }

        for (HrmsSalarySettlementUserInfoDO settlementUserInfoDO : settlementUserInfoDOList) {
            List<HrmsUserInfoDO> multipleExistUserList = userMap.get(settlementUserInfoDO.getUserId());
            if (CollectionUtils.isEmpty(multipleExistUserList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_USER_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_USER_NOT_EXIST.getDesc()));
            }
            List<HrmsEntDeptDO> multipleExistDeptList = deptMap.get(multipleExistUserList.get(0).getDeptId());
            SalarySettlementFormUserVO formUserVO = new SalarySettlementFormUserVO();
            formUserVO.setLocationCountry(multipleExistUserList.get(0).getLocationCountry());
            formUserVO.setLocationProvince(multipleExistUserList.get(0).getLocationProvince());
            formUserVO.setLocationCity(multipleExistUserList.get(0).getLocationCity());
            List<String> locationList = new ArrayList<>();
            if (StringUtils.isNotBlank(formUserVO.getLocationCountry())) {
                locationList.add(formUserVO.getLocationCountry());
            }
            if (StringUtils.isNotBlank(formUserVO.getLocationProvince())) {
                locationList.add(formUserVO.getLocationProvince());
            }
            if (StringUtils.isNotBlank(formUserVO.getLocationCity())) {
                locationList.add(formUserVO.getLocationCity());
            }
            if (CollectionUtils.isNotEmpty(locationList)) {
                formUserVO.setResidentLocation(StringUtils.join(locationList, "-"));
            }
            formUserVO.setUserId(multipleExistUserList.get(0).getId());
            formUserVO.setUserCode(multipleExistUserList.get(0).getUserCode());
            formUserVO.setUserName(multipleExistUserList.get(0).getUserName());
            formUserVO.setUserNameEn(multipleExistUserList.get(0).getUserNameEn());
            formUserVO.setOriginCountry(multipleExistUserList.get(0).getOriginCountry());
            formUserVO.setCountryName(multipleExistUserList.get(0).getCountryName());
            formUserVO.setPaymentCountry(settlementUserInfoDO.getPaymentCountry());
            formUserVO.setPaymentMonth(settlementUserInfoDO.getPaymentMonth());
            formUserVO.setSalarySchemeConfigNo(settlementUserInfoDO.getSalarySchemeConfigNo());
            if (CollectionUtils.isNotEmpty(multipleExistDeptList)) {
                formUserVO.setDeptName(RequestInfoHolder.isChinese() ? multipleExistDeptList.get(0).getDeptNameCn() : multipleExistDeptList.get(0).getDeptNameEn());
            }
            formUserVO.setSalarySettlementUserInfoId(settlementUserInfoDO.getId());
            formUserVO.setUserWorkStatus(settlementUserInfoDO.getUserWorkStatus());
            List<SalarySettlementFormUserDetailVO> formUserDetailVOList = new ArrayList<>();
            formUserVO.setFormUserDetailVOList(formUserDetailVOList);
            formUserVOList.add(formUserVO);
            //找出单据中改结薪人员填写的信息
            List<HrmsSalarySettlementApprovalItemInfoDO> existSettlementApprovalItemInfoDOList = settlementApprovalItemInfoDOList.stream()
                    .filter(item -> item.getSalarySettlementUserInfoId().equals(settlementUserInfoDO.getId()))
                    .collect(Collectors.toList());
            Map<Long, List<HrmsSalarySettlementApprovalItemInfoDO>> isFormUserDetailMap = existSettlementApprovalItemInfoDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementApprovalItemInfoDO::getItemId));
            List<HrmsSalarySettlementUserDetailDO> existSalarySettlementUserDetailDOList = salarySettlementUserDetailDOList.stream()
                    .filter(item -> item.getSalarySettlementUserInfoId().equals(settlementUserInfoDO.getId()))
                    .collect(Collectors.toList());
            Map<Long, List<HrmsSalarySettlementUserDetailDO>> notFormUserDetailMap = existSalarySettlementUserDetailDOList.stream().collect(Collectors.groupingBy(HrmsSalarySettlementUserDetailDO::getItemId));

            List<HrmsSalarySettlementUserDetailDO> importItemUserDetailDOList = importMap.get(settlementUserInfoDO.getId());

            for (Long itemId : itemDetail) {
                List<HrmsSalarySubmitTemplateItemConfigDO> existSubmitTemplateItemList = submitTemplateItemMap.get(itemId);
                if (CollectionUtils.isEmpty(existSubmitTemplateItemList)) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SUBMIT_TEMPLATE_ITEM_CONFIG_NOT_EXIST.getDesc()));
                }
                List<HrmsSalaryItemConfigDO> multipleExistItemList = itemMap.get(itemId);
                if (CollectionUtils.isEmpty(multipleExistItemList)) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_NOT_EXIST.getDesc()));
                }
                SalarySettlementFormUserDetailVO formUserDetailVO = new SalarySettlementFormUserDetailVO();
                formUserDetailVO.setItemId(multipleExistItemList.get(0).getId());
                formUserDetailVO.setItemNo(multipleExistItemList.get(0).getItemNo());
                formUserDetailVO.setItemName(RequestInfoHolder.isChinese() ? multipleExistItemList.get(0).getItemNameCn() + "(" + multipleExistItemList.get(0).getItemNo() + ")" : multipleExistItemList.get(0).getItemNameEn() + "(" + multipleExistItemList.get(0).getItemNo() + ")");
                formUserDetailVO.setItemAttribute(multipleExistItemList.get(0).getItemAttribute());
                SalaryItemAttributeEnum attributeEnum = SalaryItemAttributeEnum.getInstance(multipleExistItemList.get(0).getItemAttribute());
                if (attributeEnum != null) {
                    formUserDetailVO.setItemAttributeDesc(RequestInfoHolder.isChinese() ? attributeEnum.getDesc() : attributeEnum.getDescEn());
                }
                formUserDetailVO.setItemFormat(multipleExistItemList.get(0).getItemFormat());
                SalaryItemFormatEnum formatEnum = SalaryItemFormatEnum.getInstance(multipleExistItemList.get(0).getItemFormat());
                if (formatEnum != null) {
                    formUserDetailVO.setItemFormatDesc(RequestInfoHolder.isChinese() ? formatEnum.getDesc() : formatEnum.getDescEn());
                }
                formUserDetailVO.setItemValueType(multipleExistItemList.get(0).getItemValueType());
                SalaryItemValueTypeEnum salaryItemValueTypeEnum = SalaryItemValueTypeEnum.getInstance(multipleExistItemList.get(0).getItemValueType());
                if (salaryItemValueTypeEnum != null) {
                    formUserDetailVO.setItemValueTypeDesc(RequestInfoHolder.isChinese() ? salaryItemValueTypeEnum.getDesc() : salaryItemValueTypeEnum.getDescEn());
                }
                if (formatEnum != null && (StringUtils.equalsIgnoreCase(formatEnum.getCode(), SalaryItemFormatEnum.AMOUNT.getCode()) || StringUtils.equalsIgnoreCase(formatEnum.getCode(), SalaryItemFormatEnum.NUMBER.getCode()))) {
                    formUserDetailVO.setCurrency(paymentCountryConfigDOList.get(0).getCurrency());
                    formUserDetailVO.setPrecision(paymentCountryConfigDOList.get(0).getCurrencyPrecision());
                }
                formUserDetailVO.setIsAllowEdit(existSubmitTemplateItemList.get(0).getIsAllowEdit());
                formUserDetailVO.setIsAllowEditDesc(RequestInfoHolder.isChinese() ? "否" : "No");
                if (existSubmitTemplateItemList.get(0).getIsRequired().equals(BusinessConstant.Y)) {
                    formUserDetailVO.setIsAllowEditDesc(RequestInfoHolder.isChinese() ? "是" : "Yes");
                }
                formUserDetailVO.setIsRequired(existSubmitTemplateItemList.get(0).getIsRequired());
                formUserDetailVO.setIsRequiredDesc(RequestInfoHolder.isChinese() ? "否" : "No");
                if (existSubmitTemplateItemList.get(0).getIsRequired().equals(BusinessConstant.Y)) {
                    formUserDetailVO.setIsRequiredDesc(RequestInfoHolder.isChinese() ? "是" : "Yes");
                }
                formUserDetailVO.setMaxValue(existSubmitTemplateItemList.get(0).getMaxValue());
                formUserDetailVO.setMinValue(existSubmitTemplateItemList.get(0).getMinValue());
                formUserDetailVO.setMaxCharacter(existSubmitTemplateItemList.get(0).getMaxCharacter());
                formUserDetailVO.setSort(existSubmitTemplateItemList.get(0).getSort());

                formUserDetailVO.setItemValue("");
                if (isForm.equals(BusinessConstant.Y)) {
                    List<HrmsSalarySettlementApprovalItemInfoDO> existUserDetailList = isFormUserDetailMap.get(itemId);
                    if (CollectionUtils.isNotEmpty(existUserDetailList) && StringUtils.isNotBlank(existUserDetailList.get(0).getItemValue())) {
                        if (StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.NUMBER.getCode())
                                || StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.AMOUNT.getCode())) {
                            BigDecimal itemValueBigdecimal = new BigDecimal(existUserDetailList.get(0).getItemValue());
                            formUserDetailVO.setItemValue(itemValueBigdecimal.setScale(paymentCountryConfigDOList.get(0).getCurrencyPrecision(), RoundingMode.HALF_UP).toString());
                        }
                        if (StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM_DD.getCode())
                                || StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM.getCode())) {
                            formUserDetailVO.setItemValue(existUserDetailList.get(0).getItemValue());
                        }
                    }
                } else {
                    List<HrmsSalarySettlementUserDetailDO> existUserDetailList = notFormUserDetailMap.get(itemId);
                    if (CollectionUtils.isNotEmpty(existUserDetailList) && StringUtils.isNotBlank(existUserDetailList.get(0).getItemValue())) {
                        if (StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.NUMBER.getCode())
                                || StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.AMOUNT.getCode())) {
                            BigDecimal itemValueBigdecimal = new BigDecimal(existUserDetailList.get(0).getItemValue());
                            formUserDetailVO.setItemValue(itemValueBigdecimal.setScale(paymentCountryConfigDOList.get(0).getCurrencyPrecision(), RoundingMode.HALF_UP).toString());
                        }
                        if (StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM_DD.getCode())
                                || StringUtils.equalsIgnoreCase(multipleExistItemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM.getCode())) {
                            formUserDetailVO.setItemValue(existUserDetailList.get(0).getItemValue());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(importItemUserDetailDOList)) {
                    List<HrmsSalarySettlementUserDetailDO> collect = importItemUserDetailDOList.stream().filter(o -> o.getItemId().equals(itemId)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        formUserDetailVO.setItemValue(collect.get(0).getItemValue());
                    }
                }
                formUserDetailVOList.add(formUserDetailVO);
            }
        }
    }

    private void salarySettlementFormAddBpmDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO, SalarySettlementFormAddParam param,
                                                     HrmsUserInfoDO hrmsUserInfoDO, HrmsSalarySettlementAgentRecordDO agentRecordDO,
                                                     HrmsSalarySettlementApprovalFormDO addSalarySettlementApprovalFormDO) {
        initInfoApiDTO.setBizId(addSalarySettlementApprovalFormDO.getId().toString());
        initInfoApiDTO.setApprovalType(HrAttendanceApplicationFormTypeEnum.SALARY_SETTLEMENT.getCode());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(addSalarySettlementApprovalFormDO.getPaymentCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(addSalarySettlementApprovalFormDO.getApplyUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(addSalarySettlementApprovalFormDO.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(addSalarySettlementApprovalFormDO.getApplicationCode());

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //申请人姓名
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.APPLY_USER_NAME.getCode(), addSalarySettlementApprovalFormDO.getApplyUserName(), null);
        //申请人部门
        HrmsEntDeptDO hrmsEntDeptDO = hrmsDeptManage.selectById(hrmsUserInfoDO.getDeptId());
        if (hrmsEntDeptDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> applyDeptMap = new HashMap<>();
        applyDeptMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsEntDeptDO.getDeptNameCn());
        applyDeptMap.put(LanguageTypeEnum.en_US.getCode(), hrmsEntDeptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.APPLY_DEPT_NAME.getCode(), hrmsEntDeptDO.getDeptNameEn(), applyDeptMap);
        //申请人岗位
        HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(hrmsUserInfoDO.getPostId());
        if (hrmsEntPostDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.POST_NOT_EXITS.getDesc()));
        }
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.APPLY_POST_NAME.getCode(), hrmsEntPostDO.getPostNameEn(), null);

        //申请人常驻国(核算组织)
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.APPLY_ORIGIN_COUNTRY.getCode(), hrmsUserInfoDO.getOriginCountry(), null);

        //提报方式(常规月度提报&非窗口期提报)
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.SUBMIT_TYPE.getCode(), param.getSubmitType(), null);

        //代办单据名称
        if (agentRecordDO != null) {
            customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.AGENT_RECORD_NAME.getCode(), agentRecordDO.getName(), null);
            //数据统计周期
            customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.DATA_COLLECT_CYCLE.getCode(), DateUtil.format(agentRecordDO.getDataCollectStartDate(), "yyyy-MM-dd HH:mm:ss") + " - " + DateUtil.format(agentRecordDO.getDataCollectEndDate(), "yyyy-MM-dd HH:mm:ss"), null);
            //提报时间窗口
            customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.SUBMIT_CYCLE.getCode(), DateUtil.format(agentRecordDO.getSubmitStartDate(), "yyyy-MM-dd HH:mm:ss") + " - " + DateUtil.format(agentRecordDO.getSubmitEndDate(), "yyyy-MM-dd HH:mm:ss"), null);

            SalarySchemeConfigQuery schemeConfigQuery = new SalarySchemeConfigQuery();
            schemeConfigQuery.setSchemeNo(agentRecordDO.getSalarySchemeConfigNo());
            List<HrmsSalarySchemeConfigDO> salarySchemeConfigDOList = hrmsSalarySchemeConfigManage.selectSchemeConfigList(schemeConfigQuery).stream()
                    .sorted(Comparator.comparing(HrmsSalarySchemeConfigDO::getExpireTime).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(salarySchemeConfigDOList)) {
                //计薪方案编码
                customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.SCHEME_CONFIG_NO.getCode(), salarySchemeConfigDOList.get(0).getSchemeNo(), null);
                //计薪方案名称
                customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.SCHEME_CONFIG_NAME.getCode(), salarySchemeConfigDOList.get(0).getSchemeName(), null);
            }
        }

        //计薪国
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.PAYMENT_COUNTRY.getCode(), addSalarySettlementApprovalFormDO.getPaymentCountry(), null);

        //计薪月
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.PAYMENT_MONTH.getCode(), addSalarySettlementApprovalFormDO.getPaymentMonth(), null);

        //备注
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.REMARK.getCode(), addSalarySettlementApprovalFormDO.getRemark(), null);

        //总员工数
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.TOTAL_USER_NUMBER.getCode(), addSalarySettlementApprovalFormDO.getTotalUserNumber().toString(), null);

        //科目数
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.TOTAL_SALARY_ITEM_NUMBER.getCode(), addSalarySettlementApprovalFormDO.getTotalSalaryItemNumber().toString(), null);

        //数据条目数
        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.TOTAL_SUBMIT_NUMBER.getCode(), String.valueOf(addSalarySettlementApprovalFormDO.getTotalUserNumber() * addSalarySettlementApprovalFormDO.getTotalSalaryItemNumber()), null);

        //附件
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : param.getAttachmentList()) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }


        //本次提报薪资数据的具体用户
        List<Long> userIdList = param.getFormUserParamList().stream()
                .map(SalarySettlementFormUserParam::getUserId)
                .collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByIds(userIdList);
        Map<Long, List<HrmsUserInfoDO>> userMap = userInfoDOList.stream().collect(Collectors.groupingBy(HrmsUserInfoDO::getId));
        List<Long> deptIdList = userInfoDOList.stream()
                .filter(item -> item.getDeptId() != null)
                .map(HrmsUserInfoDO::getDeptId)
                .collect(Collectors.toList());
        List<HrmsEntDeptDO> entDeptDOList = hrmsDeptManage.selectDeptByIds(deptIdList);
        Map<Long, List<HrmsEntDeptDO>> deptMap = entDeptDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));

        //获取系统所有科目
        List<HrmsSalaryItemConfigDO> allItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(new SalaryItemConfigQuery());
        Map<Long, List<HrmsSalaryItemConfigDO>> itemMap = allItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        //获取报表配置中的科目信息
        List<HrmsSalarySubmitTemplateItemConfigDO> allTemplateItemConfigDOS = new ArrayList<>();
        if (agentRecordDO != null) {
            allTemplateItemConfigDOS = hrmsSalarySubmitTemplateItemConfigManage.listByQuery(HrmsSalarySubmitTemplateItemConfigQuery.builder().submitTemplateConfigId(agentRecordDO.getSalarySubmitTemplateConfigId()).build());
        }
        Map<Long, List<HrmsSalarySubmitTemplateItemConfigDO>> templateItemMap = allTemplateItemConfigDOS.stream().collect(Collectors.groupingBy(HrmsSalarySubmitTemplateItemConfigDO::getItemConfigId));

        SalaryPaymentCountryQuery salaryPaymentCountryQuery = SalaryPaymentCountryQuery.builder()
                .paymentCountry(addSalarySettlementApprovalFormDO.getPaymentCountry())
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(salaryPaymentCountryQuery);
        if (CollectionUtils.isEmpty(paymentCountryConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.PAYMENT_COUNTRY_CONFIG_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PAYMENT_COUNTRY_CONFIG_RECORD_NOT_EXIST.getDesc()));
        }

        List<String> multipleUserCodeList = new ArrayList<>();
        List<String> multipleUserNameList = new ArrayList<>();
        List<String> multipleOriginCountryList = new ArrayList<>();
        List<String> multipleResidentLocationList = new ArrayList<>();
        List<String> multipleCountryList = new ArrayList<>();
        List<String> multipleDeptList = new ArrayList<>();
        List<String> multipleItemList = new ArrayList<>();
        List<String> multipleAttributeList = new ArrayList<>();
        List<String> multipleValueList = new ArrayList<>();
        List<String> multipleIsRequiredList = new ArrayList<>();
        List<String> multipleFormatList = new ArrayList<>();
        List<String> multipleCurrencyList = new ArrayList<>();
        List<Map<String, Object>> userDetailDownloadList = new ArrayList<>();
        //多值  本次申请单具体提报信息展示
        for (SalarySettlementFormUserParam userParam : param.getFormUserParamList()) {
            List<HrmsUserInfoDO> multipleExistUserList = userMap.get(userParam.getUserId());
            if (CollectionUtils.isEmpty(multipleExistUserList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_USER_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_USER_NOT_EXIST.getDesc()));
            }
            List<HrmsEntDeptDO> multipleExistDeptList = deptMap.get(multipleExistUserList.get(0).getDeptId());
            for (SalarySettlementFormUserDetailParam userDetailParam : userParam.getFormUserDetailParamList()) {
                List<HrmsSalaryItemConfigDO> multipleExistItemList = itemMap.get(userDetailParam.getItemId());
                if (CollectionUtils.isEmpty(multipleExistItemList)) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_NOT_EXIST.getDesc()));
                }
                SalaryItemAttributeEnum attributeEnum = SalaryItemAttributeEnum.getInstance(multipleExistItemList.get(0).getItemAttribute());
                if (attributeEnum == null) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_ATTRIBUTE_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_ATTRIBUTE_NOT_EXIST.getDesc()));
                }
                SalaryItemFormatEnum formatEnum = SalaryItemFormatEnum.getInstance(multipleExistItemList.get(0).getItemFormat());
                if (formatEnum == null) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_FORMAT_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_FORMAT_NOT_EXIST.getDesc()));
                }
                List<HrmsSalarySubmitTemplateItemConfigDO> multipleExistTemplateItemList = templateItemMap.get(userDetailParam.getItemId());
                if (CollectionUtils.isEmpty(multipleExistTemplateItemList)) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_TEMPLATE_CONFIG_ITEM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_TEMPLATE_CONFIG_ITEM_NOT_EXIST.getDesc()));
                }
                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                map.put(RequestInfoHolder.isChinese() ? "账号" : "user code", multipleExistUserList.get(0).getUserCode());
                map.put(RequestInfoHolder.isChinese() ? "姓名" : "user name", multipleExistUserList.get(0).getUserName());
                map.put(RequestInfoHolder.isChinese() ? "国籍" : "country", multipleExistUserList.get(0).getCountryName());
                map.put(RequestInfoHolder.isChinese() ? "核算组织" : "Accounting Organization", multipleExistUserList.get(0).getOriginCountry());

                List<String> locationList = new ArrayList<>();
                if (StringUtils.isNotBlank(multipleExistUserList.get(0).getLocationCountry())) {
                    locationList.add(multipleExistUserList.get(0).getLocationCountry());
                }
                if (StringUtils.isNotBlank(multipleExistUserList.get(0).getLocationProvince())) {
                    locationList.add(multipleExistUserList.get(0).getLocationProvince());
                }
                if (StringUtils.isNotBlank(multipleExistUserList.get(0).getLocationCity())) {
                    locationList.add(multipleExistUserList.get(0).getLocationCity());
                }
                if (CollectionUtils.isNotEmpty(locationList)) {
                    map.put(RequestInfoHolder.isChinese() ? "常驻地" : "Resident Location", StringUtils.join(locationList, "-"));
                    multipleResidentLocationList.add(StringUtils.join(locationList, "-"));
                } else {
                    map.put(RequestInfoHolder.isChinese() ? "常驻地" : "Resident Location", "");
                    multipleResidentLocationList.add("");
                }

                multipleUserCodeList.add(multipleExistUserList.get(0).getUserCode());
                multipleUserNameList.add(multipleExistUserList.get(0).getUserName());
                multipleOriginCountryList.add(multipleExistUserList.get(0).getOriginCountry());
                multipleCountryList.add(multipleExistUserList.get(0).getCountryName());
                if (CollectionUtils.isNotEmpty(multipleExistDeptList)) {
                    multipleDeptList.add(RequestInfoHolder.isChinese() ? multipleExistDeptList.get(0).getDeptNameCn() : multipleExistDeptList.get(0).getDeptNameEn());
                    map.put(RequestInfoHolder.isChinese() ? "部门" : "dept", RequestInfoHolder.isChinese() ? multipleExistDeptList.get(0).getDeptNameCn() : multipleExistDeptList.get(0).getDeptNameEn());
                } else {
                    multipleDeptList.add("");
                    map.put(RequestInfoHolder.isChinese() ? "部门" : "dept", "");
                }
                map.put(RequestInfoHolder.isChinese() ? "科目" : "item", RequestInfoHolder.isChinese() ? multipleExistItemList.get(0).getItemNameCn() + "(" + multipleExistItemList.get(0).getItemNo() + ")" : multipleExistItemList.get(0).getItemNameEn() + "(" + multipleExistItemList.get(0).getItemNo() + ")");
                map.put(RequestInfoHolder.isChinese() ? "属性" : "attribute", RequestInfoHolder.isChinese() ? attributeEnum.getDesc() : attributeEnum.getDescEn());
                map.put(RequestInfoHolder.isChinese() ? "值" : "value", StringUtils.isBlank(userDetailParam.getItemValue()) ? "" : userDetailParam.getItemValue());

                multipleItemList.add(RequestInfoHolder.isChinese() ? multipleExistItemList.get(0).getItemNameCn() + "(" + multipleExistItemList.get(0).getItemNo() + ")" : multipleExistItemList.get(0).getItemNameEn() + "(" + multipleExistItemList.get(0).getItemNo() + ")");
                multipleAttributeList.add(RequestInfoHolder.isChinese() ? attributeEnum.getDesc() : attributeEnum.getDescEn());
                multipleValueList.add(StringUtils.isBlank(userDetailParam.getItemValue()) ? "" : userDetailParam.getItemValue());
                if (multipleExistTemplateItemList.get(0).getIsRequired().equals(BusinessConstant.Y)) {
                    multipleIsRequiredList.add(RequestInfoHolder.isChinese() ? "是" : "Yes");
                    map.put(RequestInfoHolder.isChinese() ? "是否必填" : "is required", RequestInfoHolder.isChinese() ? "是" : "Yes");
                } else {
                    multipleIsRequiredList.add(RequestInfoHolder.isChinese() ? "否" : "No");
                    map.put(RequestInfoHolder.isChinese() ? "是否必填" : "is required", RequestInfoHolder.isChinese() ? "否" : "No");
                }
                multipleFormatList.add(RequestInfoHolder.isChinese() ? formatEnum.getDesc() : formatEnum.getDescEn());
                map.put(RequestInfoHolder.isChinese() ? "格式" : "format", RequestInfoHolder.isChinese() ? formatEnum.getDesc() : formatEnum.getDescEn());
                if (StringUtils.equalsIgnoreCase(formatEnum.getCode(), SalaryItemFormatEnum.AMOUNT.getCode())) {
                    multipleCurrencyList.add(paymentCountryConfigDOList.get(0).getCurrency());
                    map.put(RequestInfoHolder.isChinese() ? "币种" : "currency", paymentCountryConfigDOList.get(0).getCurrency());
                } else {
                    multipleCurrencyList.add("");
                    map.put(RequestInfoHolder.isChinese() ? "币种" : "currency", "");
                }
                userDetailDownloadList.add(map);
            }
        }
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_USER_CODE.getCode(), JSON.toJSONString(multipleUserCodeList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_USER_NAME.getCode(), JSON.toJSONString(multipleUserNameList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_ORIGIN_COUNTRY.getCode(), JSON.toJSONString(multipleOriginCountryList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_RESIDENT_LOCATION.getCode(), JSON.toJSONString(multipleResidentLocationList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_COUNTRY.getCode(), JSON.toJSONString(multipleCountryList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_DEPT.getCode(), JSON.toJSONString(multipleDeptList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_ITEM.getCode(), JSON.toJSONString(multipleItemList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_ATTRIBUTE.getCode(), JSON.toJSONString(multipleAttributeList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_VALUE.getCode(), JSON.toJSONString(multipleValueList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_IS_REQUIRED.getCode(), JSON.toJSONString(multipleIsRequiredList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_FORMAT.getCode(), JSON.toJSONString(multipleFormatList), null);
//        customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.MULTIPLE_CURRENCY.getCode(), JSON.toJSONString(multipleCurrencyList), null);

        //附件 本次申请单具体提报信息下载(excel)  纵表
        String fileName = "Salary Settlement Form User Item Info" + System.currentTimeMillis() + ".xlsx";
        File file = ExcelUtils.getInstance().createExcelNoUpload(userDetailDownloadList, fileName, "Salary Settlement", false, null);
        String itemInfoUrl = ipepIntegration.upload("hermes/ent/image/", true, fileName, 1, file);
        if (StringUtils.isNotBlank(itemInfoUrl)) {
            List<FileTemplateApiDTO> apiDTOList = new ArrayList<>();
            FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
            apiDTO.setFileName("Salary Settlement Form.xlsx");
            apiDTO.setFileUrl(itemInfoUrl);
            apiDTO.setFileType("xlsx");
            apiDTOList.add(apiDTO);
            customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.FORM_USER_DETAIL_DOWNLOAD_VERTICAL.getCode(), JSON.toJSONString(apiDTOList), null);
        }

        //附件 本次申请单具体提报信息下载(excel)  横表
        List<Map<String, Object>> horizontalMapList = new ArrayList<>();
        for (SalarySettlementFormUserParam userParam : param.getFormUserParamList()) {
            List<HrmsUserInfoDO> multipleExistUserList = userMap.get(userParam.getUserId());
            if (CollectionUtils.isEmpty(multipleExistUserList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_USER_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_USER_NOT_EXIST.getDesc()));
            }
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            map.put(RequestInfoHolder.isChinese() ? "账号" : "Account", multipleExistUserList.get(0).getUserCode());
            map.put(RequestInfoHolder.isChinese() ? "姓名" : "Legal Name", multipleExistUserList.get(0).getUserName());
            map.put(RequestInfoHolder.isChinese() ? "核算组织" : "Accounting Organization", multipleExistUserList.get(0).getOriginCountry());

            List<String> locationList = new ArrayList<>();
            if (StringUtils.isNotBlank(multipleExistUserList.get(0).getLocationCountry())) {
                locationList.add(multipleExistUserList.get(0).getLocationCountry());
            }
            if (StringUtils.isNotBlank(multipleExistUserList.get(0).getLocationProvince())) {
                locationList.add(multipleExistUserList.get(0).getLocationProvince());
            }
            if (StringUtils.isNotBlank(multipleExistUserList.get(0).getLocationCity())) {
                locationList.add(multipleExistUserList.get(0).getLocationCity());
            }
            map.put(RequestInfoHolder.isChinese() ? "常驻地" : "Resident Location", "");
            if (CollectionUtils.isNotEmpty(locationList)) {
                map.put(RequestInfoHolder.isChinese() ? "常驻地" : "Resident Location", StringUtils.join(locationList, "-"));
            }

            map.put(RequestInfoHolder.isChinese() ? "计薪国" : "Payroll Country", addSalarySettlementApprovalFormDO.getPaymentCountry());
            map.put(RequestInfoHolder.isChinese() ? "部门" : "Department", "");
            List<HrmsEntDeptDO> multipleExistDeptList = deptMap.get(multipleExistUserList.get(0).getDeptId());
            if (CollectionUtils.isNotEmpty(multipleExistDeptList)) {
                map.put(RequestInfoHolder.isChinese() ? "部门" : "Department", RequestInfoHolder.isChinese() ? multipleExistDeptList.get(0).getDeptNameCn() : multipleExistDeptList.get(0).getDeptNameEn());
            }
            for (SalarySettlementFormUserDetailParam userDetailParam : userParam.getFormUserDetailParamList()) {
                List<HrmsSalaryItemConfigDO> multipleExistItemList = itemMap.get(userDetailParam.getItemId());
                if (CollectionUtils.isEmpty(multipleExistItemList)) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_MULTIPLE_ITEM_NOT_EXIST.getDesc()));
                }
                map.put(RequestInfoHolder.isChinese() ? multipleExistItemList.get(0).getItemNameCn() + "(" + multipleExistItemList.get(0).getItemNo() + ")" : multipleExistItemList.get(0).getItemNameEn() + "(" + multipleExistItemList.get(0).getItemNo() + ")", userDetailParam.getItemValue());
            }
            horizontalMapList.add(map);
        }
        String horizontalFileName = "Salary Settlement Form User Item Info" + System.currentTimeMillis() + ".xlsx";
        File horizontalFile = ExcelUtils.getInstance().createExcelNoUpload(horizontalMapList, horizontalFileName, "Salary Settlement", false, null);
        String horizontalItemInfoUrl = ipepIntegration.upload("hermes/ent/image/", true, horizontalFileName, 1, horizontalFile);
        if (StringUtils.isNotBlank(horizontalItemInfoUrl)) {
            List<FileTemplateApiDTO> apiDTOList = new ArrayList<>();
            FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
            apiDTO.setFileName("Salary Settlement Form.xlsx");
            apiDTO.setFileUrl(horizontalItemInfoUrl);
            apiDTO.setFileType("xlsx");
            apiDTOList.add(apiDTO);
            customFieldBuild(fieldApiDTOList, SalarySettlementCustomFieldEnum.FORM_USER_DETAIL_DOWNLOAD_HORIZONTAL.getCode(), JSON.toJSONString(apiDTOList), null);
        }


        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);
        log.info("salarySettlementFormAddBpmDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));
    }

    private void salarySettlementFormAddDataBuild(SalarySettlementFormAddParam param, HrmsUserInfoDO hrmsUserInfoDO,
                                                  HrmsSalarySettlementAgentRecordDO agentRecordDO,
                                                  HrmsSalarySettlementApprovalFormDO addSalarySettlementApprovalFormDO,
                                                  List<HrmsSalarySettlementApprovalItemInfoDO> addSalarySettlementApprovalItemInfoDOList,
                                                  List<HrmsSalarySettlementApprovalUserInfoDO> addSalarySettlementApprovalUserInfoDOList) {
        //可能为暂存，那么是会可能存在代办单据没有的情况，即没有计薪国
        SalaryPaymentCountryQuery salaryPaymentCountryQuery = SalaryPaymentCountryQuery.builder()
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<HrmsSalaryPaymentCountryConfigDO> paymentCountryConfigDOList = hrmsSalaryPaymentCountryConfigManage.selectPaymentCountryConfigList(salaryPaymentCountryQuery);
        Map<String, List<HrmsSalaryPaymentCountryConfigDO>> paymentCountryMap = paymentCountryConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryPaymentCountryConfigDO::getPaymentCountry));
        //获取系统所有科目
        List<HrmsSalaryItemConfigDO> allItemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigList(new SalaryItemConfigQuery());
        Map<Long, List<HrmsSalaryItemConfigDO>> itemMap = allItemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        //默认为暂存
        addSalarySettlementApprovalFormDO.setFormStatus(SalarySettlementFormStatusEnum.STAGING.getCode());
        if (param.getOperationType() == 1) {
            //保存入口
            addSalarySettlementApprovalFormDO.setFormStatus(SalarySettlementFormStatusEnum.IN_REVIEW.getCode());
        }
        if (param.getApplicationFormId() == null) {
            //ADD入口
            addSalarySettlementApprovalFormDO.setId(IdWorkerUtil.getId());
            addSalarySettlementApprovalFormDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.SALARY_SETTLEMENT));
            BaseDOUtil.fillDOInsert(addSalarySettlementApprovalFormDO);
        } else {
            //UPDATE入口
            BaseDOUtil.fillDOUpdate(addSalarySettlementApprovalFormDO);
        }
        addSalarySettlementApprovalFormDO.setApplyUserId(hrmsUserInfoDO.getId());
        addSalarySettlementApprovalFormDO.setApplyUserCode(hrmsUserInfoDO.getUserCode());
        addSalarySettlementApprovalFormDO.setApplyUserName(hrmsUserInfoDO.getUserName());
        addSalarySettlementApprovalFormDO.setSubmitType(param.getSubmitType());
        addSalarySettlementApprovalFormDO.setSalarySettlementAgentRecordId(param.getSalarySettlementAgentRecordId());
        if (agentRecordDO != null) {
            addSalarySettlementApprovalFormDO.setPaymentCountry(agentRecordDO.getPaymentCountry());
            addSalarySettlementApprovalFormDO.setPaymentMonth(agentRecordDO.getPaymentMonth());
        }
        addSalarySettlementApprovalFormDO.setTotalUserNumber(BusinessConstant.N);
        addSalarySettlementApprovalFormDO.setTotalSalaryItemNumber(BusinessConstant.N);
        if (CollectionUtils.isNotEmpty(param.getFormUserParamList())) {
            List<Long> salarySettlementUserInfoIdList = param.getFormUserParamList().stream()
                    .map(SalarySettlementFormUserParam::getSalarySettlementUserInfoId)
                    .collect(Collectors.toList());
            addSalarySettlementApprovalFormDO.setTotalUserNumber(salarySettlementUserInfoIdList.size());
            addSalarySettlementApprovalFormDO.setTotalSalaryItemNumber(param.getFormUserParamList().get(0).getFormUserDetailParamList().size());
            List<Long> itemIdList = param.getFormUserParamList().get(0).getFormUserDetailParamList().stream()
                    .map(SalarySettlementFormUserDetailParam::getItemId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemIdList)) {
                addSalarySettlementApprovalFormDO.setItemDetail(JSON.toJSONString(itemIdList));
            }
        }
        addSalarySettlementApprovalFormDO.setRemark(param.getRemark());
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            addSalarySettlementApprovalFormDO.setAttachment(JSON.toJSONString(param.getAttachmentList()));
        }
        List<HrmsSalaryPaymentCountryConfigDO> existPaymentCountryList = paymentCountryMap.get(addSalarySettlementApprovalFormDO.getPaymentCountry());
        for (SalarySettlementFormUserParam userParam : param.getFormUserParamList()) {
            HrmsSalarySettlementApprovalUserInfoDO approvalUserInfoDO = new HrmsSalarySettlementApprovalUserInfoDO();
            approvalUserInfoDO.setId(IdWorkerUtil.getId());
            approvalUserInfoDO.setSalarySettlementApprovalFormId(addSalarySettlementApprovalFormDO.getId());
            approvalUserInfoDO.setSalarySettlementUserInfoId(userParam.getSalarySettlementUserInfoId());
            BaseDOUtil.fillDOInsert(approvalUserInfoDO);
            addSalarySettlementApprovalUserInfoDOList.add(approvalUserInfoDO);
            for (SalarySettlementFormUserDetailParam userDetailParam : userParam.getFormUserDetailParamList()) {
                HrmsSalarySettlementApprovalItemInfoDO approvalItemInfoDO = new HrmsSalarySettlementApprovalItemInfoDO();
                approvalItemInfoDO.setId(IdWorkerUtil.getId());
                approvalItemInfoDO.setSalarySettlementApprovalFormId(addSalarySettlementApprovalFormDO.getId());
                approvalItemInfoDO.setSalarySettlementUserInfoId(approvalUserInfoDO.getSalarySettlementUserInfoId());
                approvalItemInfoDO.setItemId(userDetailParam.getItemId());
                //金额或者数值类型不为空时，需要使用计薪国精度
                approvalItemInfoDO.setItemValue(userDetailParam.getItemValue());
                List<HrmsSalaryItemConfigDO> existItemList = itemMap.get(userDetailParam.getItemId());
                if (CollectionUtils.isNotEmpty(existPaymentCountryList) && CollectionUtils.isNotEmpty(existItemList) && StringUtils.isNotBlank(userDetailParam.getItemValue())
                        && (StringUtils.equalsIgnoreCase(existItemList.get(0).getItemFormat(), SalaryItemFormatEnum.NUMBER.getCode())
                        || StringUtils.equalsIgnoreCase(existItemList.get(0).getItemFormat(), SalaryItemFormatEnum.AMOUNT.getCode()))) {
                    BigDecimal itemValueBigdecimal = new BigDecimal(userDetailParam.getItemValue());
                    approvalItemInfoDO.setItemValue(itemValueBigdecimal.setScale(existPaymentCountryList.get(0).getCurrencyPrecision(), RoundingMode.HALF_UP).toString());
                    //入参这里也赋值下，后续传bpm的参数是从入参取的，不是从approvalItemInfoDO取
                    userDetailParam.setItemValue(approvalItemInfoDO.getItemValue());
                }
                if (CollectionUtils.isNotEmpty(existItemList) && StringUtils.isNotBlank(userDetailParam.getItemValue()) && StringUtils.equalsIgnoreCase(existItemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM_DD.getCode())) {
                    userDetailParam.setItemValue(DateUtil.format(DateUtil.parse(userDetailParam.getItemValue(), "yyyyMMdd"), "yyyy-MM-dd"));
                }
                if (CollectionUtils.isNotEmpty(existItemList) && StringUtils.isNotBlank(userDetailParam.getItemValue()) && StringUtils.equalsIgnoreCase(existItemList.get(0).getItemFormat(), SalaryItemFormatEnum.DATE_YYYY_MM.getCode())) {
                    userDetailParam.setItemValue(DateUtil.format(DateUtil.parse(userDetailParam.getItemValue(), "yyyyMM"), "yyyy-MM"));
                }
                approvalItemInfoDO.setItemNo(userDetailParam.getItemNo());
                BaseDOUtil.fillDOInsert(approvalItemInfoDO);
                addSalarySettlementApprovalItemInfoDOList.add(approvalItemInfoDO);
            }
        }
    }

    private void salarySettlementFormAddDataCheck(SalarySettlementFormAddParam param, HrmsSalarySettlementAgentRecordDO agentRecordDO) {
        if (StringUtils.isBlank(param.getSubmitType())) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_SUBMIT_TYPE_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_SUBMIT_TYPE_NOT_EMPTY.getDesc()));
        }
        //常规月度提报
        if (param.getSalarySettlementAgentRecordId() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NORMAL_MONTH_REPORT_AGENT_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NORMAL_MONTH_REPORT_AGENT_NOT_EMPTY.getDesc()));
        }
        if (agentRecordDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NORMAL_MONTH_REPORT_AGENT_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_NORMAL_MONTH_REPORT_AGENT_NOT_EXIST.getDesc()));
        }
        String paymentCountry = agentRecordDO.getPaymentCountry();
        String paymentMonth = agentRecordDO.getPaymentMonth();
        String salarySchemeConfigNo = agentRecordDO.getSalarySchemeConfigNo();

        //校验用户明细  可能暂存很久了，在提交，结薪人员都变了
        //科目不用管，因为报表配置每次变动，都会生成新的版本，所有一旦新增配置，科目就固定死了 暂存后不管多久进来，报表配置不会变
        HrmsSalarySettlementUserInfoQuery settlementUserInfoQuery = new HrmsSalarySettlementUserInfoQuery();
        settlementUserInfoQuery.setPaymentCountry(paymentCountry);
        settlementUserInfoQuery.setPaymentMonth(paymentMonth);
        settlementUserInfoQuery.setSalarySchemeConfigNo(salarySchemeConfigNo);
        settlementUserInfoQuery.setIsLatest(BusinessConstant.Y);
        List<HrmsSalarySettlementUserInfoDO> existSettlementUserInfoDOList = hrmsSalarySettlementUserInfoManage.listByQuery(settlementUserInfoQuery);
        List<Long> existSalarySettlementUserInfoIdList = existSettlementUserInfoDOList.stream()
                .map(HrmsSalarySettlementUserInfoDO::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(param.getFormUserParamList())) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_USER_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_USER_NOT_EMPTY.getDesc()));
        }
        if (CollectionUtils.isEmpty(param.getFormUserParamList().get(0).getFormUserDetailParamList())) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_USER_ITEM_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_FORM_USER_ITEM_NOT_EMPTY.getDesc()));
        }

        for (SalarySettlementFormUserParam userParam : param.getFormUserParamList()) {
            if (!existSalarySettlementUserInfoIdList.contains(userParam.getSalarySettlementUserInfoId())) {
                throw BusinessException.get(HrmsErrorCodeEnums.SALARY_SETTLEMENT_USER_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_SETTLEMENT_USER_RECORD_NOT_EXIST.getDesc()));
            }
        }
    }

    private void customFieldBuild(List<ApprovalTypeFieldApiDTO> fieldApiDTOList, String fieldType, String fieldValue, Map<String, String> fieldValueMap) {
        ApprovalTypeFieldApiDTO fieldApiDTO = new ApprovalTypeFieldApiDTO();
        fieldApiDTO.setFieldType(fieldType);
        fieldApiDTO.setFieldValue(fieldValue);
        fieldApiDTO.setFieldValueMap(fieldValueMap);
        fieldApiDTOList.add(fieldApiDTO);
    }

    private void titleExportBuild(String key, String value, List<SalarySettlementUserDataTitleExportVO> resultList) {
        SalarySettlementUserDataTitleExportVO titleExportVO = new SalarySettlementUserDataTitleExportVO();
        titleExportVO.setKey(key);
        titleExportVO.setValue(value);
        resultList.add(titleExportVO);
    }
}
