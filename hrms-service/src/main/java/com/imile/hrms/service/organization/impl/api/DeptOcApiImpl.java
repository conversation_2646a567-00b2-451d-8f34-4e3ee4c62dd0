package com.imile.hrms.service.organization.impl.api;

import com.imile.hrms.api.organization.api.DeptOcApi;
import com.imile.hrms.api.organization.dto.WarehouseOcDTO;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.imile.rpc.common.RpcResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/5
 */
@Service(version = "1.0.0")
public class DeptOcApiImpl implements DeptOcApi {

    @Resource
    private EntDeptNewService entDeptNewService;


    @Override
    public RpcResult<List<WarehouseOcDTO>> getWarehouseOcList(List<String> ocCodeList) {
        if (CollectionUtils.isEmpty(ocCodeList)) {
            return RpcResult.ok(Collections.emptyList());
        }
        return RpcResult.ok(entDeptNewService.getWarehouseOcList(ocCodeList));
    }
}
