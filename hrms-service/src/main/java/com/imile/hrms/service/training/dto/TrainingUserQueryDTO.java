package com.imile.hrms.service.training.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrainingUserQueryDTO implements Serializable {
    private static final long serialVersionUID = -6655241991920866887L;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 培训类型
     */
    private String trainingType;

    /**
     * 次数
     */
    private Integer frequency;

    /**
     * 是否通过 1：是 0：否
     */
    private Integer isPass;

    /**
     * 账号或姓名
     */
    private String codeOrName;

    /**
     * 日期
     */
    private Date date;
}
