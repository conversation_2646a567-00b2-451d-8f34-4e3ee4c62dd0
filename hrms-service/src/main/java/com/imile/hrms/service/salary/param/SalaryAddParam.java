package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.common.dto.AttachmentDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-8-26
 * @version: 1.0
 */
@Data
public class SalaryAddParam {
    /**
     * 申请单据ID(新增无需传)
     */
    private Long applicationFormId;

    /**
     * 申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long applyUserId;

    /**
     * 结算时间-年月(202308)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long settlementDate;

    /**
     * 记薪周期（20230825-20230924）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String salaryConfigCycle;

    /**
     * 算薪国家（申请国）
     */
    private String applyCountry;

    /**
     * 发薪类型
     */
    private String salaryType;

    /**
     * 费用承担组织
     */
    private Long costOrgId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 币种
     */
    private String currency;

    /**
     * 银行名称-开户银行
     */
    private String bankName;

    /**
     * 银行卡账户-银行账户
     */
    private String bankAccount;

    /**
     * 银行账户名称- 账号名称
     */
    private String accountName;

    /**
     * swift_code
     */
    private String swiftCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发票号
     */
    private String invoiceNumber;

    /**
     * 支付类型，字典ErsPaymentMethod
     */
    private String payType;

    /**
     * 期望支付日期
     */
    private Date overdueDate;

    /**
     * 本次申请用户ID
     */
    private List<Long> employeeUserIdList;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;


    //后端需要字段，前端无需传
    private String applyUserCode;

    private String applyUserName;

    private Long applyUserDeptId;

    private Long applyUserPostId;

    private Long applyUserCostOrgId;

    private String totalSalaryAmount;

    private String applyUserCountry;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 计税方式
     */
    private String taxationMethod;

    /**
     * 税率类型
     */
    private String taxRateType;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 税额
     */
    private String taxAmount;
}
