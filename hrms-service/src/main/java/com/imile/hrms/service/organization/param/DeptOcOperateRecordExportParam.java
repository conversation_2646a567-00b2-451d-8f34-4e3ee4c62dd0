package com.imile.hrms.service.organization.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeptOcOperateRecordExportParam extends ResourceQuery {

    /**
     * 运营组织名称
     */
    private String ocName;

    /**
     * 运营类型
     */
    private String ocType;

    /**
     * 处理类型
     */
    private String operationType;

    /**
     * 记录状态
     */
    private String recordStatus;

    /**
     * 部门地理国
     */
    private List<String> countryList;
}
