package com.imile.hrms.service.punch.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceCycleConfigDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.punch.model.HrmsAttendanceUserCardConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.punch.HrmsAttendanceUserCardConfigManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.approval.dto.AttendanceDayCycleDTO;
import com.imile.hrms.service.attendance.HrmsAttendanceBaseService;
import com.imile.hrms.service.punch.HrmsAttendanceUserCardConfigService;
import com.imile.hrms.service.punch.param.UserCardParam;
import com.imile.hrms.service.punch.param.WarehouseUserCardParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceUserCardConfigServiceImpl
 * {@code @since:} 2024-11-29 14:13
 * {@code @description:}
 */
@Service
@Slf4j
public class HrmsAttendanceUserCardConfigServiceImpl implements HrmsAttendanceUserCardConfigService {

    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsAttendanceUserCardConfigManage hrmsAttendanceUserCardConfigManage;
    @Autowired
    private HrmsAttendanceBaseService hrmsAttendanceBaseService;
    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    /**
     * 绑定用户补卡次数
     *
     * @param param 入参
     */
    @Override
    public void bindUserCardConfig(WarehouseUserCardParam param) {

        if (ObjectUtil.isNull(param) || CollUtil.isEmpty(param.getAllUserCodeList())) {
            log.info("bindUserCardConfig param is null or allUserCodeList is empty");
            return;
        }

        log.info("bindUserCardConfig param:{}", param);
        List<String> allUserCodeList = param.getAllUserCodeList();


        // 获取用户
        List<HrmsUserInfoDO> allUserInfoList = hrmsUserInfoManage.selectUserInfoByCodes(allUserCodeList);

        allUserInfoList = allUserInfoList.stream()
                .filter(item -> ObjectUtil.isNotEmpty(item.getUserCode())
                        && ObjectUtil.equal(item.getStatus(), StatusEnum.ACTIVE.getCode())
                        && ObjectUtil.equal(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())).collect(Collectors.toList());

        List<Long> userIdList = allUserInfoList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
        //查询员工所有的记录
        List<HrmsAttendanceUserCardConfigDO> userCardConfigList = hrmsAttendanceUserCardConfigManage.selectByUserIdList(userIdList).stream()
                .sorted(Comparator.comparing(HrmsAttendanceUserCardConfigDO::getCycleStartDate)).collect(Collectors.toList());
        Map<Long, List<HrmsAttendanceUserCardConfigDO>> userCardConfigMap = userCardConfigList.stream().collect(Collectors.groupingBy(HrmsAttendanceUserCardConfigDO::getUserId));

        List<HrmsAttendanceUserCardConfigDO> addUserCardConfigList = new ArrayList<>();

        List<UserCardParam> userCardParamList = param.getUserCardParamList();
        if (CollUtil.isEmpty(userCardParamList)) {
            log.info("bindUserCardConfig userCardParamList is empty");
            return;
        }
        for (UserCardParam userCardParam : userCardParamList) {
            // 用户列表
            List<String> userCodeList = userCardParam.getUserCodeList();
            // 这批用户异常所属日期
            Long initDayId = userCardParam.getInitDayId();

            List<HrmsUserInfoDO> userInfoList = allUserInfoList.stream().filter(item -> userCodeList.contains(item.getUserCode())).collect(Collectors.toList());

            for (HrmsUserInfoDO userInfo : userInfoList) {
                Long userId = userInfo.getId();

                HrmsAttendanceCycleConfigDO userAttendanceCycleConfig = hrmsAttendanceBaseService.getUserAttendanceCycleConfigUserCard(userId);
                AttendanceDayCycleDTO attendanceDayCycle = hrmsAttendanceBaseService.getUserAttendanceCycleConfigDay(initDayId, userAttendanceCycleConfig);

                log.info("userCode:{} userAttendanceCycleConfig:{} attendanceDayCycle:{}", userInfo.getUserCode(), userAttendanceCycleConfig, attendanceDayCycle);

                if (ObjectUtil.isNull(attendanceDayCycle)) {
                    log.info("userCode:{} has no cycle config", userInfo.getUserCode());
                    continue;
                }

                List<HrmsAttendanceUserCardConfigDO> userCardList = userCardConfigMap.get(userId);
                if (CollUtil.isEmpty(userCardList)) {
                    addCardConfigBuild(userId, attendanceDayCycle, addUserCardConfigList);
                    continue;
                }
                List<HrmsAttendanceUserCardConfigDO> existList = userCardList.stream().filter(item -> DateUtil.format(item.getCycleStartDate(), "yyyyMMdd").equals(DateUtil.format(attendanceDayCycle.getAttendanceStartDate(), "yyyyMMdd"))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(existList)) {
                    log.info("userCode:{} has exist user card config", userInfo.getUserCode());
                    continue;
                }
                addCardConfigBuild(userId, attendanceDayCycle, addUserCardConfigList);
            }
        }
        hrmsAttendanceUserCardConfigManage.batchSave(addUserCardConfigList);

    }

    /**
     * 构建补卡记录
     *
     * @param userId                  用户id
     * @param attendanceDayCycleDTO   考勤周期
     * @param addUserCardConfigDOList 目标集合
     */
    private void addCardConfigBuild(Long userId, AttendanceDayCycleDTO attendanceDayCycleDTO, List<HrmsAttendanceUserCardConfigDO> addUserCardConfigDOList) {
        HrmsAttendanceUserCardConfigDO userCardConfig = new HrmsAttendanceUserCardConfigDO();
        userCardConfig.setId(iHrmsIdWorker.nextId());
        userCardConfig.setUserId(userId);
        userCardConfig.setUsedCardCount(BusinessConstant.ZERO);
        userCardConfig.setCycleStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
        userCardConfig.setCycleEndDate(DateUtil.offset(attendanceDayCycleDTO.getAttendanceEndDate(), DateField.MILLISECOND, -999));
        BaseDOUtil.fillDOInsert(userCardConfig);
        addUserCardConfigDOList.add(userCardConfig);
    }
}
