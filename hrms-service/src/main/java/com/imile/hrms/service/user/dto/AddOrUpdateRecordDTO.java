package com.imile.hrms.service.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddOrUpdateRecordDTO implements Serializable {
    private static final long serialVersionUID = 8613575315090999777L;

    private Long id;
    /**
     * 企业id
     */
    private Long orgId;

    /**
     * 国家
     */
    private String country;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 结算主体编码
     */
    private String settlementCenterCode;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 所属网点id
     */
    private Long ocId;

    /**
     * 业务节点id
     */
    private Long bizModelId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 工作岗位
     */
    private Long postId;

    /**
     * 职级id
     */
    private Long gradeId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 生效时间
     */
    private Date effectDate;

    /**
     * 失效时间
     */
    private Date expireDate;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 备注
     */
    private String remark;
}
