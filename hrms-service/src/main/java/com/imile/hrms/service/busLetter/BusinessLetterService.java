package com.imile.hrms.service.busLetter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.busletter.model.BusinessLetterDO;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.busLetter.param.BusinessLetterAddParam;
import com.imile.hrms.service.busLetter.param.BusinessLetterListParam;
import com.imile.hrms.service.busLetter.vo.BusinessLetterDetailVO;
import com.imile.hrms.service.busLetter.vo.BusinessLetterPageVO;

import java.util.List;

/**
 * <p>
 * 人事变动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-28
 */
public interface BusinessLetterService extends IService<BusinessLetterDO> {

    /**
     * 人事变动申请列表
     * @param query
     * @return
     */
    PaginationResult<BusinessLetterPageVO> pageList(BusinessLetterListParam query);

    /**
     * 人事变动申请详情
     * @param id
     * @return
     */
    BusinessLetterDetailVO detail(Long id);

    /**
     * 人事变动预览
     * @param param
     * @return
     */
    List<ApprovalDetailStepRecordDTO> preview(BusinessLetterAddParam param);

    /**
     * 人事变动申请新增/修改
     * @param param
     */
    Boolean add(BusinessLetterAddParam param);

    /**
     * 人事变动申请删除
     * @param id
     */
    Boolean delete(Long id);

    /**
     * 人事变动申请取消
     * @param id
     */
    Boolean cancel(Long id);
}
