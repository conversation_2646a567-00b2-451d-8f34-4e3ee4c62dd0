package com.imile.hrms.service.promotion.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Data
public class PromotionAnnualSaveParam {

    /**
     * 批次ID（重新提交时必传）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long batchId;

    /**
     * 一级部门ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long firstDeptId;

    /**
     * 一级部门总人数
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer firstDeptUserCount;

    /**
     * 提报比例
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String applyRatio;

    /**
     * 候选人列表
     */
    @Valid
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<ApplyCandidate> candidateList;

    @Data
    public static class ApplyCandidate {

        /**
         * 人员ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long userId;

        /**
         * 是否答辩
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Integer isDefense;

        /**
         * 评委ID(多个英文逗号分隔)
         */
        private String judgeUserIds;

        /**
         * 评委意见
         */
        private String judgeOpinions;

        /**
         * 晋升岗位ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long promotionPostId;

        /**
         * 晋升职级序列ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long promotionGradeId;

        /**
         * 晋升职级序列
         */
        @NotBlank(message = ValidCodeConstant.NOT_BLANK)
        private String promotionJobSequence;

        /**
         * 晋升职级
         */
        @NotBlank(message = ValidCodeConstant.NOT_BLANK)
        private String promotionJobLevel;

        /**
         * 晋升职等
         */
        @NotBlank(message = ValidCodeConstant.NOT_BLANK)
        private String promotionJobGrade;

        /**
         * 晋升原因
         */
        private String promotionReason;

        /**
         * 晋升附件（多个附件）
         */
        @Size(max = 1)
        private List<AttachmentDTO> promotionFileList;
    }
}



