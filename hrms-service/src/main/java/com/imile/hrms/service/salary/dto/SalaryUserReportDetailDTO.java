package com.imile.hrms.service.salary.dto;

import lombok.Data;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/11 10:40
 * @version: 1.0
 */
@Data
public class SalaryUserReportDetailDTO {

    /**
     * 薪资报表配置编码(即USER_CODE等，或者科目ID)
     */
    private String key;

    /**
     * 薪资报表配置值(员工薪资明细表存的值，比如部门这里就是ID,支付方式存的是编码)
     */
    private String actualValue;

    /**
     * 薪资报表配置值(页面真正展示的值,比如IT部门，KSA国家，电汇等)
     */
    private String formatValue;

    /**
     * 薪资项属性(科目独有的)
     */
    private String itemAttributeDesc;

    /**
     * 薪资项属性(科目独有的)
     */
    private String itemAttribute;

    /**
     * 薪资项格式
     */
    private String itemFormat;
}
