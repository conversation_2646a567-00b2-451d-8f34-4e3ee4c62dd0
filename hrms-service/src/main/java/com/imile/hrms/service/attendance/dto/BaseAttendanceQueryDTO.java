package com.imile.hrms.service.attendance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class BaseAttendanceQueryDTO extends ResourceQuery {
    private static final long serialVersionUID = -5475880997124362664L;
    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 用户账号
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 员工姓名或邮箱
     */
    private String userNameOrEmail;

    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位id(多选)
     */
    private List<Long> postIds;
    /**
     * 部门ids
     */
    private List<Long> deptIds;

    private String country;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 考勤日历编码(多选)
     */
    private List<String> attendanceConfigNoList;

    /**
     * 配置打卡No
     */
    private String punchConfigNo;

    /**
     * 配置打卡No(多选)
     */
    private List<String> punchConfigNoList;

    /**
     * 常住地城市(多选)
     */
    private List<String> locationCityList;
}
