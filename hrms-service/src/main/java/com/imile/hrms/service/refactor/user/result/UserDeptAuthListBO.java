package com.imile.hrms.service.refactor.user.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Data
public class UserDeptAuthListBO {

    /**
     * userId
     */
    private Long userId;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 姓名全称
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    private String employeeTypeDesc;

    /**
     * 部门Id
     */
    @RelationObjectValue(beanId = "hrmsDeptNewManageImpl", fieldId = "deptName", extendFieldIdList = "deptNamePath",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门链
     */
    private String deptNamePath;

    /**
     * 岗位id
     */
    @RelationObjectValue(beanId = "postManageImpl", fieldId = "postName", fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dimissionDate;

    /**
     * 账号状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
    private String status;
    private String statusDesc;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

}
