package com.imile.hrms.service.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserInfoResultDTO implements Serializable {
    private static final long serialVersionUID = 8997605653951873374L;

    private Long id;

    private String userCode;

    private String userName;

    /**
     * 性别 性别(1:男,2:女)
     */
    private Integer sex;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 手机号码
     */
    private String phone;
    /**
     * 区号
     */
    private Long  countryCallingId;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位名称
     */
    private String postName;

    private String status;

    /**
     * 证件信息
     */
    private List<UserCertificateResultDTO> certificateList;

}
