package com.imile.hrms.service.user.dto.transform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 调动后的数据
 *
 * <AUTHOR>
 * @since 2024/9/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserTransformAfterDTO {

    /**
     * 工作岗位
     */
    private Long postId;

    /**
     * 岗位名称
     */
    private String postNameCn;

    /**
     * 岗位名称
     */
    private String postNameEn;

    /**
     * 岗职体系id
     */
    private Long gradeId;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 职级序列
     */
    private String jobSequence;

    /**
     * 是否为司机
     */
    private Integer isDriver;

    /**
     * 是否仓内人员
     */
    private Integer isWarehouseStaff;

    /**
     * 是否司机leader
     */
    private Integer isDtl;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 部门中文
     */
    private String deptNameCn;

    /**
     * 部门英文
     */
    private String deptNameEn;

    /**
     * 核算单元ID
     */
    @Deprecated
    private Long ocId;

    /**
     * 核算单元
     */
    private String ocCode;

    /**
     * 核算单元中文
     */
    private String ocNameCn;

    /**
     * 核算单元英文
     */
    private String ocNameEn;

    /**
     * 核算组织(所属国)
     */
    private String originCountry;

    /**
     * 汇报上级
     */
    private Long leaderId;

    /**
     * 汇报上级名
     */
    private String leaderName;

    /**
     * 业务节点
     */
    private Long bizModelId;

    /**
     * 业务节点名称（中文）
     */
    private String bizModelNameCn;

    /**
     * 业务节点名称（英文）
     */
    private String bizModelNameEn;

    /**
     * 项目ID
     */
    @Deprecated
    private Long projectId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 是否全球派遣（0:否 1:是）
     */
    private Integer isGlobalRelocation;

    /**
     * 签证类型
     */
    private Integer visaType;

    /**
     * 签证类型
     */
    private String visaTypeCn;

    /**
     * 签证类型
     */
    private String visaTypeEn;

    /**
     * 派遣开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dispatchStartDate;

    /**
     * 回国开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date returnStartDate;

    /**
     * 是否涉及工作交接(0: 不涉及 1: 涉及)
     */
    private Integer workHandover;

    /**
     * 工作交接人id
     */
    private Long workHandoverUserId;

    /**
     * 相关附件（最多存储五个附件）
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 调动备注
     */
    private String remark;

    public boolean deptOrLeaderChange(Long deptId, Long leaderId) {
        return !(this.deptId.equals(deptId) && this.leaderId.equals(leaderId));
    }
}
