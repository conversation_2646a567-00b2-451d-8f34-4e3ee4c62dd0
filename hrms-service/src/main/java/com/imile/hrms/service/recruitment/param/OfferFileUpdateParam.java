package com.imile.hrms.service.recruitment.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.dto.AttachmentParamDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfferFileUpdateParam {

    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 签字件
     */
    private List<AttachmentParamDTO> signatureFileUrls;

    /**
     * offer信附件
     */
    private List<AttachmentParamDTO> offerLetterFileUrls;

    /**
     * 背景调查文件附件
     */
    private List<AttachmentParamDTO> referenceCheckProfileUrls;
}
