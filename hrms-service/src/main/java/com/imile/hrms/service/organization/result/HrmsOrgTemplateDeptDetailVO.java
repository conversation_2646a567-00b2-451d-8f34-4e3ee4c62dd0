package com.imile.hrms.service.organization.result;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/1
 */
@Data
public class HrmsOrgTemplateDeptDetailVO {

    /**
     * 组织模板部门ID
     */
    private Long id;

    /**
     * 上级部门ID
     */
    private Long parentId;

    /**
     * 组织类型（1:实体组织 2:项目组织 3:职能模块 4:网点）
     */
    private Integer deptOrgType;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 业务领域（1:销售 2:客服 3:运营 4:商务 5:财务 6:人事 7:产研 8:采购 9:加盟 10:综合管理 11:综合管理-区域）
     */
    private List<String> bizAreaList;

    /**
     * 组织定位
     */
    private String deptPosition;

    /**
     * 组织职责
     */
    private String deptDuty;
}
