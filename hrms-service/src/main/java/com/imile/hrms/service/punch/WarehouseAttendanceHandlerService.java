package com.imile.hrms.service.punch;

import com.imile.hrms.dao.approval.model.HrmsApplicationFormDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.model.HrmsWarehousePunchPeriodDO;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.approval.bo.HrmsApplicationFormDetailBO;
import com.imile.hrms.service.attendance.dto.DayAttendanceHandlerDTO;
import com.imile.hrms.service.punch.dto.AttendanceAbnormalCheckDTO;

import java.util.List;

/**
 * 仓内考勤结果处理服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
public interface WarehouseAttendanceHandlerService {

    /**
     * 仓内考勤计算结果处理
     */
    void warehouseAttendanceResultHandler(HrmsWarehouseDetailDO warehouseDetail, Long dayId, String stateType);

    /**
     * 判断考勤异常计算结果
     */
    AttendanceAbnormalCheckDTO checkExistAttendanceException(Long dayId, Long userId, Long classId);

    /**
     * 考勤异常处理
     */
    void attendanceAbnormalHandler(HrmsWarehouseDetailDO warehouseDetail, Long dayId, Boolean updateFaceRecord, List<HrmsWarehousePunchPeriodDO> warehousePunchPeriodDOList);

    /**
     * 仓内考勤请假处理
     */
    void warehouseAttendanceLeaveAuditPassHandler(HrmsApplicationFormDO formDO, DayAttendanceHandlerDTO targetDayAttendanceHandlerDTO);

    /**
     * 仓内考勤补卡处理
     */
    void warehouseAttendanceReissueAuditPassHandler(HrmsUserInfoDO userInfoDO, Long abnormalId, Long dayId);

    /**
     * 仓内未打卡处理
     */
    void warehouseAttendanceNoPunchHandler(List<HrmsUserInfoDO> noClassUserList, List<HrmsUserInfoDO> offClassUserList, List<HrmsUserInfoDO> userListPreDay, List<HrmsUserInfoDO> userList, Long preDayId, Long dayId);

    /**
     * 仓内确认异常处理
     */
    void warehouseConfirmExceptionHandler(Long abnormalId, Long userId, Long dayId);

    /**
     * 仓内补工时处理
     */
    void warehouseAddDurationPassHandler(HrmsApplicationFormDetailBO applicationFormDetailBO);

    /**
     * 重试仓内异常计算
     */
    void retryCalculateAbnormal(WarehouseDetailParam param);

    /**
     * 仓内异常计算
     */
    void calculateAbnormal(String userCodes, Long dayId);

}
