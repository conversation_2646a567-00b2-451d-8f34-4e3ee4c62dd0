package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/13 11:02
 * @version: 1.0
 */
@Data
public class SalaryAttendanceUserDetailParam {

    /**
     * 考勤记录表ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long salaryAttendanceUserInfoId;
}
