package com.imile.hrms.service.bpm.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.imile.bpm.enums.ApprovalRoleEnum;
import com.imile.bpm.enums.ApprovalRoleValueEnum;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.api.bpm.api.BpmApprovalInfoApi;
import com.imile.hrms.api.bpm.dto.BpmApprovalRoleUserApiDTO;
import com.imile.hrms.api.bpm.query.BpmApprovalRoleApiQuery;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.organization.DeptLeaderPropertyEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.dao.bussiness.area.HrmsBizeAreaBaseConfigDO;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.dao.HrmsOrgBusinessConfigDao;
import com.imile.hrms.dao.organization.dto.EntDeptDTO;
import com.imile.hrms.dao.organization.enums.DeptBizAreaEnum;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.organization.model.HrmsOrgBusinessConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.dict.service.DictService;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.business.area.HrmsBizeAreaBaseConfigService;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.imile.hrms.service.organization.HrmsEntDeptService;
import com.imile.rpc.common.RpcResult;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-11-8
 * @version: 1.0
 */
@Service(version = "1.0.0")
@Slf4j
public class BpmApprovalInfoApiImpl implements BpmApprovalInfoApi {

    @Resource
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Resource
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Resource
    private HrmsEntPostDao hrmsEntPostDao;
    @Resource
    private HrmsEntDeptService hrmsEntDeptService;
    @Resource
    private EntDeptNewService entDeptNewService;
    @Resource
    private HrmsDeptNewManage hrmsDeptNewManage;
    @Resource
    private HrmsOrgBusinessConfigDao hrmsOrgBusinessConfigDao;
    @Resource
    private HrmsBizeAreaBaseConfigService hrmsBizeAreaBaseConfigService;
    @Resource
    private DictService dictService;

    @Value("${dept.root.id:1032652}")
    private Long rootDeptId;

    private static final String HR = "HR";
    private static final String COMMERCIAL_CONTRACT = "COMMERCIAL_CONTRACT";
    private static final String SALES_DIRECTOR = "SALES_DIRECTOR";
    private static final String FINANCIAL_MANAGER = "FINANCIAL_MANAGER";
    private static final String COD_ACCOUNTING = "COD_ACCOUNTING";
    private static final String CASHIER = "CASHIER";
    private static final String FINANCE_MANAGER = "FINANCE_MANAGER";
    private static final String LEGAL_DIRECTOR = "LEGAL_DIRECTOR";
    private static final String ADMIN_OFFICER = "ADMIN_OFFICER";
    private static final String OPERATION_DEPT_HEADER = "Operation Mgt. Dep.";

    @Override
    public RpcResult<List<BpmApprovalRoleUserApiDTO>> selectApprovalUserInfoList(BpmApprovalRoleApiQuery query) {
        log.info("selectApprovalUserInfoList入参值为:{}", JSON.toJSONString(query));
        if (query == null || StringUtils.isBlank(query.getApprovalRoleFieldValue()) || StringUtils.isBlank(query.getValue()) || CollectionUtils.isEmpty(query.getApprovalRoleValueList())) {
            return RpcResult.fail(HrmsErrorCodeEnums.QUERY_PARAM_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.QUERY_PARAM_IS_EMPTY.getDesc()));
        }
        if (ApprovalRoleValueEnum.getInstanceByCode(query.getApprovalRoleFieldValue()) == null) {
            return RpcResult.fail(HrmsErrorCodeEnums.APPROVAL_ROLE_VALUE_NOT_FIND.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.APPROVAL_ROLE_VALUE_NOT_FIND.getDesc()));
        }

        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        try {
            //申请人/被申请人
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.APPLY_USER.getCode())
                    || StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())) {
                return RpcResult.ok(applyUserHandler(query));
            }

            //部门
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.DEPT.getCode())) {
                return RpcResult.ok(deptHandler(query));
            }

            //网点
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.STATION.getCode())) {
                return RpcResult.ok(stationHandler(query));
            }

            //费用承担国家/结算主体
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.COUNTRY.getCode())
                    || StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())) {
                return RpcResult.ok(countryHandler(query));
            }

            //出发国家/目的国家
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                    || StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                return RpcResult.ok(travelCountryHandler(query));
            }

            //行业线
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.BIZ_AREA.getCode())) {
                return RpcResult.ok(bizAreaHandler(query));
            }

            // 调入部门
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.TRANSFER_IN_DEPT.getCode())) {
                return RpcResult.ok(deptHandler(query));
            }

            // 指定审批人
            if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.ASSIGNED_APPROVER.getCode())) {
                return RpcResult.ok(assignedApproverHandler(query));
            }
            return RpcResult.ok(resultApiDTOList);
        } catch (Exception e) {
            return RpcResult.fail("404", e.getMessage());
        }
    }

    private List<BpmApprovalRoleUserApiDTO> bizAreaHandler(BpmApprovalRoleApiQuery query) {
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        // 目前就一个角色后续也不会新增，不需要判断
        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            List<String> userCodeList = new ArrayList<>();
            HrmsBizeAreaBaseConfigDO bizeAreaBaseConfigDO = hrmsBizeAreaBaseConfigService.getById(Long.valueOf(query.getValue()));
            if (bizeAreaBaseConfigDO != null) {
                userApiDTO.setApprovalRoleFieldName(RequestInfoHolder.isChinese() ? bizeAreaBaseConfigDO.getBusinessAreaNameCn() : bizeAreaBaseConfigDO.getBusinessAreaNameEn());
                HrmsUserInfoDO user = hrmsUserInfoManage.getUserInfoById(bizeAreaBaseConfigDO.getResponsibleUserId());
                userCodeList.add(user.getUserCode());
            }
            userApiDTO.setUserCodeList(userCodeList);
            resultApiDTOList.add(userApiDTO);
        }
        return resultApiDTOList;
    }

    private List<BpmApprovalRoleUserApiDTO> assignedApproverHandler(BpmApprovalRoleApiQuery query) {
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        List<Long> userIds = Arrays.stream(query.getValue().split(",")).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
        List<HrmsUserInfoDO> users = hrmsUserInfoManage.selectUserInfoByIds(userIds);
        if (CollectionUtils.isEmpty(users)) {
            return resultApiDTOList;
        }
        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            List<String> userCodeList = new ArrayList<>();
            userApiDTO.setUserCodeList(userCodeList);
            resultApiDTOList.add(userApiDTO);
            //指定审批人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ONE_SELF.getCode())) {
                List<String> userNameList = Lists.newArrayList();
                for (HrmsUserInfoDO user : users) {
                    userNameList.add(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()));
                    userCodeList.add(user.getUserCode());
                }
                userApiDTO.setApprovalRoleFieldName(String.join(",", userNameList));
            }
        }
        return resultApiDTOList;
    }

    /**
     * 出发国家/目的国家
     */
    private List<BpmApprovalRoleUserApiDTO> travelCountryHandler(BpmApprovalRoleApiQuery query) {
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            userApiDTO.setApprovalRoleFieldName(CountryCodeEnum.getCountryName(query.getValue(), RequestInfoHolder.isChinese()));
            List<String> userCodeList = new ArrayList<>();
            userApiDTO.setUserCodeList(userCodeList);
            resultApiDTOList.add(userApiDTO);

            //岗位获取
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ADMIN_OFFICER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.ADMIN_OFFICER.getCode());
            }

            //作业部门负责人
//            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.OPERATION_DEPT_HEAD.getCode())) {
//                operationDeptHeadHandler(userCodeList, query.getValue());
//            }
            // 国家负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COUNTRY_HEAD.getCode())) {
                countryHeadHandler(userCodeList, query.getValue());
            }

            // 常驻国相关角色
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_ADMIN.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.ADMIN.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_ER.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.ER.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_HR_BP.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.HRBP.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_HRM.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.HRM.getType());
            }
        }
        return resultApiDTOList;
    }

    /**
     * 费用承担国家/结算主体
     */
    private List<BpmApprovalRoleUserApiDTO> countryHandler(BpmApprovalRoleApiQuery query) {
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            List<String> userCodeList = new ArrayList<>();
            userApiDTO.setUserCodeList(userCodeList);
            userApiDTO.setApprovalRoleFieldName(CountryCodeEnum.getCountryName(query.getValue(), RequestInfoHolder.isChinese()));
            resultApiDTOList.add(userApiDTO);
//            //子公司管理员
//            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COMPANY_HEAD.getCode())) {
//                companyHeadHandler(userCodeList, Long.valueOf(query.getValue()));
//            }
            //国家负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COUNTRY_HEAD.getCode())) {
                countryHeadHandler(userCodeList, query.getValue());
            }

            // 常驻国相关角色
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_ADMIN.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.ADMIN.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_ER.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.ER.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_HR_BP.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, query.getValue(), DeptLeaderPropertyEnum.HRBP.getType());
            }

            //岗位获取
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HRM.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.HRM.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.FINANCE_MANAGER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.FINANCE_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LEGAL_DIRECTOR.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.LEGAL_DIRECTOR.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ADMIN_OFFICER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.ADMIN_OFFICER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ACCOUNT_MANAGER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.ACCOUNT_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.AM_SUPERVISOR.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.AM_SUPERVISOR.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ACCOUNT_MANAGER_INTERN.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.ACCOUNT_MANAGER_INTERN.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_CUSTOMS_AFFAIRS.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.HEAD_OF_CUSTOMS_AFFAIRS.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ACCOUNT_MANAGEMENT_SPECIALIST.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.ACCOUNT_MANAGEMENT_SPECIALIST.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.IT_SUPPORT.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.IT_SUPPORT.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.AR_ACCOUNTANT.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.AR_ACCOUNTANT.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.GENERAL_MANAGER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.GENERAL_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_SHEIN_KEY_ACCOUNT.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.HEAD_OF_SHEIN_KEY_ACCOUNT.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.SENIOR_LEGAL_OFFICER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.SENIOR_LEGAL_OFFICER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_SALES.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.HEAD_OF_SALES.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.KEY_ACCOUNT_SPECIALIST.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.KEY_ACCOUNT_SPECIALIST.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LEGAL_COUNSEL.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.LEGAL_COUNSEL.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.GENERAL_LEGAL_COUNSEL_GLOBAL.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.GENERAL_LEGAL_COUNSEL_GLOBAL.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.KEY_ACCOUNT_MANAGER.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.KEY_ACCOUNT_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COMMERCIAL_CONTRACT_ORGANIZER_SPECIALIST.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.COMMERCIAL_CONTRACT_ORGANIZER_SPECIALIST.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_GLOBAL_CONTRACT_BUSINESS.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.HEAD_OF_GLOBAL_CONTRACT_BUSINESS.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_OPERATIONS.getCode())) {
                postHandler(userCodeList, query.getValue(), ApprovalRoleEnum.HEAD_OF_OPERATIONS.getCode());
            }
        }
        return resultApiDTOList;
    }

    /**
     * 网点
     */
    private List<BpmApprovalRoleUserApiDTO> stationHandler(BpmApprovalRoleApiQuery query) {
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        HrmsEntDeptDO deptDO = hrmsEntDeptDao.getById(Long.valueOf(query.getValue()));
        if (deptDO == null) {
            return resultApiDTOList;
        }
        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            List<String> userCodeList = new ArrayList<>();
            userApiDTO.setUserCodeList(userCodeList);
            userApiDTO.setApprovalRoleFieldName(RequestInfoHolder.isChinese() ? deptDO.getDeptNameCn() : deptDO.getDeptNameEn());
            resultApiDTOList.add(userApiDTO);

            //网点负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.STATION_HEAD.getCode())) {
                stationHeadHandler(userCodeList, deptDO);
            }

            //作业部门负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.OPERATION_DEPT_HEAD.getCode())) {
                operationDeptHeadHandler(userCodeList, deptDO);
            }
        }
        return resultApiDTOList;
    }

    /**
     * 部门
     */
    private List<BpmApprovalRoleUserApiDTO> deptHandler(BpmApprovalRoleApiQuery query) {
        log.info("deptHandler入参值为:{}", JSON.toJSONString(query));
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = Lists.newArrayList();
        HrmsEntDeptDO deptDO = hrmsEntDeptDao.getById(Long.valueOf(query.getValue()));
        if (deptDO == null) {
            return resultApiDTOList;
        }
        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            List<String> userCodeList = new ArrayList<>();
            userApiDTO.setUserCodeList(userCodeList);
            userApiDTO.setApprovalRoleFieldName(BusinessFieldUtils.getUnifiedDeptName(deptDO.getDeptNameCn(), deptDO.getDeptNameEn()));
            resultApiDTOList.add(userApiDTO);
            //部门负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_HEAD.getCode())) {
                deptHeadHandler(userCodeList, Long.valueOf(query.getValue()));
            }
            //作业部门负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.OPERATION_DEPT_HEAD.getCode())) {
                operationDeptHeadHandler(userCodeList, deptDO);
            }
            //区域负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.REGIONAL_MANAGER.getCode())) {
                regionalManagerHandler(userCodeList, Long.valueOf(query.getValue()));
            }
            //部门负责人-hrm
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_HRM.getCode())) {
                deptManagerHandler(userCodeList, Long.valueOf(query.getValue()), DeptLeaderPropertyEnum.HRM.getType());
            }
            //部门负责人-er
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_ER.getCode())) {
                deptManagerHandler(userCodeList, Long.valueOf(query.getValue()), DeptLeaderPropertyEnum.ER.getType());
            }
            //部门负责人-gm
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_GM.getCode())) {
                deptManagerHandler(userCodeList, Long.valueOf(query.getValue()), DeptLeaderPropertyEnum.GM.getType());
            }
            //部门负责人-recruiter
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_RECRUITER.getCode())) {
                deptManagerHandler(userCodeList, Long.valueOf(query.getValue()), DeptLeaderPropertyEnum.RECRUITER.getType());
            }
            //部门负责人-部门Admin
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_ADMIN.getCode())) {
                deptManagerHandler(userCodeList, Long.valueOf(query.getValue()), DeptLeaderPropertyEnum.ADMIN.getType());
            }

            //部门HRBP-hrbp
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_HR_BP.getCode()) || StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_HRBP.getCode())) {
                deptManagerHandler(userCodeList, Long.valueOf(query.getValue()), DeptLeaderPropertyEnum.HRBP.getType());
            }

            //部门领域负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_BIZ_AREA.getCode())) {
                bizAreaByDeptManagerHandler(userCodeList, Long.valueOf(query.getValue()));
            }
            //二级部门主管
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.SECOND_DEPT_LEADER.getCode())) {
                assignLevelDeptManagerHandler(userCodeList, Long.valueOf(query.getValue()), 2);
            }
            //上层部门主管
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.PARENT_DEPT_LEADER.getCode())) {
                parentDeptLeaderHandler(userCodeList, Long.valueOf(query.getValue()));
            }
            //一级部门主管
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.FIRST_DEPT_LEADER.getCode())) {
                assignLevelDeptManagerHandler(userCodeList, Long.valueOf(query.getValue()), 1);
            }
        }
        return resultApiDTOList;
    }

    private void parentDeptLeaderHandler(List<String> userCodeList, Long deptId) {
        HrmsEntDeptDO dept = hrmsEntDeptDao.getById(deptId);
        if (dept == null) {
            return;
        }

        Long parentId = dept.getParentId();
        if (parentId == null) {
            return;
        }

        HrmsEntDeptDO parentDept = hrmsEntDeptDao.getById(parentId);
        if (parentDept == null) {
            return;
        }

        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(parentDept.getLeaderCode());
        if (hrmsUserInfoDO == null) {
            return;
        }

        userCodeList.add(hrmsUserInfoDO.getUserCode());
    }

    /**
     * 常驻国的业务类型相关负责人
     *
     * @param userCodeList
     * @param locationCountry
     * @param responsibleType 业务类型
     */
    private void locationCountryResponsibleTypeHeadHandler(List<String> userCodeList, String locationCountry, Integer responsibleType) {
        if (StringUtils.isBlank(locationCountry)) {
            return;
        }
        // 取一级组织的地理国为员工的常驻国；存在多个则取第一个
        List<HrmsEntDeptDO> deptList = hrmsDeptNewManage.selectFirstLevelDeptByCountry(locationCountry);
        if (CollectionUtils.isEmpty(deptList)) {
            return;
        }
        HrmsEntDeptDO firstDept = deptList.get(0);
        log.info("locationCountryResponsibleTypeHeadHandler firstDept:{}", JSON.toJSONString(firstDept));
        deptManagerHandler(userCodeList, firstDept.getId(), responsibleType);
    }

    private void assignLevelDeptManagerHandler(List<String> userCodeList, Long id, Integer level) {
        // 部门层级必须大于0
        if (level < 1 || id == null) {
            return;
        }
        log.info("assignLevelDeptManagerHandler id:{}", id);
        Map<Long, List<HrmsEntDeptDO>> deptChainMap = entDeptNewService.getDeptChainMap(Lists.newArrayList(id));
        if (deptChainMap.isEmpty()) {
            return;
        }
        log.info("deptChainMap:{}", JSON.toJSONString(deptChainMap));
        if (deptChainMap.containsKey(id)) {
            List<HrmsEntDeptDO> deptChain = deptChainMap.getOrDefault(id, Lists.newArrayList());
            if (deptChain.size() == 1 && deptChain.get(0).getParentId() == 0L) {
                // 兼容选择的部门就是imile group 或者 网点的场景
                HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(deptChain.get(0).getLeaderCode());
                log.info("hrmsUserInfoDO:{}", JSON.toJSONString(hrmsUserInfoDO));
                userCodeList.add(hrmsUserInfoDO.getUserCode());
                return;
            }
            // 选择的部门非imile group时，过滤掉deptList中parentId为0的数据(去除imile group)
            List<HrmsEntDeptDO> deptList = deptChain.stream().filter(dept -> dept.getParentId() != 0L).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deptList)) {
                return;
            }
            // x级部门主管 = 组织架构树结构中x+1层部门所配置的主管, 如果当前选择的部门层级小于等于x，则取当前部门主管
            // (假设level = 1 , 代表取一级部门主管，此时，若审批选择的部门本身就是一级部门，那就取自己的主管，如果选择的部门是3级部门，那就取1级部门的主管。)
            HrmsEntDeptDO dept = deptList.size() <= level ? deptList.get(deptList.size() - 1) : deptList.get(level - 1);
            HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(dept.getLeaderCode());
            if (hrmsUserInfoDO != null) {
                userCodeList.add(hrmsUserInfoDO.getUserCode());
            }
            log.info("hrmsUserInfoDO:{}", JSON.toJSONString(hrmsUserInfoDO));
        }

    }

    private void bizAreaByDeptManagerHandler(List<String> userCodeList, Long id) {
        HrmsEntDeptDO dept = hrmsEntDeptDao.getById(id);
        if (dept == null || StringUtils.isEmpty(dept.getBizArea())) {
            return;
        }

        HrmsBizeAreaBaseConfigDO hrmsBizeAreaBaseConfigDO = hrmsBizeAreaBaseConfigService.getById(dept.getBizArea());

        if (hrmsBizeAreaBaseConfigDO == null) {
            return;
        }

        HrmsUserInfoDO user = hrmsUserInfoManage.getUserInfoById(hrmsBizeAreaBaseConfigDO.getResponsibleUserId());

        userCodeList.add(user.getUserCode());
    }

    private void deptManagerHandler(List<String> userCodeList, Long valueOf, Integer code) {
        HrmsOrgBusinessConfigDO hrmsOrgBusinessConfigDO = hrmsOrgBusinessConfigDao.getBaseMapper().selectOne(new LambdaQueryWrapper<HrmsOrgBusinessConfigDO>()
                .eq(HrmsOrgBusinessConfigDO::getDeptId, valueOf)
                .eq(HrmsOrgBusinessConfigDO::getLeaderProperty, code)
                .eq(HrmsOrgBusinessConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .last("limit 1")
        );

        if (hrmsOrgBusinessConfigDO == null) {
            return;
        }
        String userIdList = hrmsOrgBusinessConfigDO.getLeaderId();

        // 转换成List<Long>
        List<Long> userIds = Arrays.stream(userIdList.split(",")).map(Long::valueOf).collect(Collectors.toList());

        // 查询用户信息
        List<HrmsUserInfoDO> userInfoList = hrmsUserInfoManage.selectUserInfoByIds(userIds);
        if (CollectionUtils.isEmpty(userInfoList)) {
            return;
        }

        userCodeList.addAll(userInfoList.stream().map(HrmsUserInfoDO::getUserCode).collect(Collectors.toList()));
    }

    /**
     * 申请人/被申请人
     */
    private List<BpmApprovalRoleUserApiDTO> applyUserHandler(BpmApprovalRoleApiQuery query) {
        List<BpmApprovalRoleUserApiDTO> resultApiDTOList = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(query.getApprovalRoleFieldValue(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
            //如果是申请人，将CODE转为ID
            List<HrmsUserInfoDO> infoDOList = hrmsUserInfoManage.selectUserInfoByCodes(Arrays.asList(query.getValue()));
            if (CollectionUtils.isEmpty(infoDOList) || StringUtils.isBlank(infoDOList.get(0).getUserCode())) {
                return resultApiDTOList;
            }
            query.setValue(String.valueOf(infoDOList.get(0).getId()));
        }

        List<Long> values = Arrays.stream(query.getValue().split(","))
                .map(Long::valueOf).collect(Collectors.toList());
        List<HrmsUserInfoDO> hrmsUserInfoDOList = hrmsUserInfoManage.selectUserInfoByIds(values);
        if (CollectionUtils.isEmpty(hrmsUserInfoDOList)) {
            return resultApiDTOList;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoDOList.get(0);

        for (String role : query.getApprovalRoleValueList()) {
            BpmApprovalRoleUserApiDTO userApiDTO = new BpmApprovalRoleUserApiDTO();
            userApiDTO.setApprovalRoleValue(role);
            List<String> userCodeList = new ArrayList<>();
            userApiDTO.setUserCodeList(userCodeList);
            userApiDTO.setApprovalRoleFieldName(infoDO.getUserName());
            resultApiDTOList.add(userApiDTO);

            //特殊逻辑:自己本身
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ONE_SELF.getCode())) {
                if (hrmsUserInfoDOList.size() == 1) {
                    oneSelfHandler(userCodeList, Long.valueOf(query.getValue()));
                } else {
                    //todo 特殊处理,传多个员工
                    oneSelfHandler(userCodeList, values);
                }
            }
            //汇报上级
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.REPORT_LEADER.getCode())) {
                reportLeaderHandler(userCodeList, Long.valueOf(query.getValue()));
            }
            // 间接上级
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.SECOND_LEADER.getCode())) {
                secondLeaderHandler(userCodeList, Long.valueOf(query.getValue()));
            }
            if (infoDO.getOriginCountry() == null) {
                continue;
            }
            //作业部门负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.OPERATION_DEPT_HEAD.getCode())) {
                HrmsEntDeptDO dept = hrmsEntDeptDao.getById(infoDO.getDeptId());
                operationDeptHeadHandler(userCodeList, dept);
            }
            //一级部门负责人(找到用户的部门，通过部门找到部门链，然后把部门链中level为1的部门拿出来，用改部门的负责人就可以)
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.FIRST_LEVEL_DEPT_HEAD.getCode())) {
                HrmsEntDeptDO dept = hrmsEntDeptDao.getById(infoDO.getDeptId());
                firstLevelDeptHeaderHandler(userCodeList, dept);
            }
//            //子公司管理员
//            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COMPANY_HEAD.getCode())) {
//                companyHeadHandler(userCodeList, infoDO.getCompanyId());
//            }
            //国家负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COUNTRY_HEAD.getCode())) {
                countryHeadHandler(userCodeList, infoDO.getOriginCountry());
            }

            //部门负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_HEAD.getCode())) {
                deptHeadHandler(userCodeList, infoDO.getDeptId());
            }

            //区域负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.REGIONAL_MANAGER.getCode())) {
                regionalManagerHandler(userCodeList, infoDO.getDeptId());
            }

            //费用部门负责人
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COST_DEPT_HEAD.getCode())) {
                costDeptHeadHandler(userCodeList, infoDO.getDeptId());
            }
            // 部门HRM
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_HRM.getCode())) {
                deptManagerHandler(userCodeList, infoDO.getDeptId(), DeptLeaderPropertyEnum.HRM.getType());
            }
            // 部门GM
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.DEPT_GM.getCode())) {
                deptManagerHandler(userCodeList, infoDO.getDeptId(), DeptLeaderPropertyEnum.GM.getType());
            }
            // 一级部门主管
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.FIRST_DEPT_LEADER.getCode())) {
                assignLevelDeptManagerHandler(userCodeList, infoDO.getDeptId(), 1);
            }
            // 二级部门主管
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.SECOND_DEPT_LEADER.getCode())) {
                assignLevelDeptManagerHandler(userCodeList, infoDO.getDeptId(), 2);
            }

            // 常驻国相关角色
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_ADMIN.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, infoDO.getLocationCountry(), DeptLeaderPropertyEnum.ADMIN.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_ER.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, infoDO.getLocationCountry(), DeptLeaderPropertyEnum.ER.getType());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LOCATION_COUNTRY_HR_BP.getCode())) {
                locationCountryResponsibleTypeHeadHandler(userCodeList, infoDO.getLocationCountry(), DeptLeaderPropertyEnum.HRBP.getType());
            }

            //岗位获取
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HRM.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.HRM.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.FINANCE_MANAGER.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.FINANCE_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LEGAL_DIRECTOR.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.LEGAL_DIRECTOR.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ADMIN_OFFICER.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.ADMIN_OFFICER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ACCOUNT_MANAGER.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.ACCOUNT_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.AM_SUPERVISOR.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.AM_SUPERVISOR.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ACCOUNT_MANAGER_INTERN.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.ACCOUNT_MANAGER_INTERN.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_CUSTOMS_AFFAIRS.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.HEAD_OF_CUSTOMS_AFFAIRS.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.ACCOUNT_MANAGEMENT_SPECIALIST.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.ACCOUNT_MANAGEMENT_SPECIALIST.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.IT_SUPPORT.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.IT_SUPPORT.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.AR_ACCOUNTANT.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.AR_ACCOUNTANT.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.GENERAL_MANAGER.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.GENERAL_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_SHEIN_KEY_ACCOUNT.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.HEAD_OF_SHEIN_KEY_ACCOUNT.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.SENIOR_LEGAL_OFFICER.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.SENIOR_LEGAL_OFFICER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_SALES.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.HEAD_OF_SALES.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.KEY_ACCOUNT_SPECIALIST.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.KEY_ACCOUNT_SPECIALIST.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.LEGAL_COUNSEL.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.LEGAL_COUNSEL.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.GENERAL_LEGAL_COUNSEL_GLOBAL.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.GENERAL_LEGAL_COUNSEL_GLOBAL.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.KEY_ACCOUNT_MANAGER.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.KEY_ACCOUNT_MANAGER.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.COMMERCIAL_CONTRACT_ORGANIZER_SPECIALIST.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.COMMERCIAL_CONTRACT_ORGANIZER_SPECIALIST.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_GLOBAL_CONTRACT_BUSINESS.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.HEAD_OF_GLOBAL_CONTRACT_BUSINESS.getCode());
            }
            if (StringUtils.equalsIgnoreCase(role, ApprovalRoleEnum.HEAD_OF_OPERATIONS.getCode())) {
                postHandler(userCodeList, infoDO.getOriginCountry(), ApprovalRoleEnum.HEAD_OF_OPERATIONS.getCode());
            }
        }
        return resultApiDTOList;
    }


    /**
     * 自己本身获取
     */
    private void oneSelfHandler(List<String> userCodeList, Long userId) {
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (infoDO == null) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 自己本身获取
     */
    private void oneSelfHandler(List<String> userCodeList, List<Long> userIds) {
        List<HrmsUserInfoDO> hrmsUserInfoDOList = hrmsUserInfoManage.selectUserInfoByIds(userIds);
        if (CollectionUtils.isEmpty(hrmsUserInfoDOList)) {
            return;
        }
        userCodeList.addAll(hrmsUserInfoDOList.stream()
                .map(HrmsUserInfoDO::getUserCode)
                .collect(Collectors.toList()));
    }

    /**
     * 汇报上级获取
     */
    private void reportLeaderHandler(List<String> userCodeList, Long userId) {
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (infoDO == null || infoDO.getLeaderId() == null) {
            //没有汇报上级
            return;
        }
        HrmsUserInfoDO leaderInfoDO = hrmsUserInfoManage.getUserInfoById(infoDO.getLeaderId());
        if (Objects.isNull(leaderInfoDO) || StringUtils.isBlank(leaderInfoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(leaderInfoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(leaderInfoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(leaderInfoDO.getUserCode());
    }

    /**
     * 二级汇报上级获取
     */
    private void secondLeaderHandler(List<String> userCodeList, Long valueOf) {
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(valueOf);
        if (infoDO == null || infoDO.getLeaderId() == null) {
            //没有汇报上级
            return;
        }
        HrmsUserInfoDO leaderInfoDO = hrmsUserInfoManage.getUserInfoById(infoDO.getLeaderId());
        if (Objects.isNull(leaderInfoDO) || leaderInfoDO.getLeaderId() == null) {
            //没有二级汇报上级
            return;
        }
        HrmsUserInfoDO secondLeaderInfoDO = hrmsUserInfoManage.getUserInfoById(leaderInfoDO.getLeaderId());
        if (Objects.isNull(secondLeaderInfoDO) || StringUtils.isBlank(secondLeaderInfoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(secondLeaderInfoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(secondLeaderInfoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(secondLeaderInfoDO.getUserCode());
    }

//    /**
//     * 子公司负责人获取
//     */
//    private void companyHeadHandler(List<String> userCodeList, Long companyId) {
//        List<Long> userIdList = hrmsUserRoleDao.selectUserByRole(AttributeTypeEnum.SUBSIDIARY.getCode()).stream().map(item -> item.getUserId()).collect(Collectors.toList());
//        List<String> filterUserCodeList = hrmsUserInfoManage.selectUserInfoByIds(userIdList)
//                .stream().filter(item -> item.getCompanyId() != null && item.getCompanyId().equals(companyId) && StringUtils.isNotBlank(item.getUserCode()) && StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()) && StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()))
//                .map(item -> item.getUserCode()).collect(Collectors.toList());
//        userCodeList.addAll(filterUserCodeList);
//    }

    /**
     * 一级部门负责人
     */
    private void firstLevelDeptHeaderHandler(List<String> userCodeList, HrmsEntDeptDO dept) {
        if (Objects.isNull(dept)) {
            return;
        }
        Map<Long, List<HrmsEntDeptDO>> deptChainMap = entDeptNewService.getDeptChainMap(Lists.newArrayList(dept.getId()));
        if (deptChainMap.isEmpty()) {
            return;
        }
        List<HrmsEntDeptDO> existDeptList = deptChainMap.get(dept.getId());
        if (CollectionUtils.isEmpty(existDeptList)) {
            return;
        }
        List<HrmsEntDeptDO> firstLevelDeptList = existDeptList.stream()
                .filter(item -> item.getLevel() != null && item.getLevel().equals(BusinessConstant.ONE))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(firstLevelDeptList)) {
            return;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(firstLevelDeptList.get(0).getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 作业部门负责人
     */
    private void operationDeptHeadHandler(List<String> userCodeList, HrmsEntDeptDO dept) {
        if (Objects.isNull(dept)) {
            return;
        }
        List<HrmsEntDeptDO> deptDOList = hrmsDeptNewManage.getByBizCountriesAndArea(dept.getBizCountry(),
                DeptBizAreaEnum.OPERATION.getValue());
        if (CollectionUtils.isEmpty(deptDOList)) {
            return;
        }
        // 递归查找符合条件的上级组织
        List<HrmsEntDeptDO> matchedParentDeptList = Lists.newArrayList();
        List<Long> operationDeptIdList = deptDOList.stream()
                .map(HrmsEntDeptDO::getId)
                .collect(Collectors.toList());
        this.matchOperationDept(dept, operationDeptIdList, matchedParentDeptList);
        if (matchedParentDeptList.isEmpty()) {
            return;
        }
        // 取matchedDeptList最后一个元素即顶级作业部门
        HrmsEntDeptDO topOperationDept = matchedParentDeptList.get(matchedParentDeptList.size() - 1);
        if (Objects.isNull(topOperationDept.getLeaderCode())) {
            return;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(topOperationDept.getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 部门负责人获取
     */
    private void deptHeadHandler(List<String> userCodeList, Long deptId) {
        HrmsEntDeptDO deptDO = hrmsEntDeptDao.getById(deptId);
        if (deptDO == null || deptDO.getLeaderCode() == null) {
            return;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(deptDO.getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 费用部门负责人
     */
    private void costDeptHeadHandler(List<String> userCodeList, Long deptId) {
        EntDeptDTO deptDO = hrmsEntDeptService.getSettlementDept(deptId);
        if (deptDO == null || deptDO.getLeaderCode() == null) {
            return;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(deptDO.getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 网点负责人获取
     */
    private void stationHeadHandler(List<String> userCodeList, HrmsEntDeptDO deptDO) {
        if (deptDO == null || deptDO.getLeaderCode() == null) {
            return;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(deptDO.getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 区域负责人获取
     */
    private void regionalManagerHandler(List<String> userCodeList, Long deptId) {
        HrmsEntDeptDO dept = hrmsEntDeptDao.getById(deptId);
        if (Objects.isNull(dept)) {
            return;
        }
        // 查找当前组织业务覆盖国下的综合管理区域部门
        List<HrmsEntDeptDO> managementAreaDeptList = hrmsDeptNewManage.getByBizCountriesAndArea(dept.getBizCountry(),
                DeptBizAreaEnum.MANAGEMENT_REGION.getValue());
        if (CollectionUtils.isEmpty(managementAreaDeptList)) {
            return;
        }
        // 递归查找符合条件的上级组织
        List<Long> managementAreaDeptIdList = managementAreaDeptList.stream()
                .map(HrmsEntDeptDO::getId)
                .collect(Collectors.toList());
        HrmsEntDeptDO managementAreaDept = this.matchManagementAreaDept(dept, managementAreaDeptIdList);
        if (Objects.isNull(managementAreaDept) || Objects.isNull(managementAreaDept.getLeaderCode())) {
            return;
        }
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(dept.getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 国家负责人获取
     */
    private void countryHeadHandler(List<String> userCodeList, String country) {
        List<HrmsEntDeptDO> deptList = entDeptNewService.listByBizAreaList(Collections.singletonList(
                DeptBizAreaEnum.MANAGEMENT.getValue()));
        HrmsEntDeptDO dept = deptList.stream().filter(d -> d.getBizCountry().contains(country)).findFirst().orElse(new HrmsEntDeptDO());
        HrmsUserInfoDO infoDO = hrmsUserInfoManage.getUserInfoById(dept.getLeaderCode());
        if (Objects.isNull(infoDO) || StringUtils.isBlank(infoDO.getUserCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getStatus(), StatusEnum.ACTIVE.getCode())
                || !StringUtils.equalsIgnoreCase(infoDO.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())) {
            return;
        }
        userCodeList.add(infoDO.getUserCode());
    }

    /**
     * 相关岗位用户获取
     */
    private void postHandler(List<String> userCodeList, String country, String postName) {
        //给出指定的岗位
        List<Long> postIdList = hrmsEntPostDao.getByPostName(postName).stream().map(HrmsEntPostDO::getId).collect(Collectors.toList());
        List<String> filterUserCodeList = hrmsUserInfoManage.selectByPostId(postIdList)
                .stream().filter(item -> Objects.equals(country, item.getOriginCountry()) && StringUtils.isNotBlank(item.getUserCode()) && StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()) && StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()))
                .map(HrmsUserInfoDO::getUserCode).collect(Collectors.toList());
        userCodeList.addAll(filterUserCodeList);
    }

    private void matchOperationDept(HrmsEntDeptDO dept, List<Long> operationDeptIdList, List<HrmsEntDeptDO> matchedDeptList) {
        // 当前组织的业务领域是运营则加入结果集
        if (operationDeptIdList.contains(dept.getId())) {
            matchedDeptList.add(dept);
        }
        // 上级组织为根节点则停止递归
        if (rootDeptId.equals(dept.getParentId())) {
            return;
        }
        // 上级组织不存在则停止递归
        HrmsEntDeptDO parentDept = hrmsEntDeptDao.getById(dept.getParentId());
        if (Objects.isNull(parentDept)) {
            return;
        }
        // 继续递归判断上级组织是否符合条件
        this.matchOperationDept(parentDept, operationDeptIdList, matchedDeptList);
    }

    private HrmsEntDeptDO matchManagementAreaDept(HrmsEntDeptDO dept, List<Long> managementAreaDeptIdList) {
        // 当前组织的业务领域是综合管理区域则返回 否则递归查找上级组织是否符合条件
        if (managementAreaDeptIdList.contains(dept.getId())) {
            return dept;
        }
        // 上级组织为根节点则停止递归
        if (rootDeptId.equals(dept.getParentId())) {
            return null;
        }
        HrmsEntDeptDO parentDept = hrmsEntDeptDao.getById(dept.getParentId());
        // 上级组织不存在则停止递归
        if (Objects.isNull(parentDept)) {
            return null;
        }
        // 继续递归判断上级组织是否符合条件
        return this.matchManagementAreaDept(parentDept, managementAreaDeptIdList);
    }

    private void functionLeaderHandler(List<String> userCodeList, HrmsEntDeptDO dept) {
        if (dept == null) {
            return;
        }
        String bizCountry = dept.getBizCountry();
        if (StringUtils.isBlank(bizCountry)) {
            return;
        }
        List<String> bizAreaList = Arrays.stream(dept.getBizArea().split(HrmsStringUtil.COMMA)).collect(Collectors.toList());
        // 查到他的一级部门
        List<HrmsEntDeptDO> hrmsEntDeptList = hrmsEntDeptDao.listByDeptIdListAndBizArea(Lists.newArrayList(dept.getId()), null);
        if (CollectionUtils.isEmpty(hrmsEntDeptList)) {
            return;
        }
        List<Long> userIdList = hrmsEntDeptList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getLeaderCode())).filter(o -> o.getLevel().equals(BusinessConstant.ONE)).map(o -> o.getLeaderCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        List<HrmsUserInfoDO> userInfoList = hrmsUserInfoManage.selectUserInfoByIds(userIdList);
        userInfoList.stream().map(HrmsUserInfoDO::getUserCode).filter(StringUtils::isNotBlank).forEach(userCodeList::add);
    }
}
