package com.imile.hrms.service.achievement.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
@AllArgsConstructor
public class AchievementsEmployeeAppraisalEntrustCheckResultDTO {

    /**
     * 校验结果（true:校验通过 false:校验不通过）
     */
    private Boolean result;

    /**
     * 结果信息
     */
    private String message;

    public static AchievementsEmployeeAppraisalEntrustCheckResultDTO of(Boolean result, String message) {
        return new AchievementsEmployeeAppraisalEntrustCheckResultDTO(result, message);
    }
}
