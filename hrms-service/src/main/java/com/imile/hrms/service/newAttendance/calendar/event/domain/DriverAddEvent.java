package com.imile.hrms.service.newAttendance.calendar.event.domain;

import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR> chen
 * @Date 2025/2/13
 * @Description
 */
public class DriverAddEvent extends ApplicationEvent {

    private final HrmsUserInfoDO user;

    public HrmsUserInfoDO getData() {
        return user;
    }

    public DriverAddEvent(Object source, HrmsUserInfoDO user) {
        super(source);
        this.user = user;
    }
}
