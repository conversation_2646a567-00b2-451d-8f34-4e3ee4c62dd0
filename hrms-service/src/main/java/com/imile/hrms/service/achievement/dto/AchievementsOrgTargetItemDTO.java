package com.imile.hrms.service.achievement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 组织目标明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class AchievementsOrgTargetItemDTO{

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 指标库id
     */
    private Long indicatorLibraryId;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标名称
     */
    private String targetNameEn;

    /**
     * 指标分类
     */
    private String targetType;

    /**
     * 目标描述
     */
    private String itemDescription;

    /**
     * 完成时间
     */
    private String completionTime;

    /**
     * kpi分类
     */
    private String kpiType;

    /**
     * kpi分类描述
     */
    private String kpiTypeDesc;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 部门负责人
     */
    private String leaderName;


    /**
     * 计量单位
     */
    private String unit;

    /**
     * 可见性
     */
    private String visibility;

    /**
     * 对齐目标
     */
    private String parentTargetId;

    /**
     * 对齐目标名称
     */
    private String parentTargetName;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 基线值
     */
    private BigDecimal baseValue;

    /**
     * 目标值
     */
    private BigDecimal targetValue;

    /**
     * 完成值
     */
    private BigDecimal completionValue;


    /**
     * 挑战值
     */
    private BigDecimal challengeValue;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 子节点数量
     */
    private Integer childCount;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 数据来源部门
     */
    private String dataSourceDept;

    /**
     * 牵头责任部门
     */
    private String leadDutyDept;

    /**
     * 目标值同比增长率
     */
    private String targetValueYoyRate;

    /**
     * 衡量标准
     */
    private String measurementStandard;

    private Integer isLatest;


    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String month;

    /**
     * 备注
     */
    private String remark;


    /**
     * 完成日期
     */
    private String comDate;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 上一次完成率
     */
    private String lastCompletionRate;

    /**
     * 上一次创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCreateDate;

    /**
     * 上一次完成值
     */
    private BigDecimal lastCompletionValue;

    /**
     * 组织名称
     */
    private String deptName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    /**
     * 统计说明
     */
    private String statisticalDescription;

    /**
     * 指标库编码
     */
    private String indicatorLibraryCode;

    /**
     * 周期数
     */
    private Integer cycleNum;

    /**
     * 完成值来源(1prism 0手动 2运营)
     */
    private String isComplet;
}
