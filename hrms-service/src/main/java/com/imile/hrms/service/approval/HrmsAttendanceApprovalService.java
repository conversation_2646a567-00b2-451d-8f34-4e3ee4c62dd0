package com.imile.hrms.service.approval;

import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.manage.approval.bo.HrmsApplicationFormDetailBO;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.approval.dto.DayDurationInfoDTO;
import com.imile.hrms.service.approval.dto.UserAuthDTO;
import com.imile.hrms.service.approval.dto.UserLeaveImportParam;
import com.imile.hrms.service.approval.param.AddDurationParam;
import com.imile.hrms.service.approval.param.AttendanceApprovalInfoParam;
import com.imile.hrms.service.approval.param.DurationDetailParam;
import com.imile.hrms.service.approval.param.LeaveAddParam;
import com.imile.hrms.service.approval.param.LeaveRevokeAddParam;
import com.imile.hrms.service.approval.param.OutOfOfficeAddParam;
import com.imile.hrms.service.approval.param.OutOfOfficeRevokeAddParam;
import com.imile.hrms.service.approval.param.ReissueCardAddParam;
import com.imile.hrms.service.approval.param.ReissueCardRevokeAddParam;
import com.imile.hrms.service.approval.param.UserAuthParam;
import com.imile.hrms.service.approval.param.UserDayReissueInfoParam;
import com.imile.hrms.service.approval.vo.ApprovalResultVO;
import com.imile.hrms.service.approval.vo.AttendanceApplicationFromDetailVO;
import com.imile.hrms.service.approval.vo.AttendanceApprovalInfoVO;
import com.imile.hrms.service.approval.vo.DurationDetailVO;
import com.imile.hrms.service.approval.vo.LeaveFormInfoExportVO;
import com.imile.hrms.service.approval.vo.UserAuthVO;
import com.imile.hrms.service.approval.vo.UserDayReissueInfoVO;
import com.imile.hrms.service.approval.vo.UserLeaveResidualVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface HrmsAttendanceApprovalService {

    /**
     * 获取用户假期余额信息
     */
    List<UserLeaveResidualVO> selectUserLeaveResidual(Long userId);

    /**
     * 列表查询
     */
    PaginationResult<AttendanceApprovalInfoVO> list(AttendanceApprovalInfoParam param);

    /**
     * 登录人权限获取
     */
    List<UserAuthVO> userAuthList(UserAuthParam param);

    /**
     * 登录人部门权限获取
     */
    UserAuthDTO userDeptAuthList(UserAuthParam param);

    /**
     * 请假/外勤获取冲突单据和具体时长计算信息
     */
    DurationDetailVO durationDetail(DurationDetailParam param);

    /**
     * 获取用户指定日期的可补卡异常及当天排班信息
     */
    UserDayReissueInfoVO getUserDayReissueInfo(UserDayReissueInfoParam param);

    /**
     * 请假申请(点击新增按钮调用 新增/暂存)
     */
    ApprovalResultVO leaveAdd(LeaveAddParam param);

    /**
     * 请假申请(暂存更新/重提交)
     */
    ApprovalResultVO leaveUpdate(LeaveAddParam param);

    /**
     * 外勤申请(点击新增按钮调用 新增/暂存)
     */
    ApprovalResultVO outOfOfficeAdd(OutOfOfficeAddParam param);

    /**
     * 外勤申请(暂存后更新/驳回后更新)
     */
    ApprovalResultVO outOfOfficeUpdate(OutOfOfficeAddParam param);

    /**
     * 补卡申请(点击新增按钮调用 新增/暂存)
     */
    ApprovalResultVO reissueCardAdd(ReissueCardAddParam param);

    /**
     * 补时长申请(点击新增按钮调用)
     */
    ApprovalResultVO addDuration(AddDurationParam param);

    /**
     * 补卡申请(暂存后更新/驳回后更新)
     */
    ApprovalResultVO reissueCardUpdate(ReissueCardAddParam param);

    /**
     * 请假-撤销申请
     */
    ApprovalResultVO leaveRevokeAdd(LeaveRevokeAddParam param);

    /**
     * 外勤-撤销申请
     */
    ApprovalResultVO outOfOfficeRevokeAdd(OutOfOfficeRevokeAddParam param);

    /**
     * 补卡-撤销申请
     */
    ApprovalResultVO reissueCardRevokeAdd(ReissueCardRevokeAddParam param);

    /**
     * 取消单据
     */
    void cancel(Long formId);

    /**
     * 详情
     */
    AttendanceApplicationFromDetailVO getFromDetail(Long formId);

    /**
     * 删除
     */
    void delete(Long formId);

    /**
     * 请假申请预览
     */
    List<ApprovalDetailStepRecordDTO> leavePreview(LeaveAddParam param);

    /**
     * 外勤申请预览
     */
    List<ApprovalDetailStepRecordDTO> outOfOfficePreview(OutOfOfficeAddParam param);

    /**
     * 补卡申请预览
     */
    List<ApprovalDetailStepRecordDTO> reissueCardPreview(ReissueCardAddParam param);

    /**
     * 补时长申请预览
     */
    List<ApprovalDetailStepRecordDTO> addDurationPreview(AddDurationParam param);

    /**
     * 请假-撤销申请预览
     */
    List<ApprovalDetailStepRecordDTO> leaveRevokePreview(LeaveRevokeAddParam param);

    /**
     * 外勤-撤销申请预览
     */
    List<ApprovalDetailStepRecordDTO> outOfOfficeRevokePreview(OutOfOfficeRevokeAddParam param);

    /**
     * 补卡-撤销申请预览
     */
    List<ApprovalDetailStepRecordDTO> reissueCardRevokePreview(ReissueCardRevokeAddParam param);

    /**
     * 构建预览实体
     */
    void previewDTOBuildContainsErrors(List<ApprovalEmptyRecordApiDTO> recordApiDTOList, List<ApprovalDetailStepRecordDTO> resultDTOList, String userCode);

    /**
     * 特殊入口:用户假期导入
     */
    List<UserLeaveImportParam> leaveImport(List<UserLeaveImportParam> importList);

    PaginationResult<LeaveFormInfoExportVO> listExport(AttendanceApprovalInfoParam param);

    /**
     * 外勤/请假-每天时长计算明细
     * 需要获取请假开始时间前一天-请假结束时间后一天的所有排班
     */
    void dayDurationInfoHandler0(Long userId, Date startDate, Date endDate, HrmsCompanyLeaveConfigDO companyLeaveConfigDO, List<DayDurationInfoDTO> dayDurationInfoDTOList, BigDecimal dayAttendanceHours);

    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    BigDecimal handlerLeaveTotalTime0(List<DayDurationInfoDTO> dayDurationInfoList);

    /**
     * 查询国家的假期配置
     * @param country 国家
     * @param leaveType 假期类型
     * @return HrmsCompanyLeaveConfigDO 假期配置
     */
    HrmsCompanyLeaveConfigDO getCountryLeaveTypeConfig(String country, String leaveType);

    /**
     * 通过人员编码查询国家的假期配置
     * @param userCodeList 用户编码
     * @param leaveType 假期类型
     * @return HrmsCompanyLeaveConfigDO 假期配置
     */
    HrmsCompanyLeaveConfigDO getUserLeaveTypeConfig(List<String> userCodeList, String leaveType);


    /**
     *
     * @param detailBO 假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
     * @param userLeaveStageDetailInfoList 用来接收-需要修改的假期详情数据列表
     * @param remark 备注
     * @return HrmsUserLeaveRecordDO
     */
    HrmsUserLeaveRecordDO handlerUserLeaveStageDetailList0(HrmsApplicationFormDetailBO detailBO, List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList, String remark);
}

