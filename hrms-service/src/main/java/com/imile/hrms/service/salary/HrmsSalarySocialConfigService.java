package com.imile.hrms.service.salary;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.common.ConfigNoStatusSwitchParamDTO;
import com.imile.hrms.dao.salary.dto.SalarySocialConfigDTO;
import com.imile.hrms.dao.salary.dto.AbstractSalaryConfigDetailDTO;
import com.imile.hrms.dao.salary.param.SocialConfigAddAndUpdateParam;
import com.imile.hrms.dao.salary.dto.ConfigSelectDTO;
import com.imile.hrms.dao.salary.param.DefaultSwitchParam;
import com.imile.hrms.dao.salary.param.DetailParam;
import com.imile.hrms.dao.salary.query.ConfigQuery;

import java.util.List;


/**
 * 社保service
 * <AUTHOR>
 */
public interface HrmsSalarySocialConfigService  {
    /**
     * 列表查询
     * @param query
     * @return
     */
    PaginationResult<SalarySocialConfigDTO> list(ConfigQuery query);

    /**
     * 根据configNo查询该社保的具体信息
     * @param param
     * @return
     */
    AbstractSalaryConfigDetailDTO detail(DetailParam param);

    /**
     * 社保增加
     * @param param
     */
    void addSocialConfig(SocialConfigAddAndUpdateParam param);

    /**
     * 社保编辑
     * @param param
     */
    void updateSocialConfig(SocialConfigAddAndUpdateParam param);

    /**
     * 状态切换
     * @param switchParam
     */
    void statusSwitch(ConfigNoStatusSwitchParamDTO switchParam);

    /**
     * 默认方案设置
     * @param param
     */
    void switchDefault(DefaultSwitchParam param);

    /**
     * 社保下拉列表
     * @return
     */
    List<ConfigSelectDTO> selectList(String country,String status);


}
