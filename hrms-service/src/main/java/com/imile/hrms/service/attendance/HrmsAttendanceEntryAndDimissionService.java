package com.imile.hrms.service.attendance;

/**
 * 员工入离职确认 人员考勤处理
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
public interface HrmsAttendanceEntryAndDimissionService {

    /**
     * 员工确认入职考勤处理
     * 生成因HR延迟确认入职的考勤结果
     * 无排班生成无排班计划异常
     * 有排班且工作日生成上下班缺卡异常
     *
     * @param userId 员工ID
     */
    void entryAttendanceHandler(Long userId);

    /**
     * 员工确认离职考勤处理
     * 1、清理用户考勤组适用范围
     * 2、清理用户日历适用范围
     * 3、清理员工排班计划&循环排班配置
     * 4、清理因HR延迟确认离职后产生的考勤异常数据
     *
     * @param userId 员工ID
     */
    void dimissionAttendanceHandler(Long userId);
}
