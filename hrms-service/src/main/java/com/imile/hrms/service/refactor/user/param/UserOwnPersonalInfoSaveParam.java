package com.imile.hrms.service.refactor.user.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.genesis.api.model.component.Phone;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
public class UserOwnPersonalInfoSaveParam {

    /**
     * 免冠照
     */
    private String userAvatar;

    /**
     * 国籍编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String nationalityCode;

    /**
     * 国籍名称
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String nationalityName;

    /**
     * 人员性别(1:男 2:女)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer userSex;

    /**
     * 出生日期
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date userBirthday;

    /**
     * 联系电话
     */
    @Valid
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Phone contactPhone;

    /**
     * 个人邮箱
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String personalEmail;

    /**
     * 现居住地址
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String postAddress;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 人员个人扩展信息
     *
     * @see com.imile.hrms.service.refactor.user.component.UserPersonalExtendInfo
     */
    @Valid
    private Object personalExtendInfo;
}
