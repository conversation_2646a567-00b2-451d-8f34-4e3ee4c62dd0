package com.imile.hrms.service.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class FreelancerDriverDeliveryPreferenceSaveParam {

    /**
     * 人员编码（仅单独保存派送偏好时使用）
     */
    private String userCode;

    /**
     * 国家（仅单独保存派送偏好时使用，取税收国）
     */
    private String country;

    /**
     * 派送日期列表
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<String> deliveryDateList;

    /**
     * 派送时间段列表
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<String> deliveryTimePeriodList;

    /**
     * 每天工作时长
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer dailyWorkingHour;

    /**
     * 邮编
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String postcode;

    /**
     * 所属网点编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String ocCode;

    /**
     * 所属供应商编码（即DTL）
     */
    private String vendorCode;
}
