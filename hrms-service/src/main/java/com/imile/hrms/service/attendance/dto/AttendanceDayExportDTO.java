package com.imile.hrms.service.attendance.dto;

import lombok.Data;
import org.checkerframework.checker.units.qual.min;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-2-28
 * @version: 1.0
 */
@Data
public class AttendanceDayExportDTO {

    /**
     * 序号
     */
    private String sr;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 部门组织编码
     */
    private String organizationCode;

    /**
     * 部门
     */
    private String department;

    /**
     * 岗位
     */
    private String designation;

    /**
     * 入职时间
     */
    private Date joiningDate;

    /**
     * 员工类型
     */
    private String character;

    /**
     * 员工状态
     */
    private String workStatus;

    /**
     * 状态
     */
    private String status;

    /**
     * 停用时间
     */
    private Date disabledDate;

    /**
     * 日期
     */
    private Long dayId;

    /**
     * 出勤类型
     */
    private String attendanceType;

    /**
     * 考勤状态
     */
    private String attendanceStatus;

    /**
     * 打卡规则名称
     */
    private String punchRuleName;

    /**
     * 打卡规则类型
     */
    private String punchRuleType;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 应出勤时间
     */
    private BigDecimal attendanceHours;

    /**
     * 实际出勤(小时)
     */
    private BigDecimal presentHours;

    /**
     * 缺卡
     */
    private Integer lackNumber;

    /**
     * 加班(小时)
     */
    private BigDecimal overtimeHours;

    /**
     * 上班时间&下班时间
     */
    private String workTimeOffTimeType1;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTimeType1;

    /**
     * 可打卡时间
     */
    private String clockInTimeType1;

    /**
     * 打卡记录
     */
    private String punchRecordType1;

    /**
     * 异常类型
     */
    private String abnormalTypeType1;

    /**
     * 迟到(分钟)
     */
    private BigDecimal lateMinType1;

    /**
     * 早退(分钟)
     */
    private BigDecimal earlyMinType1;

    /**
     * 旷工(小时)
     */
    // private BigDecimal absentHoursType1;


    /**
     * 上班时间&下班时间
     */
    private String workTimeOffTimeType2;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTimeType2;

    /**
     * 可打卡时间
     */
    private String clockInTimeType2;

    /**
     * 打卡记录
     */
    private String punchRecordType2;

    /**
     * 异常类型
     */
    private String abnormalTypeType2;

    /**
     * 迟到(分钟)
     */
    private BigDecimal lateMinType2;

    /**
     * 早退(分钟)
     */
    private BigDecimal earlyMinType2;

    /**
     * 旷工(小时)
     */
    //private BigDecimal absentHoursType2;


    /**
     * 上班时间&下班时间
     */
    private String workTimeOffTimeType3;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTimeType3;

    /**
     * 可打卡时间
     */
    private String clockInTimeType3;

    /**
     * 打卡记录
     */
    private String punchRecordType3;

    /**
     * 异常类型
     */
    private String abnormalTypeType3;

    /**
     * 迟到(分钟)
     */
    private BigDecimal lateMinType3;

    /**
     * 早退(分钟)
     */
    private BigDecimal earlyMinType3;

    /**
     * 旷工(小时)
     */
    //private BigDecimal absentHoursType3;


    /*    *//**
     * 上班时间&下班时间
     *//*
    private String workTimeOffTimeType4;

    *//**
     * 弹性时间
     *//*
    private BigDecimal elasticTimeType4;

    *//**
     * 可打卡时间
     *//*
    private String clockInTimeType4;

    *//**
     * 打卡记录
     *//*
    private String punchRecordType4;

    *//**
     * 异常类型
     *//*
    private String abnormalTypeType4;

    *//**
     * 迟到(分钟)
     *//*
    private BigDecimal lateMinType4;

    *//**
     * 早退(分钟)
     *//*
    private BigDecimal earlyMinType4;

    *//**
     * 旷工(小时)
     *//*
    private BigDecimal absentHoursType4;*/
}
