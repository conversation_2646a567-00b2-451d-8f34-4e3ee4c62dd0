package com.imile.hrms.service.newAttendance.punchConfig.factory;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigAddOrUpdateCommand;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchConfigRangeDTO;
import com.imile.hrms.service.newAttendance.punchConfig.helper.PunchConfigHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 默认打卡方式
 *
 * <AUTHOR> chen
 * @Date 2025/2/17
 * @Description
 */
@Slf4j
@Component
public class DefaultPunchConfigStrategy implements PunchConfigStrategy {

    @Resource
    private PunchConfigHelper punchConfigHelper;

    @Override
    public List<PunchConfigRangeDTO> apply(PunchConfigAddOrUpdateCommand addCommand) {
        log.info("Using DefaultPunchConfigStrategy {}", JSON.toJSONString(addCommand));
        punchConfigHelper.addDefaultPunchConfig(addCommand);
        return Collections.emptyList();
    }
}
