package com.imile.hrms.service.recruitment.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.common.enums.recruitment.OfferApproveQueryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfferApproveListParam {

    /**
     * 部门id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long deptId;

    /**
     * 是否只查未关联员工的审批单；true=是；false=否
     */
    private Boolean isNotRelateUser;
}
