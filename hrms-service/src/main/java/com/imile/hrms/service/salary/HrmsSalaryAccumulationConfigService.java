package com.imile.hrms.service.salary;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.common.ConfigNoStatusSwitchParamDTO;
import com.imile.hrms.dao.salary.dto.*;
import com.imile.hrms.dao.salary.param.DefaultSwitchParam;
import com.imile.hrms.dao.salary.param.SalaryAccumulationParam;
import com.imile.hrms.dao.salary.query.ConfigQuery;

import java.util.List;

/**
 * 公积金接口
 * <AUTHOR>
 */
public interface HrmsSalaryAccumulationConfigService {
    /**
     * 列表查询
     * @param query
     * @return
     */
    PaginationResult<SalaryAccumulationConfigDTO> list(ConfigQuery query);

    /**
     * 根据configNo查询该公积金的具体信息
     * @param configNo
     * @return
     */
    SalaryAccumulationConfigDetailDTO detail(String configNo);

    /**
     * 公积金增加
     * @param param
     */
    void addConfig(SalaryAccumulationParam param);

    /**
     * 公积金编辑
     * @param param
     */
    void updateConfig(SalaryAccumulationParam param);

    /**
     * 状态切换
     * @param switchParam
     */
    void statusSwitch(ConfigNoStatusSwitchParamDTO switchParam);

    /**
     * 默认方案设置
     * @param param
     */
    void switchDefault(DefaultSwitchParam param);

    /**
     * 社保下拉列表
     * @return
     */
    List<ConfigSelectDTO> selectList(String country,String status);
}
