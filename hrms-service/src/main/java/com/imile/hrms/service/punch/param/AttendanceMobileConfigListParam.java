package com.imile.hrms.service.punch.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * {@code @author:} allen
 * {@code @className:} AttendanceMobileConfigParam
 * {@code @since:} 2024-05-25 10:34
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description="考勤手机列表查询入参")
public class AttendanceMobileConfigListParam extends ResourceQuery {
    /**
     * 用户账号或名称
     */
    private String userCodeOrName;

    /**
     * 国家
     */
    private String country;
}
