package com.imile.hrms.service.newAttendance.calendar.event.domain;

import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR> chen
 * @Date 2025/2/12
 * @Description
 */
public class EmployeeDeptModifyEvent extends ApplicationEvent {

    private final ChangeUserInfo changeUserInfo;

    public ChangeUserInfo getData() {
        return changeUserInfo;
    }

    public EmployeeDeptModifyEvent(Object source, ChangeUserInfo changeUserInfo) {
        super(source);
        this.changeUserInfo = changeUserInfo;
    }

    public EmployeeDeptModifyEvent(Object source, HrmsUserInfoDO userInfoDO, HrmsUserInfoDO oldUserInfoRecord) {
        super(source);
        this.changeUserInfo = ChangeUserInfo.of(userInfoDO, oldUserInfoRecord);
    }
}
