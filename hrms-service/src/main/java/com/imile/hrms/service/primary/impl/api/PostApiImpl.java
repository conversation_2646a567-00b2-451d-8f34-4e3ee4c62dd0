package com.imile.hrms.service.primary.impl.api;

import com.imile.hrms.api.primary.model.param.base.PostConditionParam;
import com.imile.hrms.api.primary.model.result.base.PostDTO;
import com.imile.hrms.api.primary.service.PostApi;
import com.imile.hrms.service.primary.PostService;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service(version = "1.0.0")
public class PostApiImpl implements PostApi {

    @Resource
    private PostService postService;

    @Override
    public RpcResult<PostDTO> getPostById(Long id) {
        return RpcResult.ok(postService.getPostById(id));
    }

    @Override
    public RpcResult<List<PostDTO>> listPostByCondition(PostConditionParam param) {
        return RpcResult.ok(postService.listPostByCondition(param));
    }
}
