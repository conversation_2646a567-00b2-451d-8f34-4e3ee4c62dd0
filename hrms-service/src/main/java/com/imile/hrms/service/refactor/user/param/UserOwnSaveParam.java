package com.imile.hrms.service.refactor.user.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.hrms.service.user.param.UserContractSaveParam;
import com.imile.hrms.service.user.param.UserDependentSaveParam;
import com.imile.hrms.service.user.param.UserEducationSaveParam;
import com.imile.hrms.service.user.param.UserEmergencyContactSaveParam;
import com.imile.hrms.service.user.param.UserLanguageAbilitySaveParam;
import com.imile.hrms.service.user.param.UserQualificationSaveParam;
import com.imile.hrms.service.user.param.UserVisaSaveParam;
import com.imile.hrms.service.user.param.UserWagesCardSaveParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserOwnSaveParam {

    /**
     * 人员编码
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String userCode;

    /**
     * 人员工作信息
     */
    @Valid
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private UserOwnWorkInfoSaveParam userWorkInfo;

    /**
     * 人员合同列表
     */
    @Valid
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    @Size(max = 4, message = ValidCodeConstant.MAX)
    private List<UserContractSaveParam> userContractList;

    /**
     * 人员个人信息
     */
    @Valid
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private UserOwnPersonalInfoSaveParam userPersonalInfo;

    /**
     * 人员紧急联系人列表
     */
    @Valid
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    @Size(max = 2, message = ValidCodeConstant.MAX)
    private List<UserEmergencyContactSaveParam> userEmergencyContactList;

    /**
     * 人员证件列表
     */
    @Valid
    private List<UserCertificateSaveParam> userCertificateList;

    /**
     * 人员依赖人列表
     */
    @Valid
    @Size(max = 10, message = ValidCodeConstant.MAX)
    private List<UserDependentSaveParam> userDependentList;

    /**
     * 人员签证列表
     */
    @Valid
    @Size(max = 10, message = ValidCodeConstant.MAX)
    private List<UserVisaSaveParam> userVisaList;

    /**
     * 人员工资卡列表
     */
    @Valid
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    @Size(max = 10, message = ValidCodeConstant.MAX)
    private List<UserWagesCardSaveParam> userWagesCardList;

    /**
     * 人员教育经历列表
     */
    @Valid
    @Size(max = 2, message = ValidCodeConstant.MAX)
    private List<UserEducationSaveParam> userEducationList;

    /**
     * 人员语言能力列表
     */
    @Valid
    @Size(max = 10, message = ValidCodeConstant.MAX)
    private List<UserLanguageAbilitySaveParam> userLanguageAbilityList;

    /**
     * 人员资格证书列表
     */
    @Valid
    @Size(max = 10, message = ValidCodeConstant.MAX)
    private List<UserQualificationSaveParam> userQualificationList;
}
