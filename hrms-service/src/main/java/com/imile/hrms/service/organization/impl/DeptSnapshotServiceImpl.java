package com.imile.hrms.service.organization.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.easylog.annotation.EasyLog;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.base.OperationSceneEnum;
import com.imile.hrms.common.enums.organization.DeptStatusEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.common.util.SplitUtil;
import com.imile.hrms.common.util.tree.TreeUtils;
import com.imile.hrms.dao.organization.dao.HrmsDeptSnapshotDao;
import com.imile.hrms.dao.organization.dto.DeptSnapshotTreeDTO;
import com.imile.hrms.dao.organization.dto.EntDeptDTO;
import com.imile.hrms.dao.organization.enums.DeptOrgTypeEnum;
import com.imile.hrms.dao.organization.model.HrmsDeptSnapshotDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsOrgBusinessConfigPlusDO;
import com.imile.hrms.dao.organization.query.DeptSnapshotQuery;
import com.imile.hrms.dao.organization.query.EntDeptQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.organization.DeptSnapshotService;
import com.imile.hrms.service.organization.vo.DeptSnapshotDetailVO;
import com.imile.hrms.service.organization.vo.HrmsEntDeptExportVO;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
@Service
@Slf4j
public class DeptSnapshotServiceImpl extends BaseService implements DeptSnapshotService {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsProperties hrmsProperties;
    @Resource
    private HrmsDeptSnapshotDao hrmsDeptSnapshotDao;
    @Resource
    private EntOcService entOcService;
    @Resource
    private UserResourceService userResourceService;
    /**
     * 目标看板部门树白名单
     */
    @Value("#{'${achievement.tree.viewable.user.code.list:2101180,2101947,2105461,2102255,2102354,2102405,2103051,2103671,2104959,2105554}'.split(',')}")
    private List<String> achievementTreeViewableUserCodeList;

    /**
     * 部门树根节点id
     */
    @Value("${dept.root.id:1032652}")
    private Long rootDeptId;

    @Override
    public List<DeptSnapshotTreeDTO> getDeptTree(DeptSnapshotQuery query) {
        List<HrmsDeptSnapshotDO> deptList = getParentDeptFromIdList(query);
        //排除测试部门
        deptList = excludeTestDept(deptList);
        return CollectionUtils.isEmpty(deptList) ? Lists.newArrayList() : getEntDeptTree(deptList,null);
    }

    @Override
    @EasyLog(module = OperationSceneEnum.Fields.HRMS_DEPT_MANAGE, type = OperationCodeEnum.Fields.HRMS_DEPT_EXPORT,
            successParamList = {"{{#query.snapshotVersion}}"})
    public List<HrmsEntDeptExportVO> getDeptExportList(DeptSnapshotQuery query) {
        // 由于导出时要求排序同组织树一致 这里获取组织树转换为列表作为最终拼装结果的排序基准
        List<HrmsDeptSnapshotDO> deptList = getParentDeptFromIdList(query);
        //排除测试部门
        deptList = excludeTestDept(deptList);
        List<DeptSnapshotTreeDTO> deptTreeList = CollectionUtils.isEmpty(deptList) ? Lists.newArrayList() : getEntDeptTree(deptList,null);
        if (deptTreeList.isEmpty()) {
            return Collections.emptyList();
        }
        DeptSnapshotTreeDTO rootDept = deptTreeList.get(0);
        List<DeptSnapshotTreeDTO> treeDeptList = treeToList(rootDept);
        // 准备最终拼装结果所需的依赖数据
        boolean isChinese = RequestInfoHolder.isChinese();
        // deptId -> dept
        Map<Long, HrmsDeptSnapshotDO> filterDeptMap = deptList.stream().collect(Collectors.toMap(HrmsDeptSnapshotDO::getDeptId, Function.identity()));
        // deptId -> deptName
        Map<Long, String> deptNameMap = deptList.stream().collect(Collectors.toMap(HrmsDeptSnapshotDO::getDeptId, s -> getDeptName(s, isChinese)));
        return treeDeptList.stream()
                // 作为连接节点的无权限部门不需要返回
                .map(dept -> {
                    HrmsDeptSnapshotDO snapshotDO = filterDeptMap.get(dept.getId());
                    HrmsEntDeptExportVO item = BeanUtils.convert(snapshotDO, HrmsEntDeptExportVO.class);
                    item.setRecentActiveTime(snapshotDO.getEnabledDate());
                    item.setRecentDisabledTime(snapshotDO.getDisabledDate());
                    item.setParentName(deptNameMap.getOrDefault(snapshotDO.getParentId(), ""));
                    // 1-7级部门
                    if (StringUtils.isNotBlank(snapshotDO.getDeptPath())) {
                        List<String> deptPathList = Arrays.stream(snapshotDO.getDeptPath().split(HrmsStringUtil.COMMA)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(deptPathList)) {
                            item.setFirstDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 1, deptPathList, filterDeptMap));
                            item.setSecondDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 2, deptPathList, filterDeptMap));
                            item.setThirdDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 3, deptPathList, filterDeptMap));
                            item.setFourDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 4, deptPathList, filterDeptMap));
                            item.setFiveDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 5, deptPathList, filterDeptMap));
                            item.setSixDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 6, deptPathList, filterDeptMap));
                            item.setSevenDeptName(getDesignatedLevelDeptName(deptPathList.size() - 1, 7, deptPathList, filterDeptMap));
                        }
                    }
                    // 拼装业务负责人
                    if (StringUtils.isNotBlank(snapshotDO.getDeptBizLeaderConfig())) {
                        List<HrmsOrgBusinessConfigPlusDO> deptBizOwnerConfigList
                                = JSONObject.parseArray(snapshotDO.getDeptBizLeaderConfig(), HrmsOrgBusinessConfigPlusDO.class);
                        String bizOwnerDesc = deptBizOwnerConfigList.stream()
                                .map(config -> this.getBizOwnerDesc(config, isChinese))
                                .collect(Collectors.joining("\r\n"));
                        item.setBizOwnerDesc(bizOwnerDesc);
                    }
                    item.setParentName(deptNameMap.getOrDefault(snapshotDO.getParentId(), ""));
                    item.setDeptLevel(snapshotDO.getDeptLevel());
                    item.setStatusDesc(DeptStatusEnum.getDescByCode(snapshotDO.getStatus(), isChinese));
                    item.setLeaderUserCode(snapshotDO.getLeaderCode());
                    item.setLeaderUserName(snapshotDO.getLeaderName());
                    item.setBizAreaDesc(isChinese ? snapshotDO.getBizAreaNameCn() : snapshotDO.getBizAreaNameEn());
                    item.setBizModelNames(isChinese ? snapshotDO.getBizModelNamesCn() : snapshotDO.getBizModelNamesEn());
                    item.setOcType(snapshotDO.getOcType());
                    item.setOcCode(snapshotDO.getOcCode());
                    return item;
                })
                .collect(Collectors.toList());
    }

    private String getDesignatedLevelDeptName(Integer deptLevel, Integer designatedLevel, List<String> deptPathList, Map<Long, HrmsDeptSnapshotDO> filterDeptMap) {
        if (deptLevel >= designatedLevel && deptPathList.size() > designatedLevel) {
            HrmsDeptSnapshotDO dept = filterDeptMap.get(Long.parseLong(deptPathList.get(designatedLevel)));
            if (dept != null) {
                return BusinessFieldUtils.getUnifiedDeptName(dept.getDeptNameCn(), dept.getDeptNameEn());
            }
        }
        return null;
    }

    private List<HrmsDeptSnapshotDO> excludeTestDept(List<HrmsDeptSnapshotDO> deptList) {
        Long userId = RequestInfoHolder.getUserId();
        // 兼容定时任务场景无操作人信息
        if (Objects.isNull(userId)) {
            return deptList;
        }
        HrmsUserInfoDO user = hrmsUserInfoDao.getById(userId);
        String excludeTestCountry = hrmsProperties.getEntry().getExcludeTestCountry();
        if (!StringUtils.equalsIgnoreCase(excludeTestCountry, user.getOriginCountry())) {
            deptList = deptList.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getBizCountry()) && !SplitUtil.splitNew(e.getBizCountry(), HrmsStringUtil.COMMA).contains(excludeTestCountry))
                    .collect(Collectors.toList());
        }
        return deptList;
    }

    @Override
    public DeptSnapshotDetailVO getDeptDetail(Long id) {
        HrmsDeptSnapshotDO deptDO = hrmsDeptSnapshotDao.getById(id);
        if (deptDO == null || IsDeleteEnum.YES.getCode().equals(deptDO.getIsDelete())) {
            return null;
        }
        return convertDo2Vo(deptDO);
    }

    @Override
    public List<DeptSnapshotTreeDTO> getPermissionDeptTree(DeptSnapshotQuery query) {
        // 1. 判断isClover 2.true: 获取主管权限以及判断是否在白名单获取数据权限 3.false: 获取数据权限
        Long userId = RequestInfoHolder.getUserId();
        String curUserCode = RequestInfoHolder.getUserCode();

        if (Objects.isNull(userId) || StringUtils.isBlank(curUserCode)) {
            return Collections.emptyList();
        }

        PermissionDeptVO permissionDeptVO = getPermissionDeptVO(userId);

        if (query.getIsClover()) {
            // 获取主管权限
            List<HrmsDeptSnapshotDO> leaderDeptList = hrmsDeptSnapshotDao.getBaseMapper().selectList(new LambdaQueryWrapper<HrmsDeptSnapshotDO>()
                    .eq(HrmsDeptSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode())
                    .eq(HrmsDeptSnapshotDO::getSnapshotVersion, query.getSnapshotVersion())
                    .eq(HrmsDeptSnapshotDO::getStatus, DeptStatusEnum.ACTIVE.getCode())
                    .eq(HrmsDeptSnapshotDO::getLeaderCode, userId)
            );
            List<Long> leaderDeptIdList = leaderDeptList.stream().map(HrmsDeptSnapshotDO::getDeptId).collect(Collectors.toList());
            query.setParentIdList(leaderDeptIdList);
            List<HrmsDeptSnapshotDO> subDeptList = hrmsDeptSnapshotDao.getSubDeptFromParentIdList(query);
            List<Long> subDeptIdList = subDeptList.stream().map(HrmsDeptSnapshotDO::getDeptId).collect(Collectors.toList());
            leaderDeptIdList.addAll(subDeptIdList);

            // 判断是否在白名单获取数据权限
            if (achievementTreeViewableUserCodeList.contains(curUserCode)) {
                //HR权限控制
                if (!permissionDeptVO.getIsSysAdmin()) {
                    leaderDeptIdList.addAll(permissionDeptVO.getDeptIdList());
                }
            } else {
                query.setDeptIdList(leaderDeptIdList);
            }
            permissionDeptVO.setDeptIdList(leaderDeptIdList);
        } else {
            // 获取数据权限
            query.setDeptIdList(permissionDeptVO.getDeptIdList());
        }

        if (!permissionDeptVO.getIsSysAdmin() && CollectionUtils.isEmpty(permissionDeptVO.getDeptIdList())) {
            return new ArrayList<>();
        }

        List<HrmsDeptSnapshotDO> deptList = getParentDeptFromIdListForAchievement(query);
        //排除测试部门
        deptList = excludeTestDept(deptList);
        return CollectionUtils.isEmpty(deptList) ? Lists.newArrayList() : getEntDeptTree(deptList,permissionDeptVO);
    }

    private List<DeptSnapshotTreeDTO> getEntDeptTree(List<HrmsDeptSnapshotDO> deptList,PermissionDeptVO permissionDeptVO) {
        boolean chinese = RequestInfoHolder.isChinese();
        //组装树
        Function<HrmsDeptSnapshotDO, String> nameGetter = chinese ? HrmsDeptSnapshotDO::getDeptNameCn : HrmsDeptSnapshotDO::getDeptNameEn;
        deptList = sortDeptList(deptList);
        //递归构建树结构
        List<DeptSnapshotTreeDTO> entDeptTreeList = TreeUtils.listToTree(
                deptList,
                nameGetter,
                null,
                HrmsDeptSnapshotDO::getDeptId,
                HrmsDeptSnapshotDO::getParentId,
                DeptSnapshotTreeDTO.class,
                (node, e) -> {
                    String name = chinese && StringUtils.isNotBlank(e.getDeptNameCn()) ? e.getDeptNameCn() : e.getDeptNameEn();
                    node.setSnapshotId(e.getId());
                    node.setNodeName(name);
                    node.setExpand(true);
                    node.setStatus(e.getStatus());
                    node.setOcId(e.getOcId());
                    node.setCountry(e.getCountry());
                    node.setDeptCode(e.getDeptCode());
                    node.setDeptName(name);
                    node.setDeptNameCn(e.getDeptNameCn());
                    node.setDeptNameEn(e.getDeptNameEn());
                    node.setDeptOrgType(Objects.equals(e.getDeptOrgType(), 0) ? null : String.valueOf(e.getDeptOrgType()));
                    node.setDeptOrgTypeDesc(DeptOrgTypeEnum.getInstance(e.getDeptOrgType()).getDesc());
                    node.setBizModelNamesEn(e.getBizModelNamesEn());
                    node.setBizModelNamesCn(e.getBizModelNamesCn());
                    node.setBizAreaList(Lists.newArrayList(e.getBizAreaId().toString()));
                    node.setBizAreaNameEnList(Lists.newArrayList(e.getBizAreaNameCn()));
                    node.setBizAreaNameEnList(Lists.newArrayList(e.getBizAreaNameEn()));
                    node.setLeaderName(e.getLeaderName());
                    node.setLeaderCode(e.getLeaderCode());
                    if (Objects.isNull(permissionDeptVO)) {
                        node.setHasPermission(WhetherEnum.YES.getKey());
                    } else {
                        node.setHasPermission((permissionDeptVO.getIsSysAdmin() || permissionDeptVO.getDeptIdList().contains(e.getDeptId())) ? 1 : 0);
                    }

                    if (Objects.equals(StatusEnum.ACTIVE, StatusEnum.getStatusEnum(e.getStatus()))) {
                        node.setMissingDataNum(queryMissingData(e));
                    }
                    node.setIsOperationDept(DeptOrgTypeEnum.getInstance(e.getDeptOrgType()).getIsStation());
                    node.setBizCountryList(SplitUtil.splitNew(e.getBizCountry(), HrmsStringUtil.COMMA));
                }
        );
        Map<Long, List<DeptSnapshotTreeDTO>> map = entDeptTreeList.stream().collect(Collectors.groupingBy(DeptSnapshotTreeDTO::getId));
        return map.getOrDefault(rootDeptId, entDeptTreeList);
    }

    private List<HrmsDeptSnapshotDO> sortDeptList(List<HrmsDeptSnapshotDO> deptList) {
        Map<Boolean, List<HrmsDeptSnapshotDO>> map = deptList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(dept ->
                        SplitUtil.containsIgnoreCase(dept.getBizCountry(), HrmsStringUtil.COMMA, "HQ")
                                && Objects.equals(dept.getParentId(), rootDeptId)));
        return Stream.of(map.get(Boolean.TRUE), map.get(Boolean.FALSE))
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private Integer queryMissingData(HrmsDeptSnapshotDO dept) {
        Integer count = 0;
        if (Objects.isNull(dept.getParentId())) {
            count++;
        }
        if (Objects.isNull(dept.getDeptOrgType()) || Objects.equals(dept.getDeptOrgType(), DeptOrgTypeEnum.DEFAULT.getValue())) {
            count++;
        }
        if (Strings.isBlank(dept.getDeptNameEn())) {
            count++;
        }
        if (Objects.isNull(dept.getLeaderCode())) {
            count++;
        }
        if (Strings.isBlank(dept.getCity())) {
            count++;
        }
        if (Strings.isBlank(dept.getProvince())) {
            count++;
        }
        if (Strings.isBlank(dept.getCountry())) {
            count++;
        }
        if (dept.getBizAreaId() == null) {
            count++;
        }
        return count;
    }

    private DeptSnapshotDetailVO convertDo2Vo(HrmsDeptSnapshotDO snapshotDO) {
        DeptSnapshotDetailVO detail = new DeptSnapshotDetailVO();
        BeanUtil.copyProperties(snapshotDO, detail);
        detail.setId(snapshotDO.getDeptId());
        detail.setIsOperationDept(DeptOrgTypeEnum.getInstance(snapshotDO.getDeptOrgType()).getIsStation());
        detail.setParentId(snapshotDO.getParentId());
        if (detail.getParentId() != null && detail.getParentId() > 0) {
            LambdaQueryWrapper<HrmsDeptSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HrmsDeptSnapshotDO::getDeptId, detail.getParentId());
            queryWrapper.eq(HrmsDeptSnapshotDO::getSnapshotVersion, snapshotDO.getSnapshotVersion());
            List<HrmsDeptSnapshotDO> parentList = hrmsDeptSnapshotDao.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(parentList)) {
                HrmsDeptSnapshotDO parentDept = parentList.get(0);
                detail.setParentName(RequestInfoHolder.isChinese() ? parentDept.getDeptNameCn() : parentDept.getDeptNameEn());
            }
        }
        detail.setDeptOrgType(Objects.equals(snapshotDO.getDeptOrgType(), 0) ? null : String.valueOf(snapshotDO.getDeptOrgType()));
        detail.setRegion(snapshotDO.getRegion());
        detail.setCity(snapshotDO.getCity());
        detail.setProvince(snapshotDO.getProvince());
        detail.setCountry(snapshotDO.getCountry());
        detail.setAddress(snapshotDO.getAddress());
        if (snapshotDO.getBizAreaId() != null) {
            detail.setBizAreaList(Lists.newArrayList(snapshotDO.getBizAreaId().toString()));
            detail.setBizAreaNameList(Lists.newArrayList(RequestInfoHolder.isChinese() ? snapshotDO.getBizAreaNameCn() : snapshotDO.getBizAreaNameEn()));
        }
        detail.setDeptPosition(snapshotDO.getDeptPosition());
        detail.setDeptDuty(snapshotDO.getDeptDuty());
        if (Strings.isNotBlank(snapshotDO.getBizModelIds())) {
            List<String> bizModelIdList = Arrays.stream(snapshotDO.getBizModelIds().split(HrmsStringUtil.COMMA)).collect(Collectors.toList());
            detail.setBizModelIdList(bizModelIdList);
            detail.setBizModelNamesList(Arrays.stream((RequestInfoHolder.isChinese() ? snapshotDO.getBizModelNamesCn() : snapshotDO.getBizModelNamesEn()).
                    split(HrmsStringUtil.COMMA)).collect(Collectors.toList()));
        }
        if (Strings.isNotBlank(snapshotDO.getBizCountry())) {
            List<String> bizCountryList = Arrays.stream(snapshotDO.getBizCountry().split(HrmsStringUtil.COMMA))
                    .collect(Collectors.toList());
            detail.setBizCountryList(bizCountryList);
        }
        if (StringUtils.isNotBlank(snapshotDO.getDeptBizLeaderConfig())) {
            List<HrmsOrgBusinessConfigPlusDO> deptBizOwnerConfigList
                    = JSONObject.parseArray(snapshotDO.getDeptBizLeaderConfig(), HrmsOrgBusinessConfigPlusDO.class);
            detail.setOrgBusinessConfigVOList(getOrgBusinessConfigs(deptBizOwnerConfigList));
        }

        detail.setIsCostSettlement(DeptOrgTypeEnum.getInstance(snapshotDO.getDeptOrgType()).getCostSettlementValue());
        detail.setIsOrgManagement(DeptOrgTypeEnum.getInstance(snapshotDO.getDeptOrgType()).getOrgManagementValue());
        if (StringUtils.isNotBlank(snapshotDO.getLeaderCode())) {
            detail.setLeaderCode(Long.parseLong(snapshotDO.getLeaderCode()));
            detail.setLeaderNameAndCode(snapshotDO.getLeaderName());
        }
        detail.setActiveTime(snapshotDO.getEnabledDate());
        detail.setDisabledTime(snapshotDO.getDisabledDate());
        if (DeptOrgTypeEnum.STATION.getValue().equals(snapshotDO.getDeptOrgType())) {
            EntOcApiDTO oc = entOcService.getOcById(BusinessConstant.DEFAULT_ORG_ID, snapshotDO.getOcId());
            detail.setOcType(oc.getOcType());
        }
        return detail;
    }

    private List<EntDeptDTO.OrgBusinessConfigVO> getOrgBusinessConfigs(List<HrmsOrgBusinessConfigPlusDO> businessConfigList) {
        Map<Long, List<EntDeptDTO.LeaderInfoVO>> leaderIdListMap = businessConfigList.stream().collect(Collectors.toMap(HrmsOrgBusinessConfigPlusDO::getId, config -> {
            List<Long> leaderIds = splitIds2List(config.getLeaderId());
            List<EntDeptDTO.LeaderInfoVO> result = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(leaderIds)) {
                String leaderUserNameEn = config.getLeaderUserNameEn();
                List<String> leaderUserNameEnList = Strings.isNotBlank(leaderUserNameEn) ? Arrays.stream(leaderUserNameEn.split(HrmsStringUtil.COMMA)).collect(Collectors.toList()) : Lists.newArrayList();
                String leaderUserNameCn = config.getLeaderUserNameCn();
                List<String> leaderUserNameCnList = Strings.isNotBlank(leaderUserNameCn) ? Arrays.stream(leaderUserNameCn.split(HrmsStringUtil.COMMA)).collect(Collectors.toList()) : Lists.newArrayList();
                for (int i = 0; i < leaderIds.size(); i++) {
                    EntDeptDTO.LeaderInfoVO item = new EntDeptDTO.LeaderInfoVO();
                    item.setLeaderId(leaderIds.get(i));
                    item.setLeaderName(RequestInfoHolder.isChinese() ? leaderUserNameCnList.get(i) : leaderUserNameEnList.get(i));
                    result.add(item);
                }
            }
            return result;
        }, (v1, v2) -> v1));

        return businessConfigList.stream().map(config -> {
            EntDeptDTO.OrgBusinessConfigVO vo = new EntDeptDTO.OrgBusinessConfigVO();
            vo.setId(config.getId());
            vo.setLeaderProperty(String.valueOf(config.getLeaderProperty()));
            vo.setLeaderInfoVOList(leaderIdListMap.getOrDefault(config.getId(), Lists.newArrayList()));
            return vo;
        }).collect(Collectors.toList());
    }

    public List<HrmsDeptSnapshotDO> getParentDeptFromIdList(DeptSnapshotQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        Set<Long> isUseDeptId = new HashSet<>();
        List<HrmsDeptSnapshotDO> resList = new ArrayList<>();
        List<HrmsDeptSnapshotDO> parentList;
        List<Long> deptIdList;
        int count = 0;
        do {
            LambdaQueryWrapper<HrmsDeptSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HrmsDeptSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
            queryWrapper.eq(HrmsDeptSnapshotDO::getSnapshotVersion, query.getSnapshotVersion());
            queryWrapper.in(HrmsDeptSnapshotDO::getStatus, Lists.newArrayList(DeptStatusEnum.DISABLED.getCode(), DeptStatusEnum.ACTIVE.getCode()));
            parentList = hrmsDeptSnapshotDao.list(queryWrapper);
            isUseDeptId.addAll(parentList.stream().map(HrmsDeptSnapshotDO::getDeptId).collect(Collectors.toList()));
            resList.addAll(parentList);
            deptIdList = parentList.stream().
                    map(HrmsDeptSnapshotDO::getDeptId).
                    filter(id -> id != null && !Objects.equals(id, 0L) && !isUseDeptId.contains(id)).collect(Collectors.toList());
            count++;
            if (count > 10) {
                log.error("count={} | deptIdList={}", count, deptIdList);
                break;
            }
        } while (!CollectionUtils.isEmpty(parentList) && !CollectionUtils.isEmpty(deptIdList));
        return resList;
    }

    public List<HrmsDeptSnapshotDO> getParentDeptFromIdListForAchievement(DeptSnapshotQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        Set<Long> isUseDeptId = new HashSet<>();
        List<HrmsDeptSnapshotDO> resList = new ArrayList<>();
        List<HrmsDeptSnapshotDO> parentList;
        List<Long> deptIdList = query.getDeptIdList();
        int count = 0;
        do {
            query.setDeptIdList(deptIdList);
            LambdaQueryWrapper<HrmsDeptSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HrmsDeptSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
            queryWrapper.eq(HrmsDeptSnapshotDO::getSnapshotVersion, query.getSnapshotVersion());
            queryWrapper.in(HrmsDeptSnapshotDO::getStatus, Lists.newArrayList(DeptStatusEnum.ACTIVE.getCode()));
            queryWrapper.in(CollectionUtils.isNotEmpty(query.getDeptIdList()),HrmsDeptSnapshotDO::getDeptId, query.getDeptIdList());
            parentList = hrmsDeptSnapshotDao.list(queryWrapper);
            isUseDeptId.addAll(parentList.stream().map(HrmsDeptSnapshotDO::getDeptId).collect(Collectors.toList()));
            resList.addAll(parentList);
            deptIdList = parentList.stream()
                    .map(HrmsDeptSnapshotDO::getParentId)
                    .filter(Objects::nonNull)
                    .filter(id -> !Objects.equals(id, 0L))
                    .filter(id -> !isUseDeptId.contains(id))
                    .collect(Collectors.toList());
            count++;
            if (count > 10) {
                log.error("count={} | deptIdList={}", count, deptIdList);
                break;
            }
        } while (!CollectionUtils.isEmpty(parentList) && !CollectionUtils.isEmpty(deptIdList));
        return resList;
    }

    private String getDeptName(HrmsDeptSnapshotDO dept, boolean isChinese) {
        // 中文环境下 若中文名为空则展示英文名
        String deptNameCn = StringUtils.isBlank(dept.getDeptNameCn()) ? dept.getDeptNameEn() : dept.getDeptNameCn();
        return isChinese ? deptNameCn : dept.getDeptNameEn();
    }

    private List<Long> splitIds2List(String ids) {
        if (StringUtils.isBlank(ids)) {
            return Collections.emptyList();
        }
        return Splitter.on(HrmsStringUtil.COMMA).splitToList(ids).stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    private String getBizOwnerDesc(HrmsOrgBusinessConfigPlusDO config, boolean isChinese) {
        String bizOwnerInfo = splitIds2List(config.getLeaderId()).stream()
                .map(userId -> isChinese ? config.getLeaderUserNameCn() : config.getLeaderUserNameEn())
                .collect(Collectors.joining(HrmsStringUtil.COMMA));
        return config.getLeaderPropertyStr() + "：" + bizOwnerInfo;
    }

    private List<DeptSnapshotTreeDTO> treeToList(DeptSnapshotTreeDTO node) {
        List<DeptSnapshotTreeDTO> list = Lists.newArrayList();
        if (Objects.isNull(node)) {
            return list;
        }
        list.add(node);
        if (CollectionUtils.isEmpty(node.getChildren())) {
            return list;
        }
        for (DeptSnapshotTreeDTO child : node.getChildren()) {
            list.addAll(this.treeToList(child));
        }
        return list;
    }

    private PermissionDeptVO getPermissionDeptVO(Long userId) {
        return userResourceService.getPermissionDept(userId, new ArrayList<>());
    }

}
