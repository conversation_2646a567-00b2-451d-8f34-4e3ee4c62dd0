package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/8 17:05
 * @version: 1.0
 */
@Data
public class SalarySettlementFormUserInfoParam extends ResourceQuery {

    /**
     * 薪资代办结算记录ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long salarySettlementAgentRecordId;

    /**
     * 结薪员工ID(申请单刷新查询)
     */
    private String salarySettlementFormUserInfoIdString;

    /**
     * 科目ID(申请单刷新查询)
     */
    private String salarySettlementFormUserItemIdString;

    /**
     * 导出用(暂存的单据在编辑导出,要展示暂存的数据)
     */
    private Long formId;

}
