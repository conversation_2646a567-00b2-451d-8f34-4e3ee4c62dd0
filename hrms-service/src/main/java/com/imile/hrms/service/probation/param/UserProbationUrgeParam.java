package com.imile.hrms.service.probation.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.common.enums.WechatTextMessageEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 试用期催办参数
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class UserProbationUrgeParam {

    /**
     * 试用期id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userProbationId;

    /**
     * 催办类型
     * @see WechatTextMessageEnum
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String urgeType;
}
