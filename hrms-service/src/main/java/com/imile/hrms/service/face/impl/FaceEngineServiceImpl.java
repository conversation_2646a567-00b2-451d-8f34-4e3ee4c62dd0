package com.imile.hrms.service.face.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.io.BaseEncoding;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.user.UserUpdateParam;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.api.blacklist.api.BlacklistApi;
import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.common.config.FaceConfiguration;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.FaceErrorCodeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.blacklist.BlacklistBanStatusEnum;
import com.imile.hrms.common.enums.punch.FaceRecordStatusEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.face.cache.FaceFeatureCache;
import com.imile.hrms.dao.face.dto.FaceCompareDTO;
import com.imile.hrms.dao.face.model.FaceFeatureDO;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.dao.HrmsFaceRecordDao;
import com.imile.hrms.dao.punch.model.HrmsFaceRecordDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.UpdateUserParam;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.genesis.RpcUserService;
import com.imile.hrms.integration.ipep.IpepIntegration;
import com.imile.hrms.integration.recognition.RecognitionService;
import com.imile.hrms.manage.face.FaceEngineManage;
import com.imile.hrms.service.face.FaceEngineService;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.punch.WarehouseOcService;
import com.imile.hrms.service.punch.WarehouseSupplierService;
import com.imile.hrms.service.punch.WarehouseUserService;
import com.imile.hrms.service.punch.param.warehouse.FaceCheckParam;
import com.imile.hrms.service.punch.param.warehouse.FaceSaveParam;
import com.imile.hrms.service.punch.param.warehouse.FaceSearchParam;
import com.imile.hrms.service.punch.param.warehouse.FaceSearchRepeatParam;
import com.imile.hrms.service.punch.vo.warehouse.UserFaceSearchRepeatVO;
import com.imile.hrms.service.punch.vo.warehouse.UserFaceSearchVO;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.hrms.service.user.result.UserCertificateBO;
import com.imile.recognition.api.face.model.dto.UserFaceSearchDTO;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.stereotype.Service;
import com.imile.hrms.service.punch.vo.warehouse.UserFaceSearchScanVO;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人脸识别服务Service
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Slf4j
@Service
public class FaceEngineServiceImpl implements FaceEngineService {

    @Resource
    private FaceEngineManage faceEngineManage;

    @Resource
    private IpepIntegration ipepIntegration;

    @Resource
    private FaceConfiguration faceConfiguration;

    @Resource
    private HrmsUserInfoService hrmsUserInfoService;

    @Resource
    private HrmsFaceRecordDao hrmsFaceRecordDao;

    @Resource
    private FaceFeatureCache faceFeatureCache;

    @Resource
    private HrmsUserInfoDao userInfoDao;

    @Resource
    private WarehouseUserService warehouseUserService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private HrmsEntDeptDao entDeptDao;

    @Resource
    private RpcUserService rpcUserService;

    @Resource
    private UserCertificateService userCertificateService;

    @Resource
    private RecognitionService recognitionService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private BlacklistApi blacklistApi;


    @SneakyThrows
    @Override
    public void faceInput(FaceSaveParam param) {
        HrmsUserInfoDO userInfoDO = Optional.ofNullable(userInfoDao.getByUserCode(param.getUserCode()))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage((HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()))));
        if (Objects.equals(BusinessConstant.Y, userInfoDO.getIsDriver())) {
            throw BusinessException.get(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getCode(), I18nUtils.getMessage(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getDesc()));
        }
        String faceUrl = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        faceInput(param.getFaceTime(), userInfoDO, faceUrl, imageBase64);
    }


/*    @Override
    public void faceInput(String fileKey, String userCode, Long vendorId, boolean updateUserPhoto) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(userCode), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), "userCode");
        HrmsUserInfoDO userInfoDO = Optional.ofNullable(hrmsUserInfoService.getByUserCode(userCode))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage((HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()))));

        if (Objects.nonNull(vendorId) && BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(vendorId)) {
            return;
        }

        OssApiVo ossApiVo = Optional.ofNullable(ipepIntegration.getUrlByFileKey(fileKey, BusinessConstant.OSS_PRIVATE_BUCKET_TYPE))
                .orElseThrow(() -> BusinessException.get(FaceErrorCodeEnum.FACE_QUERY_OSS_FILE_URL_FAIL.getCode(), I18nUtils.getMessage(FaceErrorCodeEnum.FACE_QUERY_OSS_FILE_URL_FAIL.getDesc())));

        InputStream inputStream = convertInputStream(ossApiVo.getFileUrl());

        ImageInfo imageInfo = faceEngineManage.convertImageInfo(inputStream);

        byte[] faceFeature = faceEngineManage.extractFaceFeature(imageInfo, false, fileKey);

        saveOrUpdate(userCode, fileKey, faceFeature);

        faceFeatureCache.deleteKey(RedisConstants.RedisPrefix.FACE_FEATURE_FULL_INFO);
    }*/

   /* @Override
    public void faceInput(List<UserDO> userDOS) {
        userDOS.forEach(user -> this.faceInput(user.getProfilePhotoUrl(), user.getUserCode(), null, false));
    }
*/

    @SneakyThrows
    @Override
    public UserFaceSearchVO faceCheck(FaceCheckParam param) {
        checkFaceBaseParams(param.getEmployeeType(), param.getOcId(), param.getVendorId());
        HrmsUserInfoDO userInfoDO = Optional.ofNullable(userInfoDao.getByUserCode(param.getUserCode()))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage((HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()))));
        if (Objects.equals(BusinessConstant.Y, userInfoDO.getIsDriver())) {
            throw BusinessException.get(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getCode(), I18nUtils.getMessage(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getDesc()));
        }

        String faceUrl = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        log.info("人脸检查识别图: {}", faceUrl);
        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        UserFaceSearchDTO userFaceSearchDTO;
        try {
            userFaceSearchDTO = recognitionService.faceSingleCheck(userInfoDO.getUserCode(), faceUrl, imageBase64);
        } catch (RpcException e) {
            log.info("人脸一对一检查失败,RPC{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }
        saveFaceRecord(userInfoDO, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), faceUrl, userFaceSearchDTO.getFaceKey(), param.getFaceTime());
        return convert(userInfoDO, userFaceSearchDTO.getScore(), userFaceSearchDTO.getFaceKey(), param.getVendorId());
    }

    @SneakyThrows
    @Override
    public UserFaceSearchVO faceRecognition(FaceSearchParam param) {
        checkFaceBaseParams(param.getEmployeeType(), param.getOcId(), param.getVendorId());
        HrmsEntDeptDO entDeptDO = Optional.ofNullable(entDeptDao.getById(param.getOcId()))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.STATION_NOT_EXITS.getDesc())));
        //人脸识别照上传
        String faceUrl = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        log.info("人脸搜索识别图: {}", faceUrl);

        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        UserFaceSearchDTO userFaceSearchDTO;
        try {
            userFaceSearchDTO = recognitionService.faceRecognition(faceUrl, imageBase64, entDeptDO.getCountry(), convertEmployeeType(param.getEmployeeType()));
        } catch (RpcException e) {
            log.info("人脸识别失败,RPC{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }

        log.info("人脸识别结果: {}", JSON.toJSONString(userFaceSearchDTO));
        if (Objects.isNull(userFaceSearchDTO) || StringUtils.isEmpty(userFaceSearchDTO.getUserCode())) {
            return convertDefaultUserFaceSearchVO();
        }
        HrmsUserInfoDO user = userInfoDao.getByUserCode(userFaceSearchDTO.getUserCode());
        if (Objects.isNull(user)) {
            return convertDefaultUserFaceSearchVO();
        }
        //检测用户：1：黑名单、用户状态; 2:用户群体是否符合WPM打卡
        checkUser(user);

        boolean pass = userFaceSearchDTO.getScore() >= faceConfiguration.getSimilarScore();
        saveFaceRecord(user, userFaceSearchDTO.getScore(), pass, faceUrl, userFaceSearchDTO.getFaceKey(), param.getFaceTime());
        if (pass && BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType()) && !Objects.equals(user.getDeptId(), param.getOcId())) {
            EntOcApiDTO oc = warehouseOcService.getOc(param.getOcId());
            UserUpdateParam build = UserUpdateParam.builder()
                    .userCode(user.getUserCode())
                    .ocCode(oc.getOcCode())
                    .locationCountry(oc.getCountry())
                    .locationCity(oc.getCity())
                    .locationProvince(oc.getProvince())
                    .operationScene(OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE)
                    .build();
            rpcUserService.updateUser(build);
            HrmsUserInfoDO updateUser = userInfoDao.getByUserCode(user.getUserCode());
            warehouseUserService.saveUserInfo(updateUser, new ArrayList<>(), oc);
            hrmsUserInfoService.updateAttendanceAndPunchConfigHandler(updateUser, user);
        }
        return convert(user, userFaceSearchDTO.getScore(), userFaceSearchDTO.getFaceKey(), param.getVendorId());
    }


    @SneakyThrows
    @Override
    public UserFaceSearchRepeatVO faceRecognitionV2(FaceSearchRepeatParam param) {
        HrmsUserInfoDO userInfo = Optional.ofNullable(userInfoDao.getById(param.getUserId()))
                .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc())));
        //人脸识别照上传
        String faceUrl = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        log.info("人脸搜索识别图: {}", faceUrl);

        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        UserFaceSearchDTO userFaceSearchDTO;
        try {
            userFaceSearchDTO = recognitionService.faceRecognition(faceUrl, imageBase64, userInfo.getLocationCountry(), null);
        } catch (RpcException e) {
            log.info("人脸识别失败,RPC{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }

        log.info("人脸识别结果: {}", JSON.toJSONString(userFaceSearchDTO));
        if (Objects.isNull(userFaceSearchDTO) || StringUtils.isEmpty(userFaceSearchDTO.getUserCode())) {
            //人脸录入
            return faceInput(param.getFaceTime(), userInfo, faceUrl, imageBase64);
        }
        HrmsUserInfoDO user = userInfoDao.getByUserCode(userFaceSearchDTO.getUserCode());
        if (Objects.isNull(user)
                || !Objects.equals(BusinessConstant.STATUS, user.getStatus())
                || Objects.equals(BusinessConstant.Y, user.getIsDriver())
                || Objects.equals(BusinessConstant.N, user.getIsWarehouseStaff())) {
            //人脸录入
            return faceInput(param.getFaceTime(), userInfo, faceUrl, imageBase64);
        }

        saveFaceRecord(user, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), faceUrl, userFaceSearchDTO.getFaceKey(), param.getFaceTime());

        Map<Long, UserCertificateBO> certificateCodeMap = userCertificateService.getUserCertificateList(Lists.newArrayList(userInfo.getId(), user.getId()), CertificateTypeEnum.INE.getCode())
                .stream()
                .collect(Collectors.toMap(UserCertificateBO::getUserId, Function.identity(), (v1, v2) -> v1));

        UserFaceSearchRepeatVO result = new UserFaceSearchRepeatVO();
        result.setUserId(userInfo.getId());
        result.setUserCode(userInfo.getUserCode());
        result.setUserName(userInfo.getUserName());
        if (Objects.nonNull(certificateCodeMap.get(userInfo.getId()))) {
            result.setCertificatesCode(certificateCodeMap.get(userInfo.getId()).getCertificateCode());
        }
        result.setSimilarUserId(user.getId());
        result.setSimilarUserCode(user.getUserCode());
        result.setSimilarUserName(user.getUserName());
        if (Objects.nonNull(certificateCodeMap.get(user.getId()))) {
            result.setSimilarCertificatesCode(certificateCodeMap.get(user.getId()).getCertificateCode());
        }
        result.setPass(true);
        result.setScore(userFaceSearchDTO.getScore());
        result.setFaceUrl(userFaceSearchDTO.getFaceKey());
        return result;
    }


 /*   @Override
    public void faceDBInit(String countryCode) {
        log.info("[faceDBInit start] 开始初始化人脸库数据");

        List<UserDO> userDOS = userDao.selectProfilePhotoUrlIsNotNull();

        List<List<UserDO>> partitionList = Lists.partition(userDOS, 50);

        partitionList.forEach(partition -> facePoolExecutor.execute(() -> this.faceInput(partition)));

        log.info("[faceDBInit end] 初始化人脸库数据结束");
    }*/

    @Override
    public void scanDuplicateFace() {
        List<FaceFeatureDO> faceFeatureList = Optional.ofNullable(faceFeatureCache.selectAll())
                .orElseThrow(() -> BusinessException.get(FaceErrorCodeEnum.PLEASE_INIT_FACE_FEATURE_LIBRARY_FIRST.getCode(), I18nUtils.getMessage(FaceErrorCodeEnum.PLEASE_INIT_FACE_FEATURE_LIBRARY_FIRST.getDesc())));
        List<UserFaceSearchScanVO> result = new ArrayList<>();
        Set<String> existCodeList = new HashSet<>();
        for (FaceFeatureDO faceFeature : faceFeatureList) {
            List<FaceCompareDTO> list = faceEngineManage.faceRecognitionV2(faceFeature.getFeatureData(), faceFeatureList, faceConfiguration.getSimilarScore());
            List<String> userCodeList = list.stream().map(FaceCompareDTO::getUserCode).collect(Collectors.toList());
            Optional<String> userOptional = userCodeList.stream().filter(existCodeList::contains).findFirst();
            if (list.size() > 1 && !userOptional.isPresent()) {
                UserFaceSearchScanVO userFaceSearchScanVO = new UserFaceSearchScanVO();
                userFaceSearchScanVO.setUserCode(faceFeature.getUserCode());
                userFaceSearchScanVO.setPassUserCodeList(userCodeList);
                result.add(userFaceSearchScanVO);
                existCodeList.addAll(userCodeList);
            }
        }
        log.info("人脸库重复人脸扫描结果: {}", JSON.toJSONString(result));
    }

    @SneakyThrows
    @Override
    public List<UserFaceSearchVO> faceSearchTest(MultipartFile file) {
        //人脸识别照上传
        String faceUrl = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, file.getOriginalFilename(), file.getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        log.info("人脸搜索识别图: {}", faceUrl);

        String imageBase64 = BaseEncoding.base64().encode(file.getBytes());
        List<UserFaceSearchDTO> userFaceSearchList = recognitionService.faceRecognitionPlus(faceUrl, imageBase64);
        return BeanUtils.convert(UserFaceSearchVO.class, userFaceSearchList);
    }

    private void checkFaceBaseParams(String employeeType, Long ocId, Long vendorId) {
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(employeeType), HrmsErrorCodeEnums.PARAM_VALID_ERROR);
        if (BusinessConstant.LABOR_DISPATCH.equals(employeeType) && (Objects.isNull(ocId) || Objects.isNull(vendorId))) {
            throw BusinessException.get(HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc()));
        }
    }

    private UserFaceSearchVO convertDefaultUserFaceSearchVO() {
        UserFaceSearchVO userFaceSearchVO = new UserFaceSearchVO();
        userFaceSearchVO.setScore(0f);
        userFaceSearchVO.setPass(false);
        return userFaceSearchVO;
    }

    private List<String> convertEmployeeType(String employeeType) {
        if (BusinessConstant.LABOR_DISPATCH.equals(employeeType)) {
            return Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode());
        } else {
            return Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode());
        }
    }


    private UserFaceSearchVO convert(HrmsUserInfoDO userDO,
                                     Float score,
                                     String fileUrl,
                                     Long vendorId) {
        if (StringUtils.isBlank(userDO.getUserCode()) || Objects.isNull(score)) {
            UserFaceSearchVO userFaceSearchVO = new UserFaceSearchVO();
            userFaceSearchVO.setScore(0f);
            userFaceSearchVO.setPass(false);
            return userFaceSearchVO;
        }
        UserFaceSearchVO faceSearchVO = new UserFaceSearchVO();
        faceSearchVO.setUserCode(userDO.getUserCode());
        faceSearchVO.setScore(score);
        faceSearchVO.setPass(score >= faceConfiguration.getSimilarScore());
        faceSearchVO.setFaceUrl(fileUrl);

        HrmsEntDeptDO oc = entDeptDao.getById(userDO.getDeptId());
        Map<String, String> supplierMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(userDO.getVendorCode()));
        faceSearchVO.setSex(userDO.getSex());
        faceSearchVO.setOcId(userDO.getDeptId());
        faceSearchVO.setOcName(Objects.nonNull(oc) ? oc.getDeptNameEn() : null);
        faceSearchVO.setUserName(userDO.getUserName());
        faceSearchVO.setVendorId(userDO.getVendorId());
        faceSearchVO.setVendorCode(userDO.getVendorCode());
        faceSearchVO.setVendorName(supplierMap.get(userDO.getVendorCode()));
        faceSearchVO.setUserId(userDO.getId());
        faceSearchVO.setVendorChange(Objects.nonNull(vendorId) && !Objects.equals(faceSearchVO.getVendorId(), vendorId) && !Objects.equals(CountryCodeEnum.BRA.getCode(), userDO.getLocationCountry()));
        return faceSearchVO;
    }

    private void updateUserProfilePhotoUrl(String userCode, String fileUrl) {
        UpdateUserParam updateUserParam = new UpdateUserParam();
        updateUserParam.setUserCode(userCode);
        updateUserParam.setKey(fileUrl);
        hrmsUserInfoService.updatePhoto(updateUserParam);
    }

    private void saveFaceRecord(HrmsUserInfoDO userInfoDO,
                                float score,
                                boolean pass,
                                String recognitionPhoto,
                                String facePhoto,
                                Date faceTime) {
        HrmsFaceRecordDO faceRecordDO = new HrmsFaceRecordDO();
        faceRecordDO.setUserId(userInfoDO.getId());
        faceRecordDO.setUserCode(userInfoDO.getUserCode());
        faceRecordDO.setFacePhoto(facePhoto);
        faceRecordDO.setRecognitionPhoto(recognitionPhoto);
        faceRecordDO.setFaceRecordStatus(pass ? FaceRecordStatusEnum.EFFECTIVE.getCode() : FaceRecordStatusEnum.INVALID.getCode());
        faceRecordDO.setFaceRecordTime(faceTime);
        faceRecordDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(faceTime)));
        faceRecordDO.setRecognitionScore(Convert.toBigDecimal(score));
        BaseDOUtil.fillDOInsert(faceRecordDO);
        hrmsFaceRecordDao.save(faceRecordDO);
    }

    private UserFaceSearchRepeatVO convertDefaultResult() {
        UserFaceSearchRepeatVO result = new UserFaceSearchRepeatVO();
        result.setScore(0f);
        result.setPass(false);
        return result;
    }

    private UserFaceSearchRepeatVO faceInput(Date faceTime, HrmsUserInfoDO userInfo, String faceUrl, String imageBase64) {
        try {
            recognitionService.faceFeatureSave(BusinessConstant.WPM, userInfo.getUserCode(),null, null, null, faceUrl, imageBase64);
        } catch (RpcException e) {
            log.error("人脸特征录入异常：{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }
        if (StringUtils.isEmpty(userInfo.getProfilePhotoUrl())) {
            updateUserProfilePhotoUrl(userInfo.getUserCode(), faceUrl);
        }
        saveFaceRecord(userInfo, 1, true, faceUrl, faceUrl, faceTime);
        return convertDefaultResult();
    }

    private void checkUser(HrmsUserInfoDO user) {
        if (!Objects.equals(BusinessConstant.STATUS, user.getStatus())) {
            //黑名单用户检查
            RpcResult<BlacklistInfoDTO> result = blacklistApi.getBlacklistInfo(user.getUserCode());
            if (!(result.isSuccess() && Objects.nonNull(result.getResult()) && BlacklistBanStatusEnum.getBanStatusList().contains(result.getResult().getBanStatus()))) {
                //非黑名单导致的账号停用
                throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_HAS_BEEN_DEACTIVATED.getDesc()));
            }
        }

        //限制司机使用
        if (Objects.equals(BusinessConstant.Y, user.getIsDriver())) {
            throw BusinessException.get(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getCode(), I18nUtils.getMessage(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getDesc()));
        }

        //限制非仓内人员使用
        if (Objects.equals(BusinessConstant.N, user.getIsWarehouseStaff())) {
            throw BusinessException.get(FaceErrorCodeEnum.NON_WAREHOUSE_EMPLOYEES_CANT_USE_WPM.getCode(), I18nUtils.getMessage(FaceErrorCodeEnum.NON_WAREHOUSE_EMPLOYEES_CANT_USE_WPM.getDesc()));
        }
    }

}
