package com.imile.hrms.service.approval.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} OverTimeImportAddListParam
 * {@code @since:} 2024-06-13 17:25
 * {@code @description:}
 */
@Data
public class OverTimeImportAddListParam {
    /**
     * 申请人用户code
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String applyUserCode;

    /**
     * 申请人所在国
     */
    private String applyUserCountry;

    /**
     * 导入的人员加班数据
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<OverTimeImportAddParam> overTimeImportAddParamList;

    /**
     * 备注
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String remark;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;
}
