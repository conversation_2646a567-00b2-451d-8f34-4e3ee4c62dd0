package com.imile.hrms.service.achievement.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AchievementsItemStatisticsListParam extends ResourceQuery implements Serializable {
    private static final long serialVersionUID = 2291531979877603576L;

    private Long id;

    /**
     * 绩效活动id
     */
    private Long eventId;

    /**
     * 部门id
     */
    private List<Long> deptIds;

    /**
     * 部门id
     */
    private String deptIdStr;


    /**
     * 目标名称
     */
    private List<String> indicatorLibraryCodes;

    /**
     * 目标名称
     */
    private String indicatorLibraryCodeStr;

    /**
     * 目标状态
     */
    private String status;


    /**
     * 目标状态（统计视图状态>=1）
     */
    private String oaStatus;

    /**
     * 是否有排名
     */
    private Boolean hasRank;

    /**
     * 1:prism读取 0:手动录入
     */
    private String isComplete;

    /**
     * 周期数集合
     */
    private List<Integer> cycleNums;

    private String cycleNumStr;

    /**
     * 版本号格式YYYY-MM-DD，将时间转为字符串形式传递
     */
    private String snapshotVersion;
}
