package com.imile.hrms.service.user.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-13
 * @version: 1.0
 */
@Data
public class UserStageLeaveDTO {

    /**
     * hrms_user_leave_stage_detail表ID
     */
    private Long id;

    /**
     * 假期剩余天数
     */
    private BigDecimal leaveResidueDay;

    /**
     * 假期已使用天数
     */
    private BigDecimal leaveUsedDay;

    /**
     * 假期剩余分钟数
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 假期已使用分钟数
     */
    private BigDecimal leaveUsedMinutes;

    /**
     * 阶段
     */
    private Integer stage;

    /**
     * 百分比日薪
     */
    private BigDecimal percentSalary;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

}
