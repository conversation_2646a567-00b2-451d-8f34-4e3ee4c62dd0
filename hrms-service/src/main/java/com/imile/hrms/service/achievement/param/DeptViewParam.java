package com.imile.hrms.service.achievement.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-10
 */
@Data
public class DeptViewParam extends ResourceQuery {
    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 部门id集合
     */
    private List<Long> deptIds;

    /**
     * 部门ID列表
     */
    private List<Long> deptIdList;

    /**
     * 活动id
     */
    private Long eventId;

    /**
     * 待处理人(0:带我处理 1全部 -1:其他)
     */
    private String pendingType;

    /**
     * 考核结果
     */
    private String leaderRate;

    /**
     * 行权人结论 01同意 02不同意
     */
    private String exerciserConclusion;

    /**
     * 职级名称
     */
    private String gradeIds;

    private String deptIdStr;

    /**
     * 国家
     */
    private String country;

    /**
     * 01考核责任人 02行权人
     */
    private String userType;

    private Boolean isExport;
}
