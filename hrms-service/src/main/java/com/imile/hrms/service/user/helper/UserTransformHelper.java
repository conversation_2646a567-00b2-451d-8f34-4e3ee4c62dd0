package com.imile.hrms.service.user.helper;

import com.alibaba.fastjson.JSON;
import com.imile.accounting.center.dto.ProjectPageResponseDto;
import com.imile.hrms.common.enums.user.transform.TransformStatusEnum;
import com.imile.hrms.common.enums.user.transform.TransformTypeEnum;
import com.imile.hrms.common.enums.user.transform.UserTransformFieldEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.dao.base.model.CountryDO;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntGradeDO;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.UserTransformDO;
import com.imile.hrms.service.user.context.UserImportTempContext;
import com.imile.hrms.service.user.dto.transform.UserTransformBeforeDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/20
 */
public class UserTransformHelper {

    public static UserTransformDO buildUserTransform(TransformTypeEnum transformTypeEnum, HrmsUserInfoDO user,
                                                     UserImportTempContext tempContext,
                                                     Map<String, Object> transformFieldMap) {
        fillTransformFieldMap(user, tempContext, transformFieldMap);
        UserTransformDO userTransform = new UserTransformDO();
        userTransform.setUserId(user.getId());
        userTransform.setTransformType(transformTypeEnum.getCode());
        userTransform.setTransformStatus(TransformStatusEnum.TRANSFORM_COMPLETE.getStatus());
        userTransform.setTransformTime(new Date());
        userTransform.setUserSnapshot(JSON.toJSONString(buildUserSnapshot(tempContext.getOriginUser(), tempContext)));
        userTransform.setTransformContent(JSON.toJSONString(transformFieldMap));
        return userTransform;
    }

    private static UserTransformBeforeDTO buildUserSnapshot(HrmsUserInfoDO user, UserImportTempContext tempContext) {
        CountryDO nationality = tempContext.getOriginNationality();
        HrmsEntDeptDO ocDept = tempContext.getOriginOcDept();
        HrmsBizModelDO bizModel = tempContext.getOriginBizModel();
        ProjectPageResponseDto project = tempContext.getOriginProject();
        HrmsUserInfoDO leader = tempContext.getOriginLeader();
        PostDO post = tempContext.getOriginPost();
        HrmsEntGradeDO grade = tempContext.getOriginGrade();
        return UserTransformBeforeDTO.builder()
                .userId(user.getId())
                .workNo(user.getWorkNo())
                .countryCode(user.getCountryCode())
                .countryNameCn(Objects.isNull(nationality) ? "" : nationality.getCountryNameCn())
                .countryNameEn(Objects.isNull(nationality) ? "" : nationality.getCountryNameEn())
                .employeeType(user.getEmployeeType())
                .postId(user.getPostId())
                .postNameCn(Objects.isNull(post) ? "" : post.getPostNameCn())
                .postNameEn(Objects.isNull(post) ? "" : post.getPostNameEn())
                .gradeId(user.getGradeId())
                .jobLevel(user.getGradeNo())
                .jobSequence(Objects.isNull(grade) ? "" : grade.getRankSequence())
                .isDriver(user.getIsDriver())
                .isDtl(user.getIsDtl())
                .deptId(user.getDeptId())
                .ocCode(user.getOcCode())
                .ocNameCn(Objects.isNull(ocDept) ? "" : ocDept.getDeptNameCn())
                .ocNameEn(Objects.isNull(ocDept) ? "" : ocDept.getDeptNameEn())
                .originCountry(user.getOriginCountry())
                .leaderId(user.getLeaderId())
                .leaderName(Objects.isNull(leader) ? "" : leader.getUserName())
                .bizModelId(user.getBizModelId())
                .bizModelNameCn(Objects.isNull(bizModel) ? "" : bizModel.getBizModelNameCn())
                .bizModelNameEn(Objects.isNull(bizModel) ? "" : bizModel.getBizModelNameEn())
                .projectCode(user.getProjectCode())
                .projectName(Objects.isNull(project) ? "" : project.getProjectDescEn())
                .locationCountry(user.getLocationCountry())
                .locationProvince(user.getLocationProvince())
                .locationCity(user.getLocationCity())
                .isGlobalRelocation(user.getIsGlobalRelocation())
                .build();
    }

    private static void fillTransformFieldMap(HrmsUserInfoDO user, UserImportTempContext tempContext,
                                              Map<String, Object> transformFieldMap) {
        if (Objects.nonNull(user.getDeptId())) {
            HrmsEntDeptDO dept = tempContext.getDept();
            transformFieldMap.put(UserTransformFieldEnum.DEPT_ID.getKey(), dept.getId().toString());
            transformFieldMap.put(UserTransformFieldEnum.DEPT_NAME_CN.getKey(), BusinessFieldUtils.getUnifiedDeptName(dept.getDeptNameCn(), dept.getDeptNameEn()));
            transformFieldMap.put(UserTransformFieldEnum.DEPT_NAME_EN.getKey(), dept.getDeptNameEn());
        }
        if (StringUtils.isNotBlank(user.getOcCode())) {
            HrmsEntDeptDO ocDept = tempContext.getOcDept();
            transformFieldMap.put(UserTransformFieldEnum.OC_CODE.getKey(), ocDept.getOcCode());
            transformFieldMap.put(UserTransformFieldEnum.OC_ID.getKey(), ocDept.getId().toString());
            transformFieldMap.put(UserTransformFieldEnum.OC_NAME_CN.getKey(), ocDept.getDeptNameCn());
            transformFieldMap.put(UserTransformFieldEnum.OC_NAME_EN.getKey(), ocDept.getDeptNameEn());
            transformFieldMap.put(UserTransformFieldEnum.ORIGIN_COUNTRY.getKey(), ocDept.getCountry());
        }
        if (Objects.nonNull(user.getBizModelId())) {
            HrmsBizModelDO bizModel = tempContext.getBizModel();
            transformFieldMap.put(UserTransformFieldEnum.BIZ_MODEL_ID.getKey(), bizModel.getId().toString());
            transformFieldMap.put(UserTransformFieldEnum.BIZ_MODEL_NAME_CN.getKey(), bizModel.getBizModelNameCn());
            transformFieldMap.put(UserTransformFieldEnum.BIZ_MODEL_NAME_EN.getKey(), bizModel.getBizModelNameEn());
        }
        if (StringUtils.isNotBlank(user.getProjectCode())) {
            ProjectPageResponseDto project = tempContext.getProject();
            transformFieldMap.put(UserTransformFieldEnum.PROJECT_CODE.getKey(), project.getProjectCode());
            transformFieldMap.put(UserTransformFieldEnum.PROJECT_NAME.getKey(), project.getProjectDescEn());
        }
        if (Objects.nonNull(user.getLeaderId())) {
            HrmsUserInfoDO leader = tempContext.getLeader();
            transformFieldMap.put(UserTransformFieldEnum.LEADER_ID.getKey(), leader.getId().toString());
            transformFieldMap.put(UserTransformFieldEnum.LEADER_NAME.getKey(), leader.getUserName());
        }
        if (Objects.nonNull(user.getIsGlobalRelocation())) {
            transformFieldMap.put(UserTransformFieldEnum.IS_GLOBAL_RELOCATION.getKey(), user.getIsGlobalRelocation());
        }
        if (StringUtils.isNotBlank(user.getLocationCountry())) {
            transformFieldMap.put(UserTransformFieldEnum.LOCATION_COUNTRY.getKey(), user.getLocationCountry());
            transformFieldMap.put(UserTransformFieldEnum.LOCATION_PROVINCE.getKey(), user.getLocationProvince());
            transformFieldMap.put(UserTransformFieldEnum.LOCATION_CITY.getKey(), user.getLocationCity());
        }
    }
}
