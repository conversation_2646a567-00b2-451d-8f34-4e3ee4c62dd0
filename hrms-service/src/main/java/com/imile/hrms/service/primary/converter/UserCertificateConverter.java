package com.imile.hrms.service.primary.converter;

import com.imile.genesis.api.model.component.UserCertificateField;
import com.imile.genesis.api.model.param.user.UserCertificateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.hrms.api.primary.model.result.user.CertificateDTO;
import com.imile.hrms.api.primary.model.result.user.UserCertificateCheckResultDTO;
import com.imile.hrms.dao.user.dto.UserCertificateParamDTO;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
public class UserCertificateConverter {

    /**
     * 人员证件入参转换
     * 由于老接口入参定义在DAO层，新接口入参未来使用Dubbo接口入参，
     * 所以需要老接口内部涉及人员证件逻辑时需要定义一套一模一样的入参类 在使用时需要转换
     * 未来老接口废弃后相关入参类和转换器可以清理
     *
     * @param sourceList List<UserCertificateParamDTO>
     * @return List<UserCertificateSaveParam>
     */
    public static List<UserCertificateSaveParam> convertParamList(List<UserCertificateParamDTO> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(s -> {
                    UserCertificateSaveParam item = BeanUtils.convert(s, UserCertificateSaveParam.class);
                    item.setExtendFieldList(BeanUtils.convert(UserCertificateField.class, s.getExtendFieldList()));
                    return item;
                })
                .collect(Collectors.toList());
    }

    public static List<UserCertificateSaveParam> convertSaveParamList(List<com.imile.hrms.api.primary.model.param.user.UserCertificateSaveParam> sourceList){
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(s -> {
                    UserCertificateSaveParam item = BeanUtils.convert(s, UserCertificateSaveParam.class);
                    item.setExtendFieldList(BeanUtils.convert(UserCertificateField.class, s.getExtendFieldList()));
                    return item;
                })
                .collect(Collectors.toList());
    }

    public static UserCertificateCheckParam convertCheckParam(com.imile.hrms.api.primary.model.param.user.UserCertificateCheckParam param) {
        UserCertificateCheckParam finalParam = new UserCertificateCheckParam();
        finalParam.setCountry(param.getCountry());
        finalParam.setEmployeeType(param.getEmployeeType());
        finalParam.setIsDriver(param.getIsDriver());
        finalParam.setNationalityCode(param.getNationalityCode());
        finalParam.setUserCertificateList(UserCertificateConverter.convertCertificateList(param.getCertificateList()));
        finalParam.setUserCode(param.getUserCode());
        return finalParam;
    }

    private static List<UserCertificateSaveParam> convertCertificateList(List<com.imile.hrms.api.primary.model.result.user.CertificateDTO> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(s -> {
                    UserCertificateSaveParam item = BeanUtils.convert(s, UserCertificateSaveParam.class);
                    item.setExtendFieldList(BeanUtils.convert(UserCertificateField.class, s.getExtendFieldList()));
                    return item;
                })
                .collect(Collectors.toList());
    }

    public static UserCertificateCheckResultDTO convertCheckResult(com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO result) {
        return UserCertificateCheckResultDTO.of(result.getIsRepeat(), UserCertificateConverter.convertRepeatCertificateList(result.getRepeatCertificateList()));
    }

    private static List<UserCertificateCheckResultDTO.RepeatCertificate> convertRepeatCertificateList(List<com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO.RepeatCertificate> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(s -> {
                    UserCertificateCheckResultDTO.RepeatCertificate item = new UserCertificateCheckResultDTO.RepeatCertificate();
                    item.setCertificate(BeanUtils.convert(s.getCertificate(), CertificateDTO.class));
                    item.setOwnerUserCode(s.getOwnerUserCode());
                    item.setOwnerUserName(s.getOwnerUserName());
                    return item;
                })
                .collect(Collectors.toList());
    }
}
