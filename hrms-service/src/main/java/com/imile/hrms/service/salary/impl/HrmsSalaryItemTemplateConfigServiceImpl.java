package com.imile.hrms.service.salary.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.salary.SalaryConfigDataSourceEnum;
import com.imile.hrms.common.enums.salary.SalaryItemValueTypeEnum;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemGroupConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemGroupRelationDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemTemplateConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryUserRoleDO;
import com.imile.hrms.dao.salary.query.SalaryItemConfigQuery;
import com.imile.hrms.manage.salary.HrmsSalaryItemConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemGroupConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemGroupRelationManage;
import com.imile.hrms.manage.salary.HrmsSalaryItemTemplateConfigManage;
import com.imile.hrms.manage.salary.HrmsSalaryUserRoleManage;
import com.imile.hrms.manage.salary.bo.HrmsSalaryItemTemplateConfigBO;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.salary.HrmsSalaryItemTemplateConfigService;
import com.imile.hrms.service.salary.dto.SalaryItemGroupConfigDTO;
import com.imile.hrms.service.salary.dto.SalaryItemGroupRelationDTO;
import com.imile.hrms.service.salary.dto.SalarySchemeStageDTO;
import com.imile.hrms.service.salary.param.SalaryItemGroupConfigParam;
import com.imile.hrms.service.salary.param.SalaryItemGroupRelationParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigAddParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigCountryQueryParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigDeleteParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigDetailParam;
import com.imile.hrms.service.salary.param.SalaryItemTemplateConfigInfoParam;
import com.imile.hrms.service.salary.param.SalarySchemeConfigAddParam;
import com.imile.hrms.service.salary.param.SalarySchemeItemInfoParam;
import com.imile.hrms.service.salary.param.SalarySchemeStageParam;
import com.imile.hrms.service.salary.vo.SalaryItemTemplateConfigCountryDetailVO;
import com.imile.hrms.service.salary.vo.SalaryItemTemplateConfigDetailVO;
import com.imile.hrms.service.salary.vo.SalaryItemTemplateConfigInfoVO;
import com.imile.hrms.service.salary.vo.SalarySchemeStageVO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/24 11:04
 * @version: 1.0
 */
@Service
public class HrmsSalaryItemTemplateConfigServiceImpl extends BaseService implements HrmsSalaryItemTemplateConfigService {

    @Autowired
    private HrmsSalaryItemTemplateConfigManage hrmsSalaryItemTemplateConfigManage;
    @Autowired
    private HrmsSalaryItemGroupConfigManage hrmsSalaryItemGroupConfigManage;
    @Autowired
    private HrmsSalaryItemGroupRelationManage hrmsSalaryItemGroupRelationManage;
    @Autowired
    private HrmsSalaryItemConfigManage hrmsSalaryItemConfigManage;
    @Autowired
    private IHrmsIdWorker hrmsIdWorker;
    @Autowired
    private HrmsSalaryUserRoleManage hrmsSalaryUserRoleManage;

    @Override
    public PaginationResult<SalaryItemTemplateConfigInfoVO> list(SalaryItemTemplateConfigInfoParam param) {
        List<String> countryList = new ArrayList<>();
        if (StringUtils.isBlank(param.getCountry())) {
            List<HrmsSalaryUserRoleDO> salaryUserRoleDOList = hrmsSalaryUserRoleManage.selectRoleByUserIdList(Arrays.asList(RequestInfoHolder.getUserId()));
            if (CollectionUtils.isNotEmpty(salaryUserRoleDOList)) {
                countryList = Arrays.asList(salaryUserRoleDOList.get(0).getCountry().split(","));
                if (countryList.contains("ALL")) {
                    countryList = new ArrayList<>();
                }
            }
        }
        Page<HrmsSalaryItemTemplateConfigDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PageInfo<HrmsSalaryItemTemplateConfigDO> pageInfo = new PageInfo<>();
        if (CollectionUtils.isEmpty(countryList)) {
            pageInfo = page.doSelectPageInfo(() -> hrmsSalaryItemTemplateConfigManage.selectItemTemplateConfigByCountry(param.getCountry()));
        } else {
            List<String> finalCountryList = countryList;
            pageInfo = page.doSelectPageInfo(() -> hrmsSalaryItemTemplateConfigManage.selectItemTemplateConfigByCountryList(finalCountryList));

        }
        //PageInfo<HrmsSalaryItemTemplateConfigDO> pageInfo = page.doSelectPageInfo(() -> hrmsSalaryItemTemplateConfigManage.selectItemTemplateConfigByCountry(param.getCountry()));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            PageInfo<SalaryItemTemplateConfigInfoVO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, param);
        }
        List<SalaryItemTemplateConfigInfoVO> resultList = new ArrayList<>();
        for (HrmsSalaryItemTemplateConfigDO configDO : pageInfo.getList()) {
            SalaryItemTemplateConfigInfoVO itemTemplateConfigInfoVO = BeanUtils.convert(configDO, SalaryItemTemplateConfigInfoVO.class);
            resultList.add(itemTemplateConfigInfoVO);
        }
        PageInfo<SalaryItemTemplateConfigInfoVO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(resultList);
        return getPageResult(pageInfoResult, param);
    }

    @Override
    public SalaryItemTemplateConfigDetailVO detail(SalaryItemTemplateConfigDetailParam param) {
        List<HrmsSalaryItemTemplateConfigDO> salaryItemTemplateConfigDOList = hrmsSalaryItemTemplateConfigManage.selectItemTemplateConfigByIdList(Arrays.asList(param.getItemTemplateId()));
        if (CollectionUtils.isEmpty(salaryItemTemplateConfigDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_ITEM_TEMPLATE_CONFIG_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_ITEM_TEMPLATE_CONFIG_IS_EMPTY.getDesc()));
        }
        List<HrmsSalaryItemGroupConfigDO> itemGroupConfigDOList = hrmsSalaryItemGroupConfigManage.selectItemGroupConfigByTemplateIdList(Arrays.asList(salaryItemTemplateConfigDOList.get(0).getId())).stream()
                .sorted(Comparator.comparing(HrmsSalaryItemGroupConfigDO::getItemGroupSort)).collect(Collectors.toList());
        List<Long> itemGroupIdList = itemGroupConfigDOList.stream().map(HrmsSalaryItemGroupConfigDO::getId).collect(Collectors.toList());
        List<HrmsSalaryItemGroupRelationDO> itemGroupRelationDOList = hrmsSalaryItemGroupRelationManage.selectItemGroupRelationByGroupIdList(itemGroupIdList).stream()
                .sorted(Comparator.comparing(HrmsSalaryItemGroupRelationDO::getItemConfigSort)).collect(Collectors.toList());
        List<Long> itemConfigIdList = itemGroupRelationDOList.stream().map(HrmsSalaryItemGroupRelationDO::getItemConfigId).collect(Collectors.toList());
        List<HrmsSalaryItemConfigDO> itemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigByIdList(itemConfigIdList);
        Map<Long, List<HrmsSalaryItemConfigDO>> itemConfigMaps = itemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        SalaryItemTemplateConfigDetailVO detailVO = new SalaryItemTemplateConfigDetailVO();
        detailDataBuild(detailVO, salaryItemTemplateConfigDOList.get(0), itemGroupConfigDOList, itemGroupRelationDOList, itemConfigMaps);
        return detailVO;
    }

    @Override
    public void delete(SalaryItemTemplateConfigDeleteParam param) {
        HrmsSalaryItemTemplateConfigBO templateConfigBO = new HrmsSalaryItemTemplateConfigBO();
        templateDeleteDataBuild(param.getItemTemplateId(), templateConfigBO);
        hrmsSalaryItemTemplateConfigManage.templateConfigDelete(templateConfigBO.getTemplateConfigDO(), templateConfigBO.getGroupConfigDOList(), templateConfigBO.getGroupRelationDOList());
    }

    @Override
    public void add(SalaryItemTemplateConfigAddParam param) {
        checkParam(param);
        templateAddDataCheck(param);
        HrmsSalaryItemTemplateConfigBO templateConfigBO = new HrmsSalaryItemTemplateConfigBO();
        templateAddDataBuild(param, templateConfigBO);
        hrmsSalaryItemTemplateConfigManage.templateConfigAdd(templateConfigBO.getTemplateConfigDO(), templateConfigBO.getGroupConfigDOList(), templateConfigBO.getGroupRelationDOList());
    }

    @Override
    public void update(SalaryItemTemplateConfigAddParam param) {
        checkParam(param);
        templateAddDataCheck(param);
        //直接把旧的全部删除，然后新增就可以
        HrmsSalaryItemTemplateConfigBO deleteTemplateConfigBO = new HrmsSalaryItemTemplateConfigBO();
        templateDeleteDataBuild(param.getItemTemplateId(), deleteTemplateConfigBO);

        HrmsSalaryItemTemplateConfigBO addTemplateConfigBO = new HrmsSalaryItemTemplateConfigBO();
        templateAddDataBuild(param, addTemplateConfigBO);

        hrmsSalaryItemTemplateConfigManage.templateConfigUpdate(addTemplateConfigBO.getTemplateConfigDO(), addTemplateConfigBO.getGroupConfigDOList(), addTemplateConfigBO.getGroupRelationDOList(),
                deleteTemplateConfigBO.getTemplateConfigDO(), deleteTemplateConfigBO.getGroupConfigDOList(), deleteTemplateConfigBO.getGroupRelationDOList());
    }


    @Override
    public List<SalaryItemTemplateConfigCountryDetailVO> countryList(SalaryItemTemplateConfigCountryQueryParam param) {
        List<HrmsSalaryItemTemplateConfigDO> templateConfigDOList = new ArrayList<>();
        List<String> countryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountry())) {
            countryList = Arrays.asList(param.getCountry().split(","));
        }
        List<HrmsSalaryItemTemplateConfigDO> allTemplateConfigDOList = hrmsSalaryItemTemplateConfigManage.selectAllItemTemplateConfig();
        for (HrmsSalaryItemTemplateConfigDO item : allTemplateConfigDOList) {
            List<String> existCountryList = Arrays.asList(item.getCountry().split(","));
            if (CollectionUtils.isNotEmpty(countryList) && CollectionUtils.containsAny(existCountryList, countryList)) {
                templateConfigDOList.add(item);
            }
        }

        List<Long> itemTemplateIdList = templateConfigDOList.stream().map(HrmsSalaryItemTemplateConfigDO::getId).collect(Collectors.toList());
        List<HrmsSalaryItemGroupConfigDO> itemGroupConfigDOList = hrmsSalaryItemGroupConfigManage.selectItemGroupConfigByTemplateIdList(itemTemplateIdList).stream()
                .sorted(Comparator.comparing(HrmsSalaryItemGroupConfigDO::getItemGroupSort)).collect(Collectors.toList());
        List<Long> itemGroupIdList = itemGroupConfigDOList.stream().map(HrmsSalaryItemGroupConfigDO::getId).collect(Collectors.toList());
        List<HrmsSalaryItemGroupRelationDO> itemGroupRelationDOList = hrmsSalaryItemGroupRelationManage.selectItemGroupRelationByGroupIdList(itemGroupIdList).stream()
                .sorted(Comparator.comparing(HrmsSalaryItemGroupRelationDO::getItemConfigSort)).collect(Collectors.toList());
        List<Long> itemConfigIdList = itemGroupRelationDOList.stream().map(HrmsSalaryItemGroupRelationDO::getItemConfigId).collect(Collectors.toList());
        List<HrmsSalaryItemConfigDO> itemConfigDOList = hrmsSalaryItemConfigManage.selectItemConfigByIdList(itemConfigIdList);
        Map<Long, List<HrmsSalaryItemConfigDO>> itemConfigMaps = itemConfigDOList.stream().collect(Collectors.groupingBy(HrmsSalaryItemConfigDO::getId));

        Map<Long, HrmsSalaryItemTemplateConfigDO> templateConfigMap = templateConfigDOList.stream().collect(Collectors.toMap(HrmsSalaryItemTemplateConfigDO::getId, item -> item, (k1, k2) -> k1));

        List<SalaryItemTemplateConfigCountryDetailVO> resultList = new ArrayList<>();
        for (Map.Entry<Long, HrmsSalaryItemTemplateConfigDO> entry : templateConfigMap.entrySet()) {
            HrmsSalaryItemTemplateConfigDO value = entry.getValue();
            if (value == null) {
                continue;
            }
            SalaryItemTemplateConfigCountryDetailVO detailVO = new SalaryItemTemplateConfigCountryDetailVO();
            detailVO.setCountry(value.getCountry());

            List<SalaryItemTemplateConfigDetailVO> templateConfigDetailVOList = new ArrayList<>();
            SalaryItemTemplateConfigDetailVO itemTemplateConfigDetailVO = new SalaryItemTemplateConfigDetailVO();
            detailDataBuild(itemTemplateConfigDetailVO, value, itemGroupConfigDOList, itemGroupRelationDOList, itemConfigMaps);
            templateConfigDetailVOList.add(itemTemplateConfigDetailVO);

            detailVO.setTemplateConfigDetailVOList(templateConfigDetailVOList);
            resultList.add(detailVO);
        }
        return resultList;
    }


    private void templateAddDataBuild(SalaryItemTemplateConfigAddParam param, HrmsSalaryItemTemplateConfigBO templateConfigBO) {
        if (param.getItemTemplateName().contains("copy")) {
            List<HrmsSalaryItemTemplateConfigDO> templateConfigByNameList = hrmsSalaryItemTemplateConfigManage.selectItemTemplateConfigByName(param.getItemTemplateName().replaceAll("-copy", ""));
            if (CollectionUtils.isNotEmpty(templateConfigByNameList)) {
                String name = templateConfigByNameList.get(0).getItemTemplateName();
                for (HrmsSalaryItemTemplateConfigDO item : templateConfigByNameList) {
                    if (item.getItemTemplateName().length() > name.length()) {
                        name = item.getItemTemplateName();
                    }
                }
                param.setItemTemplateName(name + "-copy");
            }
        }
        HrmsSalaryItemTemplateConfigDO templateConfigDO = new HrmsSalaryItemTemplateConfigDO();
        templateConfigDO.setId(hrmsIdWorker.nextId());
        templateConfigDO.setItemTemplateName(param.getItemTemplateName());
        templateConfigDO.setCountry(param.getCountry());
        this.fillDOInsert(templateConfigDO);

        List<HrmsSalaryItemGroupConfigDO> groupConfigDOList = new ArrayList<>();
        List<HrmsSalaryItemGroupRelationDO> groupRelationDOList = new ArrayList<>();
        for (SalaryItemGroupConfigParam groupConfigParam : param.getItemGroupConfigParamList()) {
            HrmsSalaryItemGroupConfigDO groupConfigDO = new HrmsSalaryItemGroupConfigDO();
            groupConfigDO.setId(hrmsIdWorker.nextId());
            groupConfigDO.setItemTemplateId(templateConfigDO.getId());
            groupConfigDO.setItemGroupName(groupConfigParam.getItemGroupName());
            groupConfigDO.setItemGroupSort(groupConfigParam.getItemGroupSort());
            groupConfigDO.setRemark(groupConfigParam.getRemark());
            groupConfigDO.setDataSource(groupConfigParam.getDataSource());
            this.fillDOInsert(groupConfigDO);
            groupConfigDOList.add(groupConfigDO);
            for (SalaryItemGroupRelationParam relationParam : groupConfigParam.getSalaryItemGroupRelationParamList()) {
                HrmsSalaryItemGroupRelationDO relationDO = new HrmsSalaryItemGroupRelationDO();
                relationDO.setId(hrmsIdWorker.nextId());
                relationDO.setItemGroupId(groupConfigDO.getId());
                relationDO.setItemConfigId(relationParam.getItemConfigId());
                relationDO.setItemConfigNo(relationParam.getItemConfigNo());
                relationDO.setItemConfigSort(relationParam.getItemConfigSort());
                relationDO.setItemConfigValue(relationParam.getItemConfigValue());
                relationDO.setItemGroupFormula(CollectionUtils.isEmpty(relationParam.getStageParamList()) ? null : JSONObject.toJSONString(relationParam.getStageParamList()));
                this.fillDOInsert(relationDO);
                groupRelationDOList.add(relationDO);
            }
        }

        templateConfigBO.setTemplateConfigDO(templateConfigDO);
        templateConfigBO.setGroupConfigDOList(groupConfigDOList);
        templateConfigBO.setGroupRelationDOList(groupRelationDOList);
    }


    private void templateDeleteDataBuild(Long itemTemplateId, HrmsSalaryItemTemplateConfigBO templateConfigBO) {
        List<HrmsSalaryItemTemplateConfigDO> salaryItemTemplateConfigDOList = hrmsSalaryItemTemplateConfigManage.selectItemTemplateConfigByIdList(Arrays.asList(itemTemplateId));
        if (CollectionUtils.isEmpty(salaryItemTemplateConfigDOList) || salaryItemTemplateConfigDOList.size() > 1) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_ITEM_TEMPLATE_CONFIG_IS_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_ITEM_TEMPLATE_CONFIG_IS_EMPTY.getDesc()));
        }
        List<HrmsSalaryItemGroupConfigDO> itemGroupConfigDOList = hrmsSalaryItemGroupConfigManage.selectItemGroupConfigByTemplateIdList(Arrays.asList(salaryItemTemplateConfigDOList.get(0).getId()));
        List<Long> itemGroupIdList = itemGroupConfigDOList.stream().map(HrmsSalaryItemGroupConfigDO::getId).collect(Collectors.toList());
        List<HrmsSalaryItemGroupRelationDO> itemGroupRelationDOList = hrmsSalaryItemGroupRelationManage.selectItemGroupRelationByGroupIdList(itemGroupIdList);
        salaryItemTemplateConfigDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            this.fillDOUpdate(item);
        });
        itemGroupConfigDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            this.fillDOUpdate(item);
        });
        itemGroupRelationDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            this.fillDOUpdate(item);
        });

        templateConfigBO.setTemplateConfigDO(salaryItemTemplateConfigDOList.get(0));
        templateConfigBO.setGroupConfigDOList(itemGroupConfigDOList);
        templateConfigBO.setGroupRelationDOList(itemGroupRelationDOList);
    }

    private void detailDataBuild(SalaryItemTemplateConfigDetailVO detailVO, HrmsSalaryItemTemplateConfigDO templateConfigDO,
                                 List<HrmsSalaryItemGroupConfigDO> itemGroupConfigDOList, List<HrmsSalaryItemGroupRelationDO> itemGroupRelationDOList, Map<Long, List<HrmsSalaryItemConfigDO>> itemConfigMaps) {
        detailVO.setItemTemplateConfigId(templateConfigDO.getId());
        detailVO.setItemTemplateName(templateConfigDO.getItemTemplateName());
        detailVO.setCountry(templateConfigDO.getCountry());
        detailVO.setLastUpdDate(templateConfigDO.getLastUpdDate());
        detailVO.setLastUpdUserName(templateConfigDO.getLastUpdUserName());
        detailVO.setLastUpdUserCode(templateConfigDO.getLastUpdUserCode());
        List<SalaryItemGroupConfigDTO> salaryItemGroupConfigDTOList = new ArrayList<>();
        detailVO.setSalaryItemGroupConfigDTOList(salaryItemGroupConfigDTOList);
        for (HrmsSalaryItemGroupConfigDO groupConfigDO : itemGroupConfigDOList) {
            if (!groupConfigDO.getItemTemplateId().equals(templateConfigDO.getId())) {
                continue;
            }
            SalaryItemGroupConfigDTO groupConfigDTO = new SalaryItemGroupConfigDTO();
            groupConfigDTO.setItemGroupConfigId(groupConfigDO.getId());
            groupConfigDTO.setItemGroupName(groupConfigDO.getItemGroupName());
            groupConfigDTO.setItemGroupSort(groupConfigDO.getItemGroupSort());
            groupConfigDTO.setDataSource(groupConfigDO.getDataSource());
            groupConfigDTO.setRemark(groupConfigDO.getRemark());
            List<SalaryItemGroupRelationDTO> salaryItemGroupRelationDTOList = new ArrayList<>();
            groupConfigDTO.setSalaryItemGroupRelationDTOList(salaryItemGroupRelationDTOList);
            salaryItemGroupConfigDTOList.add(groupConfigDTO);
            List<HrmsSalaryItemGroupRelationDO> existItemGroupRelationDOList = itemGroupRelationDOList.stream()
                    .filter(item -> item.getItemGroupId().equals(groupConfigDO.getId()))
                    .sorted(Comparator.comparing(HrmsSalaryItemGroupRelationDO::getItemConfigSort)).collect(Collectors.toList());
            for (HrmsSalaryItemGroupRelationDO relationDO : existItemGroupRelationDOList) {
                List<HrmsSalaryItemConfigDO> existItemConfigDOList = itemConfigMaps.get(relationDO.getItemConfigId());
                if (CollectionUtils.isEmpty(existItemConfigDOList)) {
                    continue;
                }
                SalaryItemGroupRelationDTO relationDTO = BeanUtils.convert(existItemConfigDOList.get(0), SalaryItemGroupRelationDTO.class);
                relationDTO.setItemGroupRelationId(relationDO.getId());
                relationDTO.setItemConfigValue(relationDO.getItemConfigValue());

                List<SalarySchemeStageDTO> stageVOList = new ArrayList<>();
                if (StringUtils.isNotBlank(relationDO.getItemGroupFormula())) {
                    stageVOList = JSONArray.parseArray(relationDO.getItemGroupFormula(), SalarySchemeStageDTO.class);
                    for (SalarySchemeStageDTO item : stageVOList) {
                        if (StringUtils.isBlank(item.getValue()) || !StringUtils.equalsIgnoreCase(item.getType(), "item")) {
                            continue;
                        }
                        List<HrmsSalaryItemConfigDO> itemConfigDOList = itemConfigMaps.get(Long.valueOf(item.getValue()));
                        if (CollectionUtils.isEmpty(itemConfigDOList)) {
                            continue;
                        }
                        item.setLabel(RequestInfoHolder.isChinese() ? itemConfigDOList.get(0).getItemNameCn() : itemConfigDOList.get(0).getItemNameEn());
                    }
                }
                relationDTO.setStageVOList(CollectionUtils.isEmpty(stageVOList) ? null : stageVOList);
                relationDTO.setItemConfigId(existItemConfigDOList.get(0).getId());
                relationDTO.setItemConfigSort(relationDO.getItemConfigSort());
                salaryItemGroupRelationDTOList.add(relationDTO);
            }
        }
    }

    /**
     * 参数检查
     *
     * @param param
     */
    private void checkParam(SalaryItemTemplateConfigAddParam param) {

        //获取param.getSalarySchemeItemGroupInfoParamList的所有salarySchemeItemInfoParamList
        List<SalaryItemGroupRelationParam> collect = param.getItemGroupConfigParamList().stream().flatMap(item -> item.getSalaryItemGroupRelationParamList().stream()).collect(Collectors.toList());
        List<String> itemConfigIdList = collect.stream().map(SalaryItemGroupRelationParam::getItemConfigId).map(Objects::toString).collect(Collectors.toList());

        collect = collect.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getItemValueType(), SalaryItemValueTypeEnum.FORMULA_CALCULATION.getCode())).collect(Collectors.toList());
        for (SalaryItemGroupRelationParam item : collect) {
            List<SalarySchemeStageParam> stageParamList = item.getStageParamList();
            if (CollectionUtils.isEmpty(stageParamList)) {
                continue;
            }
            List<String> idStringList = stageParamList.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getType(), "item")).map(SalarySchemeStageParam::getValue).collect(Collectors.toList());
            for (String id : idStringList) {
                if (!itemConfigIdList.contains(id)) {
                    throw BusinessException.get(HrmsErrorCodeEnums.SALARY_ITEM_FORMULA_WRONG.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_ITEM_FORMULA_WRONG.getDesc(), RequestInfoHolder.isChinese() ? item.getItemNameCn() : item.getItemNameEn()));
                }
            }
        }

    }

    private void templateAddDataCheck(SalaryItemTemplateConfigAddParam param) {
        List<SalaryItemGroupConfigParam> groupConfigParamList = param.getItemGroupConfigParamList();
        List<SalaryItemGroupConfigParam> systemGroupList = groupConfigParamList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getDataSource(), SalaryConfigDataSourceEnum.SYSTEM.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(systemGroupList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.SYSTEM_GROUP_NOT_EMPTY.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SYSTEM_GROUP_NOT_EMPTY.getDesc()));
        }
        if (systemGroupList.size() > 1) {
            throw BusinessException.get(HrmsErrorCodeEnums.SYSTEM_GROUP_ONLY_ONE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SYSTEM_GROUP_ONLY_ONE.getDesc()));
        }
        //该国家和全球下的所有强制薪资项，必须都要存在
        SalaryItemConfigQuery itemConfigQuery = new SalaryItemConfigQuery();
        itemConfigQuery.setStatus(StatusEnum.ACTIVE.getCode());
        itemConfigQuery.setIsForceUse(BusinessConstant.Y);
        List<String> countryList = new ArrayList<>(Arrays.asList(param.getCountry().split(",")));
        countryList.add("ALL");
        itemConfigQuery.setCountryList(countryList);
        List<HrmsSalaryItemConfigDO> allForceUseItemList = hrmsSalaryItemConfigManage.selectItemConfigList(itemConfigQuery);

        List<Long> newItemIdList = new ArrayList<>();
        for (SalaryItemGroupConfigParam groupConfigParam : param.getItemGroupConfigParamList()) {
            for (SalaryItemGroupRelationParam relationParam : groupConfigParam.getSalaryItemGroupRelationParamList()) {
                newItemIdList.add(relationParam.getItemConfigId());
            }
        }

        for (HrmsSalaryItemConfigDO itemConfigDO : allForceUseItemList) {
            if (!newItemIdList.contains(itemConfigDO.getId())) {
                throw BusinessException.get(HrmsErrorCodeEnums.FORCE_USE_ITEM_MUST_ADD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.FORCE_USE_ITEM_MUST_ADD.getDesc()));
            }
        }
    }
}
