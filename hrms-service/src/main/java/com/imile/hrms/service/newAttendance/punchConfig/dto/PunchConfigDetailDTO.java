package com.imile.hrms.service.newAttendance.punchConfig.dto;

import com.imile.hrms.dao.newAttendance.punchConfig.dto.PunchClassConfigDTO;
import com.imile.hrms.dao.newAttendance.punchConfig.dto.PunchOverTimeConfigDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@Data
public class PunchConfigDetailDTO {

    /**
     *   主键id
     */
    private Long id;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班  FREE_WORK：自由上下班  FIXED_WORK: 固定上下班 CLASS_WORK: 按班次上班
     */
    private String punchConfigType;

    /**
     方案类型 DEFAULT:缺省方案 CUSTOM:自定义方案,缺省方案只能有一种
     */
    private Integer isDefault;

    /**
     *  打卡时间设置
     */
    List<PunchClassConfigDTO> punchTimeConfig;

    /**
     * 加班配置
     */
    private PunchOverTimeConfigDTO overtimeConfig;

    /**
     * 打卡方式设置
     */
    private String punchCardType;

    /**
     * 最大补卡天数
     */
    private Long maxRepunchDays;

    /**
     * 最大补卡次数
     */
    private Long maxRepunchNumber;


    /**
     * 适用用户 当为默认配置时，是免打卡，自定义是适用范围
     */
    private List<PunchConfigRangeDTO> applyDeptList;

    /**
     * 适用用户，缺省方案时可为空
     */
    private List<PunchConfigRangeDTO> applyUserList;


    /**
     * 当部门/用户已存在其他打卡方案时，是否强制覆盖 1：强制覆盖  0：不覆盖
     */
    private Integer isCoverOld;

    /**
     * 国家
     */
    private String country;

    /**
     * 主负责人
     */
    private String principalUserCode;

    /**
     * 子负责人
     */
    private String subUserCodes;
}
