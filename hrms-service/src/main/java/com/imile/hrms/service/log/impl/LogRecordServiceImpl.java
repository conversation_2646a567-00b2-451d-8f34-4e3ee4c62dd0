package com.imile.hrms.service.log.impl;

import com.alibaba.fastjson.JSON;
import com.github.easylog.model.EasyLogInfo;
import com.github.easylog.service.ILogRecordService;
import com.github.easylog.util.PlaceholderResolver;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.service.log.OperationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
@Slf4j
@Service
public class LogRecordServiceImpl implements ILogRecordService {

    @Resource
    private OperationRecordService operationRecordService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void record(EasyLogInfo easyLogInfo) {
        String resolve = PlaceholderResolver.getDefaultResolver().resolve(easyLogInfo.getContent(), easyLogInfo.getContentParam());
        easyLogInfo.setContent(resolve);
        log.info("操作日志:{}", JSON.toJSONString(easyLogInfo));
        if (!easyLogInfo.getSuccess() || StringUtils.isBlank(easyLogInfo.getType())) {
            return;
        }
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        threadPoolTaskExecutor.execute(() -> {
            RequestInfoHolder.setLoginInfo(userContext);
            operationRecordService.saveOperationRecord(easyLogInfo);
        });
    }
}
