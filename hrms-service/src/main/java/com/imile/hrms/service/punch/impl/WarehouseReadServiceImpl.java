package com.imile.hrms.service.punch.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.hermes.business.dto.BusZoneListDTO;
import com.imile.hrms.common.config.FaceConfiguration;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.approval.AttendanceAbnormalOperationTypeEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormAttrKeyEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormStatusEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormTypeEnum;
import com.imile.hrms.common.enums.attendance.AbnormalAttendanceStatusEnum;
import com.imile.hrms.common.enums.attendance.AttendanceAbnormalTypeEnum;
import com.imile.hrms.common.enums.punch.WarehouseAbnormalStatusEnum;
import com.imile.hrms.common.enums.punch.WarehouseAttendanceStatusEnum;
import com.imile.hrms.common.enums.punch.WarehouseStatusEnum;
import com.imile.hrms.common.enums.punch.WarehouseTypeEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.CommonUtil;
import com.imile.hrms.common.util.DateFormatterUtil;
import com.imile.hrms.common.util.PageUtil;
import com.imile.hrms.dao.approval.dao.HrmsApplicationFormAttrDao;
import com.imile.hrms.dao.approval.dao.HrmsApplicationFormDao;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormAttrDO;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormDO;
import com.imile.hrms.dao.approval.query.ApplicationFormQuery;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalAttendanceDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.dao.HrmsFaceRecordDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseAttendanceConfigDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseBlackListDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailAbnormalDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseRecordDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseVendorClassesConfirmDao;
import com.imile.hrms.dao.punch.model.HrmsFaceRecordDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseAttendanceConfigDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseBlackListDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailAbnormalDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseRecordDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseVendorClassesConfirmDO;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.dao.punch.param.WarehouseRecordParam;
import com.imile.hrms.dao.punch.param.WarehouseVendorClassesConfirmParam;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.integration.hermes.service.ZoneService;
import com.imile.hrms.integration.hermes.vo.CountryDTO;
import com.imile.hrms.integration.ipep.OssApiVo;
import com.imile.hrms.manage.user.UserExtendAttrManage;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.punch.WarehouseBaseService;
import com.imile.hrms.service.punch.WarehouseOcService;
import com.imile.hrms.service.punch.WarehouseReadService;
import com.imile.hrms.service.punch.WarehouseSupplierService;
import com.imile.hrms.service.punch.param.warehouse.GetVendorConfirmContentParam;
import com.imile.hrms.service.punch.param.warehouse.GetVendorListByOcListParam;
import com.imile.hrms.service.punch.param.warehouse.NoBingShiftReportParam;
import com.imile.hrms.service.punch.param.warehouse.ReportParam;
import com.imile.hrms.service.punch.param.warehouse.SimpleReportParam;
import com.imile.hrms.service.punch.param.warehouse.StatisticVendorParam;
import com.imile.hrms.service.punch.param.warehouse.WpmDataStatisticsParam;
import com.imile.hrms.service.punch.vo.warehouse.BusZoneListVO;
import com.imile.hrms.service.punch.vo.warehouse.DataCountVO;
import com.imile.hrms.service.punch.vo.warehouse.DataStatisticsBlackListVO;
import com.imile.hrms.service.punch.vo.warehouse.DataStatisticsDetailsVO;
import com.imile.hrms.service.punch.vo.warehouse.DataStatisticsVO;
import com.imile.hrms.service.punch.vo.warehouse.DateDetailReportVO;
import com.imile.hrms.service.punch.vo.warehouse.DateReportSimpleVO;
import com.imile.hrms.service.punch.vo.warehouse.DateReportVO;
import com.imile.hrms.service.punch.vo.warehouse.GetVendorConfirmContentResultVO;
import com.imile.hrms.service.punch.vo.warehouse.MonthReportVO;
import com.imile.hrms.service.punch.vo.warehouse.OcVO;
import com.imile.hrms.service.punch.vo.warehouse.StatisticsVendorResultVO;
import com.imile.hrms.service.punch.vo.warehouse.StatisticsVendorUserResultVO;
import com.imile.hrms.service.punch.vo.warehouse.VendorClassesConfirmVO;
import com.imile.hrms.service.punch.vo.warehouse.VendorDataCountVO;
import com.imile.hrms.service.punch.vo.warehouse.VendorVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehouseRecordVO;
import com.imile.hrms.service.user.result.UserCertificateBO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内考勤读服务
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@Slf4j
@Service
public class WarehouseReadServiceImpl extends WarehouseBaseService implements WarehouseReadService {

    @Resource
    private ZoneService zoneService;

    @Resource
    private CountryService countryService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private HrmsWarehouseDetailDao hrmsWarehouseDetailDao;

    @Resource
    private HrmsWarehouseRecordDao hrmsWarehouseRecordDao;

    @Resource
    private HrmsWarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private HrmsFaceRecordDao faceRecordDao;

    @Resource
    private HrmsApplicationFormDao applicationFormDao;

    @Resource
    private HrmsApplicationFormAttrDao applicationFormAttrDao;

    @Resource
    private HrmsEmployeeAbnormalAttendanceDao abnormalAttendanceDao;

    @Resource
    private HrmsEmployeeAbnormalOperationRecordDao abnormalOperationRecordDao;

    @Resource
    private HrmsWarehouseAttendanceConfigDao warehouseAttendanceConfigDao;

    @Resource
    private HrmsWarehouseVendorClassesConfirmDao warehouseVendorClassesConfirmDao;

    @Resource
    private FaceConfiguration faceConfiguration;

    @Resource
    private HrmsWarehouseBlackListDao hrmsWarehouseBlackListDao;

    @Resource
    private UserCertificateService userCertificateService;

    @Resource
    private HrmsProperties hrmsProperties;

    @Resource
    private UserExtendAttrManage userExtendAttrManage;

    @Override
    public List<BusZoneListVO> getCountryList() {
        List<HrmsEntDeptDO> entDeptList = warehouseOcService.getEntDeptList(RequestInfoHolder.getUserCode());
        if (CollectionUtils.isEmpty(entDeptList)) {
            return Collections.emptyList();
        }
        List<String> countryList = entDeptList
                .stream()
                .map(HrmsEntDeptDO::getCountry)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        List<BusZoneListDTO> busZoneListDTOS = zoneService.getBusZoneByRegionLevel(BusinessConstant.ONE);
        if (CollectionUtils.isEmpty(busZoneListDTOS)) {
            return Collections.emptyList();
        }
        List<BusZoneListDTO> busZoneList = busZoneListDTOS.stream().filter(busZone -> countryList.contains(busZone.getZoneName())).collect(Collectors.toList());

        return BeanUtils.convert(BusZoneListVO.class, busZoneList);
    }

    @Override
    public Integer noBindShiftCount() {
        List<OcVO> ocVOList = warehouseOcService.getOcVOList();
        if (CollectionUtils.isEmpty(ocVOList)) {
            return BusinessConstant.ZERO;
        }
        return hrmsWarehouseDetailDao.noBindShiftCount(ocVOList.stream().map(OcVO::getOcId).distinct().collect(Collectors.toList()));
    }

    /**
     * 简易日报列表（不分页）
     */
    @Override
    public List<DateReportSimpleVO> simpleDateReport(SimpleReportParam param) {
        if (Objects.isNull(param.getOcId())) {
            return Collections.emptyList();
        }

        //供应商级联网点处理
        if (CollectionUtils.isEmpty(param.getVendorCodeList())) {
            GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
            ocListParam.setOcIdList(Collections.singletonList(param.getOcId()));
            List<VendorVO> vendorList = warehouseSupplierService.getVendorListByCondition(ocListParam);
            if (CollectionUtils.isNotEmpty(vendorList)) {
                param.setVendorCodeList(vendorList.stream().map(VendorVO::getVendorCode).distinct().collect(Collectors.toList()));
            }
        }

        //用户搜索 截取前200匹配用户
        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<HrmsUserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return Collections.emptyList();
        }

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setVendorCodeList(param.getVendorCodeList());
        warehouseDetailParam.setStartTime(param.getStartDate());
        warehouseDetailParam.setEndTime(param.getEndDate());
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setClassId(param.getClassId());
        if (Objects.equals(BusinessConstant.SUPPLIER, param.getType())) {
            warehouseDetailParam.setEmployeeTypeList(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
        }
        List<HrmsWarehouseDetailDO> warehouseDetailList = hrmsWarehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            return Collections.emptyList();
        }

        Map<Long, HrmsUserInfoDO> userNameMap = userInfoDao.getByUserIds(warehouseDetailList.stream().map(HrmsWarehouseDetailDO::getUserId).distinct().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailList.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = warehouseDetailList.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        Map<Long, List<HrmsWarehouseRecordDO>> recordMap = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailList.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));

        return convertSimpleDateReport(warehouseDetailList, userNameMap, ocMap, vendorMap, recordMap);
    }

    @Override
    public PaginationResult<DateReportVO> dateReport(ReportParam param) {
        if (StringUtils.isBlank(param.getCountry())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        buildReportParams(param);
        if (param.getSearchFlag() && CollectionUtils.isEmpty(param.getSearchKeyUserIdList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCityList(param.getCityList());
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorIdList(param.getVendorIdList());
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        warehouseDetailParam.setResult(param.getResult());
        warehouseDetailParam.setEmployeeTypeList(param.getEmployeeTypeList());
        warehouseDetailParam.setUserIdList(param.getSearchKeyUserIdList());
        warehouseDetailParam.setWarehouseAttendanceCode(param.getWarehouseAttendanceCode());
        warehouseDetailParam.setClassesId(param.getClassId());
        warehouseDetailParam.setPunchStatus(param.getPunchStatus());
        warehouseDetailParam.setEmploymentForm(param.getEmploymentForm());
        if (CollectionUtils.isNotEmpty(param.getConfirmStatusList())) {
            warehouseDetailParam.setConfirmStatusList(param.getConfirmStatusList());
        }
        Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseDetailDO> list = hrmsWarehouseDetailDao.selectPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<Long> userIdList = list.stream().map(HrmsWarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = list.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = list.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);
        // 出入仓时间
        List<Long> detailIdList = list.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList());
        Map<Long, Date> inRecordMap = Maps.newHashMap();
        Map<Long, Date> outRecordMap = Maps.newHashMap();
        List<HrmsWarehouseRecordDO> warehouseRecords = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(detailIdList);
        if (CollectionUtils.isNotEmpty(warehouseRecords)) {
            Map<Integer, List<HrmsWarehouseRecordDO>> recordTypeMap = warehouseRecords.stream().collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getRecordType));
            List<HrmsWarehouseRecordDO> outRecords = recordTypeMap.get(WarehouseTypeEnum.OUT.getCode());
            if (CollectionUtils.isNotEmpty(outRecords)) {
                outRecordMap = outRecords.stream().collect(Collectors.toMap(HrmsWarehouseRecordDO::getWarehouseDetailId,
                        HrmsWarehouseRecordDO::getWarehouseTime,
                        (k1, k2) -> k1.after(k2) ? k1 : k2));
            }
            List<HrmsWarehouseRecordDO> inRecords = recordTypeMap.get(WarehouseTypeEnum.IN.getCode());
            if (CollectionUtils.isNotEmpty(inRecords)) {
                inRecordMap = inRecords.stream().collect(Collectors.toMap(HrmsWarehouseRecordDO::getWarehouseDetailId,
                        HrmsWarehouseRecordDO::getWarehouseTime,
                        (k1, k2) -> k1.before(k2) ? k1 : k2));
            }
        }

        List<DateReportVO> collect = convertDateReportVOS(list, userNameMap, ocMap, vendorMap, outRecordMap, inRecordMap, warehouseRecords);
        return PageUtil.get(collect, page, param);
    }

    @Override
    public DateDetailReportVO dateReportDetail(Long id) {
        HrmsWarehouseDetailDO warehouseDetailDO = hrmsWarehouseDetailDao.selectById(id);
        if (Objects.isNull(warehouseDetailDO)) {
            return new DateDetailReportVO();
        }
        DateDetailReportVO result = new DateDetailReportVO();
        //出入仓详情
        convertWarehouseRecordList(warehouseDetailDO, result);

        List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailId(warehouseDetailDO.getId(), null);
        //考勤结果
        convertAttendanceResultAndAbnormal(warehouseDetailDO, result, warehouseDetailAbnormalList);

        //请假详情
        convertLeaveDetailList(warehouseDetailDO, result);

        //历史数据判断
        result.setHistoryData(!Objects.equals(warehouseDetailDO.getInFaceRecordId(), BusinessConstant.LONG_ZERO));

        //去处理按钮展示判断
        CountryDTO countryDTO = countryService.queryCountry(warehouseDetailDO.getCountry());
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        Date warehouseDate = DateUtil.parseDate(DateUtil.formatDate(date));
        result.setShow(warehouseDetailDO.getWarehouseDate().compareTo(warehouseDate) < 0);
        return result;
    }

    @Override
    public PaginationResult<MonthReportVO> monthReport(ReportParam param) {
        if (StringUtils.isEmpty(param.getCountry())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        buildReportParams(param);
        if (param.getSearchFlag() && CollectionUtils.isEmpty(param.getSearchKeyUserIdList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCityList(param.getCityList());
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorIdList(param.getVendorIdList());
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        warehouseDetailParam.setReportType(BusinessConstant.MONTH);
        warehouseDetailParam.setUserIdList(param.getSearchKeyUserIdList());
        warehouseDetailParam.setEmployeeTypeList(param.getEmployeeTypeList());
        warehouseDetailParam.setEmploymentForm(param.getEmploymentForm());
        Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseDetailDO> list = hrmsWarehouseDetailDao.selectPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(new ArrayList<>(), param);
        }

        List<Long> ocIdList = list.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = list.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        List<Long> userIdList = list.stream()
                .map(HrmsWarehouseDetailDO::getUserId)
                .collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        WarehouseDetailParam detailParam = new WarehouseDetailParam();
        detailParam.setStartDate(param.getStartDate());
        detailParam.setEndDate(param.getEndDate());
        detailParam.setUserIdList(userIdList);
        List<HrmsWarehouseDetailDO> hrmsWarehouseDetailList = hrmsWarehouseDetailDao.selectPage(detailParam);

        Map<String, Map<Date, BigDecimal>> map = hrmsWarehouseDetailList.stream()
                .collect(Collectors.groupingBy(o -> o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId(),
                        Collectors.toMap(HrmsWarehouseDetailDO::getWarehouseDate, o -> Convert.toBigDecimal(o.getStayDuration()).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP), BigDecimal::add)));

        List<MonthReportVO> collect = list.stream()
                .map(o -> {
                    MonthReportVO monthReportVO = new MonthReportVO();
                    Map<Date, BigDecimal> orDefault = map.getOrDefault(o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId(), new HashMap<>());
                    for (int i = 0; i < 31; i++) {
                        BiConsumer<MonthReportVO, String> setter = monthReportVO.getSetter(i + 1);
                        Date localDate = DateUtil.offsetDay(param.getStartDate(), i);
                        BigDecimal bigDecimal = orDefault.get(localDate);
                        setter.accept(monthReportVO, Objects.isNull(bigDecimal) ? "0 H" : bigDecimal.stripTrailingZeros().toPlainString() + " H");
                    }
                    monthReportVO.setCountry(o.getCountry());
                    monthReportVO.setCity(o.getCity());
                    monthReportVO.setInOcId(o.getOcId());
                    monthReportVO.setInOcName(ocMap.get(o.getOcId()));
                    monthReportVO.setInVendorId(o.getVendorId());
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(monthReportVO.getInVendorId())) {
                        monthReportVO.setInVendorName(vendorMap.get(o.getVendorCode()));
                    }

                    HrmsUserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new HrmsUserInfoDO());
                    monthReportVO.setOcId(o.getUserOcId());
                    monthReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    monthReportVO.setVendorId(o.getUserVendorId());
                    monthReportVO.setVendorName(vendorMap.get(o.getUserVendorCode()));
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(monthReportVO.getVendorId())) {
                        monthReportVO.setVendorName(vendorMap.get(o.getUserVendorCode()));
                    }
                    monthReportVO.setUserId(user.getId());
                    monthReportVO.setUserCode(user.getUserCode());
                    monthReportVO.setUserName(user.getUserName());
                    monthReportVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
                    EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.valueOfCode(user.getEmployeeType());
                    monthReportVO.setEmployeeType(RequestInfoHolder.isChinese() ? employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
                    monthReportVO.setEmploymentForm(o.getEmploymentForm());
                    return monthReportVO;
                }).collect(Collectors.toList());
        return PageUtil.get(collect, page, param);
    }

    @Override
    public List<DateReportVO> dateReportNoBingShift(NoBingShiftReportParam param) {
        WarehouseRecordParam warehouseRecordParam = new WarehouseRecordParam();
        Date startTime = DateFormatterUtil.convertDateTime(param.getAttendanceDateStart(), param.getStartTime());
        Date endTime = DateFormatterUtil.convertDateTime(param.getAttendanceDateEnd(), param.getEndTime());
        warehouseRecordParam.setStartTime(startTime);
        warehouseRecordParam.setEndTime(endTime);

        warehouseRecordParam.setOcId(param.getOcId());
        List<HrmsWarehouseRecordDO> warehouseRecordDOS = hrmsWarehouseRecordDao.selectByCondition(warehouseRecordParam);
        if (CollectionUtils.isEmpty(warehouseRecordDOS)) {
            return Collections.emptyList();
        }

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setIds(warehouseRecordDOS.stream().map(HrmsWarehouseRecordDO::getWarehouseDetailId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = hrmsWarehouseDetailDao.selectNoBindShiftByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return Collections.emptyList();
        }

        List<Long> userIdList = warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList).stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        List<Long> warehouseDetailIds = warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList());
        Map<Long, List<HrmsWarehouseRecordDO>> warehouseRecordMap = warehouseRecordDOS
                .stream()
                .filter(record -> Objects.nonNull(record.getWarehouseDetailId())
                        && warehouseDetailIds.contains(record.getWarehouseDetailId()))
                .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));

        return warehouseDetailDOS
                .stream()
                .map(warehouseDetail -> {
                    DateReportVO dateReportVO = new DateReportVO();
                    dateReportVO.setId(warehouseDetail.getId());
                    dateReportVO.setInOcId(warehouseDetail.getOcId());
                    dateReportVO.setInOcName(ocMap.get(warehouseDetail.getOcId()));
                    dateReportVO.setInVendorId(warehouseDetail.getVendorId());
                    dateReportVO.setInVendorCode(warehouseDetail.getVendorCode());
                    dateReportVO.setInVendorName(vendorMap.get(warehouseDetail.getVendorCode()));
                    dateReportVO.setUserId(warehouseDetail.getUserId());
                    dateReportVO.setUserCode(warehouseDetail.getUserCode());
                    HrmsUserInfoDO user = userNameMap.getOrDefault(warehouseDetail.getUserId(), new HrmsUserInfoDO());
                    dateReportVO.setUserName(user.getUserName());
                    dateReportVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
                    dateReportVO.setWarehouseDate(warehouseDetail.getWarehouseDate());
                    List<HrmsWarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetail.getId());
                    if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                        Optional<Date> inOptional = warehouseRecordDOList
                                .stream()
                                .filter(warehouseRecord -> Objects.equals(WarehouseTypeEnum.IN.getCode(), warehouseRecord.getRecordType()))
                                .map(HrmsWarehouseRecordDO::getWarehouseTime)
                                .findFirst();
                        inOptional.ifPresent(dateReportVO::setInTime);
                        List<HrmsWarehouseRecordDO> outRecordList = warehouseRecordDOList
                                .stream()
                                .filter(warehouseRecord -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), warehouseRecord.getRecordType()))
                                .sorted(Comparator.comparing(HrmsWarehouseRecordDO::getWarehouseTime, Comparator.reverseOrder()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(outRecordList)) {
                            dateReportVO.setOutTime(outRecordList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime());
                        }
                    }
                    return dateReportVO;
                }).collect(Collectors.toList());
    }

    @Override
    public DataStatisticsVO dataStatistics(WpmDataStatisticsParam param) {
        DataStatisticsVO dataStatisticsVO = new DataStatisticsVO();
        List<HrmsWarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(param.getWarehouseDate()), param.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            dataStatisticsVO.setIsSegmentedCalculation(Boolean.TRUE);
        }
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setClassId(param.getClassesId());
        List<HrmsWarehouseDetailDO> warehouseDetailDOList = hrmsWarehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return dataStatisticsVO;
        }
        List<Long> warehouseDetailIds = warehouseDetailDOList
                .stream()
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());
        List<HrmsWarehouseRecordDO> warehouseRecordDOS = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIds);

        List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = null;
        List<Long> abnormalWarehouseIds = warehouseDetailDOList
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouse.getAttendanceStatus()))
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(abnormalWarehouseIds)) {
            warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(abnormalWarehouseIds);
        }
        return convertDataStatisticsVO(dataStatisticsVO, warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS);
    }


    @Override
    public PaginationResult<DataStatisticsDetailsVO> dataStatisticsDetails(WpmDataStatisticsParam param) {
        WarehouseDetailParam detailParam = new WarehouseDetailParam();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassesId(param.getClassesId());
        detailParam.setEmployeeTypeList(param.getEmployeeTypeList());
        Set<Long> warehouseIdList = new HashSet<>();
        Set<Long> recordWarehouseIdList = new HashSet<>();
        Set<Long> abnormalWarehouseIdList = new HashSet<>();
        if (StringUtils.isNotEmpty(param.getWarehouseType())) {
            if (Objects.equals(BusinessConstant.IN, param.getWarehouseType())) {
                detailParam.setRecordType(WarehouseTypeEnum.IN.getCode());
                List<HrmsWarehouseDetailDO> warehouseDetailDOS = hrmsWarehouseDetailDao.selectJoinRecordList(detailParam);
                if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
                    return PaginationResult.get(Collections.emptyList(), param);
                }
                recordWarehouseIdList.addAll(warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toSet()));
            } else {
                detailParam.setWarehouseStatus(covertWarehouseStatus(param.getWarehouseType()));
            }
        }

        if (StringUtils.isNotEmpty(param.getAbnormalType())) {
            detailParam.setAbnormalType(param.getAbnormalType());
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = hrmsWarehouseDetailDao.selectJoinAbnormalList(detailParam);
            if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
            List<Long> warehouseDetailIds = warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
            Map<Integer, List<HrmsWarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds)
                    .stream()
                    .filter(abnormal -> Objects.equals(param.getAbnormalType(), abnormal.getAbnormalType()))
                    .collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getProcessed));

            List<HrmsWarehouseDetailAbnormalDO> processedList = warehouseAbnormalMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
            if (CollectionUtils.isNotEmpty(processedList)) {
                Map<Long, Long> abnormalIdMap = processedList
                        .stream()
                        .collect(Collectors.toMap(HrmsWarehouseDetailAbnormalDO::getAbnormalId, HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId));
                if (CollectionUtils.isNotEmpty(abnormalIdMap.keySet())) {
                    List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalIdMap.keySet()));
                    List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                            .stream()
                            .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                        List<Long> confirmAbnormalWarehouseDetailIds = new ArrayList<>();
                        for (HrmsEmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                            if (Objects.nonNull(abnormalIdMap.get(abnormalOperationRecord.getAbnormalId()))) {
                                confirmAbnormalWarehouseDetailIds.add(abnormalIdMap.get(abnormalOperationRecord.getAbnormalId()));
                            }
                        }
                        if (CollectionUtils.isNotEmpty(confirmAbnormalWarehouseDetailIds)) {
                            abnormalWarehouseIdList.addAll(confirmAbnormalWarehouseDetailIds);
                        }
                    }
                    Set<Long> durationAbnormalDetailIds = processedList
                            .stream()
                            .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                            .map(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId)
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(durationAbnormalDetailIds)) {
                        abnormalWarehouseIdList.addAll(durationAbnormalDetailIds);
                    }
                }
            }

            List<HrmsWarehouseDetailAbnormalDO> processingList = warehouseAbnormalMap.get(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
            if (CollectionUtils.isNotEmpty(processingList)) {
                abnormalWarehouseIdList.addAll(processingList.stream().map(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId).distinct().collect(Collectors.toList()));
            }

            if (CollectionUtils.isEmpty(abnormalWarehouseIdList)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
        }

        if (CollectionUtils.isNotEmpty(recordWarehouseIdList) && CollectionUtils.isNotEmpty(abnormalWarehouseIdList)) {
            warehouseIdList.addAll(recordWarehouseIdList);
            warehouseIdList.retainAll(abnormalWarehouseIdList);
        } else if (CollectionUtils.isNotEmpty(recordWarehouseIdList)) {
            warehouseIdList.addAll(recordWarehouseIdList);
        } else {
            warehouseIdList.addAll(abnormalWarehouseIdList);
        }

        detailParam.setIds(warehouseIdList);
        Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseDetailDO> list = hrmsWarehouseDetailDao.selectDataStatisticsPage(detailParam);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIds = list.stream().map(HrmsWarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userNameMap = userInfoDao.getByUserIds(userIds)
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> warehouseIds = list.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<HrmsWarehouseRecordDO>> recordMap = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));

        Map<Long, List<HrmsWarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId));
        List<DataStatisticsDetailsVO> result = list.stream().map(item -> {
            DataStatisticsDetailsVO statisticsDetailsVO = new DataStatisticsDetailsVO();
            statisticsDetailsVO.setId(item.getId());
            statisticsDetailsVO.setAttendanceStatusCode(item.getAttendanceStatus());
            HrmsUserInfoDO userInfoDO = userNameMap.getOrDefault(item.getUserId(), new HrmsUserInfoDO());
            statisticsDetailsVO.setUserCode(userInfoDO.getUserCode());
            statisticsDetailsVO.setUserName(userInfoDO.getUserName());
            statisticsDetailsVO.setUserPhotoKey(userInfoDO.getProfilePhotoUrl());
            statisticsDetailsVO.setActualAttendanceTime(item.getActualAttendanceTime());
            if ((item.getActualAttendanceTime().compareTo(item.getRequiredAttendanceTime()) > -1 && item.getActualWorkingHours().compareTo(item.getLegalWorkingHours()) > -1)
                    || Objects.equals(WarehouseAttendanceStatusEnum.NORMAL.getCode(), item.getAttendanceStatus())) {
                statisticsDetailsVO.setFullAttendance(BusinessConstant.Y);
            } else {
                statisticsDetailsVO.setFullAttendance(BusinessConstant.N);
            }

            if (CollectionUtils.isNotEmpty(recordMap.get(item.getId()))) {
                List<HrmsWarehouseRecordDO> warehouseRecordDOList = recordMap.get(item.getId());
                Optional<HrmsWarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).findFirst();
                inOptional.ifPresent(warehouseRecordDO -> statisticsDetailsVO.setClockInTime(warehouseRecordDO.getWarehouseTime()));
                List<HrmsWarehouseRecordDO> outList = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                        .sorted(Comparator.comparing(HrmsWarehouseRecordDO::getWarehouseTime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outList)) {
                    statisticsDetailsVO.setClockOutTime(outList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime());
                }
            }

            if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), item.getAttendanceStatus()) && CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(item.getId()))) {
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalList = new ArrayList<>();
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), abnormal.getProcessed())).map(abnormal -> {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                            abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                            return abnormalVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalVOList)) {
                    abnormalList.addAll(abnormalVOList);
                }

                List<HrmsWarehouseDetailAbnormalDO> abnormalProcessedList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PROCESSED.getCode(), abnormal.getProcessed()))
                        .collect(Collectors.toList());

                Map<Long, HrmsWarehouseDetailAbnormalDO> abnormalMap = abnormalProcessedList
                        .stream()
                        .collect(Collectors.toMap(HrmsWarehouseDetailAbnormalDO::getAbnormalId, Function.identity()));
                if (CollectionUtils.isNotEmpty(abnormalMap.keySet())) {
                    List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalMap.keySet()));
                    List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                            .stream()
                            .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                        List<DataStatisticsDetailsVO.AbnormalVO> confirmAbnormalList = new ArrayList<>();
                        for (HrmsEmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                            HrmsWarehouseDetailAbnormalDO warehouseDetailAbnormalDO = abnormalMap.get(abnormalOperationRecord.getAbnormalId());
                            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                                DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                                abnormalVO.setAbnormalId(warehouseDetailAbnormalDO.getAbnormalId());
                                abnormalVO.setAbnormalType(warehouseDetailAbnormalDO.getAbnormalType());
                                confirmAbnormalList.add(abnormalVO);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(confirmAbnormalList)) {
                            abnormalList.addAll(confirmAbnormalList);
                        }
                    } else {
                        List<DataStatisticsDetailsVO.AbnormalVO> abnormalDurationList = abnormalProcessedList
                                .stream()
                                .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                                .map(abnormal -> {
                                    DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                                    abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                                    abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                                    return abnormalVO;
                                }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(abnormalDurationList)) {
                            abnormalList.addAll(abnormalDurationList);
                        }
                    }
                }
                statisticsDetailsVO.setAbnormalVOList(abnormalList);
            }

            if (Lists.newArrayList(WarehouseAttendanceStatusEnum.INIT.getCode(), WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode()).contains(item.getAttendanceStatus())
                    && (Objects.nonNull(param.getAttendanceTime()) && DateUtil.between(param.getWarehouseDate(), param.getAttendanceTime(), DateUnit.HOUR) < 24)) {
                statisticsDetailsVO.setQuickOut(Boolean.TRUE);
            }

            return statisticsDetailsVO;
        }).collect(Collectors.toList());

        return PageUtil.get(result, page, param);
    }

    @Override
    public PaginationResult<DataStatisticsBlackListVO> dataStatisticsBlackList(WpmDataStatisticsParam param) {
        // 黑名单数量计算
        LambdaQueryWrapper<HrmsWarehouseBlackListDO> queryWrapper = Wrappers.lambdaQuery(HrmsWarehouseBlackListDO.class);
        queryWrapper.eq(HrmsWarehouseBlackListDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(param.getWarehouseDate() != null, HrmsWarehouseBlackListDO::getWarehouseDate, param.getWarehouseDate());
        queryWrapper.eq(param.getOcId() != null, HrmsWarehouseBlackListDO::getOcId, param.getOcId());
        queryWrapper.eq(param.getClassesId() != null, HrmsWarehouseBlackListDO::getClassesId, param.getClassesId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getEmployeeTypeList()), HrmsWarehouseBlackListDO::getEmployeeType, param.getEmployeeTypeList());
        queryWrapper.in(StringUtils.isNotBlank(param.getVendorCode()), HrmsWarehouseBlackListDO::getVendorCode, param.getVendorCode());
        Page<HrmsWarehouseBlackListDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseBlackListDO> list = hrmsWarehouseBlackListDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<Long> userIds = Lists.newArrayList();
        List<String> vendorCodes = Lists.newArrayList();
        for (HrmsWarehouseBlackListDO item : list) {
            userIds.add(item.getUserId());
            vendorCodes.add(item.getVendorCode());
        }
        List<HrmsUserInfoDO> userList = userInfoDao.getByUserIds(userIds);
        if (CollectionUtils.isEmpty(userList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        Map<Long, HrmsUserInfoDO> userNameMap = userList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodes);
        //获取用户所有的证件类型
        String certificateTypeCode = StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.BRA.getCode(), userList.get(0).getLocationCountry()) ? CertificateTypeEnum.ID_CARD.getCode() : CertificateTypeEnum.INE.getCode();

        List<UserCertificateBO> userCertificateList = userCertificateService.getUserCertificateList(userIds, certificateTypeCode);
        Map<Long, UserCertificateBO> certificateMap = CollectionUtils.isEmpty(userCertificateList) ? Maps.newHashMap() :
                userCertificateList.stream().collect(Collectors.toMap(UserCertificateBO::getUserId, Function.identity(), (v1, v2) -> v1));
        List<DataStatisticsBlackListVO> result = list.stream().collect(Collectors.toMap(HrmsWarehouseBlackListDO::getUserCode, o -> o, (k1, k2) -> k1)).values().stream().map(item -> {
            DataStatisticsBlackListVO blackListVO = new DataStatisticsBlackListVO();
            blackListVO.setId(item.getId());
            if (userNameMap.containsKey(item.getUserId())) {
                HrmsUserInfoDO userInfoDO = userNameMap.get(item.getUserId());
                blackListVO.setUserCode(userInfoDO.getUserCode());
                blackListVO.setUserName(userInfoDO.getUserName());
                blackListVO.setUserPhotoKey(userInfoDO.getProfilePhotoUrl());

            }
            if (certificateMap.containsKey(item.getUserId())) {
                UserCertificateBO userCertificateBO = certificateMap.get(item.getUserId());
                blackListVO.setCertificateTypeCode(userCertificateBO.getCertificateTypeCode());
                blackListVO.setCertificateCode(userCertificateBO.getCertificateCode());
            }
            if (vendorMap.containsKey(item.getVendorCode())) {
                blackListVO.setVendorName(vendorMap.get(item.getVendorCode()));
            }
            blackListVO.setReason(item.getReason());
            blackListVO.setType(item.getType());
            return blackListVO;
        }).sorted(Comparator.comparing(DataStatisticsBlackListVO::getId)).collect(Collectors.toList());
        return PageUtil.get(result, page, param);
    }

    @Override
    public StatisticsVendorResultVO statisticVendor(StatisticVendorParam param) {
        if (param.getClassesId() == null || StringUtils.isBlank(param.getVendorCode())) {
            return new StatisticsVendorResultVO();
        }
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setClassId(param.getClassesId());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        List<HrmsWarehouseDetailDO> warehouseDetailDOList = hrmsWarehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return new StatisticsVendorResultVO();
        }
        List<Long> warehouseDetailIds = warehouseDetailDOList
                .stream()
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());
        WarehouseRecordParam recordParam = new WarehouseRecordParam();
        recordParam.setWarehouseDetailIds(warehouseDetailIds);
        List<HrmsWarehouseRecordDO> warehouseRecordDOS = hrmsWarehouseRecordDao.selectByCondition(recordParam);
        return convertStatisticsVendorResultVO(warehouseDetailDOList, warehouseRecordDOS);
    }

    @Override
    public PaginationResult<StatisticsVendorUserResultVO> statisticVendorUser(StatisticVendorParam param) {
        if (param.getClassesId() == null || StringUtils.isBlank(param.getVendorCode()) || StringUtils.isBlank(param.getWarehouseType())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        WarehouseDetailParam detailParam = new WarehouseDetailParam();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassesId(param.getClassesId());
        detailParam.setEmployeeTypeList(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
        Set<Long> recordWarehouseIdList = new HashSet<>();
        if (StringUtils.isNotEmpty(param.getWarehouseType())) {
            if (Objects.equals(BusinessConstant.IN, param.getWarehouseType())) {
                detailParam.setRecordType(WarehouseTypeEnum.IN.getCode());
                List<HrmsWarehouseDetailDO> warehouseDetailDOS = hrmsWarehouseDetailDao.selectJoinRecordList(detailParam);
                if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
                    return PaginationResult.get(Collections.emptyList(), param);
                }
                recordWarehouseIdList.addAll(warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toSet()));
            } else {
                detailParam.setWarehouseStatus(covertWarehouseStatus(param.getWarehouseType()));
            }
        }
        if (CollectionUtils.isNotEmpty(recordWarehouseIdList)) {
            detailParam.setIds(recordWarehouseIdList);
        }

        Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseDetailDO> list = hrmsWarehouseDetailDao.selectDataStatisticsPage(detailParam);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIds = list.stream().map(HrmsWarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userNameMap = userInfoDao.getByUserIds(userIds)
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> warehouseIds = list.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<HrmsWarehouseRecordDO>> recordMap = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));

        Map<Long, List<HrmsWarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId));

        List<StatisticsVendorUserResultVO> result = list.stream().map(item -> {
            StatisticsVendorUserResultVO detail = new StatisticsVendorUserResultVO();
            HrmsUserInfoDO userInfoDO = userNameMap.getOrDefault(item.getUserId(), new HrmsUserInfoDO());
            detail.setUserCode(userInfoDO.getUserCode());
            detail.setUserName(userInfoDO.getUserName());
            detail.setUserPhotoKey(userInfoDO.getProfilePhotoUrl());
            detail.setActualAttendanceTime(item.getActualAttendanceTime());

            if ((item.getActualAttendanceTime().compareTo(item.getRequiredAttendanceTime()) > -1 && item.getActualWorkingHours().compareTo(item.getLegalWorkingHours()) > -1)
                    || Objects.equals(WarehouseAttendanceStatusEnum.NORMAL.getCode(), item.getAttendanceStatus())) {
                detail.setFullAttendance(BusinessConstant.Y);
            } else {
                detail.setFullAttendance(BusinessConstant.N);
            }

            if (CollectionUtils.isNotEmpty(recordMap.get(item.getId()))) {
                List<HrmsWarehouseRecordDO> warehouseRecordDOList = recordMap.get(item.getId());
                Optional<HrmsWarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).findFirst();
                inOptional.ifPresent(warehouseRecord -> detail.setClockInTime(warehouseRecord.getWarehouseTime()));
                List<HrmsWarehouseRecordDO> outList = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                        .sorted(Comparator.comparing(HrmsWarehouseRecordDO::getWarehouseTime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outList)) {
                    detail.setClockOutTime(outList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime());
                }
            }

            if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), item.getAttendanceStatus()) && CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(item.getId()))) {
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalList = new ArrayList<>();
                Set<Long> confirmAbnormalIds = new HashSet<>();
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), abnormal.getProcessed())).map(abnormal -> {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                            abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                            return abnormalVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalVOList)) {
                    abnormalList.addAll(abnormalVOList);
                }

                List<HrmsWarehouseDetailAbnormalDO> abnormalProcessedList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PROCESSED.getCode(), abnormal.getProcessed()))
                        .collect(Collectors.toList());

                Map<Long, HrmsWarehouseDetailAbnormalDO> abnormalMap = abnormalProcessedList
                        .stream()
                        .collect(Collectors.toMap(HrmsWarehouseDetailAbnormalDO::getAbnormalId, Function.identity()));
                if (CollectionUtils.isNotEmpty(abnormalMap.keySet())) {
                    List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalMap.keySet()));
                    List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                            .stream()
                            .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                        List<DataStatisticsDetailsVO.AbnormalVO> confirmAbnormalList = new ArrayList<>();
                        for (HrmsEmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                            HrmsWarehouseDetailAbnormalDO warehouseDetailAbnormalDO = abnormalMap.get(abnormalOperationRecord.getAbnormalId());
                            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                                DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                                abnormalVO.setAbnormalId(warehouseDetailAbnormalDO.getAbnormalId());
                                abnormalVO.setAbnormalType(warehouseDetailAbnormalDO.getAbnormalType());
                                confirmAbnormalList.add(abnormalVO);
                                confirmAbnormalIds.add(warehouseDetailAbnormalDO.getAbnormalId());
                            }
                        }
                        if (CollectionUtils.isNotEmpty(confirmAbnormalList)) {
                            abnormalList.addAll(confirmAbnormalList);
                        }
                    }
                }

                List<DataStatisticsDetailsVO.AbnormalVO> abnormalDurationList = abnormalProcessedList
                        .stream()
                        .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                                && !confirmAbnormalIds.contains(abnormal.getAbnormalId()))
                        .map(abnormal -> {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                            abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                            return abnormalVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalDurationList)) {
                    abnormalList.addAll(abnormalDurationList);
                }
                detail.setAbnormalVOList(abnormalList);
            }

            return detail;
        }).collect(Collectors.toList());
        return PageUtil.get(result, page, param);
    }

    @Override
    public VendorClassesConfirmVO vendorClassedConfirmDetailH5(Long id) {
        HrmsWarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO = warehouseVendorClassesConfirmDao.selectById(id);
        if (Objects.isNull(warehouseVendorClassesConfirmDO)) {
            return null;
        }
        VendorClassesConfirmVO vendorClassesConfirmVO = BeanUtils.convert(warehouseVendorClassesConfirmDO, VendorClassesConfirmVO.class);
        List<HrmsWarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(warehouseVendorClassesConfirmDO.getWarehouseDate()), warehouseVendorClassesConfirmDO.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            vendorClassesConfirmVO.setIsSegmentedCalculation(Boolean.TRUE);
        }

        return vendorClassesConfirmVO;
    }

    @Override
    public VendorClassesConfirmVO vendorClassedConfirmDetailWeb(Long id) {
        HrmsWarehouseDetailDO warehouseDetailDO = hrmsWarehouseDetailDao.selectById(id);
        if (Objects.isNull(warehouseDetailDO) || !Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetailDO.getEmployeeType())) {
            return null;
        }

        WarehouseVendorClassesConfirmParam param = new WarehouseVendorClassesConfirmParam();
        param.setWarehouseDate(warehouseDetailDO.getWarehouseDate());
        param.setVendorCode(warehouseDetailDO.getVendorCode());
        param.setOcId(warehouseDetailDO.getOcId());
        param.setClassesId(warehouseDetailDO.getClassesId());
        List<HrmsWarehouseVendorClassesConfirmDO> warehouseVendorClassesConfirmDOList = warehouseVendorClassesConfirmDao.selectByContidition(param);
        if (CollectionUtils.isEmpty(warehouseVendorClassesConfirmDOList)) {
            return null;
        }
        HrmsWarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO = warehouseVendorClassesConfirmDOList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
        VendorClassesConfirmVO result = BeanUtils.convert(warehouseVendorClassesConfirmDO, VendorClassesConfirmVO.class);
        List<HrmsWarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(warehouseDetailDO.getWarehouseDate()), warehouseDetailDO.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            result.setIsSegmentedCalculation(Boolean.TRUE);
        }
        if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouseDetailDO.getAttendanceStatus())) {
            List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailId(warehouseDetailDO.getId(), WarehouseAbnormalStatusEnum.PROCESSED.getCode());
            if (CollectionUtils.isEmpty(warehouseDetailAbnormalList)) {
                return result;
            }
            List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList = warehouseDetailAbnormalList
                    .stream()
                    .map(abnormal -> {
                        DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                        abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                        abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                        return abnormalVO;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(abnormalVOList)) {
                result.setAbnormalVOList(abnormalVOList);
            }
        }
        return result;
    }

    @Override
    public GetVendorConfirmContentResultVO getVendorConfirmContent(GetVendorConfirmContentParam param) {
        GetVendorConfirmContentResultVO result = new GetVendorConfirmContentResultVO();
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        warehouseDetailParam.setClassId(param.getClassesId());
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        List<HrmsWarehouseDetailDO> detailList = hrmsWarehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(detailList)) {
            return result;
        }

        List<Long> warehouseIdList = detailList
                .stream()
                .filter(warehouseDetail -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouseDetail.getAttendanceStatus()))
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());
        List<HrmsWarehouseDetailAbnormalDO> abnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIdList);
        if (CollectionUtils.isEmpty(abnormalList)) {
            result.setPunchCount(detailList.size());
            return result;
        }

        Integer inWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode());
        int outWithoutPunchCount;
        List<HrmsWarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(param.getWarehouseDate()), param.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            Set<Long> warehouseIds = new HashSet<>();
            result.setIsSegmentedCalculation(Boolean.TRUE);
            Integer abnormalDurationCount = abnormalDurationCount(abnormalList, warehouseIds);
            result.setAbnormalDurationNum(abnormalDurationCount);
            outWithoutPunchCount = Math.toIntExact(detailList.stream()
                    .filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())
                            && !warehouseIds.contains(warehouse.getId())).count());
        } else {
            outWithoutPunchCount = Math.toIntExact(detailList.stream().filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())).count());
            Integer latePunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LATE.getCode());
            Integer leaveEarlyPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode());
            result.setLatePunchCount(latePunchCount);
            result.setLeaveEarlyPunchCount(leaveEarlyPunchCount);
        }

        // 实际出勤 = 总记录 - 出仓未打卡 - 入仓未打卡
        result.setPunchCount(Math.max(detailList.size() - outWithoutPunchCount - inWithoutPunchCount, 0));
        result.setOutWithoutPunchCount(outWithoutPunchCount);
        result.setInWithoutPunchCount(inWithoutPunchCount);
        return result;
    }

    @Override
    public OssApiVo getOssFileUrl(String fileKey) {
        if (StringUtils.isEmpty(fileKey)) {
            return null;
        }
        String userCode = RequestInfoHolder.getUserCode();
        List<String> userCodes = Arrays.stream(hrmsProperties.getVendor().getUserCodesAuthority().split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
        if (!userCodes.contains(userCode)) {
            return null;
        }
        return ipepIntegration.getUrlByFileKey(fileKey, BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
    }

    private void buildReportParams(ReportParam param) {
        //网点权限处理
        if (CollectionUtils.isEmpty(param.getOcIdList())) {
            GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
            ocListParam.setCountry(param.getCountry());
            ocListParam.setCityList(param.getCityList());
            List<OcVO> ocList = warehouseOcService.getOcListByCondition(ocListParam);
            if (CollectionUtils.isNotEmpty(ocList)) {
                param.setOcIdList(ocList.stream().map(OcVO::getOcId).distinct().collect(Collectors.toList()));
            }
        }

        //供应商级联网点处理
        List<Long> vendorIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(param.getVendorIdList())) {
            GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
            ocListParam.setOcIdList(param.getOcIdList());
            List<VendorVO> vendorList = warehouseSupplierService.getVendorListByCondition(ocListParam);
            if (CollectionUtils.isNotEmpty(vendorList)) {
                vendorIdList.addAll(vendorList.stream().map(VendorVO::getVendorId).distinct().collect(Collectors.toList()));
            }
            //特殊逻辑 自有挂在虚拟imile供应商下
            vendorIdList.add(BusinessConstant.IMILE_VIRTUAL_VENDOR_ID);
            param.setVendorIdList(vendorIdList);
        }

        Set<Long> searchKeyUserIdList = new HashSet<>();
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<HrmsUserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            param.setSearchFlag(true);
            if (CollectionUtils.isNotEmpty(userInfoDOList)) {
                searchKeyUserIdList.addAll(userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toSet()));
            }
        }
        param.setSearchKeyUserIdList(new ArrayList<>(searchKeyUserIdList));
    }

    private StatisticsVendorResultVO convertStatisticsVendorResultVO(List<HrmsWarehouseDetailDO> warehouseDetailList, List<HrmsWarehouseRecordDO> warehouseRecords) {
        StatisticsVendorResultVO result = new StatisticsVendorResultVO();
        List<String> employeeTypeList = Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode());

        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailList
                .stream()
                .filter(warehouseDetail -> employeeTypeList.contains(warehouseDetail.getEmployeeType()))
                .collect(Collectors.toList());

        List<Long> warehouseIds = warehouseDetailDOS
                .stream()
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(warehouseRecords)) {
            Map<Long, List<HrmsWarehouseRecordDO>> warehouseRecordMap = warehouseRecords
                    .stream()
                    .filter(warehouseRecord -> warehouseIds.contains(warehouseRecord.getWarehouseDetailId()))
                    .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));

            if (MapUtils.isNotEmpty(warehouseRecordMap)) {
                Integer clockInCount = 0;
                Integer clockOutCount = 0;
                Integer waitClockOutCount = 0;
                for (HrmsWarehouseDetailDO warehouseDetailDO : warehouseDetailDOS) {
                    List<HrmsWarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetailDO.getId());
                    if (Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        waitClockOutCount++;
                    }
                    if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                        Optional<HrmsWarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(item -> Objects.equals(WarehouseTypeEnum.IN.getCode(), item.getRecordType())).findFirst();
                        if (inOptional.isPresent()) {
                            clockInCount++;
                        }
                    }
                    if (Objects.equals(WarehouseStatusEnum.OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        clockOutCount++;
                    }
                }
                result.setClockInCount(clockInCount);
                result.setClockOutCount(clockOutCount);
                result.setWaitClockOutCount(waitClockOutCount);
            }
        }
        return result;
    }

    private Integer abnormalCount(List<HrmsWarehouseDetailAbnormalDO> abnormalList, String abnormalType) {
        Map<Integer, List<HrmsWarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getProcessed));
        int result = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList()).size();
        List<HrmsWarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        List<Long> abnormalIds = processedList.stream().map(HrmsWarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
        List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
        List<Long> confirmAbnormalIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
            List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                    .stream()
                    .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
            confirmAbnormalIds = abnormalOperationRecordList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
            result += abnormalOperationRecordList.size();
        }

        if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
            List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
            long abnormalDurationCount = processedList.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                    && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count();
            result += abnormalDurationCount;
        }

        return result;
    }

    private Integer abnormalDurationCount(List<HrmsWarehouseDetailAbnormalDO> abnormalList, Set<Long> warehouseIds) {
        String abnormalType = AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode();
        Map<Integer, List<HrmsWarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getProcessed));
        List<HrmsWarehouseDetailAbnormalDO> processingAbnormalList = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList());
        int result = processingAbnormalList.size();
        Set<Long> warehouseIdList = processingAbnormalList.stream().map(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(warehouseIdList)) {
            warehouseIds.addAll(warehouseIdList);
        }
        List<HrmsWarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        Map<Long, List<HrmsWarehouseDetailAbnormalDO>> processedMap = processedList.stream().collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId));
        for (Long warehouseId : processedMap.keySet()) {
            List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = processedMap.get(warehouseId);

            List<Long> abnormalIds = warehouseDetailAbnormalDOS.stream().map(HrmsWarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
            List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
            List<Long> confirmAbnormalIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
                List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                confirmAbnormalIds = abnormalOperationRecordList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
                result += abnormalOperationRecordList.size();
                warehouseIds.add(warehouseId);
            }

            if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
                List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
                int abnormalDurationCount = Math.toIntExact(warehouseDetailAbnormalDOS.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                        && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count());
                result += abnormalDurationCount;
                if (abnormalDurationCount > 0) {
                    warehouseIds.add(warehouseId);
                }
            }
        }

        return result;
    }

    private static List<DateReportSimpleVO> convertSimpleDateReport(List<HrmsWarehouseDetailDO> warehouseDetailList,
                                                                    Map<Long, HrmsUserInfoDO> userNameMap,
                                                                    Map<Long, String> ocMap,
                                                                    Map<String, String> vendorMap,
                                                                    Map<Long, List<HrmsWarehouseRecordDO>> recordMap) {
        return warehouseDetailList.stream().map(warehouse -> {
            DateReportSimpleVO reportSimpleVO = new DateReportSimpleVO();
            reportSimpleVO.setId(warehouse.getId());
            reportSimpleVO.setOcId(warehouse.getOcId());
            reportSimpleVO.setOcName(ocMap.get(warehouse.getOcId()));
            reportSimpleVO.setVendorId(warehouse.getVendorId());
            reportSimpleVO.setVendorCode(warehouse.getVendorCode());
            reportSimpleVO.setVendorName(vendorMap.get(warehouse.getVendorCode()));
            reportSimpleVO.setUserCode(warehouse.getUserCode());
            HrmsUserInfoDO user = userNameMap.getOrDefault(warehouse.getUserId(), new HrmsUserInfoDO());
            reportSimpleVO.setUserName(user.getUserName());
            reportSimpleVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
            reportSimpleVO.setClassName(warehouse.getClassesName());
            reportSimpleVO.setWarehouseDate(warehouse.getWarehouseDate());
            if (CollectionUtils.isNotEmpty(recordMap.get(warehouse.getId()))) {
                List<HrmsWarehouseRecordDO> warehouseRecordDOList = recordMap.get(warehouse.getId());
                Optional<HrmsWarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).findFirst();
                inOptional.ifPresent(warehouseRecord -> reportSimpleVO.setInTime(warehouseRecord.getWarehouseTime()));
                List<HrmsWarehouseRecordDO> outList = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                        .sorted(Comparator.comparing(HrmsWarehouseRecordDO::getWarehouseTime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outList)) {
                    reportSimpleVO.setOutTime(outList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime());
                }
            }
            return reportSimpleVO;
        }).collect(Collectors.toList());
    }

    private static List<DateReportVO> convertDateReportVOS(List<HrmsWarehouseDetailDO> list,
                                                           Map<Long, HrmsUserInfoDO> userNameMap,
                                                           Map<Long, String> ocMap,
                                                           Map<String, String> vendorMap,
                                                           Map<Long, Date> outRecordMap,
                                                           Map<Long, Date> inRecordMap,
                                                           List<HrmsWarehouseRecordDO> warehouseRecords) {
        return list.stream()
                .map(o -> {
                    DateReportVO dateReportVO = new DateReportVO();
                    dateReportVO.setId(o.getId());
                    dateReportVO.setWarehouseDate(o.getWarehouseDate());
                    dateReportVO.setCountry(o.getCountry());
                    dateReportVO.setCity(o.getCity());
                    dateReportVO.setInOcId(o.getOcId());
                    dateReportVO.setInOcName(ocMap.get(o.getOcId()));
                    dateReportVO.setInVendorId(o.getVendorId());
                    dateReportVO.setInVendorCode(o.getVendorCode());
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(dateReportVO.getInVendorId())) {
                        dateReportVO.setInVendorName(vendorMap.get(o.getVendorCode()));
                    }
                    HrmsUserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new HrmsUserInfoDO());
                    dateReportVO.setOcId(o.getUserOcId());
                    dateReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    dateReportVO.setVendorId(o.getUserVendorId());
                    dateReportVO.setVendorCode(o.getUserVendorCode());
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(dateReportVO.getVendorId())) {
                        dateReportVO.setVendorName(vendorMap.get(o.getUserVendorCode()));
                    }
                    dateReportVO.setUserId(user.getId());
                    dateReportVO.setUserCode(user.getUserCode());
                    dateReportVO.setUserName(user.getUserName());
                    dateReportVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
                    EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.valueOfCode(o.getEmployeeType());
                    dateReportVO.setEmployeeType(RequestInfoHolder.isChinese() ? employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
                    dateReportVO.setEmployeeTypeCode(o.getEmployeeType());
                    dateReportVO.setSex(user.getSex());
                    dateReportVO.setSexCode(convertSexCode(user.getSex()));
                    dateReportVO.setSalaryDate(o.getSalaryDate());
                    dateReportVO.setClassType(o.getClassesType());
                    dateReportVO.setClassName(o.getClassesName());
                    dateReportVO.setRequiredAttendanceTime(o.getRequiredAttendanceTime());
                    dateReportVO.setLegalWorkingHours(o.getLegalWorkingHours());
                    dateReportVO.setActualAttendanceTime(o.getActualAttendanceTime());
                    dateReportVO.setActualWorkingHours(o.getActualWorkingHours());
                    dateReportVO.setAbsenceTime(o.getAbsenceTime());
                    dateReportVO.setOvertimeHours(o.getOvertimeHours());
                    dateReportVO.setWarehouseActualAttendanceTime(o.getWarehouseActualAttendanceTime());
                    dateReportVO.setResultCode(o.getAttendanceStatus());
                    dateReportVO.setOcLongitude(o.getOcLongitude());
                    dateReportVO.setOcLatitude(o.getOcLatitude());
                    dateReportVO.setWarehouseAttendanceCode(o.getWarehouseAttendanceCode());
                    dateReportVO.setConfirmStatus(o.getConfirmStatus());
                    dateReportVO.setPunchStatus(o.getPunchStatus());
                    if (outRecordMap.containsKey(o.getId())) {
                        dateReportVO.setOutTime(outRecordMap.get(o.getId()));
                    }
                    if (inRecordMap.containsKey(o.getId())) {
                        dateReportVO.setInTime(inRecordMap.get(o.getId()));
                    }
                    List<HrmsWarehouseRecordDO> warehouseRecordDOList = warehouseRecords.stream().filter(record -> Objects.equals(record.getWarehouseDetailId(), o.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                        dateReportVO.setEntryAndExitTime(convertEntryAndExitTime(warehouseRecordDOList));
                    }
                    dateReportVO.setEmploymentForm(o.getEmploymentForm());
                    return dateReportVO;
                }).collect(Collectors.toList());
    }

    private static String convertSexCode(Integer sex) {
        if (Objects.isNull(sex)) {
            return null;
        }
        return Objects.equals(BusinessConstant.ONE, sex) ? BusinessConstant.MALE : BusinessConstant.FEMALE;
    }

    private static String convertEntryAndExitTime(List<HrmsWarehouseRecordDO> warehouseRecordList) {
        List<Date> inRecordList = warehouseRecordList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).map(HrmsWarehouseRecordDO::getWarehouseTime).sorted().collect(Collectors.toList());
        List<Date> outRecordList = warehouseRecordList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType())).map(HrmsWarehouseRecordDO::getWarehouseTime).sorted().collect(Collectors.toList());
        int length = inRecordList.size() + outRecordList.size();

        Iterator<Date> inIterator = inRecordList.iterator();
        Iterator<Date> outIterator = outRecordList.iterator();

        StringBuilder result = new StringBuilder();
        Date inDate = getNextDate(inIterator);
        Date outDate = getNextDate(outIterator);

        for (int i = 0; i < length; i++) {
            if (inDate != null && (outDate == null || inDate.before(outDate))) {
                result.append(formatDate(inDate)).append(" ~ ");
                inDate = getNextDate(inIterator);
                if (outDate != null && inDate == null || (outDate != null && inDate.after(outDate))) {
                    result.append(formatDate(outDate)).append("\n");
                    outDate = getNextDate(outIterator);
                } else {
                    result.append("-\n");
                }
            } else if (outDate != null) {
                result.append("         -          ~ ").append(formatDate(outDate)).append("\n");
                outDate = getNextDate(outIterator);
            }
        }

        return result.toString();
    }

    private static Date getNextDate(Iterator<Date> iterator) {
        return iterator.hasNext() ? iterator.next() : null;
    }

    private static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateFormatterUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
        return (date != null) ? sdf.format(date) : null;
    }

    private void convertWarehouseRecordList(HrmsWarehouseDetailDO warehouseDetailDO, DateDetailReportVO result) {
        List<HrmsWarehouseRecordDO> warehouseRecordDOList = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailDO.getId()));
        if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
            List<Long> faceRecordIds = warehouseRecordDOList.stream().map(HrmsWarehouseRecordDO::getFaceRecordId).distinct().collect(Collectors.toList());
            Map<Long, HrmsFaceRecordDO> faceRecordMap = faceRecordDao.getByIdList(faceRecordIds).stream().collect(Collectors.toMap(HrmsFaceRecordDO::getId, Function.identity()));
            List<WarehouseRecordVO> warehouseRecordList = warehouseRecordDOList.stream().map(record -> {
                WarehouseRecordVO warehouseRecordVO = new WarehouseRecordVO();
                warehouseRecordVO.setId(record.getId());
                warehouseRecordVO.setWarehouseTime(record.getWarehouseTime());
                warehouseRecordVO.setRecordType(record.getRecordType());
                warehouseRecordVO.setCreateUserName(record.getCreateUserName());
                warehouseRecordVO.setOcLongitude(record.getOcLongitude());
                warehouseRecordVO.setOcLatitude(record.getOcLatitude());
                warehouseRecordVO.setDistance(record.getDistance());
                HrmsFaceRecordDO faceRecordDO = faceRecordMap.get(record.getFaceRecordId());
                if (Objects.nonNull(faceRecordDO)) {
                    WarehouseRecordVO.FaceRecognitionDetail faceRecognitionDetail = new WarehouseRecordVO.FaceRecognitionDetail();
                    faceRecognitionDetail.setRecognitionScore(faceRecordDO.getRecognitionScore());
                    faceRecognitionDetail.setFacePhotoKey(faceRecordDO.getFacePhoto());
                    faceRecognitionDetail.setRecognitionPhotoKey(faceRecordDO.getRecognitionPhoto());
                    faceRecognitionDetail.setPass(Convert.toFloat(faceRecordDO.getRecognitionScore()) >= faceConfiguration.getSimilarScore());
                    warehouseRecordVO.setFaceRecognitionDetail(faceRecognitionDetail);
                }
                return warehouseRecordVO;
            }).collect(Collectors.toList());
            result.setWarehouseRecordList(warehouseRecordList);
        }
    }

    private void convertAttendanceResultAndAbnormal(HrmsWarehouseDetailDO warehouseDetailDO,
                                                    DateDetailReportVO result,
                                                    List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS) {
        DateDetailReportVO.AttendanceResult attendanceResult = new DateDetailReportVO.AttendanceResult();
        attendanceResult.setId(warehouseDetailDO.getId());
        attendanceResult.setOcId(warehouseDetailDO.getOcId());
        attendanceResult.setClassesType(warehouseDetailDO.getClassesType());
        attendanceResult.setClassesName(warehouseDetailDO.getClassesName());
        attendanceResult.setResultCode(warehouseDetailDO.getAttendanceStatus());
        attendanceResult.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
        attendanceResult.setActualWorkingHours(warehouseDetailDO.getActualWorkingHours());
        attendanceResult.setUserCode(warehouseDetailDO.getUserCode());

        if (CollectionUtils.isEmpty(warehouseDetailAbnormalDOS)) {
            result.setAttendanceResult(attendanceResult);
            return;
        }

        //异常处理
        List<Long> abnormalIds = warehouseDetailAbnormalDOS.stream().map(HrmsWarehouseDetailAbnormalDO::getAbnormalId).distinct().collect(Collectors.toList());
        List<HrmsEmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceDao.selectAbnormalByIdList(abnormalIds);
        List<DateDetailReportVO.AttendanceAbnormal> attendanceAbnormalList = new ArrayList<>();
        for (HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
            List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordDao.selectByAbnormalList(Collections.singletonList(abnormalAttendanceDO.getId()));
            for (HrmsEmployeeAbnormalOperationRecordDO abnormalOperationRecordDO : abnormalOperationRecordDOList) {
                DateDetailReportVO.AttendanceAbnormal attendanceAbnormal = new DateDetailReportVO.AttendanceAbnormal();
                attendanceAbnormal.setAbnormalId(abnormalAttendanceDO.getId());
                attendanceAbnormal.setAbnormalType(abnormalAttendanceDO.getAbnormalType());
                attendanceAbnormal.setAbnormalStatus(abnormalAttendanceDO.getStatus());
                attendanceAbnormal.setHandleType(abnormalOperationRecordDO.getOperationType());
                attendanceAbnormal.setId(abnormalOperationRecordDO.getId());
                attendanceAbnormalList.add(attendanceAbnormal);
            }
        }

        result.setAttendanceAbnormalList(attendanceAbnormalList);

        List<DateDetailReportVO.AttendanceAbnormalType> allAttendanceAbnormalTypes = new ArrayList<>();

        //已过期
        List<HrmsEmployeeAbnormalAttendanceDO> expireAbnormalList = abnormalAttendanceDOList
                .stream()
                .filter(abnormal -> Objects.equals(BusinessConstant.N, abnormal.getIsDelete())
                        && AbnormalAttendanceStatusEnum.EXPIRED.getCode().equals(abnormal.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireAbnormalList)) {
            List<DateDetailReportVO.AttendanceAbnormalType> expireAttendanceAbnormalTypes = expireAbnormalList
                    .stream()
                    .map(abnormal -> {
                        DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                        abnormalType.setAbnormalId(abnormal.getId());
                        abnormalType.setAbnormalType(abnormal.getAbnormalType());
                        abnormalType.setProcessed(BusinessConstant.Y);
                        return abnormalType;
                    }).collect(Collectors.toList());
            allAttendanceAbnormalTypes.addAll(expireAttendanceAbnormalTypes);
        }

        //已通过
        List<HrmsEmployeeAbnormalAttendanceDO> passAbnormalList = abnormalAttendanceDOList
                .stream()
                .filter(abnormal -> Objects.equals(BusinessConstant.N, abnormal.getIsDelete())
                        && AbnormalAttendanceStatusEnum.PASS.getCode().equals(abnormal.getStatus()))
                .collect(Collectors.toList());
        List<Long> passAbnormalIdList = passAbnormalList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(passAbnormalIdList)) {
            //确认异常得记录
            List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(passAbnormalIdList);
            List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                    .stream()
                    .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                List<Long> confirmAbnormalIds = abnormalOperationRecordList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
                List<DateDetailReportVO.AttendanceAbnormalType> confirmAttendanceAbnormalTypes = warehouseDetailAbnormalDOS
                        .stream()
                        .filter(abnormal -> confirmAbnormalIds.contains(abnormal.getAbnormalId()))
                        .map(abnormal -> {
                            DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                            abnormalType.setAbnormalId(abnormal.getAbnormalId());
                            abnormalType.setAbnormalType(abnormal.getAbnormalType());
                            abnormalType.setProcessed(BusinessConstant.Y);
                            return abnormalType;
                        }).collect(Collectors.toList());
                allAttendanceAbnormalTypes.addAll(confirmAttendanceAbnormalTypes);
            } else {
                //补时长已处理但时长未补满
                if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                    List<DateDetailReportVO.AttendanceAbnormalType> confirmAttendanceAbnormalTypes = passAbnormalList
                            .stream()
                            .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                            .map(abnormal -> {
                                DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                                abnormalType.setAbnormalId(abnormal.getId());
                                abnormalType.setAbnormalType(abnormal.getAbnormalType());
                                abnormalType.setProcessed(BusinessConstant.Y);
                                return abnormalType;
                            }).collect(Collectors.toList());
                    allAttendanceAbnormalTypes.addAll(confirmAttendanceAbnormalTypes);
                }
            }
        }

        //待处理的记录
        List<DateDetailReportVO.AttendanceAbnormalType> noProcessAttendanceAbnormalTypes = abnormalAttendanceDOList
                .stream()
                .filter(abnormal -> Objects.equals(BusinessConstant.N, abnormal.getIsDelete())
                        && !Lists.newArrayList(AbnormalAttendanceStatusEnum.PASS.getCode(), AbnormalAttendanceStatusEnum.EXPIRED.getCode()).contains(abnormal.getStatus()))
                .map(abnormal -> {
                    DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                    abnormalType.setAbnormalId(abnormal.getId());
                    abnormalType.setAbnormalType(abnormal.getAbnormalType());
                    abnormalType.setProcessed(BusinessConstant.N);
                    return abnormalType;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noProcessAttendanceAbnormalTypes)) {
            allAttendanceAbnormalTypes.addAll(noProcessAttendanceAbnormalTypes);
        }

        attendanceResult.setAttendanceAbnormalTypes(allAttendanceAbnormalTypes);
        result.setAttendanceResult(attendanceResult);
    }

    private void convertLeaveDetailList(HrmsWarehouseDetailDO warehouseDetailDO, DateDetailReportVO result) {
        ApplicationFormQuery query = new ApplicationFormQuery();
        query.setUserId(warehouseDetailDO.getUserId());
        query.setFromTypeList(Collections.singletonList(HrAttendanceApplicationFormTypeEnum.LEAVE.getCode()));
        query.setStatusList(Lists.newArrayList(HrAttendanceApplicationFormStatusEnum.REJECT.getCode(), HrAttendanceApplicationFormStatusEnum.IN_REVIEW.getCode(), HrAttendanceApplicationFormStatusEnum.PASS.getCode()));
        List<HrmsApplicationFormDO> applicationFormDOList = applicationFormDao.selectForm(query);
        if (CollectionUtils.isNotEmpty(applicationFormDOList)) {
            List<Long> formIds = applicationFormDOList.stream().map(HrmsApplicationFormDO::getId).collect(Collectors.toList());
            Map<Long, List<HrmsApplicationFormAttrDO>> applicationFormAttrMap = applicationFormAttrDao.selectFormAttrByFormIdLit(formIds).stream().collect(Collectors.groupingBy(HrmsApplicationFormAttrDO::getFormId));
            List<DateDetailReportVO.LeaveDetail> leaveDetailList = applicationFormDOList.stream().map(applicationForm -> {
                        DateDetailReportVO.LeaveDetail leaveDetail = new DateDetailReportVO.LeaveDetail();
                        leaveDetail.setAbnormalStatus(applicationForm.getFormStatus());
                        List<HrmsApplicationFormAttrDO> applicationFormAttrDOS = applicationFormAttrMap.get(applicationForm.getId());
                        Date leaveStartTime = null;
                        Date leaveEndTime = null;
                        String leaveType = null;
                        for (HrmsApplicationFormAttrDO applicationFormAttrDO : applicationFormAttrDOS) {
                            if (applicationFormAttrDO.getAttrKey().equals(HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())) {
                                leaveType = applicationFormAttrDO.getAttrValue();
                            }
                            if (applicationFormAttrDO.getAttrKey().equals(HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())) {
                                leaveStartTime = DateUtil.parseDateTime(applicationFormAttrDO.getAttrValue());
                            }
                            if (applicationFormAttrDO.getAttrKey().equals(HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())) {
                                leaveEndTime = DateUtil.parseDateTime(applicationFormAttrDO.getAttrValue());
                            }
                        }
                        leaveDetail.setLeaveType(leaveType);
                        leaveDetail.setLeaveStartTime(leaveStartTime);
                        leaveDetail.setLeaveEndTime(leaveEndTime);
                        return leaveDetail;
                    }).filter(leaveDetail -> (warehouseDetailDO.getWarehouseDate().equals(DateUtil.parse(DateUtil.format(leaveDetail.getLeaveStartTime(), DateFormatterUtil.FORMAT_YYYY_MM_DD), DateFormatterUtil.FORMAT_YYYY_MM_DD))
                            || warehouseDetailDO.getWarehouseDate().after(DateUtil.parse(DateUtil.format(leaveDetail.getLeaveStartTime(), DateFormatterUtil.FORMAT_YYYY_MM_DD), DateFormatterUtil.FORMAT_YYYY_MM_DD)))
                            && (warehouseDetailDO.getWarehouseDate().equals(DateUtil.parse(DateUtil.format(leaveDetail.getLeaveEndTime(), DateFormatterUtil.FORMAT_YYYY_MM_DD), DateFormatterUtil.FORMAT_YYYY_MM_DD))
                            || warehouseDetailDO.getWarehouseDate().before(DateUtil.parse(DateUtil.format(leaveDetail.getLeaveEndTime(), DateFormatterUtil.FORMAT_YYYY_MM_DD), DateFormatterUtil.FORMAT_YYYY_MM_DD))))
                    .collect(Collectors.toList());
            result.setLeaveDetailList(leaveDetailList);
        }
    }

    private DataStatisticsVO convertDataStatisticsVO(DataStatisticsVO dataStatisticsVO,
                                                     List<HrmsWarehouseDetailDO> warehouseDetailDOList,
                                                     List<HrmsWarehouseRecordDO> warehouseRecordDOS,
                                                     List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS) {
        dataStatisticsVO.setEmployee(convertDataCountResult(warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS, Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode())));
        dataStatisticsVO.setLaborDispatch(convertDataCountResult(warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS, Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode())));
        DataCountVO total = convertDataCountResult(warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS, Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
        total.setWaitClockOutCount(dataStatisticsVO.getEmployee().getWaitClockOutCount() + dataStatisticsVO.getLaborDispatch().getWaitClockOutCount());
        dataStatisticsVO.setTotal(total);
        // 黑名单数量计算
        LambdaQueryWrapper<HrmsWarehouseBlackListDO> queryWrapper = Wrappers.lambdaQuery(HrmsWarehouseBlackListDO.class);
        queryWrapper.eq(HrmsWarehouseBlackListDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(HrmsWarehouseBlackListDO::getWarehouseDate, DateUtil.formatDate(warehouseDetailDOList.get(0).getWarehouseDate()));
        queryWrapper.eq(HrmsWarehouseBlackListDO::getOcId, warehouseDetailDOList.get(0).getOcId());
        queryWrapper.eq(warehouseDetailDOList.get(0).getClassesId() != null, HrmsWarehouseBlackListDO::getClassesId, warehouseDetailDOList.get(0).getClassesId());
        List<HrmsWarehouseBlackListDO> list = hrmsWarehouseBlackListDao.list(queryWrapper);
        Map<String, List<HrmsWarehouseBlackListDO>> employeeTypeMap = CollectionUtils.isEmpty(list) ? Maps.newHashMap() : list.stream().collect(Collectors.groupingBy(HrmsWarehouseBlackListDO::getEmployeeType));
        if (CollectionUtils.isNotEmpty(list)) {
            total.setBlackListCount((int) list.stream().map(HrmsWarehouseBlackListDO::getUserCode).distinct().count());
            dataStatisticsVO.getEmployee().setBlackListCount(
                    (int) employeeTypeMap.getOrDefault(EmploymentTypeEnum.EMPLOYEE.getCode(), Lists.newArrayList()).stream().map(HrmsWarehouseBlackListDO::getUserCode).distinct().count() +
                            (int) employeeTypeMap.getOrDefault(EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), Lists.newArrayList()).stream().map(HrmsWarehouseBlackListDO::getUserCode).distinct().count());
            dataStatisticsVO.getLaborDispatch().setBlackListCount(
                    (int) employeeTypeMap.getOrDefault(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), Lists.newArrayList()).stream().map(HrmsWarehouseBlackListDO::getUserCode).distinct().count());
        }
        List<HrmsWarehouseDetailDO> laborDispatchWarehouseList = warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())).collect(Collectors.toList());
        List<VendorDataCountVO> vendorDetailList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(laborDispatchWarehouseList)) {
            // 供应商黑名单
            if (employeeTypeMap.isEmpty()) {
                return dataStatisticsVO;
            }
            List<HrmsWarehouseBlackListDO> fixSalaryBlackList = employeeTypeMap.getOrDefault(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), Lists.newArrayList());
            if (CollectionUtils.isEmpty(fixSalaryBlackList)) {
                return dataStatisticsVO;
            }
            Map<String, List<HrmsWarehouseBlackListDO>> vendorBlackListMap = fixSalaryBlackList.stream().collect(Collectors.groupingBy(HrmsWarehouseBlackListDO::getVendorCode));
            Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(new ArrayList<>(vendorBlackListMap.keySet()));
            addBlackListVendorDetailList(vendorBlackListMap, vendorMap, vendorDetailList);
            return dataStatisticsVO;
        }
        Map<String, List<HrmsWarehouseDetailDO>> warehouseMap = laborDispatchWarehouseList.stream().collect(Collectors.groupingBy(HrmsWarehouseDetailDO::getVendorCode));

        List<String> vendorCodeAllList = new ArrayList<>(warehouseMap.keySet());

        List<HrmsWarehouseBlackListDO> fixSalaryBlackList = employeeTypeMap.getOrDefault(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), Lists.newArrayList());
        Map<String, List<HrmsWarehouseBlackListDO>> vendorBlackListMap = CollectionUtils.isEmpty(fixSalaryBlackList) ? Maps.newHashMap() :
                fixSalaryBlackList.stream().collect(Collectors.groupingBy(HrmsWarehouseBlackListDO::getVendorCode));
        vendorCodeAllList.addAll(vendorBlackListMap.keySet());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeAllList);
        for (Map.Entry<String, List<HrmsWarehouseDetailDO>> warehouseDetail : warehouseMap.entrySet()) {
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetail.getValue();
            VendorDataCountVO dataCountResult = new VendorDataCountVO();
            dataCountResult.setVendorCode(warehouseDetail.getKey());
            dataCountResult.setVendorName(vendorMap.get(warehouseDetail.getKey()));
            dataCountResult.setVendorDetailCount(convertDataCountResult(warehouseDetailDOS, warehouseRecordDOS, warehouseDetailAbnormalDOS, Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode())));
            dataCountResult.setTotalAbnormalCount(dataCountResult.getVendorDetailCount().getTotalAbnormalCount());
            dataCountResult.getVendorDetailCount().setBlackListCount((int) vendorBlackListMap.getOrDefault(warehouseDetail.getKey(), Lists.newArrayList()).stream().map(HrmsWarehouseBlackListDO::getUserCode).distinct().count());
            vendorBlackListMap.remove(warehouseDetail.getKey());
            vendorDetailList.add(dataCountResult);
        }
        addBlackListVendorDetailList(vendorBlackListMap, vendorMap, vendorDetailList);
        dataStatisticsVO.setVendorDetailList(vendorDetailList.stream()
                .sorted(Comparator.comparing(VendorDataCountVO::getTotalAbnormalCount).reversed())
                .collect(Collectors.toList()));
        return dataStatisticsVO;
    }

    private void addBlackListVendorDetailList(Map<String, List<HrmsWarehouseBlackListDO>> vendorBlackListMap, Map<String, String> vendorMap, List<VendorDataCountVO> vendorDetailList) {
        if (vendorBlackListMap.isEmpty()) {
            return;
        }
        for (String vendorCode : vendorBlackListMap.keySet()) {
            VendorDataCountVO dataCountResult = new VendorDataCountVO();
            dataCountResult.setVendorCode(vendorCode);
            dataCountResult.setVendorName(vendorMap.get(vendorCode));
            DataCountVO vendorDataCount = new DataCountVO();
            vendorDataCount.setEmployeeTypeList(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
            vendorDataCount.setBlackListCount((int) vendorBlackListMap.getOrDefault(vendorCode, Lists.newArrayList()).stream().map(HrmsWarehouseBlackListDO::getUserCode).distinct().count());
            dataCountResult.setVendorDetailCount(vendorDataCount);
            dataCountResult.setTotalAbnormalCount(0);
            vendorDetailList.add(dataCountResult);
        }
    }

    private DataCountVO convertDataCountResult(List<HrmsWarehouseDetailDO> warehouseDetailDOList,
                                               List<HrmsWarehouseRecordDO> warehouseRecordDOS,
                                               List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS,
                                               List<String> employeeTypeList) {
        DataCountVO result = new DataCountVO();
        result.setEmployeeTypeList(employeeTypeList);
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDOList
                .stream()
                .filter(warehouseDetail -> employeeTypeList.contains(warehouseDetail.getEmployeeType()))
                .collect(Collectors.toList());

        List<Long> warehouseIds = warehouseDetailDOS
                .stream()
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(warehouseRecordDOS)) {

            Map<Long, List<HrmsWarehouseRecordDO>> warehouseRecordMap = warehouseRecordDOS
                    .stream()
                    .filter(warehouseRecord -> warehouseIds.contains(warehouseRecord.getWarehouseDetailId()))
                    .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));

            if (MapUtils.isNotEmpty(warehouseRecordMap)) {
                Integer clockInCount = 0;
                Integer clockOutCount = 0;
                Integer waitClockOutCount = 0;
                for (HrmsWarehouseDetailDO warehouseDetailDO : warehouseDetailDOS) {
                    List<HrmsWarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetailDO.getId());
                    if (Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        waitClockOutCount++;
                    }
                    if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                        Optional<HrmsWarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(item -> Objects.equals(WarehouseTypeEnum.IN.getCode(), item.getRecordType())).findFirst();
                        if (inOptional.isPresent()) {
                            clockInCount++;
                        }
                    }
                    if (Objects.equals(WarehouseStatusEnum.OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        clockOutCount++;
                    }
                }
                result.setClockInCount(clockInCount);
                result.setClockOutCount(clockOutCount);
                result.setWaitClockOutCount(waitClockOutCount);
            }
        }


        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOS)) {
            //未处理异常
            Map<String, List<HrmsWarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(warehouseAbnormal -> warehouseIds.contains(warehouseAbnormal.getWarehouseDetailId())
                            && Objects.equals(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), warehouseAbnormal.getProcessed()))
                    .collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getAbnormalType));

            int lateCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getLateDictCode())) ? warehouseAbnormalMap.get(result.getLateDictCode()).size() : BusinessConstant.ZERO;
            int leaveEarlyCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getLeaveEarlyDictCode())) ? warehouseAbnormalMap.get(result.getLeaveEarlyDictCode()).size() : BusinessConstant.ZERO;
            int clockInLackCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getClockInLackDictCode())) ? warehouseAbnormalMap.get(result.getClockInLackDictCode()).size() : BusinessConstant.ZERO;
            int clockOutLackCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getClockOutLackDictCode())) ? warehouseAbnormalMap.get(result.getClockOutLackDictCode()).size() : BusinessConstant.ZERO;
            int abnormalDurationCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getAbnormalDurationDictCode())) ? warehouseAbnormalMap.get(result.getAbnormalDurationDictCode()).size() : BusinessConstant.ZERO;

            result.setLateCount(lateCount);
            result.setLeaveEarlyCount(leaveEarlyCount);
            result.setClockInLackCount(clockInLackCount);
            result.setClockOutLackCount(clockOutLackCount);
            result.setAbnormalDurationCount(abnormalDurationCount);
            result.calculateTotalAbnormalCount();

            //已处理异常 通过 or 确认异常
            List<HrmsWarehouseDetailAbnormalDO> processedList = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(abnormal -> warehouseIds.contains(abnormal.getWarehouseDetailId())
                            && Objects.equals(WarehouseAbnormalStatusEnum.PROCESSED.getCode(), abnormal.getProcessed()))
                    .collect(Collectors.toList());

            Map<Long, HrmsWarehouseDetailAbnormalDO> abnormalMap = processedList
                    .stream()
                    .collect(Collectors.toMap(HrmsWarehouseDetailAbnormalDO::getAbnormalId, Function.identity()));

            if (CollectionUtils.isEmpty(abnormalMap.keySet())) {
                return result;
            }

            List<DataStatisticsDetailsVO.AbnormalVO> abnormalList = new ArrayList<>();
            Set<Long> confirmAbnormalIds = new HashSet<>();
            List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalMap.keySet()));
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
                List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                    for (HrmsEmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                        HrmsWarehouseDetailAbnormalDO warehouseDetailAbnormalDO = abnormalMap.get(abnormalOperationRecord.getAbnormalId());
                        if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(warehouseDetailAbnormalDO.getAbnormalId());
                            abnormalVO.setAbnormalType(warehouseDetailAbnormalDO.getAbnormalType());
                            abnormalList.add(abnormalVO);
                            confirmAbnormalIds.add(warehouseDetailAbnormalDO.getAbnormalId());
                        }
                    }
                }
            }

            List<DataStatisticsDetailsVO.AbnormalVO> durationAbnormalList = processedList
                    .stream()
                    .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                            && !confirmAbnormalIds.contains(abnormal.getAbnormalId()))
                    .map(abnormalRecord -> {
                        DataStatisticsDetailsVO.AbnormalVO durationAbnormal = new DataStatisticsDetailsVO.AbnormalVO();
                        durationAbnormal.setAbnormalId(abnormalRecord.getAbnormalId());
                        durationAbnormal.setAbnormalType(abnormalRecord.getAbnormalType());
                        return durationAbnormal;
                    }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(durationAbnormalList)) {
                abnormalList.addAll(durationAbnormalList);
            }

            Map<String, List<DataStatisticsDetailsVO.AbnormalVO>> confirmAbnormalMap = abnormalList
                    .stream()
                    .collect(Collectors.groupingBy(DataStatisticsDetailsVO.AbnormalVO::getAbnormalType));

            lateCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getLateDictCode())) ? confirmAbnormalMap.get(result.getLateDictCode()).size() : BusinessConstant.ZERO;
            leaveEarlyCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getLeaveEarlyDictCode())) ? confirmAbnormalMap.get(result.getLeaveEarlyDictCode()).size() : BusinessConstant.ZERO;
            clockInLackCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getClockInLackDictCode())) ? confirmAbnormalMap.get(result.getClockInLackDictCode()).size() : BusinessConstant.ZERO;
            clockOutLackCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getClockOutLackDictCode())) ? confirmAbnormalMap.get(result.getClockOutLackDictCode()).size() : BusinessConstant.ZERO;
            abnormalDurationCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getAbnormalDurationDictCode())) ? confirmAbnormalMap.get(result.getAbnormalDurationDictCode()).size() : BusinessConstant.ZERO;
            result.setLateCount(lateCount);
            result.setLeaveEarlyCount(leaveEarlyCount);
            result.setClockInLackCount(clockInLackCount);
            result.setClockOutLackCount(clockOutLackCount);
            result.setAbnormalDurationCount(abnormalDurationCount);
            result.calculateTotalAbnormalCount();
        }
        return result;
    }

    private Integer covertWarehouseStatus(String warehouseType) {
        if (Objects.equals(BusinessConstant.WAIT_OUT, warehouseType)) {
            return WarehouseStatusEnum.WAIT_OUT.getCode();
        }
        return WarehouseStatusEnum.OUT.getCode();
    }
}
