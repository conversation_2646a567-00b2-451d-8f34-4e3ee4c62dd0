package com.imile.hrms.service.punch.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.google.common.base.Throwables;
import com.google.common.io.BaseEncoding;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.param.user.UserStatusSwitchParam;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.api.blacklist.api.BlacklistApi;
import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.api.primary.enums.CommonStatusEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.constants.RedisConstants;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OcrErrorCodeEnum;
import com.imile.hrms.common.enums.approval.AttendanceAbnormalOperationTypeEnum;
import com.imile.hrms.common.enums.attendance.AbnormalAttendanceStatusEnum;
import com.imile.hrms.common.enums.attendance.AttendanceAbnormalTypeEnum;
import com.imile.hrms.common.enums.attendance.ConfirmStatusEnum;
import com.imile.hrms.common.enums.blacklist.BlacklistBanStatusEnum;
import com.imile.hrms.common.enums.punch.*;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.CommonUtil;
import com.imile.hrms.common.util.DateFormatterUtil;
import com.imile.hrms.common.util.DistanceCalculatorUtils;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalAttendanceDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.hrms.dao.attendance.query.AbnormalAttendanceQuery;
import com.imile.hrms.dao.face.cache.FaceFeatureCache;
import com.imile.hrms.dao.face.dao.FaceFeatureDao;
import com.imile.hrms.dao.face.model.FaceFeatureDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.dao.*;
import com.imile.hrms.dao.punch.dto.WarehouseAttendanceConfigDetailDTO;
import com.imile.hrms.dao.punch.model.*;
import com.imile.hrms.dao.punch.param.DaysConfigParam;
import com.imile.hrms.dao.punch.param.HrmsAttendanceClassEmployeeConfigParam;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.dao.punch.param.WarehouseVendorClassesConfirmParam;
import com.imile.hrms.dao.punch.query.EmployeePunchCardRecordQuery;
import com.imile.hrms.dao.punch.query.WarehouseAttendanceConfigQuery;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.genesis.RpcUserService;
import com.imile.hrms.integration.hermes.vo.CountryDTO;
import com.imile.hrms.integration.recognition.RecognitionService;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.manage.punch.WarehouseManage;
import com.imile.hrms.service.approval.dto.DayPunchTimeDTO;
import com.imile.hrms.service.attendance.HrmsAttendanceBaseService;
import com.imile.hrms.service.attendance.HrmsEmployeeAbnormalAttendanceService;
import com.imile.hrms.service.attendance.dto.WarehouseAttendanceHandlerDTO;
import com.imile.hrms.service.attendance.param.AbnormalAttendanceBatchUpdateParam;
import com.imile.hrms.service.newAttendance.punchConfig.adapter.PunchConfigServiceAdapter;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.punch.*;
import com.imile.hrms.service.punch.param.UserCardParam;
import com.imile.hrms.service.punch.param.WarehouseUserCardParam;
import com.imile.hrms.service.punch.param.warehouse.*;
import com.imile.hrms.service.punch.vo.warehouse.CheckVendorConfirmStatusResultVO;
import com.imile.hrms.service.punch.vo.warehouse.OutVO;
import com.imile.hrms.service.user.result.UserCertificateBO;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcException;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内管理实现类
 * @date 2024/7/1 14:26:35
 */
@Slf4j
@Service
public class WarehouseServiceImpl extends WarehouseBaseService implements WarehouseService {

    @Resource
    private WarehouseClassesService warehouseClassesService;

    @Resource
    private WarehouseUserService warehouseUserService;

    @Resource
    private RpcUserService rpcUserService;

    @Resource
    private UserCertificateService userCertificateService;

//    @Resource
//    private HrmsAttendancePunchConfigService punchConfigService;

    @Resource
    private HrmsAttendanceClassEmployeeConfigService attendanceClassEmployeeConfigService;

    @Resource
    private WarehouseAttendanceCalculateService attendanceCalculateService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private RecognitionService recognitionService;

    @Resource
    private HrmsAttendanceBaseService attendanceBaseService;

    @Resource
    private HrmsAttendanceUserCardConfigService attendanceUserCardConfigService;

    @Resource
    private WarehouseAttendancePushFinService attendancePushFinService;

    @Resource
    private HrmsEmployeeAbnormalAttendanceService employeeAbnormalAttendanceService;

    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;

    @Resource
    private WarehouseAttendanceDurationCalculateService attendanceDurationCalculateService;

    @Resource
    private HrmsWarehouseDetailDao warehouseDetailDao;

    @Resource
    private HrmsWarehouseRecordDao warehouseRecordDao;

    @Resource
    private HrmsWarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private HrmsFaceRecordDao faceRecordDao;

    @Resource
    private FaceFeatureDao faceFeatureDAO;

    @Resource
    private HrmsWarehouseAttendanceConfigDao warehouseAttendanceConfigDao;

    @Resource
    private HrmsWarehouseVendorClassesConfirmDao warehouseVendorClassesConfirmDao;

    @Resource
    private HrmsAttendanceEmployeeDetailDao attendanceEmployeeDetailDao;

    @Resource
    private HrmsEmployeeAbnormalAttendanceDao abnormalAttendanceDao;

    @Resource
    private HrmsEmployeeAbnormalOperationRecordDao abnormalOperationRecordDao;

    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;

//    @Resource
//    private HrmsAttendancePunchClassConfigDao attendancePunchClassConfigDao;

//    @Resource
//    private HrmsAttendancePunchClassItemConfigDao classItemConfigDao;

    @Resource
    private WarehouseManage warehouseManage;

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private HrmsProperties hrmsProperties;

    @Resource
    private FaceFeatureCache faceFeatureCache;

    @Resource
    private BlacklistApi blacklistApi;

    @Resource
    private PunchConfigDaoFacade punchConfigDaoFacade;

    @Resource
    public PunchConfigServiceAdapter punchConfigServiceAdapter;

    @Resource
    private HrmsWarehouseBlackListDao hrmsWarehouseBlackListDao;

    @Override
    public void in(InOrOutParam param) {
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(param.getEmployeeType()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);
        BusinessLogicException.checkTrue(BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType()) && Objects.isNull(param.getVendorId()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);

        //检查网点权限
        checkAuthOc(param.getOcId());

        //查询网点
        EntOcApiDTO oc = warehouseOcService.getOc(param.getOcId());

        //检查打卡时间,打卡时间由前端从手机采集，可能存在用户调整手机系统时间
        checkPunchTime(oc.getCountry(), param.getAttendanceTime());

        //巴西劳务派遣供应商校验
        braInVendorCheck(param);

        //是否允许上多班次
        boolean isMultipleShifts = false;
        //查询仓内考勤管理规则
        HrmsWarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectByDeptId(param.getOcId());
        if (Objects.nonNull(warehouseAttendanceConfigDO) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDO.getIsMultipleShifts())) {
            isMultipleShifts = true;
        }

        for (Long userId : param.getUserIdList()) {
            HrmsUserInfoDO user = getUserForInOrOut(userId);
            if ((BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())
                    && !EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType()))
                    || (BusinessConstant.FORMAL.equals(param.getEmployeeType())
                    && !com.imile.hrms.common.enums.EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(user.getEmployeeType()))) {
                throw BusinessException.get(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getDesc()));
            }

            Long vendorId;
            String vendorCode;
            if (!BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())) {
                vendorId = user.getVendorId();
                vendorCode = user.getVendorCode();
            } else {
                vendorId = param.getVendorId();
                vendorCode = param.getVendorCode();
            }

            //检查刷脸记录
            HrmsFaceRecordDO faceRecord = Optional.ofNullable(faceRecordDao.selectLastOne(userId, param.getAttendanceTime(), FaceRecordStatusEnum.EFFECTIVE.getCode()))
                    .orElseThrow(() -> BusinessException.get(HrmsErrorCodeEnums.NOT_RECOGNITION_OR_UPLOAD_CANNOT_IN.getCode(),
                            I18nUtils.getMessage(HrmsErrorCodeEnums.NOT_RECOGNITION_OR_UPLOAD_CANNOT_IN.getDesc(), BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()))));

            //考勤日校验
            checkWarehouseDate(param.getWarehouseDate(), param.getAttendanceTime(), Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null, WarehouseTypeEnum.INIT.getCode());

            Date warehouseDate = param.getWarehouseDate();
            Long attendanceDayId = Long.parseLong(DateUtil.format(warehouseDate, DatePattern.PURE_DATE_PATTERN));
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByWarehouseDateAndUserId(warehouseDate, userId);

            //入仓校验
            checkIn(warehouseDetailDOS, param, user, isMultipleShifts);

            //获取当天日历出勤类型
            String dayType = getCurrentDayType(warehouseDate, user);

            HrmsWarehouseDetailDO hrmsWarehouseDetailDO = convertWarehouseDetail(param, warehouseDetailDOS, vendorId, vendorCode, user, oc, dayType, warehouseDate, WarehouseTypeEnum.IN.getCode(), warehouseAttendanceConfigDO);
            if (Objects.equals(WarehouseStatusEnum.INIT.getCode(), hrmsWarehouseDetailDO.getWarehouseStatus())) {
                hrmsWarehouseDetailDO.setWarehouseStatus(WarehouseTypeEnum.IN.getCode());
            }

            if (Objects.nonNull(hrmsWarehouseDetailDO.getId())) {
                hrmsWarehouseDetailDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
            }

            //生成排班计划
            if (Objects.nonNull(param.getClasses())) {
                generateSchedulingPlan(param.getClasses(), param.getAttendanceTime(), attendanceDayId, user.getId());
            }

            HrmsWarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(param, vendorId, vendorCode, user, oc, faceRecord.getId(), warehouseDate);
            Long classId = Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null;
            EmployeePunchRecordDO punchRecordDO = convertPunchRecord(param.getOcId(), param.getAttendanceTime(), classId, user, oc, attendanceDayId.toString());
            warehouseManage.warehouseAttendanceAdd(hrmsWarehouseDetailDO, warehouseRecordDO, punchRecordDO);
        }

    }

    @Override
    public void inV2(InV2Param param) {
        //入仓
        in(param);
        //停用新的重复账号
        HrmsUserInfoDO userInfo = userInfoDao.getById(param.getOriginUserId());
        Boolean disabledUser = rpcUserService.switchUserStatus(UserStatusSwitchParam.builder()
                .userCode(userInfo.getUserCode())
                .userStatus(CommonStatusEnum.DISABLED.getCode())
                .operationScene(OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE)
                .build());
        if (!disabledUser) {
            throw BusinessException.get(HrmsErrorCodeEnums.DISABLED_USER_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DISABLED_USER_ERROR.getDesc()));
        }
        //证件号更新
        List<UserCertificateBO> userCertificateBOS = userCertificateService.getUserCertificateList(param.getUserIdList(), CertificateTypeEnum.INE.getCode());
        if (CollectionUtils.isNotEmpty(userCertificateBOS) && !Objects.equals(param.getCertificatesCode(), userCertificateBOS.get(BusinessConstant.FIRST_ELEMENT_INDEX).getCertificateCode())) {
            UserCertificateSaveParam userCertificateSaveParam = BeanUtils.convert(userCertificateBOS.get(BusinessConstant.FIRST_ELEMENT_INDEX), UserCertificateSaveParam.class);
            userCertificateSaveParam.setCertificateCode(param.getCertificatesCode());
            userCertificateService.saveUserCertificate(param.getUserIdList().get(BusinessConstant.FIRST_ELEMENT_INDEX), Collections.singletonList(userCertificateSaveParam),
                    OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE);
        }
    }

    @Override
    public OutVO out(OutParam param) {
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(param.getEmployeeType()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);
        BusinessLogicException.checkTrue(BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType()) && Objects.isNull(param.getVendorId()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);

        checkAuthOc(param.getOcId());

        EntOcApiDTO oc = warehouseOcService.getOc(param.getOcId());

        checkPunchTime(oc.getCountry(), param.getAttendanceTime());

        OutVO vo = new OutVO();
        for (Long userId : param.getUserIdList()) {
            HrmsUserInfoDO user = getUserForInOrOut(userId);
            if ((BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())
                    && !EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType()))
                    || (BusinessConstant.FORMAL.equals(param.getEmployeeType())
                    && !com.imile.hrms.common.enums.EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(user.getEmployeeType()))) {
                throw BusinessException.get(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getDesc()));
            }

            Long vendorId;
            String vendorCode;
            if (!BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())) {
                vendorId = user.getVendorId();
                vendorCode = user.getVendorCode();
            } else {
                vendorId = param.getVendorId();
                vendorCode = param.getVendorCode();
            }

            //检查刷脸记录
            HrmsFaceRecordDO faceRecord = faceRecordDao.selectLastOne(userId, param.getAttendanceTime(), FaceRecordStatusEnum.EFFECTIVE.getCode());
            if (Objects.isNull(faceRecord)) {
                throw BusinessException.get(HrmsErrorCodeEnums.NOT_RECOGNITION_CANNOT_OUT.getCode(),
                        I18nUtils.getMessage(HrmsErrorCodeEnums.NOT_RECOGNITION_CANNOT_OUT.getDesc(), BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn())));
            }

            //考勤日校验
            checkWarehouseDate(param.getWarehouseDate(), param.getAttendanceTime(), Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null, WarehouseTypeEnum.OUT.getCode());

            Date warehouseDate = param.getWarehouseDate();
            Long attendanceDayId = Long.parseLong(DateUtil.format(warehouseDate, DatePattern.PURE_DATE_PATTERN));

            //查询当日考勤结果是否初始化
            List<HrmsWarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByWarehouseDateAndUserId(warehouseDate, userId);

            //出仓检测
            vo = checkOut(warehouseDetailDOList, param, user, vendorId, vendorCode);
            if (!vo.getCheckOutResult()) {
                return vo;
            }

            if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
                //直接出仓打卡
                //生成排班计划
                if (Objects.nonNull(param.getClasses())) {
                    generateSchedulingPlan(param.getClasses(), param.getAttendanceTime(), attendanceDayId, user.getId());
                }
                //获取当天日历出勤类型
                String dayType = getCurrentDayType(warehouseDate, user);
                //是否允许上多班次
                //查询仓内考勤管理规则
                HrmsWarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectByDeptId(param.getOcId());
                //保存办公室打卡记录
                Long classId = Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null;
                EmployeePunchRecordDO punchRecordDO = convertPunchRecord(param.getOcId(), param.getAttendanceTime(), classId, user, oc, attendanceDayId.toString());
                HrmsWarehouseDetailDO hrmsWarehouseDetailDO = convertWarehouseDetail(param, null, vendorId, vendorCode, user, oc, dayType, warehouseDate, WarehouseTypeEnum.OUT.getCode(), warehouseAttendanceConfigDO);
                HrmsWarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(param, oc, vendorId, vendorCode, user, faceRecord, warehouseDate, null);
                warehouseManage.warehouseAttendanceAdd(hrmsWarehouseDetailDO, warehouseRecordDO, punchRecordDO);
                //计算考勤异常并结果处理
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(hrmsWarehouseDetailDO, attendanceDayId, BusinessConstant.OFF_WORK_PUNCH_IN);
            } else {
                //入仓-》出仓
                HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDOList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
                if (param.getUpdateInVendor()) {
                    warehouseDetailDO.setVendorId(vendorId);
                    warehouseDetailDO.setVendorCode(vendorCode);
                }
                warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
                EmployeePunchRecordDO punchRecordDO = convertPunchRecord(param.getOcId(), param.getAttendanceTime(), warehouseDetailDO.getClassesId(), user, oc, attendanceDayId.toString());
                HrmsWarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(param, oc, vendorId, vendorCode, user, faceRecord, warehouseDate, warehouseDetailDO);
                warehouseManage.warehouseAttendanceAdd(warehouseDetailDO, warehouseRecordDO, punchRecordDO);

                if (Objects.nonNull(param.getClasses())) {
                    warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetailDO, attendanceDayId, BusinessConstant.OFF_WORK_PUNCH_IN);
                } else {
                    setPcsStatus(warehouseDetailDO);
                    warehouseManage.warehouseAttendanceUpdate(warehouseDetailDO, true);
                }
            }
        }
        return vo;
    }

    @SneakyThrows
    @Override
    public void quickOut(QuickOutParam param) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(param.getFile().getOriginalFilename()), HrmsErrorCodeEnums.PARAM_VALID_ERROR);
        HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDao.selectById(param.getId());
        if (Objects.isNull(warehouseDetailDO)) {
            return;
        }
        if (DateUtil.between(warehouseDetailDO.getWarehouseDate(), param.getAttendanceTime(), DateUnit.HOUR) > 23) {
            throw BusinessException.get(HrmsErrorCodeEnums.QUICK_OUT_MUST_IN_24_HOURS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.QUICK_OUT_MUST_IN_24_HOURS.getDesc()));
        }
        //文件上传
        String faceKey = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        //查询网点
        EntOcApiDTO oc = warehouseOcService.getOc(warehouseDetailDO.getOcId());

        checkPunchTime(oc.getCountry(), param.getAttendanceTime());

        //查询人脸录入照
        FaceFeatureDO faceFeatureDO = faceFeatureDAO.getByUserCode(warehouseDetailDO.getUserCode());

        saveFaceRecord(warehouseDetailDO, faceKey, faceFeatureDO.getUrl(), param.getAttendanceTime());

        HrmsFaceRecordDO faceRecord = faceRecordDao.selectLastOne(warehouseDetailDO.getUserId(), param.getAttendanceTime(), FaceRecordStatusEnum.EFFECTIVE.getCode());

        EmployeePunchRecordDO punchRecordDO = convertPunchRecord(warehouseDetailDO, oc, param.getAttendanceTime());
        HrmsWarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(warehouseDetailDO, oc, faceRecord.getId(), param);
        warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
        warehouseManage.warehouseAttendanceAdd(warehouseDetailDO, warehouseRecordDO, punchRecordDO);

        if (Objects.nonNull(warehouseDetailDO.getClassesId())) {
            warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetailDO, Long.valueOf(DateUtil.format(warehouseDetailDO.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD)), BusinessConstant.OFF_WORK_PUNCH_IN);
        } else {
            setPcsStatus(warehouseDetailDO);
            warehouseManage.warehouseAttendanceUpdate(warehouseDetailDO, true);
        }
    }

    @Override
    public void updateWorkVendor(UpdateWarehouseWorkVendorParam param) {
        List<HrmsWarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(param.getIdList())
                .stream()
                .filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())
                        && !Objects.equals(CountryCodeEnum.BRA.getCode(), warehouse.getCountry()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.error("仓内日报ID列表查询无效");
            return;
        }

        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(param.getVendorCode()));
        if (MapUtils.isEmpty(vendorMap)) {
            log.error("更新工作供应商：供应商查询异常");
            return;
        }

        List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList = new ArrayList<>();
        List<HrmsEmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList = new ArrayList<>();
        List<HrmsWarehouseVendorClassesConfirmDO> updateVendorClassesConfirmDOList = new ArrayList<>();
        List<Long> confirmAbnormalIds = new ArrayList<>();
        //已确认的需要回退日报状态 异常状态 清理异常操作记录 更新供应商班次确认记录
        supplierClassConfirmRollbackHandler(warehouseDetailList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, confirmAbnormalIds);

        warehouseDetailList.forEach(warehouseDetailDO -> {
            warehouseDetailDO.setVendorId(param.getVendorId());
            warehouseDetailDO.setVendorCode(param.getVendorCode());
            if (Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouseDetailDO.getConfirmStatus())) {
                warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode());
            }
            BaseDOUtil.fillDOUpdate(warehouseDetailDO);
        });

        List<Long> warehouseDetailIds = warehouseDetailList.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<HrmsWarehouseRecordDO> warehouseRecordDOList = warehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
            warehouseRecordDOList.forEach(warehouseRecordDO -> {
                warehouseRecordDO.setVendorId(param.getVendorId());
                warehouseRecordDO.setVendorCode(param.getVendorCode());
                BaseDOUtil.fillDOUpdate(warehouseRecordDO);
            });
        }

        List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOS)) {
            warehouseDetailAbnormalDOS.forEach(abnormal -> {
                abnormal.setVendorId(param.getVendorId());
                if (confirmAbnormalIds.contains(abnormal.getAbnormalId())) {
                    abnormal.setProcessed(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
                }
                BaseDOUtil.fillDOUpdate(abnormal);
            });
        }

        //持久化操作
        warehouseManage.warehouseAttendanceUpdate(warehouseDetailList, warehouseRecordDOList, warehouseDetailAbnormalDOS, null, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, null);

        List<Long> normalWarehouseIds = warehouseDetailList.stream().filter(warehouseDetailDO -> StringUtils.isEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<Long> retryWarehouseIds = warehouseDetailList.stream().filter(warehouseDetailDO -> StringUtils.isNotEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());

        //推送财务
        if (CollectionUtils.isNotEmpty(normalWarehouseIds)) {
            attendancePushFinService.asyncPushFin(normalWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_IN);
        }
        if (CollectionUtils.isNotEmpty(retryWarehouseIds)) {
            attendancePushFinService.asyncPushFin(retryWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
        }
    }

    @Override
    public void updateWorkOc(UpdateWarehouseWorkOcParam param) {
        List<HrmsWarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(param.getIdList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.error("仓内日报ID列表查询无效");
            return;
        }

        List<HrmsEntDeptDO> entDeptDOList = entDeptDao.listByDeptIds(Collections.singletonList(param.getOcId()));
        if (CollectionUtils.isEmpty(entDeptDOList)) {
            log.error("更新工作网点: 网点信息查询异常");
            return;
        }

        List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList = new ArrayList<>();
        List<HrmsEmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList = new ArrayList<>();
        List<HrmsWarehouseVendorClassesConfirmDO> updateVendorClassesConfirmDOList = new ArrayList<>();
        List<Long> confirmAbnormalIds = new ArrayList<>();
        //已确认的需要回退日报状态 异常状态 清理异常操作记录 更新供应商班次确认记录
        List<HrmsWarehouseDetailDO> laborWarehouseList = warehouseDetailList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())).collect(Collectors.toList());
        supplierClassConfirmRollbackHandler(laborWarehouseList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, confirmAbnormalIds);

        List<EmployeePunchRecordDO> employeePunchRecordDOList = new ArrayList<>();
        warehouseDetailList.forEach(warehouseDetailDO -> {
            warehouseDetailDO.setOcId(param.getOcId());
            if (Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouseDetailDO.getConfirmStatus())) {
                warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode());
            }
            BaseDOUtil.fillDOUpdate(warehouseDetailDO);

            String dayId = DateUtil.format(warehouseDetailDO.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN);
            List<EmployeePunchRecordDO> employeePunchRecordDOS = employeePunchRecordDao.listRecords(EmployeePunchCardRecordQuery.builder().userCode(warehouseDetailDO.getUserCode()).dayId(dayId).build());
            employeePunchRecordDOS.forEach(punchRecordDO -> punchRecordDO.setDeptId(param.getOcId()));
            employeePunchRecordDOList.addAll(employeePunchRecordDOS);
        });

        List<Long> warehouseDetailIds = warehouseDetailList.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<HrmsWarehouseRecordDO> warehouseRecordDOList = warehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
            warehouseRecordDOList.forEach(warehouseRecordDO -> {
                warehouseRecordDO.setOcId(param.getOcId());
                BaseDOUtil.fillDOUpdate(warehouseRecordDO);
            });
        }

        List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOS)) {
            warehouseDetailAbnormalDOS.forEach(abnormal -> {
                abnormal.setOcId(param.getOcId());
                if (confirmAbnormalIds.contains(abnormal.getAbnormalId())) {
                    abnormal.setProcessed(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
                }
                BaseDOUtil.fillDOUpdate(abnormal);
            });
        }

        warehouseManage.warehouseAttendanceUpdate(warehouseDetailList, warehouseRecordDOList, warehouseDetailAbnormalDOS, null, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, employeePunchRecordDOList);

        List<Long> normalWarehouseIds = laborWarehouseList.stream().filter(warehouseDetailDO -> StringUtils.isEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<Long> retryWarehouseIds = laborWarehouseList.stream().filter(warehouseDetailDO -> StringUtils.isNotEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(normalWarehouseIds)) {
            attendancePushFinService.asyncPushFin(normalWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_IN);
        }
        if (CollectionUtils.isNotEmpty(retryWarehouseIds)) {
            attendancePushFinService.asyncPushFin(retryWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
        }
    }

    @Override
    public void updateWorkClasses(UpdateWarehouseWorkClassesParam param) {
        List<HrmsWarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(param.getIdList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.error("仓内日报ID列表查询无效");
            return;
        }

        //查询班次信息
        ClassesParam classes = warehouseClassesService.getClassesInfo(param.getClassId());

        List<HrmsAttendanceEmployeeDetailDO> attendanceEmployeeDetailList = new ArrayList<>();
        List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList = new ArrayList<>();
        List<HrmsEmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList = new ArrayList<>();
        List<HrmsWarehouseVendorClassesConfirmDO> updateVendorClassesConfirmDOList = new ArrayList<>();
        List<Long> confirmAbnormalIds = new ArrayList<>();
        //已确认的需要回退日报状态 异常状态 清理异常操作记录 更新供应商班次确认记录
        List<HrmsWarehouseDetailDO> laborWarehouseList = warehouseDetailList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())).collect(Collectors.toList());
        supplierClassConfirmRollbackHandler(laborWarehouseList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, confirmAbnormalIds);

        clearOriginalClassAttendanceData(warehouseDetailList, attendanceEmployeeDetailList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList);

        List<EmployeePunchRecordDO> employeePunchRecordDOList = new ArrayList<>();
        Map<String, List<HrmsWarehouseDetailDO>> warehouseGroupMap = warehouseDetailList
                .stream()
                .collect(Collectors.groupingBy(warehouse -> DateUtil.formatDate(warehouse.getWarehouseDate()) + "_" + warehouse.getUserCode()));

        for (String warehouseKey : warehouseGroupMap.keySet()) {
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseGroupMap.get(warehouseKey);
            String[] keySplit = warehouseKey.split("_");
            Date warehouseDate = DateUtil.parseDate(keySplit[0]);
            String userCode = keySplit[1];
            String dayId = DateUtil.format(warehouseDate, DatePattern.PURE_DATE_PATTERN);
            List<EmployeePunchRecordDO> employeePunchRecordDOS = employeePunchRecordDao.listRecords(EmployeePunchCardRecordQuery.builder().userCode(userCode).dayId(dayId).build());
            employeePunchRecordDOS.forEach(punchRecordDO -> punchRecordDO.setClassId(classes.getClassId()));
            employeePunchRecordDOList.addAll(employeePunchRecordDOS);
            warehouseDetailDOS.forEach(warehouse -> {
                warehouse.setClassesId(classes.getClassId());
                warehouse.setClassesName(classes.getClassName());
                warehouse.setClassesType(classes.getClassType());
                warehouse.setRequiredAttendanceTime(classes.getAttendanceHours());
                warehouse.setLegalWorkingHours(classes.getLegalWorkingHours());

                if (Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouse.getConfirmStatus())) {
                    warehouse.setConfirmStatus(WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode());
                }
                BaseDOUtil.fillDOUpdate(warehouse);

                //重新排班
                generateSchedulingPlan(classes, warehouse.getWarehouseDate(), Long.valueOf(dayId), warehouse.getUserId());
            });
        }

        warehouseManage.warehouseAttendanceUpdate(warehouseDetailList, null, null, attendanceEmployeeDetailList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, employeePunchRecordDOList);

        //计算异常
        warehouseDetailList.forEach(warehouse -> {
            if (Objects.equals(WarehouseAttendanceStatusEnum.INIT.getCode(), warehouse.getAttendanceStatus())) {
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouse, Long.valueOf(DateUtil.format(warehouse.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN)), BusinessConstant.OFF_WORK_PUNCH_IN);
            } else {
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouse, Long.valueOf(DateUtil.format(warehouse.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN)), BusinessConstant.OFF_WORK_PUNCH_REISSUE);
            }
        });
    }

    @Override
    public Boolean bingShift(BingShiftParam param) {
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setIds(param.getIdList());
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectNoBindShiftByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return Boolean.FALSE;
        }

        List<Long> userIds = warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        List<String> userCodes = warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.getByUserIds(userIds);
        warehouseDetailDOS.forEach(warehouseDetail -> threadPoolTaskExecutor.execute(() -> {
            try {
                warehouseDetail.setClassesId(param.getClasses().getClassId());
                warehouseDetail.setClassesType(param.getClasses().getClassType());
                warehouseDetail.setClassesName(param.getClasses().getClassName());
                warehouseDetail.setRequiredAttendanceTime(param.getClasses().getAttendanceHours());
                warehouseDetail.setLegalWorkingHours(param.getClasses().getLegalWorkingHours());

                //先排班
                ClassesParam classes = BeanUtils.convert(param.getClasses(), ClassesParam.class);
                generateSchedulingPlan(classes, warehouseDetail.getWarehouseDate(), null, warehouseDetail.getUserId());

                if (StringUtils.isEmpty(warehouseDetail.getAttendanceType())) {
                    HrmsUserInfoDO userInfoDO = userInfoDOList.stream().filter(user -> Objects.equals(warehouseDetail.getUserId(), user.getId())).findFirst().orElse(new HrmsUserInfoDO());
                    String dayType = getCurrentDayType(warehouseDetail.getWarehouseDate(), userInfoDO);
                    warehouseDetail.setAttendanceType(dayType);
                }

                //考勤异常计算结果处理
                Long dayId = Long.valueOf(DateUtil.format(warehouseDetail.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD));
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetail, dayId, BusinessConstant.OFF_WORK_PUNCH_IN);
            } catch (Exception e) {
                log.info("仓内考勤：{} ,绑定班次考勤计算异常: {}", warehouseDetail.getId().toString(), e.getMessage());
            }
        }));
        //发放打卡次数
        Map<Date, List<HrmsWarehouseDetailDO>> warehouseDetailMap = warehouseDetailDOS.stream().collect(Collectors.groupingBy(HrmsWarehouseDetailDO::getWarehouseDate));
        List<UserCardParam> userCardParamList = new ArrayList<>();
        for (Date warehouseDate : warehouseDetailMap.keySet()) {
            List<HrmsWarehouseDetailDO> warehouseDetailDOList = warehouseDetailMap.get(warehouseDate);
            UserCardParam userCardParam = new UserCardParam();
            userCardParam.setInitDayId(Long.valueOf(DateUtil.format(warehouseDate, DateFormatterUtil.FORMAT_YYYYMMDD)));
            userCardParam.setUserCodeList(warehouseDetailDOList.stream().map(HrmsWarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList()));
            userCardParamList.add(userCardParam);
        }
        WarehouseUserCardParam userCardParam = new WarehouseUserCardParam();
        userCardParam.setAllUserCodeList(userCodes);
        userCardParam.setUserCardParamList(userCardParamList);
        attendanceUserCardConfigService.bindUserCardConfig(userCardParam);
        return Boolean.TRUE;
    }

    @Override
    public void retryPushFin(WarehouseDetailParam param) {

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setIds(param.getIds());
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorIdList(param.getVendorIdList());
        warehouseDetailParam.setUserCodeList(param.getUserCodeList());
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setStartTime(param.getStartTime());
        warehouseDetailParam.setEndTime(param.getEndTime());
        warehouseDetailParam.setAttendanceStatusList(Lists.newArrayList(WarehouseAttendanceStatusEnum.NORMAL.getCode(), WarehouseAttendanceStatusEnum.ABNORMAL.getCode()));
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return;
        }
        List<Long> warehouseDetailIdList = warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList());
        log.info("补推财务仓内考勤日报ids: {}", warehouseDetailIdList);
        attendancePushFinService.asyncPushFin(warehouseDetailIdList, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
    }

    @SneakyThrows
    @Override
    public Long vendorClassedConfirm(VendorConfirmClassesParam param) {
        // 判断是否到下班打卡时间
        Date endTime = getTheLastClockTime(param.getWarehouseDate(), param.getClassesId());
        if (endTime == null || endTime.after(param.getConfirmDate())) {
            throw BusinessException.get(HrmsErrorCodeEnums.NOT_YET_THE_END_OF_SHIFT.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.NOT_YET_THE_END_OF_SHIFT.getDesc()));
        }

        //判断班次结果是否重复确认
        WarehouseVendorClassesConfirmParam warehouseVendorClassesConfirmParam = new WarehouseVendorClassesConfirmParam();
        warehouseVendorClassesConfirmParam.setWarehouseDate(param.getWarehouseDate());
        warehouseVendorClassesConfirmParam.setOcId(param.getOcId());
        warehouseVendorClassesConfirmParam.setVendorCode(param.getVendorCode());
        warehouseVendorClassesConfirmParam.setClassesId(param.getClassesId());
        List<HrmsWarehouseVendorClassesConfirmDO> warehouseVendorClassesConfirmDOList = warehouseVendorClassesConfirmDao.selectByContidition(warehouseVendorClassesConfirmParam);
        if (CollectionUtils.isNotEmpty(warehouseVendorClassesConfirmDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.VENDOR_CLASSES_CONFIRM_REPEAT.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.VENDOR_CLASSES_CONFIRM_REPEAT.getDesc()));
        }

        //上传文件
        String signedUrl = ipepIntegration.upload(BusinessConstant.WAREHOUSE_UPLOAD_FILE_PATH_PREFIX, param.getSignedFile().getOriginalFilename(), param.getSignedFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        String faceUrl = ipepIntegration.upload(BusinessConstant.WAREHOUSE_UPLOAD_FILE_PATH_PREFIX, param.getFaceFile().getOriginalFilename(), param.getFaceFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);

        //人脸信息检测、人脸活性检测
        String imageBase64 = BaseEncoding.base64().encode(param.getFaceFile().getBytes());
        try {
            recognitionService.detectFace(faceUrl, imageBase64);
        } catch (RpcException e) {
            log.error("班次确认,人脸检测异常：{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }

        //查询日报
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        warehouseDetailParam.setClassId(param.getClassesId());
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            throw BusinessException.get(HrmsErrorCodeEnums.WAREHOUSE_NO_ATTENDANCE_RECORD.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.WAREHOUSE_NO_ATTENDANCE_RECORD.getDesc()));
        }

        //待计算考勤日报
        List<HrmsWarehouseDetailDO> attendanceCalculatedList = warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.INIT.getCode(), warehouse.getAttendanceStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attendanceCalculatedList)) {
            attendanceCalculatedList.forEach(warehouseDetailDO -> {
                //计算考勤时长
                List<HrmsWarehousePunchPeriodDO> warehousePunchPeriodDOList = attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, false);

                Long dayId = Long.valueOf(DateUtil.format(warehouseDetailDO.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD));
                WarehouseAttendanceHandlerDTO attendanceHandlerDTO = new WarehouseAttendanceHandlerDTO();
                attendanceHandlerDTO.setUserCodes(warehouseDetailDO.getUserCode());
                attendanceHandlerDTO.setAttendanceDayId(dayId);
                attendanceHandlerDTO.setOcId(warehouseDetailDO.getOcId());
                attendanceHandlerDTO.setClassId(warehouseDetailDO.getClassesId());
                attendanceHandlerDTO.setAttendanceConfigId(warehouseDetailDO.getAttendanceConfigId());
                attendanceHandlerDTO.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
                attendanceHandlerDTO.setActualWorkingHours(warehouseDetailDO.getActualWorkingHours());
                attendanceCalculateService.warehouseLaborWorkerAttendanceHandler(attendanceHandlerDTO);

                //考勤结果处理
                warehouseAttendanceHandlerService.attendanceAbnormalHandler(warehouseDetailDO, dayId, true, warehousePunchPeriodDOList);
            });
        }

        WarehouseDetailParam detailParam = new WarehouseDetailParam();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassId(param.getClassesId());
        detailParam.setAttendanceStatusList(Collections.singletonList(WarehouseAttendanceStatusEnum.ABNORMAL.getCode()));
        List<HrmsWarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(detailParam);

        HrmsWarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO = BeanUtils.convert(param, HrmsWarehouseVendorClassesConfirmDO.class);
        warehouseVendorClassesConfirmDO.setId(hrmsIdWorker.nextId());
        warehouseVendorClassesConfirmDO.setSignedPhoto(signedUrl);
        warehouseVendorClassesConfirmDO.setFacePhoto(faceUrl);

        if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
            List<Long> warehouseIds = warehouseDetailDOList.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList());
            List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds);
            List<Long> abnormalIdList = new ArrayList<>();
            List<Long> processingAbnormalIds = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(abnormal -> Objects.equals(BusinessConstant.N, abnormal.getProcessed()))
                    .map(HrmsWarehouseDetailAbnormalDO::getAbnormalId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(processingAbnormalIds)) {
                abnormalIdList.addAll(processingAbnormalIds);
            }

            List<Long> processedAbnormalIds = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(abnormal -> Objects.equals(BusinessConstant.Y, abnormal.getProcessed())
                            && Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                    .map(HrmsWarehouseDetailAbnormalDO::getAbnormalId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(processedAbnormalIds)) {
                List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(processedAbnormalIds);
                List<Long> confirmAbnormalIds = new ArrayList<>();
                List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                    confirmAbnormalIds.addAll(abnormalOperationRecordList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList()));
                }
                processedAbnormalIds.removeAll(confirmAbnormalIds);
                if (CollectionUtils.isNotEmpty(processedAbnormalIds)) {
                    abnormalIdList.addAll(processedAbnormalIds);
                }
            }

            if (CollectionUtils.isNotEmpty(abnormalIdList)) {
                AbnormalAttendanceBatchUpdateParam abnormalAttendanceBatchUpdateParam = new AbnormalAttendanceBatchUpdateParam();
                abnormalAttendanceBatchUpdateParam.setAbnormalIdList(abnormalIdList);
                abnormalAttendanceBatchUpdateParam.setUpdateType(AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode());
                abnormalAttendanceBatchUpdateParam.setConfirmTime(param.getConfirmDate());
                employeeAbnormalAttendanceService.vendorBatchAbnormalConfirmHandler(abnormalAttendanceBatchUpdateParam);
            }

            List<HrmsWarehouseDetailAbnormalDO> abnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds);
            Integer inWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode());
            Integer outWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode());
            Integer latePunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LATE.getCode());
            Integer leaveEarlyPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode());
            Integer abnormalDurationPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode());

            warehouseVendorClassesConfirmDO.setBeforeOfficeLackNum(inWithoutPunchCount);
            warehouseVendorClassesConfirmDO.setAfterOfficeLackNum(outWithoutPunchCount);
            warehouseVendorClassesConfirmDO.setLateNum(latePunchCount);
            warehouseVendorClassesConfirmDO.setLeaveEarlyNum(leaveEarlyPunchCount);
            warehouseVendorClassesConfirmDO.setAbnormalDurationNum(abnormalDurationPunchCount);
            //这里能这样写是因为劳务员工不存在上下班同时缺卡
            warehouseVendorClassesConfirmDO.setActualAttendanceNum(warehouseDetailDOS.size() - warehouseVendorClassesConfirmDO.getBeforeOfficeLackNum() - warehouseVendorClassesConfirmDO.getAfterOfficeLackNum());

            warehouseDetailDOS.forEach(warehouseDetailDO -> warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode()));
            warehouseManage.warehouseAttendanceConfirm(warehouseDetailDOS, warehouseVendorClassesConfirmDO);

            if (CollectionUtils.isNotEmpty(attendanceCalculatedList)) {
                attendancePushFinService.asyncPushFin(attendanceCalculatedList.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList()), BusinessConstant.OFF_WORK_PUNCH_IN);
            }
            return warehouseVendorClassesConfirmDO.getId();
        }

        //都是正常考勤
        warehouseVendorClassesConfirmDO.setActualAttendanceNum(warehouseDetailDOS.size());
        warehouseVendorClassesConfirmDO.setBeforeOfficeLackNum(0);
        warehouseVendorClassesConfirmDO.setAfterOfficeLackNum(0);
        warehouseVendorClassesConfirmDO.setLateNum(0);
        warehouseVendorClassesConfirmDO.setLeaveEarlyNum(0);
        warehouseVendorClassesConfirmDO.setAbnormalDurationNum(0);
        warehouseDetailDOS.forEach(warehouseDetailDO -> warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode()));
        warehouseManage.warehouseAttendanceConfirm(warehouseDetailDOS, warehouseVendorClassesConfirmDO);

        if (CollectionUtils.isNotEmpty(attendanceCalculatedList)) {
            attendancePushFinService.asyncPushFin(attendanceCalculatedList.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList()), BusinessConstant.OFF_WORK_PUNCH_IN);
        }
        return warehouseVendorClassesConfirmDO.getId();
    }

    @Override
    public CheckVendorConfirmStatusResultVO checkVendorConfirmStatus(CheckVendorConfirmStatusParam param) {
        CheckVendorConfirmStatusResultVO result = new CheckVendorConfirmStatusResultVO();
        // 获取确认状态和确认id
        HrmsWarehouseVendorClassesConfirmDO confirmResult = warehouseVendorClassesConfirmDao.selectConfirmResultBy(param.getOcId(), param.getVendorCode(), param.getClassesId(), param.getWarehouseDate());
        if (confirmResult != null) {
            result.setConfirmStatus(ConfirmStatusEnum.CONFIRM.getValue());
            result.setClassesConfirmId(confirmResult.getId());
            return result;
        }
        // 计算最晚打卡时间
        Date endTime = getTheLastClockTime(param.getWarehouseDate(), param.getClassesId());
        if (endTime == null) {
            result.setConfirmStatus(ConfirmStatusEnum.NOT_CONFIGURE.getValue());
            return result;
        }
        result.setConfirmStatus(endTime.after(param.getCurrentTime()) ? ConfirmStatusEnum.NOT_YET_TIME.getValue() : ConfirmStatusEnum.WAIT_CONFIRM.getValue());
        return result;
    }

    private void saveFaceRecord(HrmsWarehouseDetailDO warehouseDetailDO,
                                String recognitionPhoto,
                                String facePhoto,
                                Date faceTime) {
        HrmsFaceRecordDO faceRecordDO = new HrmsFaceRecordDO();
        faceRecordDO.setUserId(warehouseDetailDO.getUserId());
        faceRecordDO.setUserCode(warehouseDetailDO.getUserCode());
        faceRecordDO.setFacePhoto(facePhoto);
        faceRecordDO.setRecognitionPhoto(recognitionPhoto);
        faceRecordDO.setFaceRecordStatus(FaceRecordStatusEnum.EFFECTIVE.getCode());
        faceRecordDO.setFaceRecordTime(faceTime);
        faceRecordDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(faceTime)));
        faceRecordDO.setRecognitionScore(BigDecimal.ZERO);
        BaseDOUtil.fillDOInsert(faceRecordDO);
        faceRecordDao.save(faceRecordDO);
    }

    private Date getTheLastClockTime(Date warehouseDate, Long classId) {
//        HrmsAttendancePunchClassConfigDO classConfig = attendancePunchClassConfigDao.getById(classId);
        HrmsAttendancePunchClassConfigDO classConfig = punchConfigDaoFacade.getClassConfigAdapter().getById(classId);
//        List<HrmsAttendancePunchClassItemConfigDO> classItemList = classItemConfigDao.selectItemConfigByClassId(Lists.newArrayList(classId));
        List<HrmsAttendancePunchClassItemConfigDO> classItemList = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Lists.newArrayList(classId));
        if (classConfig == null || CollectionUtils.isEmpty(classItemList)) {
            return null;
        }
        return getTheLastClockTime(warehouseDate, classItemList, classConfig);
    }

    private Date getTheLastClockTime(Date warehouseDate, List<HrmsAttendancePunchClassItemConfigDO> classItemList, HrmsAttendancePunchClassConfigDO classConfig) {
        // 暂时只支持一个班次规则
        HrmsAttendancePunchClassItemConfigDO item = classItemList.stream().filter(e -> e.getSortNo().equals(1)).findFirst().orElse(null);
        if (item == null) {
            return null;
        }
        Long dayId = Long.valueOf(DateUtil.format(warehouseDate, DateFormatterUtil.FORMAT_YYYYMMDD));
        // 计算最晚打卡时间
        DayPunchTimeDTO punchTime = attendanceBaseService.getUserPunchClassItemDayTime(dayId, item.getId(), classItemList);
        Date startTime = punchTime.getDayPunchStartTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        BigDecimal hour2Minute = new BigDecimal(60);
        calendar.add(Calendar.MINUTE, classConfig.getAttendanceHours().multiply(hour2Minute).intValue());
        calendar.add(Calendar.MINUTE, item.getPunchInTimeInterval().multiply(hour2Minute).intValue());
        return calendar.getTime();
    }

    @Override
    public void deleteWarehouseDetail(WarehouseDetailParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getIds())) {
            return;
        }
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByIds(param.getIds());
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return;
        }
        warehouseDetailDOS.forEach(warehouse -> {
            warehouse.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(warehouse);
        });
        warehouseDetailDao.updateBatchById(warehouseDetailDOS);
        warehouseDetailAbnormalDao.deleteByWarehouseDetailIds(param.getIds());
        warehouseRecordDao.deleteByWarehouseDetailIds(param.getIds());
    }

    @Override
    public void deleteFaceFeature(String userCodes) {
        if (StringUtils.isEmpty(userCodes)) {
            return;
        }

        List<String> userCodeList = Arrays.asList(userCodes.split(","));

        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.userListByUserCodes(userCodeList);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return;
        }

        List<FaceFeatureDO> faceFeatureList = faceFeatureDAO.getByUserCodes(userCodeList);
        if (CollectionUtils.isEmpty(faceFeatureList)) {
            return;
        }

        faceFeatureList.forEach(faceFeatureDO -> {
            faceFeatureDO.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(faceFeatureDO);
        });

        faceFeatureDAO.updateBatchById(faceFeatureList);

        faceFeatureCache.deleteKey(RedisConstants.RedisPrefix.FACE_FEATURE_FULL_INFO, userInfoDOList.get(0).getLocationCountry(), userInfoDOList.get(0).getEmployeeType());
    }

    private void checkAuthOc(Long ocId) {
        String userCode = RequestInfoHolder.getUserCode();
        List<HrmsEntDeptDO> entDeptDOS = warehouseOcService.getEntDeptList(userCode);
        List<Long> authOcIdList = entDeptDOS.stream().map(HrmsEntDeptDO::getId).collect(Collectors.toList());
        if (!authOcIdList.contains(ocId)) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_NOT_HAVE_THE_OPERATION_PERMISSION_ON_THIS_STATION.getCode(),
                    I18nUtils.getMessage(HrmsErrorCodeEnums.USER_NOT_HAVE_THE_OPERATION_PERMISSION_ON_THIS_STATION.getDesc()));
        }
    }

    /**
     * 入仓检测
     *
     * @param warehouseDetailDOS 仓内考勤记录
     * @param param              出入参参数
     * @param user               用户
     * @param isMultipleShifts   是否允许上多班次
     */
    private void checkIn(List<HrmsWarehouseDetailDO> warehouseDetailDOS, InOrOutParam param, HrmsUserInfoDO user, boolean isMultipleShifts) {
        //判断是否是黑名单用户
        RpcResult<BlacklistInfoDTO> result = blacklistApi.getBlacklistInfo(user.getUserCode());
        if (result.isSuccess() && Objects.nonNull(result.getResult()) && BlacklistBanStatusEnum.getBanStatusList().contains(result.getResult().getBanStatus())) {
            addBlackListRecord(param, user, result, WarehouseBlackTypeEnum.IN);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.BLACKLIST_USER_CANNOT_ENTER, result.getResult().getReason());
        }

        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return;
        }
        //同一个网点
        List<HrmsWarehouseDetailDO> warehouseDetailDOList = warehouseDetailDOS
                .stream()
                .filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
            HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDOList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
            List<HrmsWarehouseRecordDO> recordList = warehouseRecordDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailDO.getId()));
            List<HrmsWarehouseRecordDO> inRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.IN.getCode().equals(record.getRecordType())).collect(Collectors.toList());
            List<HrmsWarehouseRecordDO> outRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.OUT.getCode().equals(record.getRecordType())).collect(Collectors.toList());

            //同一天同网点不同供应商限制作业
            if (Objects.nonNull(param.getVendorId()) && !Objects.equals(warehouseDetailDO.getVendorId(), param.getVendorId())) {
                Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(warehouseDetailDO.getVendorCode()));
                throw BusinessLogicException.getException(HrmsErrorCodeEnums.CANT_CHANGE_VENDOR, vendorMap.get(warehouseDetailDO.getVendorCode()));
            }

            if (Objects.nonNull(param.getClasses())) {
                //若不允许上多班次，在前一个班次离仓后限制作业 || 自有员工限制同网点一天上多班次
                if (Objects.nonNull(warehouseDetailDO.getClassesId()) && !Objects.equals(param.getClasses().getClassId(), warehouseDetailDO.getClassesId())) {
                    if ((!isMultipleShifts && Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType()))
                            || Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode()).contains(user.getEmployeeType())) {
                        throw BusinessException.get(HrmsErrorCodeEnums.ALREADY_WORKING_IN_THE_WAREHOSUE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ALREADY_WORKING_IN_THE_WAREHOSUE.getDesc()));
                    }
                }

                //若入仓两次的班次信息不同，要求前一段先出仓
                if (Objects.nonNull(warehouseDetailDO.getClassesId())
                        && !param.getClasses().getClassId().equals(warehouseDetailDO.getClassesId())
                        && inRecordList.size() > outRecordList.size()) {
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.EXIT_WAREHOUSE_THEN_IN, warehouseDetailDO.getClassesName());
                }
            }
        }

        //当前已存在考勤结果记录
        List<HrmsWarehouseDetailDO> otherOcOrVendorList = warehouseDetailDOS
                .stream()
                .filter(warehouse -> !Objects.equals(warehouse.getOcId(), param.getOcId()))
                .collect(Collectors.toList());
        //在其他网点当天考勤记录
        if (CollectionUtils.isNotEmpty(otherOcOrVendorList)) {
            Map<Long, HrmsWarehouseDetailDO> warehouseDetailDOMap = otherOcOrVendorList.stream().collect(Collectors.toMap(HrmsWarehouseDetailDO::getId, Function.identity()));
            Map<Long, List<HrmsWarehouseRecordDO>> recordGroupMap = warehouseRecordDao.selectByWarehouseDetailIds(otherOcOrVendorList.stream().map(HrmsWarehouseDetailDO::getId).distinct().collect(Collectors.toList()))
                    .stream()
                    .collect(Collectors.groupingBy(HrmsWarehouseRecordDO::getWarehouseDetailId));
            for (Long warehouseDetailId : recordGroupMap.keySet()) {
                HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDOMap.get(warehouseDetailId);
                List<HrmsWarehouseRecordDO> recordList = recordGroupMap.get(warehouseDetailId);
                List<HrmsWarehouseRecordDO> inRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.IN.getCode().equals(record.getRecordType())).collect(Collectors.toList());
                List<HrmsWarehouseRecordDO> outRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.OUT.getCode().equals(record.getRecordType())).collect(Collectors.toList());

                //在其他网点已入仓还未出仓 不可重复入仓
                if (inRecordList.size() > outRecordList.size() || CollectionUtils.isEmpty(recordList)) {
                    String ocName = Optional.ofNullable(entDeptDao.selectByIdList(Collections.singletonList(warehouseDetailDO.getOcId())))
                            .orElse(Lists.newArrayList()).get(BusinessConstant.FIRST_ELEMENT_INDEX).getDeptNameEn();
                    throw BusinessLogicException.getException(HrmsErrorCodeEnums.EXIT_OTHER_OC_WAREHOUSE_THEN_IN, ocName);
                }
            }
        }
    }

    @NotNull
    private HrmsWarehouseDetailDO convertWarehouseDetail(InOrOutParam param,
                                                         List<HrmsWarehouseDetailDO> warehouseDetailDOS,
                                                         Long vendorId,
                                                         String vendorCode,
                                                         HrmsUserInfoDO user,
                                                         EntOcApiDTO oc,
                                                         String dayType,
                                                         Date warehouseDate,
                                                         Integer warehouseType,
                                                         HrmsWarehouseAttendanceConfigDO warehouseAttendanceConfigDO) {
        if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
            if (Objects.isNull(param.getClasses())) {
                return warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId())).findFirst().orElse(warehouseDetailDOS.get(BusinessConstant.FIRST_ELEMENT_INDEX));
            }

            Optional<HrmsWarehouseDetailDO> warehouseDetailDOOptional = warehouseDetailDOS
                    .stream()
                    .filter(record -> Objects.equals(record.getOcId(), param.getOcId())
                            && Objects.equals(param.getClasses().getClassId(), record.getClassesId()))
                    .findFirst();
            if (warehouseDetailDOOptional.isPresent()) {
                return warehouseDetailDOOptional.get();
            }
            List<HrmsWarehouseDetailDO> noClassWarehouseDetailDOList = warehouseDetailDOS
                    .stream()
                    .filter(record -> Objects.equals(record.getOcId(), param.getOcId())
                            && Objects.isNull(record.getClassesId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noClassWarehouseDetailDOList)) {
                HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDOS.get(BusinessConstant.FIRST_ELEMENT_INDEX);
                warehouseDetailDO.setClassesId(param.getClasses().getClassId());
                warehouseDetailDO.setClassesType(param.getClasses().getClassType());
                warehouseDetailDO.setClassesName(param.getClasses().getClassName());
                warehouseDetailDO.setLegalWorkingHours(param.getClasses().getLegalWorkingHours());
                warehouseDetailDO.setRequiredAttendanceTime(param.getClasses().getAttendanceHours());
                return warehouseDetailDO;
            }
        }

        HrmsWarehouseDetailDO hrmsWarehouseDetailDO = new HrmsWarehouseDetailDO();
        hrmsWarehouseDetailDO.setCountry(oc.getCountry());
        hrmsWarehouseDetailDO.setCity(oc.getCity());
        hrmsWarehouseDetailDO.setOcId(param.getOcId());
        hrmsWarehouseDetailDO.setOcLongitude(oc.getLongitude());
        hrmsWarehouseDetailDO.setOcLatitude(oc.getLatitude());
        hrmsWarehouseDetailDO.setVendorId(vendorId);
        hrmsWarehouseDetailDO.setVendorCode(vendorCode);
        hrmsWarehouseDetailDO.setUserOcId(user.getDeptId());
        hrmsWarehouseDetailDO.setUserVendorId(user.getVendorId());
        hrmsWarehouseDetailDO.setUserVendorCode(user.getVendorCode());
        hrmsWarehouseDetailDO.setUserId(user.getId());
        hrmsWarehouseDetailDO.setUserCode(user.getUserCode());
        hrmsWarehouseDetailDO.setWarehouseDate(warehouseDate);
        hrmsWarehouseDetailDO.setSalaryDate(warehouseDate);
        hrmsWarehouseDetailDO.setAttendanceType(dayType);
        hrmsWarehouseDetailDO.setEmployeeType(user.getEmployeeType());
        hrmsWarehouseDetailDO.setWarehouseStatus(Objects.equals(WarehouseTypeEnum.IN.getCode(), warehouseType) ? WarehouseStatusEnum.WAIT_OUT.getCode() : WarehouseStatusEnum.OUT.getCode());
        hrmsWarehouseDetailDO.setConfirmStatus(convertConfirmStatus(param.getOcId(), user));
        hrmsWarehouseDetailDO.setEmploymentForm(warehouseUserService.getEmploymentForm(user.getId()));
        if (Objects.nonNull(warehouseAttendanceConfigDO)) {
            hrmsWarehouseDetailDO.setAttendanceConfigId(warehouseAttendanceConfigDO.getId());
        }
        BaseDOUtil.fillDOInsert(hrmsWarehouseDetailDO);

        if (Objects.isNull(param.getClasses())) {
            hrmsWarehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode());
        } else {
            hrmsWarehouseDetailDO.setClassesId(param.getClasses().getClassId());
            hrmsWarehouseDetailDO.setClassesType(param.getClasses().getClassType());
            hrmsWarehouseDetailDO.setClassesName(param.getClasses().getClassName());
            hrmsWarehouseDetailDO.setLegalWorkingHours(param.getClasses().getLegalWorkingHours());
            hrmsWarehouseDetailDO.setRequiredAttendanceTime(param.getClasses().getAttendanceHours());
        }
        return hrmsWarehouseDetailDO;
    }

    private Integer convertConfirmStatus(Long ocId, HrmsUserInfoDO userInfo) {
        if (!Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), userInfo.getEmployeeType())) {
            return WarehouseClassConfirmStatusEnum.INIT.getCode();
        }
        List<String> ocIdList = Arrays.stream(hrmsProperties.getVendor().getOcId().split(",")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ocIdList) && ocIdList.contains(ocId.toString())) {
            return WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode();
        }
        return WarehouseClassConfirmStatusEnum.NOT_CONFIG.getCode();
    }

    private HrmsWarehouseRecordDO convertWarehouseRecord(InOrOutParam param,
                                                         Long vendorId,
                                                         String vendorCode,
                                                         HrmsUserInfoDO user,
                                                         EntOcApiDTO oc,
                                                         Long faceRecordId,
                                                         Date warehouseDate) {
        HrmsWarehouseRecordDO inRecord = new HrmsWarehouseRecordDO();
        inRecord.setCountry(oc.getCountry());
        inRecord.setCity(oc.getCity());
        inRecord.setOcId(param.getOcId());
        inRecord.setUserOcId(user.getDeptId());
        inRecord.setVendorId(vendorId);
        inRecord.setVendorCode(vendorCode);
        inRecord.setUserVendorId(user.getVendorId());
        inRecord.setUserVendorCode(user.getVendorCode());
        inRecord.setUserId(user.getId());
        inRecord.setUserCode(user.getUserCode());
        inRecord.setRecordType(WarehouseTypeEnum.IN.getCode());
        inRecord.setWarehouseDate(warehouseDate);
        inRecord.setWarehouseTime(param.getAttendanceTime());
        inRecord.setFaceRecordId(faceRecordId);
        inRecord.setOcLongitude(param.getLongitude());
        inRecord.setOcLatitude(param.getLatitude());
        inRecord.setDistance(calculateDistance(oc.getLongitude(), oc.getLatitude(), param.getLongitude(), param.getLatitude()));
        BaseDOUtil.fillDOInsert(inRecord);
        return inRecord;
    }

    private EmployeePunchRecordDO convertPunchRecord(Long ocId,
                                                     Date attendanceTime,
                                                     Long classId,
                                                     HrmsUserInfoDO user,
                                                     EntOcApiDTO oc,
                                                     String dayId) {
        EmployeePunchRecordDO punchRecordDO = new EmployeePunchRecordDO();
        punchRecordDO.setCountry(oc.getCountry());
        punchRecordDO.setDeptId(ocId);
        punchRecordDO.setOcCode(oc.getOcCode());
        punchRecordDO.setOcName(oc.getOcName());
        punchRecordDO.setUserCode(user.getUserCode());
        punchRecordDO.setEmployeeType(user.getEmployeeType());
        punchRecordDO.setDayId(dayId);
        punchRecordDO.setPunchTime(attendanceTime);
        punchRecordDO.setSourceType(SourceTypeEnum.WPM.name());
        punchRecordDO.setPunchCardType(HrmsPunchCardTypeEnum.WPM_FACE.name());
        punchRecordDO.setLongitude(BigDecimal.ZERO);
        punchRecordDO.setLatitude(BigDecimal.ZERO);
        if (Objects.nonNull(classId)) {
            punchRecordDO.setClassId(classId);
        }
        return punchRecordDO;
    }

    private EmployeePunchRecordDO convertPunchRecord(HrmsWarehouseDetailDO warehouseDetailDO, EntOcApiDTO oc, Date attendanceTime) {
        EmployeePunchRecordDO punchRecordDO = new EmployeePunchRecordDO();
        punchRecordDO.setCountry(warehouseDetailDO.getCountry());
        punchRecordDO.setDeptId(warehouseDetailDO.getOcId());
        punchRecordDO.setOcCode(oc.getOcCode());
        punchRecordDO.setOcName(oc.getOcName());
        punchRecordDO.setUserCode(warehouseDetailDO.getUserCode());
        punchRecordDO.setEmployeeType(warehouseDetailDO.getEmployeeType());
        punchRecordDO.setDayId(DateUtil.format(attendanceTime, DateFormatterUtil.FORMAT_YYYYMMDD));
        punchRecordDO.setPunchTime(attendanceTime);
        punchRecordDO.setSourceType(SourceTypeEnum.WPM.name());
        punchRecordDO.setPunchCardType(HrmsPunchCardTypeEnum.WPM_FACE.name());
        punchRecordDO.setLongitude(BigDecimal.ZERO);
        punchRecordDO.setLatitude(BigDecimal.ZERO);
        if (Objects.nonNull(warehouseDetailDO.getClassesId())) {
            punchRecordDO.setClassId(warehouseDetailDO.getClassesId());
        }
        return punchRecordDO;
    }

    /**
     * 生成排班计划
     *
     * @param classes        班次信息
     * @param attendanceTime 考勤时间
     * @param dayId          考勤日
     * @param userId         用户id
     */
    private void generateSchedulingPlan(ClassesParam classes, Date attendanceTime, Long dayId, Long userId) {
        HrmsAttendanceClassEmployeeConfigParam configAddParam = new HrmsAttendanceClassEmployeeConfigParam();
        configAddParam.setUserId(userId);
        configAddParam.setPunchConfigId(classes.getPunchConfigId());
        configAddParam.setPunchConfigNo(classes.getPunchConfigNo());
        DaysConfigParam daysConfigParam = new DaysConfigParam();
        daysConfigParam.setDayId(Objects.isNull(dayId) ? Long.valueOf(DateUtil.format(attendanceTime, DateFormatterUtil.FORMAT_YYYYMMDD)) : dayId);
        daysConfigParam.setDate(DateUtil.parse(daysConfigParam.getDayId().toString(), DateFormatterUtil.FORMAT_YYYYMMDD));
        daysConfigParam.setPunchConfigId(classes.getPunchConfigId());
        daysConfigParam.setClassId(classes.getClassId());
        daysConfigParam.setClassName(classes.getClassName());
        daysConfigParam.setDayPunchType(classes.getDayPunchType());
        configAddParam.setDaysConfigParamList(Collections.singletonList(daysConfigParam));
        log.info("生成排班计划入参: {}", JSON.toJSONString(configAddParam));
        attendanceClassEmployeeConfigService.add(configAddParam);
    }

    /**
     * 查询指定日期日历出勤类型
     *
     * @param attendanceTime 出勤时间
     * @param user           用户
     * @return 出勤类型
     */
    private String getCurrentDayType(Date attendanceTime, HrmsUserInfoDO user) {
        WarehouseAttendanceConfigQuery query = new WarehouseAttendanceConfigQuery();
        query.setUserId(user.getId());
        query.setLocationCountry(user.getLocationCountry());
        query.setNow(attendanceTime);
//        WarehouseAttendanceConfigDetailDTO warehouseAttendanceConfigDetail = punchConfigService.selectNowAttendanceConfigDetail(query);
        WarehouseAttendanceConfigDetailDTO warehouseAttendanceConfigDetail = punchConfigServiceAdapter.selectNowAttendanceConfigDetail(query);
        return Objects.nonNull(warehouseAttendanceConfigDetail) ? warehouseAttendanceConfigDetail.getDayType() : null;
    }

    private HrmsWarehouseRecordDO convertWarehouseRecord(OutParam param,
                                                         EntOcApiDTO oc,
                                                         Long vendorId,
                                                         String vendorCode,
                                                         HrmsUserInfoDO user,
                                                         HrmsFaceRecordDO faceRecord,
                                                         Date warehouseDate,
                                                         HrmsWarehouseDetailDO warehouseDetailDO) {
        String country = oc.getCountry();
        String city = oc.getCity();
        Long ocId = param.getOcId();
        if (Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType()) && Objects.nonNull(warehouseDetailDO)) {
            country = warehouseDetailDO.getCountry();
            city = warehouseDetailDO.getCity();
            ocId = warehouseDetailDO.getOcId();
            vendorId = warehouseDetailDO.getVendorId();
            vendorCode = warehouseDetailDO.getVendorCode();
        }
        HrmsWarehouseRecordDO outRecord = new HrmsWarehouseRecordDO();
        outRecord.setCountry(country);
        outRecord.setCity(city);
        outRecord.setOcId(ocId);
        outRecord.setUserOcId(user.getDeptId());
        outRecord.setVendorId(vendorId);
        outRecord.setVendorCode(vendorCode);
        outRecord.setUserVendorId(user.getVendorId());
        outRecord.setUserVendorCode(user.getVendorCode());
        outRecord.setUserId(user.getId());
        outRecord.setUserCode(user.getUserCode());
        outRecord.setRecordType(WarehouseTypeEnum.OUT.getCode());
        outRecord.setWarehouseDate(warehouseDate);
        outRecord.setWarehouseTime(param.getAttendanceTime());
        outRecord.setFaceRecordId(Objects.nonNull(faceRecord) ? faceRecord.getId() : null);
        outRecord.setOcLongitude(param.getLongitude());
        outRecord.setOcLatitude(param.getLatitude());
        outRecord.setDistance(calculateDistance(oc.getLongitude(), oc.getLatitude(), param.getLongitude(), param.getLatitude()));
        BaseDOUtil.fillDOInsert(outRecord);
        return outRecord;
    }

    private HrmsWarehouseRecordDO convertWarehouseRecord(HrmsWarehouseDetailDO warehouseDetailDO,
                                                         EntOcApiDTO oc,
                                                         Long faceRecordId,
                                                         QuickOutParam param) {
        HrmsWarehouseRecordDO outRecord = new HrmsWarehouseRecordDO();
        outRecord.setCountry(oc.getCountry());
        outRecord.setCity(oc.getCity());
        outRecord.setOcId(warehouseDetailDO.getOcId());
        outRecord.setUserOcId(warehouseDetailDO.getOcId());
        outRecord.setVendorId(warehouseDetailDO.getVendorId());
        outRecord.setVendorCode(warehouseDetailDO.getVendorCode());
        outRecord.setUserVendorId(warehouseDetailDO.getVendorId());
        outRecord.setUserVendorCode(warehouseDetailDO.getVendorCode());
        outRecord.setUserId(warehouseDetailDO.getUserId());
        outRecord.setUserCode(warehouseDetailDO.getUserCode());
        outRecord.setRecordType(WarehouseTypeEnum.OUT.getCode());
        outRecord.setWarehouseDate(warehouseDetailDO.getWarehouseDate());
        outRecord.setWarehouseTime(param.getAttendanceTime());
        outRecord.setFaceRecordId(faceRecordId);
        outRecord.setOcLongitude(param.getLongitude());
        outRecord.setOcLatitude(param.getLatitude());
        outRecord.setDistance(calculateDistance(oc.getLongitude(), oc.getLatitude(), param.getLongitude(), param.getLatitude()));
        BaseDOUtil.fillDOInsert(outRecord);
        return outRecord;
    }

    /**
     * 出仓检测
     *
     * @param param 出仓入参
     */
    private OutVO checkOut(List<HrmsWarehouseDetailDO> warehouseDetailList,
                           OutParam param,
                           HrmsUserInfoDO user,
                           Long vendorId,
                           String vendorCode) {
        //判断是否是黑名单用户
        RpcResult<BlacklistInfoDTO> result = blacklistApi.getBlacklistInfo(user.getUserCode());
        if (result.isSuccess() && Objects.nonNull(result.getResult()) && BlacklistBanStatusEnum.getBanStatusList().contains(result.getResult().getBanStatus())) {
            addBlackListRecord(param, user, result, WarehouseBlackTypeEnum.OUT);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.BLACKLIST_USER_CANNOT_ENTER, result.getResult().getReason());
        }

        OutVO outVO = new OutVO();
        //未入仓直接出仓
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            if (Objects.isNull(param.getClasses())) {
                log.info("无班次员工{},必须先入仓再出仓", user.getUserCode());
                throw BusinessException.get(HrmsErrorCodeEnums.FACE_IN_FIRST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.FACE_IN_FIRST.getDesc()));
            }
            return outVO;
        }

        if (param.getUpdateInVendor()) {
            return outVO;
        }

        //匹配网点或供应商
        Optional<HrmsWarehouseDetailDO> allSameOptional = warehouseDetailList
                .stream()
                .filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()) && Objects.equals(warehouse.getVendorCode(), vendorCode))
                .findFirst();
        if (allSameOptional.isPresent()) {
            HrmsWarehouseDetailDO warehouseDetailDO = allSameOptional.get();
            //自有员工考勤日限制上多班次
            if (Lists.newArrayList(WarehouseAttendanceStatusEnum.NORMAL.getCode(), WarehouseAttendanceStatusEnum.ABNORMAL.getCode()).contains(warehouseDetailDO.getAttendanceStatus())
                    && !Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType())
                    && !Objects.equals(warehouseDetailDO.getClassesId(), param.getClasses().getClassId())) {
                throw BusinessException.get(HrmsErrorCodeEnums.ALREADY_WORKING_OUT_THE_WAREHOSUE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ALREADY_WORKING_OUT_THE_WAREHOSUE.getDesc()));
            }
            return outVO;
        }

        List<HrmsWarehouseDetailDO> anySameList = warehouseDetailList
                .stream()
                .filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()) || Objects.equals(warehouse.getVendorCode(), vendorCode))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(anySameList)) {
            HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
            return convertOutVO(param, vendorId, vendorCode, Boolean.TRUE, Boolean.TRUE, warehouseDetailDO);
        }

        Optional<HrmsWarehouseDetailDO> vendorDifferentOptional = anySameList.stream().filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()) && !Objects.equals(warehouse.getVendorCode(), vendorCode)).findFirst();
        if (vendorDifferentOptional.isPresent() && Objects.equals(CountryCodeEnum.MEX.getCode(), vendorDifferentOptional.get().getCountry())) {
            return convertOutVO(param, vendorId, vendorCode, Boolean.FALSE, Boolean.TRUE, vendorDifferentOptional.get());
        }

        Optional<HrmsWarehouseDetailDO> ocDifferentOptional = anySameList.stream().filter(warehouse -> !Objects.equals(warehouse.getOcId(), param.getOcId()) && Objects.equals(warehouse.getVendorCode(), vendorCode)).findFirst();
        return ocDifferentOptional.map(warehouseDetailDO -> convertOutVO(param, vendorId, vendorCode, Boolean.TRUE, Boolean.FALSE, warehouseDetailDO)).orElse(outVO);
    }

    private void addBlackListRecord(InOrOutParam param, HrmsUserInfoDO user, RpcResult<BlacklistInfoDTO> result, WarehouseBlackTypeEnum type) {
        try {
            HrmsWarehouseBlackListDO blackListDO = new HrmsWarehouseBlackListDO();
            blackListDO.setId(iHrmsIdWorker.nextId());
            blackListDO.setUserId(user.getId());
            blackListDO.setUserCode(user.getUserCode());
            blackListDO.setEmployeeType(user.getEmployeeType());
            blackListDO.setOcId(param.getOcId());
            if (StringUtils.isNotBlank(param.getVendorCode())) {
                blackListDO.setVendorCode(param.getVendorCode());
            }
            if (param.getClasses() != null) {
                blackListDO.setClassesId(param.getClasses().getClassId());
            }
            blackListDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(param.getWarehouseDate())));
            blackListDO.setType(type.getCode());
            blackListDO.setReason(result.getResult().getReason());
            hrmsWarehouseBlackListDao.save(blackListDO);
        } catch (Exception ex) {
            log.error("checkIn 添加黑名单记录失败, param: {}", JSONObject.toJSONString(param), ex);
        }
    }

    @NotNull
    private OutVO convertOutVO(OutParam param,
                               Long vendorId,
                               String vendorCode,
                               Boolean ocChange,
                               Boolean vendorChange,
                               HrmsWarehouseDetailDO warehouseDetailDO) {
        OutVO outVO = new OutVO();
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Lists.newArrayList(warehouseDetailDO.getVendorCode(), vendorCode));
        Map<Long, String> ocMap = getOcMap(Lists.newArrayList(warehouseDetailDO.getOcId(), param.getOcId()));
        outVO.setInOcId(warehouseDetailDO.getOcId());
        outVO.setInOcName(ocMap.get(warehouseDetailDO.getOcId()));
        outVO.setOutOcId(param.getOcId());
        outVO.setOutOcName(ocMap.get(param.getOcId()));
        outVO.setInVendorId(warehouseDetailDO.getVendorId());
        outVO.setInVendorCode(warehouseDetailDO.getVendorCode());
        outVO.setInVendorName(vendorMap.get(warehouseDetailDO.getVendorCode()));
        outVO.setOutVendorId(vendorId);
        outVO.setOutVendorCode(vendorCode);
        outVO.setOutVendorName(vendorMap.get(vendorCode));
        outVO.setCheckOutResult(Boolean.FALSE);
        outVO.setOcChange(ocChange);
        outVO.setVendorChange(vendorChange);
        return outVO;
    }

    private HrmsUserInfoDO getUserForInOrOut(Long userId) {
        HrmsUserInfoDO user = userInfoDao.getById(userId);
        if (Objects.isNull(user)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        return user;
    }

    private BigDecimal calculateDistance(BigDecimal longitude1, BigDecimal latitude1, BigDecimal longitude2, BigDecimal latitude2) {
        if (Objects.isNull(longitude1) || Objects.isNull(latitude1) || Objects.isNull(longitude2) || Objects.isNull(latitude2)) {
            return null;
        }
        double distance = DistanceCalculatorUtils.getDistance(longitude1.doubleValue(), latitude1.doubleValue(), longitude2.doubleValue(), latitude2.doubleValue());
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(distance));
        return bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }

    private static void setPcsStatus(HrmsWarehouseDetailDO warehouseDetail) {
        if (!Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetail.getEmployeeType())) {
            return;
        }
        if (Objects.isNull(warehouseDetail.getActualAttendanceTime())) {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
        }
        if (warehouseDetail.getActualAttendanceTime().compareTo(BigDecimal.ZERO) > 0) {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.NORMAL.getCode());
        } else {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
        }
    }

    private void checkWarehouseDate(Date warehouseDate, Date attendanceTime, Long classId, Integer warehouseType) {
        if (Objects.isNull(classId)) {
            return;
        }
        Long dayId = Long.parseLong(DateUtil.format(warehouseDate, DatePattern.PURE_DATE_PATTERN));
//        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigList = classItemConfigDao.selectItemConfigByClassId(Collections.singletonList(classId));
        List<HrmsAttendancePunchClassItemConfigDO> punchClassItemConfigList = punchConfigDaoFacade.getClassItemConfigAdapter().selectItemConfigByClassId(Collections.singletonList(classId));
        HrmsAttendancePunchClassItemConfigDO attendancePunchClassItemConfigDO = punchClassItemConfigList.get(0);
        DayPunchTimeDTO dayPunchTime = attendanceBaseService.getUserPunchClassItemDayTime(dayId, attendancePunchClassItemConfigDO.getId(), punchClassItemConfigList);
        log.info("checkWarehouseDate classId : {} dayPunchTimeDTO : {}", classId, dayPunchTime);
        if (ObjectUtil.isNull(dayPunchTime) || dayPunchTime.getDayPunchStartTime().compareTo(dayPunchTime.getDayPunchEndTime()) > -1) {
            log.info("checkWarehouseDate dayPunchTimeDTO data error");
        }
        if (attendanceTime.compareTo(dayPunchTime.getDayPunchStartTime()) < 0) {
            throw BusinessException.get(HrmsErrorCodeEnums.BEFORE_CLASS_EARLIEST_PUNCH_IN_TIME.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.BEFORE_CLASS_EARLIEST_PUNCH_IN_TIME.getDesc()));
        }

        if (Objects.equals(WarehouseTypeEnum.OUT.getCode(), warehouseType)) {
            Date earliestPunchInTime = dayPunchTime.getDayPunchStartTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(earliestPunchInTime);
            if (Objects.nonNull(attendancePunchClassItemConfigDO.getPunchInTimeInterval())) {
                calendar.add(Calendar.MINUTE, attendancePunchClassItemConfigDO.getPunchInTimeInterval().multiply(new BigDecimal(60)).intValue());
            }
            if (Objects.nonNull(attendancePunchClassItemConfigDO.getElasticTime())) {
                calendar.add(Calendar.MINUTE, attendancePunchClassItemConfigDO.getElasticTime().multiply(new BigDecimal(60)).intValue());
            }
            Date latestPunchInTime = calendar.getTime();
            if (attendanceTime.compareTo(earliestPunchInTime) > -1 && attendanceTime.compareTo(latestPunchInTime) < 1) {
                log.info("上班时间内未入仓直接打出仓卡");
                throw BusinessException.get(HrmsErrorCodeEnums.FACE_IN_FIRST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.FACE_IN_FIRST.getDesc()));
            }
        }
    }

    /**
     * 巴西劳务派遣供应商校验
     */
    private void braInVendorCheck(InOrOutParam param) {
        HrmsEntDeptDO entDeptDO = entDeptDao.getById(param.getOcId());
        if (Objects.isNull(entDeptDO)) {
            throw BusinessException.get(HrmsErrorCodeEnums.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.STATION_NOT_EXITS.getDesc()));
        }
        if (!StringUtils.equalsAnyIgnoreCase(entDeptDO.getCountry(), CountryCodeEnum.BRA.getCode())) {
            return;
        }
        if (!BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())) {
            return;
        }
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.getByUserIds(param.getUserIdList());
        Map<Long, HrmsUserInfoDO> userMap = userInfoDOList.stream().collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity()));

        List<String> vendorCodeList = userInfoDOList.stream()
                .map(HrmsUserInfoDO::getVendorCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        vendorCodeList.add(param.getVendorCode());
        Map<String, String> vendorNameMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        for (Long userId : param.getUserIdList()) {
            HrmsUserInfoDO existUserInfo = userMap.get(userId);
            if (Objects.isNull(existUserInfo)) {
                throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
            }
            if (Objects.equals(param.getVendorCode(), existUserInfo.getVendorCode())) {
                continue;
            }
            String userVendorName = vendorNameMap.get(existUserInfo.getVendorCode());
            String warehouseVendorName = vendorNameMap.get(param.getVendorCode());
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.USER_VENDOR_NOT_SAME_AS_WAREHOUSE_VENDOR, userVendorName, warehouseVendorName);
        }
    }


    private Integer abnormalCount(List<HrmsWarehouseDetailAbnormalDO> abnormalList, String abnormalType) {
        Map<Integer, List<HrmsWarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getProcessed));
        int result = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList()).size();
        List<HrmsWarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        List<Long> abnormalIds = processedList.stream().map(HrmsWarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
        List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
        List<Long> confirmAbnormalIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
            List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                    .stream()
                    .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
            confirmAbnormalIds = abnormalOperationRecordList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
            result += abnormalOperationRecordList.size();
        }

        if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
            List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
            long abnormalDurationCount = processedList.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                    && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count();
            result += abnormalDurationCount;
        }

        return result;
    }

    private Integer abnormalDurationCount(List<HrmsWarehouseDetailAbnormalDO> abnormalList, Set<Long> warehouseIds) {
        String abnormalType = AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode();
        Map<Integer, List<HrmsWarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getProcessed));
        List<HrmsWarehouseDetailAbnormalDO> processingAbnormalList = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList());
        int result = processingAbnormalList.size();
        Set<Long> warehouseIdList = processingAbnormalList.stream().map(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(warehouseIdList)) {
            warehouseIds.addAll(warehouseIdList);
        }
        List<HrmsWarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        Map<Long, List<HrmsWarehouseDetailAbnormalDO>> processedMap = processedList.stream().collect(Collectors.groupingBy(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId));
        for (Long warehouseId : processedMap.keySet()) {
            List<HrmsWarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = processedMap.get(warehouseId);

            List<Long> abnormalIds = warehouseDetailAbnormalDOS.stream().map(HrmsWarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
            List<HrmsEmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
            List<Long> confirmAbnormalIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
                List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AttendanceAbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                confirmAbnormalIds = abnormalOperationRecordList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
                result += abnormalOperationRecordList.size();
                warehouseIds.add(warehouseId);
            }

            if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
                List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
                int abnormalDurationCount = Math.toIntExact(warehouseDetailAbnormalDOS.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                        && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count());
                result += abnormalDurationCount;
                if (abnormalDurationCount > 0) {
                    warehouseIds.add(warehouseId);
                }
            }
        }

        return result;
    }

    private void checkPunchTime(String country, Date attendanceTime) {
        if (hrmsProperties.getVendor().getIsCheckPunchTimeOn()) {
            //临时方案颗粒度粗暂时使用
            CountryDTO countryDTO = countryService.queryCountry(country);
            Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
            long timeInterval = DateUtil.between(date, attendanceTime, DateUnit.MINUTE);
            if (Objects.equals(CountryCodeEnum.MEX.getCode(), country) && timeInterval > 120) {
                throw BusinessException.get(HrmsErrorCodeEnums.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getDesc()));
            }
            if (Objects.equals(CountryCodeEnum.BRA.getCode(), country) && timeInterval > 60) {
                throw BusinessException.get(HrmsErrorCodeEnums.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getDesc()));
            }
        }
    }

    private void supplierClassConfirmRollbackHandler(List<HrmsWarehouseDetailDO> warehouseDetailList,
                                                     List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList,
                                                     List<HrmsEmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList,
                                                     List<HrmsWarehouseVendorClassesConfirmDO> udpateVendorClassesConfirmDOList,
                                                     List<Long> confirmAbnormalIds) {

        Map<String, List<HrmsWarehouseDetailDO>> warehouseGroupMap = warehouseDetailList
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouse.getConfirmStatus()))
                .collect(Collectors.groupingBy(warehouse -> DateUtil.formatDate(warehouse.getWarehouseDate()) + "_" + warehouse.getOcId() + "_" + warehouse.getVendorCode() + "_" + warehouse.getClassesId()));


        for (String warehouseKey : warehouseGroupMap.keySet()) {
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseGroupMap.get(warehouseKey);
            String[] keySplit = warehouseKey.split("_");
            Date warehouseDate = DateUtil.parseDate(keySplit[0]);
            Long ocId = Long.valueOf(keySplit[1]);
            String vendorCode = keySplit[2];
            Long classId = Long.valueOf(keySplit[3]);
            HrmsWarehouseVendorClassesConfirmDO vendorClassesConfirmDO = warehouseVendorClassesConfirmDao.selectConfirmResultBy(ocId, vendorCode, classId, warehouseDate);
            if (Objects.isNull(vendorClassesConfirmDO)) {
                continue;
            }

            //异常表状态回退待处理 异常操作记录逻辑删除
            List<HrmsWarehouseDetailAbnormalDO> abnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailDOS.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList()));
            List<Long> abnormalIds = abnormalList.stream().map(HrmsWarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
            List<HrmsEmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = abnormalAttendanceDao.selectAbnormalByIdList(abnormalIds);

            //过滤得到供应商确认异常的操作记录
            List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds)
                    .stream()
                    .filter(abnormalRecord -> Objects.equals(BusinessConstant.SUPPLIER, abnormalRecord.getCreateUserCode()))
                    .collect(Collectors.toList());
            abnormalOperationRecordDOList.forEach(abnormal -> {
                abnormal.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(abnormal);
            });
            deleteAbnormalOperationRecordList.addAll(abnormalOperationRecordDOList);

            List<Long> confirmAbnormalIdList = abnormalOperationRecordDOList.stream().map(HrmsEmployeeAbnormalOperationRecordDO::getAbnormalId).distinct().collect(Collectors.toList());
            List<HrmsEmployeeAbnormalAttendanceDO> confirmAbnormalAttendanceDOList = employeeAbnormalAttendanceDOList.stream().filter(abnormal -> confirmAbnormalIdList.contains(abnormal.getId())).collect(Collectors.toList());
            confirmAbnormalAttendanceDOList.forEach(abnormal -> {
                abnormal.setStatus(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode());
                fillDOUpdate(abnormal);
            });
            updateAbnormalAttendanceList.addAll(confirmAbnormalAttendanceDOList);

            confirmAbnormalIds.addAll(confirmAbnormalIdList);

            Integer outWithoutPunchCount;
            Integer latePunchCount = 0;
            Integer leaveEarlyPunchCount = 0;
            Integer abnormalDurationCount = 0;
            Integer inWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode());
            List<HrmsWarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(warehouseDate), ocId);
            if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
                Set<Long> warehouseIds = new HashSet<>();
                abnormalDurationCount = abnormalDurationCount(abnormalList, warehouseIds);
                outWithoutPunchCount = Math.toIntExact(warehouseDetailDOS.stream()
                        .filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())
                                && !warehouseIds.contains(warehouse.getId())).count());
            } else {
                outWithoutPunchCount = Math.toIntExact(warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())).count());
                latePunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LATE.getCode());
                leaveEarlyPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode());
            }

            // 实际出勤 = 总记录 - 出仓未打卡 - 入仓未打卡
            Integer punchCount = Math.max(warehouseDetailDOS.size() - outWithoutPunchCount - inWithoutPunchCount, 0);

            vendorClassesConfirmDO.setActualAttendanceNum(Math.max(vendorClassesConfirmDO.getActualAttendanceNum() - punchCount, 0));
            vendorClassesConfirmDO.setBeforeOfficeLackNum(Math.max(vendorClassesConfirmDO.getBeforeOfficeLackNum() - inWithoutPunchCount, 0));
            vendorClassesConfirmDO.setAfterOfficeLackNum(Math.max(vendorClassesConfirmDO.getAfterOfficeLackNum() - outWithoutPunchCount, 0));
            vendorClassesConfirmDO.setLateNum(Math.max(vendorClassesConfirmDO.getLateNum() - latePunchCount, 0));
            vendorClassesConfirmDO.setLeaveEarlyNum(Math.max(vendorClassesConfirmDO.getLeaveEarlyNum() - leaveEarlyPunchCount, 0));
            vendorClassesConfirmDO.setAbnormalDurationNum(Math.max(vendorClassesConfirmDO.getAbnormalDurationNum() - abnormalDurationCount, 0));
            if (vendorClassesConfirmDO.getActualAttendanceNum() == 0 && vendorClassesConfirmDO.getBeforeOfficeLackNum() == 0 && vendorClassesConfirmDO.getAfterOfficeLackNum() == 0) {
                vendorClassesConfirmDO.setIsDelete(IsDeleteEnum.YES.getCode());
            }
            fillDOUpdate(vendorClassesConfirmDO);
            udpateVendorClassesConfirmDOList.add(vendorClassesConfirmDO);
        }
    }


    private void clearOriginalClassAttendanceData(List<HrmsWarehouseDetailDO> warehouseDetailList, List<HrmsAttendanceEmployeeDetailDO> attendanceEmployeeDetailList, List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList, List<HrmsEmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList) {
        Map<Integer, List<HrmsWarehouseDetailDO>> warehouseAttendanceStatusMap = warehouseDetailList
                .stream()
                .filter(warehouse -> Lists.newArrayList(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), WarehouseAttendanceStatusEnum.NORMAL.getCode()).contains(warehouse.getAttendanceStatus()))
                .collect(Collectors.groupingBy(HrmsWarehouseDetailDO::getAttendanceStatus));

        List<HrmsWarehouseDetailDO> normalWarehouseList = warehouseAttendanceStatusMap.get(WarehouseAttendanceStatusEnum.NORMAL.getCode());
        if (CollectionUtils.isNotEmpty(normalWarehouseList)) {
            for (HrmsWarehouseDetailDO warehouseDetailDO : normalWarehouseList) {
                HrmsAttendanceEmployeeDetailDO attendanceEmployeeDetailDO = attendanceEmployeeDetailDao.getHrmsAttendanceEmployeeDetailDO(warehouseDetailDO.getUserId(), Long.valueOf(DateUtil.format(warehouseDetailDO.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN)));
                if (Objects.isNull(attendanceEmployeeDetailDO)) {
                    continue;
                }
                attendanceEmployeeDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(attendanceEmployeeDetailDO);
                attendanceEmployeeDetailList.add(attendanceEmployeeDetailDO);
            }
        }

        List<HrmsWarehouseDetailDO> abnormalWarehouseList = warehouseAttendanceStatusMap.get(WarehouseAttendanceStatusEnum.ABNORMAL.getCode());
        if (CollectionUtils.isNotEmpty(abnormalWarehouseList)) {
            for (HrmsWarehouseDetailDO warehouseDetailDO : abnormalWarehouseList) {
                AbnormalAttendanceQuery query = new AbnormalAttendanceQuery();
                query.setUserIds(Collections.singletonList(warehouseDetailDO.getUserId()));
                query.setDayId(DateUtil.format(warehouseDetailDO.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN));
                List<HrmsEmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceList = abnormalAttendanceDao.listAbnormal(query);
                if (CollectionUtils.isEmpty(employeeAbnormalAttendanceList)) {
                    continue;
                }
                employeeAbnormalAttendanceList.forEach(employeeAbnormal -> {
                    employeeAbnormal.setIsDelete(IsDeleteEnum.YES.getCode());
                    fillDOUpdate(employeeAbnormal);
                });
                updateAbnormalAttendanceList.addAll(employeeAbnormalAttendanceList);
                List<Long> abnormalIds = employeeAbnormalAttendanceList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
                List<HrmsEmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
                if (CollectionUtils.isEmpty(abnormalOperationRecordDOList)) {
                    continue;
                }
                abnormalOperationRecordDOList.forEach(employeeAbnormalRecord -> {
                    employeeAbnormalRecord.setIsDelete(IsDeleteEnum.YES.getCode());
                    fillDOUpdate(employeeAbnormalRecord);
                });
                deleteAbnormalOperationRecordList.addAll(abnormalOperationRecordDOList);
            }
        }
    }


}
