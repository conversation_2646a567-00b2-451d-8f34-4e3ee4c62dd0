package com.imile.hrms.service.newAttendance.punchConfig.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/16
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PunchConfigUserQuery extends ResourceQuery {

    /**
     * 打卡规则id
     */
    private Long punchConfigId;

    /**
     * 员工常驻地国家
     */
    private String locationCountry;

    /**
     * 员工姓名/工号
     */
    private String userCodeOrName;

    /**
     * 员工部门
     */
    private Long deptId;

    /**
     * 员工部门集合
     */
    private List<Long> deptIds;
}
