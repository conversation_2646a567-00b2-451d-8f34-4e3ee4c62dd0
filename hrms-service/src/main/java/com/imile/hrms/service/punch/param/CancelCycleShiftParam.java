package com.imile.hrms.service.punch.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-13
 * @version: 1.0
 */
@Data
public class CancelCycleShiftParam {

    /**
     * 批量排班用户
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<Long> userIdList;
}
