package com.imile.hrms.service.punch;


import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.punch.dto.HrmsAttendanceClassEmployeeConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendanceClassEmployeeExportConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsPunchClassTypeImportDTO;
import com.imile.hrms.dao.punch.param.HrmsAttendanceClassEmployeeConfigParam;
import com.imile.hrms.dao.punch.param.HrmsPunchClassDeleteValidateParam;
import com.imile.hrms.dao.punch.query.AttendancePunchClassExportQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchClassQuery;
import com.imile.hrms.dao.salary.model.HrmsSalaryConfigDO;
import com.imile.hrms.service.punch.dto.AttendanceCycleDateDTO;
import com.imile.hrms.service.punch.dto.EmployeeSchedulingHandlerDTO;
import com.imile.hrms.service.punch.dto.HrmsAttendanceClassTitleExportDTO;
import com.imile.hrms.service.punch.param.BatchShiftParam;
import com.imile.hrms.service.punch.param.CancelCycleShiftParam;
import com.imile.hrms.service.punch.param.CycleShiftParam;
import com.imile.hrms.service.punch.param.ShiftCheckParam;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 员工班次配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
public interface HrmsAttendanceClassEmployeeConfigService {

    /**
     * 员工排版记录表
     *
     * @param query
     * @return
     */
    PaginationResult<HrmsAttendanceClassEmployeeConfigDTO> classEmployeeList(AttendancePunchClassQuery query);

    /**
     * 新增班次
     *
     * @param configAddParam
     */
    void add(HrmsAttendanceClassEmployeeConfigParam configAddParam);

    /**
     * 批量排班
     */
    void batchShift(BatchShiftParam param);

    /**
     * 循环排班
     */
    void cycleShift(CycleShiftParam param);

    /**
     * 取消循环排班
     */
    void cancelCycleShift(CancelCycleShiftParam param);

    /**
     * 员工是否属于同一个打卡规则
     */
    Boolean shiftCheck(ShiftCheckParam param);

    /**
     * 校验班次是否可以删除
     *
     * @param validateParam
     */
    void isDeleteClass(HrmsPunchClassDeleteValidateParam validateParam);

    /**
     * 通过日期获取考勤周期
     */
    AttendanceCycleDateDTO getAttendanceCycleDate(HrmsSalaryConfigDO hrmsSalaryConfigDO, Date date);


    List<HrmsPunchClassTypeImportDTO> importClassType(List<HrmsPunchClassTypeImportDTO> importList) throws ParseException;

    /**
     * 批量处理用户排班设置
     *
     * @param userIds   用户id
     * @param startDate 开始处理日期
     * @param endDate   结束处理日期
     */
    //void initEmployeeClassConfig(List<Long> userIds, Date startDate, Date endDate);


    /**
     * 根据入参处理用户排班信息
     *
     * @param handlerDTOList
     * @return
     */
    boolean employeeSchedulingHandler(List<EmployeeSchedulingHandlerDTO> handlerDTOList);

    /**
     * 排班导出
     *
     * @param query
     * @return
     */
    PaginationResult<HrmsAttendanceClassEmployeeExportConfigDTO> export(AttendancePunchClassExportQuery query);

    /**
     * 排班导出(NEW)
     *
     * @param query
     * @return
     */
    PaginationResult<Map<String, String>> listExport(AttendancePunchClassQuery query);

    /**
     * 排班信息字段title、name导出
     *
     * @param query
     * @return
     */
    List<HrmsAttendanceClassTitleExportDTO> titleExport(AttendancePunchClassExportQuery query);
}
