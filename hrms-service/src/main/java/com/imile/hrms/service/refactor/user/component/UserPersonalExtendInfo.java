package com.imile.hrms.service.refactor.user.component;

import com.imile.common.constant.ValidCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
@Data
public class UserPersonalExtendInfo {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Chinese {

        /**
         * 姓名全拼（入职流程时隐藏 入职后编辑必填）
         */
        private String userNamePinyin;

        /**
         * 民族
         */
        @NotBlank(message = ValidCodeConstant.NOT_BLANK)
        private String nation;

        /**
         * 户口类型
         */
        @NotBlank(message = ValidCodeConstant.NOT_BLANK)
        private String hukouType;

        /**
         * 户口所在地省份ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long residenceProvinceId;

        /**
         * 户口所在地城市ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long residenceCityId;

        /**
         * 户口所在地县区ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long residenceDistrictId;

        /**
         * 籍贯省份ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long nativePlaceProvinceId;

        /**
         * 籍贯城市ID
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Long nativePlaceCityId;

        /**
         * 政治面貌
         */
        @NotBlank(message = ValidCodeConstant.NOT_BLANK)
        private String politicsStatus;

        /**
         * 工龄
         */
        @NotNull(message = ValidCodeConstant.NOT_NULL)
        private Integer workSeniority;
    }
}
