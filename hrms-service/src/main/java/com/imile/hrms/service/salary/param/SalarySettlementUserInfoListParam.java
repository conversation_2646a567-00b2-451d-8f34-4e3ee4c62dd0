package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/11 16:57
 * @version: 1.0
 */
@Data
public class SalarySettlementUserInfoListParam extends ResourceQuery {

    /**
     * 计薪国
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String paymentCountry;

    /**
     * 计薪月份(202308)
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String paymentMonth;

    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;

    /**
     * 计薪方案编码
     */
    private String schemeNoString;

    /**
     * 用户工作状态(在职，离职)
     */
    private String userWorkStatus;

    /**
     * 是否本月新增  1是
     */
    private Integer isAddUser;
}
