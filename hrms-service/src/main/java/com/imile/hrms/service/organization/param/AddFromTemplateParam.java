package com.imile.hrms.service.organization.param;


import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 新增组织-从模版复制
 *
 * <AUTHOR>
 */
@Data
public class AddFromTemplateParam {

    /**
     * 模版组织id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long orgTemplateDepId;

    /**
     * 上级组织id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long  parentId;

}
