package com.imile.hrms.service.bpm.impl;

import com.alibaba.fastjson.JSON;
import com.github.easylog.annotation.EasyLog;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.api.wechat.api.WechatSendTextApi;
import com.imile.hrms.api.wechat.query.WeChatTextParam;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.enums.WechatTextMessageEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.punch.BizTypeEnum;
import com.imile.hrms.common.enums.punch.PlatFormTypeEnum;
import com.imile.hrms.common.enums.user.transform.TransformSceneEnum;
import com.imile.hrms.common.enums.user.transform.TransformStatusEnum;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.sys.model.HrmsPlatformRelationDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.UserTransformDO;
import com.imile.hrms.manage.base.HrmsPlatformRelationManage;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.organization.ProjectManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.manage.user.UserTransformManage;
import com.imile.hrms.mq.helper.OperatorHelper;
import com.imile.hrms.service.bpm.BpmTransformApprovalMqService;
import com.imile.hrms.service.log.component.OperationFieldDiffer;
import com.imile.hrms.service.log.helper.OperationRecordHelper;
import com.imile.hrms.service.refactor.user.param.UserDifferParam;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.dto.transform.UserTransformAfterDTO;
import com.imile.hrms.service.user.helper.UserHelper;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.imile.hrms.common.enums.WechatTextMessageEnum.LOCATION_CHANGE_TRANSFORM_NOTICE;
import static com.imile.hrms.common.enums.WechatTextMessageEnum.USER_TRANSFORM_BEFORE_SUPERINTENDENT_NOTICE;
import static com.imile.hrms.common.enums.WechatTextMessageEnum.USER_TRANSFORM_NOTICE;

/**
 * 调动审批回调处理
 *
 * <AUTHOR>
 * @since 2024/9/14
 */
@Service
@Slf4j
public class BpmTransformApprovalMqServiceImpl implements BpmTransformApprovalMqService {

    @Resource
    private UserTransformManage userTransformManage;
    @Resource
    private HrmsPlatformRelationManage platformRelationManage;
    @Resource
    private UserManage userManage;
    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private ProjectManage projectManage;
    @Resource
    private WechatSendTextApi wechatSendTextApi;
    @Resource
    private LogRecord logRecord;
    @Resource
    private UserService userService;


    @Override
    @EasyLog
    public void hrTransformMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO) {
        if (Objects.isNull(approvalPushStatusMsgDTO)) {
            log.info("hrTransformMqHandler approvalPushStatusMsgDTO is null");
            return;
        }
        log.info("hrTransformMqHandler approvalPushStatusMsgDTO {}", JSON.toJSONString(approvalPushStatusMsgDTO));
        if (StringUtils.equalsIgnoreCase(approvalPushStatusMsgDTO.getApprovalPushStatusType(), "NODE")) {
            //只监听整个流程完成
            return;
        }
        String transformId = approvalPushStatusMsgDTO.getBizId();
        if (StringUtils.isBlank(transformId)) {
            return;
        }
        UserTransformDO userTransform = userTransformManage.findNullableById(Long.parseLong(transformId));
        if (Objects.isNull(userTransform)) {
            log.info("hrTransformMqHandler userTransform not exist transformId {}", transformId);
            return;
        }
        if (IsDeleteEnum.YES.getCode().equals(userTransform.getIsDelete())) {
            // 重新提交的时候会删除旧调动记录
            log.info("hrTransformMqHandler userTransform isDelete transformId {}", transformId);
            return;
        }

        UserTransformDO updateTransform = new UserTransformDO();
        updateTransform.setId(Long.parseLong(transformId));
        //审批状态 1审批中 2通过 -1审批终止 -2驳回 -3撤回
        Integer status = approvalPushStatusMsgDTO.getStatus();
        HrmsUserInfoDO updateUserInfo = null;
        HrmsUserInfoDO dbUserInfo = null;
        switch (status) {
            case -1:
                // 审批终止
            case -3: {
                // 审批撤回
                log.info("update transform status is Transfer Cancelled approval status is {}", status);
                updateTransform.setTransformStatus(TransformStatusEnum.TRANSFORM_CANCEL.getStatus());
                break;
            }
            case -2: {
                // 审批驳回
                updateTransform.setTransformStatus(TransformStatusEnum.TRANSFORM_REJECT.getStatus());
                break;
            }
            case 2: {
                // 审批通过
                updateTransform.setTransformStatus(TransformStatusEnum.TRANSFORM_COMPLETE.getStatus());
                updateTransform.setTransformTime(new Date());
                dbUserInfo = userManage.getUserById(userTransform.getUserId());
                updateUserInfo = this.buildUpdateUserInfo(userTransform, dbUserInfo.getLocationCountry());
                // 给被调动人发企微通知
                this.sendWechatText(dbUserInfo, JSON.parseObject(userTransform.getTransformScenes(), Map.class));
                this.sendLeaderAndDeptLeader(dbUserInfo);
            }
        }
        log.info("transform end mq handle result updateTransform: {}, userInfo: {}",
                JSON.toJSONString(updateTransform), JSON.toJSONString(updateUserInfo));

        HrmsUserInfoDO operator = getUserInfoDO(approvalPushStatusMsgDTO);
        log.info("last update user: {}", JSON.toJSONString(operator));
        updateTransform.setLastUpdDate(new Date());
        updateTransform.setLastUpdUserCode(operator.getUserCode());
        updateTransform.setLastUpdUserName(operator.getUserName());
        // 设置操作人
        OperatorHelper.setOperatorInfo(BeanUtils.convert(operator, UserInfoDTO.class));
        userManage.doUserTransformSave(updateUserInfo, updateTransform);
        if (Objects.nonNull(updateUserInfo)) {
            logRecord.diffObj(updateUserInfo, dbUserInfo, OperationTypeEnum.USER_TRANSFORM_UPDATE.getCode());
            List<OperationFieldDiffer> fieldDiffList = UserHelper.saveLog(UserDifferParam.builder()
                    .id(dbUserInfo.getId())
                    .operationCode(OperationCodeEnum.HRMS_TRANSFER_FINISH)
                    .beforeUser(dbUserInfo)
                    .afterUser(updateUserInfo)
                    .build());
            // 人员基础信息变更通知
            userService.doUserBaseInfoChangeNotice(dbUserInfo, fieldDiffList);
            // 调动审批通过MQ
            List<OperationFieldDiffer> fieldDifferList = OperationRecordHelper.getFieldDifferList(dbUserInfo, updateUserInfo);
            userService.doTransformNotice(userTransform.getId(), dbUserInfo.getUserCode(), fieldDifferList);
        }
    }

    /**
     * 发送企微通知调动前的部门负责人、汇报上级进行权限审视（权限清空和授予规则待接入权限系统后再考虑）
     * 打这么多日志是为了好排查问题
     */
    public void sendLeaderAndDeptLeader(HrmsUserInfoDO userInfo) {
        List<String> userCodeList = new ArrayList<>(2);
        // 汇报上级
        Long leaderId = userInfo.getLeaderId();
        if (Objects.nonNull(leaderId)) {
            HrmsUserInfoDO leader = userManage.getUserById(leaderId);
            userCodeList.add(leader.getUserCode());
        } else {
            log.info("transform pass send leader fail user relation leaderId is null, userId: {}", userInfo.getId());
        }
        // 部门负责人
        Long deptId = userInfo.getDeptId();
        HrmsEntDeptDO dept = deptManage.getDeptNullableById(deptId);
        if (Objects.nonNull(dept)) {
            Long deptLeaderId = dept.getLeaderCode();
            if (Objects.nonNull(deptLeaderId)) {
                HrmsUserInfoDO deptLeader = userManage.getUserById(deptLeaderId);
                userCodeList.add(deptLeader.getUserCode());
            } else {
                log.info("transform pass send deptLeader fail dept relation leaderId is null, deptId: {}", dept.getId());
            }
        } else {
            log.info("transform pass send deptLeader fail user relation deptId is null, userId: {}", userInfo.getId());
        }
        WeChatTextParam chatTextParam = WeChatTextParam.builder()
                .userCodes(userCodeList)
                .content(String.format(USER_TRANSFORM_BEFORE_SUPERINTENDENT_NOTICE.getDesc(), userInfo.getUserName()))
                .contentEn(String.format(USER_TRANSFORM_BEFORE_SUPERINTENDENT_NOTICE.getDescEn(), userInfo.getUserName()))
                .build();
        try {
            wechatSendTextApi.sendText(chatTextParam);
        } catch (Exception e) {
            log.info("transform pass send leader and deptLeader wechat text error cause: ", e);
        }
    }

    /**
     * 更新为【否】的逻辑：
     * - 在场景2、场景5下，发生【常驻地-国家】变更，且审批通过，系统赋值为【否】
     * 更新为【是】的逻辑：
     * - 在场景3、场景4下，该字段在审批通过后，系统赋值为【是】
     */
    private HrmsUserInfoDO buildUpdateUserInfo(UserTransformDO userTransform, String dbUserLocationCountry) {
        UserTransformAfterDTO transformContentAfter =
                JSON.parseObject(userTransform.getTransformContent(), UserTransformAfterDTO.class);
        HrmsUserInfoDO userInfo = new HrmsUserInfoDO();
        userInfo.setId(userTransform.getUserId());
        String transformScenes = userTransform.getTransformScenes();
        // 为了兼容
        @SuppressWarnings("unchecked")
        Map<String, Integer> transferSceneMap = JSON.parseObject(transformScenes, Map.class);
        boolean isTransfer = Objects.equals(transferSceneMap.get(TransformSceneEnum.TRANSFER.getKey()), 1);
        boolean isDispatch = Objects.equals(transferSceneMap.get(TransformSceneEnum.DISPATCH.getKey()), 1);
        boolean isBackFlow = Objects.equals(transferSceneMap.get(TransformSceneEnum.BACK_FLOW.getKey()), 1);
        boolean isLocationSwitch = Objects.equals(transferSceneMap.get(TransformSceneEnum.LOCATION_SWITCH.getKey()), 1);

        // 不用策略，感觉处理逻辑还能接受
        // 场景1
        if (isTransfer && !isDispatch && !isBackFlow && !isLocationSwitch) {
            HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
            HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
            this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
        }
        // 场景2
        if (isTransfer && !isDispatch && !isBackFlow && isLocationSwitch) {
            HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
            HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
            this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
            this.locationCountryChangeFillUserInfo(userInfo, transformContentAfter);
        }
        // 场景3
        if (isTransfer && isDispatch && isLocationSwitch) {
            HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
            HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
            this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
            userInfo.setLocationCountry(transformContentAfter.getLocationCountry());
            userInfo.setLocationProvince(transformContentAfter.getLocationProvince());
            userInfo.setLocationCity(transformContentAfter.getLocationCity());
            // 是否派遣  在场景3、场景4下，该字段在审批通过后，系统赋值为【是】
            userInfo.setIsGlobalRelocation(1);
        }
        // 场景4
        if (!isTransfer && isDispatch && isLocationSwitch) {
            userInfo.setLocationCountry(transformContentAfter.getLocationCountry());
            userInfo.setLocationProvince(transformContentAfter.getLocationProvince());
            userInfo.setLocationCity(transformContentAfter.getLocationCity());
            // 是否派遣 在场景3、场景4下，该字段在审批通过后，系统赋值为【是】
            userInfo.setIsGlobalRelocation(1);
        }
        // 场景5
        if (!isTransfer && !isDispatch && !isBackFlow && isLocationSwitch) {
            this.locationCountryChangeFillUserInfo(userInfo, transformContentAfter);
        }
        // 场景6
        if (!isTransfer && !isDispatch && isBackFlow && isLocationSwitch) {
            this.locationCountryChangeFillUserInfo(userInfo, transformContentAfter);
            // 回流
            userInfo.setIsGlobalRelocation(0);
        }
        // 场景7
        if (isTransfer && !isDispatch && isBackFlow && isLocationSwitch) {
            HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
            HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
            this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
            this.locationCountryChangeFillUserInfo(userInfo, transformContentAfter);
            // 回流
            userInfo.setIsGlobalRelocation(0);
        }
//        Integer scene =
//                UserTransformServiceImpl.getTransferScene(JSON.parseArray(transformScenes, Integer.class));
//        switch (scene) {
//            // 是否切换常驻地 (场景5)
//            case 1: {
//                this.locationCountryChangeFillUserInfo(userInfo, transformContentAfter, dbUserLocationCountry);
//                break;
//            }
//            // 是否派遣、是否切换常驻地(场景4)
//            case 3: {
//                userInfo.setLocationCountry(transformContentAfter.getLocationCountry());
//                userInfo.setLocationProvince(transformContentAfter.getLocationProvince());
//                userInfo.setLocationCity(transformContentAfter.getLocationCity());
//                // 是否派遣 在场景3、场景4下，该字段在审批通过后，系统赋值为【是】
//                userInfo.setIsGlobalRelocation(1);
//                break;
//            }
//            // 是否调动(场景1)
//            case 4: {
//                HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
//                HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
//                this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
//                break;
//            }
//            // 是否调动、是否切换常驻地(场景2)
//            case 5: {
//                HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
//                HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
//                this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
//                this.locationCountryChangeFillUserInfo(userInfo, transformContentAfter, dbUserLocationCountry);
//                break;
//            }
//            // 是否调动、是否派遣、是否切换常驻地(场景3)
//            case 7: {
//                HrmsUserInfoDO leader = userManage.getUserById(transformContentAfter.getLeaderId());
//                HrmsEntDeptDO oc = this.getOcDept(transformContentAfter.getOcId(), transformContentAfter.getOcCode());
//                this.buildUserTransformField(userInfo, transformContentAfter, oc, leader.getUserName());
//                userInfo.setLocationCountry(transformContentAfter.getLocationCountry());
//                userInfo.setLocationProvince(transformContentAfter.getLocationProvince());
//                userInfo.setLocationCity(transformContentAfter.getLocationCity());
//                // 是否派遣  在场景3、场景4下，该字段在审批通过后，系统赋值为【是】
//                userInfo.setIsGlobalRelocation(1);
//                break;
//            }
//            default:
//                log.info("不支持调动业务场景, {}", transformScenes);
//        }
        return userInfo;
    }

    /**
     * 常驻地切换
     */
    private void locationCountryChangeFillUserInfo(HrmsUserInfoDO userInfo,
                                                   UserTransformAfterDTO transformContentAfter) {
        String newLocationCountry = transformContentAfter.getLocationCountry();
        userInfo.setLocationCountry(newLocationCountry);
        userInfo.setLocationProvince(transformContentAfter.getLocationProvince());
        userInfo.setLocationCity(transformContentAfter.getLocationCity());
    }

    /**
     * 构建更新人员信息
     */
    private void buildUserTransformField(HrmsUserInfoDO userInfo, UserTransformAfterDTO transformContentAfter,
                                         HrmsEntDeptDO oc, String leaderName) {
        userInfo.setIsDriver(transformContentAfter.getIsDriver());
        userInfo.setIsWarehouseStaff(transformContentAfter.getIsWarehouseStaff());
        userInfo.setPostId(transformContentAfter.getPostId());
        userInfo.setGradeId(transformContentAfter.getGradeId());
        // 职级(加密好的)
        userInfo.setGradeNo(transformContentAfter.getJobLevel());
        userInfo.setIsDtl(transformContentAfter.getIsDtl());
        userInfo.setDeptId(transformContentAfter.getDeptId());
        userInfo.setOcId(oc.getId());
        userInfo.setOcCode(oc.getOcCode());
        userInfo.setOriginCountry(oc.getCountry());
        userInfo.setLeaderId(transformContentAfter.getLeaderId());
        userInfo.setLeaderName(leaderName);
        userInfo.setBizModelId(transformContentAfter.getBizModelId());
        userInfo.setProjectCode(transformContentAfter.getProjectCode());
        userInfo.setProjectId(StringUtils.isBlank(transformContentAfter.getProjectCode())
                ? transformContentAfter.getProjectId()
                : projectManage.convertProjectCodeToId(transformContentAfter.getProjectCode()));
    }

    /**
     * 发送企微通知
     */
    private void sendWechatText(HrmsUserInfoDO userInfo, Map<String, Integer> transformSceneMap) {
        // 判断当前人员是否存在企微的关联关系
        List<HrmsPlatformRelationDO> relationList =
                platformRelationManage.getPlatformRelationList(Lists.newArrayList(userInfo.getUserCode()),
                        BizTypeEnum.USER.name(), PlatFormTypeEnum.WECHAT_WORK.name());
        if (CollectionUtils.isEmpty(relationList)) {
            log.info("transform pass send wechat message fail because no wechat relation, userId: {}", userInfo.getId());
            return;
        }
        WechatTextMessageEnum wechatTextMessageEnum =
                transformSceneMap.get(TransformSceneEnum.TRANSFER.getKey()).equals(1)
                        ? USER_TRANSFORM_NOTICE
                        : LOCATION_CHANGE_TRANSFORM_NOTICE;
        WeChatTextParam chatTextParam = WeChatTextParam.builder()
                .userCodes(Lists.newArrayList(userInfo.getUserCode()))
                .content(String.format(wechatTextMessageEnum.getDesc(), userInfo.getUserName()))
                .contentEn(String.format(wechatTextMessageEnum.getDescEn(), userInfo.getUserName()))
                .build();
        try {
            wechatSendTextApi.sendText(chatTextParam);
        } catch (Exception e) {
            log.info("transform pass send wechat text error cause: {}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * copy from com.imile.hrms.service.bpm.impl.BpmJobApprovalMqServiceImpl#getUserInfoDO(com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO)
     */
    public HrmsUserInfoDO getUserInfoDO(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO) {
        String userCode = approvalPushStatusMsgDTO.getApplyUserCode();
        if (CollectionUtils.isNotEmpty(approvalPushStatusMsgDTO.getApprovalProcessInfoList())) {
            userCode = approvalPushStatusMsgDTO.getApprovalProcessInfoList().get(0).getApprovalUserCode();
        }
        return userManage.getUserByUserCode(userCode);
    }

    private HrmsEntDeptDO getOcDept(Long ocId, String ocCode) {
        if (Objects.nonNull(ocId)) {
            return deptManage.getDeptById(ocId);
        }
        return deptManage.getDeptByOcCode(ocCode);
    }
}
