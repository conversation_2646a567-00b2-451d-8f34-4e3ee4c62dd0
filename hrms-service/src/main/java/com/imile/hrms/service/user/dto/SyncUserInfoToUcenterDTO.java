package com.imile.hrms.service.user.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SyncUserInfoToUcenterDTO {

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 所属网点
     */
    private Long ocId;

    /**
     * 所属网点编码
     */
    private String ocCode;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 所属国家
     */
    private String country;

    /**
     * 企业微信id
     */
    private String wecomId;


}
