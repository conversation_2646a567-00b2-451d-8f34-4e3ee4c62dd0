package com.imile.hrms.service.bpm;

import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;

import java.text.ParseException;

public interface BpmApprovalMqService {

    /**
     * HR监听HR用户新增的返回结果处理
     */
    void hrUserAddMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);

    /**
     * HR监听HR用户离职的返回结果处理
     */
    void hrUserDimissionMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);

    /**
     * HR监听HR用户调动的返回结果处理
     */
    void hrUserTransferMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);


    /**
     * HR监听TMS用户新增的返回结果处理
     */
    void tmsUserAddMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO) throws ParseException;

    /**
     * HR监听TMS用户离职的返回结果处理
     */
    void tmsUserDimissionMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);

    /**
     * HR监听TMS用户调动的返回结果处理
     */
    void tmsUserTransferMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO);
}
