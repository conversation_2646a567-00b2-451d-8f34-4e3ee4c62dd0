package com.imile.hrms.service.recruitment.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/11/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OfferDetailParam {

    /**
     * 岗位申请单Id
     */
    private Long offerId;

    /**
     * 是否查看草稿
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Boolean isDraft;

}
