package com.imile.hrms.service.driver.attendance.impl;

import com.imile.hrms.dao.driver.attendance.mapper.HrmsDriverPunchRecordDetailMapper;
import com.imile.hrms.service.driver.attendance.HrmsDriverPunchRecordDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * {@code @author:} allen
 * {@code @className:} HrmsDriverPunchRecordDetailServiceImpl
 * {@code @since:} 2024-01-25 13:54
 * {@code @description:} 
 */
@Service
public class HrmsDriverPunchRecordDetailServiceImpl implements HrmsDriverPunchRecordDetailService {

    @Autowired
    private HrmsDriverPunchRecordDetailMapper hrmsDriverPunchRecordDetailMapper;

}
