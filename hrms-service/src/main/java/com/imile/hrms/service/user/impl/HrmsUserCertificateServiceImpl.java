package com.imile.hrms.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.EmployeeNatureEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.EntryStatusEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OperationModuleEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.HrmsValidationUtil;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.sys.dao.HrmsCompanyCertificateConfigDao;
import com.imile.hrms.dao.sys.model.HrmsCompanyCertificateConfigDO;
import com.imile.hrms.dao.user.dao.HrmsCountryConfigDao;
import com.imile.hrms.dao.user.dao.HrmsLaborContractInfoDao;
import com.imile.hrms.dao.user.dao.HrmsUserCertificateDao;
import com.imile.hrms.dao.user.dao.HrmsUserEntryRecordDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.RegisterCertificateInfoDTO;
import com.imile.hrms.dao.user.dto.UserCertificateInfoParamDTO;
import com.imile.hrms.dao.user.dto.UserInfoExpandParamDTO;
import com.imile.hrms.dao.user.mapper.HrmsUserCertificateMapper;
import com.imile.hrms.dao.user.model.HrmsCountryConfigDO;
import com.imile.hrms.dao.user.model.HrmsLaborContractInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserCertificateDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.CertificateConfigQuery;
import com.imile.hrms.dao.user.query.CountryQuery;
import com.imile.hrms.manage.user.HrmsUserCertificateManage;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.sys.HrmsCompanyCertificateConfigService;
import com.imile.hrms.service.sys.dto.CertificateTypeDTO;
import com.imile.hrms.service.sys.dto.HrmsCompanyCertificateConfigDTO;
import com.imile.hrms.service.tx.HrmsTxManageFactory;
import com.imile.hrms.service.user.HrmsUserCertificateService;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.dto.UserCertificateCheckDTO;
import com.imile.hrms.service.user.dto.UserCertificateCheckResultDTO;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Service
public class HrmsUserCertificateServiceImpl extends BaseService implements HrmsUserCertificateService {

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsUserCertificateDao hrmsUserCertificateDao;
    @Autowired
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Autowired
    private HrmsCompanyCertificateConfigService hrmsCompanyCertificateConfigService;
    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;
    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Autowired
    private HrmsCountryConfigDao hrmsCountryConfigDao;
    @Autowired
    private HrmsCompanyCertificateConfigDao hrmsCompanyCertificateConfigDao;
    @Autowired
    private HrmsUserCertificateManage hrmsUserCertificateManage;
    @Autowired
    private HrmsUserCertificateMapper hrmsUserCertificateMapper;

    @Autowired
    private HrmsUserEntryRecordManage hrmsUserEntryRecordManage;

    @Autowired
    private HrmsLaborContractInfoDao hrmsLaborContractInfoDao;

    @Resource
    private HrmsUserInfoService hrmsUserInfoService;

    @Override
    public void add(UserInfoExpandParamDTO userInfoExpandParamDTO) {
        List<UserCertificateInfoParamDTO> userCertificateInfos = userInfoExpandParamDTO.getUserCertificateInfos();
        Long userId = userInfoExpandParamDTO.getUserId();

        //校验入参
        //校验员工信息是不是存在
        HrmsUserInfoDO userInfoDO = hrmsUserInfoDao.getById(userId);
        if (userInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }

        this.checkCertificateParam(userCertificateInfos, userInfoDO.getOriginCountry(), userInfoDO.getIsDriver(), userInfoDO.getEmployeeType());

        List<HrmsUserCertificateDO> userCertificateDOS = hrmsUserCertificateManage.getCertificate(userCertificateInfos, userInfoDO);
        hrmsUserCertificateMapper.replaceIntoBatchSomeColumn(userCertificateDOS);

        //校验是不是需要由更改状态为待确认入职
        HrmsUserEntryRecordDO recordDO = hrmsUserEntryRecordDao.getByUserId(userId);

        if (recordDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ENTRY_DATE_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ENTRY_DATE_NOT_EXITS.getDesc()));
        }

        if (ObjectUtil.equal(userInfoDO.getIsDriver(), BusinessConstant.N)
                && (StringUtils.equalsIgnoreCase(userInfoDO.getEmployeeType(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode())
                || StringUtils.equalsIgnoreCase(userInfoDO.getEmployeeType(), EmploymentTypeEnum.EMPLOYEE.getCode()))) {

            //校验用户合同信息是不是存在
            List<HrmsLaborContractInfoDO> laborContractInfoDOS = hrmsLaborContractInfoDao.getByUserId(userId);
            List<Integer> probationMonths = laborContractInfoDOS.stream().map(HrmsLaborContractInfoDO::getProbationMonths).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(probationMonths)) {
                //校验是不是需要由更改状态为待确认入职
                //状态需要改变
                if (userInfoDO.getIsFinish() == 1
                        && (recordDO.getEntryStatus().equals(EntryStatusEnum.PREPARE_REGISTER.getCode())
                        || recordDO.getEntryStatus().equals(EntryStatusEnum.PREPARE_INVITED.getCode()))) {
                    recordDO.setEntryStatus(EntryStatusEnum.PREPARE_ENTRY_CONFIRM.getCode());
                    fillDOUpdate(recordDO);

                }
            }
        } else {
            //校验是不是需要由更改状态为待确认入职
            //状态需要改变
            if (userInfoDO.getIsFinish() == 1
                    && (recordDO.getEntryStatus().equals(EntryStatusEnum.PREPARE_REGISTER.getCode())
                    || recordDO.getEntryStatus().equals(EntryStatusEnum.PREPARE_INVITED.getCode()))) {
                recordDO.setEntryStatus(EntryStatusEnum.PREPARE_ENTRY_CONFIRM.getCode());
                fillDOUpdate(recordDO);

            }
        }


        hrmsUserEntryRecordDao.updateById(recordDO);
        //HrmsTxManageFactory.getTxManage(OperationModuleEnum.USER_EDIT).handle(String.valueOf(userInfoDO.getId()));

        //更新主表操作信息
        BaseDOUtil.fillDOUpdate(userInfoDO);
        hrmsUserInfoDao.updateById(userInfoDO);

        HrmsTxManageFactory.getTxManage(OperationModuleEnum.USER_EDIT).handle(String.valueOf(userInfoDO.getId()));
        //若员工在职，则需要同步证件信息事务
        /*if (WorkStatusEnum.ON_JOB.getCode().equals(userInfoDO.getWorkStatus())) {
            HrmsTxManageFactory.getTxManage(OperationModuleEnum.USER_EDIT).handle(String.valueOf(userInfoDO.getId()));
        }*/
        //日志记录
        //logRecord.diffObj(model, hrmsUserCertificateInfoDO, OperationTypeEnum.EMPLOYEE_CERTIFICATE_INFO_UPDATE.getCode());
    }


    @Deprecated
    @Override
    public void checkCertificateParam(List<UserCertificateInfoParamDTO> userCertificateInfos, String country, Integer isDriver, String employeeType) {
        CertificateConfigQuery configQuery = new CertificateConfigQuery();
        configQuery.setCountry(country);
        // 用工类型标准化 hrms_company_certificate_config的employee_type并不是标准的用工类型，而是聚合后的类型等同于枚举类中的nature
        configQuery.setEmployeeType(this.convertEmployeeType2Nature(employeeType));
        configQuery.setDriver(isDriver);
        HrmsCompanyCertificateConfigDTO hrmsCompanyCertificateConfigDTO = hrmsCompanyCertificateConfigService.listCompanyCertificateConfig(configQuery);
        Map<String, UserCertificateInfoParamDTO> certificateInfoParamDTOMap
                = userCertificateInfos.stream()
                .collect(Collectors.toMap(UserCertificateInfoParamDTO::getCertificateTypeCode, item -> item, (oldValue, newValue) -> oldValue));
        if(certificateInfoParamDTOMap.size() < userCertificateInfos.size()) {
            throw BusinessException.get(HrmsErrorCodeEnums.EMPLOYEE_CERTIFICATE_TYPE_REPEAT.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.EMPLOYEE_CERTIFICATE_TYPE_REPEAT.getDesc()));
        }
        List<CertificateTypeDTO> mustHandlerCertificates = hrmsCompanyCertificateConfigDTO.getMustHandlerCertificates();
        List<CertificateTypeDTO> needHandlerCertificates = hrmsCompanyCertificateConfigDTO.getNeedHandlerCertificates();
        //基本非空校验
        HrmsValidationUtil.validateList(userCertificateInfos);
        //must_handler 必须全部填写完毕
        mustHandlerCertificates.forEach(item -> {
            UserCertificateInfoParamDTO certificateInfoParamDTO = certificateInfoParamDTOMap.get(item.getCertificateTypeCode());
            BusinessLogicException.checkTrue(certificateInfoParamDTO == null, HrmsErrorCodeEnums.MUST_HANDLER_CERTIFICATE_ERROR.getCode(), HrmsErrorCodeEnums.MUST_HANDLER_CERTIFICATE_ERROR.getDesc(), item.getCertificateTypeCode());
            BusinessLogicException.checkTrue(StringUtils.isEmpty(certificateInfoParamDTO.getCertificateCode()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), item.getCertificateTypeCode());
            // todo 暂时不做必填证件的证件照校验
        });
        //need_handler 只需要有一个填写完毕
        // todo 选填证件是否还需要校验证件照
        boolean isError = needHandlerCertificates.stream().allMatch(o -> certificateInfoParamDTOMap.get(o.getCertificateTypeCode()) == null);
        // || certificateInfoParamDTOMap.get(o.getCertificateTypeCode()).getCertificateFrontPath() == null
        // || certificateInfoParamDTOMap.get(o.getCertificateTypeCode()).getCertificateBackPath() == null);

        BusinessLogicException.checkTrue(CollUtil.isEmpty(needHandlerCertificates) && isError, HrmsErrorCodeEnums.NEED_HANDLER_CERTIFICATE_ERROR.getCode(), HrmsErrorCodeEnums.NEED_HANDLER_CERTIFICATE_ERROR.getDesc());

        CertificateConfigQuery query = new CertificateConfigQuery();
        query.setCountry(country);
        List<HrmsCompanyCertificateConfigDO> configDOList = hrmsCompanyCertificateConfigDao.listConfigByCompanyId(query);
        if (CollUtil.isEmpty(configDOList)) {
            query.setCountry(BusinessConstant.DEFAULT_COUNTRY);
            configDOList = hrmsCompanyCertificateConfigDao.listConfigByCompanyId(query);
        }
        Map<String, String> certificateNameMap = new HashMap<>();
        if (RequestInfoHolder.isChinese()) {
            certificateNameMap = configDOList.stream().collect(Collectors.toMap(HrmsCompanyCertificateConfigDO::getCertificateTypeCode, HrmsCompanyCertificateConfigDO::getCertificateTypeNameCn, (v1, v2) -> v1));
        } else {
            certificateNameMap = configDOList.stream().collect(Collectors.toMap(HrmsCompanyCertificateConfigDO::getCertificateTypeCode, HrmsCompanyCertificateConfigDO::getCertificateTypeNameEn, (v1, v2) -> v1));
        }

        List<String> errCertificateNameList = new ArrayList<>();
        for (UserCertificateInfoParamDTO paramDTO : userCertificateInfos) {
            LambdaQueryWrapper<HrmsUserCertificateDO> queryWrapper = Wrappers.lambdaQuery();
            if (StringUtils.isBlank(paramDTO.getCertificateCode())) {
                continue;
            }
            queryWrapper.eq(HrmsUserCertificateDO::getCertificateTypeCode, paramDTO.getCertificateTypeCode());
            queryWrapper.eq(HrmsUserCertificateDO::getCertificateCode, paramDTO.getCertificateCode());
            queryWrapper.eq(HrmsUserCertificateDO::getIsDelete, IsDeleteEnum.NO.getCode());
            List<HrmsUserCertificateDO> certificateDOList = hrmsUserCertificateDao.list(queryWrapper);
            if (CollectionUtils.isEmpty(certificateDOList)
                    || (certificateDOList.size() == 1 && certificateDOList.get(0).getUserId().equals(paramDTO.getUserId()))) {
                continue;
            }
            //errCertificateNameList.add(certificateNameMap.get(paramDTO.getCertificateTypeCode()));
            BusinessLogicException.checkTrue(certificateNameMap.get(paramDTO.getCertificateTypeCode()) != null, HrmsErrorCodeEnums.DUPLICATE_CERTIFICATE_ERROR.getCode(), HrmsErrorCodeEnums.DUPLICATE_CERTIFICATE_ERROR.getDesc(), paramDTO.getCertificateCode());
        }

    }


    @Deprecated
    @Override
    public void checkEntryCertificateParam(List<RegisterCertificateInfoDTO> userCertificateInfos, HrmsUserInfoDO userInfoDO) {
        CertificateConfigQuery configQuery = BeanUtil.copyProperties(userInfoDO, CertificateConfigQuery.class);
        configQuery.setDriver(userInfoDO.getIsDriver());
        // 用工类型标准化 hrms_company_certificate_config的employee_type并不是标准的用工类型，而是聚合后的类型等同于枚举类中的nature
        configQuery.setEmployeeType(this.convertEmployeeType2Nature(userInfoDO.getEmployeeType()));
        HrmsCompanyCertificateConfigDTO hrmsCompanyCertificateConfigDTO = hrmsCompanyCertificateConfigService.listCompanyCertificateConfig(configQuery);
        Map<String, RegisterCertificateInfoDTO> certificateInfoParamDTOMap
                = userCertificateInfos.stream()
                .collect(Collectors.toMap(RegisterCertificateInfoDTO::getCertificateTypeCode, item -> item, (oldValue, newValue) -> oldValue));
        List<CertificateTypeDTO> needHandlerCertificates = hrmsCompanyCertificateConfigDTO.getNeedHandlerCertificates();

        boolean isError = needHandlerCertificates.stream().allMatch(o -> certificateInfoParamDTOMap.get(o.getCertificateTypeCode()) == null);
        BusinessLogicException.checkTrue(CollUtil.isEmpty(needHandlerCertificates) && isError, HrmsErrorCodeEnums.NEED_HANDLER_CERTIFICATE_ERROR.getCode(), HrmsErrorCodeEnums.NEED_HANDLER_CERTIFICATE_ERROR.getDesc());

    }


    @Override
    public UserCertificateCheckResultDTO checkUserCertificate(UserCertificateCheckDTO checkDTO) {
        LambdaQueryWrapper<HrmsUserCertificateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsUserCertificateDO::getCertificateTypeCode, checkDTO.getCertificateType());
        queryWrapper.eq(HrmsUserCertificateDO::getCertificateCode, checkDTO.getCertificateCode());
        queryWrapper.eq(HrmsUserCertificateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<HrmsUserCertificateDO> certificateDOList = hrmsUserCertificateDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(certificateDOList)) {
            return null;
        }
        HrmsUserCertificateDO certificateDO = certificateDOList.get(0);
        HrmsUserInfoDO infoDO = hrmsUserInfoDao.getById(certificateDO.getUserId());

        if (infoDO == null) {
            return null;
        }

        HrmsEntDeptDO hrmsEntDeptDO = hrmsEntDeptDao.getById(infoDO.getDeptId());
        HrmsEntPostDO hrmsEntPostDO = hrmsEntPostDao.getById(infoDO.getPostId());

        UserCertificateCheckResultDTO resultDTO = buildUserCertificateResultDto(checkDTO, infoDO, hrmsEntDeptDO, hrmsEntPostDO);
        return resultDTO;
    }


    private UserCertificateCheckResultDTO buildUserCertificateResultDto(UserCertificateCheckDTO checkDTO, HrmsUserInfoDO infoDO, HrmsEntDeptDO deptDO, HrmsEntPostDO postDO) {
        UserCertificateCheckResultDTO resultDTO = BeanUtils.convert(infoDO, UserCertificateCheckResultDTO.class);
        if (deptDO != null) {
            resultDTO.setDeptName(RequestInfoHolder.isChinese() ? deptDO.getDeptNameCn() : deptDO.getDeptNameEn());
        }
        if (postDO != null) {
            resultDTO.setPostName(RequestInfoHolder.isChinese() ? postDO.getPostNameCn() : postDO.getPostNameEn());
        }
        CountryQuery countryQuery = CountryQuery.builder().countryCode(infoDO.getCountryCode()).build();
        List<HrmsCountryConfigDO> configDOS = hrmsCountryConfigDao.getCountryConfig(countryQuery);
        if (!org.springframework.util.CollectionUtils.isEmpty(configDOS)) {
            resultDTO.setCountryName(RequestInfoHolder.isChinese() ? configDOS.get(0).getCountryNameCn() : configDOS.get(0).getCountryNameEn());
        }


        HrmsUserEntryRecordDO recordDO = hrmsUserEntryRecordDao.getByUserId(RequestInfoHolder.getUserId());
        resultDTO.setEntryDate(recordDO.getEntryDate());

        //先判断当前用户是否有操作的权限，然后在进行二次入职的判断
        resultDTO.setCurrentDept(checkCurrentDeptByLoginUser(infoDO));


        String status = infoDO.getStatus();
        String workStatus = infoDO.getWorkStatus();

        if (StringUtils.equalsIgnoreCase(StatusEnum.ACTIVE.getCode(), status) && StringUtils.isBlank(workStatus)) {
            resultDTO.setStatus("PENDING");
        }
        if (StringUtils.equalsIgnoreCase(StatusEnum.ACTIVE.getCode(), status)
                && (StringUtils.equalsIgnoreCase(WorkStatusEnum.ON_JOB.getCode(), workStatus) || StringUtils.equalsIgnoreCase(WorkStatusEnum.TRANSFER.getCode(), workStatus))) {
            resultDTO.setStatus(WorkStatusEnum.ON_JOB.getCode());
        }
        if (StringUtils.equalsIgnoreCase(StatusEnum.DISABLED.getCode(), status)
                && (StringUtils.equalsIgnoreCase(WorkStatusEnum.ON_JOB.getCode(), workStatus) || StringUtils.equalsIgnoreCase(WorkStatusEnum.TRANSFER.getCode(), workStatus))) {
            resultDTO.setStatus(StatusEnum.DISABLED.getCode());
        }
        if (StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), workStatus)) {
            resultDTO.setStatus(WorkStatusEnum.DIMISSION.getCode());
        }

        if (StringUtils.isBlank(infoDO.getLeaderName()) && infoDO.getLeaderId() != null) {
            HrmsUserInfoDO userInfo = hrmsUserInfoDao.getById(infoDO.getLeaderId());
            resultDTO.setLeaderName(userInfo.getUserName());
        }

        //DIMISSION:离职后再二次入职判断
        if (StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), infoDO.getWorkStatus())
                && checkDTO.getEntryAgainType() == null) {
            resultDTO.setEntryAgainType(WorkStatusEnum.DIMISSION.getCode());
            return resultDTO;
        }

        //CANCEL_ENTRY：已放弃入职后再二次入职
        //入职记录表查询该条记录
        List<HrmsUserEntryRecordDO> recordList = hrmsUserEntryRecordManage.selectUserEntryByUserIds(Arrays.asList(infoDO.getId()));
        if (CollectionUtils.isNotEmpty(recordList)) {
            HrmsUserEntryRecordDO record = recordList.get(0);
            if (EntryStatusEnum.CANCEL_ENTRY.getCode().equals(record.getEntryStatus())
                    && checkDTO.getEntryAgainType() == null) {
                resultDTO.setEntryAgainType(EntryStatusEnum.CANCEL_ENTRY.getCode());
                return resultDTO;
            }
        }

        return resultDTO;
    }
    @Resource
    private UserResourceService userResourceService;


    // permission_reconfiguration
    private boolean checkCurrentDeptByLoginUser(HrmsUserInfoDO infoDO) {
        HrmsUserInfoDO userInfoDO = hrmsUserInfoDao.getById(RequestInfoHolder.getUserId());
        if (infoDO.getDeptId() == null || userInfoDO.getDeptId() == null) {
            return false;
        }

        PermissionDeptVO permissionDept = userResourceService.getPermissionDept();
        if (!permissionDept.getHasDeptPermission()) {
            return false;
        }
        if (permissionDept.getIsSysAdmin()||permissionDept.getDeptIdList().contains(infoDO.getDeptId())) {
            return true;
        }
        //如果登录人是当前员工的上级，可以变更
        if (infoDO.getLeaderId() == null || infoDO.getLeaderId().equals(userInfoDO.getId())) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkVendorHandleUserCertificate(UserCertificateCheckDTO checkDTO) {
        LambdaQueryWrapper<HrmsUserCertificateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsUserCertificateDO::getCertificateTypeCode, checkDTO.getCertificateType());
        queryWrapper.eq(HrmsUserCertificateDO::getCertificateCode, checkDTO.getCertificateCode());
        queryWrapper.eq(HrmsUserCertificateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<HrmsUserCertificateDO> certificateDOList = hrmsUserCertificateDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(certificateDOList)) {
            return true;
        }
        return false;
    }

    @Override
    public List<HrmsUserCertificateDO> listCertificateInfo(Long userId, String userCode) {
        if (Objects.isNull(userId)&&Objects.isNull(userCode)) {
            return new ArrayList<>();
        }
        return hrmsUserCertificateDao.listCertificateInfo(userId,userCode);
    }

    private String convertEmployeeType2Nature(String employeeType) {
        String employeeNature = EmploymentTypeEnum.getByCode(employeeType).getNature();
        return StringUtils.isBlank(employeeNature) ? EmployeeNatureEnum.EXTERNAL.getCode() : employeeNature;
    }
}
