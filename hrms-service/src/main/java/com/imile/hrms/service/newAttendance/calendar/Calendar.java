package com.imile.hrms.service.newAttendance.calendar;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/8 
 * @Description
 */
@Data
public class Calendar {

    /**
     * 日历配置ID
     */
    private Long id;

    private String country;

    private String attendanceConfigNo;
    /**
     * 日历配置方案名称
     */
    private String attendanceConfigName;
    /**
     * 日历出勤方案类型 缺省方案、自定义方案
     */
    private String type;

    private String status;

    private String deptIds;

    private String deptCodes;

    private Integer isLatest;

    private Integer isDelete;

    private List<CalendarDetail> calendarDetails;

    private List<CalendarRange> calendarRanges;
}
