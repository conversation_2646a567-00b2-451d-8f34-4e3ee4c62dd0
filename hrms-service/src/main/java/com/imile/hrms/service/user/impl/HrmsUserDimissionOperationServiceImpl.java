package com.imile.hrms.service.user.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.user.mapper.HrmsUserDimissionOperationRecordMapper;
import com.imile.hrms.dao.user.model.HrmsUserDimissionOperationRecordDO;
import com.imile.hrms.service.user.HrmsUserDimissionOperationRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户离职操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class HrmsUserDimissionOperationServiceImpl extends ServiceImpl<HrmsUserDimissionOperationRecordMapper, HrmsUserDimissionOperationRecordDO> implements HrmsUserDimissionOperationRecordService {

}
