package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/1/10
 */
@Data
public class SalaryUserSchemeBankInfoVO {

    /**
     * 银行账户名
     */
    private String userName;

    /**
     * 工资卡开户银行
     */
    private String wagesCardBank;

    /**
     * 工资卡开户网点
     */
    private String wagesCardBankBranch;

    /**
     * 工资卡号
     */
    private String wagesCardNo;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

}
