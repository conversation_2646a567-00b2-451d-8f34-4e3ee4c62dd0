package com.imile.hrms.service.punch.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-10
 * @version: 1.0
 */
@Data
public class BatchShiftParam {

    /**
     * 打卡规则ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long punchConfigId;

    /**
     * 循环排班用户
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<Long> userIdList;

    /**
     * 循环排班周期班次信息
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<BatchShiftDayParam> batchShiftDayParamList;

    /**
     * 来自页面排班
     */
    private Boolean fromPage = Boolean.FALSE;

}
