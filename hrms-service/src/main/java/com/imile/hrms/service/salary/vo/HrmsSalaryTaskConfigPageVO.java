package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 薪资时间窗口配置
 *
 * <AUTHOR>
 * @since 2024/3/4
 */
@Data
public class HrmsSalaryTaskConfigPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记薪国
     */
    private String country;

    /**
     * 期间类型
     */
    private String periodType;

    /**
     * 期间类型
     */
    private String periodTypeDesc;

    /**
     * 时间规则基准
     */
    private String timeReference;

    /**
     * 时间规则基准
     */
    private String timeReferenceDesc;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 最后修改人编码
     */
    private String lastUpdUserCode;

    /**
     * 最后修改人编码
     */
    private String lastUpdUserName;

}
