package com.imile.hrms.service.common;

import com.imile.hrms.mq.param.PostEventParam;
import com.imile.hrms.mq.param.UserEventParam;
import com.imile.ucenter.api.mq.OwnUserInfoMqDTO;

/**
 * <AUTHOR>
 * @date 2024/8/26
 */
public interface MessageSendService {

    /**
     * 人员同步ucenter通知
     *
     * @param param OwnUserInfoMqDTO
     */
    void doNotice2UserCenter(OwnUserInfoMqDTO param);

    /**
     * 人员入职通知
     *
     * @param param UserEventParam
     */
    void doNotice4UserEntry(UserEventParam<UserEventParam.Entry> param);

    /**
     * 人员基础信息变更通知
     *
     * @param param UserEventParam
     */
    void doNotice4UserBaseInfoChange(UserEventParam<UserEventParam.BaseInfoChange> param);

    /**
     * 人员支付信息变更通知
     *
     * @param param UserEventParam
     */
    void doNotice4UserPaymentInfoChange(UserEventParam<UserEventParam.Default> param);

    /**
     * 人员调动成功通知
     *
     * @param param UserEventParam
     */
    void doNotice4UserTransform(UserEventParam<UserEventParam.Transform> param);

    /**
     * 人员离职通知
     *
     * @param param UserEventParam
     */
    void doNotice4UserDimission(UserEventParam<UserEventParam.Dimission> param);

    /**
     * 岗位变更通知
     *
     * @param param PostEventParam.PostInfoChange
     */
    void doNotice4PostInfoChange(PostEventParam.PostInfoChange param);

    /**
     * 职位族类变更通知
     *
     * @param param PostEventParam.PostFamilyInfoChange
     */
    void doNotice4PostFamilyInfoChange(PostEventParam.PostFamilyInfoChange param);
}
