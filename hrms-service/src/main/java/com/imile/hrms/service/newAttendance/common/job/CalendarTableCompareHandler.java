package com.imile.hrms.service.newAttendance.common.job;

import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigDao;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigDetailDao;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigRangeDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.leave.HrmsCompanyLegalLeaveConfigDao;
import com.imile.hrms.dao.leave.model.HrmsCompanyLegalLeaveConfigDO;
import com.imile.hrms.dao.newAttendance.calendar.dao.CalendarConfigDao;
import com.imile.hrms.dao.newAttendance.calendar.dao.CalendarConfigDetailDao;
import com.imile.hrms.dao.newAttendance.calendar.dao.CalendarConfigRangeDao;
import com.imile.hrms.dao.newAttendance.calendar.dao.CalendarLegalLeaveConfigDao;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDetailDO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigRangeDO;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.*;
import com.imile.hrms.dao.newAttendance.punchConfig.model.*;
import com.imile.hrms.dao.punch.dao.*;
import com.imile.hrms.dao.punch.model.*;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.hrms.service.newAttendance.common.constant.AttendanceModule;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CalendarTableCompareHandler {

    private final HrmsAttendanceConfigDao hrmsAttendanceConfigDao;
    private final HrmsAttendanceConfigDetailDao hrmsAttendanceConfigDetailDao;
    private final HrmsAttendanceConfigRangeDao hrmsAttendanceConfigRangeDao;
    private final HrmsCompanyLegalLeaveConfigDao hrmsCompanyLegalLeaveConfigDao;

    private final CalendarConfigDao calendarConfigDao;
    private final CalendarConfigDetailDao calendarConfigDetailDao;
    private final CalendarConfigRangeDao calendarConfigRangeDao;
    private final CalendarLegalLeaveConfigDao calendarLegalLeaveConfigDao;

    private final HrmsAttendancePunchConfigDao attendancePunchConfigDao;
    private final HrmsAttendancePunchConfigRangeDao attendancePunchConfigRangeDao;
    private final HrmsAttendancePunchClassConfigDao attendancePunchClassConfigDao;
    private final HrmsAttendancePunchClassItemConfigDao attendancePunchClassItemConfigDao;
    private final HrmsAttendanceGpsConfigDao hrmsAttendanceGpsConfigDao;
    private final HrmsAttendanceWifiConfigDao hrmsAttendanceWifiConfigDao;

    private final PunchConfigDao punchConfigDao;
    private final PunchConfigRangeDao punchConfigRangeDao;
    private final PunchClassConfigDao punchClassConfigDao;
    private final PunchClassItemConfigDao punchClassItemConfigDao;
    private final PunchGpsConfigDao punchGpsConfigDao;
    private final PunchWifiConfigDao punchWifiConfigDao;

    @XxlJob("calendarTableCompareHandler")
    public ReturnT<String> compareHandler(String param) {
        if (StringUtils.isEmpty(param)) {
            XxlJobLogger.log("param is null");
            return ReturnT.SUCCESS;
        }
        AttendanceModule attendanceModule = AttendanceModule.getByName(param);
        switch (attendanceModule) {
            case ALL:
                XxlJobLogger.log("sync ALL start");
                compareCalendarTables();
                comparePunchConfigTables();
                XxlJobLogger.log("sync ALL end");
                break;
            case CALENDAR:
                XxlJobLogger.log("sync CALENDAR start");
                compareCalendarTables();
                XxlJobLogger.log("sync CALENDAR end");
                break;
            case PUNCH_CONFIG:
                XxlJobLogger.log("sync PUNCH_CONFIG start");
                comparePunchConfigTables();
                XxlJobLogger.log("sync PUNCH_CONFIG end");
                break;
            default:
                XxlJobLogger.log("param=" + param + " not support");
                break;
        }
        return ReturnT.SUCCESS;
    }

    public void compareCalendarTables() {
        // 比较4个表的数据
        compareCalendarConfig();
        compareCalendarConfigDetail();
        compareCalendarConfigRange();
        compareCalendarLegalLeaveConfig();
    }

    public void comparePunchConfigTables() {
        // 比较6个表的数据
        comparePunchConfig();
        comparePunchConfigRange();
        comparePunchClassConfig();
        comparePunchClassItemConfig();
        comparePunchGpsConfig();
        comparePunchWifiConfig();
    }

    //========================日历====================================

    public void compareCalendarConfig() {
        compareTable(
                "hrms_attendance_config",
                hrmsAttendanceConfigDao::listByPage,
                calendarConfigDao::listByPage,
                this::compareCalendarConfig
        );
    }

    public void compareCalendarConfigDetail() {
        compareTable(
                "hrms_attendance_config_detail",
                hrmsAttendanceConfigDetailDao::listByPage,
                calendarConfigDetailDao::listByPage,
                this::compareCalendarConfigDetail
        );
    }

    public void compareCalendarConfigRange() {
        compareTable(
                "hrms_attendance_config_range",
                hrmsAttendanceConfigRangeDao::listByPage,
                calendarConfigRangeDao::listByPage,
                this::compareCalendarConfigRange
        );
    }

    //========================考勤组====================================
    public void comparePunchConfig() {
        compareTable(
                "hrms_attendance_punch_config",
                attendancePunchConfigDao::listByPage,
                punchConfigDao::listByPage,
                this::comparePunchConfig
        );
    }

    public void comparePunchConfigRange() {
        compareTable(
                "hrms_attendance_punch_config_range",
                attendancePunchConfigRangeDao::listByPage,
                punchConfigRangeDao::listByPage,
                this::comparePunchConfigRange
        );
    }

    public void comparePunchClassConfig() {
        compareTable(
                "hrms_attendance_punch_class_config",
                attendancePunchClassConfigDao::listByPage,
                punchClassConfigDao::listByPage,
                this::comparePunchClassConfig
        );
    }

    public void comparePunchClassItemConfig() {
        compareTable(
                "hrms_attendance_punch_class_item_config",
                attendancePunchClassItemConfigDao::listByPage,
                punchClassItemConfigDao::listByPage,
                this::comparePunchClassItemConfig
        );
    }

    public void comparePunchGpsConfig() {
        compareTable(
                "hrms_attendance_gps_config",
                hrmsAttendanceGpsConfigDao::listByPage,
                punchGpsConfigDao::listByPage,
                this::compareGpsConfig
        );
    }

    public void comparePunchWifiConfig() {
        compareTable(
                "hrms_attendance_wifi_config",
                hrmsAttendanceWifiConfigDao::listByPage,
                punchWifiConfigDao::listByPage,
                this::compareWifiConfig
        );
    }

    public void compareCalendarLegalLeaveConfig() {
        compareTable(
                "hrms_company_legal_leave_config",
                hrmsCompanyLegalLeaveConfigDao::listByPage,
                calendarLegalLeaveConfigDao::listByPage,
                this::compareCalendarLegalLeaveConfig
        );
    }


    private <S, T> void compareTable(String tableName,
                                     BiFunction<Integer, Integer, List<S>> oldDataProvider,
                                     BiFunction<Integer, Integer, List<T>> newDataProvider,
                                     BiConsumer<S, T> compareFunction) {
        XxlJobLogger.log("=======开始比较{}==========", tableName);

        // 第一步：收集所有数据的ID并比较数据量
        CompareResult compareResult = compareDataCount(tableName, oldDataProvider, newDataProvider);

        // 如果数据量不一致，直接报告差异后返回
        if (!compareResult.isCountMatch()) {
            String summary = String.format("%s 表存在数据量差异，停止字段比对", tableName);
            XxlJobLogger.log(summary);
            sendAlertMessage(summary);
            return;
        }

        // 数据量一致才进行字段比对
        compareDataFields(tableName, oldDataProvider, newDataProvider, compareFunction);

        XxlJobLogger.log("=======比较{}结束==========", tableName);
    }

    private <S, T> CompareResult compareDataCount(String tableName,
                                                  BiFunction<Integer, Integer, List<S>> oldDataProvider,
                                                  BiFunction<Integer, Integer, List<T>> newDataProvider) {
        int pageSize = 1000;
        int currentPage = 1;

        Set<Object> oldIds = new HashSet<>();
        Set<Object> newIds = new HashSet<>();

        while (true) {
            List<S> oldData = oldDataProvider.apply(currentPage, pageSize);
            List<T> newData = newDataProvider.apply(currentPage, pageSize);

            if (CollectionUtils.isEmpty(oldData) && CollectionUtils.isEmpty(newData)) {
                break;
            }

            // 收集本页数据的ID
            oldData.forEach(item -> oldIds.add(extractId(item)));
            newData.forEach(item -> newIds.add(extractId(item)));

            if (oldData.size() < pageSize && newData.size() < pageSize) {
                break;
            }
            currentPage++;
        }

        // 比较差异并记录日志
        Set<Object> onlyInOld = new HashSet<>(oldIds);
        onlyInOld.removeAll(newIds);

        Set<Object> onlyInNew = new HashSet<>(newIds);
        onlyInNew.removeAll(oldIds);

        if (!onlyInOld.isEmpty()) {
            String errorMsg = String.format("%s 表中仅在旧表存在的记录(共%d条): %s",
                    tableName, onlyInOld.size(), String.join(", ", convertToStringList(onlyInOld)));
            logError(errorMsg);
        }

        if (!onlyInNew.isEmpty()) {
            String errorMsg = String.format("%s 表中仅在新表存在的记录(共%d条): %s",
                    tableName, onlyInNew.size(), String.join(", ", convertToStringList(onlyInNew)));
            logError(errorMsg);
        }

        return new CompareResult(oldIds.size() == newIds.size(), oldIds.size(), newIds.size());
    }

    private <S, T> void compareDataFields(String tableName,
                                          BiFunction<Integer, Integer, List<S>> oldDataProvider,
                                          BiFunction<Integer, Integer, List<T>> newDataProvider,
                                          BiConsumer<S, T> compareFunction) {
        int pageSize = 1000;
        int currentPage = 1;
        int totalDiff = 0;

        while (true) {
            List<S> oldData = oldDataProvider.apply(currentPage, pageSize);
            List<T> newData = newDataProvider.apply(currentPage, pageSize);

            if (CollectionUtils.isEmpty(oldData) && CollectionUtils.isEmpty(newData)) {
                break;
            }

            // 逐条比较字段
            for (int i = 0; i < oldData.size(); i++) {
                try {
                    compareFunction.accept(oldData.get(i), newData.get(i));
                } catch (DataDifferenceException e) {
                    logError(String.format("%s 表数据字段不一致: %s", tableName, e.getMessage()));
                    totalDiff++;
                }
            }

            if (oldData.size() < pageSize) {
                break;
            }
            currentPage++;
        }

        if (totalDiff > 0) {
            String summary = String.format("%s 表共发现 %d 处字段差异", tableName, totalDiff);
            XxlJobLogger.log(summary);
            sendAlertMessage(summary);
        }
    }


    private void compareCalendarConfig(HrmsAttendanceConfigDO oldData, CalendarConfigDO newData) {
        List<String> differences = new ArrayList<>();

        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }
        if (!Objects.equals(oldData.getCountry(), newData.getCountry())) {
            differences.add(String.format("country不一致: 旧=%s, 新=%s", oldData.getCountry(), newData.getCountry()));
        }
        if (!Objects.equals(oldData.getAttendanceConfigNo(), newData.getAttendanceConfigNo())) {
            differences.add(String.format("attendanceConfigNo不一致: 旧=%s, 新=%s", oldData.getAttendanceConfigNo(), newData.getAttendanceConfigNo()));
        }
        if (!Objects.equals(oldData.getAttendanceConfigName(), newData.getAttendanceConfigName())) {
            differences.add(String.format("attendanceConfigName不一致: 旧=%s, 新=%s", oldData.getAttendanceConfigName(), newData.getAttendanceConfigName()));
        }
        if (!Objects.equals(oldData.getType(), newData.getType())) {
            differences.add(String.format("type不一致: 旧=%s, 新=%s", oldData.getType(), newData.getType()));
        }
        if (!Objects.equals(oldData.getStatus(), newData.getStatus())) {
            differences.add(String.format("status不一致: 旧=%s, 新=%s", oldData.getStatus(), newData.getStatus()));
        }
        if (!Objects.equals(oldData.getDeptIds(), newData.getDeptIds())) {
            differences.add(String.format("deptIds不一致: 旧=%s, 新=%s", oldData.getDeptIds(), newData.getDeptIds()));
        }
        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }
        if (!Objects.equals(oldData.getIsDelete(), newData.getIsDelete())) {
            differences.add(String.format("isDelete不一致: 旧=%s, 新=%s", oldData.getIsDelete(), newData.getIsDelete()));
        }
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("日历表数据[oldId=" + oldData.getId() + ",newId" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void compareCalendarConfigDetail(HrmsAttendanceConfigDetailDO oldData, CalendarConfigDetailDO newData) {
        List<String> differences = new ArrayList<>();

        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }
        if (!Objects.equals(oldData.getAttendanceConfigId(), newData.getAttendanceConfigId())) {
            differences.add(
                    String.format(
                            "attendanceConfigId不一致: 旧=%s, 新=%s",
                            oldData.getAttendanceConfigId(), newData.getAttendanceConfigId()));
        }
        if (!Objects.equals(oldData.getYear(), newData.getYear())) {
            differences.add(String.format("year不一致: 旧=%s, 新=%s", oldData.getYear(), newData.getYear()));
        }
        if (!Objects.equals(oldData.getMonth(), newData.getMonth())) {
            differences.add(String.format("month不一致: 旧=%s, 新=%s", oldData.getMonth(), newData.getMonth()));
        }
        if (!Objects.equals(oldData.getDay(), newData.getDay())) {
            differences.add(String.format("day不一致: 旧=%s, 新=%s", oldData.getDay(), newData.getDay()));
        }
        if (!Objects.equals(oldData.getDate(), newData.getDate())) {
            differences.add(String.format("date不一致: 旧=%s, 新=%s", oldData.getDate(), newData.getDate()));
        }
        if (!Objects.equals(oldData.getDayId(), newData.getDayId())) {
            differences.add(String.format("dayId不一致: 旧=%s, 新=%s", oldData.getDayId(), newData.getDayId()));
        }
        if (!Objects.equals(oldData.getDayType(), newData.getDayType())) {
            differences.add(
                    String.format("dayType不一致: 旧=%s, 新=%s", oldData.getDayType(), newData.getDayType()));
        }
        if (!Objects.equals(oldData.getRemark(), newData.getRemark())) {
            differences.add(String.format("remark不一致: 旧=%s, 新=%s", oldData.getRemark(), newData.getRemark()));
        }
        if (!Objects.equals(oldData.getOrderby(), newData.getOrderby())) {
            differences.add(String.format("orderby不一致: 旧=%s, 新=%s", oldData.getOrderby(), newData.getOrderby()));
        }
        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(
                    String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }

        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("日历详情表数据[oldId=" + oldData.getId() + ",newId" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }


    private void compareCalendarConfigRange(HrmsAttendanceConfigRangeDO oldData, CalendarConfigRangeDO newData) {
        List<String> differences = new ArrayList<>();

        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        if (!Objects.equals(oldData.getBizId(), newData.getBizId())) {
            differences.add(String.format("bizId不一致: 旧=%s, 新=%s", oldData.getBizId(), newData.getBizId()));
        }

        // 新增 bizCode 的比较
        if (newData.getBizCode() != null && !newData.getBizCode().equals("")) {
            // 只有当 CalendarConfigRangeDO 中 bizCode 存在时才进行比较
            if (!Objects.equals(oldData.getBizId(), newData.getBizCode())) {
                differences.add(String.format("bizCode不一致: 旧=%s, 新=%s", oldData.getBizId(), newData.getBizCode()));
            }
        }

        if (!Objects.equals(oldData.getAttendanceConfigId(), newData.getAttendanceConfigId())) {
            differences.add(
                    String.format(
                            "attendanceConfigId不一致: 旧=%s, 新=%s",
                            oldData.getAttendanceConfigId(), newData.getAttendanceConfigId()));
        }

        if (!Objects.equals(oldData.getAttendanceConfigNo(), newData.getAttendanceConfigNo())) {
            differences.add(
                    String.format(
                            "attendanceConfigNo不一致: 旧=%s, 新=%s",
                            oldData.getAttendanceConfigNo(), newData.getAttendanceConfigNo()));
        }

        if (!Objects.equals(oldData.getRangeType(), newData.getRangeType())) {
            differences.add(
                    String.format("rangeType不一致: 旧=%s, 新=%s", oldData.getRangeType(), newData.getRangeType()));
        }

        if (!Objects.equals(oldData.getStartDate(), newData.getStartDate())) {
            differences.add(
                    String.format("startDate不一致: 旧=%s, 新=%s", oldData.getStartDate(), newData.getStartDate()));
        }

        if (!Objects.equals(oldData.getEndDate(), newData.getEndDate())) {
            differences.add(
                    String.format("endDate不一致: 旧=%s, 新=%s", oldData.getEndDate(), newData.getEndDate()));
        }

        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(
                    String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }

        if (!Objects.equals(oldData.getRemark(), newData.getRemark())) {
            differences.add(String.format("remark不一致: 旧=%s, 新=%s", oldData.getRemark(), newData.getRemark()));
        }

        if (!Objects.equals(oldData.getOrderby(), newData.getOrderby())) {
            differences.add(String.format("orderby不一致: 旧=%s, 新=%s", oldData.getOrderby(), newData.getOrderby()));
        }

        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("日历配置范围数据[oldId=" + oldData.getId() + ",newId" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void compareCalendarLegalLeaveConfig(HrmsCompanyLegalLeaveConfigDO oldData, CalendarLegalLeaveConfigDO newData) {
        List<String> differences = new ArrayList<>();

        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }
        if (!Objects.equals(oldData.getLocationCountry(), newData.getLocationCountry())) {
            differences.add(String.format("locationCountry不一致: 旧=%s, 新=%s", oldData.getLocationCountry(), newData.getLocationCountry()));
        }
        if (!Objects.equals(oldData.getAttendanceConfigId(), newData.getAttendanceConfigId())) {
            differences.add(String.format("attendanceConfigId不一致: 旧=%s, 新=%s", oldData.getAttendanceConfigId(), newData.getAttendanceConfigId()));
        }
        if (!Objects.equals(oldData.getYear(), newData.getYear())) {
            differences.add(String.format("year不一致: 旧=%s, 新=%s", oldData.getYear(), newData.getYear()));
        }
        if (!Objects.equals(oldData.getLegalLeaveName(), newData.getLegalLeaveName())) {
            differences.add(String.format("legalLeaveName不一致: 旧=%s, 新=%s", oldData.getLegalLeaveName(), newData.getLegalLeaveName()));
        }
        if (!Objects.equals(oldData.getLegalLeaveStartDayId(), newData.getLegalLeaveStartDayId())) {
            differences.add(String.format("legalLeaveStartDayId不一致: 旧=%s, 新=%s", oldData.getLegalLeaveStartDayId(), newData.getLegalLeaveStartDayId()));
        }
        if (!Objects.equals(oldData.getLegalLeaveEndDayId(), newData.getLegalLeaveEndDayId())) {
            differences.add(String.format("legalLeaveEndDayId不一致: 旧=%s, 新=%s", oldData.getLegalLeaveEndDayId(), newData.getLegalLeaveEndDayId()));
        }
        if (!Objects.equals(oldData.getLegalLeaveDuration(), newData.getLegalLeaveDuration())) {
            differences.add(String.format("legalLeaveDuration不一致: 旧=%s, 新=%s", oldData.getLegalLeaveDuration(), newData.getLegalLeaveDuration()));
        }
        if (!Objects.equals(oldData.getIsDelete(), newData.getIsDelete())) {
            differences.add(String.format("isDelete不一致: 旧=%s, 新=%s", oldData.getIsDelete(), newData.getIsDelete()));
        }
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("日历表数据[oldId=" + oldData.getId() + ",newId" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void comparePunchConfig(HrmsAttendancePunchConfigDO oldData, PunchConfigDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较 id
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        // 比较 country
        if (!Objects.equals(oldData.getCountry(), newData.getCountry())) {
            differences.add(String.format("country不一致: 旧=%s, 新=%s", oldData.getCountry(), newData.getCountry()));
        }

        // 新表中有 companyId，旧表中没有，跳过或特殊处理（这里假设不比较）
        // 如果需要特殊逻辑，请说明

        // 比较 punchConfigNo
        if (!Objects.equals(oldData.getPunchConfigNo(), newData.getPunchConfigNo())) {
            differences.add(String.format("punchConfigNo不一致: 旧=%s, 新=%s", oldData.getPunchConfigNo(), newData.getPunchConfigNo()));
        }

        // 比较 punchConfigName
        if (!Objects.equals(oldData.getPunchConfigName(), newData.getPunchConfigName())) {
            differences.add(String.format("punchConfigName不一致: 旧=%s, 新=%s", oldData.getPunchConfigName(), newData.getPunchConfigName()));
        }

        // 比较 punchConfigType
        if (!Objects.equals(oldData.getPunchConfigType(), newData.getPunchConfigType())) {
            differences.add(String.format("punchConfigType不一致: 旧=%s, 新=%s", oldData.getPunchConfigType(), newData.getPunchConfigType()));
        }

        // 比较 isDefault
        if (!Objects.equals(oldData.getIsDefault(), newData.getIsDefault())) {
            differences.add(String.format("isDefault不一致: 旧=%s, 新=%s", oldData.getIsDefault(), newData.getIsDefault()));
        }

        // 比较 overtimeConfig
        if (!Objects.equals(oldData.getOvertimeConfig(), newData.getOvertimeConfig())) {
            differences.add(String.format("overtimeConfig不一致: 旧=%s, 新=%s", oldData.getOvertimeConfig(), newData.getOvertimeConfig()));
        }

        // 比较 maxRepunchDays
        if (!Objects.equals(oldData.getMaxRepunchDays(), newData.getMaxRepunchDays())) {
            differences.add(String.format("maxRepunchDays不一致: 旧=%s, 新=%s", oldData.getMaxRepunchDays(), newData.getMaxRepunchDays()));
        }

        // 比较 maxRepunchNumber
        if (!Objects.equals(oldData.getMaxRepunchNumber(), newData.getMaxRepunchNumber())) {
            differences.add(String.format("maxRepunchNumber不一致: 旧=%s, 新=%s", oldData.getMaxRepunchNumber(), newData.getMaxRepunchNumber()));
        }

        // 比较 effectTime
        if (!Objects.equals(oldData.getEffectTime(), newData.getEffectTime())) {
            differences.add(String.format("effectTime不一致: 旧=%s, 新=%s", oldData.getEffectTime(), newData.getEffectTime()));
        }

        // 比较 expireTime
        if (!Objects.equals(oldData.getExpireTime(), newData.getExpireTime())) {
            differences.add(String.format("expireTime不一致: 旧=%s, 新=%s", oldData.getExpireTime(), newData.getExpireTime()));
        }

        // 比较 status
        if (!Objects.equals(oldData.getStatus(), newData.getStatus())) {
            differences.add(String.format("status不一致: 旧=%s, 新=%s", oldData.getStatus(), newData.getStatus()));
        }

        // 比较 deptIds
        String oldDeptIds = oldData.getDeptIds();
        String newDeptIds = newData.getDeptIds();
        if (StringUtils.isEmpty(oldDeptIds) && StringUtils.isEmpty(newDeptIds)) {
            return;
        } else {
            if (!Objects.equals(oldDeptIds, newDeptIds)) {
                differences.add(String.format("deptIds不一致: 旧=%s, 新=%s", oldData.getDeptIds(), newData.getDeptIds()));
            }
        }

        // 比较 punchCardType
        if (!Objects.equals(oldData.getPunchCardType(), newData.getPunchCardType())) {
            differences.add(String.format("punchCardType不一致: 旧=%s, 新=%s", oldData.getPunchCardType(), newData.getPunchCardType()));
        }

        // 比较 isLatest
        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }

        // 比较 orderby
        if (!Objects.equals(oldData.getOrderby(), newData.getOrderby())) {
            differences.add(String.format("orderby不一致: 旧=%s, 新=%s", oldData.getOrderby(), newData.getOrderby()));
        }

        // 比较 principalUserCode
        if (!Objects.equals(oldData.getPrincipalUserCode(), newData.getPrincipalUserCode())) {
            differences.add(String.format("principalUserCode不一致: 旧=%s, 新=%s", oldData.getPrincipalUserCode(), newData.getPrincipalUserCode()));
        }

        // 比较 subUserCodes
        if (!Objects.equals(oldData.getSubUserCodes(), newData.getSubUserCodes())) {
            differences.add(String.format("subUserCodes不一致: 旧=%s, 新=%s", oldData.getSubUserCodes(), newData.getSubUserCodes()));
        }

        // 如果存在差异，抛出异常
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("打卡规则数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void comparePunchConfigRange(HrmsAttendancePunchConfigRangeDO oldData, PunchConfigRangeDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较 id
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        // 比较 bizId
        if (!Objects.equals(oldData.getBizId(), newData.getBizId())) {
            differences.add(String.format("bizId不一致: 旧=%s, 新=%s", oldData.getBizId(), newData.getBizId()));
        }

        // 比较 punchConfigId
        if (!Objects.equals(oldData.getPunchConfigId(), newData.getPunchConfigId())) {
            differences.add(String.format("punchConfigId不一致: 旧=%s, 新=%s", oldData.getPunchConfigId(), newData.getPunchConfigId()));
        }

        // 比较 punchConfigNo
        if (!Objects.equals(oldData.getPunchConfigNo(), newData.getPunchConfigNo())) {
            differences.add(String.format("punchConfigNo不一致: 旧=%s, 新=%s", oldData.getPunchConfigNo(), newData.getPunchConfigNo()));
        }

        // 比较 rangeType
        if (!Objects.equals(oldData.getRangeType(), newData.getRangeType())) {
            differences.add(String.format("rangeType不一致: 旧=%s, 新=%s", oldData.getRangeType(), newData.getRangeType()));
        }

        // 比较 isNeedPunch
        if (!Objects.equals(oldData.getIsNeedPunch(), newData.getIsNeedPunch())) {
            differences.add(String.format("isNeedPunch不一致: 旧=%s, 新=%s", oldData.getIsNeedPunch(), newData.getIsNeedPunch()));
        }

        // 比较 effectTime
        if (!Objects.equals(oldData.getEffectTime(), newData.getEffectTime())) {
            differences.add(String.format("effectTime不一致: 旧=%s, 新=%s", oldData.getEffectTime(), newData.getEffectTime()));
        }

        // 比较 expireTime
        if (!Objects.equals(oldData.getExpireTime(), newData.getExpireTime())) {
            differences.add(String.format("expireTime不一致: 旧=%s, 新=%s", oldData.getExpireTime(), newData.getExpireTime()));
        }

        // 比较 isLatest
        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }

        // 比较 remark
        if (!Objects.equals(oldData.getRemark(), newData.getRemark())) {
            differences.add(String.format("remark不一致: 旧=%s, 新=%s", oldData.getRemark(), newData.getRemark()));
        }

        // 比较 orderby
        if (!Objects.equals(oldData.getOrderby(), newData.getOrderby())) {
            differences.add(String.format("orderby不一致: 旧=%s, 新=%s", oldData.getOrderby(), newData.getOrderby()));
        }

        // 新表中有 companyId，旧表中没有，跳过对比
        // 如果需要特殊处理，请说明

        // 如果存在差异，抛出异常
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("打卡规则配置适用范围数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void comparePunchClassConfig(HrmsAttendancePunchClassConfigDO oldData, PunchClassConfigDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较 id
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        // 比较 punchConfigId
        if (!Objects.equals(oldData.getPunchConfigId(), newData.getPunchConfigId())) {
            differences.add(String.format("punchConfigId不一致: 旧=%s, 新=%s", oldData.getPunchConfigId(), newData.getPunchConfigId()));
        }

        // 比较 className
        if (!Objects.equals(oldData.getClassName(), newData.getClassName())) {
            differences.add(String.format("className不一致: 旧=%s, 新=%s", oldData.getClassName(), newData.getClassName()));
        }

        // 比较 classType
        if (!Objects.equals(oldData.getClassType(), newData.getClassType())) {
            differences.add(String.format("classType不一致: 旧=%s, 新=%s", oldData.getClassType(), newData.getClassType()));
        }

        // 比较 punchInTime
        if (!Objects.equals(oldData.getPunchInTime(), newData.getPunchInTime())) {
            differences.add(String.format("punchInTime不一致: 旧=%s, 新=%s", oldData.getPunchInTime(), newData.getPunchInTime()));
        }

        // 比较 punchOutTime
        if (!Objects.equals(oldData.getPunchOutTime(), newData.getPunchOutTime())) {
            differences.add(String.format("punchOutTime不一致: 旧=%s, 新=%s", oldData.getPunchOutTime(), newData.getPunchOutTime()));
        }

        // 比较 earliestPunchInTime
        if (!Objects.equals(oldData.getEarliestPunchInTime(), newData.getEarliestPunchInTime())) {
            differences.add(String.format("earliestPunchInTime不一致: 旧=%s, 新=%s", oldData.getEarliestPunchInTime(), newData.getEarliestPunchInTime()));
        }

        // 比较 latestPunchOutTime
        if (!Objects.equals(oldData.getLatestPunchOutTime(), newData.getLatestPunchOutTime())) {
            differences.add(String.format("latestPunchOutTime不一致: 旧=%s, 新=%s", oldData.getLatestPunchOutTime(), newData.getLatestPunchOutTime()));
        }

        // 比较 isAcross
        if (!Objects.equals(oldData.getIsAcross(), newData.getIsAcross())) {
            differences.add(String.format("isAcross不一致: 旧=%s, 新=%s", oldData.getIsAcross(), newData.getIsAcross()));
        }

        // 比较 punchTimeInterval
        if (!Objects.equals(oldData.getPunchTimeInterval(), newData.getPunchTimeInterval())) {
            differences.add(String.format("punchTimeInterval不一致: 旧=%s, 新=%s", oldData.getPunchTimeInterval(), newData.getPunchTimeInterval()));
        }

        // 比较 status
        if (!Objects.equals(oldData.getStatus(), newData.getStatus())) {
            differences.add(String.format("status不一致: 旧=%s, 新=%s", oldData.getStatus(), newData.getStatus()));
        }

        // 比较 isLatest
        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }

        // 比较 orderby
        if (!Objects.equals(oldData.getOrderby(), newData.getOrderby())) {
            differences.add(String.format("orderby不一致: 旧=%s, 新=%s", oldData.getOrderby(), newData.getOrderby()));
        }

        // 比较 punchInTimeInterval
        if (!Objects.equals(oldData.getPunchInTimeInterval(), newData.getPunchInTimeInterval())) {
            differences.add(String.format("punchInTimeInterval不一致: 旧=%s, 新=%s", oldData.getPunchInTimeInterval(), newData.getPunchInTimeInterval()));
        }

        // 比较 attendanceHours
        if (!Objects.equals(oldData.getAttendanceHours(), newData.getAttendanceHours())) {
            differences.add(String.format("attendanceHours不一致: 旧=%s, 新=%s", oldData.getAttendanceHours(), newData.getAttendanceHours()));
        }

        // 比较 legalWorkingHours
        if (!Objects.equals(oldData.getLegalWorkingHours(), newData.getLegalWorkingHours())) {
            differences.add(String.format("legalWorkingHours不一致: 旧=%s, 新=%s", oldData.getLegalWorkingHours(), newData.getLegalWorkingHours()));
        }

        // 如果存在差异，抛出异常
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("打卡规则班次配置数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void comparePunchClassItemConfig(HrmsAttendancePunchClassItemConfigDO oldData, PunchClassItemConfigDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较 id
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        // 比较 punchClassId
        if (!Objects.equals(oldData.getPunchClassId(), newData.getPunchClassId())) {
            differences.add(String.format("punchClassId不一致: 旧=%s, 新=%s", oldData.getPunchClassId(), newData.getPunchClassId()));
        }

        // 比较 sortNo
        if (!Objects.equals(oldData.getSortNo(), newData.getSortNo())) {
            differences.add(String.format("sortNo不一致: 旧=%s, 新=%s", oldData.getSortNo(), newData.getSortNo()));
        }

        // 比较 punchInTime
        if (!Objects.equals(oldData.getPunchInTime(), newData.getPunchInTime())) {
            differences.add(String.format("punchInTime不一致: 旧=%s, 新=%s", oldData.getPunchInTime(), newData.getPunchInTime()));
        }

        // 比较 punchOutTime
        if (!Objects.equals(oldData.getPunchOutTime(), newData.getPunchOutTime())) {
            differences.add(String.format("punchOutTime不一致: 旧=%s, 新=%s", oldData.getPunchOutTime(), newData.getPunchOutTime()));
        }

        // 比较 earliestPunchInTime
        if (!Objects.equals(oldData.getEarliestPunchInTime(), newData.getEarliestPunchInTime())) {
            differences.add(String.format("earliestPunchInTime不一致: 旧=%s, 新=%s", oldData.getEarliestPunchInTime(), newData.getEarliestPunchInTime()));
        }

        // 比较 latestPunchInTime
        if (!Objects.equals(oldData.getLatestPunchInTime(), newData.getLatestPunchInTime())) {
            differences.add(String.format("latestPunchInTime不一致: 旧=%s, 新=%s", oldData.getLatestPunchInTime(), newData.getLatestPunchInTime()));
        }

        // 比较 latestPunchOutTime
        if (!Objects.equals(oldData.getLatestPunchOutTime(), newData.getLatestPunchOutTime())) {
            differences.add(String.format("latestPunchOutTime不一致: 旧=%s, 新=%s", oldData.getLatestPunchOutTime(), newData.getLatestPunchOutTime()));
        }

        // 比较 isAcross
        if (!Objects.equals(oldData.getIsAcross(), newData.getIsAcross())) {
            differences.add(String.format("isAcross不一致: 旧=%s, 新=%s", oldData.getIsAcross(), newData.getIsAcross()));
        }

        // 比较 punchTimeInterval
        if (!Objects.equals(oldData.getPunchTimeInterval(), newData.getPunchTimeInterval())) {
            differences.add(String.format("punchTimeInterval不一致: 旧=%s, 新=%s", oldData.getPunchTimeInterval(), newData.getPunchTimeInterval()));
        }

        // 比较 status
        if (!Objects.equals(oldData.getStatus(), newData.getStatus())) {
            differences.add(String.format("status不一致: 旧=%s, 新=%s", oldData.getStatus(), newData.getStatus()));
        }

        // 比较 isLatest
        if (!Objects.equals(oldData.getIsLatest(), newData.getIsLatest())) {
            differences.add(String.format("isLatest不一致: 旧=%s, 新=%s", oldData.getIsLatest(), newData.getIsLatest()));
        }

        // 比较 orderby
        if (!Objects.equals(oldData.getOrderby(), newData.getOrderby())) {
            differences.add(String.format("orderby不一致: 旧=%s, 新=%s", oldData.getOrderby(), newData.getOrderby()));
        }

        // 比较 punchInTimeInterval
        if (!Objects.equals(oldData.getPunchInTimeInterval(), newData.getPunchInTimeInterval())) {
            differences.add(String.format("punchInTimeInterval不一致: 旧=%s, 新=%s", oldData.getPunchInTimeInterval(), newData.getPunchInTimeInterval()));
        }

        // 比较 elasticTime
        if (!Objects.equals(oldData.getElasticTime(), newData.getElasticTime())) {
            differences.add(String.format("elasticTime不一致: 旧=%s, 新=%s", oldData.getElasticTime(), newData.getElasticTime()));
        }

        // 比较 restStartTime
        if (!Objects.equals(oldData.getRestStartTime(), newData.getRestStartTime())) {
            differences.add(String.format("restStartTime不一致: 旧=%s, 新=%s", oldData.getRestStartTime(), newData.getRestStartTime()));
        }

        // 比较 restEndTime
        if (!Objects.equals(oldData.getRestEndTime(), newData.getRestEndTime())) {
            differences.add(String.format("restEndTime不一致: 旧=%s, 新=%s", oldData.getRestEndTime(), newData.getRestEndTime()));
        }

        // 如果存在差异，抛出异常
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("打卡规则班次时间配置数据[oldId=" + oldData.getId() + ",newId=" + newData.getId() + "]不一致差异为：" + String.join(", ", differences));
        }
    }

    private void compareGpsConfig(HrmsAttendanceGpsConfigDO oldData, PunchGpsConfigDO newData) {
        List<String> differences = new ArrayList<>();

        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        if (!Objects.equals(oldData.getCountry(), newData.getCountry())) {
            differences.add(String.format("国家(country)不一致: 旧=%s, 新=%s", oldData.getCountry(), newData.getCountry()));
        }

        if (!Objects.equals(oldData.getLocationCity(), newData.getLocationCity())) {
            differences.add(String.format("城市(locationCity)不一致: 旧=%s, 新=%s", oldData.getLocationCity(), newData.getLocationCity()));
        }

        if (!Objects.equals(oldData.getAddressName(), newData.getAddressName())) {
            differences.add(String.format("地址名称(addressName)不一致: 旧=%s, 新=%s", oldData.getAddressName(), newData.getAddressName()));
        }

        if (!Objects.equals(oldData.getAddressDetail(), newData.getAddressDetail())) {
            differences.add(String.format("地址详情(addressDetail)不一致: 旧=%s, 新=%s", oldData.getAddressDetail(), newData.getAddressDetail()));
        }

        if (!Objects.equals(oldData.getLongitude(), newData.getLongitude())) {
            differences.add(String.format("经度(longitude)不一致: 旧=%s, 新=%s", oldData.getLongitude(), newData.getLongitude()));
        }

        if (!Objects.equals(oldData.getLatitude(), newData.getLatitude())) {
            differences.add(String.format("纬度(latitude)不一致: 旧=%s, 新=%s", oldData.getLatitude(), newData.getLatitude()));
        }

        if (!Objects.equals(oldData.getEffectiveRange(), newData.getEffectiveRange())) {
            differences.add(String.format("有效范围(effectiveRange)不一致: 旧=%s, 新=%s", oldData.getEffectiveRange(), newData.getEffectiveRange()));
        }

        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("GPS打卡配置数据[oldId=" + oldData.getId() + ", newId=" + newData.getId() + "]不一致，差异为：" + String.join(", ", differences));
        }
    }

    private void compareWifiConfig(HrmsAttendanceWifiConfigDO oldData, PunchWifiConfigDO newData) {
        List<String> differences = new ArrayList<>();

        // 比较主键ID
        if (!Objects.equals(oldData.getId(), newData.getId())) {
            differences.add(String.format("ID不一致: 旧=%s, 新=%s", oldData.getId(), newData.getId()));
        }

        // 比较国家字段
        if (!Objects.equals(oldData.getCountry(), newData.getCountry())) {
            differences.add(String.format("国家(country)不一致: 旧=%s, 新=%s", oldData.getCountry(), newData.getCountry()));
        }

        // 比较城市字段
        if (!Objects.equals(oldData.getLocationCity(), newData.getLocationCity())) {
            differences.add(String.format("城市(locationCity)不一致: 旧=%s, 新=%s", oldData.getLocationCity(), newData.getLocationCity()));
        }

        // 比较WiFi名称字段
        if (!Objects.equals(oldData.getWifiName(), newData.getWifiName())) {
            differences.add(String.format("WiFi名称(wifiName)不一致: 旧=%s, 新=%s", oldData.getWifiName(), newData.getWifiName()));
        }

        // 比较MAC地址字段
        if (!Objects.equals(oldData.getMacAddress(), newData.getMacAddress())) {
            differences.add(String.format("MAC地址(macAddress)不一致: 旧=%s, 新=%s", oldData.getMacAddress(), newData.getMacAddress()));
        }

        // 比较考勤业务覆盖国字段
        if (!Objects.equals(oldData.getBizCountry(), newData.getBizCountry())) {
            differences.add(String.format("考勤业务覆盖国(bizCountry)不一致: 旧=%s, 新=%s", oldData.getBizCountry(), newData.getBizCountry()));
        }

        // 检查是否存在差异并抛出异常
        if (CollectionUtils.isNotEmpty(differences)) {
            throw new DataDifferenceException("WiFi配置数据[oldId=" + oldData.getId() + ", newId=" + newData.getId() + "]不一致，差异为：" + String.join(", ", differences));
        }
    }


    private void logError(String message) {
        log.error(message);
        XxlJobLogger.log(message);
    }

    private void sendAlertMessage(String message) {
        // 实现告警发送逻辑，如调用消息推送服务
    }

    @Getter
    public static class DataDifferenceException extends RuntimeException {
        public DataDifferenceException(String message) {
            super(message);
        }
    }

    @Getter
    @AllArgsConstructor
    private static class CompareResult {
        private final boolean countMatch;
        private final int oldCount;
        private final int newCount;
    }


    private Object extractId(Object obj) {
        try {
            // 根据具体实体类的getter方法获取ID
            Method getIdMethod = obj.getClass().getMethod("getId");
            return getIdMethod.invoke(obj);
        } catch (Exception e) {
            log.error("获取ID失败", e);
            return null;
        }
    }

    private List<String> convertToStringList(Set<Object> set) {
        return set.stream()
                .map(Object::toString)
                .collect(Collectors.toList());
    }


}
