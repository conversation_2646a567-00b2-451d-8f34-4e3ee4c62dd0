package com.imile.hrms.service.punch.param.warehouse;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@Data
public class WarehouseAttendanceConfigUpdateStatusParam {

    /**
     * id
     */
    @NotNull(message = "id cannot be empty")
    private Long id;

    /**
     * 状态
     */
    @NotNull(message = "status cannot be empty")
    private String status;
}
