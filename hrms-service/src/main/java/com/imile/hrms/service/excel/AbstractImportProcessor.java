package com.imile.hrms.service.excel;

import com.alibaba.fastjson.JSON;
import com.github.easylog.annotation.EasyLog;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.imile.common.excel.ExcelImport;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.common.util.excel.ExcelUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/12
 */
@Slf4j
public abstract class AbstractImportProcessor<T extends ExcelImport, G, L> {

    @EasyLog
    public List<T> importData(List<T> dataList) {
        G globalContext = this.buildGlobalContext(dataList);
        List<T> errorList = Lists.newArrayList();
        for (T data : dataList) {
            try {
                // 字段值预处理 首尾去空格
                ExcelUtils.trim(data);
                // 初始化临时上下文对象
                L tempContext = this.buildTempContext(data, globalContext);
                // 校验数据
                String errorTips = this.checkData(data, globalContext, tempContext);
                // 判断校验结果 非空则加入错误列表
                if (StringUtils.isNotBlank(errorTips)) {
                    IpepUtils.putFail(data, errorTips);
                    errorList.add(data);
                    continue;
                }
                // 处理数据
                this.processData(data, tempContext);
            } catch (Exception e) {
                log.error("导入失败,data:{}", JSON.toJSONString(data), e);
                IpepUtils.putFail(data, "system error:" + e.getMessage());
                errorList.add(data);
            }
        }
        return errorList;
    }

    /**
     * 构建全局上下文
     *
     * @param dataList 数据列表
     * @return 全局上下文
     */
    protected abstract G buildGlobalContext(List<T> dataList);

    /**
     * 构建临时上下文
     *
     * @param data          数据
     * @param globalContext 全局上下文
     * @return 临时上下文
     */
    protected abstract L buildTempContext(T data, G globalContext);

    /**
     * 快速失败字段校验
     *
     * @param data          数据
     * @param globalContext 全局上下文
     * @return String
     */
    protected abstract String checkFailFastField(T data, G globalContext);

    /**
     * 安全失败字段校验
     *
     * @param data          数据
     * @param globalContext 全局上下文
     * @param tempContext   临时上下文
     * @return List<String>
     */
    protected abstract List<String> checkFailSafeField(T data, G globalContext, L tempContext);

    /**
     * 数据处理
     *
     * @param data        数据
     * @param tempContext 临时上下文
     */
    protected abstract void processData(T data, L tempContext);

    /**
     * 获取错误提示
     *
     * @param errorCodeEnum 错误码
     * @param params        可变参数
     * @return String
     */
    protected String getErrorTip(HrmsErrorCodeEnums errorCodeEnum, Object... params) {
        return I18nUtils.getMessage(errorCodeEnum.getDesc(), params);
    }

    /**
     * 获取匹配数据
     * 内部先判断数据是否可用 再判断可用数据是否有多条
     *
     * @param dataList           数据列表
     * @param availablePredicate 过滤器
     * @param fieldDesc          字段描述
     * @param fieldValue         字段值
     * @param errorTipList       错误提示列表
     * @return 匹配数据
     */
    protected <S> S getMappingData(List<S> dataList, Predicate<S> availablePredicate, String fieldDesc,
                                   String fieldValue, List<String> errorTipList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        List<S> filterList = dataList.stream()
                .filter(availablePredicate)
                .collect(Collectors.toList());
        if (filterList.isEmpty()) {
            errorTipList.add(this.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_UNAVAILABLE,
                    fieldDesc, fieldValue));
            return null;
        }
        // 判断数据是否重复
        if (filterList.size() > 1) {
            errorTipList.add(this.getErrorTip(HrmsErrorCodeEnums.IMPORT_FIELD_DATA_DUPLICATE,
                    fieldDesc, fieldValue));
            return null;
        }
        return filterList.get(0);
    }

    protected <M> List<Long> getIdList(List<M> dataList, Function<M, String> getter) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream()
                .map(getter)
                .filter(StringUtils::isNotBlank)
                .filter(s -> BusinessConstant.ID_PATTERN.matcher(s).matches())
                .map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());
    }

    private String checkData(T data, G globalContext, L tempContext) {
        String errorTip = this.checkFailFastField(data, globalContext);
        if (StringUtils.isNotBlank(errorTip)) {
            return errorTip;
        }
        List<String> errorTipList = this.checkFailSafeField(data, globalContext, tempContext);
        if (errorTipList.isEmpty()) {
            return "";
        }
        return Joiner.on(";\n").join(errorTipList) + ";";
    }
}
