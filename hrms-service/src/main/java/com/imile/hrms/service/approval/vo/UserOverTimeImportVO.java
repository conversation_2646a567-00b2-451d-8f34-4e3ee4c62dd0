package com.imile.hrms.service.approval.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * {@code @author:} allen
 * {@code @className:} UserOverTimeImportVO
 * {@code @since:} 2024-06-13 11:36
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserOverTimeImportVO extends OverTimeImportVO {
    /**
     * 被申请人主键id
     */
    private Long userId;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人所在国
     */
    private String userCountry;


    /**
     * 申请人所在国
     */
    private String applyUserCountry;

}
