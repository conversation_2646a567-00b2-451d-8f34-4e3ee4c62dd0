package com.imile.hrms.service.ocr.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.CertificateCheckEnum;
import com.imile.hrms.common.enums.CertificateTypeEnum;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.OcrErrorCodeEnum;
import com.imile.hrms.common.util.DateFormatterUtil;
import com.imile.hrms.service.ocr.CountryCertificateHandler;
import com.imile.hrms.service.ocr.KsaCertificateAbstractHandler;
import com.imile.hrms.service.ocr.Strategy;
import com.imile.hrms.service.punch.vo.warehouse.CertificatesVO;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.chrono.HijrahDate;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 沙特驾照OCR文字识别处理实现类
 *
 * <AUTHOR>
 * @since 2024/9/25
 */
@Slf4j
@Service
@Strategy(value = CountryCertificateHandler.class, implKey = "KsaDriverLicenseCertificateHandlerImpl")
public class KsaDriverLicenseCertificateHandlerImpl extends KsaCertificateAbstractHandler {

    private static final String DRIVING_LICENSE_EN = "DRIVING";

    private static final String SAUDI_AR = "المملكة العربية السعودية";

    private static final String SAUDI_EN = "KINGDOM OF SAUDI ARABIA";

    private static final String DRIVING_LICENSE_AR = "رخصة سياقة";

    private static final String DRIVING_LICENSE_AR_SHORT = "سياقة";
    /**
     * 第一个开始处理的词 DRIVING_LICENSE
     */
    private static final List<String> DRIVING_LICENSE_FIRST_WORDS = Lists.newArrayList(SAUDI_AR, SAUDI_EN, DRIVING_LICENSE_EN, DRIVING_LICENSE_AR);

    private static final List<String> DRIVING_LICENSE_SPECIAL_WORDS = Lists.newArrayList(DRIVING_LICENSE_EN, DRIVING_LICENSE_AR_SHORT);

    @Override
    public boolean isMatch(String countryCode, String certificateType) {
        return CountryCodeEnum.KSA.getCode().equals(countryCode) && CertificateTypeEnum.DRIVING_LICENSE.getCode().equals(certificateType);
    }

    @Override
    protected List<String> wordsFilter(List<String> wordList) {
        //不同的图片在识别后，要判断真正从数组的第几个开始计算
        Iterator<String> iterator = wordList.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            if (DRIVING_LICENSE_FIRST_WORDS.stream().anyMatch(w -> StringUtils.isNotBlank(next) && next.contains(w))) {
                break;
            } else {
                iterator.remove();
            }
        }
        return wordList;
    }

    @Override
    protected CertificateCheckEnum.IndexCombination confirmIndexCombination(List<String> wordList, CertificateCheckEnum checkCertificateEnum) {
        CertificateCheckEnum.IndexCombination indexCombination = new CertificateCheckEnum.IndexCombination();
        // 指定模版
        if (Objects.nonNull(checkCertificateEnum)) {
            indexCombination = checkCertificateEnum.getIndexCombination();
            return indexCombination;
        }
        // 自动匹配
        if (super.getDateByIndex(BusinessConstant.ZERO, wordList).equals(DRIVING_LICENSE_AR)) {
            // 类型1
            if (matchUpperCaseLetters(super.getDateByIndex(BusinessConstant.THREE, wordList))) {
                indexCombination.setFullNameIndex(BusinessConstant.THREE);
            }
            for (String item : wordList) {
                if (isExpireDate(item)) {
                    if (indexCombination.getExpireDateIndex() == null) {
                        indexCombination.setExpireDateIndex(wordList.indexOf(item));
                    }
                } else if (isKsaDriverCertificateNo(item)) {
                    if (indexCombination.getNumIndex() == null) {
                        indexCombination.setNumIndex(wordList.indexOf(item));
                    }
                }
            }
        } else if (super.getDateByIndex(BusinessConstant.ZERO, wordList).equals(SAUDI_EN) || super.getDateByIndex(BusinessConstant.ZERO, wordList).equals(SAUDI_AR)) {
            // 类型2
            // 过滤掉包含非英文字母和数字的字段
            wordList = filterLetterAndNumber(wordList);
            log.info("OCR过滤非英文字母和数字字段后的文字列表: {}", JSON.toJSONString(wordList));
            // 姓名下标为3
            indexCombination.setFullNameIndex(3);
            if (CollectionUtils.isNotEmpty(wordList)) {
                for (String item : wordList) {
                    if (item.toUpperCase().startsWith("EXP")) {
                        indexCombination.setExpireDateIndex(wordList.indexOf(item));
                    } else if (matchesNumberDigitsOnly(filterNumbers(item)) && filterNumbers(item).length() > 7) {
                        indexCombination.setNumIndex(wordList.indexOf(item));
                    }
                }
            }
        } else {
            // 缺省值
            indexCombination = CertificateCheckEnum.OTHER.getIndexCombination();
        }
        return indexCombination;
    }

    private Boolean isExpireDate(String expireDateStr) {
        expireDateStr = getFirstDates(convertArabicNumbers(expireDateStr));
        if (StringUtils.isBlank(expireDateStr)) {
            return Boolean.FALSE;
        }
        List<Integer> split = Arrays.stream(expireDateStr.split("/")).map(Integer::parseInt).collect(Collectors.toList());
        // 默认到期日期大于当前年份-16；绝大多数情况下不可能过期16年
        return CollectionUtils.isNotEmpty(split);
    }

    public String filterNumbers(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        // 使用正则表达式替换非数字字符
        return input.replaceAll("[^0-9/\\\\]", "");
    }

    /**
     * 将MM/dd/yyyy转换为yyyy/MM/dd
     *
     * @param dateString
     * @return
     */
    public String convertDateFormat(String dateString) {
        // 检查格式
        if (dateString.matches("\\d{2}/\\d{2}/\\d{4}")) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            try {
                Date date = sdf.parse(dateString);
                if (date == null) {
                    return dateString;
                }
                return DateUtil.format(date, DateFormatterUtil.SLASH_YYYYMMDD);
            } catch (Exception e) {
                log.error("convertStringToDate 日期转换异常", e);
            }
        }
        return dateString;
    }

    public List<String> filterLetterAndNumber(List<String> list) {
        // 正则表达式
        String regex = "^[a-zA-Z0-9\\s./-]*$";
        list.removeIf(x -> !x.matches(regex));
        return list;
    }

    private boolean isKsaDriverCertificateNo(String item) {
        String num = convertArabicNumbers(item);
        return matchesNumberDigitsOnly(num) && num.length() > 7 && num.startsWith("2");
    }

    @Override
    protected void checkCertificateFormat(List<String> wordList) {
        //检测证件格式有效性
        if (DRIVING_LICENSE_SPECIAL_WORDS.stream().allMatch(x -> wordList.stream().noneMatch(w -> w.contains(x)))) {
            log.info("证件扫描模版不匹配, wordList: {}", wordList);
            throw BusinessException.get(OcrErrorCodeEnum.OCR_FILE_UPLOAD_FAIL.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.OCR_FILE_UPLOAD_FAIL.getDesc()));
        }
    }

    @Override
    protected CertificatesVO convert(CertificateCheckEnum.IndexCombination indexCombination, List<String> wordList) {
        CertificatesVO certificatesVO = new CertificatesVO();
        certificatesVO.setUserName(getName(indexCombination, wordList));
        certificatesVO.setCertificatesCode(getCertificatesCode(indexCombination, wordList));
        certificatesVO.setExpireDate(getExpireDate(indexCombination, wordList));
        return certificatesVO;
    }

    private String getName(CertificateCheckEnum.IndexCombination indexCombination, List<String> wordList) {
        if (indexCombination.getFullNameIndex() != null) {
            return getDateByIndex(indexCombination.getFullNameIndex(), wordList);
        }
        return null;
    }

    private String getCertificatesCode(CertificateCheckEnum.IndexCombination indexCombination, List<String> wordList) {
        if (Objects.nonNull(indexCombination.getNumIndex())) {
            String content = convertArabicNumbers(super.getDateByIndex(indexCombination.getNumIndex(), wordList));
            String contentTrim = content.replaceAll("[^0-9]", "").trim();
            return StringUtils.isBlank(contentTrim) ? null : (contentTrim.length() > 10 ? contentTrim.substring(0, 10) : contentTrim);
        }
        return null;
    }


    private LocalDate getExpireDate(CertificateCheckEnum.IndexCombination indexCombination, List<String> wordList) {
        String expireDateStr = getDateByIndex(indexCombination.getExpireDateIndex(), wordList);
        if (Strings.isEmpty(expireDateStr)) {
            return null;
        }
        expireDateStr = convertDateFormat(convertArabicNumbers(expireDateStr));
        if (StringUtils.isBlank(expireDateStr)) {
            return null;
        }
        List<Integer> split = Arrays.stream(expireDateStr.split("/")).map(Integer::parseInt).collect(Collectors.toList());
        if (split.get(0) < 1900) {
            // 伊斯兰历 转换为 公历
            HijrahDate hijrahDate = HijrahDate.of(split.get(0), split.get(1), split.get(2));
            return LocalDate.from(hijrahDate);
        } else {
            // 世界历
            return LocalDate.of(split.get(0), split.get(1), split.get(2));
        }
    }
}
