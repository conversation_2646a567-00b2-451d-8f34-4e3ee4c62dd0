package com.imile.hrms.service.recruitment.vo;

import com.imile.hrms.service.recruitment.param.OfferAddParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfferDetailVO extends OfferAddParam {

    /**
     * 单据编号-就是审批编号
     */
    private String approveCode;


    /**
     * 单据状态
     */
    private Integer status;

    /**
     * 推荐人名称
     */
    private String referrerUser;

    /**
     * 状态变更记录
     */
    private List<StatusChangeOperationRecordVO> statusChangeRecords;

    /**
     * 国籍名称
     */
    private String nationalityName;
}
