package com.imile.hrms.service.user.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserBaseInfoDTO implements Serializable {
    private static final long serialVersionUID = 2506105180925309102L;

    private Long userId;

    private String userCode;

    private String userName;
    /**
     * 所属国家 -- 所在子公司对应国家
     */
    private String country;

    /**
     * 国籍
     * 现在库里存的二字码 而外部系统需要三字码 后面看是否可以统一
     */
    private String nationality;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 所属部门
     */
    private Long deptId;

    private String deptName;
    /**
     * 签约主体
     */
    private String settlementCenterCode;

    private String settlementCenterName;

    private String settlementRegisterCountry;

    private OrgInfoDTO orgInfo;

    /**
     * 图片url
     */
    private String profilePhotoUrl;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 用工类型
     */
    private String employeeType;
}
