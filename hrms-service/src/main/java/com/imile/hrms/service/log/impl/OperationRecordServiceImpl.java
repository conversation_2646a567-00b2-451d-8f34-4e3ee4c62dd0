package com.imile.hrms.service.log.impl;

import com.alibaba.fastjson.JSON;
import com.github.easylog.model.EasyLogInfo;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.imile.accounting.center.dto.ProjectPageResponseDto;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.CompanyInfoApiDTO;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hermes.resource.dto.DictDataDTO;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.base.OperationFieldChangeTypeEnum;
import com.imile.hrms.common.enums.base.OperationFieldParseMethodEnum;
import com.imile.hrms.common.enums.base.OperationSceneEnum;
import com.imile.hrms.common.enums.base.OperationTargetEnum;
import com.imile.hrms.common.enums.base.OperationTargetFieldEnum;
import com.imile.hrms.common.enums.user.UserContractCompanyTypeEnum;
import com.imile.hrms.common.enums.user.UserSexEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.common.util.PageConvertUtil;
import com.imile.hrms.dao.base.dao.OperationRecordDao;
import com.imile.hrms.dao.base.model.OperationDO;
import com.imile.hrms.dao.base.model.OperationRecordDO;
import com.imile.hrms.dao.base.model.condition.OperationRecordCustomConditionBuilder;
import com.imile.hrms.dao.bussiness.area.HrmsBizeAreaBaseConfigDO;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntGradeDO;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.dict.service.DictService;
import com.imile.hrms.manage.base.CnRegionManage;
import com.imile.hrms.manage.base.OperationManage;
import com.imile.hrms.manage.base.OperationSceneManage;
import com.imile.hrms.manage.organization.BizAreaManage;
import com.imile.hrms.manage.organization.BizModelManage;
import com.imile.hrms.manage.organization.GradeManage;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.log.OperationRecordService;
import com.imile.hrms.service.log.component.OperationDataDifferHolder;
import com.imile.hrms.service.log.component.OperationFieldDiffer;
import com.imile.hrms.service.log.context.OperationRecordContext;
import com.imile.hrms.service.log.param.OperationRecordQueryParam;
import com.imile.hrms.service.log.result.OperationRecordBO;
import com.imile.hrms.service.user.helper.ExternalDependencyHelper;
import com.imile.util.BeanUtils;
import com.imile.util.date.LocalTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/1
 */
@Slf4j
@Service
public class OperationRecordServiceImpl implements OperationRecordService {

    @Resource
    private OperationRecordDao operationRecordDao;
    @Resource
    private OperationSceneManage operationSceneManage;
    @Resource
    private OperationManage operationManage;
    @Resource
    private UserManage userManage;
    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private BizModelManage bizModelManage;
    @Resource
    private BizAreaManage bizAreaManage;
    @Resource
    private PostManage postManage;
    @Resource
    private GradeManage gradeManage;
    @Resource
    private CnRegionManage cnRegionManage;
    @Resource
    private ExternalDependencyHelper externalDependencyHelper;
    @Resource
    private DictService dictService;

    @Override
    public PaginationResult<OperationRecordBO> getOperationRecordList(OperationRecordQueryParam param) {
        OperationRecordCustomConditionBuilder condition = BeanUtils.convert(param, OperationRecordCustomConditionBuilder.class);
        PageInfo<OperationRecordDO> pageInfo = PageMethod.startPage(param.getCurrentPage(), param.getShowCount())
                .doSelectPageInfo(() -> operationRecordDao.selectByCustomCondition(condition));
        if (pageInfo.getList().isEmpty()) {
            return PageConvertUtil.getPageResult(Collections.emptyList(), param, (int) pageInfo.getTotal(), pageInfo.getPages());
        }
        OperationRecordContext context = this.buildOperationRecordContext(pageInfo.getList());
        List<OperationRecordBO> list = pageInfo.getList().stream()
                .filter(s -> context.getOperationMap().containsKey(s.getOperationCode()))
                .map(s -> {
                    // 上下文中设置操作对象编码 用于编辑场景解析 如职级职等解密
                    context.setOperationTargetCode(s.getOperationTargetCode());
                    OperationDO operation = context.getOperationMap().get(s.getOperationCode());
                    OperationRecordBO item = BeanUtils.convert(s, OperationRecordBO.class);
                    item.setOperationName(this.getOperationName(operation));
                    item.setOperationSceneName(context.getOperationSceneNameMap().getOrDefault(s.getOperationSceneCode(), ""));
                    item.setOperationTargetDesc(this.getOperationTargetDesc(s.getOperationTargetCode(), operation, context));
                    item.setOperationContent(this.getOperationContent(operation, s.getOperationVariable(), context));
                    return item;
                })
                .collect(Collectors.toList());
        return PageConvertUtil.getPageResult(list, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public void saveOperationRecord(EasyLogInfo log) {
        List<String> operatorFieldList = Splitter.on("|").splitToList(log.getOperator());
        OperationRecordDO entity = new OperationRecordDO();
        entity.setApplicationCode(log.getPlatform());
        entity.setOperatorUserCode(operatorFieldList.get(0));
        entity.setOperatorUserName(operatorFieldList.get(1));
        entity.setOperationTime(new Date(log.getOperateTime()));
        entity.setOperationModuleCode(OperationSceneEnum.getModule(log.getModule()).getCode());
        entity.setOperationSceneCode(log.getModule());
        entity.setOperationCode(log.getType());
        entity.setOperationTargetCode(log.getBizNo());
        entity.setOperationVariable(JSON.toJSONString(log.getContentParam()));
        BaseDOUtil.fillDOInsert(entity);
        operationRecordDao.save(entity);
    }

    private OperationRecordContext buildOperationRecordContext(List<OperationRecordDO> recordList) {
        OperationRecordContext context = new OperationRecordContext();
        if (CollectionUtils.isEmpty(recordList)) {
            return context;
        }
        try {
            this.fillOperationTargetMap(recordList, context);
            List<String> operationCodeList = recordList.stream()
                    .map(OperationRecordDO::getOperationCode)
                    .distinct()
                    .collect(Collectors.toList());
            context.setOperationMap(operationManage.getOperationCodeMap(operationCodeList));
            List<String> operationSceneCodeList = recordList.stream()
                    .map(OperationRecordDO::getOperationSceneCode)
                    .distinct()
                    .collect(Collectors.toList());
            context.setOperationSceneNameMap(operationSceneManage.getOperationSceneNameMap(operationSceneCodeList));
            // 编辑操作才需要构建上下文
            List<OperationFieldDiffer> fieldDifferList = recordList.stream()
                    .filter(s -> OperationCodeEnum.valueOfCode(s.getOperationCode()).getIsEdit())
                    .map(s -> this.getFieldDifferList(s.getOperationVariable()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            if (fieldDifferList.isEmpty()) {
                return context;
            }
            // 追加伴生数据字段
            this.appendFieldDifferListFromJson(fieldDifferList);
            // 提取涉及的数据字典编码
            List<String> dictCodeList = fieldDifferList.stream()
                    .map(s -> OperationTargetFieldEnum.valueOfKey(s.getFieldName()))
                    .filter(s -> "DICT".equals(s.getParseMethod().getType()))
                    .map(s -> s.getParseMethod().getCode())
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, List<DictDataDTO>> dictCodeDataListMap = dictService.getDictCodeDataListMap(dictCodeList);
            context.setDictCodeDataListMap(dictCodeDataListMap);
            // 提取各实体主键ID
            List<Long> userIdList
                    = this.getTargetIdList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_USER);
            userIdList.addAll(this.getTargetIdList4MultipleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_USER));
            context.setUserMap(userManage.getUserMapById(userIdList));

            List<Long> deptIdList
                    = this.getTargetIdList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_DEPT);
            List<HrmsEntDeptDO> deptList = deptManage.getDeptListByIdList(deptIdList);
            context.setDeptMap(deptList.stream()
                    .collect(Collectors.toMap(HrmsEntDeptDO::getId, Function.identity())));

            List<Long> bizModelIdList
                    = this.getTargetIdList4MultipleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_BIZ_MODEL);
            context.setBizModelMap(bizModelManage.getBizModelMap(bizModelIdList));

            List<Long> bizAreaIdList
                    = this.getTargetIdList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_BIZ_AREA);
            context.setBizAreaMap(bizAreaManage.getBizAreaMap(bizAreaIdList));

            List<Long> postIdList
                    = this.getTargetIdList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_POST);
            context.setPostMap(postManage.getPostMap(postIdList));

            List<Long> gradeIdList
                    = this.getTargetIdList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_GRADE);
            context.setGradeMap(gradeManage.getGradeMap(gradeIdList));

            List<String> ocCodeList
                    = this.getTargetCodeList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_OC);
            context.setOcMap(externalDependencyHelper.getOcMap(ocCodeList));

            List<String> projectCodeList
                    = this.getTargetCodeList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_PROJECT);
            context.setProjectMap(externalDependencyHelper.getProjectMap(projectCodeList));

            List<Long> regionIdList
                    = this.getTargetIdList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_REGION);
            context.setRegionNameMap(cnRegionManage.getRegionNameMap(regionIdList));

            this.fillVendorAndContractEntityMap(fieldDifferList, context);
            return context;
        } catch (Exception e) {
            log.info("构建操作记录上下文失败,", e);
            return context;
        }
    }

    private void fillOperationTargetMap(List<OperationRecordDO> recordList, OperationRecordContext context) {
        List<Long> userIdList = recordList.stream()
                .filter(s -> OperationTargetEnum.USER.equals(OperationCodeEnum.valueOfCode(s.getOperationCode()).getTarget()))
                .map(OperationRecordDO::getOperationTargetCode)
                .distinct()
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        context.setOperationTargetUserMap(userManage.getUserMapById(userIdList));

        List<Long> deptIdList = recordList.stream()
                .filter(s -> OperationTargetEnum.DEPT.equals(OperationCodeEnum.valueOfCode(s.getOperationCode()).getTarget()))
                .map(OperationRecordDO::getOperationTargetCode)
                .distinct()
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        context.setOperationTargetDeptMap(deptManage.getDeptMap(deptIdList));
    }

    private void fillVendorAndContractEntityMap(List<OperationFieldDiffer> fieldDifferList, OperationRecordContext context) {
        // 由于多条操作记录字段压平之后无法识别合同公司类型及合同公司编码的组合关系（比如两条操作记录一条是签约主体一条是供应商）
        // 提取到合同公司编码后追加到供应商编码及签约主体编码中分别查询信息
        // 这样虽然会有无效传参影响查询结果 但不影响实际字段值解析（因为下方字段值解析是每条操作记录单独解析 可以识别出合同公司类型）
        List<String> companyCodeList
                = this.getTargetCodeList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_VENDOR_OR_COMPANY);

        List<String> vendorCodeList
                = this.getTargetCodeList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_VENDOR);
        vendorCodeList.addAll(companyCodeList);
        context.setVendorMap(externalDependencyHelper.getVendorMap(vendorCodeList));

        List<String> contractEntityCodeList
                = this.getTargetCodeList4SingleValue(fieldDifferList, OperationFieldParseMethodEnum.REFER_COMPANY);
        contractEntityCodeList.addAll(companyCodeList);
        context.setContractEntityMap(externalDependencyHelper.getContractEntityMap(contractEntityCodeList));
    }

    private List<OperationFieldDiffer> getFieldDifferList(String operationVariable) {
        if (StringUtils.isBlank(operationVariable)) {
            return Collections.emptyList();
        }
        List<String> variableList = JSON.parseArray(operationVariable, String.class);
        if (variableList.isEmpty()) {
            return Collections.emptyList();
        }
        // 字段变更变量位于最后一个
        String changedFieldVariable = variableList.get(variableList.size() - 1);
        return JSON.parseArray(changedFieldVariable, OperationFieldDiffer.class);
    }

    private void appendFieldDifferListFromJson(List<OperationFieldDiffer> fieldDifferList) {
        List<OperationFieldDiffer> list = fieldDifferList.stream()
                .filter(s -> OperationTargetFieldEnum.parseMethodOfKey(s.getFieldName())
                        .equals(OperationFieldParseMethodEnum.JSON))
                .map(s -> {
                    OperationDataDifferHolder differHolder
                            = JSON.parseObject(s.getAfterValue().toString(), OperationDataDifferHolder.class);
                    return this.getFieldDifferListFromDifferHolder(differHolder);
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        fieldDifferList.addAll(list);
    }

    private List<OperationFieldDiffer> getFieldDifferListFromDifferHolder(OperationDataDifferHolder differHolder) {
        return Lists.newArrayList(differHolder.getInsertList(), differHolder.getUpdateList(),
                        differHolder.getDeleteList()).stream()
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<Long> getTargetIdList4SingleValue(List<OperationFieldDiffer> fieldDifferList,
                                                   OperationFieldParseMethodEnum parseMethodEnum) {
        return fieldDifferList.stream()
                .filter(s -> {
                    OperationTargetFieldEnum fieldEnum = OperationTargetFieldEnum.valueOfKey(s.getFieldName());
                    return !fieldEnum.getIsMultipleValue() && fieldEnum.getParseMethod().equals(parseMethodEnum);
                })
                .map(s -> Lists.newArrayList(s.getBeforeValue(), s.getAfterValue()))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<Long> getTargetIdList4MultipleValue(List<OperationFieldDiffer> fieldDifferList,
                                                     OperationFieldParseMethodEnum parseMethodEnum) {
        return fieldDifferList.stream()
                .filter(s -> {
                    OperationTargetFieldEnum fieldEnum = OperationTargetFieldEnum.valueOfKey(s.getFieldName());
                    return fieldEnum.getIsMultipleValue() && fieldEnum.getParseMethod().equals(parseMethodEnum);
                })
                .map(s -> Lists.newArrayList(s.getBeforeValue(), s.getAfterValue()))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(s -> CommonUtils.splitIds2List(s.toString()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<String> getTargetCodeList4SingleValue(List<OperationFieldDiffer> fieldDifferList,
                                                       OperationFieldParseMethodEnum parseMethodEnum) {
        return fieldDifferList.stream()
                .filter(s -> {
                    OperationTargetFieldEnum fieldEnum = OperationTargetFieldEnum.valueOfKey(s.getFieldName());
                    return !fieldEnum.getIsMultipleValue() && fieldEnum.getParseMethod().equals(parseMethodEnum);
                })
                .map(s -> Lists.newArrayList(s.getBeforeValue(), s.getAfterValue()))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    private String getOperationName(OperationDO operation) {
        return RequestInfoHolder.isChinese()
                ? operation.getOperationNameCn()
                : operation.getOperationNameEn();
    }

    private String getOperationTargetDesc(String operationTargetCode, OperationDO operation, OperationRecordContext context) {
        OperationTargetEnum targetEnum = OperationCodeEnum.valueOfCode(operation.getOperationCode()).getTarget();
        if (OperationTargetEnum.DEPT.equals(targetEnum)) {
            return this.getDeptDesc(this.convert2Id(operationTargetCode), context.getOperationTargetDeptMap());
        } else if (OperationTargetEnum.USER.equals(targetEnum)) {
            return this.getUserDesc(this.convert2Id(operationTargetCode), context.getOperationTargetUserMap());
        }
        return "";
    }

    private String getOperationContent(OperationDO operation, String operationVariable, OperationRecordContext context) {
        OperationCodeEnum operationCodeEnum = OperationCodeEnum.valueOfCode(operation.getOperationCode());
        List<String> variableList = JSON.parseArray(operationVariable, String.class);
        try {
            String content = this.getContent(operation, variableList.toArray());
            // 非编辑操作替换占位符即可 否则还需拼接详细字段变更信息
            if (!operationCodeEnum.getIsEdit()) {
                return content;
            }
            List<OperationFieldDiffer> fieldDifferList = this.getFieldDifferList(operationVariable);
            if (fieldDifferList.isEmpty()) {
                return content;
            }
            List<String> fieldDescList = this.getFieldDescList(fieldDifferList, context,
                    OperationFieldChangeTypeEnum.UPDATE, operationCodeEnum.getTarget());
            if (fieldDescList.isEmpty()) {
                return content;
            }
            return content + "<br/>" + String.join("<br/>", fieldDescList);
        } catch (Exception e) {
            log.info("组装操作内容失败,operationCode:{},operationVariable:{}", operation.getOperationCode(), operationVariable, e);
            return "";
        }
    }

    private List<String> getFieldDescList(List<OperationFieldDiffer> fieldDifferList, OperationRecordContext context,
                                          OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                          OperationTargetEnum operationTargetEnum) {
        List<String> fieldDescList = Lists.newArrayList();
        boolean hasContractChanged = fieldDifferList.stream()
                .anyMatch(s -> OperationTargetFieldEnum.USER_CONTRACT_INFO.getKey().equals(s.getFieldName()));
        context.setHasContractChanged(hasContractChanged);
        for (OperationFieldDiffer fieldDiffer : fieldDifferList) {
            String desc = "";
            OperationTargetFieldEnum fieldEnum
                    = OperationTargetFieldEnum.valueOfKeyAndTarget(fieldDiffer.getFieldName(), operationTargetEnum);
            switch (fieldEnum.getParseMethod()) {
                case DEFAULT:
                    desc = this.getFieldDesc4Default(fieldEnum, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue());
                    break;
                case DATE:
                    desc = this.getFieldDesc4Date(fieldEnum, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue());
                    break;
                case ENUM:
                    desc = this.getFieldDesc4Enum(fieldEnum, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue(), context);
                    break;
                case JSON:
                    desc = this.getFieldDesc4Json(fieldEnum, fieldDiffer.getAfterValue(), context);
                    break;
                case DECRYPT:
                    desc = this.getFieldDesc4Decrypt(fieldEnum, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue(), context);
                    break;
                case DICT_DEPT_TYPE:
                case DICT_EMPLOYMENT_TYPE:
                case DICT_NATION:
                case DICT_RESIDENCE_TYPE:
                case DICT_MARITAL_STATUS:
                case DICT_POLITICAL_AFFILIATION:
                    desc = this.getFieldDesc4Dict(fieldEnum, Boolean.FALSE, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue(), context);
                    break;
                case DICT_LEADER_PROPERTY:
                case DICT_CERTIFICATE_TYPE:
                case DICT_VISA_TYPE:
                case DICT_DEPENDENT_TYPE:
                case DICT_EDUCATION:
                case DICT_LANGUAGE_TYPE:
                case DICT_CONTRACT_TYPE:
                    desc = this.getFieldDesc4Dict(fieldEnum, Boolean.TRUE, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue(), context);
                    break;
                case REFER_USER:
                case REFER_DEPT:
                case REFER_BIZ_AREA:
                case REFER_BIZ_MODEL:
                case REFER_POST:
                case REFER_GRADE:
                case REFER_PROJECT:
                case REFER_VENDOR:
                case REFER_COMPANY:
                case REFER_OC:
                case REFER_VENDOR_OR_COMPANY:
                    desc = this.getFieldDesc4Refer(fieldEnum, operationFieldChangeTypeEnum,
                            fieldDiffer.getBeforeValue(), fieldDiffer.getAfterValue(), context);
                    break;
                default:
            }
            if (StringUtils.isBlank(desc)) {
                continue;
            }
            fieldDescList.add(desc);
        }
        return fieldDescList;
    }

    private String getFieldDesc4Default(OperationTargetFieldEnum fieldEnum,
                                        OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                        Object beforeValue, Object afterValue) {
        String beforeDesc = Objects.isNull(beforeValue) ? BusinessConstant.RUNG : beforeValue.toString();
        String afterDesc = Objects.isNull(afterValue) ? BusinessConstant.RUNG : afterValue.toString();
        return this.getFieldDesc(fieldEnum, operationFieldChangeTypeEnum, beforeDesc, afterDesc, Boolean.FALSE);
    }

    private String getFieldDesc4Date(OperationTargetFieldEnum fieldEnum,
                                     OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                     Object beforeValue, Object afterValue) {
        String beforeDesc = Objects.isNull(beforeValue) ? BusinessConstant.RUNG : this.convert2Date(beforeValue);
        String afterDesc = Objects.isNull(afterValue) ? BusinessConstant.RUNG : this.convert2Date(afterValue);
        return this.getFieldDesc(fieldEnum, operationFieldChangeTypeEnum, beforeDesc, afterDesc, Boolean.FALSE);
    }

    private String getFieldDesc4Enum(OperationTargetFieldEnum fieldEnum,
                                     OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                     Object beforeValue, Object afterValue, OperationRecordContext context) {
        String beforeDesc = BusinessConstant.RUNG;
        String afterDesc = BusinessConstant.RUNG;
        Boolean isCore = Boolean.FALSE;
        switch (fieldEnum) {
            case IS_DRIVER:
            case IS_DTL:
            case IS_GLOBAL_RELOCATION:
            case IS_FULL_TIME:
                beforeDesc = WhetherEnum.valueOfKey(this.convert2Integer(beforeValue)).getDesc();
                afterDesc = WhetherEnum.valueOfKey(this.convert2Integer(afterValue)).getDesc();
                break;
            case SEX:
                beforeDesc = UserSexEnum.valueOfKey(this.convert2Integer(beforeValue)).getDesc();
                afterDesc = UserSexEnum.valueOfKey(this.convert2Integer(afterValue)).getDesc();
                break;
            case CONTRACT_COMPANY_TYPE:
                // 该字段无需展示 赋值到上下文中用于签约公司解析
                context.getTempVariableMap().put(fieldEnum.getKey(), afterValue);
                break;
            default:
        }
        return this.getFieldDesc(fieldEnum, operationFieldChangeTypeEnum, beforeDesc, afterDesc, isCore);
    }

    private String getFieldDesc4Json(OperationTargetFieldEnum fieldEnum, Object value, OperationRecordContext context) {
        if (Objects.isNull(value)) {
            return "";
        }
        OperationDataDifferHolder differHolder = JSON.parseObject(value.toString(), OperationDataDifferHolder.class);
        if (Objects.isNull(differHolder)) {
            return "";
        }
        OperationTargetEnum targetEnum;
        switch (fieldEnum) {
            case BIZ_LEADER_INFO:
                targetEnum = OperationTargetEnum.DEPT_BIZ_LEADER;
                break;
            case USER_CERTIFICATE_INFO:
                targetEnum = OperationTargetEnum.USER_CERTIFICATE;
                break;
            case USER_VISA_INFO:
                targetEnum = OperationTargetEnum.USER_VISA;
                break;
            case USER_DEPENDENT_INFO:
                targetEnum = OperationTargetEnum.USER_DEPENDENT;
                break;
            case USER_EDUCATION_INFO:
                targetEnum = OperationTargetEnum.USER_EDUCATION;
                break;
            case USER_LANGUAGE_ABILITY_INFO:
                targetEnum = OperationTargetEnum.USER_LANGUAGE_ABILITY;
                break;
            case USER_QUALIFICATION_INFO:
                targetEnum = OperationTargetEnum.USER_QUALIFICATION;
                break;
            case USER_CONTRACT_INFO:
                targetEnum = OperationTargetEnum.USER_CONTRACT;
                break;
            default:
                targetEnum = OperationTargetEnum.UNKNOWN;
                break;
        }
        List<String> targetExtraFieldDescList = Lists.newArrayList();
        this.appendFieldDescList(OperationFieldChangeTypeEnum.ADD, targetEnum,
                differHolder.getInsertList(), context, targetExtraFieldDescList);
        this.appendFieldDescList(OperationFieldChangeTypeEnum.UPDATE, targetEnum,
                differHolder.getUpdateList(), context, targetExtraFieldDescList);
        this.appendFieldDescList(OperationFieldChangeTypeEnum.DELETE, targetEnum,
                differHolder.getDeleteList(), context, targetExtraFieldDescList);
        if (targetExtraFieldDescList.isEmpty()) {
            return "";
        }
        targetExtraFieldDescList.add(0, "<br/><<" + fieldEnum.getDesc()
                + (RequestInfoHolder.isChinese() ? "变更>>" : " has been updated>>"));
        return String.join("<br/>", targetExtraFieldDescList);
    }

    private String getFieldDesc4Decrypt(OperationTargetFieldEnum fieldEnum,
                                        OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                        Object beforeValue, Object afterValue, OperationRecordContext context) {
        String beforeDesc = Objects.isNull(beforeValue)
                ? BusinessConstant.RUNG
                : this.decryptValue(beforeValue, context.getOperationTargetCode());
        String afterDesc = Objects.isNull(afterValue)
                ? BusinessConstant.RUNG
                : this.decryptValue(afterValue, context.getOperationTargetCode());
        return this.getFieldDesc(fieldEnum, operationFieldChangeTypeEnum, beforeDesc, afterDesc, Boolean.FALSE);
    }

    private String getFieldDesc4Dict(OperationTargetFieldEnum fieldEnum, Boolean isCore,
                                     OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                     Object beforeValue, Object afterValue, OperationRecordContext context) {
        List<DictDataDTO> dictDataList
                = context.getDictCodeDataListMap().getOrDefault(fieldEnum.getParseMethod().getCode(), Collections.emptyList());
        Map<String, String> dictDataMap = dictDataList.stream()
                .filter(item -> item.getLangType().equals(RequestInfoHolder.getLocaleTxt()))
                .collect(Collectors.toMap(DictDataDTO::getDataCode, DictDataDTO::getDataValue));
        String beforeDesc = dictDataMap.getOrDefault(String.valueOf(beforeValue), BusinessConstant.RUNG);
        String afterDesc = dictDataMap.getOrDefault(String.valueOf(afterValue), BusinessConstant.RUNG);
        return this.getFieldDesc(fieldEnum, operationFieldChangeTypeEnum, beforeDesc, afterDesc, isCore);
    }

    private String getFieldDesc4Refer(OperationTargetFieldEnum fieldEnum,
                                      OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                      Object beforeValue, Object afterValue, OperationRecordContext context) {
        String beforeDesc = BusinessConstant.RUNG;
        String afterDesc = BusinessConstant.RUNG;
        switch (fieldEnum) {
            case DEPT_LEADER_ID:
            case USER_LEADER_ID:
                beforeDesc = this.getUserDesc(this.convert2Id(beforeValue), context.getUserMap());
                afterDesc = this.getUserDesc(this.convert2Id(afterValue), context.getUserMap());
                break;
            case DEPT_BIZ_LEADER_IDS:
                beforeDesc = this.getUsersDesc(this.convert2IdList(beforeValue), context);
                afterDesc = this.getUsersDesc(this.convert2IdList(afterValue), context);
                break;
            case USER_DEPT_ID:
            case DEPT_PARENT_ID:
                beforeDesc = this.getDeptDesc(this.convert2Id(beforeValue), context.getDeptMap());
                afterDesc = this.getDeptDesc(this.convert2Id(afterValue), context.getDeptMap());
                break;
            case USER_BIZ_MODEL_ID:
            case DEPT_BIZ_MODEL_IDS:
                beforeDesc = this.getBizModelsDesc(this.convert2IdList(beforeValue), context);
                afterDesc = this.getBizModelsDesc(this.convert2IdList(afterValue), context);
                break;
            case DEPT_BIZ_AREA_ID:
                beforeDesc = this.getBizAreaDesc(this.convert2Id(beforeValue), context);
                afterDesc = this.getBizAreaDesc(this.convert2Id(afterValue), context);
                break;
            case USER_POST_ID:
                beforeDesc = this.getPostDesc(this.convert2Id(beforeValue), context);
                afterDesc = this.getPostDesc(this.convert2Id(afterValue), context);
                break;
            case USER_GRADE_ID:
                beforeDesc = this.getGradeDesc(this.convert2Id(beforeValue), context);
                afterDesc = this.getGradeDesc(this.convert2Id(afterValue), context);
                break;
            case OC_CODE:
                beforeDesc = this.getOcDesc(String.valueOf(beforeValue), context);
                afterDesc = this.getOcDesc(String.valueOf(afterValue), context);
                break;
            case VENDOR_CODE:
                if (context.isHasContractChanged()) {
                    break;
                }
                beforeDesc = this.getVendorDesc(String.valueOf(beforeValue), context);
                afterDesc = this.getVendorDesc(String.valueOf(afterValue), context);
                break;
            case CONTRACT_ENTITY_CODE:
                if (context.isHasContractChanged()) {
                    break;
                }
                beforeDesc = this.getContractEntityDesc(String.valueOf(beforeValue), context);
                afterDesc = this.getContractEntityDesc(String.valueOf(afterValue), context);
                break;
            case PROJECT_CODE:
                beforeDesc = this.getProjectDesc(String.valueOf(beforeValue), context);
                afterDesc = this.getProjectDesc(String.valueOf(afterValue), context);
                break;
            case RESIDENCE_PROVINCE_ID:
            case RESIDENCE_CITY_ID:
            case RESIDENCE_DISTRICT_ID:
            case NATIVE_PLACE_PROVINCE_ID:
            case NATIVE_PLACE_CITY_ID:
            case NATIVE_PLACE_DISTRICT_ID:
                beforeDesc = this.getRegionDesc(this.convert2Id(beforeValue), context);
                afterDesc = this.getRegionDesc(this.convert2Id(afterValue), context);
                break;
            case CONTRACT_COMPANY_CODE:
                Integer contractCompanyType = (Integer) context.getTempVariableMap().get(OperationTargetFieldEnum.CONTRACT_COMPANY_TYPE.getKey());
                boolean isVendor = UserContractCompanyTypeEnum.VENDOR.getType().equals(contractCompanyType);
                beforeDesc = isVendor ? this.getVendorDesc(String.valueOf(beforeValue), context)
                        : this.getContractEntityDesc(String.valueOf(beforeValue), context);
                afterDesc = isVendor ? this.getVendorDesc(String.valueOf(afterValue), context)
                        : this.getContractEntityDesc(String.valueOf(afterValue), context);
                // 根据签约公司类型覆盖原有字段名
                fieldEnum = isVendor ? OperationTargetFieldEnum.VENDOR_CODE : OperationTargetFieldEnum.CONTRACT_ENTITY_CODE;
                break;
            default:
        }
        return this.getFieldDesc(fieldEnum, operationFieldChangeTypeEnum, beforeDesc, afterDesc, Boolean.FALSE);
    }

    private String getFieldDesc(OperationTargetFieldEnum fieldEnum,
                                OperationFieldChangeTypeEnum operationFieldChangeTypeEnum,
                                String beforeDesc, String afterDesc, Boolean isCore) {
        // 兼容历史默认值为null的字段 改之前是null 改之后是空字符的情况
        if (!isCore && afterDesc.equals(beforeDesc)) {
            return "";
        }
        // 删除场景取改之前值 其他场景取改之后值
        String valueDesc = OperationFieldChangeTypeEnum.DELETE.equals(operationFieldChangeTypeEnum) ? beforeDesc : afterDesc;
        // 伴生数据中的核心字段直接展示字段值
        if (isCore) {
            return valueDesc;
        }
        // 区分更新及新增、删除场景的文案格式
        return OperationFieldChangeTypeEnum.UPDATE.equals(operationFieldChangeTypeEnum)
                ? String.format(operationFieldChangeTypeEnum.getContent(), fieldEnum.getDesc(), beforeDesc, afterDesc)
                : String.format(operationFieldChangeTypeEnum.getContent(), fieldEnum.getDesc(), valueDesc);
    }

    private void appendFieldDescList(OperationFieldChangeTypeEnum operationFieldChangeTypeEnum, OperationTargetEnum operationTargetEnum,
                                     List<List<OperationFieldDiffer>> fieldDifferList,
                                     OperationRecordContext context,
                                     List<String> targetExtraFieldDescList) {
        if (CollectionUtils.isEmpty(fieldDifferList)) {
            return;
        }
        List<String> mergedFieldDescList = fieldDifferList.stream()
                .map(s -> {
                    List<String> fieldDescList = this.getFieldDescList(s, context, operationFieldChangeTypeEnum, operationTargetEnum);
                    if (fieldDescList.isEmpty()) {
                        return "";
                    }
                    String coreFieldDesc = fieldDescList.get(0);
                    List<String> otherFieldDescList = fieldDescList.size() > 1
                            ? fieldDescList.subList(1, fieldDescList.size())
                            : Collections.emptyList();
                    return operationFieldChangeTypeEnum.getDesc() + " > " + coreFieldDesc + "：" + String.join("、", otherFieldDescList);
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        targetExtraFieldDescList.addAll(mergedFieldDescList);
    }

    private Long convert2Id(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        String stringValue = value.toString();
        if (StringUtils.isBlank(stringValue)) {
            return null;
        }
        return Long.parseLong(value.toString());
    }

    private Integer convert2Integer(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        String stringValue = value.toString();
        if (StringUtils.isBlank(stringValue)) {
            return null;
        }
        return Integer.parseInt(value.toString());
    }

    private String convert2Date(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        String stringValue = value.toString();
        if (StringUtils.isBlank(stringValue)) {
            return null;
        }
        return LocalTimeUtil.ofSeconds(Long.parseLong(stringValue) / 1000).format(LocalTimeUtil.YYYY_MM_DD);
    }

    private List<Long> convert2IdList(Object value) {
        if (Objects.isNull(value)) {
            return Collections.emptyList();
        }
        return CommonUtils.splitIds2List(value.toString());
    }

    private String decryptValue(Object value, String salt) {
        if (Objects.isNull(value)) {
            return BusinessConstant.RUNG;
        }
        String stringValue = value.toString();
        if (StringUtils.isBlank(stringValue)) {
            return BusinessConstant.RUNG;
        }
        return BusinessFieldUtils.decryption(stringValue, salt);
    }

    private String getUsersDesc(List<Long> userIdList, OperationRecordContext context) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return BusinessConstant.RUNG;
        }
        return userIdList.stream()
                .map(userId -> this.getUserDesc(userId, context.getUserMap()))
                .filter(s -> !BusinessConstant.RUNG.equals(s))
                .collect(Collectors.joining("、"));
    }

    private String getUserDesc(Long userId, Map<Long, HrmsUserInfoDO> userMap) {
        HrmsUserInfoDO user = userMap.get(userId);
        if (Objects.isNull(user)) {
            return BusinessConstant.RUNG;
        }
        // 确认入职前展示ID 入职后展示编码
        return StringUtils.isBlank(user.getUserCode())
                ? user.getUserName() + "(" + user.getId() + ")"
                : user.getUserName() + "(" + user.getUserCode() + ")";
    }

    private String getDeptDesc(Long deptId, Map<Long, HrmsEntDeptDO> deptMap) {
        HrmsEntDeptDO dept = deptMap.get(deptId);
        if (Objects.isNull(dept)) {
            return BusinessConstant.RUNG;
        }
        return BusinessFieldUtils.getUnifiedDeptName(dept.getDeptNameCn(), dept.getDeptNameEn()) + "(" + dept.getDeptCode() + ")";
    }

    private String getBizModelsDesc(List<Long> bizModelIdList, OperationRecordContext context) {
        if (CollectionUtils.isEmpty(bizModelIdList)) {
            return BusinessConstant.RUNG;
        }
        return bizModelIdList.stream()
                .map(bizModelId -> this.getBizModelDesc(bizModelId, context))
                .filter(s -> !BusinessConstant.RUNG.equals(s))
                .collect(Collectors.joining("、"));
    }

    private String getBizModelDesc(Long bizModelId, OperationRecordContext context) {
        HrmsBizModelDO bizModel = context.getBizModelMap().get(bizModelId);
        if (Objects.isNull(bizModel)) {
            return BusinessConstant.RUNG;
        }
        return RequestInfoHolder.isChinese() ? bizModel.getBizModelNameCn() : bizModel.getBizModelNameEn();
    }

    private String getBizAreaDesc(Long bizAreaId, OperationRecordContext context) {
        HrmsBizeAreaBaseConfigDO bizArea = context.getBizAreaMap().get(bizAreaId);
        if (Objects.isNull(bizArea)) {
            return BusinessConstant.RUNG;
        }
        return RequestInfoHolder.isChinese() ? bizArea.getBusinessAreaNameCn() : bizArea.getBusinessAreaNameEn();
    }

    private String getPostDesc(Long postId, OperationRecordContext context) {
        PostDO post = context.getPostMap().get(postId);
        if (Objects.isNull(post)) {
            return BusinessConstant.RUNG;
        }
        return post.getPostNameEn();
    }

    private String getGradeDesc(Long gradeId, OperationRecordContext context) {
        HrmsEntGradeDO grade = context.getGradeMap().get(gradeId);
        if (Objects.isNull(grade)) {
            return BusinessConstant.RUNG;
        }
        return RequestInfoHolder.isChinese() ? grade.getGradeTypeCn() : grade.getGradeTypeEn();
    }

    private String getOcDesc(String ocCode, OperationRecordContext context) {
        EntOcApiDTO oc = context.getOcMap().get(ocCode);
        if (Objects.isNull(oc)) {
            return BusinessConstant.RUNG;
        }
        return oc.getOcName() + "(" + oc.getOcCode() + ")";
    }

    private String getVendorDesc(String vendorCode, OperationRecordContext context) {
        VendorInfoApiDTO vendor = context.getVendorMap().get(vendorCode);
        if (Objects.isNull(vendor)) {
            return BusinessConstant.RUNG;
        }
        return vendor.getVendorShortName() + "(" + vendor.getVendorCode() + ")";
    }

    private String getContractEntityDesc(String contractEntityCode, OperationRecordContext context) {
        CompanyInfoApiDTO contractEntity = context.getContractEntityMap().get(contractEntityCode);
        if (Objects.isNull(contractEntity)) {
            return BusinessConstant.RUNG;
        }
        return contractEntity.getCompanyName() + "(" + contractEntity.getCompanyOrgId().toString() + ")";
    }

    private String getProjectDesc(String projectCode, OperationRecordContext context) {
        ProjectPageResponseDto project = context.getProjectMap().get(projectCode);
        if (Objects.isNull(project)) {
            return BusinessConstant.RUNG;
        }
        String projectName = RequestInfoHolder.isChinese() ? project.getProjectDesc() : project.getProjectDescEn();
        return projectName + "(" + project.getProjectCode() + ")";
    }

    private String getRegionDesc(Long regionId, OperationRecordContext context) {
        return context.getRegionNameMap().getOrDefault(regionId, BusinessConstant.RUNG);
    }

    private String getContent(OperationDO operation, Object... params) {
        OperationCodeEnum.reassignParams(operation.getOperationCode(), params);
        return String.format(RequestInfoHolder.isChinese()
                        ? operation.getOperationContentCn()
                        : operation.getOperationContentEn(),
                params);
    }
}
