package com.imile.hrms.service.achievement.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

@Data
public class QueryOrgTargetParam extends ResourceQuery {
    private Long eventId;

    private Boolean isClover;

    private String deptName;

    private Long deptId;

    private Boolean isExport = false;

    /**
     * 版本号格式YYYY-MM-DD，将时间转为字符串形式传递
     */
    private String snapshotVersion;
}
