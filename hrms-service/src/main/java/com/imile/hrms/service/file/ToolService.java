package com.imile.hrms.service.file;

import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024/7/15
 */
public interface ToolService {

    /**
     * 文件上传，需要获取token
     *
     * @param directory 文件夹
     * @param file      文件
     * @return ossUrl
     */
    String upload(String directory, MultipartFile file);

    /**
     * 获取文件url
     *
     * @param fileKey ossKey
     * @return url
     */
    String getUrlByFileKey(String fileKey);
}
