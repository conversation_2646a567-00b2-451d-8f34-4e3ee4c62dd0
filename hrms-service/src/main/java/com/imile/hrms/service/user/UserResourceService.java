package com.imile.hrms.service.user;

import com.imile.hrms.service.user.vo.PermissionCountryDeptVO;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import com.imile.hrms.api.user.dto.UserPermissionDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserResourceService {

    /**
     * 根据用户id获取有权限部门id列表
     *
     * @param userId 用户id
     * @return PermissionDeptVO
     */
    PermissionDeptVO getPermissionDept(Long userId);

    /**
     * 根据用户userCode获取有权限部门id列表
     *
     * @param userCode 用户id
     * @return PermissionDeptVO
     */
    PermissionDeptVO getPermissionDept(String userCode);

    /**
     * 根据当前用户权限部门id列表
     *
     * @return PermissionDeptVO
     */
    PermissionDeptVO getPermissionDept();

    /**
     * 根据用户id,和指定部门获取有权限部门id列表
     *
     * @param userId         用户id
     * @param userDeptIdList 指定部门
     * @return PermissionDeptVO
     */
    PermissionDeptVO getPermissionDept(Long userId, List<Long> userDeptIdList);

    /**
     * 根据当前用户id,和指定部门获取有权限部门id列表
     *
     * @param userDeptIdList 指定部门
     * @return PermissionDeptVO
     */
    PermissionDeptVO getPermissionDept(List<Long> userDeptIdList);

    /**
     * 获取用户有权限的业务覆盖国列表
     *
     * @param userId 用户ID
     * @return List<String>
     */
    List<String> getAuthorizedBizCountryList(Long userId);

    /**
     * 获取当前用户有权限的业务覆盖国列表
     *
     * @return List<String>
     */
    List<String> getAuthorizedBizCountryList();

    /**
     * 获取用户有权限的业务覆盖国列表
     *
     * @param userId 用户ID
     * @return List<String>
     */
    List<String> getAuthorizedBizCountryList(Long userId, List<String> bizCountryList);

    /**
     * 获取当前用户有权限的业务覆盖国列表
     *
     * @return List<String>
     */
    List<String> getAuthorizedBizCountryList(List<String> bizCountryList);

    /**
     * 根据用户id获取供应商id列表
     *
     * @param userId 用户id
     * @return PermissionDeptVO
     */
    List<Long> getVendorIdList(Long userId);

    /**
     * 获取全球业务覆盖国列表
     *
     * @return List<String>
     */
    List<String> getAllBizCountryList();

    PermissionCountryDeptVO getPermissionCountryDeptVO(List<Long> deptIds, List<String> country);

    PermissionCountryDeptVO getPermissionCountryDeptVO();

    /**
     * 根据用户编码和指定数据权限类型获取用户权限
     * @param userCode
     * @param permissionType PermissionTypeEnum.DEPT; PermissionTypeEnum.HRMS_EMPLOYEE_COUNTRY_OF_RESIDENCE
     * @return
     */
    UserPermissionDTO getUserPermission(String userCode, String permissionType);
}
