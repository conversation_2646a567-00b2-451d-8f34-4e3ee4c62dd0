package com.imile.hrms.service.refactor.user.service;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.service.refactor.user.param.UserCountryPageParam;
import com.imile.hrms.service.refactor.user.param.UserOwnPageParam;
import com.imile.hrms.service.refactor.user.param.UserOwnSaveParam;
import com.imile.hrms.service.refactor.user.result.UserDeptAuthListBO;
import com.imile.hrms.service.refactor.user.result.UserLocationCountryAuthListBO;
import com.imile.hrms.service.refactor.user.result.UserOwnDetailBO;
import com.imile.hrms.service.refactor.user.result.UserOwnExportBO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
public interface UserOwnService {

    /**
     * 保存自有人员
     *
     * @param param UserOwnSaveParam
     * @return Boolean
     */
    Boolean saveOwnUser(UserOwnSaveParam param);

    /**
     * 获取自有人员详情
     *
     * @param userCode 人员编码
     * @return UserOwnDetailBO
     */
    UserOwnDetailBO getOwnUserDetail(String userCode);

    /**
     * 常驻地国家维度的权限列表
     *
     * @param param UserCountryPageParam
     * @return UserCountryListBO
     */
    PaginationResult<UserLocationCountryAuthListBO> locationCountryAuthorizedList(UserCountryPageParam param);

    /**
     * 在职/离职列表导出 (常驻地维度行权限)
     *
     * @param userCountryPageParam UserCountryPageParam
     * @return UserLocationCountryAuthorizedExportBO
     */
    PaginationResult<UserOwnExportBO> locationCountryAuthorizedExport(UserCountryPageParam userCountryPageParam);

    /**
     * 获取当前登录人有权限的常驻地(国家)
     *
     * @return List<AuthorizedCountryBO>
     */
    List<String> getAuthorizedCountryList();

    /**
     * 自有人员列表 (部门维度行权限)
     *
     * @param userOwnPageParam UserOwnPageParam
     * @return UserDeptAuthListBO
     */
    PaginationResult<UserDeptAuthListBO> deptAuthorizedList(UserOwnPageParam userOwnPageParam);

    /**
     * 自有人员导入 (部门维度行权限)
     *
     * @param userOwnPageParam UserOwnPageParam
     * @return UserOwnExportBO
     */
    PaginationResult<UserOwnExportBO> deptAuthorizedExport(UserOwnPageParam userOwnPageParam);
}
