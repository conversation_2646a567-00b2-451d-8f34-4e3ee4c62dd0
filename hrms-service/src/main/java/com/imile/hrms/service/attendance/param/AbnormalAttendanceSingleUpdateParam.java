package com.imile.hrms.service.attendance.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-5-11
 * @version: 1.0
 */
@Data
public class AbnormalAttendanceSingleUpdateParam {
    /**
     * 异常考勤记录ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long abnormalId;

    /**
     * 更新类型 :AttendanceAbnormalOperationTypeEnum(请假/外勤/补卡不走这个请求，直接走对应的审批发起入口,只支持P,OFF,PH,异常确认)
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String updateType;

    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长
     */
    private BigDecimal attendanceHours;

}
