package com.imile.hrms.service.approval.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-20
 * @version: 1.0
 */
@Data
public class AbnormalExtendDTO {

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;

    /**
     * 多时段打卡，根据分段打卡记录汇总计算得到的实际打卡时长
     */
    private BigDecimal actualWorkingHours;

    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;
}
