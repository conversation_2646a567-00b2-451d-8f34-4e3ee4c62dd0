package com.imile.hrms.service.business.salary.impl.api;

import com.imile.hrms.api.salary.api.BizFeeCostSalaryApi;
import com.imile.hrms.api.salary.dto.BizFeeCostSalaryApiDTO;
import com.imile.hrms.api.salary.query.BizFeeCostSalaryApiQuery;
import com.imile.hrms.dao.bussiness.salary.model.BizFeeCostSalaryDO;
import com.imile.hrms.dao.bussiness.salary.query.BizFeeCostSalaryQuery;
import com.imile.hrms.service.business.salary.BizFeeCostSalaryService;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @date 2021/10/21 3:50 下午
 */
@Service(version = "1.0.0")
public class BizFeeCostSalaryApiImpl implements BizFeeCostSalaryApi {

    @Resource
    private BizFeeCostSalaryService bizFeeCostSalaryService;

    @Override
    public RpcResult<List<BizFeeCostSalaryApiDTO>> getFeeCostSalaryList(BizFeeCostSalaryApiQuery query) {
        BizFeeCostSalaryQuery salaryQuery = BeanUtils.convert(query, BizFeeCostSalaryQuery.class);
        List<BizFeeCostSalaryDO> bizFeeCostSalaryDOS = bizFeeCostSalaryService.getFeeCostSalaryList(salaryQuery);
        return RpcResult.ok(BeanUtils.convert(BizFeeCostSalaryApiDTO.class, bizFeeCostSalaryDOS));
    }
}
