package com.imile.hrms.service.refactor.user.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOutsourceWorkInfoBO {

    /**
     * 工号
     */
    private String workNo;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_STATUS, ref = "workStatusDesc")
    private String workStatus;
    private String workStatusDesc;

    /**
     * 账号状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
    private String status;
    private String statusDesc;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryDate;

    /**
     * 司龄
     */
    private String seniority;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dimissionDate;

    /**
     * 姓名全称
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    private String employeeTypeDesc;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 所属部门ID
     */
    @RelationObjectValue(beanId = "hrmsDeptNewManageImpl", fieldId = "deptName", extendFieldIdList = "deptNamePath",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 部门链
     */
    private String deptNamePath;

    /**
     * 所属网点编码
     */
    @RelationObjectValue(beanId = "entOcServiceImpl", fieldId = "ocName")
    private String ocCode;

    /**
     * 所属网点名称
     */
    private String ocName;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 业务节点ID
     */
    @RelationObjectValue(beanId = "bizModelManageImpl", fieldId = "bizModelName",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long bizModelId;

    /**
     * 业务节点名称
     */
    private String bizModelName;

    /**
     * 项目编码
     */
    @RelationObjectValue(beanId = "rpcProjectServiceImpl", fieldId = "projectName",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 岗位ID
     */
    @RelationObjectValue(beanId = "postManageImpl", fieldId = "postName", fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 是否司机（0:否 1:是）
     */
    private Integer isDriver;

    /**
     * 是否是DTL（0:否 1:是）
     */
    private Integer isDtl;

    /**
     * 汇报上级ID
     */
    @RelationObjectValue(beanId = "hrmsUserManageImpl", fieldId = "leaderUserName", extendFieldIdList = "leaderUserCode")
    private Long leaderId;

    /**
     * 汇报上级账号
     */
    private String leaderUserCode;

    /**
     * 汇报上级姓名
     */
    private String leaderUserName;

    /**
     * 供应商编码
     */
    @RelationObjectValue(beanId = "vendorServiceImpl", fieldId = "vendorName")
    private String vendorCode;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;
}
