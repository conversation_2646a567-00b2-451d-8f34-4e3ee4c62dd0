package com.imile.hrms.service.excel.field;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/26
 */
@Getter
public enum UserUpdate4OutsourcingDriverImportFieldEnum implements ImportField {
    /**
     * 劳务派遣、合作伙伴司机导入字段枚举类
     */
    USER_CODE("userCode", "账号", "Account"),
    USER_NAME("userName", "姓名全称", "Full Name"),
    OC_NAME("ocName", "网点", "Station"),
    LEADER_CODE("leaderCode", "汇报上级账号", "Account of Reporting Supervisor"),
    VENDOR_CODE("vendorCode", "供应商", "Vendor"),
    LOCATION_COUNTRY("locationCountry", "常驻国", "Base Location(Country)"),
    LOCATION_PROVINCE("locationProvince", "常驻省", "Base Location(Province)"),
    LOCATION_CITY("locationCity", "常驻市", "Base Location(City)"),
    ;

    private final String key;

    private final String descCn;

    private final String descEn;

    UserUpdate4OutsourcingDriverImportFieldEnum(String key, String descCn, String descEn) {
        this.key = key;
        this.descCn = descCn;
        this.descEn = descEn;
    }

    @Override
    public String getDesc() {
        return RequestInfoHolder.isChinese() ? descCn : descEn;
    }
}
