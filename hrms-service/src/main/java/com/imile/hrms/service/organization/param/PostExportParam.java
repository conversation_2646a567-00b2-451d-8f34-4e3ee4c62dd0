package com.imile.hrms.service.organization.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PostExportParam extends ResourceQuery {

    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 启用状态
     */
    private String status;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 中文名
     */
    private String postNameCn;
    /**
     * 英文名
     */
    private String postNameEn;

    /**
     * 岗位列表
     */
    private String postIdList;

    /**
     * 职位族id列表
     */
    private String jobFamilyIdList;

    /**
     * 职位类id列表
     */
    private String jobCategoryIdList;

    /**
     * 职位子类id列表
     */
    private String jobSubCategoryIdList;

}
