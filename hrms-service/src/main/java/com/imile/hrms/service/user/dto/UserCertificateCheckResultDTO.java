package com.imile.hrms.service.user.dto;

import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import java.util.Date;


@Data
public class UserCertificateCheckResultDTO {

    private Long id;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 所属国家名称
     */
    private String countryName;

    /**
     * 所属国家编码
     */
    private String countryCode;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 工作岗位
     */
    private String postName;


    /**
     * 所属部门
     */
    private String deptName;


    /**
     * 汇报上级名称
     */
    private String leaderName;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 状态 状态(ACTIVE 生效, PENDING 入职中, DISABLED 冻结)
     */
    private String status;

    /**
     * 入职时间
     */
    private Date entryDate;

    /**
     * 图片url
     */
    @HyperLink(ref = "profilePhotoUrlHttps")
    private String profilePhotoUrl;

    /**
     * 员工头像地址Https
     */
    private String profilePhotoUrlHttps;

    /**
     * 是否当前部门
     */
    private boolean isCurrentDept;


    /**
     * 需要二次入职的类型
     * null:不需要二次入职
     * DIMISSION:离职后再二次入职
     * CANCEL_ENTRY：已放弃入职后再二次入职
     */
    private String entryAgainType;

}
