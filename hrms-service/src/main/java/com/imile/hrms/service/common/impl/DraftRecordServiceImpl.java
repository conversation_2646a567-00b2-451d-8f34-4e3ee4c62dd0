package com.imile.hrms.service.common.impl;

import com.imile.common.enums.StatusEnum;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.entity.DataOverrideHolder;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.dao.user.model.HrmsDraftRecordDO;
import com.imile.hrms.manage.common.DraftRecordManage;
import com.imile.hrms.service.common.DraftRecordService;
import com.imile.hrms.service.common.param.DraftSaveParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Slf4j
@Service
public class DraftRecordServiceImpl implements DraftRecordService {

    private static final String LOCK_KEY = "HRMS:DRAFT:LOCK:";

    @Resource
    private DraftRecordManage draftRecordManage;
    @Resource
    private ImileRedisClient imileRedisClient;

    @Override
    public Boolean saveDraft(DraftSaveParam param) {
        String lockKey = LOCK_KEY + param.getDraftTypeEnum().getCode() + ":" + param.getForeignKey();
        try {
            boolean flag = imileRedisClient.tryLock(lockKey, 5, 180);
            if (!flag) {
                log.error("获取分布式锁异常,lockKey:{}", lockKey);
                return Boolean.FALSE;
            }
            HrmsDraftRecordDO record = draftRecordManage.getDraftRecord(param.getDraftTypeEnum(), param.getForeignKey());
            HrmsDraftRecordDO originEntity = null;
            if (Objects.nonNull(record)) {
                originEntity = new HrmsDraftRecordDO();
                originEntity.setId(record.getId());
                originEntity.setIsLatest(WhetherEnum.NO.getKey());
            }
            HrmsDraftRecordDO latestEntity = this.buildDraftRecord4Init(param);
            draftRecordManage.doSave(DataOverrideHolder.of(originEntity, latestEntity));
            return Boolean.TRUE;
        } catch (InterruptedException e) {
            log.info("获取分布式锁异常,lockKey:{}", lockKey, e);
            Thread.currentThread().interrupt();
        } finally {
            imileRedisClient.unlock(lockKey);
        }
        return Boolean.TRUE;
    }

    private HrmsDraftRecordDO buildDraftRecord4Init(DraftSaveParam param) {
        HrmsDraftRecordDO entity = new HrmsDraftRecordDO();
        entity.setDraftType(param.getDraftTypeEnum().getCode());
        entity.setForeignKey(param.getForeignKey());
        entity.setContent(param.getContent());
        entity.setStatus(StatusEnum.ACTIVE.getCode());
        entity.setIsLatest(WhetherEnum.YES.getKey());
        entity.setIsDelete(WhetherEnum.NO.getKey());
        return entity;
    }
}
