package com.imile.hrms.service.achievement.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AchievementsHasAuditParam {
    /**
     * 部门id
     */
    @NotEmpty(message = "部门id不能为空")
    private List<Long> deptIds;

    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long eventId;
}
