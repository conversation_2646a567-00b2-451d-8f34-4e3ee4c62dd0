package com.imile.hrms.service.refactor.report.param;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.imile.hrms.dao.user.query.ResourceQuery;
import com.imile.util.BeanUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class UserPermissionReportExportParam extends ResourceQuery {

    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 部门
     */
    private String deptIdList;


    public UserPermissionReportPageParam convert() {
        UserPermissionReportPageParam param = BeanUtils.convert(this, UserPermissionReportPageParam.class);
        // 部门
        if (StringUtils.isNotBlank(this.getDeptIdList())) {
            param.setDeptIdList(JSON.parseArray(this.getDeptIdList(), Long.class));
        }
        param.setShowCount(2000);
        param.setIsExport(true);
        return param;
    }
}
