package com.imile.hrms.service.organization.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/2
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListModeDisabledDeptListParam extends ResourceQuery {

    /**
     * 部门id
     */
    private Long parentId;

    /**
     * 停用起始时间
     */
    private Date disabledStartTime;

    /**
     * 停用结束时间
     */
    private Date disabledEndTime;

    /**
     * 业务领域
     */
    private String bizArea;
}
