package com.imile.hrms.service.user.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.FileTemplateApiDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.user.EducationLevelEnum;
import com.imile.hrms.common.enums.user.UserSexEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.util.date.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
@Component
public class ApprovalBodyHelper {

    @Resource
    private HrmsDeptNewManage deptManage;
    @Resource
    private PostManage postManage;

    public Map<String, String> getSexDescListMap(List<Integer> sexList) {
        if (CollectionUtils.isEmpty(sexList)) {
            return Collections.emptyMap();
        }
        List<String> cnList = Lists.newArrayList();
        List<String> enList = Lists.newArrayList();
        sexList.forEach(s -> {
            cnList.add(UserSexEnum.valueOfKey(s).getDescCn());
            enList.add(UserSexEnum.valueOfKey(s).getDescEn());
        });
        return this.getLanguageValueMap(JSON.toJSONString(cnList), JSON.toJSONString(enList));
    }

    public Map<String, String> getWhetherDescListMap(List<Integer> valueList) {
        if (CollectionUtils.isEmpty(valueList)) {
            return Collections.emptyMap();
        }
        List<String> cnList = Lists.newArrayList();
        List<String> enList = Lists.newArrayList();
        valueList.forEach(s -> {
            cnList.add(WhetherEnum.valueOfKey(s).getDescCn());
            enList.add(WhetherEnum.valueOfKey(s).getDescEn());
        });
        return this.getLanguageValueMap(JSON.toJSONString(cnList), JSON.toJSONString(enList));
    }

    public Map<String, String> getEducationLevelDescListMap(List<String> educationLevelList) {
        if (CollectionUtils.isEmpty(educationLevelList)) {
            return Collections.emptyMap();
        }
        List<String> cnList = Lists.newArrayList();
        List<String> enList = Lists.newArrayList();
        educationLevelList.forEach(s -> {
            cnList.add(EducationLevelEnum.getByCode(s).getDesc());
            enList.add(EducationLevelEnum.getByCode(s).getDescEn());
        });
        return this.getLanguageValueMap(JSON.toJSONString(cnList), JSON.toJSONString(enList));
    }

    public Map<String, String> getDeptNameListMap(List<Long> deptIdList) {
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyMap();
        }
        Map<Long, HrmsEntDeptDO> deptMap = deptManage.getDeptMap(deptIdList);
        List<String> cnList = Lists.newArrayList();
        List<String> enList = Lists.newArrayList();
        deptIdList.forEach(s -> {
            HrmsEntDeptDO dept = deptMap.get(s);
            String cn = Objects.isNull(dept)
                    ? BusinessConstant.RUNG
                    : BusinessFieldUtils.getUnifiedDeptName(dept.getDeptNameCn(), dept.getDeptNameEn());
            String en = Objects.isNull(dept)
                    ? BusinessConstant.RUNG
                    : dept.getDeptNameEn();
            cnList.add(cn);
            enList.add(en);
        });
        return this.getLanguageValueMap(JSON.toJSONString(cnList), JSON.toJSONString(enList));
    }

    public Map<String, String> getDeptNameMap(Long deptId) {
        HrmsEntDeptDO dept = deptManage.getDeptById(deptId);
        String cnName = BusinessFieldUtils.getUnifiedDeptName(dept.getDeptNameCn(), dept.getDeptNameEn());
        String enName = dept.getDeptNameEn();
        return this.getLanguageValueMap(cnName, enName);
    }

    public Map<String, String> getPostNameListMap(List<Long> postIdList) {
        if (CollectionUtils.isEmpty(postIdList)) {
            return Collections.emptyMap();
        }
        Map<Long, PostDO> postMap = postManage.getPostMap(postIdList);
        List<String> enList = Lists.newArrayList();
        postIdList.forEach(s -> {
            PostDO post = postMap.get(s);
            // 岗位名称固定使用英文名
            String en = Objects.isNull(post) ? BusinessConstant.RUNG : post.getPostNameEn();
            enList.add(en);
        });
        return this.getLanguageValueMap(JSON.toJSONString(enList), JSON.toJSONString(enList));
    }

    public static <T> List<T> getMultipleFieldValue(Map<String, List<Object>> fieldValueListMap, String fieldName, Class<T> clazz) {
        List<Object> valueList = fieldValueListMap.getOrDefault(fieldName, Collections.emptyList());
        // 处理日期类型转换为字符串
        List<Object> processedList = valueList.stream()
                .map(obj -> {
                    if (obj instanceof java.util.Date) {
                        return DateUtils.date2Str((java.util.Date) obj);
                    }
                    return obj;
                })
                .collect(Collectors.toList());
        return JSON.parseArray(JSON.toJSONString(processedList), clazz);
    }

    public static Map<String, String> getLanguageValueMap(String cn, String en) {
        Map<String, String> map = Maps.newHashMap();
        map.put(LanguageTypeEnum.zh_CN.getCode(), cn);
        map.put(LanguageTypeEnum.en_US.getCode(), en);
        return map;
    }

    public static List<FileTemplateApiDTO> convert2FileList(String files) {
        return AttachmentDTO.convert2List(files).stream()
                .map(e -> {
                    FileTemplateApiDTO file = new FileTemplateApiDTO();
                    file.setFileName(e.getAttachmentName());
                    file.setFileUrl(e.getUrlPath());
                    file.setFileType(e.getAttachmentType());
                    return file;
                })
                .collect(Collectors.toList());
    }

    public static List<FileTemplateApiDTO> convert2FileList(List<AttachmentDTO> attachmentList) {
        if (CollectionUtils.isEmpty(attachmentList)) {
            return Collections.emptyList();
        }
        return attachmentList.stream()
                .map(e -> {
                    FileTemplateApiDTO file = new FileTemplateApiDTO();
                    file.setFileName(e.getAttachmentName());
                    file.setFileUrl(e.getUrlPath());
                    file.setFileType(e.getAttachmentType());
                    return file;
                })
                .collect(Collectors.toList());
    }
}
