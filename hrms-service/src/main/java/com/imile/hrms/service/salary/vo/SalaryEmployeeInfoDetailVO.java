package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.salary.dto.SalaryEmployeeOtherDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/9/11 17:15
 * @version: 1.0
 */
@Data
public class SalaryEmployeeInfoDetailVO {

    private Long employeeSalaryInfoId;

    private Long userId;
    /**
     * 员工姓名
     */
    private String userName;
    /**
     * 账号
     */
    private String userCode;
    /**
     * 工号
     */
    private String workNo;
    /**
     * 员工邮箱
     */
    private String email;
    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 员工所属国
     */
    private String country;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 员工性质
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    private String employeeTypeDesc;

    /**
     * 算薪国家（申请国）
     */
    private String applyCountry;

    /**
     * 结算时间-年月(202308)
     */
    private Long settlementDate;

    /**
     * 计薪开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 计薪结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 费用承担组织
     */
    private Long costOrgId;
    /**
     * 费用承担组织名称
     */
    private String costOrgName;
    /**
     * 费用承担国家
     */
    private String costCountry;
    /**
     * 费用承担部门
     */
    private Long costDeptId;
    /**
     * 费用承担部门
     */
    private String costDeptName;
    /**
     * 费用承担网点
     */
    private Long costStationId;
    /**
     * 费用承担网点
     */
    private String costStationName;
    /**
     * 费用承担项目
     */
    private String costProjectName;
    /**
     * 发薪总金额
     */
    private String totalSalaryAmount;

    /**
     * 支付方式ErsPaymentMethod
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ERS_PAYMENT_METHOD, ref = "payTypeDesc")
    private String payType;
    private String payTypeDesc;

    /**
     * 增减项
     */
    private List<SalaryEmployeeOtherDTO> otherDetail;


    /**
     * 用户状态
     */
    private String status;

}
