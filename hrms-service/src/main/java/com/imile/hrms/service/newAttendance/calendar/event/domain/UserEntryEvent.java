package com.imile.hrms.service.newAttendance.calendar.event.domain;

import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR> chen
 * @Date 2025/2/13
 * @Description
 */
public class UserEntryEvent extends ApplicationEvent {

    private final HrmsUserInfoDO userInfoDO;

    public HrmsUserInfoDO getData() {
        return userInfoDO;
    }

    public UserEntryEvent(Object source, HrmsUserInfoDO userInfoDO) {
        super(source);
        this.userInfoDO = userInfoDO;
    }
}
