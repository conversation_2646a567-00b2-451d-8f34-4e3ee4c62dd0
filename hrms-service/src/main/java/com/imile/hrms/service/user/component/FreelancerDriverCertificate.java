package com.imile.hrms.service.user.component;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/2/5
 */
@Data
public class FreelancerDriverCertificate {

    /**
     * 证件类型编码（详见枚举类：CertificateTypeEnum）
     *
     * @see com.imile.genesis.api.enums.CertificateTypeEnum
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String certificateTypeCode;

    /**
     * 证件正面路径
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    @HyperLink(ref = "certificateFrontUrl")
    private String certificateFrontPath;

    /**
     * 证件正面照链接
     */
    private String certificateFrontUrl;

    /**
     * 证件背面路径
     */
    @HyperLink(ref = "certificateBackUrl")
    private String certificateBackPath;

    /**
     * 证件背面照链接
     */
    private String certificateBackUrl;
}
