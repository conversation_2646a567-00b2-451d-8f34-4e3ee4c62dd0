package com.imile.hrms.service.user.dto;

import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 众包司机Payment信息
 */
@Data
public class LhdDriverPaymentInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * CPF NUMBER
     */
    private String cpfNumber;

    /**
     * CNPJ NUMBER
     */
    private String cnpjNumber;

    /**
     * CPF证件信息
     */
    @HyperLink(ref = "cpfCertificatePathHttps")
    private String cpfCertificatePath;
    private String cpfCertificatePathHttps;


    /**
     * CNPJ证件信息
     */
    @HyperLink(ref = "cnpjCertificatePathHttps")
    private String cnpjCertificatePath;
    private String cnpjCertificatePathHttps;
    /**
     * 税务id
     */
    private String taxId;

    /**
     * 税务id
     */
    private String taxIdSource;

    /**
     * 邮编
     */
    private List<ZipCodeInfoDTO> zipCodeList;

    /**
     * 驾驶证照片正面
     */
    @HyperLink(ref = "certificateFrontPathHttps")
    private String certificateFrontPath;
    private String certificateFrontPathHttps;
    /**
     * 驾驶证照片背面
     */
    @HyperLink(ref = "certificateBackPathHttps")
    private String certificateBackPath;
    private String certificateBackPathHttps;

}
