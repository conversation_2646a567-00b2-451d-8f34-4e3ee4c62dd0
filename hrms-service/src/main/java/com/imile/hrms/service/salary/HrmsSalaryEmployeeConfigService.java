package com.imile.hrms.service.salary;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.salary.dto.SalaryConfigEmployeeInfoDTO;
import com.imile.hrms.dao.salary.dto.SalaryEmployeeConfigDetailDTO;
import com.imile.hrms.dao.salary.param.SalaryConfigEmployeeAddParam;
import com.imile.hrms.dao.salary.param.SalaryConfigEmployeeUpdateParam;
import com.imile.hrms.dao.salary.query.SalaryConfigEmployeeInfoQuery;
import com.imile.hrms.service.attendance.dto.EmployeeAttendanceImportDTO;
import com.imile.hrms.service.salary.dto.EmployeeSalaryConfigImportDTO;
import com.imile.hrms.service.salary.dto.EmployeeSalaryConfigImportResultDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/31
 */
public interface HrmsSalaryEmployeeConfigService {

    /**
     * 员工薪酬管理导入数据
     */
    List<EmployeeSalaryConfigImportDTO> salaryManageImport(List<EmployeeSalaryConfigImportDTO> importList);

    /**
     * 员工薪酬方案配置 查询
     *
     * @param query
     * @return
     */
    PaginationResult<SalaryConfigEmployeeInfoDTO> list(SalaryConfigEmployeeInfoQuery query);

    /**
     * 员工薪酬方案配置 新增
     *
     * @param addParam
     */
    void add(SalaryConfigEmployeeAddParam addParam);

    /**
     * 员工薪酬方案配置 修改
     *
     * @param updateParam
     */
    void update(SalaryConfigEmployeeUpdateParam updateParam);

    /**
     * 获取详情,第一条为最新记录，第二条为上次修改记录
     *
     * @param id
     * @return
     */
    List<SalaryEmployeeConfigDetailDTO> getDetailAndHistory(Long id);

    /**
     * 获取员工薪酬配置历史记录
     *
     * @param query
     * @return
     */
    PaginationResult<SalaryConfigEmployeeInfoDTO> history(SalaryConfigEmployeeInfoQuery query);

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    SalaryEmployeeConfigDetailDTO detail(Long id);

}
