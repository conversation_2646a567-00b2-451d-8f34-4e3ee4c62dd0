package com.imile.hrms.service.probation.bo;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * 评委评价
 *
 * <AUTHOR>
 * @date 2024/07/17
 */
@Data
public class ProbationJudgeInfoBO implements Serializable {
    private static final long serialVersionUID = -1899810468640926391L;

    /**
     * 评委评价id
     */
    private Long id;

    /**
     * 试用期id
     */
    private Long userProbationId;

    /**
     * 转正建议/结果
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.PROBATION_CONFIRMATION_RESULT, ref = "confirmationAdviceDesc")
    private Integer confirmationAdvice;
    private String confirmationAdviceDesc;

    /**
     * 评委评价
     */
    private String judgeEvaluate;

    /**
     * 评委人员id
     */
    private Long judgeId;

    /**
     * 评委人员姓名
     */
    private String judgeName;

    /**
     * 评委人员账号
     */
    private String judgeCode;
}
