package com.imile.hrms.service.log.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1
 */
@Data
public class OperationQueryParam {

    /**
     * 操作模块编码
     *
     * @see com.imile.hrms.common.enums.base.OperationModuleEnum
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String operationModuleCode;

    /**
     * 操作场景编码列表
     *
     * @see com.imile.hrms.common.enums.base.OperationSceneEnum
     */
    private List<String> operationSceneCodeList;
}
