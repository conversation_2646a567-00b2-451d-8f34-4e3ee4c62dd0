package com.imile.hrms.service.salary.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.BigDecimalUtil;
import com.imile.hrms.common.util.PattenUtil;
import com.imile.hrms.common.util.HrmsValidationUtil;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.dao.salary.dao.HrmsSalaryEmployeeUploadItemsDao;
import com.imile.hrms.dao.salary.dto.SalaryEmployeeUploadItemDTO;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryEmployeeUploadItemsMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeUploadItemsDO;
import com.imile.hrms.dao.salary.param.ImportSalaryEmployeeUploadParam;
import com.imile.hrms.dao.salary.param.SalaryEmployeeUploadParam;
import com.imile.hrms.dao.salary.query.SalaryEmployeeUploadItemQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.resource.SystemResourceManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.salary.HrmsSalaryEmployeeUploadItemsService;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HrmsSalaryEmployeeUploadItemsServiceImpl extends BaseService implements HrmsSalaryEmployeeUploadItemsService {
    @Autowired
    private SystemResourceManage systemResourceManage;
    @Autowired
    private HrmsSalaryEmployeeUploadItemsMapper hrmsSalaryEmployeeUploadItemsMapper;
    @Autowired
    private HrmsSalaryEmployeeUploadItemsDao hrmsSalaryEmployeeUploadItemsDao;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    private static final String MONEY_PATTERN_STR = "^\\d+(.(\\d)*[1-9])*$";

    private static final BigDecimal TEN_MILLION = new BigDecimal("10000000");


    @Override
    public List<ImportSalaryEmployeeUploadParam> importUpdateItems(List<ImportSalaryEmployeeUploadParam> updateItems) {
        log.info("importUpdateItems | updateItems:{}", JSON.toJSONString(updateItems));
        //上传失败
        List<ImportSalaryEmployeeUploadParam> failList = new ArrayList<>();
        //需要新增的上传项集合
        List<HrmsSalaryEmployeeUploadItemsDO> addList = new ArrayList<>();
        for (ImportSalaryEmployeeUploadParam uploadParam : updateItems) {
            try {
                //基本非空校验
                validate(uploadParam);
                //逻辑校验
                HrmsUserInfoDO userInfoDO = ImportItemValidate(uploadParam);
                //新增
                addNewRecord(uploadParam, addList, userInfoDO);
            } catch (Exception e) {
                log.info("import employee upload item [" + uploadParam.getUserCode() + "] do fail", e);
                IpepUtils.putFail(uploadParam, e.getMessage());
                failList.add(uploadParam);
            }
        }
        //落库
        if (!CollUtil.isEmpty(addList)) {
            hrmsSalaryEmployeeUploadItemsMapper.replaceIntoBatchSomeColumn(addList);
        }


        //返回错误列表
        return failList;
    }

    private void validate(ImportSalaryEmployeeUploadParam uploadParam) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(uploadParam.getSalaryDate()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), "Date");
        BusinessLogicException.checkTrue(StringUtils.isEmpty(uploadParam.getUserCode()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), "Work No");
    }

    /**
     * 新增导入落库数据
     *
     * @param uploadParam
     * @param addList
     */
    private void addNewRecord(ImportSalaryEmployeeUploadParam uploadParam, List<HrmsSalaryEmployeeUploadItemsDO> addList, HrmsUserInfoDO userInfoDO) {
        HrmsSalaryEmployeeUploadItemsDO uploadItemsDO = BeanUtil.copyProperties(uploadParam, HrmsSalaryEmployeeUploadItemsDO.class);
        uploadItemsDO.setIsDelete(IsDeleteEnum.NO.getCode());
        uploadItemsDO.setId(iHrmsIdWorker.nextId());
        uploadItemsDO.setUserId(userInfoDO.getId());
        uploadItemsDO.setIsLatest(BusinessConstant.Y);
        uploadItemsDO.setIncentiveSalary(Optional.ofNullable(uploadParam.getIncentiveSalary()).map(BigDecimal::new).orElse(null));
        uploadItemsDO.setOtherDecreaseSalary(Optional.ofNullable(uploadParam.getOtherDecreaseSalary()).map(BigDecimal::new).orElse(null));
        uploadItemsDO.setOtherIncreaseSalary(Optional.ofNullable(uploadParam.getOtherIncreaseSalary()).map(BigDecimal::new).orElse(null));
        uploadItemsDO.setTaxation(Optional.ofNullable(uploadParam.getTaxation()).map(BigDecimal::new).orElse(null));
        uploadItemsDO.setCountry(userInfoDO.getOriginCountry());
        BaseDOUtil.fillDOInsert(uploadItemsDO);
        addList.add(uploadItemsDO);
    }

    /**
     * 逻辑校验
     *
     * @param uploadParam
     */
    private HrmsUserInfoDO ImportItemValidate(ImportSalaryEmployeeUploadParam uploadParam) {
        //校验时间参数是否合法
        BusinessLogicException.checkTrue(!PattenUtil.pattern(uploadParam.getSalaryDate()), HrmsErrorCodeEnums.DATE_FORMAT_ERROR.getCode(), HrmsErrorCodeEnums.DATE_FORMAT_ERROR.getDesc(), uploadParam.getSalaryDate());
        //校验工号是否存在
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.userListByUserCodes(Arrays.asList(uploadParam.getUserCode()));

        if (CollectionUtils.isEmpty(userInfoDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        //校验数值参数合法化
        if (!StringUtils.isEmpty(uploadParam.getIncentiveSalary())) {
            BusinessLogicException.checkTrue(!PattenUtil.pattern(uploadParam.getIncentiveSalary(), MONEY_PATTERN_STR) || !BigDecimalUtil.range(new BigDecimal(uploadParam.getIncentiveSalary()), BigDecimal.ZERO, TEN_MILLION), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, uploadParam.getIncentiveSalary());
        }
        if (!StringUtils.isEmpty(uploadParam.getOtherDecreaseSalary())) {
            BusinessLogicException.checkTrue(!PattenUtil.pattern(uploadParam.getOtherDecreaseSalary(), MONEY_PATTERN_STR) || !BigDecimalUtil.range(new BigDecimal(uploadParam.getOtherDecreaseSalary()), BigDecimal.ZERO, TEN_MILLION), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, uploadParam.getOtherDecreaseSalary());
        }
        if (!StringUtils.isEmpty(uploadParam.getOtherIncreaseSalary())) {
            BusinessLogicException.checkTrue(!PattenUtil.pattern(uploadParam.getOtherIncreaseSalary(), MONEY_PATTERN_STR) || !BigDecimalUtil.range(new BigDecimal(uploadParam.getOtherIncreaseSalary()), BigDecimal.ZERO, TEN_MILLION), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, uploadParam.getOtherIncreaseSalary());
        }
        if (!StringUtils.isEmpty(uploadParam.getTaxation())) {
            BusinessLogicException.checkTrue(!PattenUtil.pattern(uploadParam.getTaxation(), MONEY_PATTERN_STR) || !BigDecimalUtil.range(new BigDecimal(uploadParam.getTaxation()), BigDecimal.ZERO, TEN_MILLION), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, uploadParam.getTaxation());
        }

        return userInfoDOList.get(0);
    }

    @Override
    public void update(SalaryEmployeeUploadParam updateParam) {
        //查询库中数据
        HrmsSalaryEmployeeUploadItemsDO uploadItemsDO = hrmsSalaryEmployeeUploadItemsDao.getById(updateParam.getId());
        //校验数据是否存在
        BusinessLogicException.checkTrue(uploadItemsDO == null || IsDeleteEnum.YES.getCode().equals(uploadItemsDO.getIsDelete()), HrmsErrorCodeEnums.DATA_NOT_EXITS.getCode(), HrmsErrorCodeEnums.DATA_NOT_EXITS.getDesc(), updateParam.getId());
        //日期不能更改
        BusinessLogicException.checkTrue(!uploadItemsDO.getSalaryDate().equals(updateParam.getSalaryDate()), HrmsErrorCodeEnums.DATA_NOT_EXITS.getCode(), HrmsErrorCodeEnums.DATA_NOT_EXITS.getDesc());
        //校验工号是否正确
        HrmsUserInfoDO userInfoDO = hrmsUserInfoDao.getByUserCode(updateParam.getUserCode());
        BusinessLogicException.checkTrue(ObjectUtil.isEmpty(userInfoDO) || !userInfoDO.getId().equals(uploadItemsDO.getUserId()), HrmsErrorCodeEnums.WORK_NO_NOT_EXISTS_ERROR.getCode(), HrmsErrorCodeEnums.WORK_NO_NOT_EXISTS_ERROR.getDesc(), updateParam.getUserCode());

        HrmsSalaryEmployeeUploadItemsDO model = BeanUtil.copyProperties(updateParam, uploadItemsDO.getClass());
        BaseDOUtil.fillDOUpdate(model);
        //落库
        hrmsSalaryEmployeeUploadItemsDao.updateById(model);
        //日志记录
        logRecord.diffObj(model, uploadItemsDO, OperationTypeEnum.SALARY_UPLOAD_ITEM_UPDATE.getCode());
    }


    @Override
    public PaginationResult<SalaryEmployeeUploadItemDTO> list(SalaryEmployeeUploadItemQuery query) {
        this.handlerQuery(query);
        systemResourceManage.setResource(query, SalaryEmployeeUploadItemQuery::setResourceType, SalaryEmployeeUploadItemQuery::setOrganizationIds);
        // 数据库查找记录
        PageInfo<SalaryEmployeeUploadItemDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> hrmsSalaryEmployeeUploadItemsMapper.list(query));
        List<SalaryEmployeeUploadItemDTO> list = pageInfo.getList();
        list.stream().forEach(item -> {
            item.setDeptName(RequestInfoHolder.isChinese() ? item.getDeptNameCn() : item.getDeptNameEn());
            item.setPostName(RequestInfoHolder.isChinese() ? item.getPostNameCn() : item.getPostNameEn());
            item.setUserName(RequestInfoHolder.isChinese() ? item.getUserNameCn() : item.getUserNameEn());
        });
        return getPageResult(pageInfo, query);
    }

    @Override
    public void delete(Long id) {
        //查询库中数据
        HrmsSalaryEmployeeUploadItemsDO deleteDO
                = hrmsSalaryEmployeeUploadItemsDao.getById(id);
        //校验数据是否存在
        BusinessLogicException.checkTrue(deleteDO == null || deleteDO.getIsDelete() == 1, HrmsErrorCodeEnums.DATA_NOT_EXITS.getCode(), HrmsErrorCodeEnums.DATA_NOT_EXITS.getDesc(), id);
        HrmsSalaryEmployeeUploadItemsDO model = BeanUtil.copyProperties(deleteDO, HrmsSalaryEmployeeUploadItemsDO.class);
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        fillDOUpdate(model);
        hrmsSalaryEmployeeUploadItemsDao.updateById(model);
        //日志记录
        logRecord.diffObj(model, deleteDO, OperationTypeEnum.SALARY_UPLOAD_ITEM_UPDATE.getCode());

    }

    /**
     * 处理查询类
     *
     * @param query
     */
    private void handlerQuery(SalaryEmployeeUploadItemQuery query) {
        if (!StringUtils.isEmpty(query.getUserNameOrEmail())) {
            query.setUserNameOrEmail(query.getUserNameOrEmail().trim());
        }
        this.handlerDeptId(query, SalaryEmployeeUploadItemQuery::getDeptId, SalaryEmployeeUploadItemQuery::setDeptIds).accept(query);
    }
}
