package com.imile.hrms.service.punch.vo.warehouse;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @project hrms
 * @description 证件OCR结果
 * @date 2024/6/29 19:37:34
 */
@Data
public class CertificatesVO {

    /**
     * 姓名
     */
    private String userName;

    /**
     * 1:男 2:女
     */
    private Integer sex;

    /**
     * 证件类型编码
     */
    private String certificateTypeCode;


    /**
     * 证件号
     */
    private String certificatesCode;

    /**
     * oss短链
     */
    private String url;

    /**
     * 失效日期
     */
    private LocalDate expireDate;

    /**
     * 用户id（用来判断是否用户已存在）
     */
    private Long userId;
}
