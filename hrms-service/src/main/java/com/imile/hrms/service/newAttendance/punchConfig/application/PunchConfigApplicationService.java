package com.imile.hrms.service.newAttendance.punchConfig.application;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.WorkStatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.common.enums.attendance.AttendanceTypeEnum;
import com.imile.hrms.common.enums.punch.PunchDayTypeEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchClassConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchConfigRangeDao;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigRangeDO;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchConfigQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveDetailDO;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.dao.user.query.UserLeaveDetailQuery;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDaoFacade;
import com.imile.hrms.manage.newAttendance.punchConfig.PunchConfigManage;
import com.imile.hrms.manage.user.HrmsUserLeaveDetailManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.newAttendance.common.AttendanceDeptService;
import com.imile.hrms.service.newAttendance.common.AttendanceUserService;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceDept;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceUser;
import com.imile.hrms.service.newAttendance.punchConfig.PunchService;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigAddOrUpdateCommand;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigAuthUpdateCommand;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchConfigStatusSwitchCommand;
import com.imile.hrms.service.newAttendance.punchConfig.dto.*;
import com.imile.hrms.service.newAttendance.punchConfig.factory.PunchConfigFactory;
import com.imile.hrms.service.newAttendance.punchConfig.mapstruct.PunchConfigMapstruct;
import com.imile.hrms.service.newAttendance.punchConfig.query.PunchClassExportQuery;
import com.imile.hrms.service.newAttendance.punchConfig.query.PunchConfigUserQuery;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.util.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/2/16
 * @Description
 */
@Slf4j
@Service
public class PunchConfigApplicationService extends BaseService {

    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private CalendarConfigDaoFacade calendarConfigDaoFacade;
    @Resource
    private PunchConfigFactory punchConfigFactory;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private PunchService punchService;

    //todo 注意
    @Resource
    private UserResourceService userResourceService;

    //todo 后续适配 hrmsUserLeaveDetailManage
    @Resource
    private HrmsUserLeaveDetailManage hrmsUserLeaveDetailManage;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;


    /**
     * 判断新增打卡规则是否为默认打卡规则
     */
    public PunchAndCalendarTemplateJudgeDTO punchTemplateJudge(String country) {
        PunchAndCalendarTemplateJudgeDTO punchTemplateJudgeDTO = PunchAndCalendarTemplateJudgeDTO.init();
        // 校验是否存在默认规则
        List<PunchConfigDO> list = punchConfigDao.listDefaultConfig(country);
        if (!list.isEmpty()) {
            punchTemplateJudgeDTO.setIsContainDefaultTemplate(BusinessConstant.Y);
        }
//        Integer result = hrmsAttendanceConfigDao.countDefaultAttendanceConfig(country);
        Integer result = calendarConfigDaoFacade.getCalendarConfigAdapter().countDefaultAttendanceConfig(country);
        if (result > 0) {
            punchTemplateJudgeDTO.setIsHaveDefaultAttendance(BusinessConstant.Y);
        }
        return punchTemplateJudgeDTO;
    }

    /**
     * 新增打卡规则
     */
    public List<PunchConfigRangeDTO> addPunchConfig(PunchConfigAddOrUpdateCommand addCommand) {
        return punchConfigFactory.addPunchConfig(addCommand);
    }

    /**
     * 打卡规则列表
     */
    public PaginationResult<PunchConfigDTO> listPunchList(PunchConfigQuery query) {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        // 添加人员查询对应考勤组
        handleUserPunchConfigQuery(query);

        // 查询分页数据
        PageInfo<PunchConfigDO> pageInfo = fetchPagedPunchConfigData(query);
        List<PunchConfigDO> punchConfigDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        // 获取所有考勤组ID
        List<Long> punchConfigIds = punchConfigDOList.stream()
                .map(PunchConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<PunchConfigRangeDO> punchConfigRangeDOAllList = punchConfigRangeDao.listPunchConfigRangesByPunchConfigIds(punchConfigIds);

        // 构建PunchConfigDTO列表
        List<PunchConfigDTO> attendanceConfigDTOS = buildPunchConfigDTOs(punchConfigDOList, punchConfigRangeDOAllList, userContext);
        return getPageResult(attendanceConfigDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 更新打卡规则
     */
    public List<PunchConfigRangeDTO> updatePunchConfig(PunchConfigAddOrUpdateCommand updateCommand) {
        return punchConfigFactory.updatePunchConfig(updateCommand);
    }

    /**
     * 更新打卡规则权限
     */
    public List<PunchConfigRangeDTO> updateAuthPunchConfig(PunchConfigAuthUpdateCommand authUpdateCommand) {
        return punchConfigFactory.updateAuthPunchConfig(authUpdateCommand);
    }

    /**
     * 关闭开启打卡规则
     */
    public List<PunchConfigRangeDTO> stateSwitch(PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return punchConfigFactory.stateSwitch(statusSwitchCommand);
    }

    /**
     * 规则详情
     */
    public PunchConfigDetailDTO detailPunchConfig(PunchConfigDetailQuery query) {
        return punchService.detailPunchConfig(query);
    }

    /**
     * 打卡规则下拉框
     */
    public List<PunchConfigSelectDTO> selectList(String country) {
        List<String> countryList = userResourceService.getAuthorizedBizCountryList();
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyList();
        }
        PunchConfigQuery query = new PunchConfigQuery();
        query.setCountryList(countryList);
        query.setCountry(country);
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.list(query);
        return PunchConfigMapstruct.INSTANCE.toPunchConfigSelectDTO(punchConfigDOList);
    }

    /**
     * 该打卡规则可以选择的排班类型
     */
    public List<PunchDayTypeDTO> selectDayTypeList(Long punchConfigId, Long userId) {
        // 查询班次信息
        List<PunchClassConfigDO> punchClassConfigDOS = punchClassConfigDao.listPunchClassConfigsByPunchConfigId(punchConfigId);

        // 转换班次信息为 PunchDayTypeDTO 列表
        List<PunchDayTypeDTO> punchDayTypeDTOList = punchClassConfigDOS.stream()
                .map(PunchDayTypeDTO::fromClassConfig)
                .collect(Collectors.toList());

        // 添加枚举值对应的打卡类型
        for (PunchDayTypeEnum value : PunchDayTypeEnum.values()) {
            punchDayTypeDTOList.add(PunchDayTypeDTO.fromEnum(value));
        }

        // 查询用户假期信息
        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        query.setStatus(StatusEnum.ACTIVE.getCode());
        query.setUserId(userId);

        List<HrmsUserLeaveDetailDO> userLeaveDetailDOList = hrmsUserLeaveDetailManage.selectUserLeaveDetail(query);

        // 转换用户假期为 PunchDayTypeDTO 并添加到列表中
        for (HrmsUserLeaveDetailDO hrmsUserLeaveDetailDO : userLeaveDetailDOList) {
            PunchDayTypeDTO leaveDayTypeDTO = PunchDayTypeDTO.fromLeaveType(hrmsUserLeaveDetailDO);
            if (leaveDayTypeDTO != null) {
                punchDayTypeDTOList.add(leaveDayTypeDTO);
            }
        }

        return punchDayTypeDTOList;
    }


    /**
     * 打卡规则导出
     */
    public PaginationResult<PunchConfigExportConfigDTO> export(PunchClassExportQuery query) {
        PageInfo<PunchConfigExportConfigDTO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
        List<PunchConfigExportConfigDTO> exportList = punchService.exportList(query);
        if (CollectionUtils.isEmpty(exportList)) {
            return getPageResult(pageInfoResult, query);
        }
        return getPageResult(exportList, query, (int) pageInfoResult.getTotal(), pageInfoResult.getPages());
    }

    public PaginationResult<PunchUserInfoDTO> selectPunchUserList(PunchConfigUserQuery query) {
        if (Objects.isNull(query.getPunchConfigId())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<PunchConfigRangeDO> punchConfigRangeDOAllList = punchConfigRangeDao.listPunchConfigRangesByPunchConfigId(query.getPunchConfigId());
        if (CollectionUtils.isEmpty(punchConfigRangeDOAllList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        Map<Long, List<PunchConfigRangeDO>> userRangeMap = punchConfigRangeDOAllList.stream()
                .collect(Collectors.groupingBy(PunchConfigRangeDO::getBizId));
        List<Long> userIds = new ArrayList<>(userRangeMap.keySet());
        //查询员工
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .deptIds(query.getDeptIds())
                .locationCountry(query.getLocationCountry())
                .codeOrNameLike(query.getUserCodeOrName())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDelete(IsDeleteEnum.NO.getCode()).build();
        if (Objects.isNull(query.getDeptId())) {
            userDaoQuery.setDeptId(null);
        }
        List<HrmsUserInfoDO> userList;
        Page<HrmsUserInfoDO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<HrmsUserInfoDO> pageInfo = page.doSelectPageInfo(() -> hrmsUserInfoDao.userList(userDaoQuery));
        userList = pageInfo.getList();
        List<PunchUserInfoDTO> userDTOList = transferUserDTO(userService.transferToUser(userList), userRangeMap);
        return getPageResult(userDTOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    private List<PunchUserInfoDTO> transferUserDTO(List<AttendanceUser> userList,
                                                   Map<Long, List<PunchConfigRangeDO>> userRangeMap) {
        List<Long> deptIds = userList.stream()
                .map(AttendanceUser::getDeptId)
                .filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> entDeptDOMap = deptService.listByDeptIds(deptIds)
                .stream().collect(Collectors.toMap(AttendanceDept::getId, item -> item, (oldVal, newVal) -> oldVal));
        List<PunchUserInfoDTO> userDTOList = BeanUtils.convert(PunchUserInfoDTO.class, userList);

        for (PunchUserInfoDTO punchUserInfoDTO : userDTOList) {
            if (Objects.nonNull(entDeptDOMap)) {
                AttendanceDept attendanceDept = entDeptDOMap.get(punchUserInfoDTO.getDeptId());
                if (Objects.nonNull(attendanceDept)) {
                    punchUserInfoDTO.setCountry(attendanceDept.getCountry());
                    punchUserInfoDTO.setDeptCode(attendanceDept.getDeptCode());
                    punchUserInfoDTO.setDeptName(BusinessFieldUtils.getUnifiedDeptName(attendanceDept.getDeptNameCn(), attendanceDept.getDeptNameEn()));
                }
            }

            if (Objects.nonNull(userRangeMap)) {
                List<PunchConfigRangeDO> userRangeList = userRangeMap.get(punchUserInfoDTO.getId());
                if (CollectionUtils.isNotEmpty(userRangeList)) {
                    punchUserInfoDTO.setCreateDate(userRangeList.get(0).getCreateDate());
                }
            }
        }
        return userDTOList;
    }


    /**
     * 添加人员查询对应考勤组
     */
    private void handleUserPunchConfigQuery(PunchConfigQuery query) {
        if (CollectionUtils.isEmpty(query.getUserIdList())) {
            return;
        }
        List<PunchConfigRangeDO> userRangeList = punchConfigManage.listUserPunchConfigRanges(query.getUserIdList());
        if (CollectionUtils.isEmpty(userRangeList)) {
            return;
        }
        List<Long> punchConfigIdList = userRangeList.stream()
                .map(PunchConfigRangeDO::getPunchConfigId)
                .collect(Collectors.toList());
        query.setIds(punchConfigIdList);
    }

    /**
     * 分页查询PunchConfig数据
     */
    private PageInfo<PunchConfigDO> fetchPagedPunchConfigData(PunchConfigQuery query) {
        return PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> punchConfigDao.list(query));
    }

    /**
     * 构建PunchConfigDTO列表
     */
    private List<PunchConfigDTO> buildPunchConfigDTOs(List<PunchConfigDO> punchConfigDOList,
                                                      List<PunchConfigRangeDO> punchConfigRangeDOAllList,
                                                      UserContext userContext) {
        List<PunchConfigDTO> dtoList = new ArrayList<>();
        for (PunchConfigDO punchConfigDO : punchConfigDOList) {
            PunchConfigDTO dto = PunchConfigMapstruct.INSTANCE.toPunchConfigDTO(punchConfigDO);
            setMasterAndPrincipleStatus(dto, punchConfigDO, userContext);
            setRangeAndUserInfo(dto, punchConfigDO, punchConfigRangeDOAllList);
            dto.setType(punchConfigDO.areDefault() ? AttendanceTypeEnum.DEFAULT.name() : AttendanceTypeEnum.CUSTOM.name());
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 设置主负责人和系统管理员标志
     */
    private void setMasterAndPrincipleStatus(PunchConfigDTO dto,
                                             PunchConfigDO punchConfigDO,
                                             UserContext userContext) {
        List<String> responsibleUsers = new ArrayList<>();
        if (StringUtils.isNotBlank(punchConfigDO.getSubUserCodes())) {
            responsibleUsers.addAll(Arrays.asList(punchConfigDO.getSubUserCodes().split(",")));
        }
        if (userContext.isSystem()) {
            dto.setIsMaster(true);
            dto.setIsPrinciple(true);
        }
        if (StringUtils.isNotBlank(punchConfigDO.getPrincipalUserCode())) {
            responsibleUsers.add(punchConfigDO.getPrincipalUserCode());
            if (punchConfigDO.getPrincipalUserCode().equals(RequestInfoHolder.getUserCode())) {
                dto.setIsMaster(true);
            }
        }
        if (responsibleUsers.contains(RequestInfoHolder.getUserCode())) {
            dto.setIsPrinciple(true);
        }
    }

    /**
     * 设置范围和员工信息
     */
    private void setRangeAndUserInfo(PunchConfigDTO dto,
                                     PunchConfigDO punchConfigDO,
                                     List<PunchConfigRangeDO> punchConfigRangeDOAllList) {
        List<PunchConfigRangeDTO> rangeRecords = new ArrayList<>();
        List<Long> userIds = extractBoundUserIds(punchConfigDO, punchConfigRangeDOAllList);
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
            dto.setEmployeeCount(activeUsers.size());
            //如果为默认打卡配置，不添加员工适用范围
            if (punchConfigDO.areCustom()) {
                rangeRecords.addAll(buildUserRangeRecords(activeUsers, punchConfigRangeDOAllList));
            }
        }
        rangeRecords.addAll(buildDeptRangeRecords(punchConfigDO));
        dto.setRangeRecords(rangeRecords);
    }

    /**
     * 提取绑定员工的ID列表
     */
    private List<Long> extractBoundUserIds(PunchConfigDO punchConfigDO,
                                           List<PunchConfigRangeDO> punchConfigRangeDOAllList) {
        return punchConfigRangeDOAllList.stream()
                .filter(o -> o.getPunchConfigId().equals(punchConfigDO.getId()))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 构建用户范围记录
     */
    private List<PunchConfigRangeDTO> buildUserRangeRecords(List<AttendanceUser> activeUsers,
                                                            List<PunchConfigRangeDO> punchConfigRangeDOAllList) {

        Set<Long> existingUserIds = punchConfigRangeDOAllList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRangeType(), RangeTypeEnum.USER.getCode()))
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        return activeUsers.stream()
                .filter(user -> existingUserIds.contains(user.getId()))
                .map(PunchConfigRangeDTO::buildUserRangeDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建部门范围记录
     */
    private List<PunchConfigRangeDTO> buildDeptRangeRecords(PunchConfigDO punchConfigDO) {
        if (punchConfigDO == null || StringUtils.isBlank(punchConfigDO.getDeptIds())) {
            return Collections.emptyList();
        }

        List<Long> deptIdList = punchConfigDO.listDeptIds();
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        return deptService.listByDeptIds(deptIdList).stream()
                .map(PunchConfigRangeDTO::buildDeptRangeDTO)
                .collect(Collectors.toList());
    }

}
