package com.imile.hrms.service.leave.param;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * {@code @author:} allen
 * {@code @className:} LegalLeaveConfigQueryDetailParam
 * {@code @since:} 2024-03-11 10:32
 * {@code @description:}
 */
@Data
@ApiModel(description = "国家法定假期记录表详情查询入参")
public class LegalLeaveConfigQueryDetailParam {

    /**
     * 常驻地国家
     */
    @ApiModelProperty(value="常驻地国家")
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private String locationCountry;

    /**
     * 年份
     */
    @ApiModelProperty(value="年份")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer year;

    /**
     * 日历id
     */
    @ApiModelProperty(value="日历id")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long attendanceConfigId;

}
