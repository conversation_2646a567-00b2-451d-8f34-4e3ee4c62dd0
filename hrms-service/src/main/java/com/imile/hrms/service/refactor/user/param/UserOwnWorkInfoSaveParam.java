package com.imile.hrms.service.refactor.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
public class UserOwnWorkInfoSaveParam {

    /**
     * 英文名
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    @Pattern(regexp = "^[a-zA-Z0-9]+([-_ .][a-zA-Z0-9]+)*$")
    private String userNameEn;

    /**
     * 用工类型
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String employeeType;

    /**
     * 项目编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String projectCode;

    /**
     * 汇报上级ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long leaderId;
}
