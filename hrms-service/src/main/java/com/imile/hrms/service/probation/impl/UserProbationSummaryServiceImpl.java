package com.imile.hrms.service.probation.impl;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.probation.dao.UserProbationSummaryDao;
import com.imile.hrms.dao.probation.model.HrmsUserProbationDO;
import com.imile.hrms.dao.probation.model.UserProbationSummaryDO;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.service.probation.AbstractProbationService;
import com.imile.hrms.service.probation.UserProbationSummaryService;
import com.imile.hrms.service.probation.bo.UserProbationSummaryBO;
import com.imile.hrms.service.probation.param.UserProbationSummarySubmitParam;
import com.imile.util.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_ATTACHMENT_EXCEED;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_SUMMARY_SUBMIT_NOT_MODIFY;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_TARGET_NOT_SUBMIT;
import static com.imile.hrms.common.enums.WechatTextMessageEnum.PROBATION_SUMMARY_SUBMIT_NOTICE;
import static com.imile.hrms.common.enums.WechatTextMessageEnum.PROBATION_SUMMARY_SUBMIT_NOTICE_LEADER;
import static com.imile.hrms.common.enums.probation.ProbationValidateCodeEnum.PROBATION_SUMMARY;

/**
 * <p>
 * 员工试用期总结
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class UserProbationSummaryServiceImpl extends AbstractProbationService<UserProbationSummaryDO> implements UserProbationSummaryService {

    @Resource
    private UserProbationSummaryDao userProbationSummaryDao;


    @Override
    public UserProbationSummaryBO detail(Long userProbationId) {
        // 白领转蓝领/本身是蓝领
        HrmsUserProbationDO userProbation = userProbationManage.findByProbationId(userProbationId);
        Long probationUserId = userProbation.getUserId();
        Long loginUserId = RequestInfoHolder.getUserId();
        if (whetherBlueCollar(probationUserId)) {
            return null;
        }
        UserProbationSummaryDO userProbationSummary = userProbationSummaryManage.findNullableByProbationId(userProbationId);
        if (Objects.isNull(userProbationSummary)) {
            return null;
        }
        UserProbationSummaryBO result = BeanUtils.convert(userProbationSummary, UserProbationSummaryBO.class);
        result.setDefenceFileList(AttachmentDTO.convert2List(userProbationSummary.getDefenceFiles()));
        return result;
    }

    @Override
    public Boolean save(UserProbationSummarySubmitParam param) {
        HrmsUserProbationDO probation = this.check(param.getUserProbationId(), param.getDefenceFileList(), param.getId());
        UserProbationSummaryDO updateSummary = BeanUtils.convert(param, UserProbationSummaryDO.class);
        updateSummary.setDefenceFiles(JSON.toJSONString(param.getDefenceFileList()));
        userProbationSummaryDao.saveOrUpdate(updateSummary);
        if (param.getIsSubmit().equals(1)) {
            // 发生企微通知
            HrmsUserInfoDO user = userManage.getUserById(probation.getUserId());
            super.sendThirdPartyWechat(
                    Collections.singletonList(probation.getBizMentorId()),
                    user, PROBATION_SUMMARY_SUBMIT_NOTICE);
            super.sendThirdPartyWechat(
                    Collections.singletonList(user.getLeaderId()),
                    user, PROBATION_SUMMARY_SUBMIT_NOTICE_LEADER);
        }
        return Boolean.TRUE;
    }

    private HrmsUserProbationDO check(Long userProbationId, List<AttachmentDTO> defenceFileList, Long id) {
        // 附件不能超过五个
        BusinessLogicException.checkTrue(defenceFileList.size() > 5, PROBATION_ATTACHMENT_EXCEED);
        // 只有白领员工需要填写（员工、挂靠且职级序列不为O）
        HrmsUserProbationDO userProbation = userProbationManage.findByProbationId(userProbationId);
        super.checkBlueCollar(userProbation.getUserId());
        super.checkProbationStatusOperate(userProbation.getProbationStatus(), userProbation.getProbationEndDate(), PROBATION_SUMMARY);
        if (Objects.nonNull(id)) {
            UserProbationSummaryDO dbSummary = userProbationSummaryManage.findById(id);
            // 试用期总结提交之后不能编辑
            BusinessLogicException.checkTrue(dbSummary.getIsSubmit().equals(1), PROBATION_SUMMARY_SUBMIT_NOT_MODIFY);
        } else {
            // 未提交试用期目标不能编辑周期总结
            BusinessLogicException.checkTrue(userProbation.getHasTargetSubmit().equals(0), PROBATION_TARGET_NOT_SUBMIT);

            // 如果已经存在试用期总结不能在新建（前端有bug会导致脏数据）
            UserProbationSummaryDO summary = userProbationSummaryManage.findNullableByProbationId(userProbationId);
            BusinessLogicException.checkTrue(Objects.nonNull(summary), PROBATION_SUMMARY_SUBMIT_NOT_MODIFY);
        }
        return userProbation;
    }
}
