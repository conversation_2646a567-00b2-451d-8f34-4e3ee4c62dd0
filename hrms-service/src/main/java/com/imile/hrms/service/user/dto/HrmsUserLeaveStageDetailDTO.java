package com.imile.hrms.service.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class HrmsUserLeaveStageDetailDTO implements Serializable {
    private static final long serialVersionUID = 2812954822142983238L;

    private Long id;

    /**
     * 用户假期id
     */
    private Long leaveId;

    /**
     * 阶段
     */
    private Integer stage;

    /**
     * 假期标记: 0：表示非结转，1：结转，...
     */
    private Integer leaveMark;

    /**
     * 是否失效: 0：表示未失效，1：失效，...
     */
    private Integer isInvalid;

    /**
     * 假期已使用天数
     */
    private BigDecimal leaveUsedDay;

    /**
     * 百分比日薪
     */
    private BigDecimal percentSalary;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 假期剩余分钟数据
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 假期已使用分钟数
     */
    private BigDecimal leaveUsedMinutes;

    /**
     * 假期总分钟数
     */
    private BigDecimal leaveTotalMinutes;

    /**
     * 发放日期
     */
    private String issueDate;

    /**
     * 失效日期
     */
    private String invalidDate;

    /**
     * 创建时间
     */
    private Date createDate;
}
