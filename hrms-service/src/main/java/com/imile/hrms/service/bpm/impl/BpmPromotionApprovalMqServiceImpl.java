package com.imile.hrms.service.bpm.impl;

import com.alibaba.fastjson.JSON;
import com.github.easylog.annotation.EasyLog;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.hrms.api.wechat.api.WechatSendTextApi;
import com.imile.hrms.api.wechat.query.WeChatTextParam;
import com.imile.hrms.common.enums.ApprovalStatusEnum;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormTypeEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.organization.DeptLeaderPropertyEnum;
import com.imile.hrms.common.enums.promotion.PromotionReviewResultEnum;
import com.imile.hrms.common.enums.promotion.PromotionStatusEnum;
import com.imile.hrms.dao.promotion.model.UserPromotionDO;
import com.imile.hrms.dao.promotion.model.UserPromotionReviewDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.organization.DeptBizLeaderManage;
import com.imile.hrms.manage.promotion.UserPromotionManage;
import com.imile.hrms.manage.promotion.UserPromotionReviewManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.mq.helper.OperatorHelper;
import com.imile.hrms.service.bpm.BpmPromotionApprovalMqService;
import com.imile.hrms.service.log.component.OperationFieldDiffer;
import com.imile.hrms.service.refactor.user.param.UserDifferParam;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.helper.UserHelper;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.BeanUtils;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.hrms.common.enums.WechatTextMessageEnum.USER_PROMOTION_EFFECTIVE_NOTICE_DEPT_HRM;

/**
 * 晋升审批回调处理
 *
 * <AUTHOR>
 * @since 2025/03/03
 */
@Service
@Slf4j
public class BpmPromotionApprovalMqServiceImpl implements BpmPromotionApprovalMqService {

    @Resource
    private UserPromotionManage userPromotionManage;
    @Resource
    private UserPromotionReviewManage userPromotionReviewManage;
    @Resource
    private UserManage userManage;
    @Resource
    private DeptBizLeaderManage deptBizLeaderManage;
    @Resource
    private UserService userService;
    @Resource
    private LogRecord logRecord;
    @Resource
    private WechatSendTextApi wechatSendTextApi;

    @Override
    @EasyLog
    public void hrPromotionMqHandler(ApprovalPushStatusMsgDTO approvalNotice) {
        if (Objects.isNull(approvalNotice)) {
            log.info("hrPromotionMqHandler approvalNotice is null");
            return;
        }
        log.info("hrPromotionMqHandler approvalNotice {}", JSON.toJSONString(approvalNotice));
        if (StringUtils.equalsIgnoreCase(approvalNotice.getApprovalPushStatusType(), "NODE")) {
            //只监听整个流程完成
            return;
        }
        ApprovalStatusEnum approvalStatusEnum = ApprovalStatusEnum.valueOfStatus(approvalNotice.getStatus());
        if (ApprovalStatusEnum.APPROVING.equals(approvalStatusEnum)) {
            return;
        }
        // 特殊晋升时为晋升ID 年度晋升时为批次ID
        String bizId = approvalNotice.getBizId();
        if (StringUtils.isBlank(bizId)) {
            return;
        }
        // 设置操作人
        HrmsUserInfoDO operator = getOperatorInfo(approvalNotice);
        log.info("last update user: {}", JSON.toJSONString(operator));
        OperatorHelper.setOperatorInfo(BeanUtils.convert(operator, UserInfoDTO.class));
        if (HrAttendanceApplicationFormTypeEnum.PROMOTION_SINGLE.getCode().equals(approvalNotice.getApprovalType())) {
            this.doBusiness4SpecialPromotion(approvalStatusEnum, bizId);
        } else if (HrAttendanceApplicationFormTypeEnum.PROMOTION_ANNUAL_APPLY.getCode().equals(approvalNotice.getApprovalType())) {
            this.doBusiness4AnnualPromotionApply(approvalStatusEnum, bizId);
        } else if (HrAttendanceApplicationFormTypeEnum.PROMOTION_ANNUAL_REVIEW.getCode().equals(approvalNotice.getApprovalType())) {
            this.doBusiness4AnnualPromotionReview(approvalStatusEnum, bizId);
        }
    }

    /**
     * copy from com.imile.hrms.service.bpm.impl.BpmJobApprovalMqServiceImpl#getUserInfoDO(com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO)
     */
    public HrmsUserInfoDO getOperatorInfo(ApprovalPushStatusMsgDTO approvalNotice) {
        String userCode = approvalNotice.getApplyUserCode();
        if (CollectionUtils.isNotEmpty(approvalNotice.getApprovalProcessInfoList())) {
            userCode = approvalNotice.getApprovalProcessInfoList().get(0).getApprovalUserCode();
        }
        return userManage.getUserByUserCode(userCode);
    }

    private void doBusiness4SpecialPromotion(ApprovalStatusEnum approvalStatusEnum, String bizId) {
        UserPromotionDO userPromotion = userPromotionManage.findById(Long.parseLong(bizId));
        Integer promotionStatus = this.mappingStatus4SpecialPromotion(approvalStatusEnum);
        // 组装晋升记录
        UserPromotionDO updatePromotion = new UserPromotionDO();
        updatePromotion.setId(Long.parseLong(bizId));
        updatePromotion.setPromotionStatus(promotionStatus);
        // 组装人员信息
        List<HrmsUserInfoDO> promotionUserList = null;
        if (PromotionStatusEnum.EFFECTIVE.getStatus().equals(promotionStatus)) {
            updatePromotion.setEffectDate(new Date());
            HrmsUserInfoDO promotionUser = this.buildPromotionUser(userPromotion);
            promotionUserList = Collections.singletonList(promotionUser);
        }
        log.info("promotion end mq handle result updatePromotion: {}, promotionUser: {}",
                JSON.toJSONString(updatePromotion), JSON.toJSONString(promotionUserList));
        Map<Long, HrmsUserInfoDO> originUserMap = userManage.getUserMapById(Collections.singletonList(userPromotion.getUserId()));
        userManage.doUserPromotionSave(promotionUserList, Collections.singletonList(updatePromotion));
        // 记录操作日志及发送人员基础信息变更通知
        this.doUserChangeLogAndNotice(promotionUserList, originUserMap);
        // 发送企微消息（部门的HRM）
        if (PromotionStatusEnum.EFFECTIVE.getStatus().equals(promotionStatus)) {
            HrmsUserInfoDO user = userManage.getUserById(userPromotion.getUserId());
            this.sendWechatMsg2DeptHrm(user.getDeptId());
        }
    }

    private void doBusiness4AnnualPromotionApply(ApprovalStatusEnum approvalStatusEnum, String bizId) {
        Long batchId = Long.parseLong(bizId);
        List<UserPromotionDO> userPromotionList = userPromotionManage.getUserPromotionOngoingListByBatchId(batchId);
        Integer promotionStatus = this.mappingStatus4AnnualPromotionApply(approvalStatusEnum);
        List<UserPromotionDO> updatePromotionList = userPromotionList.stream()
                .map(s -> {
                    UserPromotionDO entity = new UserPromotionDO();
                    entity.setId(s.getId());
                    entity.setPromotionStatus(promotionStatus);
                    return entity;
                })
                .collect(Collectors.toList());
        userPromotionManage.doAnnualBatchUpdate(updatePromotionList, null);
    }

    private void doBusiness4AnnualPromotionReview(ApprovalStatusEnum approvalStatusEnum, String bizId) {
        Long batchId = Long.parseLong(bizId);
        List<UserPromotionDO> userPromotionList = userPromotionManage.getUserPromotionOngoingListByBatchId(batchId);
        List<Long> userPromotionIdList = userPromotionList.stream()
                .map(UserPromotionDO::getId)
                .collect(Collectors.toList());
        List<UserPromotionReviewDO> userPromotionReviewList
                = userPromotionReviewManage.getUserPromotionReviewList(userPromotionIdList);
        Map<Long, UserPromotionReviewDO> userPromotionReviewMap = userPromotionReviewList.stream()
                .collect(Collectors.toMap(UserPromotionReviewDO::getUserPromotionId, Function.identity()));
        // 合并晋升记录及评议结果
        List<Pair<UserPromotionDO, UserPromotionReviewDO>> pairList = userPromotionList.stream()
                .map(s -> Pair.of(s, userPromotionReviewMap.get(s.getId())))
                .collect(Collectors.toList());
        List<UserPromotionDO> updatePromotionList = pairList.stream()
                // 过滤评议结果为空的晋升记录
                .filter(s -> Objects.nonNull(s.getRight()))
                .map(s -> {
                    UserPromotionReviewDO review = s.getRight();
                    Integer promotionStatus = this.mappingStatus4AnnualPromotionReview(approvalStatusEnum, review.getReviewResult());
                    UserPromotionDO entity = new UserPromotionDO();
                    entity.setId(s.getLeft().getId());
                    entity.setPromotionStatus(promotionStatus);
                    if (PromotionStatusEnum.EFFECTIVE.getStatus().equals(promotionStatus)) {
                        entity.setEffectDate(new Date());
                    }
                    return entity;
                })
                .collect(Collectors.toList());
        List<HrmsUserInfoDO> promotionUserList = pairList.stream()
                // 过滤评议结果为空的晋升记录
                .filter(s -> Objects.nonNull(s.getRight()))
                .filter(s -> PromotionReviewResultEnum.PASS.getKey().equals(s.getRight().getReviewResult()))
                .map(s -> this.buildPromotionUser(s.getLeft()))
                .collect(Collectors.toList());
        // 更新前查出原始人员信息
        List<Long> promotionUserIdList = promotionUserList.stream()
                .map(HrmsUserInfoDO::getId)
                .collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> originUserMap = userManage.getUserMapById(promotionUserIdList);
        userManage.doUserPromotionSave(promotionUserList, updatePromotionList);
        // 记录操作日志及发送人员基础信息变更通知
        this.doUserChangeLogAndNotice(promotionUserList, originUserMap);
    }

    private Integer mappingStatus4SpecialPromotion(ApprovalStatusEnum approvalStatusEnum) {
        // 审批状态 1审批中 2通过 -1审批终止 -2驳回 -3撤回
        Integer promotionStatus = null;
        switch (approvalStatusEnum) {
            case TERMINATE:
            case WITHDRAW:
                promotionStatus = PromotionStatusEnum.CANCELLED.getStatus();
                break;
            case REJECT:
                promotionStatus = PromotionStatusEnum.REJECTED.getStatus();
                break;
            case PASS:
                promotionStatus = PromotionStatusEnum.EFFECTIVE.getStatus();
            default:
        }
        return promotionStatus;
    }

    private Integer mappingStatus4AnnualPromotionApply(ApprovalStatusEnum approvalStatusEnum) {
        Integer promotionStatus = null;
        switch (approvalStatusEnum) {
            case TERMINATE:
            case WITHDRAW:
                promotionStatus = PromotionStatusEnum.CANCELLED.getStatus();
                break;
            case REJECT:
                promotionStatus = PromotionStatusEnum.REJECTED.getStatus();
                break;
            case PASS:
                promotionStatus = PromotionStatusEnum.PENDING_EVALUATION.getStatus();
            default:
        }
        return promotionStatus;
    }

    private Integer mappingStatus4AnnualPromotionReview(ApprovalStatusEnum approvalStatusEnum,
                                                        Integer reviewResult) {
        Integer promotionStatus = null;
        switch (approvalStatusEnum) {
            case TERMINATE:
            case WITHDRAW:
                promotionStatus = PromotionStatusEnum.CANCELLED.getStatus();
                break;
            case REJECT:
                promotionStatus = PromotionStatusEnum.REJECTED.getStatus();
                break;
            case PASS:
                promotionStatus = PromotionReviewResultEnum.PASS.getKey().equals(reviewResult)
                        ? PromotionStatusEnum.EFFECTIVE.getStatus()
                        : PromotionStatusEnum.CANCELLED.getStatus();
            default:
        }
        return promotionStatus;
    }

    private HrmsUserInfoDO buildPromotionUser(UserPromotionDO promotion) {
        HrmsUserInfoDO entity = new HrmsUserInfoDO();
        entity.setId(promotion.getUserId());
        entity.setPostId(promotion.getPromotionPostId());
        entity.setGradeId(promotion.getPromotionGradeId());
        // 晋升记录存储的职级和职等已加密 可直接使用
        entity.setGradeNo(promotion.getPromotionJobLevel());
        entity.setJobGrade(promotion.getPromotionJobGrade());
        return entity;
    }

    private void doUserChangeLogAndNotice(List<HrmsUserInfoDO> updateUserList, Map<Long, HrmsUserInfoDO> beforeUserMap) {
        if (CollectionUtils.isEmpty(updateUserList)) {
            return;
        }
        updateUserList.forEach(afterUser -> {
            HrmsUserInfoDO beforeUser = beforeUserMap.get(afterUser.getId());
            // 记录操作日志
            logRecord.diffObj(afterUser, beforeUser, OperationTypeEnum.USER_PROMOTION_UPDATE.getCode());
            List<OperationFieldDiffer> fieldDiffList = UserHelper.saveLog(UserDifferParam.builder()
                    .id(beforeUser.getId())
                    .operationCode(OperationCodeEnum.HRMS_PROMOTION_FINISH)
                    .beforeUser(beforeUser)
                    .afterUser(afterUser)
                    .build());
            // 人员基础信息变更通知
            userService.doUserBaseInfoChangeNotice(beforeUser, fieldDiffList);
        });
    }

    private void sendWechatMsg2DeptHrm(Long deptId) {
        if (Objects.isNull(deptId)) {
            return;
        }
        List<Long> hrmUserIdList = deptBizLeaderManage.getLeaderByDeptId(DeptLeaderPropertyEnum.HRM.getType(), deptId);
        if (CollectionUtils.isEmpty(hrmUserIdList)) {
            return;
        }
        List<HrmsUserInfoDO> hrmUserList = userManage.batchFindByUserIdList(hrmUserIdList);
        for (HrmsUserInfoDO user : hrmUserList) {
            WeChatTextParam chatTextParam = WeChatTextParam.builder()
                    .userCodes(Lists.newArrayList(user.getUserCode()))
                    .content(String.format(USER_PROMOTION_EFFECTIVE_NOTICE_DEPT_HRM.getDesc(), user.getUserName()))
                    .contentEn(String.format(USER_PROMOTION_EFFECTIVE_NOTICE_DEPT_HRM.getDescEn(), user.getUserName()))
                    .build();
            try {
                wechatSendTextApi.sendText(chatTextParam);
            } catch (Exception e) {
                log.info("promotion pass send dept HRM wechat text error cause: ", e);
            }
        }
    }
}
