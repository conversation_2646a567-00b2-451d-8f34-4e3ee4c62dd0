package com.imile.hrms.service.probation;

import com.alibaba.fastjson.JSON;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.google.common.base.Throwables;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.api.wechat.api.WechatSendTextApi;
import com.imile.hrms.api.wechat.query.WeChatTextParam;
import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.common.enums.WechatTextMessageEnum;
import com.imile.hrms.common.enums.probation.ProbationStatusEnum;
import com.imile.hrms.common.enums.probation.ProbationValidateCodeEnum;
import com.imile.hrms.common.enums.punch.BizTypeEnum;
import com.imile.hrms.common.enums.punch.PlatFormTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.organization.model.HrmsEntGradeDO;
import com.imile.hrms.dao.probation.model.HrmsUserProbationDO;
import com.imile.hrms.dao.probation.model.UserProbationBaseDO;
import com.imile.hrms.dao.probation.model.UserProbationSummaryDO;
import com.imile.hrms.dao.sys.model.HrmsPlatformRelationDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.helper.LoginEnvHelper;
import com.imile.hrms.manage.base.HrmsPlatformRelationManage;
import com.imile.hrms.manage.organization.GradeManage;
import com.imile.hrms.manage.probation.UserProbationManage;
import com.imile.hrms.manage.probation.UserProbationSummaryManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.probation.param.UserProbationBaseParam;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.imile.hrms.common.constants.BusinessConstant.DEFAULT_ID;
import static com.imile.hrms.common.constants.BusinessConstant.SEQUENCE_O;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_BLUE_COLLAR_CHECK;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_PROCESS_DATA_SUBMITTED;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_STATUS_CHECK;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_URGE_STATUS_CHECK;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PROBATION_WECHAT_TEXT_SEND_FAIL;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Service
@Slf4j
public abstract class AbstractProbationService<T extends UserProbationBaseDO> {


    @Resource
    protected UserProbationManage userProbationManage;
    @Resource
    protected UserProbationSummaryManage userProbationSummaryManage;
    @Resource
    protected HrmsPlatformRelationManage hrmsPlatformRelationManage;
    @Resource
    protected UserManage userManage;
    @Resource
    protected GradeManage gradeManage;
    @Resource
    protected WechatSendTextApi wechatSendTextApi;
    @Resource
    protected LoginEnvHelper loginEnvHelper;


    /**
     * 蓝领校验
     */
    protected void checkBlueCollar(Long userId) throws BusinessLogicException {
        BusinessLogicException.checkTrue(this.isBlueCollar(userId), PROBATION_BLUE_COLLAR_CHECK);
    }

    /**
     * 是否蓝领
     */
    public boolean whetherBlueCollar(Long userId) throws BusinessLogicException {
        return this.isBlueCollar(userId);
    }

    /**
     * 是否蓝领
     */
    protected boolean isBlueCollar(Long userId) throws BusinessLogicException {
        HrmsUserInfoDO user = userManage.getUserById(userId);
        if (Objects.nonNull(user.getGradeId()) && !user.getGradeId().equals(DEFAULT_ID)) {
            HrmsEntGradeDO grade = gradeManage.getGradeById(user.getGradeId());
            // 蓝领
            return Objects.nonNull(grade) && SEQUENCE_O.equals(grade.getRankSequence());
        }
        return Boolean.FALSE;
    }

    /**
     * 发送试用期企微文本消息
     */
    public void sendThirdPartyWechat(List<Long> userIdList, HrmsUserInfoDO user, WechatTextMessageEnum messageEnum) {
        Map<Long, HrmsUserInfoDO> userMapById = userManage.getUserMapById(userIdList);
        List<String> userCodeList = userMapById.values().stream()
                .map(HrmsUserInfoDO::getUserCode)
                .distinct()
                .collect(Collectors.toList());
        this.sendThirdPartyWechatByUserCode(userCodeList, user, messageEnum);
    }

    /**
     * 发送试用期企微文本消息
     */
    public void sendThirdPartyWechatByUserCode(List<String> userCodeList, HrmsUserInfoDO user, WechatTextMessageEnum messageEnum) {
        List<HrmsPlatformRelationDO> relationList = hrmsPlatformRelationManage.getPlatformRelationList(userCodeList,
                BizTypeEnum.USER.name(), PlatFormTypeEnum.WECHAT_WORK.name());
        if (CollectionUtils.isEmpty(relationList)) {
            log.info("sendThirdPartyWechat failed no wechat relation1, userCodeList:{}", JSON.toJSONString(userCodeList));
            return;
        }
        List<String> bizIdList = relationList.stream()
                .map(HrmsPlatformRelationDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        if (relationList.size() < userCodeList.size()) {
            List<String> difference = userCodeList.stream()
                    .filter(it -> !bizIdList.contains(it))
                    .collect(Collectors.toList());
            log.info("sendThirdPartyWechat failed no wechat relation2, userCodeList:{}", JSON.toJSONString(difference));
        }
        try {
            wechatSendTextApi.sendText(this.buildWeChatTextParam(bizIdList, user, messageEnum));
        } catch (Exception e) {
            log.info("probation send wechat text error cause: {}", Throwables.getStackTraceAsString(e));
        }
    }

    protected WeChatTextParam buildWeChatTextParam(List<String> userCodeList, HrmsUserInfoDO user, WechatTextMessageEnum messageEnum) {
        String cloverUrl = loginEnvHelper.getNullableCloverUrl();
        return WeChatTextParam.builder()
                .userCodes(userCodeList)
                .content(String.format(messageEnum.getDesc(), user.getUserName(), cloverUrl))
                .contentEn(String.format(messageEnum.getDescEn(), user.getUserName(), cloverUrl))
                .build();
    }

    /**
     * 发送试用期企微文本消息
     */
    public Boolean sendOneselfWechat(Long userProbationId, String urgeType) {
        WechatTextMessageEnum wechatTextMessageEnum = WechatTextMessageEnum.getInstance(urgeType);
        // 对应状态的校验(试用期目标未提交、周期总结未提交、试用期总结未提交)
        this.urgeCheck(userProbationId, wechatTextMessageEnum);
        Long userId = userProbationManage.findUserIdByProbationId(userProbationId);
        HrmsUserInfoDO user = userManage.getUserById(userId);
        String userName = user.getUserName();
        List<String> userCodeList = Lists.newArrayList(user.getUserCode());
        // 判断当前人员是否存在企微的关联关系
        List<HrmsPlatformRelationDO> relationList = hrmsPlatformRelationManage.getPlatformRelationList(userCodeList,
                BizTypeEnum.USER.name(), PlatFormTypeEnum.WECHAT_WORK.name());
        if (CollectionUtils.isEmpty(relationList)) {
            log.info("urge failed because no wechat relation, userId: {}, userName: {}", userId, userName);
            throw BusinessException.ofI18nCode(PROBATION_WECHAT_TEXT_SEND_FAIL.getCode(), PROBATION_WECHAT_TEXT_SEND_FAIL.getDesc());
        }
        String cloverUrl = loginEnvHelper.getNullableCloverUrl();
        // 发送催办信息(催办)
        WeChatTextParam chatTextParam = WeChatTextParam.builder()
                .userCodes(userCodeList)
                .content(String.format(wechatTextMessageEnum.getDesc(), userName, cloverUrl))
                .contentEn(String.format(wechatTextMessageEnum.getDescEn(), userName, cloverUrl))
                .build();
        try {
            // 测试的时候有可能会有脏数据报错
            RpcResult<Boolean> messageResult = wechatSendTextApi.sendText(chatTextParam);
            return messageResult.getResult();
        } catch (Exception e) {
            log.info("probation send wechat urge text error cause: {}", Throwables.getStackTraceAsString(e));
            throw BusinessException.ofI18nCode(PROBATION_WECHAT_TEXT_SEND_FAIL.getCode(), PROBATION_WECHAT_TEXT_SEND_FAIL.getDesc());
        }
    }

    protected void urgeCheck(Long userProbationId, WechatTextMessageEnum wechatTextMessageEnum) {
        // 催办校验（待制定目标、试用期考核中、待配置答辩、待反馈结果）
        HrmsUserProbationDO userProbation = userProbationManage.findByProbationId(userProbationId);
        Integer probationStatus = userProbation.getProbationStatus();
        switch (wechatTextMessageEnum) {
            case PROBATION_TARGET_URGE:
            case CLOVER_PROBATION_TARGET_URGE: {
                // 员工未提交试用期目标时,可以点击催办,给员工发送企微通知
                BusinessLogicException.checkTrue(userProbation.getHasTargetSubmit().equals(1), PROBATION_PROCESS_DATA_SUBMITTED);

                //待制定目标、试用期考核中、待配置答辩、待反馈结果（是不是得先判断试用期目标未提交）
                BusinessLogicException.checkTrue(!ProbationStatusEnum.canTargetUrge(probationStatus),
                        PROBATION_URGE_STATUS_CHECK, ProbationStatusEnum.getDesc(probationStatus));
                break;
            }
            case PROBATION_CYCLE_SUMMARY_URGE:
            case CLOVER_PROBATION_CYCLE_SUMMARY_URGE:
                //试用期考核中、待配置答辩、待反馈结果可催办
                BusinessLogicException.checkTrue(!ProbationStatusEnum.canCycleUrge(probationStatus),
                        PROBATION_URGE_STATUS_CHECK, ProbationStatusEnum.getDesc(probationStatus));
                break;
            case PROBATION_SUMMARY_URGE:
            case CLOVER_PROBATION_SUMMARY_URGE: {
                // 员工未提交试用期总结时,可以点击催办,给员工发送企微通知
                UserProbationSummaryDO summary = userProbationSummaryManage.findNullableByProbationId(userProbationId);
                BusinessLogicException.checkTrue(
                        Objects.nonNull(summary) && summary.getIsSubmit().equals(1), PROBATION_PROCESS_DATA_SUBMITTED);
                //待配置答辩、待反馈结果可催办（是不是得先判断周期总结未提交）
                BusinessLogicException.checkTrue(!ProbationStatusEnum.canSummaryUrge(probationStatus),
                        PROBATION_URGE_STATUS_CHECK, ProbationStatusEnum.getDesc(probationStatus));
            }
        }
    }

    /**
     * 试用期状态与操作校验
     */
    protected void checkProbationStatusOperate(Integer probationStatus, Date probationEndDate,
                                               ProbationValidateCodeEnum operateCode) throws BusinessLogicException {
        // 待配置试用期、已转正、转正失败（过了试用期到期日）不能编辑试用期目标
        boolean canUpdate = this.updateCheck(probationStatus, probationEndDate, operateCode);
        BusinessLogicException.checkTrue(canUpdate, PROBATION_STATUS_CHECK);
    }

    protected DataDifferHolder<T> differ(Long userProbationId,
                                         List<? extends UserProbationBaseParam> paramList,
                                         Integer isSubmit, Class<T> clazz) {
        // 由于需要兼容完全清空的场景，这里只能判断paramList是否为null，不能判断是否空列表
        if (Objects.isNull(paramList)) {
            return DataDifferHolder.emptyHolder();
        }
        List<T> existList = this.getDBList(userProbationId);
        List<Long> existIdList = existList.stream()
                .map(UserProbationBaseDO::getId)
                .collect(Collectors.toList());
        List<Long> updateIdList = paramList.stream()
                .map(UserProbationBaseParam::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());


        // 传参中的 id 为空则代表新增
        @SuppressWarnings("ConstantConditions")
        List<T> insertList = paramList.stream()
                .filter(item -> Objects.nonNull(item) && Objects.isNull(item.getId()))
                .map(item -> {
                    T record = BeanUtils.convert(item, clazz);
                    record.setUserProbationId(userProbationId);
                    record.setIsSubmit(isSubmit);
                    return record;
                })
                .collect(Collectors.toList());

        // 传参中的 id 在存量数据中存在则代表更新
        List<T> updateList = paramList.stream()
                .filter(item -> existIdList.contains(item.getId()))
                .map(item -> {
                    T record = BeanUtils.convert(item, clazz);
                    record.setUserProbationId(userProbationId);
                    record.setIsSubmit(isSubmit);
                    return record;
                })
                .collect(Collectors.toList());

        // 存量数据的 id 不在传参中则代表需删除
        List<T> deleteList = existList.stream()
                .filter(item -> !updateIdList.contains(item.getId()))
                .map(item -> {
                    T record;
                    try {
                        record = clazz.newInstance();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    record.setId(item.getId());
                    record.setIsDelete(IsDeleteEnum.YES.getCode());
                    return record;
                })
                .collect(Collectors.toList());
        return DataDifferHolder.buildHolder(insertList, updateList, deleteList);
    }

    protected List<T> getDBList(Long userProbationId) {
        return Lists.newArrayList();
    }

    /**
     * 判断逻辑在prd上 <a href="https://modao.cc/proto/2hFYGrdqrra703kQ9A6rcl/sharing?view_mode=read_only">...</a>
     */
    private boolean updateCheck(Integer probationStatus, Date probationEndDate, ProbationValidateCodeEnum operateCode) {
        if (ProbationValidateCodeEnum.GROWTH_PLAN.equals(operateCode)) {
            // 成长计划校验（待配置试用期、待制定目标、待转正、已转正、 转正失败）
            return !(ProbationStatusEnum.WAIT_SUBMIT_TARGET.getStatus().equals(probationStatus)
                    || ProbationStatusEnum.PROBATION_ASSESSMENT.getStatus().equals(probationStatus)
                    || ProbationStatusEnum.WAIT_CONFIG_DEFENSE.getStatus().equals(probationStatus)
                    || ProbationStatusEnum.WAIT_FEEDBACK_RESULT.getStatus().equals(probationStatus));
        }
        // 待配置试用期、已转正、 转正失败且过了试用期到期日
        return ProbationStatusEnum.WAIT_CONFIG.getStatus().equals(probationStatus)
                || ProbationStatusEnum.REGULAR.getStatus().equals(probationStatus)
                || (ProbationStatusEnum.REGULAR_FAIL.getStatus().equals(probationStatus)
                && Objects.nonNull(probationEndDate) && probationEndDate.before(new Date()));
    }

}
