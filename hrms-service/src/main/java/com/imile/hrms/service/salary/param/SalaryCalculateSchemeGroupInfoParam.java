package com.imile.hrms.service.salary.param;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/27 16:03
 * @version: 1.0
 */
@Data
public class SalaryCalculateSchemeGroupInfoParam {

    /**
     * 计薪方案薪资项目组名称
     */
    private String itemGroupName;

    /**
     * 计薪方案薪资项目组序号
     */
    private Integer itemGroupSort;

    /**
     * 数据来源(系统生成/用户自定义添加)
     */
    private String dataSource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计薪方案薪资项目组中具体薪资项值
     */
    private List<SalaryCalculateTaskSchemeEditPram> salarySchemeItemInfoList;
}
