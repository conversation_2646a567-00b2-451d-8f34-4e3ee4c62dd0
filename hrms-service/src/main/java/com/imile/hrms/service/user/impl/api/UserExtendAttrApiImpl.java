/*
package com.imile.hrms.service.user.impl.api;

import com.imile.hrms.api.user.api.UserExtendAttrApi;
import com.imile.hrms.api.user.dto.UserExtendAttrInfoDTO;
import com.imile.hrms.api.user.param.UserExtendAttrParam;
import com.imile.hrms.dao.user.dto.UserExtendAttrDTO;
import com.imile.hrms.dao.user.model.UserExtendAttrDO;
import com.imile.hrms.dao.user.query.UserExtendAttrQuery;
import com.imile.hrms.manage.user.UserExtendAttrManage;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

*/
/**
 * <AUTHOR>
 * @since 2025/4/10
 *//*

@Service(version = "1.0.0")
@Slf4j
public class UserExtendAttrApiImpl implements UserExtendAttrApi {

    @Resource
    private UserExtendAttrManage userExtendAttrManage;

    @Override
    public List<UserExtendAttrInfoDTO> selectByAttrKeyCondition(UserExtendAttrParam param) {
        UserExtendAttrQuery query = BeanUtils.convert(param, UserExtendAttrQuery.class);
        List<UserExtendAttrDTO> userExtendAttrDTOList = userExtendAttrManage.selectByAttrKeyCondition(query);
        if (CollectionUtils.isEmpty(userExtendAttrDTOList)) {
            return Collections.emptyList();
        }
        return BeanUtils.convert(UserExtendAttrInfoDTO.class, userExtendAttrDTOList);
    }

    @Override
    public List<UserExtendAttrInfoDTO> getUserExtendAttrList(List<Long> userIdList, List<String> attrKeyList) {
        List<UserExtendAttrDO> userExtendAttrDOList = userExtendAttrManage.getUserExtendAttrList(userIdList, attrKeyList);
        if (CollectionUtils.isEmpty(userExtendAttrDOList)) {
            return Collections.emptyList();
        }
        return BeanUtils.convert(UserExtendAttrInfoDTO.class, userExtendAttrDOList);
    }
}
*/
