package com.imile.hrms.service.user.result;

import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserEntryWorkInfoBO {

    /**
     * 是否司机（0:否 1:是）
     */
    private Integer isDriver;

    /**
     * 是否是DTL（0:否 1:是）
     */
    private Integer isDtl;

    /**
     * 所属部门ID
     */
    @RelationObjectValue(beanId = "hrmsDeptNewManageImpl", fieldId = "deptName", extendFieldIdList = "deptNamePath",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 部门链
     */
    private String deptNamePath;

    /**
     * 所属网点编码
     */
    private String ocCode;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 所属供应商编码
     */
    private String vendorCode;

    /**
     * 岗位ID
     */
    private Long postId;

    /**
     * 汇报上级ID
     */
    private Long leaderId;

    /**
     * 职级序列ID
     */
    @RelationObjectValue(beanId = "gradeManageImpl", fieldId = "jobSequence")
    private Long gradeId;

    /**
     * 职级序列
     */
    private String jobSequence;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 职等
     */
    private String jobGrade;

    /**
     * 试用期月数
     */
    private Integer probationMonths;

    /**
     * 业务节点ID
     */
    private Long bizModelId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;
}
