package com.imile.hrms.service.driver.attendance.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailExportParam
 * {@code @since:} 2024-01-24 18:56
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DriverAttendanceDetailExportParam extends ResourceQuery {
    /**
     * 国家
     */
    @ApiModelProperty(value="国家")
    private String country;

    /**
     * day_id 示例：20240124
     */
    @ApiModelProperty(value="day_id 示例：20240124")
    private Long dayId;

    /**
     * dayDate 示例：2024-01-24
     * 司机日报查询条件
     */
    @ApiModelProperty(value="dayDate 示例：2024-01-24")
    private String dayDate;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    @ApiModelProperty(value="出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...")
    private String attendanceTypeList;

    /**
     * 用工类型
     */
    @ApiModelProperty(value="用工类型")
    private String employeeTypeList;

    /**
     * 账号状态
     */
    @ApiModelProperty(value="账号状态")
    private String status;

    /**
     * 工作状态集合
     */
    @ApiModelProperty(value="工作状态")
    private String workStatusList;

    /**
     * 供应商code
     */
    @ApiModelProperty(value="供应商code")
    private String vendorCode;

    /**
     * 账号/姓名
     */
    @ApiModelProperty(value="账号/姓名")
    private String userCodeOrName;

    /**
     * 部门id
     */
    @ApiModelProperty(value="部门id")
    private String deptIdList;

    /**
     * 汇报上级
     */
    @ApiModelProperty(value="汇报上级")
    private Long leaderId;
}
