package com.imile.hrms.service.vehicle.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hermes.enterprise.dto.EntOcSlimpleApiDTO;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.common.util.SplitUtil;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.enums.DeptOrgTypeEnum;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.vehicle.model.HrmsVehicleFuelWhiteDriverDO;
import com.imile.hrms.dao.vehicle.query.WarningConfigQuery;
import com.imile.hrms.dao.vehicle.query.WhiteDriverQuery;
import com.imile.hrms.integration.hermes.service.EntEmployeeService;
import com.imile.hrms.integration.hermes.service.UserAuthorizeService;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.manage.vehicle.HrmsVehicleFuelWhiteDriverManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import com.imile.hrms.service.vehicle.HrmsVehicleFuelWhiteDriverService;
import com.imile.hrms.service.vehicle.dto.*;
import com.imile.prism.api.hrms.dto.WareHouseOfdApiDTO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-27
 * @version: 1.0
 */
@Slf4j
@Service
public class HrmsVehicleFuelWhiteDriverServiceImpl extends BaseService implements HrmsVehicleFuelWhiteDriverService {
    @Autowired
    private EntEmployeeService entEmployeeService;
    @Autowired
    private UserAuthorizeService userAuthorizeService;
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsVehicleFuelWhiteDriverManage hrmsVehicleFuelWhiteDriverManage;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Override
    public List<AuthWhiteDriverDTO> selectAuthWhiteDriver() {
        WhiteDriverQuery query = new WhiteDriverQuery();
        vehicleFuelRecordQueryBuild(query, new WhiteDriverQueryDTO());
        List<HrmsEntDeptDO> deptDOList = hrmsEntDeptDao.listDeptForTree(query.getDeptIdList());
        Map<Long, List<HrmsEntDeptDO>> deptMap = deptDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));
        //仅仅选择司机
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectByDeptId(query.getDeptIdList()).stream()
                .filter(item ->  item.getOriginCountry() != null && query.getCountryList().contains(item.getOriginCountry()) && StringUtils.isNotBlank(item.getUserCode()) && item.getIsDriver() != null && item.getIsDriver().equals(1))
                .collect(Collectors.toList());
        List<AuthWhiteDriverDTO> resultList = new ArrayList<>();
        for (HrmsUserInfoDO hrmsUserInfoDO : userInfoDOList) {
            List<HrmsEntDeptDO> existDeptList = deptMap.get(hrmsUserInfoDO.getDeptId());
            if (CollectionUtils.isEmpty(existDeptList)) {
                continue;
            }
            AuthWhiteDriverDTO authWhiteDriverDTO = new AuthWhiteDriverDTO();
            authWhiteDriverDTO.setUserId(hrmsUserInfoDO.getId());
            authWhiteDriverDTO.setUserCode(hrmsUserInfoDO.getUserCode());
            authWhiteDriverDTO.setUserName(hrmsUserInfoDO.getUserName());
            authWhiteDriverDTO.setSysAccountName(hrmsUserInfoDO.getSysAccountName());
            authWhiteDriverDTO.setDeptId(existDeptList.get(0).getId());
            authWhiteDriverDTO.setStation(RequestInfoHolder.isChinese() ? existDeptList.get(0).getDeptNameCn() : existDeptList.get(0).getDeptNameEn());
            resultList.add(authWhiteDriverDTO);
        }
        return resultList;
    }
    @Resource
    private UserResourceService userResourceService;


    @Override
    public PaginationResult<WhiteDriverDTO> selectWhiteDriver(WhiteDriverQueryDTO queryDTO) {
        WhiteDriverQuery query = BeanUtils.convert(queryDTO, WhiteDriverQuery.class);
        vehicleFuelRecordQueryBuild(query, queryDTO);
        PermissionDeptVO permissionDept = userResourceService.getPermissionDept(query.getDeptIdList());
        if (!permissionDept.getHasDeptPermission()) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setDeptIdList(permissionDept.getDeptIdList());
        Page<WhiteDriverDTO> page = PageHelper.startPage(queryDTO.getCurrentPage(), queryDTO.getShowCount(), queryDTO.getShowCount() > 0);
        PageInfo<HrmsVehicleFuelWhiteDriverDO> pageInfo = page.doSelectPageInfo(() -> hrmsVehicleFuelWhiteDriverManage.selectWhiteDriver(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            PageInfo<WhiteDriverDTO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return getPageResult(pageInfoResult, queryDTO);
        }
        List<Long> deptIdList = pageInfo.getList().stream().map(HrmsVehicleFuelWhiteDriverDO::getDeptId).collect(Collectors.toList());
        List<HrmsEntDeptDO> entDeptDOList = hrmsEntDeptDao.listByIds(deptIdList);
        Map<Long, List<HrmsEntDeptDO>> entDeptMaps = entDeptDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));
        List<WhiteDriverDTO> resultList = new ArrayList<>();
        for (HrmsVehicleFuelWhiteDriverDO recordDO : pageInfo.getList()) {
            WhiteDriverDTO recordDTO = BeanUtils.convert(recordDO, WhiteDriverDTO.class);
            List<HrmsEntDeptDO> existEntDeptList = entDeptMaps.get(recordDO.getDeptId());
            if (CollectionUtils.isNotEmpty(existEntDeptList)) {
                recordDTO.setStation(RequestInfoHolder.isChinese() ? existEntDeptList.get(0).getDeptNameCn() : existEntDeptList.get(0).getDeptNameEn());
            }
            resultList.add(recordDTO);
        }
        PageInfo<WhiteDriverDTO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(resultList);
        return getPageResult(pageInfoResult, queryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateWhiteDriver(WhiteDriverUpdateDTO dto) {
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoManage.getUserInfoById(dto.getUserId());
        if (hrmsUserInfoDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        WhiteDriverQuery query = new WhiteDriverQuery();
        query.setUserId(dto.getUserId());
        query.setDeptIdList(Arrays.asList(dto.getDeptId()));
        //根据结束时间倒序
        List<HrmsVehicleFuelWhiteDriverDO> whiteDriverDOList = hrmsVehicleFuelWhiteDriverManage.selectWhiteDriver(query)
                .stream().sorted(Comparator.comparing(HrmsVehicleFuelWhiteDriverDO::getEndDate).reversed()).collect(Collectors.toList());
        if (dto.getOperationType().equals(0)) {
            //新增操作
            //和所有单据有交集就报错
            for (HrmsVehicleFuelWhiteDriverDO whiteDriverDO : whiteDriverDOList) {
                if (DateUtil.compare(dto.getStartDate(), whiteDriverDO.getStartDate()) < 1
                        && DateUtil.compare(dto.getEndDate(), whiteDriverDO.getStartDate()) > -1) {
                    throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getDesc()));
                }
                if (DateUtil.compare(dto.getStartDate(), whiteDriverDO.getEndDate()) < 1
                        && DateUtil.compare(dto.getEndDate(), whiteDriverDO.getEndDate()) > -1) {
                    throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getDesc()));
                }
            }
            HrmsVehicleFuelWhiteDriverDO addDO = new HrmsVehicleFuelWhiteDriverDO();
            addWhiteDriverBuild(addDO, dto, hrmsUserInfoDO);
            hrmsVehicleFuelWhiteDriverManage.addWhiteDriver(addDO);
            return;
        }
        //更新操作
        if (dto.getId() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.WHITE_DRIVER_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.WHITE_DRIVER_RECORD_NOT_EXIST.getDesc()));
        }
        WhiteDriverQuery oldQuery = new WhiteDriverQuery();
        oldQuery.setId(dto.getId());
        List<HrmsVehicleFuelWhiteDriverDO> oldRecordList = hrmsVehicleFuelWhiteDriverManage.selectWhiteDriver(oldQuery);
        if (CollectionUtils.isEmpty(oldRecordList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.WHITE_DRIVER_RECORD_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.WHITE_DRIVER_RECORD_NOT_EXIST.getDesc()));
        }
        //排除旧的记录，在校验更新后的时间是否和存在记录有交集
        for (HrmsVehicleFuelWhiteDriverDO whiteDriverDO : whiteDriverDOList) {
            if (whiteDriverDO.getId().compareTo(dto.getId()) == 0) {
                continue;
            }
            if (DateUtil.compare(dto.getStartDate(), whiteDriverDO.getStartDate()) < 1
                    && DateUtil.compare(dto.getEndDate(), whiteDriverDO.getStartDate()) > -1) {
                throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getDesc()));
            }
            if (DateUtil.compare(dto.getStartDate(), whiteDriverDO.getEndDate()) < 1
                    && DateUtil.compare(dto.getEndDate(), whiteDriverDO.getEndDate()) > -1) {
                throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DRIVER_EXIST_AND_ACTIVE.getDesc()));
            }
        }
        HrmsVehicleFuelWhiteDriverDO oldWhiteDriverDO = oldRecordList.get(0);
        oldWhiteDriverDO.setStartDate(dto.getStartDate());
        oldWhiteDriverDO.setEndDate(dto.getEndDate());
        oldWhiteDriverDO.setRemark(dto.getRemark());
        BaseDOUtil.fillDOUpdate(oldWhiteDriverDO);
        hrmsVehicleFuelWhiteDriverManage.updateWhiteDriver(oldWhiteDriverDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WhiteDriverImportDTO> whiteDriverImport(List<WhiteDriverImportDTO> importList) {
        List<WhiteDriverImportDTO> failImportList = new ArrayList<>();
        List<String> userCodeList = importList.stream().filter(item -> StringUtils.isNotBlank(item.getUserCode())).map(WhiteDriverImportDTO::getUserCode).collect(Collectors.toList());
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        Map<String, List<HrmsUserInfoDO>> userInfoMaps = userInfoDOList.stream().collect(Collectors.groupingBy(HrmsUserInfoDO::getUserCode));
        //数据校验
        List<WhiteDriverImportDTO> filterImportDTOList = importDataBasicCheck(userInfoMaps, importList, failImportList);
        if (CollectionUtils.isEmpty(filterImportDTOList)) {
            return failImportList;
        }
        List<WhiteDriverImportDTO> finalImportDTOList = new ArrayList<>();
        Map<Long, List<WhiteDriverImportDTO>> importMaps = filterImportDTOList.stream().collect(Collectors.groupingBy(WhiteDriverImportDTO::getUserId));
        for (Map.Entry<Long, List<WhiteDriverImportDTO>> entry : importMaps.entrySet()) {
            List<WhiteDriverImportDTO> sameImportList = entry.getValue();
            if (sameImportList.size() != 1) {
                //不能一次导入某个司机多条数据
                for (WhiteDriverImportDTO importDTO : sameImportList) {
                    IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该司机不允许导入多条记录" : "driver not import more record");
                    failImportList.add(importDTO);
                }
                continue;
            }
            finalImportDTOList.addAll(sameImportList);
        }
        if (CollectionUtils.isEmpty(finalImportDTOList)) {
            return failImportList;
        }

        List<Long> userIdList = finalImportDTOList.stream().map(WhiteDriverImportDTO::getUserId).collect(Collectors.toList());
        WhiteDriverQuery query = new WhiteDriverQuery();
        query.setUserIdList(userIdList);
        List<HrmsVehicleFuelWhiteDriverDO> whiteDriverDOList = hrmsVehicleFuelWhiteDriverManage.selectWhiteDriver(query);
        Map<String, List<HrmsVehicleFuelWhiteDriverDO>> whiteDriverMaps = whiteDriverDOList.stream().collect(Collectors.groupingBy(item -> item.getUserId() + "$" + item.getDeptId()));
        List<HrmsVehicleFuelWhiteDriverDO> addList = new ArrayList<>();
        for (WhiteDriverImportDTO importDTO : finalImportDTOList) {
            List<HrmsVehicleFuelWhiteDriverDO> vehicleFuelWhiteDriverDOList = whiteDriverMaps.get(importDTO.getUserId() + "$" + importDTO.getDeptId());
            boolean temp = false;
            if (CollectionUtils.isNotEmpty(vehicleFuelWhiteDriverDOList)) {
                //存在交集就报错
                for (HrmsVehicleFuelWhiteDriverDO whiteDriverDO : vehicleFuelWhiteDriverDOList) {
                    if (DateUtil.compare(importDTO.getStartDate(), whiteDriverDO.getStartDate()) < 1
                            && DateUtil.compare(importDTO.getEndDate(), whiteDriverDO.getStartDate()) > -1) {
                        IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "司机已存在白名单且生效中" : "driver exist and active");
                        failImportList.add(importDTO);
                        temp = true;
                        break;
                    }
                    if (DateUtil.compare(importDTO.getStartDate(), whiteDriverDO.getEndDate()) < 1
                            && DateUtil.compare(importDTO.getEndDate(), whiteDriverDO.getEndDate()) > -1) {
                        IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "司机已存在白名单且生效中" : "driver exist and active");
                        failImportList.add(importDTO);
                        temp = true;
                        break;
                    }
                }
                if (temp) {
                    continue;
                }
            }

            //直接 新增
            HrmsVehicleFuelWhiteDriverDO addDO = new HrmsVehicleFuelWhiteDriverDO();
            addDO.setCountry(importDTO.getCountry());
            addDO.setDeptId(importDTO.getDeptId());
            addDO.setUserId(importDTO.getUserId());
            addDO.setUserCode(importDTO.getUserCode());
            addDO.setUserName(importDTO.getReallyUserName());
            addDO.setSysAccountName(importDTO.getSysAccountName());
            addDO.setStartDate(importDTO.getStartDate());
            addDO.setEndDate(importDTO.getEndDate());
            addDO.setRemark(importDTO.getRemark());
            BaseDOUtil.fillDOInsert(addDO);
            addList.add(addDO);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            hrmsVehicleFuelWhiteDriverManage.batchAddWhiteDriver(addList);
        }
        return failImportList;
    }

    private List<WhiteDriverImportDTO> importDataBasicCheck(Map<String, List<HrmsUserInfoDO>> userInfoMaps, List<WhiteDriverImportDTO> importList, List<WhiteDriverImportDTO> failImportList) {
        List<WhiteDriverImportDTO> filterList = new ArrayList<>();

        for (WhiteDriverImportDTO importDTO : importList) {
            if (StringUtils.isBlank(importDTO.getUserCode())) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "司机编码不能为空" : "DA code not empty");
                failImportList.add(importDTO);
                continue;
            }
            List<HrmsUserInfoDO> userInfoDOList = userInfoMaps.get(importDTO.getUserCode());
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "司机不存在" : "driver not exist");
                failImportList.add(importDTO);
                continue;
            }
            HrmsUserInfoDO hrmsUserInfoDO = userInfoDOList.get(0);
            if (!hrmsUserInfoDO.getIsDriver().equals(1)) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "请导入司机" : "please import driver");
                failImportList.add(importDTO);
                continue;
            }
            if (hrmsUserInfoDO.getDeptId() == null) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该司机没有网点" : "driver not have station");
                failImportList.add(importDTO);
                continue;
            }
            if (StringUtils.isBlank(hrmsUserInfoDO.getOriginCountry())) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "该司机没有所属国" : "driver not have origin country");
                failImportList.add(importDTO);
                continue;
            }

            if (StringUtils.isBlank(importDTO.getStartDateString())) {
                importDTO.setStartDate(new Date());
            } else {
                Date startDate = DateUtil.date();
                try {
                    startDate = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(importDTO.getStartDateString());
                } catch (Exception e) {
                    IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "开始时间格式有问题" : "There is a problem with the start date format");
                    failImportList.add(importDTO);
                    continue;
                }
                importDTO.setStartDate(startDate);
            }

            if (StringUtils.isBlank(importDTO.getEndDateString())) {
                importDTO.setEndDate(DateUtil.parse("2099-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss"));
            } else {
                Date endDate = DateUtil.date();
                try {
                    endDate = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(importDTO.getEndDateString());
                } catch (Exception e) {
                    IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "结束时间格式有问题" : "There is a problem with the end date format");
                    failImportList.add(importDTO);
                    continue;
                }
                importDTO.setEndDate(endDate);
            }

            if (DateUtil.compare(importDTO.getStartDate(), importDTO.getEndDate()) > -1) {
                IpepUtils.putFail(importDTO, RequestInfoHolder.isChinese() ? "开始时间不能大于等于结束时间" : "start date not bigger than end date");
                failImportList.add(importDTO);
                continue;
            }

            importDTO.setUserId(hrmsUserInfoDO.getId());
            importDTO.setReallyUserName(hrmsUserInfoDO.getUserName());
            importDTO.setSysAccountName(hrmsUserInfoDO.getSysAccountName());
            importDTO.setDeptId(hrmsUserInfoDO.getDeptId());
            importDTO.setCountry(hrmsUserInfoDO.getOriginCountry());
            filterList.add(importDTO);
        }
        return filterList;
    }

    private void addWhiteDriverBuild(HrmsVehicleFuelWhiteDriverDO addDO, WhiteDriverUpdateDTO dto, HrmsUserInfoDO hrmsUserInfoDO) {
        addDO.setCountry(hrmsUserInfoDO.getOriginCountry());
        addDO.setDeptId(dto.getDeptId());
        addDO.setUserId(dto.getUserId());
        addDO.setUserCode(hrmsUserInfoDO.getUserCode());
        addDO.setUserName(hrmsUserInfoDO.getUserName());
        addDO.setSysAccountName(hrmsUserInfoDO.getSysAccountName());
        addDO.setStartDate(dto.getStartDate());
        addDO.setEndDate(dto.getEndDate());
        addDO.setRemark(dto.getRemark());
        BaseDOUtil.fillDOInsert(addDO);
    }

    /**
     * 查询参数构建
     */
    private void vehicleFuelRecordQueryBuild(WhiteDriverQuery query, WhiteDriverQueryDTO queryDTO) {
        //获取供应商
        EntEmployeeApiDTO employee = entEmployeeService.getEmployeeByCode(RequestInfoHolder.getUserCode());
        if (employee == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        List<EntOcSlimpleApiDTO> ocSlimpleApiDTOList = userAuthorizeService.getOcByUserId(10L, employee.getId());
        List<String> ocNameList = ocSlimpleApiDTOList.stream().map(item -> item.getOcName().trim().toUpperCase()).collect(Collectors.toList());

        List<HrmsEntDeptDO> allDeptList = hrmsDeptManage.getAllDept().stream().filter(item -> DeptOrgTypeEnum.getInstance(item.getDeptOrgType()).getIsStationFlag()).collect(Collectors.toList());
        List<String> countryList = new ArrayList<>();
        List<Long> deptIdList = new ArrayList<>();
        //国家全选
        if (queryDTO.getCountry() == null) {
            //选择指定部门
            if (queryDTO.getStationId() != null) {
                HrmsEntDeptDO entDeptDO = hrmsDeptManage.selectById(queryDTO.getStationId());
                if (entDeptDO != null) {
                    countryList.addAll(SplitUtil.splitNew(entDeptDO.getBizCountry(), HrmsStringUtil.COMMA));
                    deptIdList.add(entDeptDO.getId());
                    query.setCountryList(countryList);
                    query.setDeptIdList(deptIdList);
                }
                return;
            }
            //部门全选
            for (String stationName : ocNameList) {
                List<HrmsEntDeptDO> filterEntDeptDOList = allDeptList.stream().filter(item -> !hrmsDeptManage.isTestDept(item) && (StringUtils.equalsIgnoreCase(item.getDeptNameCn(), stationName) || StringUtils.equalsIgnoreCase(item.getDeptNameEn(), stationName))).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterEntDeptDOList)) {
                    continue;
                }
                countryList.addAll(SplitUtil.splitNew(filterEntDeptDOList.get(0).getBizCountry(), HrmsStringUtil.COMMA));
                deptIdList.add(filterEntDeptDOList.get(0).getId());
            }
            query.setCountryList(countryList);
            query.setDeptIdList(deptIdList);
            return;
        }

        //指定国家
        //选择指定部门
        if (queryDTO.getStationId() != null) {
            HrmsEntDeptDO entDeptDO = hrmsDeptManage.selectById(queryDTO.getStationId());
            if (entDeptDO != null) {
                countryList.addAll(SplitUtil.splitNew(entDeptDO.getBizCountry(), HrmsStringUtil.COMMA));
                deptIdList.add(entDeptDO.getId());
                query.setCountryList(countryList);
                query.setDeptIdList(deptIdList);
            }
            return;
        }
        //部门全选
        for (String stationName : ocNameList) {
            List<HrmsEntDeptDO> filterEntDeptDOList = allDeptList.stream().filter(item -> SplitUtil.splitNew(item.getBizCountry(), HrmsStringUtil.COMMA).contains(queryDTO.getCountry()) && (StringUtils.equalsIgnoreCase(item.getDeptNameCn(), stationName) || StringUtils.equalsIgnoreCase(item.getDeptNameEn(), stationName))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterEntDeptDOList)) {
                continue;
            }

            countryList.addAll(SplitUtil.splitNew(filterEntDeptDOList.get(0).getBizCountry(), HrmsStringUtil.COMMA));
            deptIdList.add(filterEntDeptDOList.get(0).getId());
        }
        query.setCountryList(countryList);
        query.setDeptIdList(deptIdList);
    }
}
