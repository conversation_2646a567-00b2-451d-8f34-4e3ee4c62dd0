package com.imile.hrms.service.zkteco.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.EnvEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.ZktVersionEnum;
import com.imile.hrms.dao.attendance.mapper.HrmsZktecoAreaSnRelationMapper;
import com.imile.hrms.dao.attendance.model.HrmsZktecoAreaSnRelationDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.zkteco.DO.ZktecoAreasDO;
import com.imile.hrms.dao.zkteco.DO.ZktecoEmployeeDO;
import com.imile.hrms.dao.zkteco.DO.ZktecoEmployeeUpdateDO;
import com.imile.hrms.manage.zkteco.impl.ZktecoManageImpl;
import com.imile.hrms.manage.zkteco.impl.ZktecoVersion8ManageImpl;
import com.imile.hrms.service.attendance.HrmsZktecoAreaSnRelationService;
import com.imile.hrms.service.zkteco.ZKTecoUtils;
import com.imile.hrms.service.zkteco.ZktecoService;
import com.imile.hrms.service.zkteco.dto.EmployeeResultDTO;
import com.imile.hrms.service.zkteco.dto.ZktecoAreasDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ZktecoServiceImpl implements ZktecoService {

    @Resource
    private ZktecoManageImpl zktecoManage;
    @Resource
    private ZktecoVersion8ManageImpl zktecoVersion8Manage;
    /* @Autowired
     private ZktecoManage zktecoManage;*/
    @Autowired
    private ZKTecoUtils zkTecoUtils;
    @Autowired
    private HrmsUserInfoDao userInfoDao;
    @Autowired
    private HrmsZktecoAreaSnRelationService relationService;
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsZktecoAreaSnRelationMapper relationMapper;

    @Value("${imile.hrms.sync.driver.country:KSA}")
    private String syncDriverCountry;

    @Value("${imile.hrms.sync.driver.vendor.code:300001,2101847,2104408,2104616,2106054}")
    private String syncDriverVendorCode;

    @Value("${imile.hrms.sync.driver.post.id}")
    private String syncDriverPostId;

    @Value(value = "${server.env}")
    private String env;

    @Override
    public Map<String, List<ZktecoAreasDTO>> selectSnByArea() {
        Map<String, List<ZktecoAreasDTO>> map = new HashMap<>();

        List<ZktecoAreasDO> zktecoAreasList = zktecoManage.selectSnByArea();
        if (CollectionUtils.isNotEmpty(zktecoAreasList)) {
            List<ZktecoAreasDTO> zktecoAreasDTOS = convertZktecoAreasDTO(zktecoAreasList);
            map.put("zkt8.5",zktecoAreasDTOS);
        }

        if (StringUtils.isNotBlank(hrmsProperties.getZkteco().getCountryVersion8())) {
            //走8.0版本
            List<ZktecoAreasDO> zktecoAreasList8 = zktecoVersion8Manage.selectSnByArea();
            if (CollectionUtils.isNotEmpty(zktecoAreasList8)) {
                List<ZktecoAreasDTO> zktecoAreasDTOS = convertZktecoAreasDTO(zktecoAreasList8);
                map.put("zkt8",zktecoAreasDTOS);
            }
        }
        return map;
    }

    /**
     * 判断Zkteco是否使用8.0版本
     *
     * @param country 国家
     * @return
     */
    @Override
    public boolean isCountryVersion8(String country){
        if (StringUtils.isBlank(country)) {
            return false;
        }
        String countryVersion8 = hrmsProperties.getZkteco().getCountryVersion8();
        return StringUtils.isNotBlank(countryVersion8)
                && Arrays.stream(countryVersion8.split(",")).anyMatch(o -> StringUtils.equalsIgnoreCase(o, country));
    }


    @Override
    public void syncEmployee2Zkteco(Long userId) {
        log.info("syncEmployee2Zkteco | userId :{}，env：{}", userId, env);
        if (EnvEnum.isInformal(env)) {
            log.info("syncEmployee2Zkteco | env is not prod or uat, return");
            return;
        }
        log.info("syncEmployee2Zkteco | userId :{}，start sync zkt", userId);
        HrmsUserInfoDO userInfoDO = userInfoDao.getById(userId);
        if (userInfoDO == null || StringUtils.isBlank(userInfoDO.getUserCode()) || ObjectUtil.isEmpty(userInfoDO.getLocationCountry())) {
            // todo 异常信息
            return;
        }
        try {
            List<ZktecoEmployeeDO> zktecoEmployeeDOS = new ArrayList<>();
            String serverUrl = hrmsProperties.getZkteco().getSERVER_URL();
            String token = "";
            // 是否是8.0版本的国家
            boolean countryVersion8 = isCountryVersion8(userInfoDO.getLocationCountry());
            log.info("syncEmployee2Zkteco | countryVersion8 :{}", countryVersion8);
            if (countryVersion8) {
                //走8.0版本
                zktecoEmployeeDOS = zktecoVersion8Manage.selectEmployee(Arrays.asList(userInfoDO.getUserCode()));
                serverUrl = hrmsProperties.getZkteco().getSERVER_URL_VERSION_8();
                token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserNameVersion8(), hrmsProperties.getZkteco().getPasswordVersion8(), serverUrl);
            } else {
                zktecoEmployeeDOS = zktecoManage.selectEmployee(Arrays.asList(userInfoDO.getUserCode()));
                token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserName(), hrmsProperties.getZkteco().getPassword(), serverUrl);
            }
            if (CollectionUtils.isEmpty(zktecoEmployeeDOS)) {
                //需要同步员工信息到中控考勤机
                addEmployee(token, userInfoDO, serverUrl, countryVersion8);
                return;
            }
            updateEmployee(token, zktecoEmployeeDOS.get(0), userInfoDO, serverUrl, countryVersion8);
        } catch (Exception e) {
            log.error("syncEmployee2Zkteco error", e);
        }

    }


    @Override
    public void syncEmployees(String country, HrmsZktecoAreaSnRelationDO relationDO, List<Long> deptIdList, List<Long> userIdList) {
        String token;
        ZktVersionEnum zktVersionEnum = ZktVersionEnum.ZKT_VERSION_85;
        if (isCountryVersion8(country)) {
            //走8.0版本
            token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserNameVersion8(), hrmsProperties.getZkteco().getPasswordVersion8(), hrmsProperties.getZkteco().getSERVER_URL_VERSION_8());
            zktVersionEnum = ZktVersionEnum.ZKT_VERSION_80;
        } else {
            //走原版本，8.5
            token = zkTecoUtils.getToken(hrmsProperties.getZkteco().getUserName(), hrmsProperties.getZkteco().getPassword(), hrmsProperties.getZkteco().getSERVER_URL());
        }
        log.info("env：{}", env);
        // 如果是uat环境
        if (EnvEnum.UAT.getCode().equalsIgnoreCase(env)) {
            log.info("into uat");
            //List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(relationDO.getDeptIds().split(","), Long.class));

            //List<Long> userIds = Arrays.asList((Long[]) ConvertUtils.convert(relationDO.getUserIds().split(","), Long.class));

            //获取原来的用户信息
            // uat环境去掉比对，直接全量同步
            //List<HrmsUserInfoDO> userInfoList = selectUser(deptIds, userIds);
            //List<String> userCodeList = userInfoList.stream().map(o -> o.getUserCode()).distinct().collect(Collectors.toList());
            //获取用户信息
            // 直接全量同步数据
            List<HrmsUserInfoDO> userInfoDOList = selectUser(deptIdList, userIdList);

            //当前新增的员工
            //List<HrmsUserInfoDO> filterUserList = userInfoDOList.stream().filter(o -> !userCodeList.contains(o.getUserCode())).collect(Collectors.toList());
            log.info("uat本次全量同步中控考勤机的员工：userInfoDOList ={}",JSON.toJSONString(userInfoDOList));
            syncEmployeeHandler(token, relationDO, userInfoDOList, zktVersionEnum);
            return;
        }
        log.info("进入生产环境");
        // 如果是prod环境
        if (EnvEnum.PROD.getCode().equalsIgnoreCase(env)) {
            log.info("into prod");
            // 如果是zkt第一次同步过来的数据，deptIds是null，country是null
            if (ObjectUtil.isNull(relationDO.getCountry()) && ObjectUtil.isNull(relationDO.getDeptIds())) {
                // 如果数据库该条数据country = null && deptIds = null，那么就是第一次同步过来的数据，那么直接全部前端传过来的数据到中控考勤机
                List<HrmsUserInfoDO> userInfoDOList = selectUser(deptIdList, userIdList);
                syncEmployeeHandler(token, relationDO, userInfoDOList, zktVersionEnum);
                return;
            }

            // 否则就不是第一次同步过来的数据，那么就需要判断前端传过来的数据和数据库中的数据做对比，然后同步新增的数据到中控考勤机
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(relationDO.getDeptIds().split(","), Long.class));

            List<Long> userIds = Arrays.asList((Long[]) ConvertUtils.convert(relationDO.getUserIds().split(","), Long.class));

            //获取原来的用户信息
            List<HrmsUserInfoDO> userInfoList = selectUser(deptIds, userIds);
            List<String> userCodeList = userInfoList.stream().map(o -> o.getUserCode()).distinct().collect(Collectors.toList());
            //获取用户信息
            List<HrmsUserInfoDO> userInfoDOList = selectUser(deptIdList, userIdList);

            //当前新增的员工
            List<HrmsUserInfoDO> filterUserList = userInfoDOList.stream().filter(o -> !userCodeList.contains(o.getUserCode())).collect(Collectors.toList());
            log.info("本次同步中控考勤机的员工：filterUserList ={}", JSON.toJSONString(filterUserList));
            syncEmployeeHandler(token, relationDO, filterUserList, zktVersionEnum);
            return;
        }
        log.info("not uat or prod ，not executed");
    }

    private void addEmployee(String token, HrmsUserInfoDO userInfoDO, String serverUrl, boolean countryVersion8) {
        if (!checkNeedSyncDriver(userInfoDO)) {
            return;
        }
        if (userInfoDO.getDeptId() == null) {
            //todo 记录异常
            return;
        }

        //todo 根据部门id查询对应区域
        List<HrmsZktecoAreaSnRelationDO> hrmsZktecoAreaSnRelationDOS = relationService.selectByDeptId(userInfoDO.getDeptId());
        if (CollectionUtils.isEmpty(hrmsZktecoAreaSnRelationDOS)) {
            //todo 记录异常
            return;
        }
        Integer deptId = hrmsProperties.getZkteco().getOfficeDeptId();
        if (userInfoDO.getIsWarehouseStaff() == 1) {
            deptId = hrmsProperties.getZkteco().getWarehouseDeptId();
        }
        // 根据zkt版本选择不同的部门
        if(countryVersion8){
            // 如果是8.0版本的国家
            deptId = hrmsProperties.getZkteco().getOfficeDeptId8();
            if (userInfoDO.getIsWarehouseStaff() == 1) {
                deptId = hrmsProperties.getZkteco().getWarehouseDeptId8();
            }
        }

        List<Integer> areaIds = hrmsZktecoAreaSnRelationDOS.stream().map(o -> o.getZktecoAreaId()).collect(Collectors.toList());
        String result = zkTecoUtils.createEmployee(token, userInfoDO.getUserCode(), userInfoDO.getUserName(), null, deptId, areaIds, serverUrl);
        if (StringUtils.isBlank(result)) {
            //todo 记录异常
            return;
        }
        EmployeeResultDTO employeeResultDTO = JSONObject.parseObject(result, EmployeeResultDTO.class);
        if (employeeResultDTO.getId() == null || !StringUtils.equalsIgnoreCase(userInfoDO.getUserCode(), employeeResultDTO.getEmp_code())) {
            //todo 记录异常
            return;
        }

    }

    private void updateEmployee(String token, ZktecoEmployeeDO employeeDO, HrmsUserInfoDO userInfoDO, String serverUrl, boolean countryVersion8) {
        if (!checkNeedSyncDriver(userInfoDO)) {
            return;
        }
        if (userInfoDO.getDeptId() == null) {
            //todo 记录异常
            return;
        }
        ZktecoEmployeeUpdateDO updateDO = new ZktecoEmployeeUpdateDO();
        updateDO.setId(employeeDO.getId());
        updateDO.setUserCode(userInfoDO.getUserCode());
        updateDO.setFirstName(userInfoDO.getUserName());
        Integer deptId = hrmsProperties.getZkteco().getOfficeDeptId();
        if (userInfoDO.getIsWarehouseStaff() == 1) {
            deptId = hrmsProperties.getZkteco().getWarehouseDeptId();
        }
        // 根据zkt版本选择不同的部门
        if(countryVersion8){
            // 如果是8.0版本的国家
            deptId = hrmsProperties.getZkteco().getOfficeDeptId8();
            if (userInfoDO.getIsWarehouseStaff() == 1) {
                deptId = hrmsProperties.getZkteco().getWarehouseDeptId8();
            }
        }
        updateDO.setDeptId(deptId);

        //todo 根据部门id查询对应区域
        List<HrmsZktecoAreaSnRelationDO> userSnList = Lists.newArrayList();
        // 通过部门查询绑定设备区域
        List<HrmsZktecoAreaSnRelationDO> userSnList_dept = relationService.selectByDeptId(userInfoDO.getDeptId());
        if (CollectionUtils.isNotEmpty(userSnList_dept)) {
            userSnList.addAll(userSnList_dept);
        }
        // 通过用户查询绑定设备区域
        List<HrmsZktecoAreaSnRelationDO> userSnList_user = relationService.selectByUserId(userInfoDO.getId());
        if (CollectionUtils.isNotEmpty(userSnList_user)) {
            userSnList.addAll(userSnList_user);
        }
        if (CollectionUtils.isEmpty(userSnList)) {
            log.info("updateEmployee | userInfo :{}, userInfo :{}, userSnList :{}", userInfoDO.getUserCode(), JSON.toJSONString(userInfoDO), JSON.toJSONString(userSnList));
            return;
        }
        List<Integer> areaIds = userSnList.stream().map(o -> o.getZktecoAreaId()).collect(Collectors.toList());

        updateDO.setAreaIds(areaIds);

        updateDO.setStatus(0);
        if (StringUtils.equalsIgnoreCase(StatusEnum.DISABLED.getCode(), userInfoDO.getStatus())
                || StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), userInfoDO.getWorkStatus())) {
            updateDO.setStatus(99);
        }
        String result = zkTecoUtils.updateEmployee(token, updateDO, serverUrl);
        EmployeeResultDTO employeeResultDTO = JSONObject.parseObject(result, EmployeeResultDTO.class);
        if (employeeResultDTO.getId() == null || !StringUtils.equalsIgnoreCase(userInfoDO.getUserCode(), employeeResultDTO.getEmp_code())) {
            //todo 记录异常
            return;
        }
    }


    private void syncEmployeeHandler(String token, HrmsZktecoAreaSnRelationDO relationDO, List<HrmsUserInfoDO> userInfoDOList, ZktVersionEnum zktVersionEnum) {
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return;
        }
        List<String> countryList = Arrays.asList(syncDriverCountry.split(","));

        Map<Long, List<HrmsZktecoAreaSnRelationDO>> map = new HashMap<>();
        List<HrmsUserInfoDO> driverList = userInfoDOList.stream().filter(o -> o.getIsDriver() == 1 && o.getIsVirtual() == 0
                && countryList.contains(o.getLocationCountry()) && StringUtils.equalsIgnoreCase(EmploymentTypeEnum.EMPLOYEE.getCode(), o.getEmployeeType())).collect(Collectors.toList());
        List<HrmsUserInfoDO> warehouseList = userInfoDOList.stream().filter(o -> o.getIsWarehouseStaff() != null && o.getIsVirtual() != null && o.getIsWarehouseStaff() == 1 && o.getIsVirtual() == 0).collect(Collectors.toList());
        List<HrmsUserInfoDO> officeList = userInfoDOList.stream().filter(o -> o.getIsWarehouseStaff() != null && o.getIsVirtual() != null && o.getIsDriver() == 0 && o.getIsWarehouseStaff() == 0 && o.getIsVirtual() == 0).collect(Collectors.toList());

        // 如果ZKTeco版本是8.0，则需要设置一下ZKT的部门id，因为8.0版本的部门id跟8.5的不一样，8.5的部门id是阿波罗配置的，8.0的部门id是新的
        if (ObjectUtil.equal(zktVersionEnum.getCode(), ZktVersionEnum.ZKT_VERSION_80.getCode())) {
            syncEmployee(token, hrmsProperties.getZkteco().getDriverDeptId8(), driverList, map, zktVersionEnum);
            syncEmployee(token, hrmsProperties.getZkteco().getWarehouseDeptId8(), warehouseList, map, zktVersionEnum);
            syncEmployee(token, hrmsProperties.getZkteco().getOfficeDeptId8(), officeList, map, zktVersionEnum);
        } else {
            syncEmployee(token, hrmsProperties.getZkteco().getDriverDeptId(), driverList, map, zktVersionEnum);
            syncEmployee(token, hrmsProperties.getZkteco().getWarehouseDeptId(), warehouseList, map, zktVersionEnum);
            syncEmployee(token, hrmsProperties.getZkteco().getOfficeDeptId(), officeList, map, zktVersionEnum);
        }

    }

    private void syncEmployee(String token, Integer deptId, List<HrmsUserInfoDO> userInfoDOList, Map<Long, List<HrmsZktecoAreaSnRelationDO>> map, ZktVersionEnum zktVersionEnum) {
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return;
        }
        List<String> userCodeList = userInfoDOList.stream().map(o -> o.getUserCode()).distinct().collect(Collectors.toList());
        List<ZktecoEmployeeDO> zktecoEmployeeDOS = new ArrayList<>();
        String serverUrl = hrmsProperties.getZkteco().getSERVER_URL();
        if (StringUtils.equalsIgnoreCase(zktVersionEnum.getCode(), ZktVersionEnum.ZKT_VERSION_80.getCode())) {
            zktecoEmployeeDOS = zktecoVersion8Manage.selectEmployee(userCodeList);
            serverUrl = hrmsProperties.getZkteco().getSERVER_URL_VERSION_8();
        } else {
            zktecoEmployeeDOS = zktecoManage.selectEmployee(userCodeList);
        }
        Map<String, ZktecoEmployeeDO> employeeMap = zktecoEmployeeDOS.stream().collect(Collectors.toMap(o -> o.getEmpCode(), o -> o, (v1, v2) -> v1));
        log.info("syncEmployee | userInfoDOList :{}, zktecoEmployeeDOS :{}, employeeMap :{}", JSON.toJSONString(userInfoDOList), JSON.toJSONString(zktecoEmployeeDOS), JSON.toJSONString(employeeMap));
        if (employeeMap == null) {
            employeeMap = new HashMap<>();
        }
        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {

            List<Integer> areaIds = getAreaIds(map, userInfoDO);
            if (CollectionUtils.isEmpty(areaIds)) {
                continue;
            }
            ZktecoEmployeeDO zktecoEmployeeDO = employeeMap.get(userInfoDO.getUserCode());
            log.info("syncEmployee | userInfoDO :{}, zktecoEmployeeDO :{}", JSON.toJSONString(userInfoDO), JSON.toJSONString(zktecoEmployeeDO));

            if (zktecoEmployeeDO != null) {
                ZktecoEmployeeUpdateDO updateDO = new ZktecoEmployeeUpdateDO();
                updateDO.setId(zktecoEmployeeDO.getId());
                updateDO.setFirstName(userInfoDO.getUserName());
                updateDO.setUserCode(userInfoDO.getUserCode());
                updateDO.setDeptId(deptId);
                updateDO.setAreaIds(areaIds);
                updateDO.setStatus(0);
                zkTecoUtils.updateEmployee(token, updateDO, serverUrl);
                continue;
            }
            zkTecoUtils.createEmployee(token, userInfoDO.getUserCode(), userInfoDO.getUserName(), null, deptId, areaIds, serverUrl);
        }
    }


    /**
     * 查询用户信息
     *
     * @param deptIdList
     * @param userIdList
     * @return
     */
    private List<HrmsUserInfoDO> selectUser( List<Long> deptIdList, List<Long> userIdList) {
        //查询对应公司的员工
        LambdaQueryWrapper<HrmsUserInfoDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HrmsUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(deptIdList) && CollectionUtils.isEmpty(userIdList)) {
            wrapper.in(HrmsUserInfoDO::getDeptId, deptIdList);
        }
        if (CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isEmpty(deptIdList)) {
            wrapper.in(HrmsUserInfoDO::getId, userIdList);
        }
        if (CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isNotEmpty(deptIdList)) {
            wrapper.and(param ->
                    param.in(HrmsUserInfoDO::getDeptId, deptIdList)
                            .or(i -> i.in(HrmsUserInfoDO::getId, userIdList)));
        }
        wrapper.eq(HrmsUserInfoDO::getStatus, StatusEnum.ACTIVE.getCode());
        wrapper.in(HrmsUserInfoDO::getWorkStatus, Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()));
        wrapper.isNotNull(HrmsUserInfoDO::getUserCode);
        return hrmsUserInfoDao.list(wrapper);
    }

    private List<Integer> getAreaIds(Map<Long, List<HrmsZktecoAreaSnRelationDO>> map, HrmsUserInfoDO userInfoDO) {
        List<HrmsZktecoAreaSnRelationDO> areaByUser = getAreaByUser(userInfoDO);
        Set<Integer> areaIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(areaByUser)) {
            List<Integer> collect = areaByUser.stream().map(o -> o.getZktecoAreaId()).distinct().collect(Collectors.toList());
            areaIds.addAll(collect);
        }
        List<HrmsZktecoAreaSnRelationDO> areaByDept = map.get(userInfoDO.getDeptId());
        if (CollectionUtils.isNotEmpty(areaByDept)) {
            List<Integer> collect = areaByDept.stream().map(o -> o.getZktecoAreaId()).distinct().collect(Collectors.toList());
            areaIds.addAll(collect);
            return new ArrayList<>(areaIds);
        }
        List<HrmsZktecoAreaSnRelationDO> hrmsZktecoAreaSnRelationDOS = relationService.selectByDeptId(userInfoDO.getDeptId());
        if (CollectionUtils.isNotEmpty(hrmsZktecoAreaSnRelationDOS)) {
            List<Integer> collect = hrmsZktecoAreaSnRelationDOS.stream().map(o -> o.getZktecoAreaId()).distinct().collect(Collectors.toList());
            areaIds.addAll(collect);
            return new ArrayList<>(areaIds);
        }
        return new ArrayList<>(areaIds);
    }

    private List<HrmsZktecoAreaSnRelationDO> getAreaByUser(HrmsUserInfoDO userInfoDO) {
        List<HrmsZktecoAreaSnRelationDO> hrmsZktecoAreaSnRelationDOS = new ArrayList<>();
        LambdaQueryWrapper<HrmsZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.like(HrmsZktecoAreaSnRelationDO::getUserIds, userInfoDO.getId());
        List<HrmsZktecoAreaSnRelationDO> snRelationDOList = relationMapper.selectList(queryWrapper);
        for (HrmsZktecoAreaSnRelationDO relationDO : snRelationDOList) {
            List<String> userIdStr = Arrays.asList(relationDO.getUserIds().split(","));
            if (userIdStr.contains(userInfoDO.getId().toString())) {
                hrmsZktecoAreaSnRelationDOS.add(relationDO);
            }
        }
        return hrmsZktecoAreaSnRelationDOS;
    }


    /**
     * 检查是否同步司机信息
     * @param userInfoDO
     * @return
     */
    private boolean checkNeedSyncDriver(HrmsUserInfoDO userInfoDO) {
        if (userInfoDO.getIsDriver().equals(BusinessConstant.ZERO)) {
            return true;
        }
        List<String> countryList = Arrays.asList(syncDriverCountry.split(","));

        List<String> vendorCodeList = Arrays.asList(syncDriverVendorCode.split(","));

        List<Long> postIdList = Arrays.stream(syncDriverPostId.split(",")).map(Long::valueOf).collect(Collectors.toList());

        if (!postIdList.contains(userInfoDO.getPostId())) {
            return false;
        }

        if (!countryList.contains(userInfoDO.getLocationCountry())) {
            return false;
        }
        if (StringUtils.equalsIgnoreCase(EmploymentTypeEnum.EMPLOYEE.getCode(), userInfoDO.getEmployeeType())
                || StringUtils.equalsIgnoreCase(EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), userInfoDO.getEmployeeType())) {
            return true;
        }
        if (StringUtils.equalsIgnoreCase(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), userInfoDO.getEmployeeType()) && vendorCodeList.contains(userInfoDO.getUserCode())) {
            return true;
        }
        return false;
    }

    private List<ZktecoAreasDTO> convertZktecoAreasDTO(List<ZktecoAreasDO> zktecoAreasList) {
        if (CollectionUtils.isEmpty(zktecoAreasList)) {
            return new ArrayList<>();
        }
        Map<String, List<ZktecoAreasDO>> areaGroup = zktecoAreasList.stream().collect(Collectors.groupingBy(o -> o.getAreaName()));

        List<ZktecoAreasDTO> areasDTOList = new ArrayList<>(areaGroup.size());
        for (Map.Entry<String, List<ZktecoAreasDO>> entry : areaGroup.entrySet()) {
            ZktecoAreasDTO areasDTO = new ZktecoAreasDTO();
            ZktecoAreasDO zktecoAreasDO = entry.getValue().get(0);
            areasDTO.setId(zktecoAreasDO.getId());
            areasDTO.setAreaCode(zktecoAreasDO.getAreaCode());
            areasDTO.setAreaName(zktecoAreasDO.getAreaName());
            areasDTO.setSnList(entry.getValue().stream().map(o -> o.getSn()).collect(Collectors.toList()));
            areasDTOList.add(areasDTO);
        }
        return areasDTOList;
    }

}
