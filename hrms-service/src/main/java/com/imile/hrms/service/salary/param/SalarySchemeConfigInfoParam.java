package com.imile.hrms.service.salary.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/26 15:01
 * @version: 1.0
 */
@Data
public class SalarySchemeConfigInfoParam extends ResourceQuery {

    /**
     * 计薪国
     */
    private String country;

    /**
     * 状态
     */
    private String status;

    /**
     * 计薪周期
     */
    private String cycleType;

    /**
     * 计薪方案编码或者名称(模糊查询)
     */
    private String schemeNameOrNo;

    /**
     * 特殊查询条件:薪资项发生变动导致计薪方案查询
     */
    private Integer isItemChange;
}
