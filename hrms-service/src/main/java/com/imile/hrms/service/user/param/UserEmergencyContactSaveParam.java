package com.imile.hrms.service.user.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.genesis.api.model.component.Phone;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
@Data
public class UserEmergencyContactSaveParam {

    /**
     * 联系人姓名
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String contactName;

    /**
     * 联系人关系
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String contactRelationship;

    /**
     * 联系人电话
     */
    @Valid
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Phone contactPhone;
}
