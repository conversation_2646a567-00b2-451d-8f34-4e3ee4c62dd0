package com.imile.hrms.service.user;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.service.achievement.dto.CommonCountDTO;
import com.imile.hrms.service.user.param.FreelancerDriverAuditParam;
import com.imile.hrms.service.user.param.FreelancerDriverAuditRecordQueryParam;
import com.imile.hrms.service.user.result.FreelancerDriverAuditRecordDetailBO;
import com.imile.hrms.service.user.result.FreelancerDriverAuditRecordListBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/19
 */
public interface FreelancerDriverAuditRecordService {

    /**
     * 获取众包司机审核状态计数列表
     *
     * @return List<CommonCountDTO>
     */
    List<CommonCountDTO> getFreelancerDriverAuditStatusCountList();

    /**
     * 获取众包司机审核记录列表
     *
     * @param param FreelancerDriverAuditRecordQueryParam
     * @return PageResult<FreelancerDriverAuditRecordListBO>
     */
    PaginationResult<FreelancerDriverAuditRecordListBO> getFreelancerDriverAuditRecordList(FreelancerDriverAuditRecordQueryParam param);

    /**
     * 获取众包司机审核记录详情
     *
     * @param id 审核记录ID
     * @return FreelancerDriverAuditRecordDetailBO
     */
    FreelancerDriverAuditRecordDetailBO getFreelancerDriverAuditRecordDetail(Long id);

    /**
     * 审核众包司机
     *
     * @param param FreelancerDriverAuditParam
     * @return Boolean
     */
    Boolean auditFreelancerDriver(FreelancerDriverAuditParam param);
}
