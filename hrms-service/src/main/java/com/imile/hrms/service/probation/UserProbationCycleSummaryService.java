package com.imile.hrms.service.probation;

import com.imile.hrms.service.probation.bo.UserProbationCycleSummaryBO;
import com.imile.hrms.service.probation.param.UserProbationCycleSummarySubmitParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 员工试用期周期总结
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public interface UserProbationCycleSummaryService {

    /**
     * 周期总结详情
     *
     * @param userProbationId 试用期id
     * @return List<UserProbationCycleSummaryBO>
     */
    List<UserProbationCycleSummaryBO> getCycleSummaryList(Long userProbationId);

    /**
     * 保存周期总结
     *
     * @param param 周期总结
     * @return true/false
     */
    Boolean save(UserProbationCycleSummarySubmitParam param);
}
