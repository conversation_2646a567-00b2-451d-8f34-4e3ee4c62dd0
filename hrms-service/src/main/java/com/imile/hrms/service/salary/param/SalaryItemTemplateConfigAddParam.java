package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/25 15:52
 * @version: 1.0
 */
@Data
public class SalaryItemTemplateConfigAddParam {
    /**
     * 薪资费用项模版ID(更新的时候传)
     */
    private Long itemTemplateId;


    /**
     * 薪资费用项模版名称
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String itemTemplateName;

    /**
     * 适用国
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * 项目组明细
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    List<SalaryItemGroupConfigParam> itemGroupConfigParamList;
}
