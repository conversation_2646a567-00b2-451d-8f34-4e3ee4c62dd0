package com.imile.hrms.service.organization.impl.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.api.organization.api.BizOrganizationInfoApi;
import com.imile.hrms.api.organization.dto.CountryConfigApiDTO;
import com.imile.hrms.api.organization.dto.DeptRegionInfoApiDTO;
import com.imile.hrms.api.organization.dto.EntDeptInfoByLevelApiDTO;
import com.imile.hrms.api.organization.dto.EntOcOperationRecordDTO;
import com.imile.hrms.api.organization.dto.EntPostApiDTO;
import com.imile.hrms.api.organization.dto.OrgDeptApiDTO;
import com.imile.hrms.api.organization.dto.OrgDeptAttributeApiDTO;
import com.imile.hrms.api.organization.dto.OrgDeptTreeApiDTO;
import com.imile.hrms.api.organization.dto.OrgPostInfoApiDTO;
import com.imile.hrms.api.organization.dto.OrgProjectDTO;
import com.imile.hrms.api.organization.dto.OrgSettlementCenterDTO;
import com.imile.hrms.api.organization.dto.OrgUserInfoApiDTO;
import com.imile.hrms.api.organization.dto.OrgUserInfoDetailApiDTO;
import com.imile.hrms.api.organization.dto.SelectUserApiDTO;
import com.imile.hrms.api.organization.dto.SyncCountryKingdeeDTO;
import com.imile.hrms.api.organization.dto.UserInfoApiDTO;
import com.imile.hrms.api.organization.query.PostApiQuery;
import com.imile.hrms.api.organization.query.SelectUserApiQuery;
import com.imile.hrms.api.organization.query.SelectUserInfoApiQuery;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.constants.KingdeeConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.entity.BaseDO;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.RelationBizTypeEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.common.util.KingdeeUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.bo.HrmsEntDeptBO;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.dao.HrmsEntProjectDao;
import com.imile.hrms.dao.organization.dto.DeptRegionInfoDTO;
import com.imile.hrms.dao.organization.dto.EntPostDTO;
import com.imile.hrms.dao.organization.model.HrmsDeptAttributeDO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.organization.model.HrmsEntProjectDO;
import com.imile.hrms.dao.organization.query.PostQuery;
import com.imile.hrms.dao.sys.dao.HrmsPlatformRelationDao;
import com.imile.hrms.dao.sys.model.HrmsPlatformRelationDO;
import com.imile.hrms.dao.user.dao.HrmsCountryConfigDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.SelectUserDTO;
import com.imile.hrms.dao.user.dto.UserDTO;
import com.imile.hrms.dao.user.dto.UserListInfoDTO;
import com.imile.hrms.dao.user.model.HrmsCountryConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.CountryQuery;
import com.imile.hrms.dao.user.query.SelectUserPageQuery;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.dao.user.query.UserQuery;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.integration.kingdee.KingdeeIntegration;
import com.imile.hrms.integration.kingdee.dto.KingDeeCountryInfoDTO;
import com.imile.hrms.integration.kingdee.dto.UnAuditDTO;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.organization.HrmsBaseDataService;
import com.imile.hrms.service.organization.HrmsEntDeptService;
import com.imile.hrms.service.organization.HrmsEntOcService;
import com.imile.hrms.service.organization.HrmsEntPostService;
import com.imile.hrms.service.organization.result.HrmsSettlementCenterVO;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.openapi.api.kingdee.NewKingdeeResult;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service(version = "1.0.0")
@Slf4j
public class HrmsBizOrganizationInfoApiImpl implements BizOrganizationInfoApi {

    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;

    @Autowired
    private HrmsDeptManage hrmsDeptManage;

    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;

    @Autowired
    private HrmsEntPostService hrmsEntPostService;

    @Autowired
    private EntOcService entOcService;

    @Autowired
    private HrmsBaseDataService hrmsBaseDataService;

    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;

    @Autowired
    private ConverterService converterService;

    @Autowired
    private HrmsEntProjectDao hrmsEntProjectDao;

    @Autowired
    private HrmsPlatformRelationDao hrmsPlatformRelationDao;

    @Autowired
    private KingdeeIntegration kingdeeIntegration;

    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    @Autowired
    private HrmsCountryConfigDao hrmsCountryConfigDao;

    @Autowired
    private HrmsEntOcService hrmsEntOcService;

    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;

    @Resource
    private HrmsProperties hrmsProperties;

    @Autowired
    private HrmsEntDeptService hrmsEntDeptService;

    @Override
    public RpcResult<List<OrgUserInfoApiDTO>> getUserInfoByUserCodeList(List<String> userCodeList) {
        List<HrmsUserInfoDO> hrmsUserInfoDOSList = hrmsUserInfoManage.selectUserInfoByCodes(userCodeList);
        if (ObjectUtil.isEmpty(hrmsUserInfoDOSList)) {
            return RpcResult.ok(Collections.EMPTY_LIST);
        }
        return ObjectUtil.isNotEmpty(hrmsUserInfoDOSList) ? RpcResult.ok(buildUserApiInfo(hrmsUserInfoDOSList)) : RpcResult.ok(Collections.EMPTY_LIST);
    }

    @Override
    public RpcResult<List<Long>> getDeptIdInfoByBizArea(Integer bizArea) {
        if (ObjectUtil.isEmpty(bizArea)) {
            return RpcResult.ok(Collections.EMPTY_LIST);
        }
        return RpcResult.ok(hrmsEntDeptDao.getDeptIdByBizArea(bizArea));
    }

    @Override
    public RpcResult<List<OrgUserInfoApiDTO>> getUserInfoByBizArea(Integer bizArea) {
        if (ObjectUtil.isEmpty(bizArea)) {
            return RpcResult.ok(Collections.EMPTY_LIST);
        }
        List<Long> deptIdList = hrmsEntDeptDao.getDeptIdByBizArea(bizArea);
        List<HrmsUserInfoDO> result = getHrmsUserInfoDOSByDeptIdList(deptIdList);
        return ObjectUtil.isNotEmpty(result) ? RpcResult.ok(buildUserApiInfo(result)) : RpcResult.ok(Collections.EMPTY_LIST);
    }


    @Override
    public RpcResult<List<OrgUserInfoApiDTO>> getUserInfoByDeptIds(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsUserInfoDO> userInfoDOList = getHrmsUserInfoDOSByDeptIdList(deptIds);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        return RpcResult.ok(buildUserApiInfo(userInfoDOList));
    }

    @NotNull
    private List<HrmsUserInfoDO> getHrmsUserInfoDOSByDeptIdList(List<Long> deptIds) {
        if (ObjectUtil.isEmpty(deptIds)) {
            return Collections.EMPTY_LIST;
        }
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoManage.selectByDeptId(deptIds);
        userInfoDOList = userInfoDOList.stream().filter(u -> StringUtils.equalsIgnoreCase(u.getStatus(), StatusEnum.ACTIVE.getCode()) &&
                StringUtils.equalsIgnoreCase(u.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()) && StringUtils.isNotBlank(u.getUserCode())).collect(Collectors.toList());
        return userInfoDOList;
    }

    @Override
    public RpcResult<List<String>> getUserCodesByDeptIds(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsUserInfoDO> userInfoDOList = getHrmsUserInfoDOSByDeptIdList(deptIds);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        return RpcResult.ok(userInfoDOList.stream().map(HrmsUserInfoDO::getUserCode).collect(Collectors.toList()));
    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getDeptInfo(String country) {
        List<HrmsEntDeptDO> deptDOList = hrmsEntDeptDao.selectByBizCountry(country);
        if (CollectionUtils.isEmpty(deptDOList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        return RpcResult.ok(buildDeptApiInfo(deptDOList));
    }


    private RpcResult<List<OrgDeptApiDTO>> getDeptListByCountry(String country, Function<String, List<HrmsEntDeptBO>> getList) {
        String excludeTestCountry = hrmsProperties.getEntry().getExcludeTestCountry();
        if (StringUtils.isBlank(country) || Objects.equals(country, excludeTestCountry)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntDeptBO> deptList = getList.apply(country).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        return RpcResult.ok(buildOrgDeptApi(deptList));

    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getStationByCountry(String country) {
        return getDeptListByCountry(country, hrmsDeptManage::selectOcDeptTile);
    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getDeptByCountry(String country) {
        return getDeptListByCountry(country, hrmsDeptManage::selectDeptTile);
    }

    @Override
    public RpcResult<List<OrgSettlementCenterDTO>> getSettlement() {
        List<HrmsSettlementCenterVO> settlementDTOS = hrmsBaseDataService.getSettlementCenterSelectList();
        return RpcResult.ok(HrmsCollectionUtils.convert(settlementDTOS, OrgSettlementCenterDTO.class));
    }

    @Override
    public RpcResult<List<OrgUserInfoApiDTO>> getUserBySettlement(List<Long> companyOrgIds) {
        List<HrmsSettlementCenterVO> settlementList = hrmsBaseDataService.getSettlementCenterSelectList();
        if (CollectionUtils.isEmpty(settlementList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<String> settlementCenterCodes = settlementList.stream().filter(s -> companyOrgIds.contains(s.getCompanyOrgId())).map(HrmsSettlementCenterVO::getSettlementCenterCode).collect(Collectors.toList());

        UserDaoQuery build = UserDaoQuery.builder()
                .settlementCenterCodes(settlementCenterCodes)
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .build();
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.userList(build);

        return RpcResult.ok(buildUserApiInfo(userInfoDOList));
    }

    @Override
    public RpcResult<List<OrgSettlementCenterDTO>> getSettlementByOrgId(List<Long> companyOrgIds) {
        List<HrmsSettlementCenterVO> settlementDTOS = hrmsBaseDataService.getSettlementCenterSelectList();
        if (CollectionUtils.isEmpty(settlementDTOS)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsSettlementCenterVO> settlementDTOList = settlementDTOS.stream().filter(s -> companyOrgIds.contains(s.getCompanyOrgId())).collect(Collectors.toList());
        return RpcResult.ok(HrmsCollectionUtils.convert(settlementDTOList, OrgSettlementCenterDTO.class));
    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getDeptByIds(List<Long> deptIds) {
        List<HrmsEntDeptDO> hrmsEntDeptDOS = hrmsDeptManage.selectDeptByIds(deptIds);
        if (CollectionUtils.isEmpty(hrmsEntDeptDOS)) {
            return RpcResult.ok(new ArrayList<>());
        }
        return RpcResult.ok(buildDeptApiInfo(hrmsEntDeptDOS));
    }

//    @Override
//    public RpcResult<List<OrgCompanyApiDTO>> getCompanyByIds(List<Long> companyIds) {
//
//        List<HrmsEntCompanyDO> companyDOList = hrmsEntCompanyDao.selectByIds(companyIds);
//        if (CollectionUtils.isEmpty(companyDOList)) {
//            return RpcResult.ok(new ArrayList<>());
//        }
//        companyDOList.forEach(c -> {
//            if (c.getId() == 100) {
//                c.setCountry("HQ");
//            }
//        });
//        return RpcResult.ok(HrmsCollectionUtils.convert(companyDOList, OrgCompanyApiDTO.class));
//    }

//    @Override
//    public RpcResult<List<OrgCompanyApiDTO>> selectAllCompany() {
//        List<HrmsEntCompanyDO> companyDOList = hrmsEntCompanyDao.selectAllList();
//        if (CollectionUtils.isEmpty(companyDOList)) {
//            return RpcResult.ok(new ArrayList<>());
//        }
//        companyDOList.forEach(c -> {
//            if (c.getId() == 100) {
//                c.setCountry("HQ");
//            }
//        });
//        return RpcResult.ok(HrmsCollectionUtils.convert(companyDOList, OrgCompanyApiDTO.class));
//    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getStationByCodes(List<String> ocCodes) {
        if (CollectionUtils.isEmpty(ocCodes)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<EntOcApiDTO> ocByCodes = entOcService.getOcByCodes(null, ocCodes);
        if (CollectionUtils.isEmpty(ocByCodes)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<Long> ocIds = ocByCodes.stream().map(EntOcApiDTO::getId).collect(Collectors.toList());
        List<HrmsEntDeptDO> entDeptDOS = hrmsDeptManage.getOcByIds(ocIds);
        return RpcResult.ok(buildDeptApiInfo(entDeptDOS));
    }

    @Override
    public RpcResult<OrgUserInfoDetailApiDTO> userInfoDetail(String userCode) {
        UserDTO userDTO = hrmsUserInfoService.getCrmUserByUserCode(userCode);
        if (userDTO == null) {
            return RpcResult.ok(null);
        }
        OrgUserInfoDetailApiDTO orgUserInfoDetailApiDTO = BeanUtil.copyProperties(userDTO, OrgUserInfoDetailApiDTO.class);
        orgUserInfoDetailApiDTO.setCountry(orgUserInfoDetailApiDTO.getOriginCountry());
        HrmsUserInfoDO byUserCode = hrmsUserInfoDao.getByUserCode(userDTO.getUserCode());
        if (byUserCode != null) {
            orgUserInfoDetailApiDTO.setSettlementCenterCode(byUserCode.getSettlementCenterCode());
            orgUserInfoDetailApiDTO.setUserId(byUserCode.getId());
            return RpcResult.ok(orgUserInfoDetailApiDTO);
        }
        return RpcResult.ok(null);
    }

    @Override
    public RpcResult<List<OrgUserInfoDetailApiDTO>> userInfoDetailByUserCodes(List<String> userCodes) {
        List<UserDTO> userDTOS = hrmsUserInfoService.listUserInfoByUserCode(userCodes);
        if (CollectionUtils.isEmpty(userDTOS)) {
            return RpcResult.ok(new ArrayList<>());
        }
        converterService.withAnnotation(userDTOS);
        List<OrgUserInfoDetailApiDTO> infoDetailApiDTOS = HrmsCollectionUtils.convert(userDTOS, OrgUserInfoDetailApiDTO.class);
        List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.userListByUserCodes(userCodes);
        if (CollectionUtils.isNotEmpty(userInfoDOList)) {
            Map<String, HrmsUserInfoDO> userInfoDOMap = userInfoDOList.stream().collect(Collectors.toMap(o -> o.getUserCode(), o -> o, (v1, v2) -> v1));
            infoDetailApiDTOS.forEach(u -> {
                HrmsUserInfoDO userInfoDO = userInfoDOMap.get(u.getUserCode());
                if (userInfoDO != null) {
                    u.setSettlementCenterCode(userInfoDO.getSettlementCenterCode());
                    u.setUserId(userInfoDO.getId());
                }
            });
            return RpcResult.ok(infoDetailApiDTOS);
        }

        return RpcResult.ok(new ArrayList<>());

    }

    @Override
    public RpcResult<List<OrgProjectDTO>> getProjectInfoByCode(List<String> projectCodes) {
        List<HrmsEntProjectDO> projectDOS = hrmsEntProjectDao.getByProjectCode(projectCodes);
        return RpcResult.ok(HrmsCollectionUtils.convert(projectDOS, OrgProjectDTO.class));
    }

    @Override
    public RpcResult<Boolean> syncCountryKingdee(SyncCountryKingdeeDTO dto) {
        if (!dto.getIsDelete()) {
            build2KingdeeCountryData(dto, dto.getIsAdd());
            return RpcResult.ok(true);
        }
        kingdeeIntegration.noAuditAndStatusSwitch(String.valueOf(dto.getId()), RelationBizTypeEnum.KINGDEE_COUNTRY.getCode(), "", BusinessConstant.PLAT_FORM_HERMES, StatusEnum.DISABLED.getCode(), KingdeeConstant.BusKey.COUNTRY_KEY);
        return RpcResult.ok(true);
    }

    @Override
    public RpcResult<List<OrgDeptTreeApiDTO>> selectTreeByDeptIds(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntDeptDO> allDept = hrmsDeptManage.getAllDept();

        List<HrmsEntDeptDO> parentDeptList = allDept.stream().filter(o -> deptIds.contains(o.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentDeptList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<OrgDeptTreeApiDTO> parentDepts = BeanUtils.convert(OrgDeptTreeApiDTO.class, parentDeptList);

        buildSubDept(allDept, parentDepts);

        return RpcResult.ok(parentDepts);
    }

    @Override
    public RpcResult<PaginationResult<SelectUserApiDTO>> selectUserList(SelectUserApiQuery userApiQuery) {
        SelectUserPageQuery query = BeanUtils.convert(userApiQuery, SelectUserPageQuery.class);
        //是否需要做子公司数据隔离查询员工
        query.setIsNeedSeparate(Optional.ofNullable(query.getIsNeedSeparate()).orElse(Boolean.FALSE));
        PaginationResult<SelectUserDTO> result = hrmsUserInfoService.selectUserList(query);
        return RpcResult.ok(BeanUtils.convert(SelectUserApiDTO.class, result));
    }

    @Override
    public RpcResult<List<OrgPostInfoApiDTO>> listByPostIds(List<Long> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntPostDO> postDOList = hrmsEntPostDao.listByPostList(postIds);

        List<OrgPostInfoApiDTO> resList = new ArrayList<>();

        postDOList.forEach(p -> {
            OrgPostInfoApiDTO orgPostInfoApiDTO = new OrgPostInfoApiDTO();
            orgPostInfoApiDTO.setPostId(p.getId());
            orgPostInfoApiDTO.setPostName(RequestInfoHolder.isChinese() ? p.getPostNameCn() : p.getPostNameEn());
            resList.add(orgPostInfoApiDTO);
        });

        return RpcResult.ok(resList);
    }

    @Override
    public RpcResult<List<EntDeptInfoByLevelApiDTO>> listByParentId(Long parentId) {
        if (parentId == null) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntDeptDO> deptDOS = hrmsDeptManage.listByParentId(parentId);

        return RpcResult.ok(BeanUtils.convert(EntDeptInfoByLevelApiDTO.class, deptDOS));
    }

    @Override
    public RpcResult<List<EntOcOperationRecordDTO>> listUnhandledOperationRecord4Hermes() {
        return RpcResult.ok(hrmsEntOcService.listUnhandledOperationRecord4Hermes());
    }

    @Override
    public RpcResult<CountryConfigApiDTO> getCountryConfig(CountryConfigApiDTO dto) {
        CountryConfigApiDTO resDTO = new CountryConfigApiDTO();

        List<HrmsCountryConfigDO> countryConfig = new ArrayList<>();
        if (StringUtils.isNotBlank(dto.getCountry())) {
            String countryCode = CountryCodeEnum.convert2StandardCountryCode(dto.getCountry());
            countryConfig = hrmsCountryConfigDao.getCountryConfig(CountryQuery.builder().countryCode(countryCode).build());
        }

        if (StringUtils.isNotBlank(dto.getCountryCallingCode())) {
            countryConfig = hrmsCountryConfigDao.getCountryConfig(CountryQuery.builder().countryCallingCode(dto.getCountryCallingCode()).build());
        }

        if (CollectionUtils.isEmpty(countryConfig)) {
            return RpcResult.ok(null);
        }
        resDTO.setCountryCallingId(countryConfig.get(0).getId());
        resDTO.setCountryCallingCode(countryConfig.get(0).getCountryCallingCode());
        return RpcResult.ok(resDTO);
    }

    @Override
    public RpcResult<List<DeptRegionInfoApiDTO>> getRegion(String country) {
        if (StringUtils.isBlank(country)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<DeptRegionInfoDTO> regionList = hrmsEntDeptService.getRegion(country);

        return RpcResult.ok(com.imile.util.BeanUtils.convert(DeptRegionInfoApiDTO.class, regionList));
    }

    /**
     * 一级部门列表
     */
    @Value("#{'${one.dept.id.list:1032437,1032652,1033895,1034067,1034067,1034274,1034396,1034450,1034587,1034699,1034755,1034810,1034935,1035635,1035782,1035930,1036116,1036175,1036175}'.split(',')}")
    private List<Long> oneDeptIdList;

    /**
     * 二级部门列表
     */
    @Value("#{'${two.dept.id.list:1035925,1035926,1035927}'.split(',')}")
    private List<Long> twoDeptIdList;

    @Override
    public RpcResult<List<EntDeptInfoByLevelApiDTO>> getOneDeptForPms(Integer isFindTwo) {
        if (!Objects.equals(isFindTwo, 1)) {
            return RpcResult.ok(getDeptForPms(oneDeptIdList));
        } else {
            return RpcResult.ok(getDeptForPms(twoDeptIdList));
        }
    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getDeptByIdList(List<Long> deptIdList) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(deptIdList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntDeptDO> deptList = hrmsEntDeptDao.selectByIdListWithDelete(deptIdList, null);
        List<OrgDeptApiDTO> orgDeptList = deptList.stream()
                .map(dept -> {
                    OrgDeptApiDTO orgDept = new OrgDeptApiDTO();
                    orgDept.setId(dept.getId());
                    orgDept.setOrgId(dept.getOrgId());
                    orgDept.setDeptCode(dept.getDeptCode());
                    orgDept.setOcCenterCode(dept.getOcCenterCode());
                    orgDept.setCountry(dept.getCountry());
                    orgDept.setDeptNameEn(dept.getDeptNameEn());
                    orgDept.setDeptNameCn(dept.getDeptNameCn());
                    orgDept.setOrganizationCode("");
                    orgDept.setOcId(dept.getOcId());
                    orgDept.setStatus(dept.getStatus());
                    orgDept.setDeleteFlag(dept.getIsDelete());
                    return orgDept;
                }).collect(Collectors.toList());
        return RpcResult.ok(orgDeptList);
    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> getStationByCodeList(List<String> ocCodeList) {
        if (CollectionUtils.isEmpty(ocCodeList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<EntOcApiDTO> entOcList = entOcService.getOcByCodes(null, ocCodeList);
        if (CollectionUtils.isEmpty(entOcList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<Long> ocIdList = entOcList.stream().map(EntOcApiDTO::getId).collect(Collectors.toList());
        List<HrmsEntDeptDO> ocDeptList = hrmsEntDeptDao.selectByIdListWithDelete(null, ocIdList);

        Map<Long, EntOcApiDTO> entOcMap = entOcList.stream()
                .collect(Collectors.toMap(EntOcApiDTO::getId, Function.identity(), (v1, v2) -> v1));

        List<OrgDeptApiDTO> orgOcList = ocDeptList.stream()
                .map(dept -> {
                    OrgDeptApiDTO orgOc = new OrgDeptApiDTO();
                    EntOcApiDTO entOcApiDTO = entOcMap.getOrDefault(dept.getOcId(), new EntOcApiDTO());
                    orgOc.setOcCode(entOcApiDTO.getOcCode());
                    orgOc.setId(dept.getId());
                    orgOc.setOrgId(dept.getOrgId());
                    orgOc.setDeptCode(dept.getDeptCode());
                    orgOc.setOcCenterCode(dept.getOcCenterCode());
                    orgOc.setCountry(dept.getCountry());
                    orgOc.setDeptNameEn(dept.getDeptNameEn());
                    orgOc.setDeptNameCn(dept.getDeptNameCn());
                    orgOc.setOrganizationCode("");
                    orgOc.setOcId(dept.getOcId());
                    orgOc.setStatus(dept.getStatus());
                    orgOc.setDeleteFlag(dept.getIsDelete());
                    return orgOc;
                }).collect(Collectors.toList());
        return RpcResult.ok(orgOcList);
    }

    @Override
    public RpcResult<List<OrgProjectDTO>> getProjectByCodeList(List<String> projectCodeList) {
        if (CollectionUtils.isEmpty(projectCodeList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntProjectDO> projectList = hrmsEntProjectDao.selectByCodeListWithDelete(projectCodeList);
        List<OrgProjectDTO> orgProjectList = projectList.stream().filter(Objects::nonNull).map(project -> {
            OrgProjectDTO orgProject = new OrgProjectDTO();
            orgProject.setId(project.getId());
            orgProject.setProjectCode(project.getProjectCode());
            orgProject.setProjectName(project.getProjectName());
            orgProject.setCountry(project.getCountry());
            orgProject.setAuditStatus(project.getAuditStatus());
            orgProject.setDeleteFlag(project.getIsDelete());
            return orgProject;
        }).collect(Collectors.toList());
        return RpcResult.ok(orgProjectList);
    }

    @Override
    public RpcResult<List<OrgPostInfoApiDTO>> getPostList() {
        List<HrmsEntPostDO> postList = hrmsEntPostDao.getAllPostList();
        List<OrgPostInfoApiDTO> resList = new ArrayList<>();
        postList.forEach(p -> {
            OrgPostInfoApiDTO orgPostInfoApiDTO = new OrgPostInfoApiDTO();
            orgPostInfoApiDTO.setPostId(p.getId());
            orgPostInfoApiDTO.setPostName(RequestInfoHolder.isChinese() ? p.getPostNameCn() : p.getPostNameEn());
            resList.add(orgPostInfoApiDTO);
        });
        return RpcResult.ok(resList);
    }

    @Override
    public RpcResult<List<OrgPostInfoApiDTO>> getByPostName(String postName) {
        List<HrmsEntPostDO> postList = hrmsEntPostDao.getPostNameList(postName);
        List<OrgPostInfoApiDTO> resList = new ArrayList<>();
        postList.forEach(p -> {
            OrgPostInfoApiDTO orgPostInfoApiDTO = new OrgPostInfoApiDTO();
            orgPostInfoApiDTO.setPostId(p.getId());
            orgPostInfoApiDTO.setPostName(RequestInfoHolder.isChinese() ? p.getPostNameCn() : p.getPostNameEn());
            resList.add(orgPostInfoApiDTO);
        });
        return RpcResult.ok(resList);
    }

    @Override
    public RpcResult<PaginationResult<EntPostApiDTO>> getPostPage(PostApiQuery postApiQuery) {
        PostQuery query = BeanUtils.convert(postApiQuery, PostQuery.class);
        PaginationResult<EntPostDTO> entPostDTOPaginationResult = hrmsEntPostService.getPostPage(query);
        PaginationResult<EntPostApiDTO> result =
                PaginationResult.get(
                        com.imile.util.BeanUtils.convert(EntPostApiDTO.class, entPostDTOPaginationResult.getResults()),
                        entPostDTOPaginationResult.getPagination()
                );
        return RpcResult.ok(result);
    }

    @Override
    public RpcResult<List<UserInfoApiDTO>> selectUserInfoList(SelectUserInfoApiQuery selectUserInfoApiQuery) {
        UserQuery query = BeanUtils.convert(selectUserInfoApiQuery, UserQuery.class);
        List<UserListInfoDTO> userListInfoDTOS = hrmsUserInfoManage.selectUserInfoList(query);
        return RpcResult.ok(com.imile.util.BeanUtils.convert(UserInfoApiDTO.class, userListInfoDTOS));
    }

    @Override
    public RpcResult<PaginationResult<UserInfoApiDTO>> selectUserInfoPage(SelectUserInfoApiQuery selectUserInfoApiQuery) {
        UserQuery query = BeanUtils.convert(selectUserInfoApiQuery, UserQuery.class);
        PaginationResult<UserListInfoDTO> userListInfoDTOResult = hrmsUserInfoService.selectUserInfoPage(query);
        PaginationResult<UserInfoApiDTO> userListInfoDTOPaginationResult =
                PaginationResult.get(
                        com.imile.util.BeanUtils.convert(UserInfoApiDTO.class, userListInfoDTOResult.getResults()),
                        userListInfoDTOResult.getPagination()
                );
        return RpcResult.ok(userListInfoDTOPaginationResult);
    }

    @Override
    public RpcResult<List<OrgDeptApiDTO>> selectAllDept() {
        List<HrmsEntDeptDO> entDeptList = hrmsEntDeptDao.selectAllDept();
        if (CollectionUtils.isEmpty(entDeptList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<HrmsEntDeptDO> filterList = entDeptList.stream().filter(o -> !StringUtils.equalsIgnoreCase("TEST", o.getCountry())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return RpcResult.ok(new ArrayList<>());
        }
        List<OrgDeptApiDTO> orgDeptList = filterList.stream()
                .map(dept -> {
                    OrgDeptApiDTO orgDept = new OrgDeptApiDTO();
                    orgDept.setId(dept.getId());
                    orgDept.setOrgId(dept.getOrgId());
                    orgDept.setDeptCode(dept.getDeptCode());
                    orgDept.setOcCenterCode(dept.getOcCenterCode());
                    orgDept.setCountry(dept.getCountry());
                    orgDept.setDeptNameEn(dept.getDeptNameEn());
                    orgDept.setDeptNameCn(dept.getDeptNameCn());
                    orgDept.setOrganizationCode("");
                    orgDept.setOcId(dept.getOcId());
                    orgDept.setStatus(dept.getStatus());
                    orgDept.setDeleteFlag(dept.getIsDelete());
                    return orgDept;
                }).collect(Collectors.toList());
        return RpcResult.ok(orgDeptList);
    }

    private List<EntDeptInfoByLevelApiDTO> getDeptForPms(List<Long> deptIdList) {
        if (CollectionUtils.isEmpty(deptIdList)) {
            return new ArrayList<>();
        }
        List<HrmsEntDeptDO> deptList = hrmsDeptManage.selectDeptByIdsAll(deptIdList);
        return BeanUtils.convert(EntDeptInfoByLevelApiDTO.class, deptList);
    }

    private void buildSubDept(List<HrmsEntDeptDO> allDept, List<OrgDeptTreeApiDTO> deptList) {
        for (OrgDeptTreeApiDTO item : deptList) {
            List<HrmsEntDeptDO> tempDeptList = allDept.stream().filter(o -> item.getId().compareTo(o.getParentId()) == 0).collect(Collectors.toList());
            List<OrgDeptTreeApiDTO> subDeptList = BeanUtils.convert(OrgDeptTreeApiDTO.class, tempDeptList);
            if (CollectionUtils.isNotEmpty(subDeptList)) {
                item.setSubDeptList(subDeptList);
                buildSubDept(allDept, subDeptList);
            }
        }
    }

    /**
     * 构建返回值
     */
    private List<OrgDeptApiDTO> buildOrgDeptApi(List<HrmsEntDeptBO> hrmsEntDeptBOList) {
        List<OrgDeptApiDTO> orgDeptApiDTOList = new ArrayList<>();
        hrmsEntDeptBOList.forEach(d -> {
            OrgDeptApiDTO orgDeptApiDTO = BeanUtils.convert(d, OrgDeptApiDTO.class);
            List<OrgDeptAttributeApiDTO> deptAttributeApiDTOList = HrmsCollectionUtils.convert(d.getAttributeList(), OrgDeptAttributeApiDTO.class);
            orgDeptApiDTO.setDeptAttributeApiList(deptAttributeApiDTOList);
            orgDeptApiDTOList.add(orgDeptApiDTO);
        });
        return orgDeptApiDTOList;
    }

    private List<OrgDeptApiDTO> buildDeptApiInfo(List<HrmsEntDeptDO> deptDOList) {
        log.info("buildDeptApiInfo | deptDOList:{}", JSON.toJSON(deptDOList));

        List<Long> ocIds = deptDOList.stream().filter(o -> o.getOcId() != null).map(HrmsEntDeptDO::getOcId).collect(Collectors.toList());
        List<EntOcApiDTO> ocApiDTOS = CollectionUtils.isEmpty(ocIds) ? new ArrayList<>() : entOcService.getOcByIds(BusinessConstant.DEFAULT_ORG_ID, ocIds);
        log.info("buildDeptApiInfo | ocIds:{}, deptDOList:{}", JSON.toJSONString(ocIds), JSON.toJSONString(ocApiDTOS));

        Map<Long, EntOcApiDTO> apiDTOMap = ocApiDTOS.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));

        Map<Long, List<HrmsDeptAttributeDO>> deptAttributeMap = hrmsDeptManage.getDeptAttributeMap(deptDOList);
        List<OrgDeptApiDTO> deptApiList = HrmsCollectionUtils.convert(deptDOList, OrgDeptApiDTO.class);

        deptApiList.forEach(d -> {
            EntOcApiDTO entOcApiDTO = apiDTOMap.get(d.getOcId());
            if (entOcApiDTO != null) {
                d.setOcCode(entOcApiDTO.getOcCode());
            }
            List<HrmsDeptAttributeDO> deptAttributeDOS = deptAttributeMap.get(d.getId());
            if (CollectionUtils.isNotEmpty(deptAttributeDOS)) {
                d.setDeptAttributeApiList(HrmsCollectionUtils.convert(deptAttributeDOS, OrgDeptAttributeApiDTO.class));
            }
        });
        return deptApiList;
    }

    /**
     * 构建人员返回数据
     *
     * @param userInfoDOList
     * @return
     */
    private List<OrgUserInfoApiDTO> buildUserApiInfo(List<HrmsUserInfoDO> userInfoDOList) {
        List<OrgUserInfoApiDTO> orgUserInfoApiDTOList = HrmsCollectionUtils.convert(userInfoDOList, OrgUserInfoApiDTO.class);
        List<Long> deptIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getDeptId).distinct().collect(Collectors.toList());
        List<HrmsEntDeptDO> deptDOList = hrmsDeptManage.selectDeptByIds(deptIdList);
        Map<Long, HrmsEntDeptDO> entDeptDOMap = deptDOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));

        List<Long> postIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getPostId).distinct().collect(Collectors.toList());
        List<HrmsEntPostDO> postDOList = hrmsEntPostDao.listByPostList(postIdList);
        Map<Long, HrmsEntPostDO> entPostDOMap = postDOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));

        orgUserInfoApiDTOList.forEach(u -> {
            HrmsEntDeptDO deptDO = entDeptDOMap.get(u.getDeptId());
            if (deptDO != null) {
                u.setDeptNameCn(deptDO.getDeptNameCn());
                u.setDeptNameEn(deptDO.getDeptNameEn());
                u.setOrganizationCode("");
            }
            HrmsEntPostDO postDO = entPostDOMap.get(u.getPostId());
            if (postDO != null) {
                u.setPostNameCn(postDO.getPostNameCn());
                u.setPostNameEn(postDO.getPostNameEn());
            }
        });
        return orgUserInfoApiDTOList;
    }


    /**
     * 构建对接金蝶数据
     *
     * @param dto
     * @param isAdd
     */
    private void build2KingdeeCountryData(SyncCountryKingdeeDTO dto, boolean isAdd) {
        Long relationId = 0L;
        if (!isAdd) {
            HrmsPlatformRelationDO relationDO = hrmsPlatformRelationDao.getPlatFormRelation(String.valueOf(dto.getId()), RelationBizTypeEnum.KINGDEE_COUNTRY.getCode(), "", BusinessConstant.PLAT_FORM_HERMES);
            if (relationDO == null || StringUtils.isBlank(relationDO.getRelationId())) {
                throw BusinessException.get(HrmsErrorCodeEnums.RELATION_PLATFORM_NOT_EXIST.getDesc(), I18nUtils.getMessage(HrmsErrorCodeEnums.RELATION_PLATFORM_NOT_EXIST.getDesc()));
            }
            relationId = Long.parseLong(relationDO.getRelationId());
            //若不为空,修改信息前说明已经是审核通过了，需要先做反审核操作
            UnAuditDTO unAuditDTO = new UnAuditDTO();
            unAuditDTO.setId(relationId);
            kingdeeIntegration.unAudit(unAuditDTO, KingdeeConstant.BusKey.COUNTRY_KEY);
        }
        KingDeeCountryInfoDTO countryInfoDTO = new KingDeeCountryInfoDTO();
        countryInfoDTO.setId(relationId);
        countryInfoDTO.setCountry(dto.getCountry());
        countryInfoDTO.setCountryName(KingdeeUtil.convertKingDeeName(dto.getCountryNameCn(), dto.getCountryNameEn()));
        countryInfoDTO.setType("Country");

        NewKingdeeResult kingdeeResult = kingdeeIntegration.syncOfCountryInfo(countryInfoDTO);

        if (isAdd && StringUtils.isNotBlank(kingdeeResult.getId())) {
            HrmsPlatformRelationDO relation = new HrmsPlatformRelationDO();
            relation.setRelationId(kingdeeResult.getId());
            relation.setBizId(dto.getId().toString());
            relation.setBizType(RelationBizTypeEnum.KINGDEE_COUNTRY.getCode());
            relation.setPlatformType(BusinessConstant.PLAT_FORM_HERMES);
            relation.setStatus(StatusEnum.ACTIVE.getCode());
            relation.setIsLatest(BusinessConstant.Y);
            relation.setId(iHrmsIdWorker.nextId());
            this.fillDOInsert(relation);
            hrmsPlatformRelationDao.save(relation);
        }
    }

    public void fillDOInsert(BaseDO baseDO) {
        UserContext userInfo = RequestInfoHolder.getLoginInfo();
        baseDO.setCreateDate(new Date());
        baseDO.setCreateUserCode(userInfo.getUserCode());
        baseDO.setCreateUserName(userInfo.getUserName());
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setLastUpdUserCode(userInfo.getUserCode());
        baseDO.setLastUpdUserName(userInfo.getUserName());
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }
}
