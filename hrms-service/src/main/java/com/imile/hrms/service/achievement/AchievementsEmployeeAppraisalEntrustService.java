package com.imile.hrms.service.achievement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.achievement.model.AchievementsEmployeeAppraisalEntrustDO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustCancelParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustCheckResultDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustCreateParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustHandleParamDTO;
import com.imile.hrms.service.achievement.dto.AchievementsEmployeeAppraisalEntrustPrepareCheckParamDTO;

import java.util.List;

/**
 * <p>
 * 员工考核委托表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
public interface AchievementsEmployeeAppraisalEntrustService extends IService<AchievementsEmployeeAppraisalEntrustDO> {

    /**
     * 考核委托预校验
     *
     * @param param AchievementsEmployeeAppraisalEntrustPrepareCheckParamDTO
     * @return AchievementsEmployeeAppraisalEntrustCheckResultDTO
     */
    AchievementsEmployeeAppraisalEntrustCheckResultDTO prepareCheck(AchievementsEmployeeAppraisalEntrustPrepareCheckParamDTO param);

    /**
     * 发起考核委托
     *
     * @param param AchievementsEmployeeAppraisalEntrustCreateParamDTO
     * @return Boolean
     */
    Boolean createEntrust(AchievementsEmployeeAppraisalEntrustCreateParamDTO param);

    /**
     * 取消考核委托
     *
     * @param param AchievementsEmployeeAppraisalEntrustCancelParamDTO
     * @return Boolean
     */
    Boolean cancelEntrust(AchievementsEmployeeAppraisalEntrustCancelParamDTO param);

    /**
     * 处理考核委托
     *
     * @param param AchievementsEmployeeAppraisalEntrustHandleParamDTO
     * @return Boolean
     */
    Boolean handleEntrust(AchievementsEmployeeAppraisalEntrustHandleParamDTO param);

    /**
     * 根据员工考核ID列表获取
     *
     * @param employeeAppraisalIdList 员工考核ID列表
     * @return List<AchievementsEmployeeAppraisalDO>
     */
    List<AchievementsEmployeeAppraisalEntrustDO> listByEmployeeAppraisalIdList(List<Long> employeeAppraisalIdList);
}
