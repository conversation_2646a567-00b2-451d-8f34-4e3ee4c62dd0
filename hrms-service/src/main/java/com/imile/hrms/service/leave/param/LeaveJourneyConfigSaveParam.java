package com.imile.hrms.service.leave.param;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * {@code @author:} han.wang
 * {@code @className:} LeaveJourneyConfigSaveParam
 * {@code @since:} 2024-12-12 20:52
 * {@code @description:}
 */
@Data
@ApiModel(description = "国家福利假期：路途假保存入参")
public class LeaveJourneyConfigSaveParam {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家发放规则表主键id
     */
    private Long issueRuleId;

    /**
     * 国家
     */
    private String country;

    /**
     * 路程假天数
     */
    private BigDecimal journeyDays;

}
