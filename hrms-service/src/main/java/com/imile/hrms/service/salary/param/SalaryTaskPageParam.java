package com.imile.hrms.service.salary.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 计算任务列表查询
 *
 * <AUTHOR>
 * @since 2023/12/15
 */
@Data
public class SalaryTaskPageParam {

    /**
     * 国家
     */
    @NotEmpty
    private String country;

    /**
     * 年
     */
    @NotNull
    private Integer year;

    /**
     * 月
     */
    @NotNull
    private Integer month;

}
