package com.imile.hrms.service.log.impl;

import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.dao.base.dao.OperationDao;
import com.imile.hrms.dao.base.model.OperationDO;
import com.imile.hrms.dao.base.model.condition.OperationConditionBuilder;
import com.imile.hrms.service.log.OperationService;
import com.imile.hrms.service.log.param.OperationQueryParam;
import com.imile.hrms.service.log.result.OperationBO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/3
 */
@Service
public class OperationServiceImpl implements OperationService {

    @Resource
    private OperationDao operationDao;

    @Override
    public List<OperationBO> getOperationSelectList(OperationQueryParam param) {
        List<OperationDO> operationList = operationDao.selectByCondition(OperationConditionBuilder.builder()
                .operationModuleCode(param.getOperationModuleCode())
                .operationSceneCodeList(param.getOperationSceneCodeList())
                .build());
        return operationList.stream()
                .map(s -> OperationBO.builder()
                        .operationModuleCode(s.getOperationModuleCode())
                        .operationSceneCode(s.getOperationSceneCode())
                        .operationCode(s.getOperationCode())
                        .operationName(RequestInfoHolder.isChinese() ? s.getOperationNameCn() : s.getOperationNameEn())
                        .build())
                .collect(Collectors.toList());
    }
}
