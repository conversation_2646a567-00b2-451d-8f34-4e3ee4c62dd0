package com.imile.hrms.service.approval;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.approval.dto.OverTimeApprovalListDTO;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.approval.param.OverTimeAddParam;
import com.imile.hrms.service.approval.param.OverTimeCalcParam;
import com.imile.hrms.service.approval.param.OverTimeImportAddListParam;
import com.imile.hrms.service.approval.param.OverTimeListParam;
import com.imile.hrms.service.approval.vo.ApprovalResultListVO;
import com.imile.hrms.service.approval.vo.ApprovalResultVO;
import com.imile.hrms.service.approval.vo.OverTimeApprovalCloverListVO;
import com.imile.hrms.service.approval.vo.OverTimeApprovalFromListVO;
import com.imile.hrms.service.approval.vo.OverTimeApprovalFromVO;
import com.imile.hrms.service.approval.vo.UserOverTimeImportVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsApprovalFormService
 * {@code @since:} 2024-06-12 14:25
 * {@code @description:} 
 */
public interface HrmsApprovalFormService{

    /**
     * 加班申请
     *
     * @param param
     * @return
     */
    ApprovalResultVO overTimeAdd(OverTimeAddParam param);

    ApprovalResultVO overTimeUpdate(OverTimeAddParam param);

    List<UserOverTimeImportVO> overTimeImportImport(List<UserOverTimeImportVO> importOverTimeList);

    ApprovalResultListVO overTimeImportAdd(OverTimeImportAddListParam param);

    OverTimeApprovalFromVO getApprovalFromDetail(Long approvalFormId);

    PaginationResult<OverTimeApprovalFromListVO> list(OverTimeListParam param);

    PaginationResult<OverTimeApprovalListDTO> cloverList(OverTimeListParam param);

    /**
     * 取消单据
     */
    void cancel(Long formId);

    /**
     * 删除单据
     */
    void delete(Long formId);

    /**
     * 加班预览
     */
    List<ApprovalDetailStepRecordDTO> overtimePreview(OverTimeAddParam param);

    /**
     * 预计加班时长计算
     */
    BigDecimal overtimeCalc(OverTimeCalcParam param);

    /**
     * 根据常住地校验加班权限
     */
    Boolean checkUserOverTimeAuth();
}
