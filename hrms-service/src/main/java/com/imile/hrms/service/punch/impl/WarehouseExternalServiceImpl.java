package com.imile.hrms.service.punch.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.attendance.AttendanceAbnormalTypeEnum;
import com.imile.hrms.common.enums.punch.WarehouseAttendanceStatusEnum;
import com.imile.hrms.common.enums.punch.WarehousePcsStatusEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.PageUtil;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailAbnormalDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailDao;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailAbnormalDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.service.punch.WarehouseExternalService;
import com.imile.hrms.service.punch.param.warehouse.GetOcListByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportCountByConditionParam;
import com.imile.hrms.service.punch.vo.warehouse.OcVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehousePcsMonthReportCountVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehousePcsMonthReportPassRateVO;
import com.imile.hrms.service.punch.vo.warehouse.WarehousePcsReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Slf4j
@Service
public class WarehouseExternalServiceImpl implements WarehouseExternalService {

    @Resource
    private HrmsWarehouseDetailDao warehouseDetailDao;

    @Resource
    private HrmsWarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private HrmsEntDeptDao hrmsEntDeptDao;

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;

    @Override
    public List<OcVO> getOcListByCondition(GetOcListByConditionParam param) {
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setStartTime(param.getStartDate());
        warehouseDetailParam.setEndTime(param.getEndDate());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        warehouseDetailParam.setCity(param.getCity());
        List<HrmsWarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return Collections.emptyList();
        }
        List<Long> ocIds = warehouseDetailDOList.stream().map(HrmsWarehouseDetailDO::getOcId).distinct().collect(Collectors.toList());
        return hrmsEntDeptDao.selectByIdList(ocIds).stream().map(dept -> {
            OcVO ocVO = new OcVO();
            ocVO.setOcId(dept.getId());
            ocVO.setOcCode(dept.getDeptCode());
            ocVO.setOcName(dept.getDeptNameEn());
            return ocVO;
        }).collect(Collectors.toList());
    }

    @Override
    public PaginationResult<WarehousePcsReportVO> pcsDateReport(GetPcsReportByConditionParam param) {
        List<Long> ocList = param.getOcIdList();
        if (CollectionUtils.isEmpty(ocList)) {
            GetOcListByConditionParam getOcListByConditionParam = new GetOcListByConditionParam();
            getOcListByConditionParam.setVendorCode(param.getVendorCode());
            getOcListByConditionParam.setStartDate(param.getStartDate());
            getOcListByConditionParam.setEndDate(param.getEndDate());
            List<OcVO> ocVOList = getOcListByCondition(getOcListByConditionParam);
            if (CollectionUtils.isEmpty(ocVOList)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
            ocList = ocVOList.stream().map(OcVO::getOcId).collect(Collectors.toList());
        }

        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        warehouseDetailParam.setOcIdList(ocList);
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        warehouseDetailParam.setPcsStatus(param.getPcsStatus());
        Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIdList = list.stream().map(HrmsWarehouseDetailDO::getUserId).collect(Collectors.toList());
        Map<Long, HrmsUserInfoDO> userNameMap = hrmsUserInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = list.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = hrmsEntDeptDao.selectByIdList(ocIdList).stream().
                collect(Collectors.toMap(HrmsEntDeptDO::getId, HrmsEntDeptDO::getDeptNameEn, (v1, v2) -> v1));

        List<Long> warehouseDetailIds = list
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouse.getAttendanceStatus()))
                .map(HrmsWarehouseDetailDO::getId)
                .collect(Collectors.toList());


        List<Long> workMissCardWarehouseDetailList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds)
                .stream()
                .filter(warehouseAbnormal -> Objects.equals(AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), warehouseAbnormal.getAbnormalType()))
                .map(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId)
                .distinct().collect(Collectors.toList());


        List<WarehousePcsReportVO> collect = convertWarehousePcsDateReportVOS(list, userNameMap, ocMap, workMissCardWarehouseDetailList);
        return PageUtil.get(collect, page, param);
    }

    @Override
    public PaginationResult<WarehousePcsReportVO> pcsWeekOrMonthReport(GetPcsReportByConditionParam param) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(param.getReportType()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), "reportType");
        BusinessLogicException.checkTrue(!Lists.newArrayList(BusinessConstant.WEEK, BusinessConstant.MONTH).contains(param.getReportType()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc(), "reportType");

        List<Long> ocList = param.getOcIdList();
        if (CollectionUtils.isEmpty(ocList)) {
            GetOcListByConditionParam getOcListByConditionParam = new GetOcListByConditionParam();
            getOcListByConditionParam.setVendorCode(param.getVendorCode());
            getOcListByConditionParam.setStartDate(param.getStartDate());
            getOcListByConditionParam.setEndDate(param.getEndDate());
            List<OcVO> ocVOList = getOcListByCondition(getOcListByConditionParam);
            if (CollectionUtils.isEmpty(ocVOList)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
            ocList = ocVOList.stream().map(OcVO::getOcId).collect(Collectors.toList());
        }

        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<HrmsUserInfoDO> userInfoDOList = hrmsUserInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        warehouseDetailParam.setOcIdList(ocList);
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        warehouseDetailParam.setPcsStatus(param.getPcsStatus());
        warehouseDetailParam.setReportType(param.getReportType());
        Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIdList = warehouseDetailDOS.stream()
                .map(HrmsWarehouseDetailDO::getUserId)
                .collect(Collectors.toList());

        WarehouseDetailParam detailParam = new WarehouseDetailParam();
        detailParam.setStartDate(param.getStartDate());
        detailParam.setEndDate(param.getEndDate());
        detailParam.setUserIdList(userIdList);
        List<HrmsWarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectPcsPage(detailParam);

        Map<String, List<HrmsWarehouseDetailDO>> groupMap = warehouseDetailDOList
                .stream().collect(Collectors.groupingBy(o -> o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId()));

        Map<Long, HrmsUserInfoDO> userNameMap = hrmsUserInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(HrmsUserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = hrmsEntDeptDao.selectByIdList(ocIdList).stream().
                collect(Collectors.toMap(HrmsEntDeptDO::getId, HrmsEntDeptDO::getDeptNameEn, (v1, v2) -> v1));

        List<WarehousePcsReportVO> collect = convertWarehousePcsReportVOS(warehouseDetailDOS, userNameMap, ocMap, groupMap);
        return PageUtil.get(collect, page, param);
    }

    @Override
    public WarehousePcsMonthReportCountVO pcsDateReportCount(GetPcsReportCountByConditionParam param) {
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        List<HrmsWarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        WarehousePcsMonthReportCountVO reportCountVO = new WarehousePcsMonthReportCountVO();
        if (CollectionUtils.isEmpty(list)) {
            reportCountVO.setTotalCount(BusinessConstant.ZERO);
            reportCountVO.setNormalCount(BusinessConstant.ZERO);
            reportCountVO.setAbnormalCount(BusinessConstant.ZERO);
            return reportCountVO;
        }
        reportCountVO.setTotalCount(list.size());
        reportCountVO.setNormalCount((int) list.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count());
        reportCountVO.setAbnormalCount((int) list.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.ABNORMAL.getCode(), warehouse.getPcsStatus())).count());
        return reportCountVO;
    }

    @Override
    public WarehousePcsMonthReportPassRateVO pcsDateMonthReportPassRate(GetPcsReportCountByConditionParam param) {
        WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        List<HrmsWarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        WarehousePcsMonthReportPassRateVO reportPassRateVO = new WarehousePcsMonthReportPassRateVO();
        if (CollectionUtils.isEmpty(list)) {
            return reportPassRateVO;
        }

        Map<Date, List<HrmsWarehouseDetailDO>> groupByDateMap = list.stream().collect(Collectors.groupingBy(HrmsWarehouseDetailDO::getWarehouseDate));

        for (int i = 0; i < 31; i++) {
            BiConsumer<WarehousePcsMonthReportPassRateVO, BigDecimal> setter = reportPassRateVO.getSetter(i + 1);
            Date warehouseDate = DateUtil.offsetDay(param.getStartDate(), i);
            List<HrmsWarehouseDetailDO> warehouseDetailDOS = groupByDateMap.get(warehouseDate);
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
                int normalCount = (int) warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count();
                bigDecimal = new BigDecimal(normalCount).divide(new BigDecimal(warehouseDetailDOS.size()), 2, RoundingMode.HALF_UP);
            }
            setter.accept(reportPassRateVO, bigDecimal);
        }
        return reportPassRateVO;
    }

    @Override
    public void refreshHistoryPcsStatus() {
        int currentPage = 1;
        int pageSize = 500;
        PageInfo<HrmsWarehouseDetailDO> pageInfo;
        do {
            Page<HrmsWarehouseDetailDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> warehouseDetailDao.selectByCondition(new WarehouseDetailParam()));
            // 总记录数
            List<HrmsWarehouseDetailDO> warehouseDetailList = pageInfo.getList();
            if (CollectionUtils.isEmpty(warehouseDetailList)) {
                currentPage++;
                continue;
            }
            warehouseDetailList.forEach(warehouseDetail -> {
                if (Objects.nonNull(warehouseDetail.getActualAttendanceTime()) && warehouseDetail.getActualAttendanceTime().compareTo(BigDecimal.ZERO) > 0) {
                    warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.NORMAL.getCode());
                } else {
                    warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
                }
            });
            warehouseDetailDao.updateBatchById(warehouseDetailList);
            log.info("pcsStatus刷新当前页：{}", currentPage);
            currentPage++;
        } while (currentPage <= pageInfo.getPages());
        log.info("历史pcs状态刷新完毕");
    }

    @NotNull
    private static List<WarehousePcsReportVO> convertWarehousePcsDateReportVOS(List<HrmsWarehouseDetailDO> list,
                                                                               Map<Long, HrmsUserInfoDO> userNameMap,
                                                                               Map<Long, String> ocMap,
                                                                               List<Long> workMissCardWarehouseDetailList) {
        return list.stream()
                .map(o -> {
                    WarehousePcsReportVO pcsReportVO = new WarehousePcsReportVO();
                    pcsReportVO.setId(o.getId());
                    pcsReportVO.setWarehouseDate(o.getWarehouseDate());
                    pcsReportVO.setCountry(o.getCountry());
                    pcsReportVO.setCity(o.getCity());
                    pcsReportVO.setInOcId(o.getOcId());
                    pcsReportVO.setInOcName(ocMap.get(o.getOcId()));
                    pcsReportVO.setInVendorCode(o.getVendorCode());
                    HrmsUserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new HrmsUserInfoDO());
                    pcsReportVO.setOcId(o.getUserOcId());
                    pcsReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    pcsReportVO.setUserCode(user.getUserCode());
                    pcsReportVO.setUserName(user.getUserName());
                    pcsReportVO.setUserNameEn(user.getUserNameEn());
                    pcsReportVO.setPcsStatus(o.getPcsStatus());
                    pcsReportVO.setClockIn(!workMissCardWarehouseDetailList.contains(o.getId()));
                    pcsReportVO.setClockOut(Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), o.getPcsStatus()));
                    return pcsReportVO;
                }).collect(Collectors.toList());
    }

    @NotNull
    private static List<WarehousePcsReportVO> convertWarehousePcsReportVOS(List<HrmsWarehouseDetailDO> list,
                                                                           Map<Long, HrmsUserInfoDO> userNameMap,
                                                                           Map<Long, String> ocMap,
                                                                           Map<String, List<HrmsWarehouseDetailDO>> groupMap) {
        return list.stream()
                .map(o -> {
                    WarehousePcsReportVO pcsReportVO = new WarehousePcsReportVO();
                    pcsReportVO.setId(o.getId());
                    pcsReportVO.setWarehouseDate(o.getWarehouseDate());
                    pcsReportVO.setCountry(o.getCountry());
                    pcsReportVO.setCity(o.getCity());
                    pcsReportVO.setInOcId(o.getOcId());
                    pcsReportVO.setInOcName(ocMap.get(o.getOcId()));
                    pcsReportVO.setInVendorCode(o.getVendorCode());
                    HrmsUserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new HrmsUserInfoDO());
                    pcsReportVO.setOcId(o.getUserOcId());
                    pcsReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    pcsReportVO.setUserCode(user.getUserCode());
                    pcsReportVO.setUserName(user.getUserName());
                    pcsReportVO.setUserNameEn(user.getUserNameEn());
                    List<HrmsWarehouseDetailDO> warehouseDetailDOList = groupMap.get(o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId());
                    if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
                        pcsReportVO.setDays(warehouseDetailDOList.size());
                        pcsReportVO.setNormalDays((int) warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count());
                        pcsReportVO.setAbnormalDays((int) warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.ABNORMAL.getCode(), warehouse.getPcsStatus())).count());
                    }
                    return pcsReportVO;
                }).collect(Collectors.toList());
    }
}
