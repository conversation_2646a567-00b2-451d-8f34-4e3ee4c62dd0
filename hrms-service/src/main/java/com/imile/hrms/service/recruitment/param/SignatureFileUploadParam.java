package com.imile.hrms.service.recruitment.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.dto.AttachmentParamDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignatureFileUploadParam {

    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String token;

    /**
     * 签字件
     */
    @NotEmpty(message = ValidCodeConstant.NOT_NULL)
    private List<AttachmentParamDTO> signatureFileUrls;
}
