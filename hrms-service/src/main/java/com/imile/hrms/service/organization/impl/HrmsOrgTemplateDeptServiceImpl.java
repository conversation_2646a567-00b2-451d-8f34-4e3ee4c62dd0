package com.imile.hrms.service.organization.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.dao.organization.dao.HrmsOrgTemplateDeptDao;
import com.imile.hrms.dao.organization.model.HrmsOrgTemplateDeptDO;
import com.imile.hrms.service.organization.HrmsOrgTemplateDeptService;
import com.imile.hrms.service.organization.param.HrmsOrgTemplateDeptSaveParam;
import com.imile.hrms.service.organization.result.HrmsOrgTemplateDeptDetailVO;
import com.imile.hrms.service.organization.result.HrmsOrgTemplateDeptTreeNodeVO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/1
 */
@Slf4j
@Service
public class HrmsOrgTemplateDeptServiceImpl implements HrmsOrgTemplateDeptService {

    @Autowired
    private HrmsOrgTemplateDeptDao hrmsOrgTemplateDeptDao;

    @Override
    public Boolean saveOrgTemplateDept(HrmsOrgTemplateDeptSaveParam param) {
        if (Objects.isNull(param.getId())) {
            return this.addOrgTemplateDept(param);
        } else {
            return this.updateOrgTemplateDept(param);
        }
    }

    @Override
    public HrmsOrgTemplateDeptDetailVO getOrgTemplateDeptDetail(Long id) {
        HrmsOrgTemplateDeptDO dept = this.getOrgTemplateDeptById(id);
        HrmsOrgTemplateDeptDetailVO result = BeanUtils.convert(dept, HrmsOrgTemplateDeptDetailVO.class);
        if (StringUtils.isNotBlank(dept.getBizArea())) {
            List<String> bizAreaList = Arrays.stream(dept.getBizArea()
                    .split(HrmsStringUtil.COMMA))
                    .collect(Collectors.toList());
            result.setBizAreaList(bizAreaList);
        }
        return result;
    }

    @Override
    public HrmsOrgTemplateDeptTreeNodeVO getOrgTemplateDeptTree(Long templateId) {
        List<HrmsOrgTemplateDeptDO> deptList = hrmsOrgTemplateDeptDao.selectByTemplateId(templateId);
        Map<Long, HrmsOrgTemplateDeptTreeNodeVO> nodeMap = Maps.newHashMap();
        HrmsOrgTemplateDeptTreeNodeVO root = null;

        // 构建节点映射表
        for (HrmsOrgTemplateDeptDO dept : deptList) {
            HrmsOrgTemplateDeptTreeNodeVO treeNode = new HrmsOrgTemplateDeptTreeNodeVO();
            treeNode.setId(dept.getId());
            treeNode.setDeptNameCn(dept.getDeptNameCn());
            treeNode.setDeptNameEn(dept.getDeptNameEn());
            treeNode.setChildrenList(Lists.newArrayList());
            nodeMap.put(dept.getId(), treeNode);
        }

        // 构建树结构
        for (HrmsOrgTemplateDeptDO dept : deptList) {
            HrmsOrgTemplateDeptTreeNodeVO treeNode = nodeMap.get(dept.getId());
            if (BusinessConstant.DEFAULT_ID.equals(dept.getParentId())) {
                root = treeNode;
                continue;
            }
            HrmsOrgTemplateDeptTreeNodeVO parent = nodeMap.get(dept.getParentId());
            if (Objects.nonNull(parent)) {
                parent.getChildrenList().add(treeNode);
            }
        }
        return root;
    }

    private Boolean addOrgTemplateDept(HrmsOrgTemplateDeptSaveParam param) {
        if (Objects.nonNull(param.getTemplateId())) {
            // 手动创建组织模板时 走该分支
            return this.doAddOrgTemplateDept4Root(param);
        } else {
            return this.doAddOrgTemplateDept4Child(param);
        }
    }

    private Boolean doAddOrgTemplateDept4Root(HrmsOrgTemplateDeptSaveParam param) {
        HrmsOrgTemplateDeptDO record = new HrmsOrgTemplateDeptDO();
        record.setTemplateId(param.getTemplateId());
        record.setDeptNameCn(param.getDeptNameCn());
        record.setDeptNameEn(param.getDeptNameEn());
        record.setDeptOrgType(param.getDeptOrgType());
        record.setBizArea(StringUtils.join(param.getBizAreaList(), HrmsStringUtil.COMMA));
        record.setParentId(0L);
        record.setDeptPosition(param.getDeptPosition());
        record.setDeptDuty(param.getDeptDuty());
        BaseDOUtil.fillDOInsert(record);
        boolean result = hrmsOrgTemplateDeptDao.save(record);
        if (!result) {
            log.error("新增组织模板起始部门失败,param:{}", param);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean doAddOrgTemplateDept4Child(HrmsOrgTemplateDeptSaveParam param) {
        HrmsOrgTemplateDeptDO parentDept = this.getOrgTemplateDeptById(param.getParentId());
        HrmsOrgTemplateDeptDO record = new HrmsOrgTemplateDeptDO();
        record.setTemplateId(parentDept.getTemplateId());
        record.setDeptNameCn(param.getDeptNameCn());
        record.setDeptNameEn(param.getDeptNameEn());
        record.setDeptOrgType(param.getDeptOrgType());
        record.setBizArea(StringUtils.join(param.getBizAreaList(), HrmsStringUtil.COMMA));
        record.setParentId(parentDept.getId());
        record.setDeptPosition(param.getDeptPosition());
        record.setDeptDuty(param.getDeptDuty());
        BaseDOUtil.fillDOInsert(record);
        boolean result = hrmsOrgTemplateDeptDao.save(record);
        if (!result) {
            log.error("新增组织模板部门失败,param:{}", param.toString());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean updateOrgTemplateDept(HrmsOrgTemplateDeptSaveParam param) {
        HrmsOrgTemplateDeptDO dept = this.getOrgTemplateDeptById(param.getId());
        // 校验上级部门ID是否合法
        if (!this.checkParentId(dept, param.getParentId())) {
            log.error("上级部门ID不合法,param:{}", param);
            throw BusinessException.get(HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(),
                    I18nUtils.getMessage(HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc()));
        }
        HrmsOrgTemplateDeptDO record = new HrmsOrgTemplateDeptDO();
        record.setId(dept.getId());
        record.setDeptNameCn(param.getDeptNameCn());
        record.setDeptNameEn(param.getDeptNameEn());
        record.setDeptOrgType(param.getDeptOrgType());
        record.setBizArea(StringUtils.join(param.getBizAreaList(), HrmsStringUtil.COMMA));
        record.setParentId(param.getParentId());
        record.setDeptPosition(param.getDeptPosition());
        record.setDeptDuty(param.getDeptDuty());
        BaseDOUtil.fillDOUpdate(record);
        boolean result = hrmsOrgTemplateDeptDao.updateById(record);
        if (!result) {
            log.error("更新组织模板部门失败,param:{}", param.toString());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean checkParentId(HrmsOrgTemplateDeptDO dept, Long targetParentId) {
        // 判断当前上级部门ID与目标上级部门ID是否一致 该判断必须在第一位
        if (dept.getParentId().equals(targetParentId)) {
            return Boolean.TRUE;
        }
        // 判断当前部门是否为起始组织 起始组织不允许更换上级组织
        if (BusinessConstant.DEFAULT_ID.equals(dept.getParentId())) {
            return Boolean.FALSE;
        }
        HrmsOrgTemplateDeptDO parentDept = this.getOrgTemplateDeptById(targetParentId);
        // 判断当前部门与目标上级部门的所属组织模板是否一致
        if (!dept.getTemplateId().equals(parentDept.getTemplateId())) {
            return Boolean.FALSE;
        }
        // 判断目标上级部门ID是否为当前部门的子部门
        if (this.isTargetParentIdBelongToCurrentDept(parentDept, dept.getId())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean isTargetParentIdBelongToCurrentDept(HrmsOrgTemplateDeptDO parentDept, Long currentDeptId) {
        // 若目标上级部门的上级ID为0 代表目标上级部门是起始部门 直接返回false
        if (BusinessConstant.DEFAULT_ID.equals(parentDept.getParentId())) {
            return Boolean.FALSE;
        }
        // 若目标上级部门的上级ID与当前部门ID一致 则代表目标上级部门属于当前部门的子部门 返回true
        if (parentDept.getParentId().equals(currentDeptId)) {
            return Boolean.TRUE;
        }
        // 否则就递归往上找 再次判断
        HrmsOrgTemplateDeptDO tempDept = this.getOrgTemplateDeptById(parentDept.getParentId());
        return this.isTargetParentIdBelongToCurrentDept(tempDept, currentDeptId);
    }

    private HrmsOrgTemplateDeptDO getOrgTemplateDeptById(Long id) {
        HrmsOrgTemplateDeptDO record = hrmsOrgTemplateDeptDao.getById(id);
        if (Objects.isNull(record) || IsDeleteEnum.YES.getCode().equals(record.getIsDelete())) {
            throw BusinessException.get(HrmsErrorCodeEnums.DATA_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(HrmsErrorCodeEnums.DATA_NOT_EXITS.getDesc()));
        }
        return record;
    }
}
