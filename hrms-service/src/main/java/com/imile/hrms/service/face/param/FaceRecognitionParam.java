package com.imile.hrms.service.face.param;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Data
public class FaceRecognitionParam implements Serializable {

    @NotNull(message = "网点编码必填")
    private String ocCode;

    private String url;

    @NotNull(message = "文件必填")
    private MultipartFile file;

}
