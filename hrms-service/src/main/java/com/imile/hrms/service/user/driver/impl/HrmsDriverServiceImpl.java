package com.imile.hrms.service.user.driver.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.enums.CommonStatusEnum;
import com.imile.hermes.enterprise.dto.EntDriverSaveApiDTO;
import com.imile.hermes.enterprise.dto.EntEmpDriverApiDTO;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hermes.vendor.dto.VendorUpdateStatusDTO;
import com.imile.hermes.vendor.dto.VendorUpdateStatusResultDTO;
import com.imile.hrms.api.primary.enums.CertificateTypeEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.CountryCodeEnum;
import com.imile.hrms.common.enums.DimissionStatusEnum;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.OperationModuleEnum;
import com.imile.hrms.common.enums.OperationTypeEnum;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.freelancer.FreelancerBasicInfoEnum;
import com.imile.hrms.common.enums.organization.EntOcBusinessCategoryEnum;
import com.imile.hrms.common.enums.user.DriverSourceTypeEnum;
import com.imile.hrms.common.enums.user.DriverTaxIdSourceEnum;
import com.imile.hrms.common.enums.user.NewDriverManageDaTypeEnum;
import com.imile.hrms.common.enums.user.UserExtendAttrKeyEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.primary.entity.condition.UserConditionBuilder;
import com.imile.hrms.dao.user.dao.HrmsCountryConfigDao;
import com.imile.hrms.dao.user.dao.HrmsUserDimissionRecordDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.FreelancerPaymentInfoDTO;
import com.imile.hrms.dao.user.dto.OutSourceDriverRegisterParam;
import com.imile.hrms.dao.user.dto.UserCertificateParamDTO;
import com.imile.hrms.dao.user.dto.UserCompanyParam;
import com.imile.hrms.dao.user.model.HrmsCountryConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserDimissionRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserExtendAttrDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.EntEmpDriverService;
import com.imile.hrms.integration.hermes.service.EntEmployeeService;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.integration.hermes.service.VehicleModelService;
import com.imile.hrms.integration.hermes.service.VendorService;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.user.HrmsUserExtendAttrManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.manage.utils.OutSourceDriverUtil;
import com.imile.hrms.mq.HrMqEventConstant;
import com.imile.hrms.mq.basic.ProducerBasicService;
import com.imile.hrms.service.tx.HrmsTxManageFactory;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.hrms.service.user.driver.AbstractDriverHandler;
import com.imile.hrms.service.user.driver.DriverAddHandlerFactory;
import com.imile.hrms.service.user.driver.HrmsDriverService;
import com.imile.hrms.service.user.dto.CheckSyncUserDTO;
import com.imile.hrms.service.user.dto.CheckSyncUserResultDTO;
import com.imile.hrms.service.user.dto.HrmsDriverTotalDTO;
import com.imile.hrms.service.user.dto.SyncUserDTO;
import com.imile.hrms.service.user.dto.VendorDisabledDTO;
import com.imile.hrms.service.user.param.DriverLeaderQueryParam;
import com.imile.hrms.service.user.result.UserOptionBO;
import com.imile.hrms.service.zkteco.ZktecoService;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022-05-27
 */
@Slf4j
@Service
public class HrmsDriverServiceImpl implements HrmsDriverService {
    @Autowired
    private VendorService vendorService;
    @Autowired
    private HrmsUserInfoDao userInfoDao;
    @Autowired
    private HrmsUserInfoManage userInfoManage;
    @Resource
    private UserManage userManage;
    @Autowired
    private HrmsUserDimissionRecordDao dimissionRecordDao;
    @Autowired
    protected LogRecord logRecord;
    @Autowired
    private HrmsDeptManage hrmsDeptManage;
    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;
    @Autowired
    private EntEmpDriverService entEmpDriverService;
    @Autowired
    private EntEmployeeService entEmployeeService;
    @Autowired
    private EntOcService entOcService;
    @Autowired
    private VehicleModelService vehicleModelService;
    @Autowired
    private ZktecoService zktecoService;
    @Autowired
    private OutSourceDriverUtil outSourceDriverUtil;
    @Autowired
    private ConverterService converterService;
    @Autowired
    private HrmsUserExtendAttrManage hrmsUserExtendAttrManage;
    @Autowired
    private HrmsCountryConfigDao hrmsCountryConfigDao;
    @Autowired
    private ProducerBasicService producerBasicService;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsProperties hrmsProperties;
    @Value("${rocket.mq.hr.topic}")
    private String hrProducerTopic;

    @Override
    public Boolean disabledDriverByVendor(VendorDisabledDTO dto) {
        VendorUpdateStatusDTO statusDTO = BeanUtils.convert(dto, VendorUpdateStatusDTO.class);
        VendorUpdateStatusResultDTO resultDTO = vendorService.checkDisabledVendorDriver(statusDTO);
        if (!resultDTO.getNeedDisableDa()) {
            return false;
        }
        if (StringUtils.equals(StatusEnum.ACTIVE.getCode(), dto.getStatus())) {
            return false;
        }

        HrmsUserInfoDO query = new HrmsUserInfoDO();
        query.setVendorId(dto.getVendorId());
        query.setIsVirtual(BusinessConstant.N);
        query.setIsDelete(BusinessConstant.N);
        query.setIsDriver(BusinessConstant.Y);
        List<HrmsUserInfoDO> userInfoDOList = userInfoManage.selectUserInfo(query);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return true;
        }

        List<HrmsUserInfoDO> noDimissionList = userInfoDOList.stream().filter(o -> StringUtils.isNotBlank(o.getWorkStatus()) && !o.getWorkStatus().equals(WorkStatusEnum.DIMISSION.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noDimissionList)) {
            return true;
        }

        //办理离职
        for (HrmsUserInfoDO userInfoDO : userInfoDOList) {
            Date dateTime = new Date();
            //修改账号状态
            userInfoDO.setStatus(StatusEnum.DISABLED.getCode());
            userInfoDO.setWorkStatus(WorkStatusEnum.DIMISSION.getCode());
            userInfoDO.setLastUpdDate(dateTime);
            userInfoDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            userInfoDO.setLastUpdUserName(RequestInfoHolder.getUserName());
            userInfoDao.saveOrUpdate(userInfoDO);

            //新增离职记录
            HrmsUserDimissionRecordDO dimissionRecordDO = new HrmsUserDimissionRecordDO();
            dimissionRecordDO.setUserId(userInfoDO.getId());
            dimissionRecordDO.setUserName(userInfoDO.getUserName());
            dimissionRecordDO.setTransfereeId(RequestInfoHolder.getUserId());
            dimissionRecordDO.setActualDimissionDate(dateTime);
            dimissionRecordDO.setPlanDimissionDate(dateTime);
            dimissionRecordDO.setDimissionStatus(DimissionStatusEnum.DIMISSION.getCode());
            dimissionRecordDO.setDimissionReason("Vendor cooperation stopped");
            dimissionRecordDO.setCreateDate(dateTime);
            dimissionRecordDO.setCreateUserCode(RequestInfoHolder.getUserCode());
            dimissionRecordDO.setCreateUserName(RequestInfoHolder.getUserName());
            dimissionRecordDO.setLastUpdDate(dateTime);
            dimissionRecordDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            dimissionRecordDO.setLastUpdUserName(RequestInfoHolder.getUserName());
            dimissionRecordDO.setRemark("Vendor cooperation stopped");
            dimissionRecordDao.save(dimissionRecordDO);

            //日志记录
            logRecord.diffObj(dimissionRecordDO, null, OperationTypeEnum.DIMMISION_CONFIRM.getCode());
            // 开启分布式事务
            // 获取 离职事务管理器并进行业务处理
            HrmsTxManageFactory.getTxManage(OperationModuleEnum.DIMISSION_MODULE).handle(String.valueOf(userInfoDO.getId()));
        }
        return true;
    }


    @Override
    public List<HrmsDriverTotalDTO> countDriverByVendor(String stationName) {
        List<HrmsEntDeptDO> deptDOList = hrmsDeptManage.selectByDeptName(stationName, BusinessConstant.Y);
        if (CollectionUtils.isEmpty(deptDOList)) {
            return new ArrayList<>();
        }
        List<Long> deptIds = deptDOList.stream().map(o -> o.getId()).collect(Collectors.toList());

        HrmsUserInfoDO query = new HrmsUserInfoDO();
        query.setIsDelete(IsDeleteEnum.NO.getCode());
        query.setVendorName(RequestInfoHolder.getUserName());
        List<HrmsUserInfoDO> userInfoDOList = userInfoManage.selectUserInfo(query);

        List<HrmsUserInfoDO> userFilterList = userInfoDOList.stream().filter(o -> deptIds.contains(o.getDeptId())).collect(Collectors.toList());

        Map<Long, List<HrmsUserInfoDO>> userMap = userFilterList.stream().collect(Collectors.groupingBy(o -> o.getDeptId()));

        Map<Long, HrmsEntDeptDO> deptMap = deptDOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));

        List<HrmsDriverTotalDTO> resultList = new ArrayList<>();
        for (Map.Entry<Long, List<HrmsUserInfoDO>> entry : userMap.entrySet()) {
            HrmsEntDeptDO deptDO = deptMap.get(entry.getKey());
            HrmsDriverTotalDTO dto = new HrmsDriverTotalDTO();
            dto.setStationName(deptDO.getDeptNameEn());
            dto.setDaCount(entry.getValue().size());
            resultList.add(dto);
        }
        return resultList;
    }

    @Override
    public void syncUserByCode(SyncUserDTO dto) {
        UserCompanyParam userParam = BeanUtils.convert(dto, UserCompanyParam.class);
        hrmsUserInfoService.syncUserCode(userParam);
    }

    @Override
    public void syncDriverByCode(SyncUserDTO dto) {
        UserCompanyParam userParam = BeanUtils.convert(dto, UserCompanyParam.class);
        hrmsUserInfoService.syncDriverCode(userParam);
    }

    @Override
    public CheckSyncUserResultDTO checkSyncUser(CheckSyncUserDTO checkDto) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(checkDto.getUserCode()), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(), HrmsErrorCodeEnums.PARAM_VALID_ERROR.getDesc());

        CheckSyncUserResultDTO resultDTO = new CheckSyncUserResultDTO();
        resultDTO.setIsDriver(BusinessConstant.N);
        // hr侧不能已存在该账号
        List<HrmsUserInfoDO> userInfoDOList = userInfoDao.getUserByUserCode(checkDto.getUserCode());
        if (CollectionUtils.isNotEmpty(userInfoDOList)) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_ALREADY_EXISTS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_ALREADY_EXISTS.getDesc()));
        }

        // 查询tms侧信息
        // 检查是否是司机，如果是司机，按照司机处理
        EntEmpDriverApiDTO driver = entEmpDriverService.getDriverByCode(checkDto.getUserCode());
        if (driver != null) {
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(driver.getType());
            resultDTO.setIsDriver(BusinessConstant.Y);
            List<HrmsEntDeptDO> deptDOList = hrmsDeptManage.selectByDeptName(driver.getOcName(), BusinessConstant.Y);
            if (CollectionUtils.isEmpty(deptDOList)) {
                throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
            }
            HrmsEntDeptDO deptDO = deptDOList.get(0);
            resultDTO.setDeptId(deptDO.getId());
            resultDTO.setCountry(deptDO.getCountry());
            if (employmentTypeEnum == null || employmentTypeEnum.getCode().equalsIgnoreCase(EmploymentTypeEnum.EMPLOYEE.getCode())) {
                resultDTO.setEmployeeType(false);
                resultDTO.setTypeErrorMessage(RequestInfoHolder.isChinese() ? "无法关联正式员工，请选择员工性质，变更后员工性质将同步到TMS系统" : "Formal employees data cannot be associated to HRMS, please select the Character. After the change, the Character will be synchronized to the TMS");
            }

            String leaderCode = driver.getDtlUserCode();
            HrmsUserInfoDO userInfoDO = userInfoDao.getByUserCode(leaderCode);
            if (userInfoDO == null) {
                resultDTO.setLeader(false);
                resultDTO.setLeaderErrorMessage(RequestInfoHolder.isChinese() ? "汇报上级信息错误，请重新选择汇报上级，变更后汇报上级将同步至TMS" : "Report to superior information error, please re select report to superior. After the change, the report to the superior will be synchronized to TMS");
            }
            return resultDTO;
        }
        // 不是司机，按照用户处理
        EntEmployeeApiDTO employee = entEmployeeService.getEmployeeByCode(checkDto.getUserCode());
        if (employee == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.ACCOUNT_NOT_EXITS.getDesc()));
        }
        if (employee.getOcId() == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.USER_TMS_STATION_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_TMS_STATION_NOT_EXIST.getDesc()));
        }
        EntOcApiDTO entOc = entOcService.getOcById(BusinessConstant.DEFAULT_ORG_ID, employee.getOcId());
        if (entOc == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SALARY_OC_NAME_NOT_EXIST.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.SALARY_OC_NAME_NOT_EXIST.getDesc()));
        }
        resultDTO.setCountry(entOc.getCountry());
        resultDTO.setEmployeeType(false);
        resultDTO.setTypeErrorMessage(RequestInfoHolder.isChinese() ? "无法关联正式员工，请选择员工性质，变更后员工性质将同步到TMS系统" : "Formal employees data cannot be associated to HRMS, please select the Character. After the change, the Character will be synchronized to the TMS");
        resultDTO.setLeader(false);
        resultDTO.setLeaderErrorMessage(RequestInfoHolder.isChinese() ? "汇报上级信息错误，请重新选择汇报上级，变更后汇报上级将同步至TMS" : "Report to superior information error, please re select report to superior. After the change, the report to the superior will be synchronized to TMS");
        return resultDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(OutSourceDriverRegisterParam param) {
        if (!StringUtils.equalsIgnoreCase(param.getDaType(), NewDriverManageDaTypeEnum.Freelancer.getCode()) && (param.getHrUserId() == null || StringUtils.isBlank(param.getUserCode()))) {
            throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_NEW_EDIT_PARAM_ERROR.getCode(), HrmsErrorCodeEnums.DRIVER_NEW_EDIT_PARAM_ERROR.getDesc());
        }
        this.checkEmployeeType(param.getEmployeeType());
        EntOcApiDTO entOc = this.getAvailableOc(param.getOcCode());
        param.setStationCountry(entOc.getCountry());

        //手机号组装
        if (param.getCountryCallingId() != null) {
            param.setPhone(buildPhone(param.getCountryCallingId(), param.getPhone()));
        }
        //手机号组装
        if (param.getAltCountryCallingId() != null && StringUtils.isNotBlank(param.getAlternativePhone())) {
            param.setAlternativePhone(buildPhone(param.getAltCountryCallingId(), param.getAlternativePhone()));
        }

        //干线校验
        validateFunction(param);

        //众包司机编辑
        if (StringUtils.equalsIgnoreCase(param.getDaType(), NewDriverManageDaTypeEnum.Freelancer.getCode())) {
            hrmsUserInfoService.editFreelancer(param);
            return;
        }

        //众包自主注册司机编辑
        if (StringUtils.equalsIgnoreCase(param.getDaType(), NewDriverManageDaTypeEnum.Registration_Freelancer.getCode())) {
            hrmsUserInfoService.editRegistrationFreelancer(param);
            return;
        }

        //通过校验 后，该参数一定 含义正确
        if (StringUtils.equalsIgnoreCase(param.getDaType(), NewDriverManageDaTypeEnum.OS_DA.getCode())) {
            //非干线司机 需要修改hr
            hrmsUserInfoService.editOutSourceDriver(param);
            //同步数据到中控考勤机
            producerBasicService.sendMessage(hrProducerTopic, HrMqEventConstant.SYNC_EMPLOYEE_DRIVER_TO_ZK_TAG, String.valueOf(param.getHrUserId()), null);
        }
        // 干线司机（干线司机由落hermes库改为落hrms后 先临时用编辑外包司机实现 除职能不同 其他字段逻辑理论上一样）
        if (StringUtils.equalsIgnoreCase(param.getDaType(), NewDriverManageDaTypeEnum.LHD.getCode())) {
            hrmsUserInfoService.editOutSourceDriver(param);
        }
        // hermes 修改
        EntDriverSaveApiDTO updateApiDTO = convertToDriverApiDTO(param);

        entEmpDriverService.doUpdate(updateApiDTO);

    }

    @Override
    public List<String> selectVehicleModelByCountry(String country) {
        return vehicleModelService.getVehicleModel(country, 10L);
    }


    private EntDriverSaveApiDTO convertToDriverApiDTO(OutSourceDriverRegisterParam param) {
        EntDriverSaveApiDTO saveApiDTO = new EntDriverSaveApiDTO();

        //todo 司机名 还需要生产吗？
        saveApiDTO.setDriverName(param.getUserName());

        org.springframework.beans.BeanUtils.copyProperties(param, saveApiDTO);
        saveApiDTO.setDtlUserCode(param.getLeaderUserCode());

        //放入驾照信息
        supplyDriverLicense(param, saveApiDTO);
        outSourceDriverUtil.dealSpecial(param, saveApiDTO);

        return saveApiDTO;
    }

    @Override
    public void checkDriverCountry(String employeeType, String vendorCode, String country) {
        // 特定用工类型时 供应商只可选择同网点国家的供应商
        if (!EmploymentTypeEnum.TYPE_OF_REQUIRED_VENDOR.contains(employeeType)) {
            return;
        }
        if (StringUtils.isBlank(vendorCode)) {
            throw BusinessException.get(HrmsErrorCodeEnums.PARAM_VALID_ERROR.getCode(),
                    RequestInfoHolder.isChinese() ? "供应商不存在" :
                            "The vendor does not exist");
        }
        //得到供应商国家
        VendorInfoApiDTO vendorInfoApiDTO = vendorService.getByVendorCode(vendorCode);
        if (Objects.isNull(vendorInfoApiDTO)) {
            throw BusinessException.get(HrmsErrorCodeEnums.VENDOR_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.VENDOR_NOT_EXITS.getDesc()));
        }
        if (Objects.isNull(vendorInfoApiDTO.getOcCenterCodeDesc())) {
            vendorInfoApiDTO.setOcCenterCodeDesc("");
        }
        //得到用户国家
        List<String> ocCenterCodeDescList = Arrays.asList(vendorInfoApiDTO.getOcCenterCodeDesc()
                .split(HrmsStringUtil.COMMA));
        if (ocCenterCodeDescList.stream()
                .noneMatch(desc -> StringUtils.equalsIgnoreCase(desc, country))) {
            throw BusinessException.get(HrmsErrorCodeEnums.VENDOR_COUNTRY_NOT_SAME.getCode(),
                    RequestInfoHolder.isChinese() ? HrmsErrorCodeEnums.VENDOR_COUNTRY_NOT_SAME.getMessage() :
                            HrmsErrorCodeEnums.VENDOR_COUNTRY_NOT_SAME.getDesc());
        }
    }

    @Override
    public void add(OutSourceDriverRegisterParam driverRegisterParam) {
        this.checkEmployeeType(driverRegisterParam.getEmployeeType());
        EntOcApiDTO entOc = this.getAvailableOc(driverRegisterParam.getOcCode());
        checkDriverCountry(driverRegisterParam.getEmployeeType(), driverRegisterParam.getVendorCode(), entOc.getCountry());
        driverRegisterParam.setStationCountry(entOc.getCountry());
        //手机号组装
        if (driverRegisterParam.getCountryCallingId() != null) {
            driverRegisterParam.setPhone(buildPhone(driverRegisterParam.getCountryCallingId(), driverRegisterParam.getPhone()));
        }
        //备用手机号组装
        if (driverRegisterParam.getAltCountryCallingId() != null) {
            driverRegisterParam.setAlternativePhone(buildPhone(driverRegisterParam.getAltCountryCallingId(), driverRegisterParam.getAlternativePhone()));
        }
        // 外包司机 或者 司机类型为空
        if (StringUtils.isBlank(driverRegisterParam.getDaType()) || StringUtils.equalsIgnoreCase(driverRegisterParam.getDaType(), NewDriverManageDaTypeEnum.OS_DA.getCode())) {
            processOsDaOrNullTypeDriver(driverRegisterParam);
            return;
        }
        // 众包司机
        if (StringUtils.equalsIgnoreCase(driverRegisterParam.getDaType(), NewDriverManageDaTypeEnum.Freelancer.getCode())) {
            hrmsUserInfoService.addFreelancer(driverRegisterParam);
            return;
        }
        // 干线司机
        if (StringUtils.equalsIgnoreCase(driverRegisterParam.getDaType(), NewDriverManageDaTypeEnum.LHD.getCode())) {
            processLhdDriver(driverRegisterParam);
        }
    }

    private void checkEmployeeType(String employeeType) {
        EmploymentTypeEnum typeEnum = EmploymentTypeEnum.getByCode(employeeType);
        if (EmploymentTypeEnum.DEFAULT.equals(typeEnum)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.EMPLOYEE_TYPE_ERROR);
        }
    }

    private void processLhdDriver(OutSourceDriverRegisterParam driverRegisterParam) {
        //职能校验
        validateFunction(driverRegisterParam);
        //网点处理
        AbstractDriverHandler handler = DriverAddHandlerFactory.getInstance(DriverSourceTypeEnum.ADD.name());
        handler.driverHandler(driverRegisterParam);
    }

    private void processOsDaOrNullTypeDriver(OutSourceDriverRegisterParam driverRegisterParam) {
        if (StringUtils.equalsIgnoreCase(driverRegisterParam.getDaType(), NewDriverManageDaTypeEnum.OS_DA.getCode())) {
            //职能校验
            validateFunction(driverRegisterParam);
        }
        //hrms 原有新增 司机逻辑
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoService.addOutSourceDriver(driverRegisterParam);
        //同步数据到中控考勤机
        zktecoService.syncEmployee2Zkteco(hrmsUserInfoDO.getId());
    }

    private String buildPhone(Long countryCallingId, String phone) {
        HrmsCountryConfigDO configDO = hrmsCountryConfigDao.getById(countryCallingId);
        if (configDO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.COUNTRY_CONFIG_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.COUNTRY_CONFIG_NOT_EXITS.getDesc()));
        }
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        return getPhone(configDO.getCountryCallingCode(), phone);
    }

    private String getPhone(String countryCallingCode, String phone) {
        StringBuilder sb = new StringBuilder(BusinessConstant.PLUS);
        sb.append(countryCallingCode)
                .append(BusinessConstant.BLANK)
                .append(phone);
        return new String(sb);
    }

    private void supplyCompanyIdNew(OutSourceDriverRegisterParam param) {
        String userCode = RequestInfoHolder.getUserCode();
        if (StringUtils.isBlank(userCode)) {
            return;
        }
        HrmsUserInfoDO user = hrmsUserInfoDao.getByUserCode(userCode);
        if (Objects.nonNull(user)) {
            param.setOriginCountry(user.getOriginCountry());
        }
    }

    private void validateFunction(OutSourceDriverRegisterParam driverRegisterParam) {
        String functional = driverRegisterParam.getFunctional();
        if (StringUtils.isBlank(functional)) {
            throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_FUNCTIONAL_NOT_EMPTY.getCode(), HrmsErrorCodeEnums.DRIVER_FUNCTIONAL_NOT_EMPTY.getDesc());
        }

        List<String> functions = Arrays.asList(functional.split(","));
        boolean containLineHaul = functions.contains(BusinessConstant.LINE_HAUL) ? Boolean.TRUE : Boolean.FALSE;

        Boolean isLineHaul = StringUtils.equalsIgnoreCase(driverRegisterParam.getDaType(), NewDriverManageDaTypeEnum.LHD.getCode());

        if (Boolean.TRUE.equals(isLineHaul) && !containLineHaul) {
            //干线司机必须有LineHaul职能
            throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_NEW_ADD_LINE_HAUL_FUNCTIONAL_ERROR.getCode(), HrmsErrorCodeEnums.DRIVER_NEW_ADD_LINE_HAUL_FUNCTIONAL_ERROR.getDesc());
        }
        if (StringUtils.isNotEmpty(driverRegisterParam.getStationCountry()) && StringUtils.equalsIgnoreCase(driverRegisterParam.getStationCountry(), CountryCodeEnum.BRA.getCode())) {
            return;
        }
        if (Boolean.FALSE.equals(isLineHaul) && containLineHaul) {
            //非干线司机不能有LineHaul职能
            throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_NEW_DRIVER_FUNCTIONAL_ERROR.getCode(), HrmsErrorCodeEnums.DRIVER_NEW_DRIVER_FUNCTIONAL_ERROR.getDesc());
        }
    }

    private void supplyDriverLicense(OutSourceDriverRegisterParam param, EntDriverSaveApiDTO saveApiDTO) {
        List<UserCertificateParamDTO> list = param.getUserCertificateList();
        if (CollectionUtils.isNotEmpty(list)) {
            UserCertificateParamDTO dto = list.stream().filter(itm -> itm.getCertificateTypeCode().equals(CertificateTypeEnum.DRIVING_LICENSE.getCode())).findFirst().orElse(null);
            if (dto != null) {
                saveApiDTO.setDriverLicense(dto.getCertificateCode());
                saveApiDTO.setLicenseBegin(dto.getCertificateReceiptDate());
                saveApiDTO.setLicenseEnd(dto.getCertificateExpireDate());
            }

            UserCertificateParamDTO idCardDto = list.stream().filter(itm -> itm.getCertificateTypeCode().equals(CertificateTypeEnum.ID_CARD.getCode())).findFirst().orElse(null);
            if (idCardDto != null) {
                saveApiDTO.setIdCard(idCardDto.getCertificateCode());
                saveApiDTO.setIdCardEnd(idCardDto.getCertificateExpireDate());
            }
            UserCertificateParamDTO residencyPermitDto = list.stream().filter(itm -> itm.getCertificateTypeCode().equals(CertificateTypeEnum.RESIDENCY_PERMIT.getCode())).findFirst().orElse(null);
            if (residencyPermitDto != null) {
                saveApiDTO.setResidencyPermit(residencyPermitDto.getCertificateCode());
            }
        }
    }

    @Override
    public List<HrmsUserExtendAttrDO> saveExtendAttr(OutSourceDriverRegisterParam driverRegisterParam, HrmsUserInfoDO userInfoDO) {
        List<HrmsUserExtendAttrDO> list = Lists.newArrayList();
        Long id = userInfoDO.getId();
        HrmsUserExtendAttrDO workType = buildUserExtendAtt(UserExtendAttrKeyEnum.driverWorkType.getCode(), driverRegisterParam.getWorkType(), id);
        if (Objects.nonNull(workType)) {
            list.add(workType);
        }
        HrmsUserExtendAttrDO deviceId = buildUserExtendAtt(UserExtendAttrKeyEnum.driverDeviceIds.getCode(), driverRegisterParam.getDeviceIds(), id);
        if (Objects.nonNull(deviceId)) {
            list.add(deviceId);
        }

        HrmsUserExtendAttrDO alternativePhone = buildUserExtendAtt(UserExtendAttrKeyEnum.alternativePhone.getCode(), driverRegisterParam.getAlternativePhone(), id);
        if (Objects.nonNull(alternativePhone)) {
            list.add(alternativePhone);
        }

        FreelancerPaymentInfoDTO paymentInfoDTO = driverRegisterParam.getPaymentInfoDTO();
        if (paymentInfoDTO != null) {

            List<HrmsUserExtendAttrDO> taxIdAttrList = hrmsUserExtendAttrManage.listByAttrKeyAndUserId(Collections.singletonList(FreelancerBasicInfoEnum.taxId.getCode()), id);
            List<HrmsUserExtendAttrDO> taxIdSourceAttrList = hrmsUserExtendAttrManage.listByAttrKeyAndUserId(Collections.singletonList(FreelancerBasicInfoEnum.taxIdSource.getCode()), id);

            Map<String, String> attrMap = new HashMap<>();

            attrMap.put(FreelancerBasicInfoEnum.cpfNumber.getCode(), paymentInfoDTO.getCpfNumber());
            attrMap.put(FreelancerBasicInfoEnum.cpfCertificatePath.getCode(), paymentInfoDTO.getCpfCertificatePath());
            attrMap.put(FreelancerBasicInfoEnum.cnpjNumber.getCode(), paymentInfoDTO.getCnpjNumber());
            attrMap.put(FreelancerBasicInfoEnum.cnpjCertificatePath.getCode(), paymentInfoDTO.getCnpjCertificatePath());
            if (CollectionUtils.isNotEmpty(taxIdAttrList) && CollectionUtils.isNotEmpty(taxIdSourceAttrList) && StringUtils.equalsIgnoreCase(taxIdSourceAttrList.get(0).getAttrValue(), DriverTaxIdSourceEnum.FINANCE.name())) {
                attrMap.put(FreelancerBasicInfoEnum.taxId.getCode(), taxIdAttrList.get(0).getAttrValue());
                attrMap.put(FreelancerBasicInfoEnum.taxIdSource.getCode(), DriverTaxIdSourceEnum.FINANCE.name());
            } else {
                attrMap.put(FreelancerBasicInfoEnum.taxId.getCode(), paymentInfoDTO.getTaxId());
                attrMap.put(FreelancerBasicInfoEnum.taxIdSource.getCode(), DriverTaxIdSourceEnum.ADD.name());
            }

            for (Map.Entry<String, String> entry : attrMap.entrySet()) {
                HrmsUserExtendAttrDO item = buildUserExtendAtt(entry.getKey(), entry.getValue(), id);
                if (Objects.nonNull(item)) {
                    list.add(item);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(driverRegisterParam.getZipCodeList())) {
            HrmsUserExtendAttrDO zipCode = buildUserExtendAtt(FreelancerBasicInfoEnum.zipCodeList.getCode(), JSONObject.toJSONString(driverRegisterParam.getZipCodeList()), id);
            if (Objects.nonNull(zipCode)) {
                list.add(zipCode);
            }
        }

        return list;
    }

    @Override
    public List<UserOptionBO> getDriverLeaderList(DriverLeaderQueryParam param) {
        EntOcApiDTO oc = entOcService.getOcByCode(param.getOcCode());
        List<HrmsUserInfoDO> ocUserList;
        // UAE时 劳务派遣：只取所选网点下在职且启用【员工/挂靠/劳务派遣】类型的人员 合作伙伴：只取所选网点下在职且启用【合作伙伴】类型的人员
        if (hrmsProperties.getOperation().getDtlRule1Country().contains(oc.getCountry())) {
            List<String> employeeTypeList = EmploymentTypeEnum.OS_PER_DELIVERED.getCode().equals(param.getEmployeeType())
                    ? Collections.singletonList(EmploymentTypeEnum.OS_PER_DELIVERED.getCode())
                    : Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), EmploymentTypeEnum.OS_FIXED_SALARY.getCode());
            ocUserList = userManage.getUserByCondition(UserConditionBuilder.builder()
                    .ocCode(param.getOcCode())
                    .employeeTypeList(employeeTypeList)
                    .status(CommonStatusEnum.ACTIVE.getCode())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .build());
        }
        // 非UAE时 先取所选网点及其所有上级网点下在职且启用的人员
        else {
            List<Long> ocIdList = entOcService.getParentOcListByOcId(oc.getId());
            List<EntOcApiDTO> ocList = entOcService.getOcByIds(BusinessConstant.DEFAULT_ORG_ID, ocIdList);
            List<String> ocCodeList = ocList.stream()
                    .map(EntOcApiDTO::getOcCode)
                    .collect(Collectors.toList());
            ocUserList = CollectionUtils.isEmpty(ocCodeList) ? Lists.newArrayList()
                    : userManage.getUserByCondition(UserConditionBuilder.builder()
                    .ocCodeList(ocCodeList)
                    .status(CommonStatusEnum.ACTIVE.getCode())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .build());
        }
        if (!hrmsProperties.getOperation().getDtlRule2Country().contains(oc.getCountry())) {
            return ocUserList.stream()
                    // 过滤脏数据（在职状态但无userCode）
                    .filter(s -> StringUtils.isNotBlank(s.getUserCode()))
                    .map(s -> UserOptionBO.builder()
                            .id(s.getId())
                            .userCode(s.getUserCode())
                            .userName(s.getUserName())
                            .build())
                    .collect(Collectors.toList());
        }
        // AUS时 再追加所属供应商下的在职启用司机
        VendorInfoApiDTO vendor = vendorService.getByVendorCode(param.getVendorCode());
        List<HrmsUserInfoDO> vendorUserList = Objects.isNull(vendor)
                ? Lists.newArrayList()
                : userManage.getUserByCondition(UserConditionBuilder.builder()
                .vendorCode(vendor.getVendorCode())
                .isDriver(WhetherEnum.YES.getKey())
                .status(CommonStatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .build());
        // 合并ocUserList和vendorUserList并去重
        return Stream.concat(ocUserList.stream(), vendorUserList.stream())
                // 过滤脏数据（在职状态但无userCode）
                .filter(s -> StringUtils.isNotBlank(s.getUserCode()))
                .filter(CommonUtils.distinctByKey(HrmsUserInfoDO::getUserCode))
                .map(s -> UserOptionBO.builder()
                        .id(s.getId())
                        .userCode(s.getUserCode())
                        .userName(s.getUserName())
                        .build())
                .collect(Collectors.toList());
    }

    private HrmsUserExtendAttrDO buildUserExtendAtt(String key, String value, Long userId) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        HrmsUserExtendAttrDO attrDO = new HrmsUserExtendAttrDO();
        attrDO.setAttrKey(key);
        attrDO.setAttrValue(value);
        attrDO.setUserId(userId);
        BaseDOUtil.fillDOInsert(attrDO);
        return attrDO;
    }

    /**
     * 校验手机号（司机管理NEW 外包、众包）
     *
     * @param phone
     */
    private void checkUserPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return;
        }
        boolean matches = phone.matches(BusinessConstant.PHONE_CHECK);
        if (phone.startsWith(BusinessConstant.PLUS) || !matches) {
            throw BusinessException.get(HrmsErrorCodeEnums.DRIVER_MOBILE_FORMAT_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.DRIVER_MOBILE_FORMAT_ERROR.getDesc()));
        }
    }

    private EntOcApiDTO getAvailableOc(String ocCode) {
        EntOcApiDTO oc = entOcService.getOcByCode(ocCode);
        if (Objects.isNull(oc)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.STATION_NOT_EXITS);
        }
        if (EntOcBusinessCategoryEnum.THREE_PL.getCode().equals(oc.getBusinessCategory())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.STATION_3PL_UNAVAILABLE);
        }
        return oc;
    }
}
