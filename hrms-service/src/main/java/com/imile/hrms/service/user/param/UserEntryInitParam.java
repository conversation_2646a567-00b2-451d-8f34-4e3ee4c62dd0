package com.imile.hrms.service.user.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserEntryInitParam {

    /**
     * 姓名
     */
    private String userName;

    /**
     * 国籍编码
     */
    private String nationalityCode;

    /**
     * 是否是司机
     */
    private Integer isDriver;

    /**
     * 是否DTL
     */
    private Integer isDtl;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 岗位ID
     */
    private Long postId;

    /**
     * 汇报上级
     */
    private Long leaderId;

    /**
     * 职级序列ID
     */
    private Long gradeId;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * offerId
     */
    private Long recruitmentJobOfferId;

    /**
     * hcId
     */
    private Long recruitmentJobHcId;

    /**
     * 预计入职时间
     */
    private Date expectedEntryDate;

    /**
     * 试用期月数
     */
    private Integer probationMonths;
}
