package com.imile.hrms.service.recruitment.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Max
 * @date: 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OfferSalaryInfoParam {

    /**
     * 发薪国
     */
    private String paymentCountry;

    /**
     *  计薪周期类型(月/单周/双周)
     */
    private String cycleType;

    private String cycleTypeDesc;

    /**
     * 计薪构成模版编码
     */
    private String templateNo;

    private String templateNameEn;

    private String templateNameCn;

    /**
     * 薪资备注
     */
    private String remark;

    /**
     * 货币精度
     */
    private Integer precision;

    /**
     * 试用期薪资
     */
    private List<ProbationarySalaryParam> probationarySalary;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProbationarySalaryParam {

        /**
         * 薪资项名称
         */
        private String name;

        private String nameEn;

        /**
         * 薪资项key
         */
        private String itemKey;

        /**
         * 试用期前(试用期工资)
         */
        private String probationarySalary;

        /**
         * 试用期后(转正工资)
         */
        private String regularSalary;

        /**
         * 货币单位
         */
        private String currency;

        /**
         * 排序
         */
        private Integer sort;
    }
}
