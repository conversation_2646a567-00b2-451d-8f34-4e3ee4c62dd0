package com.imile.hrms.service.probation.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 员工试用期目标综合表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
public class UserProbationGrowthPlanBO {

    public UserProbationGrowthPlanBO(List<AttachmentDTO> planFileList) {
        this.planFileList = planFileList;
    }

    public UserProbationGrowthPlanBO() {

    }


    /**
     * 成长计划附件
     */
    private List<AttachmentDTO> planFileList;

    /**
     * 成长计划详情
     */
    private List<UserProbationGrowthPlanDetail> userProbationGrowthPlanList;


    @Data
    public static class UserProbationGrowthPlanDetail {

        /**
         * 试用期详情id
         */
        private Long id;

        /**
         * 试用期id
         */
        private Long userProbationId;

        /**
         * 计划分类
         */
        private String planType;

        /**
         * 计划详情
         */
        private String planDetail;

        /**
         * 是否发送员工
         */
        private Integer isSubmit;

        /**
         * 最后修改日期(如果提交状态就是提交日期）
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date lastUpdDate;

    }
}
