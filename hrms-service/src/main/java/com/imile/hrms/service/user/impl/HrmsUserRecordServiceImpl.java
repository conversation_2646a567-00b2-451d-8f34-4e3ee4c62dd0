package com.imile.hrms.service.user.impl;

import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.DeptTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.SettlementDeptTypeEnum;
import com.imile.hrms.dao.organization.dto.EntDeptDTO;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.dao.user.model.HrmsUserRecordDO;
import com.imile.hrms.dao.user.query.UserRecordQuery;
import com.imile.hrms.manage.organization.HrmsBizModelManage;
import com.imile.hrms.manage.user.HrmsUserRecordManage;
import com.imile.hrms.service.organization.HrmsEntDeptService;
import com.imile.hrms.service.user.HrmsUserRecordService;
import com.imile.hrms.service.user.dto.AddOrUpdateRecordDTO;
import com.imile.hrms.service.user.dto.UserDimissionRecordDTO;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工信息记录相关
 *
 * <AUTHOR>
 * @since 2022-12-19
 */
@Service
@Slf4j
public class HrmsUserRecordServiceImpl implements HrmsUserRecordService {

    @Autowired
    private HrmsUserRecordManage userRecordManage;
    @Autowired
    private HrmsEntDeptService entDeptService;
    @Autowired
    private HrmsBizModelManage bizModelManage;
    @Autowired
    private HrmsEntDeptService hrmsEntDeptService;


    @Override
    public boolean saveAndUpdate(AddOrUpdateRecordDTO recordDTO) {
        //todo 参数校验
        HrmsUserRecordDO recordDO = new HrmsUserRecordDO();
        BeanUtils.copyProperties(recordDTO, recordDO);
//        getUserCostSettlementDept(recordDTO, recordDO);
        return userRecordManage.saveAndUpdateDO(recordDO);
    }

    @Override
    public boolean updateByDimission(UserDimissionRecordDTO recordDTO) {
        UserRecordQuery query = new UserRecordQuery();
        query.setUserId(recordDTO.getUserId());
        query.setIsLatest(BusinessConstant.Y);
        List<HrmsUserRecordDO> hrmsUserRecordDOS = userRecordManage.selectUserRecord(query);
        if (CollectionUtils.isEmpty(hrmsUserRecordDOS)) {
            return Boolean.FALSE;
            //throw BusinessException.get(HrmsErrorCodeEnums.USER_RECORD_IS_EMPTY.getDesc(), I18nUtils.getMessage(HrmsErrorCodeEnums.USER_RECORD_IS_EMPTY.getDesc()));
        }
        HrmsUserRecordDO recordDO = new HrmsUserRecordDO();
        BeanUtils.copyProperties(hrmsUserRecordDOS.get(0), recordDO);
        recordDO.setExpireDate(recordDTO.getExpireDate());
        return userRecordManage.updateDO(recordDO);
    }

    @Override
    public List<HrmsUserRecordDO> selectUserRecord(UserRecordQuery query) {
        return userRecordManage.selectUserRecord(query);
    }


    private void getUserCostSettlementDept(AddOrUpdateRecordDTO recordDTO, HrmsUserRecordDO recordDO) {
        Long deptId = recordDTO.getDeptId();
        Long ocId = recordDTO.getOcId();
        Long bizModelId = recordDTO.getBizModelId();

        EntDeptDTO deptDTO = entDeptService.getById(deptId);
        if (deptDTO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc(), I18nUtils.getMessage(HrmsErrorCodeEnums.DEPT_NOT_EXITS.getDesc()));
        }
        // 设置结算信息：1.加盟网点无需设置 2.配置了业务节点 优先设为业务节点 3.否则设置为费用承担组织（从当前部门逐级往上找）
        if (DeptTypeEnum.JOIN_STATION.getCode().equals(deptDTO.getType())) {
            return;
        }
        HrmsBizModelDO bizModelDO = bizModelManage.getBizModeById(bizModelId);
        if (bizModelDO != null && bizModelDO.getIsCostSettlement().equals(BusinessConstant.Y)) {
            recordDO.setSettlementDeptType(SettlementDeptTypeEnum.COST_BIZ_MODEL.getCode());
            recordDO.setSettlementDeptId(bizModelId);
            recordDO.setSettlementOcId(ocId);
            return;
        }
        if (deptDTO.getIsCostSettlement().equals(BusinessConstant.Y)) {
            recordDO.setSettlementDeptType(SettlementDeptTypeEnum.COST_DEPT.getCode());
            recordDO.setSettlementDeptId(deptId);
            recordDO.setSettlementOcId(ocId);
            return;
        }
        // 获取上级成本部门
        EntDeptDTO entDeptDTO = hrmsEntDeptService.getSettlementDept(deptId);
        if (entDeptDTO == null) {
            throw BusinessException.get(HrmsErrorCodeEnums.SETTLEMENT_DEPT_NOT_EXITS.getDesc(), I18nUtils.getMessage(HrmsErrorCodeEnums.SETTLEMENT_DEPT_NOT_EXITS.getDesc()));
        }
        recordDO.setSettlementDeptType(SettlementDeptTypeEnum.COST_DEPT.getCode());
        recordDO.setSettlementDeptId(entDeptDTO.getId());
        recordDO.setSettlementOcId(ocId);
    }
}
