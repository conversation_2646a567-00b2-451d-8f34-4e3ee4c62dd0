package com.imile.hrms.service.newAttendance.calendar.mapstruct;

import com.imile.hrms.dao.newAttendance.calendar.query.CalendarLegalLeaveConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarLegalLeaveConfigListQuery;
import com.imile.hrms.service.leave.param.LegalLeaveConfigQueryDetailParam;
import com.imile.hrms.service.leave.param.LegalLeaveConfigQueryParam;
import com.imile.hrms.service.leave.param.LegalLeaveConfigSaveParam;
import com.imile.hrms.service.newAttendance.calendar.command.CalendarLegalLeaveConfigAddCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarLegalLeaveConfigServiceMapstruct
 * {@code @since:} 2025-02-24 15:02
 * {@code @description:}
 */
@Mapper
public interface CalendarLegalLeaveConfigServiceMapstruct {
    CalendarLegalLeaveConfigServiceMapstruct INSTANCE = Mappers.getMapper(CalendarLegalLeaveConfigServiceMapstruct.class);

    CalendarLegalLeaveConfigAddCommand mapToAddCommand(LegalLeaveConfigSaveParam legalLeaveConfigSaveParam);

    CalendarLegalLeaveConfigDetailQuery mapToDetailQuery(LegalLeaveConfigQueryDetailParam legalLeaveConfigSaveParam);

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum", ignore = true)
    CalendarLegalLeaveConfigListQuery mapToListQuery(LegalLeaveConfigQueryParam legalLeaveConfigQueryParam);

}
