package com.imile.hrms.service.refactor.user.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.genesis.api.model.component.Phone;
import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOwnPersonalInfoBO {

    /**
     * 免冠照
     */
    @HyperLink(ref = "userAvatarUrl")
    private String userAvatar;

    /**
     * 免冠照链接
     */
    private String userAvatarUrl;

    /**
     * 国籍编码
     */
    @RelationObjectValue(beanId = "countryManageImpl", fieldId = "nationalityName",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private String nationalityCode;

    /**
     * 国籍名称
     */
    private String nationalityName;

    /**
     * 人员性别(1:男 2:女)
     */
    private Integer userSex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date userBirthday;

    /**
     * 联系电话
     */
    private Phone contactPhone;

    /**
     * 个人邮箱
     */
    private String personalEmail;

    /**
     * 现居住地址
     */
    private String postAddress;

    /**
     * 婚姻状况
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.MARITAL_STATUS, ref = "maritalStatusDesc")
    private String maritalStatus;
    private String maritalStatusDesc;

    /**
     * 人员个人扩展信息
     *
     * @see com.imile.hrms.service.refactor.user.result.UserOwnPersonalExtendInfoBO
     */
    private Object personalExtendInfo;
}
