package com.imile.hrms.service.organization;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.organization.dto.DeptRegionInfoDTO;
import com.imile.hrms.dao.organization.dto.DeptUserInfoDTO;
import com.imile.hrms.dao.organization.dto.EntDeptDTO;
import com.imile.hrms.dao.organization.dto.EntDeptInfoByLevelDTO;
import com.imile.hrms.dao.organization.dto.EntDeptTileDTO;
import com.imile.hrms.dao.organization.dto.EntOcTitleDTO;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.query.DeptQuery;
import com.imile.hrms.dao.organization.query.DeptUserQuery;
import com.imile.hrms.dao.organization.query.EntDeptInfoByLevelQuery;
import com.imile.hrms.service.organization.dto.HrmsEntDeptDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
public interface HrmsEntDeptService {
    /**
     * 根据id获取部门具体信息
     *
     * @param id
     * @return
     */
    EntDeptDTO getById(Long id);

    /**
     * 获取部门，平铺
     */
    List<EntDeptTileDTO> getDeptTile(DeptQuery query);


    /**
     * 获取上级成本核算部门
     *
     * @param deptId
     * @return
     */
    EntDeptDTO getSettlementDept(Long deptId);


    PaginationResult<DeptUserInfoDTO> getUserByDept(DeptUserQuery query);

    List<EntDeptTileDTO> getStationList(DeptQuery query);

    /**
     * 获取tms网点平铺
     *
     * @param query
     * @return
     */
    List<EntOcTitleDTO> getOcTitles(DeptQuery query);

    List<EntDeptTileDTO> getAllDeptTitle();

    List<EntDeptTileDTO> getDeptList(DeptQuery query);

    /**
     * 获取所有部门
     */
    List<EntDeptTileDTO> getAllDept();


    /**
     * 公司获取部门
     *
     * @param query
     * @return
     */
    List<EntDeptTileDTO> getDeptByCompany(DeptQuery query);
    /**
     * 获取所有非模板部门/网点
     *
     * @return
     */
    List<EntDeptTileDTO> getNonTemplateDept();

    /**
     * 根据 网点列表 获取 部门ids
     * @param ocIds
     * @return
     */
    List<Long> getDeptIdsByOcIds( List<Long> ocIds );


    /**
     * 部门平铺
     *
     * @param query
     * @return
     */
    List<EntDeptTileDTO> getDepTitleList(DeptQuery query);

    /**
     * 获取站点
     *
     * @param query
     * @return
     */
    List<EntDeptTileDTO> getStationByCountryList(DeptQuery query);

    /**
     * 获取站点
     *
     * @param query
     * @return
     */
    List<EntDeptTileDTO> getStationByCountryListNoAuth(DeptQuery query);



    /**
     * 获取部门--通过层级关系
     *
     * @param query
     * @return
     */
    List<EntDeptInfoByLevelDTO> getDeptByParentId(EntDeptInfoByLevelQuery query);

    /**
     * 获取当前及上级部门映射
     * @param deptIdList
     * @return
     */
    Map<Long, HrmsEntDeptDO> getEntBaseAndParentDeptMap(List<Long> deptIdList);


    /**
     * 获取区域
     *
     * @param country
     * @return
     */
    List<DeptRegionInfoDTO> getRegion(String country);
}
