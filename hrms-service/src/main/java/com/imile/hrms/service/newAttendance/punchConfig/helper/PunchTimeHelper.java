package com.imile.hrms.service.newAttendance.punchConfig.helper;

import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassItemConfigDO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/2/25 
 * @Description
 */
public class PunchTimeHelper {

    /**
     * 根据排序获取有序的打卡配置列表
     */
    public static List<PunchClassItemConfigDO> getSortedConfigs(List<PunchClassItemConfigDO> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return Collections.emptyList();
        }
        return configs.stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .collect(Collectors.toList());
    }

    /**
     * 获取第一个班次配置
     */
    public static PunchClassItemConfigDO getFirstConfig(List<PunchClassItemConfigDO> sortedConfigs) {
        return CollectionUtils.isEmpty(sortedConfigs) ? null : sortedConfigs.get(0);
    }

    /**
     * 获取最后一个班次配置
     */
    public static PunchClassItemConfigDO getLastConfig(List<PunchClassItemConfigDO> sortedConfigs) {
        return CollectionUtils.isEmpty(sortedConfigs) ? null : sortedConfigs.get(sortedConfigs.size() - 1);
    }
}
