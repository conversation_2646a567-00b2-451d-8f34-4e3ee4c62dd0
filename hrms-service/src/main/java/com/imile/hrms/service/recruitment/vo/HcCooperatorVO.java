package com.imile.hrms.service.recruitment.vo;

import com.imile.hrms.service.recruitment.dto.UserBaseDTO;
import com.imile.hrms.service.recruitment.param.JobAddParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HcCooperatorVO {


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 协助人 ID
     */
    private Long cooperatorUserId;

    /**
     * 协助人userCode
     */
    private String cooperatorUserCode;

    /**
     * 协助人
     */
    private String cooperatorUserName;

}
