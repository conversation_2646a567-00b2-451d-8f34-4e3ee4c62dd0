package com.imile.hrms.service.user;


import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.blacklist.dto.LockdownInfoDTO;
import com.imile.hrms.dao.user.dto.DraftDTO;
import com.imile.hrms.dao.user.dto.EmailDTO;
import com.imile.hrms.service.user.param.UserEntryAddParam;
import com.imile.hrms.service.user.param.UserEntryConfirmParam;
import com.imile.hrms.service.user.param.UserEntryInfoSaveParam;
import com.imile.hrms.service.user.param.UserEntryInitParam;
import com.imile.hrms.service.user.param.UserEntryListParam;
import com.imile.hrms.service.user.param.UserEntryRegisterParam;
import com.imile.hrms.service.user.result.UserEntryInfoBO;
import com.imile.hrms.service.user.result.UserEntryRegistrationInfoBO;
import com.imile.hrms.service.user.vo.UserEntryVO;

import java.util.Date;

/**
 * 人员入职
 *
 * <AUTHOR>
 * @since 2024/4/3
 */
public interface UserEntryService {

    /**
     * 获取人员入职记录列表
     *
     * @param param UserEntryListParam
     * @return PaginationResult<UserEntryDTO>
     */
    PaginationResult<UserEntryVO> list(UserEntryListParam param);

    /**
     * 获取人员入职登记信息
     *
     * @param token 令牌
     * @return UserEntryRegistrationInfoBO
     */
    UserEntryRegistrationInfoBO getUserEntryRegistrationInfo(String token);

    /**
     * 获取人员入职信息
     *
     * @param userId 人员ID
     * @return UserEntryDetailBO
     */
    UserEntryInfoBO getUserEntryInfo(Long userId);

    /**
     * 入职初始化
     *
     * @param param UserEntryInitParam
     * @return Long
     */
    Long init(UserEntryInitParam param);

    /**
     * 办理入职
     *
     * @param param UserEntryAddParam
     * @return Long
     */
    Long add(UserEntryAddParam param);

    /**
     * 生成令牌
     *
     * @param userId 人员ID
     * @return String
     */
    String generateToken(Long userId);

    /**
     * 发送邮件
     *
     * @param email EmailDTO
     */
    void sendEmail(EmailDTO email);

    /**
     * 保存草稿
     *
     * @param param DraftDTO
     * @return Boolean
     */
    Boolean saveDraft(DraftDTO param);

    /**
     * 入职登记
     *
     * @param param UserEntryRegisterParam
     * @return Boolean
     */
    Boolean register(UserEntryRegisterParam param);

    /**
     * 保存人员入职信息
     *
     * @param param UserEntryInfoSaveParam
     * @return Boolean
     */
    Boolean saveUserEntryInfo(UserEntryInfoSaveParam param);

    /**
     * 确认入职前置校验
     *
     * @param userId 人员ID
     */
    void checkConfirm(Long userId);

    /**
     * 确认入职黑名单前置校验
     *
     * @param userId 人员ID
     */
    LockdownInfoDTO checkBlacklistInConfirm(Long userId);

    /**
     * 确认入职
     *
     * @param param UserEntryConfirmParam
     */
    void confirm(UserEntryConfirmParam param);

    /**
     * 放弃入职
     *
     * @param userId 人员ID
     */
    void cancel(Long userId);

    /**
     * 入职后业务逻辑处理
     *
     * @param userCode 人员编码
     */
    void doBusinessAfterEntry(String userCode);

    /**
     * 初始化入职快照
     *
     * @param userIds 人员ID集合
     */
    void initEntrySnapshot(String userIds);

    /**
     * 更新预计入职日期
     *
     * @param userId            人员ID
     * @param expectedEntryDate 预计入职日期
     */
    void updateExpectedEntryDate(Long userId, Date expectedEntryDate);
}
