package com.imile.hrms.service.user.result;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class FreelancerDriverListBO {

    /**
     * 人员ID
     */
    private Long id;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 国家
     */
    private String country;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 账号状态（ACTIVE:启用 DISABLED:停用）
     */
    private String status;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    private Date lastUpdDate;
}
