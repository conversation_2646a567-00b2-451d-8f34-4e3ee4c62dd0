package com.imile.hrms.service.travelExpenses.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.travelExpenses.mapper.HrmsTravelExpensesBaseConfigMapper;
import com.imile.hrms.dao.travelExpenses.model.HrmsTravelExpensesBaseConfigDO;
import com.imile.hrms.service.travelExpenses.HrmsTravelExpensesBaseConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 差旅费用基础配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Service
public class HrmsTravelExpensesBaseConfigServiceImpl extends ServiceImpl<HrmsTravelExpensesBaseConfigMapper, HrmsTravelExpensesBaseConfigDO> implements HrmsTravelExpensesBaseConfigService {

}
