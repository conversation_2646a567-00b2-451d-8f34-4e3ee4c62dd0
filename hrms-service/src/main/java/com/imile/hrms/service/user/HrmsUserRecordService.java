package com.imile.hrms.service.user;

import com.imile.hrms.dao.user.model.HrmsUserRecordDO;
import com.imile.hrms.dao.user.query.UserRecordQuery;
import com.imile.hrms.service.user.dto.AddOrUpdateRecordDTO;
import com.imile.hrms.service.user.dto.UserDimissionRecordDTO;

import java.util.List;

public interface HrmsUserRecordService {

    boolean saveAndUpdate(AddOrUpdateRecordDTO recordDTO);

    boolean updateByDimission(UserDimissionRecordDTO recordDTO);


    // 根据条件查询员工的记录信息
    List<HrmsUserRecordDO> selectUserRecord(UserRecordQuery query);
}
