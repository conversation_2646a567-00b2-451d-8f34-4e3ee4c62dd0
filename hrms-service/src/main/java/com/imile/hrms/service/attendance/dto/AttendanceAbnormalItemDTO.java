//package com.imile.hrms.service.attendance.dto;
//
//import lombok.Data;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.List;
//
//@Data
//public class AttendanceAbnormalItemDTO implements Serializable {
//    private static final long serialVersionUID = -183913064945099895L;
//
//    private Long id;
//
//    /**
//     * 异常id
//     */
//    private Long abnormalId;
//
//    /**
//     * 用户id
//     */
//    private Long userId;
//
//    /**
//     * 打卡班次时段id
//     */
//    private Long punchClassItemConfigId;
//
//    /**
//     * 异常类型
//     */
//    private String abnormalType;
//
//    /**
//     * 迟到时长(分钟)
//     */
//    private BigDecimal lateDuration;
//
//    /**
//     * 早退时长(分钟)
//     */
//    private BigDecimal earlyLeaveDuration;
//
//    /**
//     * 缺卡次数
//     */
//    private Integer lack;
//
//    /**
//     * 出勤时长
//     */
//    private BigDecimal attendanceDuration;
//
//    /**
//     * 缺勤时长
//     */
//    private BigDecimal absenceDuration;
//
//    private List<PunchCardDTO> punchCardList;
//}
