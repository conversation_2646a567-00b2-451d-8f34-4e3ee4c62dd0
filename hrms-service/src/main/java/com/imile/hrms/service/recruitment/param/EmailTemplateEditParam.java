package com.imile.hrms.service.recruitment.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplateEditParam {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模版名称
     */
    @NotBlank(message = ValidCodeConstant.NOT_NULL)
    private String title;

    /**
     * 邮件正文
     */
    @NotBlank(message = ValidCodeConstant.NOT_NULL)
    private String content;

    /**
     * 备注
     */
    private String remark;
}
