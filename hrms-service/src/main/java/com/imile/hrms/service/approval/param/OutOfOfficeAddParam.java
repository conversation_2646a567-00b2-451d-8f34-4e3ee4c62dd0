package com.imile.hrms.service.approval.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import com.imile.hrms.service.approval.dto.DayDurationInfoDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-26
 * @version: 1.0
 */
@Data
public class OutOfOfficeAddParam {
    /**
     * 申请单据ID(新增无需传)
     */
    private Long applicationFormId;

    /**
     * 申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long applyUserId;

    /**
     * 被申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 请假开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outOfOfficeStartDate;

    /**
     * 请假结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outOfOfficeEndDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;


    //后端自己生成的业务逻辑，前端无需传递

    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人所在国
     */
    private String country;

    /**
     * 被申请人所属结算国
     */
    private String originCountry;

    /**
     * 是否仓内员工
     */
    private Integer isWarehouseStaff;

    /**
     * 每天时长计算明细
     */
    private List<DayDurationInfoDTO> dayDurationInfoDTOList;

}
