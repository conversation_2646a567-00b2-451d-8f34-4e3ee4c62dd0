package com.imile.hrms.service.newAttendance.punchConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/2/16
 * @Description
 */
@Data
public class PunchConfigAuthUpdateCommand {

    /**
     * 更新的时候需要传递
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String configNo;

    /**
     * 主负责人
     */
    private String principalUserCode;

    /**
     * 子负责人
     */
    private String subUserCodes;
}
