package com.imile.hrms.service.user.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/6/17
 */
@Data
@AllArgsConstructor
public class UserExtendAttrSaveParam {

    /**
     * 属性键
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String attrKey;

    /**
     * 属性值
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String attrValue;

    public static UserExtendAttrSaveParam of(String attrKey, String attrValue) {
        return new UserExtendAttrSaveParam(attrKey, attrValue);
    }
}
