package com.imile.hrms.service.recruitment.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Max
 * @date: 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OfferJobInfoParam {

    /**
     * 岗位id
     */
    private Long jobId;

    /**
     * 岗位hc id
     */
    private Long hcId;

    /**
     * 单据号
     */
    private String applicationCode;

    /**
     * 业务领域
     */
    private Integer professionalFunction;

    /**
     * 业务领域描述
     */
    private String professionalFunctionDesc;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门长名称，从根部门到当前部门
     */
    private List<String> deptLongName;


    /**
     * 业务所属国
     */
    private List<String> bizCountry;

    /**
     * 部门所在国家
     */
    private String deptCountry;

    /**
     * 职级
     */
    private Long gradeId;

    private String gradeIdStr;

    /**
     * 职级序列
     */
    private String gradeNo;

    /**
     * 汇报人
     */
    private Long reportToUserId;

    private String reportToUser;

    /**
     * 网点
     */
    private Long ocId;

    private String ocName;

    // 下方字段，前端展示用，不存数据库
//    /**
//     * 预算最小值
//     */
//    private BigDecimal budgetMin;
//
//    /**
//     * 预算最大值
//     */
//    private BigDecimal budgetMax;
//
//    /**
//     * 货币
//     */
//    private String currency;

    /**
     * 是否中方发起，1=是，0=否
     */
    private Integer isChineseInitiating;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectiveTime;

    /**
     * HC职级
     */
    private Long hcGradeId;

    private String hcGradeIdStr;

    /**
     * HC职级序列
     */
    private String hcGradeNo;

    /**
     * 汇报人
     */
    private Long hcReportToUserId;

    private String hcReportToUser;

    /**
     * 部门id
     */
    private Long hcDeptId;

    /**
     * 部门名称
     */
    private String hcDeptName;

    /**
     * 部门长名称，从根部门到当前部门
     */
    private List<String> hcDeptLongName;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employmentTypeStr")
    private String employmentType;

    private String employmentTypeStr;

    /**
     * 请求类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.HC_REQUEST_TYPE_ENUM, ref = "requestTypeStr")
    private Integer requestType;

    private String requestTypeStr;

    /**
     * 招聘类型 0:社会招聘 1:校园招聘 3:实习生招聘
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.RECRUITMENT_TYPE_ENUM, ref = "recruitmentTypeDesc")
    private Integer recruitmentType;

    /**
     * 招聘类型 0:社会招聘 1:校园招聘 3:实习生招聘
     */
    private String recruitmentTypeDesc;

}
