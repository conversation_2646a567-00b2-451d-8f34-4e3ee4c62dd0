package com.imile.hrms.service.refactor.report.result;

import com.imile.hrms.common.annotation.RelationObjectValue;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.RelationObjectValueStrategy;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class UserPermissionReportListBO {

    /**
     * userId
     */
    private Long id;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 姓名全称
     */
    private String userName;

    /**
     * 姓名全称
     */
    private String userNameEn;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_STATUS, ref = "workStatusDesc")
    private String workStatus;
    private String workStatusDesc;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;
    private String employeeTypeDesc;

    /**
     * 一级部门
     */
    private String firstLevelDeptName;

    /**
     * 二级部门
     */
    private String secondLevelDeptName;

    /**
     * 部门
     */
    @RelationObjectValue(beanId = "hrmsDeptNewManageImpl", fieldId = "deptName", extendFieldIdList = "deptNamePath",
            fieldStrategy = RelationObjectValueStrategy.ADAPTIVE_EN_PRIORITY)
    private Long deptId;
    private String deptName;
    private String deptNamePath;

    /**
     * 岗位
     */
    @RelationObjectValue(beanId = "postManageImpl", fieldId = "postName", fieldStrategy = RelationObjectValueStrategy.ADAPTIVE)
    private Long postId;
    private String postName;

    /**
     * 角色信息
     */
    List<RoleInfoBO> roleList;

    /**
     * 生效的角色
     */
    private String effectRoleNameStr;

    /**
     * 生效的角色
     */
    private String effectRoleNameEnStr;

    /**
     * 失效的角色
     */
    private String expireRoleNameStr;

    /**
     * 失效的角色
     */
    private String expireRoleNameEnStr;

    @Data
    @Builder
    public static class RoleInfoBO {
        private Long roleId;
        private String roleName;
        private String roleNameEn;
        private String status;
    }
}
