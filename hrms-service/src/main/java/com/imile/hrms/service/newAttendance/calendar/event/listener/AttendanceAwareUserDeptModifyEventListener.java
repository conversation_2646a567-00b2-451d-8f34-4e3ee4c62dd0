package com.imile.hrms.service.newAttendance.calendar.event.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.leave.HrmsCompanyLeaveConfigRangDao;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigRangDO;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.service.attendance.dto.AttendanceAndPunchHandlerDTO;
import com.imile.hrms.service.newAttendance.calendar.adapter.CalendarServiceAdapter;
import com.imile.hrms.service.newAttendance.calendar.event.domain.ChangeUserInfo;
import com.imile.hrms.service.newAttendance.calendar.event.domain.EmployeeDeptModifyEvent;
import com.imile.hrms.service.newAttendance.calendar.event.domain.WareHouseUserDeptModifyEvent;
import com.imile.hrms.service.newAttendance.common.AttendanceDeptService;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceDept;
import com.imile.hrms.service.salary.HrmsSalaryBaseService;
import com.imile.hrms.service.salary.dto.SalaryCountryMessageNoticeDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/2/12
 * @Description
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AttendanceAwareUserDeptModifyEventListener {


    private final CalendarServiceAdapter calendarServiceAdapter;
    private final HrmsSalaryBaseService hrmsSalaryBaseService;
//    private final HrmsEntDeptDao hrmsEntDeptDao;
    private final AttendanceDeptService deptService;
    private final HrmsCompanyLeaveConfigRangDao hrmsCompanyLeaveConfigRangDao;

    /**
     * 同步执行
     */
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2))
    @EventListener
    public void onEmployeeDeptModifyEvent(EmployeeDeptModifyEvent event) {
        if (Objects.isNull(event.getData())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
        }
        ChangeUserInfo changeUserInfo = event.getData();
        HrmsUserInfoDO userInfoDO = changeUserInfo.getUserInfoDO();
        HrmsUserInfoDO oldUserInfoRecord = changeUserInfo.getOldUserInfoRecord();

        long startTime = System.currentTimeMillis();
        log.info("EmployeeDeptModifyEvent | startTime:{},userInfoDO:{},oldUserInfoRecord:{}",
                startTime, userInfoDO, oldUserInfoRecord);
        //用户部门变动需要重新匹配日历和打卡规则的事件
        attendanceAndPunchConfigHandler(userInfoDO, oldUserInfoRecord);

        log.info("EmployeeDeptModifyEvent | endTime:{}", startTime);
        log.info("EmployeeDeptModifyEvent | 耗时:{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 同步执行
     */
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2))
    @EventListener
    public void onEmployeeDeptModifyEvent(WareHouseUserDeptModifyEvent event) {
        if (Objects.isNull(event.getData())) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PARAM_NOT_NULL);
        }
        ChangeUserInfo changeUserInfo = event.getData();
        HrmsUserInfoDO userInfoDO = changeUserInfo.getUserInfoDO();
        HrmsUserInfoDO oldUserInfoRecord = changeUserInfo.getOldUserInfoRecord();

        long startTime = System.currentTimeMillis();
        log.info("WareHouseUserDeptModifyEvent | startTime:{},userInfoDO:{},oldUserInfoRecord:{}",
                startTime, userInfoDO, oldUserInfoRecord);
        //仓内外包部门变动需要重新匹配日历和打卡规则的事件
        warehouseAttendanceAndPunchConfigHandler(userInfoDO, oldUserInfoRecord);
        log.info("WareHouseUserDeptModifyEvent | endTime:{}", startTime);
        log.info("WareHouseUserDeptModifyEvent | 耗时:{}", System.currentTimeMillis() - startTime);
    }


    private void attendanceAndPunchConfigHandler(HrmsUserInfoDO userInfoDO, HrmsUserInfoDO oldRecord) {
        log.info("userAttendanceAndPunchHandler | userInfoDO:{} | oldRecord:{}",
                JSON.toJSON(userInfoDO), JSON.toJSON(oldRecord));
        if (StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), userInfoDO.getWorkStatus())) {
            return;
        }
        if ((oldRecord.getDeptId() != null && userInfoDO.getDeptId() != null && oldRecord.getDeptId().compareTo(userInfoDO.getDeptId()) != 0)) {
            //常规国是否发生变动，需要薪资消息提示
            SalaryCountryMessageNoticeDTO noticeDTO = new SalaryCountryMessageNoticeDTO();
            noticeDTO.setUserId(userInfoDO.getId());
            noticeDTO.setBeforeCountry(oldRecord.getOriginCountry());
            noticeDTO.setAfterCountry(userInfoDO.getOriginCountry());
            hrmsSalaryBaseService.salaryCountryMessageNoticeHandler(Arrays.asList(noticeDTO));
        }

        // 查询新的部门信息
//        HrmsEntDeptDO newDeptInfo = hrmsEntDeptDao.getById(userInfoDO.getDeptId());
        AttendanceDept newDeptInfo = deptService.getByDeptId(userInfoDO.getDeptId());
        if (ObjectUtil.isNull(newDeptInfo)) {
            log.info("userAttendanceAndPunchHandler | newDeptInfo is null | newDeptInfo:{}", userInfoDO.getDeptId());
            return;
        }
        // 如果常驻国或国家变动，需要重新匹配考勤日历、打卡规则、假期绑定范围
        if (ObjectUtil.notEqual(userInfoDO.getLocationCountry(), oldRecord.getLocationCountry()) || ObjectUtil.notEqual(userInfoDO.getDeptId(), oldRecord.getDeptId())) {
            log.info("userAttendanceAndPunchHandler | country or deptId changed");
            List<AttendanceAndPunchHandlerDTO> attendanceAndPunchHandlerDTOList = new ArrayList<>();
            AttendanceAndPunchHandlerDTO attendanceAndPunchHandlerDTO = new AttendanceAndPunchHandlerDTO();
            attendanceAndPunchHandlerDTO.setUserId(userInfoDO.getId());
            // userInfoDO没有设置userCode，oldRecord有设置userCode
            attendanceAndPunchHandlerDTO.setUserCode(oldRecord.getUserCode());
            attendanceAndPunchHandlerDTO.setNewCountry(userInfoDO.getLocationCountry());
            attendanceAndPunchHandlerDTO.setNewDeptId(userInfoDO.getDeptId());
            attendanceAndPunchHandlerDTO.setOldCountry(oldRecord.getLocationCountry());
            attendanceAndPunchHandlerDTO.setOldDeptId(oldRecord.getDeptId());
            attendanceAndPunchHandlerDTOList.add(attendanceAndPunchHandlerDTO);
//            hrmsAttendanceConfigService.userAttendanceAndPunchHandler(attendanceAndPunchHandlerDTOList);
            calendarServiceAdapter.userCalendarAndPunchHandler(attendanceAndPunchHandlerDTOList);
            return;
        }

        // 如果用工类型、性别发生变化
        if (ObjectUtil.notEqual(userInfoDO.getEmployeeType(), oldRecord.getEmployeeType()) || ObjectUtil.notEqual(userInfoDO.getSex(), oldRecord.getSex())) {
            log.info("userAttendanceAndPunchHandler | employeeType or sex changed");
            // 处理假期数据
            // 假期范围
            List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang = Lists.newArrayList();
            List<HrmsCompanyLeaveConfigRangDO> addLeaveRang = Lists.newArrayList();
            // 1. 删除之前绑定的范围数据
            List<String> userCodeList = new ArrayList<>();
            userCodeList.add(oldRecord.getUserCode());
            // 查询当前所有用户绑定的假期范围
            List<HrmsCompanyLeaveConfigRangDO> updateConfigRang = hrmsCompanyLeaveConfigRangDao.selectRangByUserCode(userCodeList);
            // 2. 新增新的范围数据
//            hrmsAttendanceConfigService.handlerCompanyLeaveConfigRang(userInfoDO.getId(), updateConfigRang, addLeaveRang, updateLeaveRang);
            calendarServiceAdapter.handlerCompanyLeaveConfigRang(userInfoDO.getId(), updateConfigRang, addLeaveRang, updateLeaveRang);
            // 落库
            hrmsCompanyLeaveConfigRangDao.handlerCompanyLeaveConfigRang(addLeaveRang, updateLeaveRang);
        }
    }


    private void warehouseAttendanceAndPunchConfigHandler(HrmsUserInfoDO userInfoDO, HrmsUserInfoDO oldRecord) {
        log.info("warehouseAttendanceAndPunchConfigHandler | userInfoDO:{} | oldRecord:{}",
                JSON.toJSON(userInfoDO), JSON.toJSON(oldRecord));
        if (StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), userInfoDO.getWorkStatus())) {
            return;
        }
        if ((oldRecord.getDeptId() != null && userInfoDO.getDeptId() != null && oldRecord.getDeptId().compareTo(userInfoDO.getDeptId()) != 0)) {
            //常规国是否发生变动，需要薪资消息提示
            SalaryCountryMessageNoticeDTO noticeDTO = new SalaryCountryMessageNoticeDTO();
            noticeDTO.setUserId(userInfoDO.getId());
            noticeDTO.setBeforeCountry(oldRecord.getOriginCountry());
            noticeDTO.setAfterCountry(userInfoDO.getOriginCountry());
            hrmsSalaryBaseService.salaryCountryMessageNoticeHandler(Arrays.asList(noticeDTO));
        }

        // 查询新的部门信息
//        HrmsEntDeptDO newDeptInfo = hrmsEntDeptDao.getById(userInfoDO.getDeptId());
        AttendanceDept newDeptInfo = deptService.getByDeptId(userInfoDO.getDeptId());
        if (ObjectUtil.isNull(newDeptInfo)) {
            log.info("warehouseAttendanceAndPunchConfigHandler | newDeptInfo is null | newDeptInfo:{}", userInfoDO.getDeptId());
            return;
        }
        // 如果常驻国或国家变动，需要重新匹配考勤日历、打卡规则、假期绑定范围
        if (ObjectUtil.notEqual(userInfoDO.getLocationCountry(), oldRecord.getLocationCountry()) || ObjectUtil.notEqual(userInfoDO.getDeptId(), oldRecord.getDeptId())) {
            log.info("warehouseAttendanceAndPunchConfigHandler | country or deptId changed");
            List<AttendanceAndPunchHandlerDTO> attendanceAndPunchHandlerDTOList = new ArrayList<>();
            AttendanceAndPunchHandlerDTO attendanceAndPunchHandlerDTO = new AttendanceAndPunchHandlerDTO();
            attendanceAndPunchHandlerDTO.setUserId(userInfoDO.getId());
            // userInfoDO没有设置userCode，oldRecord有设置userCode
            attendanceAndPunchHandlerDTO.setUserCode(oldRecord.getUserCode());
            attendanceAndPunchHandlerDTO.setNewCountry(userInfoDO.getLocationCountry());
            attendanceAndPunchHandlerDTO.setNewDeptId(userInfoDO.getDeptId());
            attendanceAndPunchHandlerDTO.setOldCountry(oldRecord.getLocationCountry());
            attendanceAndPunchHandlerDTO.setOldDeptId(oldRecord.getDeptId());
            attendanceAndPunchHandlerDTOList.add(attendanceAndPunchHandlerDTO);
            calendarServiceAdapter.warehouseUserCalendarAndPunchHandler(attendanceAndPunchHandlerDTOList);
            return;
        }

        // 如果用工类型、性别发生变化
        if (ObjectUtil.notEqual(userInfoDO.getEmployeeType(), oldRecord.getEmployeeType()) || ObjectUtil.notEqual(userInfoDO.getSex(), oldRecord.getSex())) {
            log.info("warehouseAttendanceAndPunchConfigHandler | employeeType or sex changed");
            // 处理假期数据
            // 假期范围
            List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang = Lists.newArrayList();
            List<HrmsCompanyLeaveConfigRangDO> addLeaveRang = Lists.newArrayList();
            // 1. 删除之前绑定的范围数据
            List<String> userCodeList = new ArrayList<>();
            userCodeList.add(oldRecord.getUserCode());
            // 查询当前所有用户绑定的假期范围
            List<HrmsCompanyLeaveConfigRangDO> updateConfigRang = hrmsCompanyLeaveConfigRangDao.selectRangByUserCode(userCodeList);
            // 2. 新增新的范围数据
            calendarServiceAdapter.handlerCompanyLeaveConfigRang(userInfoDO.getId(), updateConfigRang, addLeaveRang, updateLeaveRang);
            // 落库
            hrmsCompanyLeaveConfigRangDao.handlerCompanyLeaveConfigRang(addLeaveRang, updateLeaveRang);
        }
    }


}
