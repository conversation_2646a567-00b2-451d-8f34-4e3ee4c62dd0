package com.imile.hrms.service.achievement.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 指标结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
public class AchievementsTargetResultAddDTO {


    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 目标明细id
     */
    private Long targetItemId;

    /**
     * 完成日期
     */
    private String comDate;

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private String month;

    /**
     * 备注
     */
    private String remark;

    /**
     * 完成值
     */
    private BigDecimal completionValue;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 计算规则01按比例 02按绝对 03手动填写
     */
    private String calculationRuleType;

    /**
     * 周期数
     */
    private Integer cycleNum;

    /**
     * 指标库编码
     */
    private String indicatorLibraryCode;

    /**
     * 周期数量
     */
    private Integer totalNum;

}
