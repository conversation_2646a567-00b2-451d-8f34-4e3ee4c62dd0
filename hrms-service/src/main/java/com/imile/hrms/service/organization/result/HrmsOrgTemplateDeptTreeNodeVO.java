package com.imile.hrms.service.organization.result;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/1
 */
@Data
public class HrmsOrgTemplateDeptTreeNodeVO {

    /**
     * 组织模板部门ID
     */
    private Long id;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 子级部门列表
     */
    private List<HrmsOrgTemplateDeptTreeNodeVO> childrenList;
}
