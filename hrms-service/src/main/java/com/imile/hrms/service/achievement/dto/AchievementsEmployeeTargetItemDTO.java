package com.imile.hrms.service.achievement.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AchievementsEmployeeTargetItemDTO {

    /**
     * 指标明细
     */
    private List<AchievementsEmployerTargetItemDTO> itemList;

    /**
     * 定性权重
     */
    private BigDecimal qualitativeWeight;

    /**
     * 定量权重
     */
    private BigDecimal quantifyWeight;
}
