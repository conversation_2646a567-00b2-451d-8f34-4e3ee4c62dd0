package com.imile.hrms.service.account.handle;

import com.imile.hrms.common.enums.account.AccountBizTypeEnum;
import com.imile.hrms.common.enums.account.AccountTypeEnum;
import com.imile.hrms.integration.hermes.dto.AccountHandleResult;

/**
 * 账号处理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/6
 */
public interface IAccountHandle<T, P> {

    /**
     * 账号创建
     *
     * @param param 参数
     * @return
     */
    AccountHandleResult<T> create(P param);

    /**
     * 账号删除
     *
     * @param param 参数
     * @return
     */
    AccountHandleResult<T> delete(P param);

    /**
     * 重试操作
     *
     * @param accountBizTypeEnum
     * @param bizId              业务ID
     * @return
     */
    AccountHandleResult<T> retry(AccountBizTypeEnum accountBizTypeEnum, String bizId);

    /**
     * 账号类型
     *
     * @return
     */
    AccountTypeEnum getAccountType();

}
