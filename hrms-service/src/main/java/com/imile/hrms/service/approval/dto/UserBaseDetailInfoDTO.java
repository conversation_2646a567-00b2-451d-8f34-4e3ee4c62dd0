package com.imile.hrms.service.approval.dto;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-10-10
 * @version: 1.0
 */
@Data
public class UserBaseDetailInfoDTO {

    /**
     * 当前详情用户ID
     */
    private Long userId;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 国家
     */
    private String country;


    /**
     * 用户系统名称
     */
    private String sysAccountName;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeTypeDesc;
    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 是否是司机
     */
    private Integer isDriver;
    /**
     * 是否是司机领导(dtl)
     */
    private Integer isDtl;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 工作岗位
     */
    private Long postId;
    /**
     * 工作岗位名称
     */
    private String postName;


    /**
     * 上级用户id
     */
    private Long leaderId;
    /**
     * 上级用户名称
     */
    private String leaderName;


    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_STATUS, ref = "workStatusDesc")
    private String workStatus;
    private String workStatusDesc;
}
