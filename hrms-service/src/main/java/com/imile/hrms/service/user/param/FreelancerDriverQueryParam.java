package com.imile.hrms.service.user.param;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FreelancerDriverQueryParam extends BaseQuery {

    private static final long serialVersionUID = -7797560498961526153L;

    /**
     * 关键字类型（1:账号/姓名 2:邮箱/电话）
     */
    private Integer keywordType;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 账号状态（ACTIVE:启用 DISABLED:停用）
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 网点编码
     */
    private String ocCode;
}
