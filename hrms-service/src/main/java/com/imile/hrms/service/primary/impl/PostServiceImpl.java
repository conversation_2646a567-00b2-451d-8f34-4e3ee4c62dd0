package com.imile.hrms.service.primary.impl;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.api.primary.model.param.base.PostConditionParam;
import com.imile.hrms.api.primary.model.result.base.PostDTO;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.organization.query.PostConditionBuilder;
import com.imile.hrms.dao.primary.dao.PostDao;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.service.primary.PostService;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Slf4j
@Service
public class PostServiceImpl implements PostService {

    @Autowired
    private PostDao postDao;

    @Override
    public PostDTO getPostById(Long id) {
        PostDO post = postDao.getById(id);
        if (Objects.isNull(post) || IsDeleteEnum.YES.getCode().equals(post.getIsDelete())) {
            log.error("岗位不存在或已删除,id:{}", id);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.POST_NOT_EXITS);
        }
        return BeanUtils.convert(post, PostDTO.class);
    }

    @Override
    public List<PostDTO> listPostByCondition(PostConditionParam param) {
        List<PostDO> postList = postDao.selectByCondition(PostConditionBuilder.builder()
                .id(param.getId())
                .idList(param.getIdList())
                .status(param.getStatus())
                .build());
        if (postList.isEmpty()) {
            return Collections.emptyList();
        }
        return postList.stream()
                .map(post -> PostDTO.builder()
                        .id(post.getId())
                        .postNameCn(post.getPostNameCn())
                        .postNameEn(post.getPostNameEn())
                        .status(post.getStatus())
                        .build())
                .collect(Collectors.toList());
    }
}
