package com.imile.hrms.service.approval.dto;

import lombok.Data;

/**
 * {@code @author:} allen
 * {@code @className:} UserOverTimeImportDTO
 * {@code @since:} 2024-06-13 16:47
 * {@code @description:}
 */
@Data
public class UserOverTimeImportDTO {

    /**
     * 被申请人用户code
     */
    private String userCode;

    /**
     * 加班开始时间
     */
    private String overTimeStartDate;

    /**
     * 请假结束时间
     */
    private String overTimeEndDate;

    /**
     * 预计加班时长
     */
    private String overTimeDuration;

}
