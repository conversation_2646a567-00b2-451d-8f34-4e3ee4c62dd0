package com.imile.hrms.service.punch.param.warehouse;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @project hrms
 * @description 入离仓参数
 * @date 2024/7/4 11:02:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InV2Param extends InOrOutParam {

    @NotNull(message = "originUserId cannot be empty")
    private Long originUserId;

    @NotNull(message = "certificatesCode cannot be empty")
    private String certificatesCode;
}
