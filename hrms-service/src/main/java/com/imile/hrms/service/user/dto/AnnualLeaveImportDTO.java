package com.imile.hrms.service.user.dto;

import com.imile.common.excel.ExcelImport;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-8-15
 * @version: 1.0
 */
@Data
public class AnnualLeaveImportDTO extends ExcelImport {

    /**
     * 入职时间(格式化前)
     */
    private String entryDate;

    /**
     * 入职时间(格式化后)
     */
    private Date entryDateFormat;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 证件号码
     */
    private String certificateCode;

    /**
     * 年假已使用时间天数（格式化前）
     */
    private String useDays;

    /**
     * 年假已使用时间天数（格式化后）
     */
    private Integer useDaysFormat;
}
