package com.imile.hrms.service.achievement.impl.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.api.achievement.api.AchievementApi;
import com.imile.hrms.api.achievement.dto.AchievementsDeptOperateRelationRpcDTO;
import com.imile.hrms.dao.achievement.model.AchievementsDeptOperateRelationDO;
import com.imile.hrms.service.achievement.AchievementsDeptOperateRelationService;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;

@Service(version = "1.0.0")
public class AchievementApiImpl implements AchievementApi {

    @Resource
    private AchievementsDeptOperateRelationService achievementsDeptOperateRelationService;

    @Override
    public RpcResult<List<AchievementsDeptOperateRelationRpcDTO>> getAchievementsDeptOperateRelation() {
        List<AchievementsDeptOperateRelationDO> list = achievementsDeptOperateRelationService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<AchievementsDeptOperateRelationDO>()
                        .eq(AchievementsDeptOperateRelationDO::getIsDelete, IsDeleteEnum.NO.getCode())
                );
        return RpcResult.ok(BeanUtils.convert(AchievementsDeptOperateRelationRpcDTO.class,list));
    }
}
