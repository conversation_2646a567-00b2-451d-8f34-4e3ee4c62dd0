package com.imile.hrms.service.punch.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchConfigDao;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchConfigRangeDao;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.punch.HrmsAttendancePunchConfigRangeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2022/1/25
 */
@Slf4j
@Service
public class HrmsAttendancePunchConfigRangeServiceImpl extends BaseService implements HrmsAttendancePunchConfigRangeService {

    @Autowired
    private HrmsAttendancePunchConfigDao punchConfigDao;
    @Autowired
    private HrmsAttendancePunchConfigRangeDao punchConfigRangeDao;
    @Autowired
    private HrmsUserEntryRecordManage userEntryRecordManage;


    @Override
    public boolean addAttendancePunchConfigRange(HrmsUserInfoDO userInfoDO) {
        List<HrmsAttendancePunchConfigDO> configList = punchConfigDao.selectConfigByCompanyAndType(userInfoDO.getLocationCountry(), null);
        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }
        List<HrmsAttendancePunchConfigDO> defaultAttendancePunchConfigDOList = configList.stream().filter(o -> o.getIsDefault() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultAttendancePunchConfigDOList)) {
            return false;
        }
        Date effectTime = new Date();
        //查询入职确认时间作为日历适用范围生效时间
        HrmsUserEntryRecordDO userEntryRecordDO = userEntryRecordManage.getByUserId(userInfoDO.getId());
        if (Objects.nonNull(userEntryRecordDO) && Objects.nonNull(userEntryRecordDO.getConfirmDate())) {
            effectTime = userEntryRecordDO.getConfirmDate();
        }
        HrmsAttendancePunchConfigDO defaultConfig = defaultAttendancePunchConfigDOList.get(0);

        List<HrmsAttendancePunchConfigDO> customList = configList.stream().filter(o -> o.getIsDefault() == 0).collect(Collectors.toList());
        for (HrmsAttendancePunchConfigDO item : customList) {
            if (StringUtils.isBlank(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(item.getDeptIds().split(","), Long.class));
            if (deptIds.contains(userInfoDO.getDeptId())) {
                return addPunchConfigRangeDO(item, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.Y, effectTime);
            }
        }

        //属于默认打卡规则，看是不是免打卡
        List<Long> deptIds = new ArrayList<>();
        if (StringUtils.isNotBlank(defaultConfig.getDeptIds())) {
            deptIds = Arrays.asList((Long[]) ConvertUtils.convert(defaultConfig.getDeptIds().split(","), Long.class));
        }
        if (deptIds.contains(userInfoDO.getDeptId())) {
            return addPunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.N, effectTime);
        }
        return addPunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEFAULT.getCode(), BusinessConstant.Y, effectTime);
    }

    @Override
    public boolean addWarehouseAttendancePunchConfigRange(HrmsUserInfoDO userInfoDO) {
        List<HrmsAttendancePunchConfigDO> configList = punchConfigDao.selectConfigByCompanyAndType(userInfoDO.getLocationCountry(), null);
        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }
        List<HrmsAttendancePunchConfigDO> defaultAttendancePunchConfigDOList = configList.stream().filter(o -> o.getIsDefault() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultAttendancePunchConfigDOList)) {
            return false;
        }
        HrmsAttendancePunchConfigDO defaultConfig = defaultAttendancePunchConfigDOList.get(0);

        List<HrmsAttendancePunchConfigDO> customList = configList.stream().filter(o -> o.getIsDefault() == 0).collect(Collectors.toList());
        for (HrmsAttendancePunchConfigDO item : customList) {
            if (StringUtils.isBlank(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(item.getDeptIds().split(","), Long.class));
            if (deptIds.contains(userInfoDO.getDeptId())) {
                return addWarehousePunchConfigRangeDO(item, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.Y);
            }
        }

        //属于默认打卡规则，看是不是免打卡
        List<Long> deptIds = new ArrayList<>();
        if (StringUtils.isNotBlank(defaultConfig.getDeptIds())) {
            deptIds = Arrays.asList((Long[]) ConvertUtils.convert(defaultConfig.getDeptIds().split(","), Long.class));
        }
        if (deptIds.contains(userInfoDO.getDeptId())) {
            return addWarehousePunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEPT.getCode(), BusinessConstant.N);
        }
        return addWarehousePunchConfigRangeDO(defaultConfig, userInfoDO, RangeTypeEnum.DEFAULT.getCode(), BusinessConstant.Y);
    }


    private boolean addPunchConfigRangeDO(HrmsAttendancePunchConfigDO punchConfigDO, HrmsUserInfoDO userInfoDO, String rangeType, Integer isNeedPunch, Date effectTime) {
        if (punchConfigDO == null) {
            return false;
        }
        // 直接新增一条用户的数据
        HrmsAttendancePunchConfigRangeDO rangeDO = new HrmsAttendancePunchConfigRangeDO();
        rangeDO.setPunchConfigId(punchConfigDO.getId());
        rangeDO.setBizId(userInfoDO.getId());
        rangeDO.setPunchConfigNo(punchConfigDO.getPunchConfigNo());
        rangeDO.setRangeType(rangeType);
        rangeDO.setEffectTime(DateUtil.beginOfDay(effectTime));
        rangeDO.setIsNeedPunch(isNeedPunch);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        fillDOInsert(rangeDO);
        return punchConfigRangeDao.save(rangeDO);
    }

    private boolean addWarehousePunchConfigRangeDO(HrmsAttendancePunchConfigDO punchConfigDO, HrmsUserInfoDO userInfoDO, String rangeType, Integer isNeedPunch) {
        if (punchConfigDO == null) {
            return false;
        }
        // 直接新增一条用户的数据
        HrmsAttendancePunchConfigRangeDO rangeDO = new HrmsAttendancePunchConfigRangeDO();
        rangeDO.setPunchConfigId(punchConfigDO.getId());
        rangeDO.setBizId(userInfoDO.getId());
        rangeDO.setPunchConfigNo(punchConfigDO.getPunchConfigNo());
        rangeDO.setRangeType(rangeType);

        // 获取当前时间减一天的时间 昨天
        Date date = new Date();
        DateTime yesterday = DateUtil.offsetDay(date, -1);
        DateTime effectTime = DateUtil.beginOfDay(yesterday);
        log.info("date : {} yesterday : {} effectTime : {}", date, yesterday, effectTime);

        rangeDO.setEffectTime(effectTime);
        rangeDO.setIsNeedPunch(isNeedPunch);
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        fillDOInsert(rangeDO);
        return punchConfigRangeDao.save(rangeDO);
    }
}
