package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-8-28
 * @version: 1.0
 */
@Data
public class SalaryFormInfoVO {

    /**
     * 申请单据ID
     */
    private Long applicationFormId;

    /**
     * 申请单据编号
     */
    private String applicationFormCode;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 发薪类型
     */
    private String salaryType;

    /**
     * 费用承担组织
     */
    private Long costOrgId;

    /**
     * 费用承担组织名称
     */
    private String costOrgName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 发薪总金额
     */
    private String totalSalaryAmount;

    /**
     * 申请人ID
     */
    private Long applyUserId;

    /**
     * 申请人编码
     */
    private String applyUserCode;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 申请人部门
     */
    private Long applyDeptId;

    /**
     * 申请人部门
     */
    private String applyDeptName;

    /**
     * 申请人岗位
     */
    private Long applyPostId;

    /**
     * 申请人岗位
     */
    private String applyPostName;

    /**
     * 申请人签约主体ID
     */
    private Long applyUserCostOrgId;

    /**
     * 申请人签约主体名称
     */
    private String applySettlementCenterName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 发薪人员个数的人数
     */
    private Integer salaryUserCount;

    /**
     * 算薪国家（申请国）
     */
    private String applyCountry;

    /**
     * 审批单ID
     */
    private Long approvalId;

    /**
     * 结算时间-年月(202308)
     */
    private Long settlementDate;

    /**
     * 支付类型，字典ErsPaymentMethod
     */
    private String payType;

    /**
     * 支付类型
     */
    private String payTypeDesc;

    /**
     * 期望支付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overdueDate;

    /**
     * 类型type
     */
    private String type;

    /**
     * 周期
     */
    private String payrollCycle;

    /**
     * 单据类型
     */
    private String documentType;

    /**
     * 供应商
     */
    private String vendorShortName;

}
