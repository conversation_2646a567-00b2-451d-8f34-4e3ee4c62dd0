package com.imile.hrms.service.primary;

import com.imile.hrms.api.primary.model.param.user.UserConditionParam;
import com.imile.hrms.api.primary.model.param.user.UserCreateParam;
import com.imile.hrms.api.primary.model.param.user.UserEditParam;
import com.imile.hrms.api.primary.model.param.user.UserStatusSwitchParam;
import com.imile.hrms.api.primary.model.result.user.UserBaseDTO;
import com.imile.hrms.api.primary.model.result.user.UserDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface UserService {

    /**
     * 创建人员
     *
     * @param param UserCreateParam
     * @return String
     */
    String createUser(UserCreateParam param);

    /**
     * 编辑人员
     *
     * @param param UserEditParam
     * @return Boolean
     */
    Boolean editUser(UserEditParam param);

    /**
     * 切换人员状态
     *
     * @param param UserStatusSwitchParam
     * @return Boolean
     */
    Boolean switchUserStatus(UserStatusSwitchParam param);

    /**
     * 根据人员编码获取人员
     *
     * @param userCode 人员编码
     * @return UserDTO
     */
    UserDTO getUserByCode(String userCode);

    /**
     * 根据动态条件获取
     *
     * @param param UserConditionParam
     * @return List<UserDTO>
     */
    List<UserDTO> listUserByCondition(UserConditionParam param);

    /**
     * 获取人员编码与汇报链映射
     *
     * @param userCodeList 人员编码列表
     * @return Map
     */
    Map<String, List<UserBaseDTO>> getUserLeaderChainMap(List<String> userCodeList);
}
