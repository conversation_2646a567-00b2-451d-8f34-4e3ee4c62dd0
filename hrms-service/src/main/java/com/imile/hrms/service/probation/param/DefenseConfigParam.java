package com.imile.hrms.service.probation.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 答辩配置新增
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
public class DefenseConfigParam {

    /**
     * 试用期ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 0 未答辩 1 已答辩
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer isDefense;

    /**
     * 答辩日期
     */
    private Date defenseDate;

    /**
     * 评委人员
     */
    private List<Long> userIds;
}
