package com.imile.hrms.service.excel;

import com.google.common.collect.Lists;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.base.OperationCodeEnum;
import com.imile.hrms.common.enums.user.IncentiveTypeEnum;
import com.imile.hrms.common.util.DateFormatUtils;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.UserIncentiveDO;
import com.imile.hrms.manage.user.UserIncentiveManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.refactor.user.param.UserImportIncentiveParam;
import com.imile.hrms.service.user.UserService;
import com.imile.hrms.service.user.context.UserImportGlobalContext;
import com.imile.hrms.service.user.context.UserImportTempContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.IMPORT_FIELD_DATA_DUPLICATE;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.IMPORT_FIELD_DATA_NOT_EXIST;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.IMPORT_FIELD_LENGTH_LIMIT;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.IMPORT_FIELD_PATTERN_ERROR;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.IMPORT_FIELD_REQUIRED;
import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.USER_WORK_STATUS_UNAVAILABLE;
import static com.imile.hrms.service.excel.field.UserIncentiveImportFieldEnum.INCENTIVE_CONTENT;
import static com.imile.hrms.service.excel.field.UserIncentiveImportFieldEnum.INCENTIVE_TIME;
import static com.imile.hrms.service.excel.field.UserIncentiveImportFieldEnum.INCENTIVE_TOPIC;
import static com.imile.hrms.service.excel.field.UserIncentiveImportFieldEnum.INCENTIVE_TYPE;
import static com.imile.hrms.service.excel.field.UserIncentiveImportFieldEnum.USER_CODE;

/**
 * <AUTHOR>
 * @date 2024/9/12
 */
@Slf4j
@Service
public class UserIncentiveImportProcessor extends UserBaseImportProcessor<UserImportIncentiveParam> {

    @Resource
    private UserManage userManage;
    @Resource
    private UserIncentiveManage userIncentiveManage;
    @Resource
    private UserService userService;

    private static final List<String> INCENTIVE_TYPE_DESC
            = Lists.newArrayList("奖励/Reward", "奖励", "Reward", "惩罚/Punishment", "惩罚", "Punishment");

    @Override
    protected UserImportGlobalContext buildGlobalContext(List<UserImportIncentiveParam> dataList) {
        UserImportGlobalContext context = new UserImportGlobalContext();
        List<String> userCodeList = dataList.stream()
                .map(UserImportIncentiveParam::getUserCode)
                .distinct()
                .collect(Collectors.toList());
        context.setUserCode2ListMap(userManage.setUserCode2ListMap(userCodeList));
        return context;
    }

    @Override
    protected UserImportTempContext buildTempContext(UserImportIncentiveParam data,
                                                     UserImportGlobalContext globalContext) {
        return new UserImportTempContext();
    }

    @Override
    protected String checkFailFastField(UserImportIncentiveParam data, UserImportGlobalContext globalContext) {
        return this.checkAccount(globalContext, data.getUserCode());
    }

    @Override
    protected List<String> checkFailSafeField(UserImportIncentiveParam data, UserImportGlobalContext globalContext,
                                              UserImportTempContext tempContext) {
        // 填充user (前面已做校验)
        tempContext.setUser(globalContext.getUserCode2ListMap().get(data.getUserCode()).get(0));
        // 必填性校验等不需要查询数据库的校验
        List<String> errorList = Lists.newArrayList();
        String userCode = data.getUserCode();
        if (StringUtils.isBlank(userCode)) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_REQUIRED, USER_CODE.getDesc()));
        }
        String incentiveType = data.getIncentiveType();
        if (StringUtils.isBlank(incentiveType)) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_REQUIRED, INCENTIVE_TYPE.getDesc()));
        } else if (!INCENTIVE_TYPE_DESC.contains(incentiveType)) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_PATTERN_ERROR, INCENTIVE_TYPE.getDesc(),
                    RequestInfoHolder.isChinese() ? "奖励或惩罚" : "Reward or Punishment"));
        }
        String incentiveTopic = data.getIncentiveTopic();
        if (StringUtils.isBlank(incentiveTopic)) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_REQUIRED, INCENTIVE_TOPIC.getDesc()));
        } else if (incentiveTopic.length() > 100) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_LENGTH_LIMIT, INCENTIVE_TOPIC.getDesc(), 100));
        }
        String incentiveTime = data.getIncentiveTime();
        if (StringUtils.isBlank(incentiveTime)) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_REQUIRED, INCENTIVE_TIME.getDesc()));
        } else if (!DATE_PATTERN.matcher(incentiveTime).matches()) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_PATTERN_ERROR, INCENTIVE_TIME.getDesc(), DateFormatUtils.DATE));
        }
        String incentiveContent = data.getIncentiveContent();
        if (StringUtils.isBlank(incentiveContent)) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_REQUIRED, INCENTIVE_CONTENT.getDesc()));
        } else if (incentiveContent.length() > 500) {
            errorList.add(super.getErrorTip(IMPORT_FIELD_LENGTH_LIMIT, INCENTIVE_CONTENT.getDesc(), 500));
        }
        return errorList;
    }

    @Override
    protected void processData(UserImportIncentiveParam data, UserImportTempContext tempContext) {
        // 组装奖罚信息相关对象
        UserIncentiveDO userIncentive = new UserIncentiveDO();
        userIncentive.setUserId(tempContext.getUser().getId());
        // 处理奖惩类型
        userIncentive.setIncentiveType(IncentiveTypeEnum.takeCodeByDesc(data.getIncentiveType()));
        userIncentive.setIncentiveTopic(data.getIncentiveTopic());
        userIncentive.setIncentiveContent(data.getIncentiveContent());
        userIncentive.setIncentiveTime(DateFormatUtils.parseDateTimeYYDDMM(data.getIncentiveTime()));
        userIncentiveManage.insert(userIncentive);

        HrmsUserInfoDO userInfo = new HrmsUserInfoDO();
        userInfo.setId(tempContext.getUser().getId());
        BaseDOUtil.fillDOUpdate(userInfo);
        userManage.batchUpdate(Lists.newArrayList(userInfo));
        // 添加操作记录
        userService.doUserAssociatedInfoImportLog(userInfo.getId(), OperationCodeEnum.HRMS_OWN_USER_INCENTIVE_IMPORT);
    }

    public String checkAccount(UserImportGlobalContext globalContext, String userCode) {
        List<HrmsUserInfoDO> userList = globalContext.getUserCode2ListMap().get(userCode);
        // 【账号】(xx)在系统中不存在
        if (CollectionUtils.isEmpty(userList)) {
            return super.getErrorTip(IMPORT_FIELD_DATA_NOT_EXIST, USER_CODE.getDesc(), userCode);
        }
        if (userList.size() > 1) {
            // 【账号】(xx)在系统中存在多条数据
            return super.getErrorTip(IMPORT_FIELD_DATA_DUPLICATE, USER_CODE.getDesc(), userCode);
        }
        HrmsUserInfoDO user = userList.get(0);
        if (!WorkStatusEnum.isOnJob(user.getWorkStatus())) {
            // 非在职状态不允许操作
            return super.getErrorTip(USER_WORK_STATUS_UNAVAILABLE, user.getUserCode());
        }
        if (!EmploymentTypeEnum.isOwn(user.getEmployeeType())) {
            return super.getErrorTip(HrmsErrorCodeEnums.USER_EMPLOYEE_TYPE_UNAVAILABLE,
                    EmploymentTypeEnum.descOfCode(user.getEmployeeType()));
        }
        return null;
    }
}
