package com.imile.hrms.service.punch.param.warehouse;

import com.imile.hrms.service.punch.vo.warehouse.ClassesVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/8/23
 */
@Data
public class BingShiftParam {

    /**
     * 网点id
     */
    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    @NotEmpty(message = "idList cannot be empty")
    private List<Long> idList;

    @NotNull(message = "classes cannot be empty")
    private ClassesVO classes;


}
