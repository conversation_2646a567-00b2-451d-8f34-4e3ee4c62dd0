package com.imile.hrms.service.organization.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/2
 */
@Data
public class HrmsEntDeptSaveParam {

    /**
     * 部门ID
     */
    private Long id;

    /**
     * 父级部门ID
     */
    private Long parentId;

    /**
     * 部门名称（英文）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    @Length(max = 150, message = ValidCodeConstant.LENGTH)
    private String deptNameEn;

    /**
     * 部门名称（中文）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    @Length(max = 50, message = ValidCodeConstant.LENGTH)
    private String deptNameCn;

    /**
     * 负责人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long leaderCode;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 组织类型
     */
    private Integer deptOrgType;

    /**
     * 国家
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String country;

    /**
     * 省份
     */
//    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String province;

    /**
     * 城市
     */
//    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String city;

    /**
     * 区域
     */
    private String region;

    /**
     * 详细地址
     */
    @Length(max = 500, message = ValidCodeConstant.LENGTH)
    private String address;

    /**
     * 业务领域列表
     */
    @NotEmpty(message = ValidCodeConstant.NOT_NULL)
    private List<Integer> bizAreaList;

    /**
     * 组织定位
     */
    @Length(max = 1000, message = ValidCodeConstant.LENGTH)
    private String deptPosition;

    /**
     * 组织职责
     */
    @Length(max = 1000, message = ValidCodeConstant.LENGTH)
    private String deptDuty;

    /**
     * 业务覆盖国列表
     */
    private List<String> bizCountryList;

    /**
     * 业务节点ID列表
     */
    @Size(max = 49, message = "最多只能选择49个业务节点")
    private List<Long> bizModelIdList;

    /**
     * 业务负责人配置列表
     */
    private List<HrmsOrgBusinessConfigSaveParam> orgBusinessConfigList;

    /**
     * 同步修改所有下级部门业务配置（1:是 0:否）
     */
    private Integer syncAllSubDept;

    /**
     * 期望生效时间
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Date expectedActiveTime;
}
