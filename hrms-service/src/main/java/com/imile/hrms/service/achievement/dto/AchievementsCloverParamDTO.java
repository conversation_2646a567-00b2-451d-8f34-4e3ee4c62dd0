package com.imile.hrms.service.achievement.dto;

import lombok.Data;

@Data
public class AchievementsCloverParamDTO {
    /**
     * 活动id
     */
    private Long eventId;

    /**
     * 组织id
     */
    private Long deptId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 绩效状态
     */
    private Integer appraisalStatus;

    /**
     * 规则类型 01相对 02绝对
     */
    private String type;
}
