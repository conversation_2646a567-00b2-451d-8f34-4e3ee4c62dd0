package com.imile.hrms.service.probation.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import com.imile.hrms.dao.user.dto.AttachmentDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 试用期周期总结参数
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
public class UserProbationCycleSummarySubmitParam {

    /**
     * id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 试用期id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userProbationId;

    /**
     * 总结详情
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = Submit.class)
    @Length(max = 3000, message = ValidCodeConstant.LENGTH)
    @Length(min = 1, message = ValidCodeConstant.LENGTH, groups = Submit.class)
    private String summaryDetail;

    /**
     * 相关附件（最多存储五个附件）
     */
    private List<AttachmentDTO> summaryFileList = new ArrayList<>();

    /**
     * 是否提交(false: 仅保存/编辑， true: 提交)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer isSubmit;

}
