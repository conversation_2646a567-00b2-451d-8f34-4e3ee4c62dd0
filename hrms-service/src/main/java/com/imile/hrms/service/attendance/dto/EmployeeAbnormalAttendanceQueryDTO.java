package com.imile.hrms.service.attendance.dto;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EmployeeAbnormalAttendanceQueryDTO extends ResourceQuery {

    /* *//**
     * 用户账号或姓名
     *//*
    private String userCodeOrName;
    *//**
     * 部门id
     *//*
    private Long deptId;
    *//**
     * 状态
     *//*
    private String status;
    *//**
     * 员工类型
     *//*
    private String employeeType;
    *//**
     * 开始时间
     *//*
    private Date startDate;
    *//**
     * 截止时间
     *//*
    private Date endDate;

    *//**
     * 司机:driver 仓内:warehouse 办公室员工:office
     *//*
    private String staffType;

    *//**
     * 仓内:warehouse 办公室员工:office
     *//*
    private List<String> staffTypes;

    *//**
     * 异常类型
     *//*
    private String abnormalType;

    private List<Long> deptIds;

    *//**
     * 邮箱
     *//*
    private String email;

    private List<Long> userIds;*/

    /**
     * 姓名、系统账号名、员工编码、工号
     */
    private String nameOrCode;

    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;

    /**
     * 用户id列表
     */
    private List<Long> userIds;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门id列表
     */
    private List<Long> deptIds;
    /*    *//**
     * 状态
     *//*
     private String status;*/
    /**
     * 异常状态
     */
    private List<String> statusList;
    /**
     * 员工类型
     */
    private String employeeType;
    /**
     * 员工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 异常类型列表
     */
    private List<String> abnormalTypeList;

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 截止时间
     */
    private Date endDate;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 司机:driver 仓内:warehouse 办公室员工:office  已处理界面不用传，全部类型都需要查出来
     */
    private List<String> staffTypes;

    private String country;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;
    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    /**
     * 考勤日历编码
     */
    private String attendanceConfigNo;

    /**
     * 配置打卡No
     */
    private String punchConfigNo;

    /**
     * 工作网点
     */
    private Long ocId;


    /**
     * 工作网点, 多选
     */
    private List<Long> ocIdList;

    /**
     * 工作供应商
     */
    private Long vendorId;

    /**
     * 工作供应商 多选
     */
    private List<Long> vendorIdList;

    /**
     * 查询来源 1:仓内异常查询
     */
    private Integer source;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 用工形式
     */
    private String employmentForm;
}
