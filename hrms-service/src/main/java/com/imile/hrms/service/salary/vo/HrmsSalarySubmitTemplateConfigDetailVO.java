package com.imile.hrms.service.salary.vo;

import lombok.Data;

import java.util.List;

/**
 * 薪资数据提报设置 详情
 *
 * <AUTHOR>
 * @since 2024/3/4
 */
@Data
public class HrmsSalarySubmitTemplateConfigDetailVO {

    /**
     * 数据模板名称
     */
    private String templateName;

    /**
     * 数据模板编码
     */
    private String templateNo;

    /**
     * 生效月份（202403）
     */
    private Long effectDate;

    /**
     * 描述
     */
    private String remark;

    /**
     * 记薪国
     */
    private String paymentCountry;

    /**
     * 关联记薪方案编码(逗号隔开)
     */
    private String schemeNoListString;

    /**
     * 指定提报人
     */
    private List<String> reporterList;

    /**
     * 指定提报人
     */
    private List<HrmsSalarySubmitTemplateConfigReporterDetailVO> reporterDetailList;

    /**
     * 数据审批方式
     */
    private String approvalMethod;

    /**
     * 企业微信通知截止小时数
     */
    private Integer remindHours;

    /**
     * 时间窗口配置id
     */
    private Long salaryTaskConfigId;

    /**
     * 期间类型
     */
    private String periodType;

    /**
     * 时间规则基准
     */
    private String timeReference;

    /**
     * 科目范围设置
     */
    private List<HrmsSalarySubmitTemplateItemConfigDetailVO> itemConfigList;

}
