package com.imile.hrms.service.salary.vo;

import lombok.Data;

/**
 * 薪酬系统首页日历提醒
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
@Data
public class HrmsSalaryToDoReminderVO {

    /**
     * 提示文案
     */
    private String reminder;

    /**
     * 提示类型 (PENDING_CONFIGURATION_PAYROLL_SCHEME / PENDING_PAYROLL_FIXING / PENDING_PAYROLL_CALCULATION / PENDING_PAYMENT_SALARY / PAYMENT_APPLICATION / SUBMISSION_APPLICATION / PENDING_SUBMISSION)
     */
    private String reminderType;

    /**
     *  提醒（加重显示）
     */
    private String reminderItem;

    /**
     * 模板编码（提报模板专用字段）
     */
    private String templateNo;

}
