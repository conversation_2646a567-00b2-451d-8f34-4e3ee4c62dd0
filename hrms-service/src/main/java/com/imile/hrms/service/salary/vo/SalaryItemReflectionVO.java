package com.imile.hrms.service.salary.vo;

import lombok.Data;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/10 13:56
 * @version: 1.0
 */
@Data
public class SalaryItemReflectionVO {


    /**
     * 科目ID
     */
    private Long itemId;

    /**
     * 科目编码
     */
    private String itemNo;

    /**
     * 计薪国
     */
    private String country;

    /**
     * 薪资项名称
     */
    private String itemName;

    /**
     * 薪资项属性
     */
    private String itemAttribute;

    /**
     * 薪资项属性名称
     */
    private String itemAttributeDesc;


    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 费用项类型
     */
    private String costType;

    /**
     * 费用项名称
     */
    private String costTypeDesc;

}
