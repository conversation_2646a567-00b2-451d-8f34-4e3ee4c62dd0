package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.service.salary.dto.SalaryAttendanceItemDetailDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/6 15:37
 * @version: 1.0
 */
@Data
public class SalaryAttendanceItemListVO {
    /**
     * 薪资项ID
     */
    private Long id;

    /**
     * 薪资项编码
     */
    private String itemNo;

    /**
     * 薪资项名称(中文)
     */
    private String itemNameCn;

    /**
     * 薪资项名称(英文)
     */
    private String itemNameEn;

    /**
     * 适用国
     */
    private String country;

    /**
     * 薪资项属性
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_ITEM_ATTRIBUTE, ref = "itemAttributeDesc")
    private String itemAttribute;
    private String itemAttributeDesc;

    /**
     * 薪资项格式
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_ITEM_FORMAT, ref = "itemFormatDesc")
    private String itemFormat;
    private String itemFormatDesc;

    /**
     * 薪资项取值方式
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_ITEM_VALUE_TYPE, ref = "itemValueTypeDesc")
    private String itemValueType;
    private String itemValueTypeDesc;

    /**
     * 薪资项取值方式-报表值
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_ITEM_REPORT_VALUE, ref = "itemReportValueDesc")
    private String itemReportValue;
    private String itemReportValueDesc;

    /**
     * 薪资项类型(薪资项目/考勤项目)
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.SALARY_ITEM_TYPE, ref = "itemTypeDesc")
    private String itemType;
    private String itemTypeDesc;

    /**
     * 是否薪资档案可用
     */
    private Integer isSalaryArchivesUse;


    /**
     * 是否强制使用
     */
    private Integer isForceUse;

    /**
     * 数据来源(系统生成/用户自定义添加)
     */
    private String dataSource;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 具体国家映射
     */
    List<SalaryAttendanceItemDetailDTO> attendanceItemDetailDTOList;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String createUserCode;

    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
}
