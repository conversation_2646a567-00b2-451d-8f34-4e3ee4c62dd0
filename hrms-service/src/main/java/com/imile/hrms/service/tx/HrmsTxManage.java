package com.imile.hrms.service.tx;

import com.imile.hrms.common.enums.OperationModuleEnum;

/**
 * 事务管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/16
 */
public interface HrmsTxManage {
    /**
     * 事物处理
     *
     * @param bizId
     */
    void handle(String bizId);

    /**
     * 事物提交重试
     *
     * @param bizId 业务唯一标识
     */
    void retry(String bizId);

    /**
     * 将事务强制完结
     *
     * @param bizId
     */
    void completed(String bizId);

    /**
     * 所属业务模块
     *
     * @return
     */
    OperationModuleEnum getOperationModule();


}
