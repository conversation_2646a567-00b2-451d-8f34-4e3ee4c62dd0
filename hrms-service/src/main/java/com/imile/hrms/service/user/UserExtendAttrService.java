package com.imile.hrms.service.user;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.user.model.UserExtendAttrDO;
import com.imile.hrms.service.user.param.UserExtendAttrSaveParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/17
 */
public interface UserExtendAttrService {

    /**
     * 获取人员扩展属性差异
     *
     * @param userId    人员ID
     * @param paramList 人员扩展属性列表
     * @return DataDifferHolder<UserExtendAttrDO>
     */
    DataDifferHolder<UserExtendAttrDO> differ(Long userId, List<UserExtendAttrSaveParam> paramList);
}
