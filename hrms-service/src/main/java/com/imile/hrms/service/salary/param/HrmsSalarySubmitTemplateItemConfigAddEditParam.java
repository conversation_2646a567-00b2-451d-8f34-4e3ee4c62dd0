package com.imile.hrms.service.salary.param;

import lombok.Data;

/**
 * 薪资数据提报设置
 *
 * <AUTHOR>
 * @since 2024/3/4
 */
@Data
public class HrmsSalarySubmitTemplateItemConfigAddEditParam {

    /**
     * id（编辑的时候传）
     */
    private Long id;

    /**
     * 科目id
     */
    private Long itemConfigId;

    /**
     * 提报时是否允许编辑（0否 / 1是）
     */
    private Integer isAllowEdit;

    /**
     * 是否必填（0否 / 1是）
     */
    private Integer isRequired;

    /**
     * 最大输入值
     */
    private Integer maxValue;

    /**
     * 最小输入值
     */
    private Integer minValue;

    /**
     * 最大可输入字符
     */
    private Integer maxCharacter;

    /**
     * 排序
     */
    private Integer sort;

}
