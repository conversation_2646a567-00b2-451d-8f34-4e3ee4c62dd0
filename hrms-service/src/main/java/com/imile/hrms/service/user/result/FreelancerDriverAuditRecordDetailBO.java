package com.imile.hrms.service.user.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class FreelancerDriverAuditRecordDetailBO {

    /**
     * 审核记录ID
     */
    private Long id;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 审核类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FREELANCER_DRIVER_AUDIT_TYPE, ref = "auditTypeDesc")
    private Integer auditType;

    /**
     * 审批描述
     */
    private String auditTypeDesc;

    /**
     * 审核状态（0:待审核 1:已通过 2:已驳回）
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 驳回原因
     */
    private String rejectionReason;

    /**
     * 众包司机详情
     */
    private FreelancerDriverDetailBO driverDetail;
}
