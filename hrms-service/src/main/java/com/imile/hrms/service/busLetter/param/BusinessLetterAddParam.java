package com.imile.hrms.service.busLetter.param;

import com.imile.hrms.dao.user.dto.AttachmentParamDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 人事变动表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-28
 */
@Data
public class BusinessLetterAddParam{


    private Long id;

    /**
     * 审批id
     */
    private Long approvalId;

    /**
     * 单据编号
     */
    private String approvalCode;

    /**
     * 单据状态 0:草稿 1:审批中 2:审批通过 -1:已终止 -2:已驳回 -3:已撤回
     */
    private String approvalStatus;

    /**
     * 事件code,隔开
     */
    private String lettersCodes;

    /**
     * 员工code
     */
    private String userCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收件人名称
     */
    private String nameAddress;

    /**
     * 收件人地址
     */
    private String locationAddress;

    /**
     * 多个文件,隔开
     */
    private String fileUrls;

    /**
     * NOC开始日期
     */
    private Date nocStartDate;

    /**
     * NOC结束日期
     */
    private Date nocEndDate;

    /**
     * 附件
     */
    private List<AttachmentParamDTO> attachments;
}
