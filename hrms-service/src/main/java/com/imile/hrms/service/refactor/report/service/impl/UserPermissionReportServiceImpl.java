package com.imile.hrms.service.refactor.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.util.PageConvertUtil;
import com.imile.hrms.dao.user.condition.UserReportConditionBuilder;
import com.imile.hrms.dao.user.dao.UserDao;
import com.imile.hrms.dao.user.po.UserPermissionReportListPO;
import com.imile.hrms.helper.RelationObjectValueHelper;
import com.imile.hrms.integration.permission.RpcUserReportPermissionService;
import com.imile.hrms.service.organization.EntDeptNewService;
import com.imile.hrms.service.refactor.report.param.UserPermissionReportPageParam;
import com.imile.hrms.service.refactor.report.result.UserPermissionReportListBO;
import com.imile.hrms.service.refactor.report.service.UserPermissionReportService;
import com.imile.hrms.service.user.UserResourceService;
import com.imile.hrms.service.user.vo.PermissionDeptVO;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.HRMSRoleDTO;
import com.imile.permission.api.dto.HRMSRoleMenuDTO;
import com.imile.permission.api.dto.HRMSUserRoleDTO;
import com.imile.permission.api.dto.MenuTreeDTO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/1/6
 */
@Service
@Slf4j
public class UserPermissionReportServiceImpl implements UserPermissionReportService {

    @Resource
    private UserDao userDao;
    @Resource
    private RpcUserReportPermissionService permissionService;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private EntDeptNewService entDeptNewService;
    @Resource
    private RelationObjectValueHelper relationObjectValueHelper;

    /**
     * 列表
     */
    @Override
    public PaginationResult<UserPermissionReportListBO> list(UserPermissionReportPageParam param) {
        UserReportConditionBuilder query = this.handleDeptPermission(param);
        if (Objects.isNull(query)) {
            return PageConvertUtil.getPageResult(Collections.emptyList(), param, 0, 0);
        }
        PageInfo<UserPermissionReportListPO> pageInfo = PageHelper.startPage(param.getCurrentPage(), param.getShowCount())
                .doSelectPageInfo(() -> userDao.selectPermissionReportByCondition(query));
        List<UserPermissionReportListPO> pageInfoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(pageInfoList)) {
            return PageConvertUtil.getPageRes(Collections.emptyList(), param, pageInfo);
        }
        return PageConvertUtil.getPageRes(this.buildListRes(pageInfoList, param.getIsExport()), param, pageInfo);
    }

    /**
     * 查询角色
     */
    @Override
    public HRMSUserRoleDTO getUserRole(String userCode) {
        return permissionService.getUserRole(userCode);
    }

    /**
     * 查全量菜单
     */
    @Override
    public List<MenuTreeDTO> getMenuTree() {
        return permissionService.getMenuTree();
    }

    /**
     * 通过角色查有权限菜单
     */
    @Override
    public List<HRMSRoleMenuDTO> getPermissionMenuByRoleId(Long roleId) {
        return permissionService.getPermissionMenuByRoleId(roleId);
    }

    /**
     * 查询有权限主数据(部门)
     */
    @Override
    public DataPermissionApiDTO getDeptMasterPermission(String userCode) {
        return permissionService.getDeptMasterPermission(userCode);
    }

    private UserReportConditionBuilder handleDeptPermission(UserPermissionReportPageParam param) {
        UserReportConditionBuilder builder = BeanUtils.convert(param, UserReportConditionBuilder.class);
        if (CollectionUtils.isEmpty(param.getDeptIdList())) {
            PermissionDeptVO permissionDept = userResourceService.getPermissionDept();
            // 管理员
            if (Objects.nonNull(permissionDept) && permissionDept.getIsSysAdmin()) {
                log.info("user report dept permission is sysadmin, userCode:{}", RequestInfoHolder.getUserCode());
                return builder;
            }
            // 非管理员 && 部门权限为空
            if (CollectionUtils.isEmpty(permissionDept.getDeptIdList())) {
                log.info("user report dept permission is null, userId: {}", RequestInfoHolder.getUserId());
                return null;
            }
            log.info("user report have permission dept deptIds: {}, userCode:{},",
                    JSON.toJSONString(permissionDept.getDeptIdList()), RequestInfoHolder.getUserCode());
            builder.setOrganizationIds(permissionDept.getDeptIdList());
            return builder;
        }
        // 页面传了部门参数
        builder.setOrganizationIds(param.getDeptIdList());
        return builder;
    }

    private List<UserPermissionReportListBO> buildListRes(List<UserPermissionReportListPO> pageInfoList, Boolean isExport) {
        List<UserPermissionReportListBO> resList = BeanUtils.convert(UserPermissionReportListBO.class, pageInfoList);
        List<String> userCodeList = resList.stream()
                .map(UserPermissionReportListBO::getUserCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, HRMSUserRoleDTO> userRoleMap = permissionService.getUserRoleList(userCodeList);
        List<Long> deptIdList = resList.stream()
                .map(UserPermissionReportListBO::getDeptId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, Map<Integer, String>> deptChainNameMap = entDeptNewService.getDeptChainNameMap(deptIdList);
        for (UserPermissionReportListBO it : resList) {
            Map<Integer, String> level2NameMap = deptChainNameMap.getOrDefault(it.getDeptId(), Maps.newHashMap());
            // 处理一级二级部门
            it.setFirstLevelDeptName(level2NameMap.getOrDefault(1,""));
            it.setSecondLevelDeptName(level2NameMap.getOrDefault(2,""));
            HRMSUserRoleDTO role = userRoleMap.get(it.getUserCode());
            if (Objects.isNull(role)) {
                continue;
            }
            List<UserPermissionReportListBO.RoleInfoBO> roleInfoList = new ArrayList<>();
            if (CollectionUtils.isEmpty(role.getActiveRoleList())
                    && CollectionUtils.isEmpty(role.getInactiveRoleList())) {
                continue;
            }
            if (isExport) {
                if (CollectionUtils.isNotEmpty(role.getActiveRoleList())) {
                    it.setEffectRoleNameStr(role.getActiveRoleList().stream()
                            .map(HRMSRoleDTO::getRoleName)
                            .collect(Collectors.joining("、")));
                    it.setEffectRoleNameEnStr(role.getActiveRoleList().stream()
                            .map(HRMSRoleDTO::getRoleNameEn)
                            .collect(Collectors.joining("、")));
                }
                if (CollectionUtils.isNotEmpty(role.getActiveRoleList())) {
                    it.setExpireRoleNameStr(role.getInactiveRoleList().stream()
                            .map(HRMSRoleDTO::getRoleName)
                            .collect(Collectors.joining("、")));
                    it.setExpireRoleNameEnStr(role.getInactiveRoleList().stream()
                            .map(HRMSRoleDTO::getRoleName)
                            .collect(Collectors.joining("、")));
                }
                continue;
            }
            if (CollectionUtils.isNotEmpty(role.getActiveRoleList())) {
                for (HRMSRoleDTO hrmsRoleDTO : role.getActiveRoleList()) {
                    roleInfoList.add(UserPermissionReportListBO.RoleInfoBO.builder()
                            .roleId(hrmsRoleDTO.getRoleId())
                            .roleName(hrmsRoleDTO.getRoleName())
                            .roleNameEn(hrmsRoleDTO.getRoleNameEn())
                            .status("effective")
                            .build());
                }
            }
            if (CollectionUtils.isNotEmpty(role.getInactiveRoleList())) {
                for (HRMSRoleDTO hrmsRoleDTO : role.getInactiveRoleList()) {
                    roleInfoList.add(UserPermissionReportListBO.RoleInfoBO.builder()
                            .roleId(hrmsRoleDTO.getRoleId())
                            .roleName(hrmsRoleDTO.getRoleName())
                            .roleNameEn(hrmsRoleDTO.getRoleNameEn())
                            .status("expire")
                            .build());
                }
            }
            it.setRoleList(roleInfoList);
        }
        return relationObjectValueHelper.batchFillObjectAndDictValue(resList);
    }
}
