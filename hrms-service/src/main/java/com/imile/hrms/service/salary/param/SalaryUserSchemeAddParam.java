package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/28 16:23
 * @version: 1.0
 */
@Data
public class SalaryUserSchemeAddParam {

    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 配置的计薪方案明细
     */
    @Valid
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<SalaryUserSchemeAddDetailParam> addDetailParamList;
}
