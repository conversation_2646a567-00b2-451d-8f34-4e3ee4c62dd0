package com.imile.hrms.service.newAttendance.calendar.adapter;

import com.imile.common.page.PaginationResult;
import com.imile.hrms.common.adapter.AbstractAdapter;
import com.imile.hrms.common.adapter.ServiceAdapter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarLegalLeaveConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarLegalLeaveConfigListQuery;
import com.imile.hrms.service.leave.HrmsCompanyLegalLeaveConfigService;
import com.imile.hrms.service.leave.param.LegalLeaveConfigQueryDetailParam;
import com.imile.hrms.service.leave.param.LegalLeaveConfigQueryParam;
import com.imile.hrms.service.leave.param.LegalLeaveConfigSaveParam;
import com.imile.hrms.service.leave.vo.LegalLeaveConfigDetailVO;
import com.imile.hrms.service.leave.vo.LegalLeaveConfigVO;
import com.imile.hrms.service.newAttendance.calendar.application.CalendarConfigApplicationService;
import com.imile.hrms.service.newAttendance.calendar.command.CalendarLegalLeaveConfigAddCommand;
import com.imile.hrms.service.newAttendance.calendar.mapstruct.CalendarLegalLeaveConfigServiceMapstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarLegalLeaveConfigServiceAdapter
 * {@code @since:} 2025-02-24 14:48
 * {@code @description:}
 */
@Component
@RequiredArgsConstructor
public class CalendarLegalLeaveConfigServiceAdapter extends AbstractAdapter implements ServiceAdapter {
    private final EnableNewAttendanceConfig config;

    private final HrmsCompanyLegalLeaveConfigService hrmsCompanyLegalLeaveConfigService;
    private final CalendarConfigApplicationService applicationService;


    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnableCalendar();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getCalendarDoubleWriteEnabled();
    }


    public void save(LegalLeaveConfigSaveParam legalLeaveConfigSaveParam) {
        CalendarLegalLeaveConfigAddCommand addCommand = CalendarLegalLeaveConfigServiceMapstruct.INSTANCE.mapToAddCommand(legalLeaveConfigSaveParam);
        applicationService.addCalendarLegalLeaveConfig(addCommand);
    }

    public LegalLeaveConfigDetailVO queryLegalLeaveDetail(@Valid LegalLeaveConfigQueryDetailParam param) {
        return commonQuery(
                () -> {
                    CalendarLegalLeaveConfigDetailQuery detailQuery = CalendarLegalLeaveConfigServiceMapstruct.INSTANCE.mapToDetailQuery(param);
                    return applicationService.queryLegalLeaveDetail(detailQuery);
                },
                () -> hrmsCompanyLegalLeaveConfigService.queryLegalLeaveDetail(param)
        );
    }

    public PaginationResult<LegalLeaveConfigVO> queryLegalLeave(@Valid LegalLeaveConfigQueryParam param) {
        return commonQuery(
                () -> {
                    CalendarLegalLeaveConfigListQuery queryList = CalendarLegalLeaveConfigServiceMapstruct.INSTANCE.mapToListQuery(param);
                    return applicationService.queryLegalLeave(queryList);
                },
                () -> hrmsCompanyLegalLeaveConfigService.queryLegalLeave(param)
        );
    }
}
