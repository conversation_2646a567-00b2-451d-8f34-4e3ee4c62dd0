package com.imile.hrms.service.salary.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 算薪驳回 参数
 *
 * <AUTHOR>
 * @date 2024-3-13
 */
@Data
public class SalaryPayRejectionNewParam {

    /**
     * 结薪员工id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long settlementUserInfoId;

    /**
     * 薪资结算人员数据项明细表id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long settlementUserDetailId;

    /**
     * 模板id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long templateId;

    /**
     * 科目id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long itemConfigId;

    /**
     * 点击计算时的时间
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;

}
