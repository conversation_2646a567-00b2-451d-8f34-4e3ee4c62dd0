package com.imile.hrms.service.punch.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-12
 * @version: 1.0
 */
@Data
public class CycleShiftParam {

    /**
     * 打卡规则ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long punchConfigId;

    /**
     * 批量排班用户
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<Long> userIdList;

    /**
     * 循环周期
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer cyclePeriod;

    /**
     * 是否覆盖公共节假日  1覆盖
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer isOverridePh;

    /**
     * 批量排班每天班次信息
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<CycleShiftDayParam> cycleShiftDayParamList;

    /**
     * 来自页面排班
     */
    private Boolean fromPage = Boolean.FALSE;
}
