package com.imile.hrms.service.newAttendance.punchConfig.adapter;

import com.imile.hrms.common.adapter.AbstractAdapter;
import com.imile.hrms.common.adapter.ServiceAdapter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchClassConfigSelectDTO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassItemConfigDO;
import com.imile.hrms.dao.punch.query.AttendancePunchClassNameQuery;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.hrms.service.approval.dto.DayNormalPunchTimeDTO;
import com.imile.hrms.service.approval.dto.DayPunchTimeDTO;
import com.imile.hrms.service.newAttendance.punchConfig.PunchClassConfigService;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchClassConfigSelectDTO;
import com.imile.hrms.service.newAttendance.punchConfig.mapstruct.PunchConfigMapstruct;
import com.imile.hrms.service.newAttendance.punchConfig.query.PunchClassNameQuery;
import com.imile.hrms.service.punch.HrmsAttendancePunchClassConfigsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/25 
 * @Description
 */
@Slf4j
@Component
public class PunchClassConfigServiceAdapter extends AbstractAdapter implements ServiceAdapter {

    @Autowired
    private EnableNewAttendanceConfig config;
    @Autowired
    private HrmsAttendancePunchClassConfigsService attendancePunchClassConfigsService;
    @Autowired
    private PunchClassConfigService punchClassConfigService;


    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }

    /**
     * 获取用户指定天的打卡规则的最早开始最晚截止时间(改天必须是有班次，不能是PH/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     * OK可用
     */
    public DayPunchTimeDTO getUserPunchDayTime(Long dayId, List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList) {
        return commonQuery(
                () -> punchClassConfigService.getUserPunchDayTime(
                        dayId,
                        PunchClassItemConfigMapstruct.INSTANCE.mapToNew(itemConfigDOList)
                ),
                () -> attendancePunchClassConfigsService.getUserPunchDayTime(dayId, itemConfigDOList)
        );
    }

    /**
     * 获取用户指定天的打卡规则的最早开始最晚截止时间(改天必须是有班次，不能是PH/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     * OK可用
     */
    public DayPunchTimeDTO getUserPunchClassItemDayTime(Long dayId, Long classItemId,
                                                        List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList) {
        return commonQuery(
                () -> punchClassConfigService.getUserPunchClassItemDayTime(
                        dayId,
                        classItemId,
                        PunchClassItemConfigMapstruct.INSTANCE.mapToNew(itemConfigDOList)
                ),
                () -> attendancePunchClassConfigsService.getUserPunchClassItemDayTime(dayId, classItemId, itemConfigDOList)
        );
    }

    /**
     * 获取用户指定天的打卡规则的正常上下班时间(改天必须是有班次，不能是PH/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     * OK可用
     */
    public DayNormalPunchTimeDTO getUserPunchClassNormalItemDayTime(Long dayId, Long classItemId,
                                                                    List<HrmsAttendancePunchClassItemConfigDO> itemConfigDOList) {
        return commonQuery(
                () -> punchClassConfigService.getUserPunchClassNormalItemDayTime(
                        dayId,
                        classItemId,
                        PunchClassItemConfigMapstruct.INSTANCE.mapToNew(itemConfigDOList)
                ),
                () -> attendancePunchClassConfigsService.getUserPunchClassNormalItemDayTime(dayId, classItemId, itemConfigDOList)
        );
    }

    /**
     * 获取用户自由打卡规则指定天的最早开始最晚截止时间
     */
    public DayPunchTimeDTO getUserFreeWorkPunchClassItemDayTime(String dayId, HrmsAttendancePunchClassItemConfigDO itemConfigDO) {
        return commonQuery(
                () -> punchClassConfigService.getUserFreeWorkPunchClassItemDayTime(
                        dayId,
                        PunchClassItemConfigMapstruct.INSTANCE.mapToNew(itemConfigDO)
                ),
                () -> attendancePunchClassConfigsService.getUserFreeWorkPunchClassItemDayTime(dayId, itemConfigDO)
        );
    }


    /**
     * hrmsAttendancePunchClassConfigsService.selectList
     */
    public List<HrmsAttendancePunchClassConfigSelectDTO> selectList(AttendancePunchClassNameQuery query) {
        return commonQuery(
                () -> {
                    PunchClassNameQuery punchClassNameQuery = PunchConfigMapstruct.INSTANCE.toNewPunchClassNameQuery(query);
                    List<PunchClassConfigSelectDTO> punchClassConfigSelectDTOS = punchClassConfigService.selectList(punchClassNameQuery);
                    return PunchConfigMapstruct.INSTANCE.toOldPunchClassConfigSelectDTO(punchClassConfigSelectDTOS);
                },
                () -> attendancePunchClassConfigsService.selectList(query)
        );
    }


}
