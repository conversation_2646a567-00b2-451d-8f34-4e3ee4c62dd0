package com.imile.hrms.service.refactor.user.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Data
public class UserCountryExportParam extends ResourceQuery {

    /**
     * 工号
     */
    private String workNo;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 岗位id
     */
    private String postId;

    /**
     * 入职起始时间
     */
    private String entryStartTime;

    /**
     * 入职结束时间
     */
    private String entryEndTime;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 用工类型
     */
    private String employeeTypeList;

    /**
     * 是否是司机
     */
    private String isDriver;

    /**
     * 常驻地
     */
    private String country;
}
