package com.imile.hrms.service.organization.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/1
 */
@Data
public class HrmsOrgTemplateDuplicateAddParam {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    @Length(max = 100, message = ValidCodeConstant.LENGTH)
    private String templateName;

    /**
     * 复制部门ID列表
     */
    @NotEmpty(message = "复制部门ID列表不能为空")
    private List<Long> duplicateDeptIdList;
}
