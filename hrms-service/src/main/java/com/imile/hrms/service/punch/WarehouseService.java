package com.imile.hrms.service.punch;

import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import com.imile.hrms.service.punch.param.warehouse.BingShiftParam;
import com.imile.hrms.service.punch.param.warehouse.CheckVendorConfirmStatusParam;
import com.imile.hrms.service.punch.param.warehouse.InOrOutParam;
import com.imile.hrms.service.punch.param.warehouse.InV2Param;
import com.imile.hrms.service.punch.param.warehouse.OutParam;
import com.imile.hrms.service.punch.param.warehouse.QuickOutParam;
import com.imile.hrms.service.punch.param.warehouse.UpdateWarehouseWorkVendorParam;
import com.imile.hrms.service.punch.param.warehouse.VendorConfirmClassesParam;
import com.imile.hrms.service.punch.vo.warehouse.CheckVendorConfirmStatusResultVO;
import com.imile.hrms.service.punch.vo.warehouse.OutVO;
import com.imile.hrms.service.punch.param.warehouse.*;


/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内管理
 * @date 2024/7/1 14:26:15
 */
public interface WarehouseService {
    /**
     * 入仓
     */
    void in(InOrOutParam param);

    /**
     * 入仓扩展
     * 老帐号停用
     * 证件号更新
     */
    void inV2(InV2Param param);

    /**
     * 出仓
     */
    OutVO out(OutParam param);

    /**
     * 快速出仓
     * 无需人脸识别检查
     */
    void quickOut(QuickOutParam param);

    /**
     * 更新仓内日报工作供应商
     */
    void updateWorkVendor(UpdateWarehouseWorkVendorParam param);

    /**
     * 更新仓内日报工作网点
     */
    void updateWorkOc(UpdateWarehouseWorkOcParam param);

    /**
     * 更新仓内日报工作班次
     */
    void updateWorkClasses(UpdateWarehouseWorkClassesParam param);

    /**
     * 绑定班次
     */
    Boolean bingShift(BingShiftParam param);

    /**
     * 补推财务考勤流水
     */
    void retryPushFin(WarehouseDetailParam param);

    /**
     * 供应商确认人员班次出勤结果
     */
    Long vendorClassedConfirm(VendorConfirmClassesParam param);

    /**
     * 检查供应商确认状态
     */
    CheckVendorConfirmStatusResultVO checkVendorConfirmStatus(CheckVendorConfirmStatusParam param);

    /**
     * 删除仓内日报
     */
    void deleteWarehouseDetail(WarehouseDetailParam param);

    /**
     * 删除人脸特征
     */
    void deleteFaceFeature(String userCodes);
}
