package com.imile.hrms.service.punch;


import com.imile.common.page.PaginationResult;
import com.imile.hrms.dao.punch.dto.*;
import com.imile.hrms.dao.punch.param.ConfigNoStatusSwitchParam;
import com.imile.hrms.dao.punch.param.HrmsAttendancePunchConfigAddParam;
import com.imile.hrms.dao.punch.param.HrmsAttendancePunchConfigUpdateParam;
import com.imile.hrms.dao.punch.query.*;
import com.imile.hrms.service.punch.dto.PunchTemplateJudgeDTO;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/1/25
 */
public interface HrmsAttendancePunchConfigService {


    /**
     * 判断新增打卡规则是否为默认打卡规则
     */
    PunchTemplateJudgeDTO punchTemplateJudge(String country);

    /**
     * 新增打卡规则
     *
     * @param configAddDTO
     * @return
     */
    List<HrmsAttendancePunchConfigRangeDTO> addPunchConfig(HrmsAttendancePunchConfigAddParam configAddDTO);

    /**
     * 打卡规则列表
     *
     * @param query
     * @return
     */
    PaginationResult<HrmsAttendancePunchConfigDTO> listPunchList(AttendancePunchConfigQuery query);

    /**
     * 更新打卡规则
     *
     * @param configAddDTO
     * @return
     */
    List<HrmsAttendancePunchConfigRangeDTO> updatePunchConfig(HrmsAttendancePunchConfigAddParam configAddDTO);

    /**
     * 更新打卡规则权限
     *
     * @param configAddDTO
     * @return
     */
    List<HrmsAttendancePunchConfigRangeDTO> updateAuthPunchConfig(HrmsAttendancePunchConfigUpdateParam configAddDTO);

    /**
     * 规则详情
     *
     * @param query
     * @return
     */
    HrmsAttendancePunchConfigDetailDTO detailPunchConfig(AttendancePunchConfigDetailQuery query);

    /**
     * 关闭开启打卡规则
     *
     * @param query
     * @return
     */
    List<HrmsAttendancePunchConfigRangeDTO> stateSwitch(ConfigNoStatusSwitchParam query);

    /**
     * 打卡规则下拉框
     *
     * @return
     */
    List<HrmsAttendancePunchConfigSelectDTO> selectList(String country);

    /**
     * 该打卡规则可以选择的排班类型
     */
    List<HrmsAttendancePunchDayTypeDTO> selectDayTypeList(Long punchConfigId, Long userId);


    /**
     * 打卡规则导出
     */
    PaginationResult<HrmsAttendancePunchConfigExportConfigDTO> export(AttendancePunchClassExportQuery query);

    /**
     * 打卡规则人员列表
     *
     * @param query
     * @return
     */
    PaginationResult<HrmsAttendancePunchUserInfoDTO> selectPunchUserList(AttendancePunchConfigUserQuery query);

//    /**
//     * 打卡规则人员范围导出
//     *
//     * @param query
//     * @return
//     */
//    List<HrmsAttendancePunchUserInfoDTO> exportPunchUserList(AttendancePunchConfigUserQuery query);

    //=================================以下为内部方法=================================================

    /**
     * 根据国家+部门id+打卡类型查找打卡规则以及班次和班次详情：done
     *
     * @param query 参数
     * @return List<WarehousePunchConfigDTO>
     */
    List<WarehousePunchConfigDTO> selectPunchConfigByWarehouseCondition(WarehousePunchConfigQuery query);

    /**
     * 获取考勤日：仓内出仓根据当前时间 + userId获取考勤日（因为需要知道当前时间打出仓卡，所属考勤日，才能计算考勤）。入仓的考勤日就是当前打卡时间的年月日（班次选错也没关系，因为班次只是表示几点上下班，dayId考勤日不会错）：done
     * 如果获取到的考勤日是未来时间，则仓内不计算考勤
     * @param query 参数
     * @return Long 返回值 ：返回null表示参数为null或用户不存在
     */
    Long getAttendanceDay(WarehouseAttendanceDayQuery query);

    /**
     * 获取指定班次，当前时间所属考勤日
     *
     * @param query 参数
     * @return Long
     */
    Long getAttendanceDayByOneClassId(WarehouseAttendanceDayByClassIdQuery query);

    /**
     * 获取该人员当天的日历：根据userId + 当前时间（dayId）获取当天日历情况（这里不需要获取考勤日了，直接取当前时间年月日即可，因为这个是在仓内入仓的时候需要的接口，考勤日就是当前时间）：done
     *
     * @param query 参数
     * @return WarehouseAttendanceConfigDetailDTO
     */
    WarehouseAttendanceConfigDetailDTO selectNowAttendanceConfigDetail(WarehouseAttendanceConfigQuery query);

    /**
     * 根据班次id获取班次详情信息：done
     *
     * @param punchClassId 班次id
     * @return List<WarehouseAttendancePunchClassItemConfigDTO>
     */
    List<WarehouseAttendancePunchClassItemConfigDTO> selectPunchClassItemDetail(Long punchClassId);

    /**
     * 获取班次的最早上班时间、最晚上班时间：done
     *
     * @param query 参数
     * @return WarehousePunchConfigInTimeDTO
     */
    WarehousePunchConfigInTimeDTO selectPunchInTime(WarehousePunchInTimeQuery query);

    /**
     * 根据国家+部门id列表+打卡类型查找班次信息：done
     *
     * @param query 参数
     * @return List<WarehousePunchClassConfigDTO>
     */
    List<WarehousePunchClassConfigDTO> selectPunchClassConfigByWarehouseCondition(WarehousePunchClassConfigQuery query);



}
