package com.imile.hrms.service.salary.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/27 16:03
 * @version: 1.0
 */
@Data
public class SalarySchemeCycleInfoVO {

    /**
     * 计薪周期类型
     */
    private String cycleType;

    /**
     * 计薪周期-开始时间
     */
    private String cycleStart;

    /**
     * 计薪周期-结束时间
     */
    private String cycleEnd;

    /**
     * 每月标准计薪天数类型
     */
    private String standardMonthSalaryDaysType;

    /**
     * 每月标准计薪天数具体值
     */
    private BigDecimal standardMonthSalaryDaysValue;

    /**
     * 每日标准小时数
     */
    private BigDecimal standardDayHours;

    /**
     * 发薪日期(依据计薪周期类型有不同类型的值)
     */
    private String payDateValue;
}
