package com.imile.hrms.service.achievement.vo;

import com.imile.hrms.service.achievement.dto.AchievementEvaluateGradeRelationDTO;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 绩效评价规则职级关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
public class AchievementEvaluateGradeRelationDetailVO {

    /**
     * 规则列表
     */
    private List<AchievementEvaluateGradeRelationDTO> list;

    /**
     * 01相对 02绝对 03全部
     */
    private String ruleType;

    /**
     * 配置强制比例人数少于x人，不做校验
     */
    private Integer ruleNum;
}
