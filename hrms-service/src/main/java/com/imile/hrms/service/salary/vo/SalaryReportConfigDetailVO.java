package com.imile.hrms.service.salary.vo;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/9 11:46
 * @version: 1.0
 */
@Data
public class SalaryReportConfigDetailVO {

    private Long id;

    /**
     * 计薪国
     */
    private String country;

    /**
     * 报表编码
     */
    private String reportNo;

    /**
     * 版本编码
     */
    private String versionNo;

    /**
     * 当前版本生效月份
     */
    private String effectMonth;

    /**
     * 当前版本失效月份
     */
    private String expireMonth;

    /**
     * 上个版本生效月份
     */
    private String lastEffectMonth;

    /**
     * 上个版本失效月份
     */
    private String lastExpireMonth;

    /**
     * 具体的分组配置信息
     */
    private List<SalaryReportConfigGroupVO> salaryReportConfigGroupVOList;

    /**
     * 实付金额科目
     */
    private Long paymentItemConfigId;

}
