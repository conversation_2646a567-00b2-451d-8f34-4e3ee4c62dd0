package com.imile.hrms.service.salary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * 计算员工详情
 *
 * <AUTHOR>
 * @since 2023/12/20
 */
@Data
public class SalaryCalculateUserInfoDetailVO {

    /**
     * id
     */
    private Long id;

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工姓名
     */
    private String userNameEn;

    /**
     * 员工邮箱
     */
    private String email;

    /**
     * 国籍
     */
    private String countryName;

    /**
     * 常驻国(核算组织)
     */
    private String originCountry;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 常驻地
     */
    private String residentLocation;

    /**
     * 结算主体名称
     */
    private String settlementCenterName;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 岗位
     */
    private String postName;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型
     */
    private String employeeTypeDesc;

    /**
     * 员工状态
     */
    private String workStatus;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dimissionDate;

    /**
     * 费用承担主体
     */
    private String costOrgName;

    /**
     * 费用承担国家
     */
    private String costCountry;

    /**
     * 费用承担部门
     */
    private String costDeptName;

    /**
     * 费用承担网点
     */
    private String costStation;

    /**
     * 费用承担项目
     */
    private String costProject;

}
