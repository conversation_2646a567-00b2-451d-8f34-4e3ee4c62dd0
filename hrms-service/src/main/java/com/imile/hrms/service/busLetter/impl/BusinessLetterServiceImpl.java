package com.imile.hrms.service.busLetter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.enums.ApprovalRoleEnum;
import com.imile.bpm.enums.ApprovalRoleValueEnum;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalCreateRoleUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalCreateUserApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.ApprovalUserInfoApiDTO;
import com.imile.bpm.mq.dto.FileTemplateApiDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hrms.api.salary.dto.UserInfoDetailApiDTO;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.TableEnum;
import com.imile.hrms.common.enums.approval.ApprovalNoPrefixEnum;
import com.imile.hrms.common.enums.approval.HrAttendanceApplicationFormTypeEnum;
import com.imile.hrms.common.enums.busletter.ApprovalStatusEnum;
import com.imile.hrms.common.enums.busletter.BusinessLetterEnum;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.common.util.IdWorkUtils;
import com.imile.hrms.common.util.PageConvertUtil;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.busletter.mapper.BusinessLetterMapper;
import com.imile.hrms.dao.busletter.model.BusinessLetterDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.user.dao.HrmsAttachmentDao;
import com.imile.hrms.dao.user.dto.AttachmentParamDTO;
import com.imile.hrms.dao.user.dto.UserDTO;
import com.imile.hrms.dao.user.model.HrmsAttachmentDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.query.AttachmentQuery;
import com.imile.hrms.integration.bpm.service.BpmCreateApprovalService;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.user.HrmsAttachmentManage;
import com.imile.hrms.service.approval.dto.ApprovalCreateRoleUserDTO;
import com.imile.hrms.service.approval.dto.ApprovalCreateUserDTO;
import com.imile.hrms.service.approval.dto.ApprovalDetailStepRecordDTO;
import com.imile.hrms.service.approval.dto.ApprovalPreviewErrorUserDTO;
import com.imile.hrms.service.approval.dto.ApprovalPreviewSuccessUserDTO;
import com.imile.hrms.service.approval.dto.CountryDTO;
import com.imile.hrms.service.bpm.dto.ApprovalUserInfoDTO;
import com.imile.hrms.service.busLetter.BusinessLetterService;
import com.imile.hrms.service.busLetter.param.BusinessLetterAddParam;
import com.imile.hrms.service.busLetter.param.BusinessLetterListParam;
import com.imile.hrms.service.busLetter.vo.BusinessLetterDetailVO;
import com.imile.hrms.service.busLetter.vo.BusinessLetterPageVO;
import com.imile.hrms.service.user.HrmsUserInfoService;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.imile.hrms.service.recruitment.RecruitmentHelper.customFieldBuild;

/**
 * <p>
 * 人事变动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-28
 */
@Service
@Slf4j
public class BusinessLetterServiceImpl extends ServiceImpl<BusinessLetterMapper, BusinessLetterDO> implements BusinessLetterService {

    @Autowired
    private HrmsAttachmentDao hrmsAttachmentDao;

    @Autowired
    private HrmsAttachmentManage hrmsAttachmentManage;

    @Autowired
    private IHrmsIdWorker hrmsIdWorker;

    @Autowired
    private IdWorkUtils idWorkUtils;

    @Autowired
    private BpmCreateApprovalService bpmCreateApprovalService;

    @Autowired
    private HrmsUserInfoService hrmsUserInfoService;

    @Autowired
    private HrmsDeptManage hrmsDeptManage;

    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;

    public static final String TIME_FORMAT = "yyyy-MM-dd";

    @Value("#{${country.main.poc.map:{UAE:'2102255',OMN:'2102255',KSA:'2102474',CHN:'2101947',MEX:'2104959',TUR:'2102354',BRA:'2105554',KWT:'2103221',QAT:'2103221',JOR:'2103221',BHR:'2103221',LBN:'2103221',HQ:'2104453',POL:'2104453',DEU:'2104453',FRA:'2104453',GBR:'2104453',CHL:'2104453',RSA:'2104453',NLD:'2104453',AUS:'2104453',ITA:'2104453'}}}")
    private Map<String, String> countryMainPocMap;

    @Autowired
    private CountryService countryService;

    @Override
    public PaginationResult<BusinessLetterPageVO> pageList(BusinessLetterListParam query) {
        String userCode = RequestInfoHolder.getUserCode();
        Page<BusinessLetterDO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);

        PageInfo<BusinessLetterPageVO> listPage = page.doSelectPageInfo(() -> this.getBaseMapper().selectList(new LambdaQueryWrapper<BusinessLetterDO>()
                        .eq(BusinessLetterDO::getIsDelete, IsDeleteEnum.NO.getCode())
                        .eq(BusinessLetterDO::getUserCode, userCode)
                        .eq(StringUtils.isNotBlank(query.getApprovalCode()), BusinessLetterDO::getApprovalCode, query.getApprovalCode())
                        .eq(StringUtils.isNotBlank(query.getApprovalStatus()), BusinessLetterDO::getApprovalStatus, query.getApprovalStatus())
                        .ge(Objects.nonNull(query.getBeginDate()), BusinessLetterDO::getCreateDate, query.getBeginDate())
                        .le(Objects.nonNull(query.getEndDate()), BusinessLetterDO::getCreateDate, query.getEndDate())
                        .orderByDesc(BusinessLetterDO::getLastUpdDate)
                )
        );

        return PageConvertUtil.getPageResult(listPage.getList(), query, (int) listPage.getTotal(), listPage.getPages());

    }

    @Override
    public BusinessLetterDetailVO detail(Long id) {
        AttachmentQuery attachmentQuery = AttachmentQuery.builder().tableName(TableEnum.HRMS_BUSINESS_LETTER.getCode()).foreignKey(String.valueOf(id)).build();
        List<HrmsAttachmentDO> attachmentDOList = hrmsAttachmentDao.getAttachment(attachmentQuery);

        BusinessLetterDO businessLetterDO = this.getBaseMapper().selectById(id);
        BusinessLetterDetailVO vo = BeanUtils.convert(businessLetterDO, BusinessLetterDetailVO.class);
        if (CollectionUtils.isNotEmpty(attachmentDOList)) {
            List<AttachmentParamDTO> attachments = com.imile.util.BeanUtils.convert(AttachmentParamDTO.class, attachmentDOList);
            vo.setAttachments(attachments);
        }
        return vo;
    }

    @Override
    public List<ApprovalDetailStepRecordDTO> preview(BusinessLetterAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();

        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoService.getByUserId(RequestInfoHolder.getUserId());
        HrmsEntDeptDO deptDO = hrmsDeptManage.selectById(hrmsUserInfoDO.getDeptId());
        HrmsEntPostDO postDO = hrmsEntPostDao.getBaseMapper().selectById(hrmsUserInfoDO.getPostId());
        BusinessLetterDO businessLetterDO = BeanUtils.convert(param, BusinessLetterDO.class);
        //发起审批
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        initInfoApiDTO.setApprovalType(HrAttendanceApplicationFormTypeEnum.BUSINESS_LETTER.getCode());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setApplyUserCode(hrmsUserInfoDO.getUserCode());
        initInfoApiDTO.setCountry(hrmsUserInfoDO.getOriginCountry());

        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());

        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();

        buildData(param, businessLetterDO, deptDO, hrmsUserInfoDO, postDO, fieldApiDTOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("buildData||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmCreateApprovalService.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        previewDTOBuild(recordApiDTOList, resultDTOList, RequestInfoHolder.getUserCode());
        return resultDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(BusinessLetterAddParam param) {
        String code = RequestInfoHolder.getUserCode();
        Long id = hrmsIdWorker.nextId();
        BusinessLetterDO businessLetterDO = BeanUtils.convert(param, BusinessLetterDO.class);
        businessLetterDO.setUserCode(code);

        if(ApprovalStatusEnum.APPROVING.getCode().equals(param.getApprovalStatus())){
            HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoService.getByUserId(RequestInfoHolder.getUserId());
            HrmsEntDeptDO deptDO = hrmsDeptManage.selectById(hrmsUserInfoDO.getDeptId());
            HrmsEntPostDO postDO = hrmsEntPostDao.getBaseMapper().selectById(hrmsUserInfoDO.getPostId());

            // 校验是否包含审批人
            List<ApprovalDetailStepRecordDTO> list = this.preview(param);
            // 排除第第一条数据
            int temp = 1;
            for (ApprovalDetailStepRecordDTO dto : list) {
                if (temp == 1) {
                    temp++;
                    continue;
                }

                if (CollectionUtils.isEmpty(dto.getApprovalUserInfoDTOS())) {
                    String descEn = "The role for "+dto.getStepName()+" in "+deptDO.getDeptNameEn()+" is empty. Please contact HRM for help.";
                    throw BusinessException.get(descEn);
                }
                temp++;
            }
            ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
            BusinessLetterDO businessLetterOld = null;
            if (Objects.nonNull(param.getId())) {
                initInfoApiDTO.setBizId(String.valueOf(param.getId()));
                businessLetterOld = this.getById(param.getId());
            } else {
                initInfoApiDTO.setBizId(String.valueOf(id));
            }

            // 调用审批流程
            if (Objects.nonNull(businessLetterDO.getApprovalId()) && Objects.nonNull(businessLetterOld)) {
                initInfoApiDTO.setResubmitApprovalId(businessLetterDO.getApprovalId());

            }

            if (StringUtils.isEmpty(businessLetterDO.getApprovalCode())) {
                businessLetterDO.setApprovalCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.BUSINESS_LETTER));
            }

            initInfoApiDTO.setApprovalType(HrAttendanceApplicationFormTypeEnum.BUSINESS_LETTER.getCode());
            initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
            initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());

            initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
            initInfoApiDTO.setApplyUserCode(code);
            initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            initInfoApiDTO.setAppointApprovalCode(businessLetterDO.getApprovalCode());


            List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();

            buildData(param, businessLetterDO, deptDO, hrmsUserInfoDO, postDO, fieldApiDTOList);

            initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

            log.info("buildData||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

            // 报文设置
            ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
            businessLetterDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        }

        // 判断有没有id
        if (Objects.nonNull(param.getId())) {
            //附件表更改和落库
            List<AttachmentParamDTO> addDTO = new ArrayList<>();
            if (!CollUtil.isEmpty(param.getAttachments())) {
                addDTO = param.getAttachments().stream().filter(item -> item.getId() == null).collect(Collectors.toList());
            }
            List<HrmsAttachmentDO> trainingAttachment = getTrainingAttachment(param.getAttachments(), param.getId());
            // 批量保存附件
            hrmsAttachmentManage.batchSave(addDTO, String.valueOf(param.getId()), TableEnum.HRMS_BUSINESS_LETTER.getCode());
            if (!CollUtil.isEmpty(trainingAttachment)) {
                hrmsAttachmentDao.updateBatchById(trainingAttachment);
            }
            BaseDOUtil.fillDOUpdate(businessLetterDO);
            this.getBaseMapper().updateById(businessLetterDO);
        } else {
            hrmsAttachmentManage.batchSave(param.getAttachments(), id.toString(), TableEnum.HRMS_BUSINESS_LETTER.getCode());
            BaseDOUtil.fillDOInsert(businessLetterDO);
            businessLetterDO.setId(id);
            this.getBaseMapper().insert(businessLetterDO);
        }



        return true;

    }

    private void buildData(BusinessLetterAddParam param, BusinessLetterDO businessLetterDO,HrmsEntDeptDO deptDO, HrmsUserInfoDO hrmsUserInfoDO, HrmsEntPostDO postDO, List<ApprovalTypeFieldApiDTO> fieldApiDTOList) {
        // 添加部门
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), deptDO.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), deptDO.getDeptNameEn());
        customFieldBuild(fieldApiDTOList, "deptName", deptDO.getDeptNameEn(), deptMap);
        customFieldBuild(fieldApiDTOList, "deptId", hrmsUserInfoDO.getDeptId().toString(), null);

        // 添加岗位
        Map<String, String> postMap = new HashMap<>();
        postMap.put(LanguageTypeEnum.zh_CN.getCode(), postDO.getPostNameCn());
        postMap.put(LanguageTypeEnum.en_US.getCode(), postDO.getPostNameEn());
        customFieldBuild(fieldApiDTOList, "postId", postDO.getId().toString(), null);
        customFieldBuild(fieldApiDTOList, "postName", postDO.getPostNameEn(), postMap);

        // 添加国家
        customFieldBuild(fieldApiDTOList, "orgCountry", hrmsUserInfoDO.getOriginCountry(), null);

        // 添加用户账号(userCode)
        customFieldBuild(fieldApiDTOList, "userCode", hrmsUserInfoDO.getUserCode(), null);

        // 添加用户姓名(userName)
        Map<String, String> userNameMap = new HashMap<>();
        userNameMap.put(LanguageTypeEnum.zh_CN.getCode(), hrmsUserInfoDO.getUserName());
        userNameMap.put(LanguageTypeEnum.en_US.getCode(), hrmsUserInfoDO.getUserNameEn());
        customFieldBuild(fieldApiDTOList, "userName", hrmsUserInfoDO.getUserName(), userNameMap);

        // 添加事件类型lettersCodes
        List<String> lettersCodes = Arrays.asList(businessLetterDO.getLettersCodes().split(","));
        customFieldBuild(fieldApiDTOList, "lettersCodes", JSON.toJSONString(lettersCodes), null);

        // 添加事件类型lettersName
        Map<String, String> lettersNameMap = new HashMap<>();
        Map<String, String> lettersNamesMap = new HashMap<>();

        List<String> letterNameCn = new ArrayList<>();
        List<String> letterNameEn = new ArrayList<>();

        // ，分割
        String letterNamesCn = "";
        String letterNamesEn = "";

        for (String item : lettersCodes) {
            letterNamesCn += BusinessLetterEnum.getInstance(item).getDesc() + ",";
            letterNamesEn += BusinessLetterEnum.getInstance(item).getDescEn() + ",";
            letterNameCn.add(BusinessLetterEnum.getInstance(item).getDesc());
            letterNameEn.add(BusinessLetterEnum.getInstance(item).getDescEn());
        }

        if (StringUtils.isNotBlank(letterNamesCn)) {
            letterNamesCn = letterNamesCn.substring(0, letterNamesCn.length() - 1);
            letterNamesEn = letterNamesEn.substring(0, letterNamesEn.length() - 1);
        }


        lettersNameMap.put(LanguageTypeEnum.zh_CN.getCode(), JSON.toJSONString(letterNameCn));
        lettersNameMap.put(LanguageTypeEnum.en_US.getCode(), JSON.toJSONString(letterNameEn));
        lettersNamesMap.put(LanguageTypeEnum.zh_CN.getCode(), letterNamesCn);
        lettersNamesMap.put(LanguageTypeEnum.en_US.getCode(), letterNamesEn);

        customFieldBuild(fieldApiDTOList, "lettersName", null, lettersNameMap);
        customFieldBuild(fieldApiDTOList, "lettersNames", null, lettersNamesMap);

        customFieldBuild(fieldApiDTOList, "nocStartDate", DateUtil.format(param.getNocStartDate(), TIME_FORMAT), null);

        customFieldBuild(fieldApiDTOList, "nocEndDate", DateUtil.format(param.getNocEndDate(), TIME_FORMAT), null);

        // 添加收件人名称
        customFieldBuild(fieldApiDTOList, "nameAddress", businessLetterDO.getNameAddress(), null);

        // 添加收件人地址
        customFieldBuild(fieldApiDTOList, "locationAddress", businessLetterDO.getLocationAddress(), null);

        // 备注
        customFieldBuild(fieldApiDTOList, "remark", businessLetterDO.getRemark(), null);

        // 附件
        buildApproveFiles(param.getAttachments(), "fileUrls", fieldApiDTOList);


    }

    @Override
    public Boolean delete(Long id) {
        BusinessLetterDO businessLetterDO = this.getBaseMapper().selectById(id);
        // 如果不是草稿状态，不能删除
        if (!ApprovalStatusEnum.OTHER.getCode().equals(businessLetterDO.getApprovalStatus())) {
            throw BusinessException.get("The process status has changed, and the request cannot be delete. Please refresh and try again");
        }
        // 逻辑删除
        businessLetterDO.setId(id);
        businessLetterDO.setIsDelete(IsDeleteEnum.YES.getCode());
        this.getBaseMapper().updateById(businessLetterDO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(Long id) {

        BusinessLetterDO businessLetterDO = this.getBaseMapper().selectById(id);
        // 非审核中状态和驳回状态下 不能撤销
        if (!ApprovalStatusEnum.APPROVING.getCode().equals(businessLetterDO.getApprovalStatus()) && !ApprovalStatusEnum.RETURN.getCode().equals(businessLetterDO.getApprovalStatus())) {
            throw BusinessException.get(HrmsErrorCodeEnums.BUSINESS_LETTER_STATUS_CHANGE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.BUSINESS_LETTER_STATUS_CHANGE.getDesc()));
        }

        // 状态变更
        businessLetterDO.setApprovalStatus(ApprovalStatusEnum.BACK.getCode());

        BaseDOUtil.fillDOUpdate(businessLetterDO);

        this.getBaseMapper().updateById(businessLetterDO);

        // 调用审批流程
        bpmCreateApprovalService.backApply(businessLetterDO.getApprovalId());

        return true;
    }

    private List<HrmsAttachmentDO> getTrainingAttachment(List<AttachmentParamDTO> attachments, Long trainingId) {
        //附件信息查询
        AttachmentQuery attachmentQuery = AttachmentQuery.builder().tableName(TableEnum.HRMS_BUSINESS_LETTER.getCode()).foreignKey(String.valueOf(trainingId)).build();
        Map<Long, HrmsAttachmentDO> attachmentDOMap = hrmsAttachmentDao.getAttachment(attachmentQuery)
                .stream()
                .collect(Collectors.toMap(HrmsAttachmentDO::getId, item -> item, (oldValue, newValue) -> oldValue));
        List<Long> collect = new ArrayList<>();
        if (!CollUtil.isEmpty(attachments)) {
            collect = attachments.stream().filter(item -> item.getId() != null).map(AttachmentParamDTO::getId).collect(Collectors.toList());
        }
        List<HrmsAttachmentDO> trainingAttachment = new ArrayList<>();
        if (CollUtil.isEmpty(attachmentDOMap)) {
            return trainingAttachment;
        }
        if (!CollUtil.isEmpty(collect)) {
            for (Long id : collect) {
                attachmentDOMap.remove(id);
            }
        }
        for (HrmsAttachmentDO item : attachmentDOMap.values()) {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(item);
            trainingAttachment.add(item);
        }
        return trainingAttachment;
    }

    private static void buildApproveFiles(List<AttachmentParamDTO> files, String code, List<ApprovalTypeFieldApiDTO> fieldApiDTOList) {
        if (!CollectionUtil.isEmpty(files)) {
            List<FileTemplateApiDTO> apiList = files.stream().map(e -> {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(e.getAttachmentName());
                apiDTO.setFileUrl(e.getUrlPath());
                apiDTO.setFileType(e.getAttachmentType());
                return apiDTO;
            }).collect(Collectors.toList());
            customFieldBuild(fieldApiDTOList, code, JSON.toJSONString(apiList), null);
        }
    }

    private void previewDTOBuild(List<ApprovalEmptyRecordApiDTO> recordApiDTOList, List<ApprovalDetailStepRecordDTO> resultDTOList, String userCode) {
        //获取所有国家的HR信息
        List<String> hrUserCodeList = new ArrayList<>();
        countryMainPocMap.forEach((k, v) -> hrUserCodeList.add(v));
        List<UserDTO> allHrUserDTOS = hrmsUserInfoService.listUserInfoByUserCode(hrUserCodeList);
        List<UserInfoDetailApiDTO> allHrUserInfoList = HrmsCollectionUtils.convert(allHrUserDTOS, UserInfoDetailApiDTO.class);
        Map<String, List<UserInfoDetailApiDTO>> hrUserCodeMap = allHrUserInfoList.stream().collect(Collectors.groupingBy(UserInfoDetailApiDTO::getUserCode));

        //获取所有国家信息
        List<CountryConfigDTO> countryConfigDTOList = countryService.queryAllCountryConfigList();
        List<CountryDTO> countryDTOList = com.imile.util.BeanUtils.convert(CountryDTO.class, countryConfigDTOList);
        Map<String, List<CountryDTO>> countryMap = countryDTOList.stream().collect(Collectors.groupingBy(CountryDTO::getCountryName));

        //获取所有人员(角色和指定人员总和)
        List<String> allAppointUserCodeList = new ArrayList<>();
        List<String> allRoleUserCodeList = new ArrayList<>();
        List<String> allUserCodeList = new ArrayList<>();
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }
            //所有指定人员
            List<ApprovalUserInfoApiDTO> appointUserList = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(appointUserList)) {
                appointUserList = new ArrayList<>();
            }
            List<String> appointUserCodeList = appointUserList.stream().map(ApprovalUserInfoApiDTO::getUserCode).collect(Collectors.toList());
            allAppointUserCodeList.addAll(appointUserCodeList);

            //所有角色对应人员
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserList = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserList)) {
                approvalCreateRoleUserList = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserDTO : approvalCreateRoleUserList) {
                List<ApprovalUserInfoApiDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
                if (CollectionUtils.isEmpty(roleUserInfos)) {
                    continue;
                }
                List<String> roleUserCodeList = roleUserInfos.stream().map(ApprovalUserInfoApiDTO::getUserCode).collect(Collectors.toList());
                allRoleUserCodeList.addAll(roleUserCodeList);
            }
        }
        allUserCodeList.addAll(allAppointUserCodeList);
        allUserCodeList.addAll(allRoleUserCodeList);
        allUserCodeList.add(userCode);
        //调用HR接口，获取所有人员信息
        List<UserDTO> users = hrmsUserInfoService.listUserInfoByUserCode(allUserCodeList);
        List<UserInfoDetailApiDTO> allUserInfoList = HrmsCollectionUtils.convert(users, UserInfoDetailApiDTO.class);
        Map<String, List<UserInfoDetailApiDTO>> userCodeMap = allUserInfoList.stream().collect(Collectors.groupingBy(UserInfoDetailApiDTO::getUserCode));


        int temp = 1;
        for (ApprovalEmptyRecordApiDTO approvalEmptyRecordApiDTO : recordApiDTOList) {
            //发起人节点
            if (temp == 1) {
                ApprovalDetailStepRecordDTO createApprovalUserDTO = new ApprovalDetailStepRecordDTO();
                createApprovalUserDTO.setApprovalType(HrAttendanceApplicationFormTypeEnum.BUSINESS_LETTER.getCode());
                createApprovalUserDTO.setApprovalRecordType("approval");
                createApprovalUserDTO.setRecordStatus(-1);
                createApprovalUserDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setStepName(RequestInfoHolder.isChinese() ? "发起审批" : "Initiate approval");
                createApprovalUserDTO.setRecordStatusUpdateDate(new Date());
                createApprovalUserDTO.setStepId("APPLY");
                List<ApprovalUserInfoDTO> approvalUserDTOList = com.imile.util.BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
                createApprovalUserDTO.setApprovalUserInfoDTOS(approvalUserDTOList);
                resultDTOList.add(createApprovalUserDTO);
                temp++;
                continue;
            }
            //非发起人节点
            ApprovalDetailStepRecordDTO stepRecordDTO = com.imile.util.BeanUtils.convert(approvalEmptyRecordApiDTO, ApprovalDetailStepRecordDTO.class);
            stepRecordDTO.setApprovalType(HrAttendanceApplicationFormTypeEnum.BUSINESS_LETTER.getCode());

            if (approvalEmptyRecordApiDTO.getApprovalRecordType().equals("cc")) {
                stepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "待抄送" : "Wait CC");
                stepRecordDTO.setRecordStatus(7);
                stepRecordDTO.setApprovalRecordType("cc");
            }else {
                stepRecordDTO.setApprovalRecordType("approval");
                stepRecordDTO.setRecordStatus(1);
                stepRecordDTO.setRecordStatusName(RequestInfoHolder.isChinese() ? "审批中" : "In review");
            }

            //指定人员
            List<ApprovalUserInfoDTO> approvalUserDTOList = com.imile.util.BeanUtils.convert(ApprovalUserInfoDTO.class, approvalEmptyRecordApiDTO.getApprovalUserInfoDTOS());
            approvalUserDTOList.forEach(item -> {
                item.setApprovalOperation("APPROVING");
            });
            stepRecordDTO.setApprovalUserInfoDTOS(approvalUserDTOList);

            //角色/指定人员处理
            //整体流程主节点信息
            List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList = new ArrayList<>();
            List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList = new ArrayList<>();
            //所有查询到的用户
            List<ApprovalUserInfoDTO> findUserList = stepRecordDTO.getApprovalUserInfoDTOS();

            ApprovalCreateUserApiDTO approvalCreateUserApiDTO = approvalEmptyRecordApiDTO.getApprovalCreateUserApiDTO();
            if (approvalCreateUserApiDTO == null) {
                approvalCreateUserApiDTO = new ApprovalCreateUserApiDTO();
            }

            ApprovalCreateUserDTO approvalCreateUserDTO = new ApprovalCreateUserDTO();
            stepRecordDTO.setApprovalCreateUserDTO(approvalCreateUserDTO);
            List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS = approvalCreateUserApiDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(approvalUserInfoApiDTOS)) {
                approvalUserInfoApiDTOS = new ArrayList<>();
            }
            approvalCreateUserDTO.setApprovalUserInfos(com.imile.util.BeanUtils.convert(ApprovalUserInfoDTO.class, approvalUserInfoApiDTOS));

            List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = new ArrayList<>();
            approvalCreateUserDTO.setApprovalCreateRoleUserDTOS(approvalCreateRoleUserDTOS);
            List<ApprovalCreateRoleUserApiDTO> approvalCreateRoleUserApiDTOS = approvalCreateUserApiDTO.getApprovalCreateRoleUserApiDTOS();
            if (CollectionUtils.isEmpty(approvalCreateRoleUserApiDTOS)) {
                approvalCreateRoleUserApiDTOS = new ArrayList<>();
            }
            for (ApprovalCreateRoleUserApiDTO roleUserApiDTO : approvalCreateRoleUserApiDTOS) {
                ApprovalCreateRoleUserDTO roleUserDTO = new ApprovalCreateRoleUserDTO();
                roleUserDTO.setApprovalRole(roleUserApiDTO.getApprovalRole());
                roleUserDTO.setApprovalRoleName(roleUserApiDTO.getApprovalRoleName());
                roleUserDTO.setFetchObject(roleUserApiDTO.getFetchObject());
                roleUserDTO.setFetchObjectName(roleUserApiDTO.getFetchObjectName());
                roleUserDTO.setFetchObjectValue(roleUserApiDTO.getFetchObjectValue());
                roleUserDTO.setFetchObjectValueName(roleUserApiDTO.getFetchObjectValueName());
                roleUserDTO.setApprovalUserCodes(roleUserApiDTO.getApprovalUserCodes());
                if (CollectionUtils.isNotEmpty(roleUserApiDTO.getApprovalUserInfos())) {
                    roleUserDTO.setApprovalUserInfos(com.imile.util.BeanUtils.convert(ApprovalUserInfoDTO.class, roleUserApiDTO.getApprovalUserInfos()));
                }
                approvalCreateRoleUserDTOS.add(roleUserDTO);
            }

            previewUserInfoFind(userCode, countryMap, hrUserCodeMap, userCodeMap, findUserList, approvalCreateUserDTO, approvalPreviewErrorUserDTOList, approvalPreviewSuccessUserDTOList);

            approvalCreateUserDTO.setApprovalPreviewErrorUserDTOList(approvalPreviewErrorUserDTOList);
            approvalCreateUserDTO.setApprovalPreviewSuccessUserDTOList(approvalPreviewSuccessUserDTOList);
            resultDTOList.add(stepRecordDTO);
            temp++;
        }
    }

    private void previewUserInfoFind(String applyUserCode, Map<String, List<CountryDTO>> countryMap, Map<String, List<UserInfoDetailApiDTO>> hrUserCodeMap,
                                     Map<String, List<UserInfoDetailApiDTO>> userCodeMap, List<ApprovalUserInfoDTO> approvalUserInfoDTOS, ApprovalCreateUserDTO approvalCreateUserDTO,
                                     List<ApprovalPreviewErrorUserDTO> approvalPreviewErrorUserDTOList, List<ApprovalPreviewSuccessUserDTO> approvalPreviewSuccessUserDTOList) {
        List<String> findUserCodeList = approvalUserInfoDTOS.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
        //所有指定人员
        List<ApprovalUserInfoDTO> appointUserList = approvalCreateUserDTO.getApprovalUserInfos();
        if (CollectionUtils.isEmpty(appointUserList)) {
            appointUserList = new ArrayList<>();
        }
        for (ApprovalUserInfoDTO userInfoDTO : appointUserList) {
            List<UserInfoDetailApiDTO> userInfoDetailApiDTOList = userCodeMap.get(userInfoDTO.getUserCode());
            if (CollectionUtils.isEmpty(userInfoDetailApiDTOList)) {
                continue;
            }
            //获取HR信息
            String hrUserCode = countryMainPocMap.get(userInfoDetailApiDTOList.get(0).getOriginCountry());
            String userHrMessage = hrUserCode;
            if (StringUtils.isNotBlank(hrUserCode)) {
                List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                    userHrMessage = hrUserDetail.get(0).getUserName();
                }
            }
            if (findUserCodeList.contains(userInfoDTO.getUserCode())) {
                //指定人员存在
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userInfoDetailApiDTOList.get(0).getUserName() + " 是指定员工" : userInfoDetailApiDTOList.get(0).getUserName() + " is fixed in process");
                successUserDTO.setUserHrMessage(userHrMessage);
                successUserDTO.setTemp(1);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                continue;
            }
            //指定人员不存在
            if (StringUtils.equalsIgnoreCase(userInfoDetailApiDTOList.get(0).getWorkStatus(), "DIMISSION")) {
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工  " + userInfoDetailApiDTOList.get(0).getUserName() + " 已经离职" : "The fixed approver " + userInfoDetailApiDTOList.get(0).getUserName() + " is Offboard");
                errorUserDTO.setUserHrMessage(userHrMessage);
                errorUserDTO.setTemp(1);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                continue;
            }
            if (StringUtils.equalsIgnoreCase(userInfoDetailApiDTOList.get(0).getStatus(), "DISABLED")) {
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? "指定员工 " + userInfoDetailApiDTOList.get(0).getUserName() + " 已经冻结" : "The fixed approver " + userInfoDetailApiDTOList.get(0).getUserName() + " is Disabled");
                errorUserDTO.setUserHrMessage(userHrMessage);
                errorUserDTO.setTemp(1);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
            }
        }

        //所有角色对应人员
        List<ApprovalCreateRoleUserDTO> approvalCreateRoleUserDTOS = approvalCreateUserDTO.getApprovalCreateRoleUserDTOS();
        if (CollectionUtils.isEmpty(approvalCreateRoleUserDTOS)) {
            approvalCreateRoleUserDTOS = new ArrayList<>();
        }
        //申请人HR相关信息
        //获取HR信息
        List<UserInfoDetailApiDTO> applyUserInfoDetailApiDTOList = userCodeMap.get(applyUserCode);
        if (CollectionUtils.isEmpty(applyUserInfoDetailApiDTOList)) {
            return;
        }

        for (ApprovalCreateRoleUserDTO roleUserDTO : approvalCreateRoleUserDTOS) {
            List<ApprovalUserInfoDTO> roleUserInfos = roleUserDTO.getApprovalUserInfos();
            if (CollectionUtils.isEmpty(roleUserInfos)) {
                //这个角色没有找到人
                ApprovalPreviewErrorUserDTO errorUserDTO = new ApprovalPreviewErrorUserDTO();
                errorUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                errorUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                errorUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                errorUserDTO.setTemp(2);
                approvalPreviewErrorUserDTOList.add(errorUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = applyUserInfoDetailApiDTOList.get(0).getUserName() + " - " + approvalRoleEnum.getDescUS() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserInfoDetailApiDTOList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<HrmsEntDeptDO> deptApiDTOList = hrmsDeptManage.selectDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + " - " + approvalRoleEnum.getDescCN() + " 不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + " - " + approvalRoleEnum.getDescCN() + " not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "不存在";
                    String userMessageEn = countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN() + "not found";
                    errorUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    errorUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                continue;
            }

            for (ApprovalUserInfoDTO userInfoDTO : roleUserInfos) {
                List<UserInfoDetailApiDTO> userInfoDetailApiDTOList = userCodeMap.get(userInfoDTO.getUserCode());
                if (CollectionUtils.isEmpty(userInfoDetailApiDTOList)) {
                    continue;
                }
                //这个角色找到人
                ApprovalPreviewSuccessUserDTO successUserDTO = new ApprovalPreviewSuccessUserDTO();
                successUserDTO.setApprovalRole(roleUserDTO.getApprovalRole());
                successUserDTO.setFetchObject(roleUserDTO.getFetchObject());
                successUserDTO.setFetchObjectValue(roleUserDTO.getFetchObjectValue());
                successUserDTO.setUserCode(userInfoDetailApiDTOList.get(0).getUserCode());
                successUserDTO.setTemp(2);
                approvalPreviewSuccessUserDTOList.add(successUserDTO);
                ApprovalRoleValueEnum approvalRoleValueEnum = ApprovalRoleValueEnum.getInstanceByCode(roleUserDTO.getFetchObject());
                ApprovalRoleEnum approvalRoleEnum = ApprovalRoleEnum.getInstanceByCode(roleUserDTO.getApprovalRole());
                if (approvalRoleValueEnum == null || approvalRoleEnum == null) {
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.BE_APPROVERED.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.APPLY_USER.getCode())) {
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(applyUserInfoDetailApiDTOList.get(0).getOriginCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPT.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.STATION.getCode())) {
                    List<HrmsEntDeptDO> deptApiDTOList = hrmsDeptManage.selectDeptByIds(Collections.singletonList(Long.valueOf(roleUserDTO.getFetchObjectValue())));
                    if (CollectionUtils.isEmpty(deptApiDTOList)) {
                        continue;
                    }
                    List<CountryDTO> countryDTOList = countryMap.get(deptApiDTOList.get(0).getCountry());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameCn() + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + deptApiDTOList.get(0).getDeptNameEn() + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息
                    String hrUserCode = countryMainPocMap.get(deptApiDTOList.get(0).getCountry());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);

                    continue;
                }
                if (StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.CLEARING_THEME.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DEPARTURE_COUNTRY.getCode())
                        || StringUtils.equalsIgnoreCase(approvalRoleValueEnum.getCode(), ApprovalRoleValueEnum.DESTINATION_COUNTRY.getCode())) {
                    List<CountryDTO> countryDTOList = countryMap.get(roleUserDTO.getFetchObjectValue());
                    if (CollectionUtils.isEmpty(countryDTOList)) {
                        continue;
                    }
                    String userMessageCn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescCN();
                    String userMessageEn = userInfoDetailApiDTOList.get(0).getUserName() + " is " + countryDTOList.get(0).getCountryName() + " - " + approvalRoleEnum.getDescUS();
                    successUserDTO.setUserMessage(RequestInfoHolder.isChinese() ? userMessageCn : userMessageEn);

                    //获取HR信息

                    String hrUserCode = countryMainPocMap.get(countryDTOList.get(0).getCountryName());
                    String userHrMessage = hrUserCode;
                    if (StringUtils.isNotBlank(hrUserCode)) {
                        List<UserInfoDetailApiDTO> hrUserDetail = hrUserCodeMap.get(hrUserCode);
                        if (CollectionUtils.isNotEmpty(hrUserDetail)) {
                            userHrMessage = hrUserDetail.get(0).getUserName();
                        }
                    }
                    successUserDTO.setUserHrMessage(userHrMessage);
                }
            }
        }
    }


}
