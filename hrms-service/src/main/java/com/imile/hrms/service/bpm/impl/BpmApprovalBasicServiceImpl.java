package com.imile.hrms.service.bpm.impl;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.HrmsLaborContractInfoDao;
import com.imile.hrms.dao.user.dao.HrmsUserEducationInfoDao;
import com.imile.hrms.dao.user.dao.HrmsUserExtendInfoDao;
import com.imile.hrms.dao.user.model.*;
import com.imile.hrms.manage.user.HrmsUserCertificateManage;
import com.imile.hrms.manage.user.HrmsUserEntryRecordManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.bpm.BpmApprovalBasicService;
import com.imile.hrms.service.user.transaction.UserInfoTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-11-17
 * @version: 1.0
 */
@Slf4j
@Service
public class BpmApprovalBasicServiceImpl extends BaseService implements BpmApprovalBasicService {
    @Autowired
    private HrmsUserInfoManage hrmsUserInfoManage;
    @Autowired
    private HrmsUserCertificateManage hrmsUserCertificateManage;
    @Autowired
    private HrmsUserEntryRecordManage hrmsUserEntryRecordManage;
    @Autowired
    private HrmsUserExtendInfoDao hrmsUserExtendInfoDao;
    @Autowired
    private HrmsLaborContractInfoDao hrmsLaborContractInfoDao;
    @Autowired
    private HrmsUserEducationInfoDao hrmsUserEducationInfoDao;
    @Autowired
    private UserInfoTransaction userInfoTransaction;


    @Override
    public void deleteUserAddInfo(Long userId) {
        HrmsUserInfoDO userInfoDO = hrmsUserInfoManage.getUserInfoById(userId);
        if (userInfoDO != null) {
            userInfoDO.setIsDelete(IsDeleteEnum.YES.getCode());
            fillDOUpdate(userInfoDO);
        }
        List<HrmsUserEntryRecordDO> userEntryRecordDOList = hrmsUserEntryRecordManage.selectUserEntryByUserIds(Arrays.asList(userId));
        if (CollectionUtils.isNotEmpty(userEntryRecordDOList)) {
            userEntryRecordDOList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(item);
            });
        }
        HrmsUserExtendInfoDO extendInfoDO = hrmsUserExtendInfoDao.getByUserId(userId);
        if (extendInfoDO != null) {
            extendInfoDO.setIsDelete(IsDeleteEnum.YES.getCode());
            fillDOUpdate(extendInfoDO);
        }
        List<HrmsLaborContractInfoDO> laborContractInfoDOList = hrmsLaborContractInfoDao.getByUserId(userId);
        if (CollectionUtils.isNotEmpty(laborContractInfoDOList)) {
            laborContractInfoDOList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(item);
            });
        }
        List<HrmsUserEducationInfoDO> userEducationInfoDOList = hrmsUserEducationInfoDao.getByUserId(userId);
        if (CollectionUtils.isNotEmpty(userEducationInfoDOList)) {
            userEducationInfoDOList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(item);
            });
        }
        List<HrmsUserCertificateDO> certificateDOList = hrmsUserCertificateManage.listByUserIds(Arrays.asList(userInfoDO.getId()));
        if (CollectionUtils.isNotEmpty(certificateDOList)) {
            certificateDOList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                fillDOUpdate(item);
            });
        }
        userInfoTransaction.deleteUserAddInfo(userInfoDO, userEntryRecordDOList, extendInfoDO, laborContractInfoDOList, userEducationInfoDOList, certificateDOList);
    }
}
