package com.imile.hrms.service.driver.attendance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.hrms.api.attendance.api.DriverAttendanceGeneralApi;
import com.imile.hrms.api.attendance.param.SalaryDriverAttendanceApiParam;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.service.driver.attendance.HrmsDriverAttendanceDetailService;
import com.imile.hrms.service.driver.attendance.param.DriverAttendanceDetailMonthParam;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceGeneralApiImpl
 * {@code @since:} 2024-12-26 19:28
 * {@code @description:}
 */
@Service(version = "1.0.0")
@Slf4j
public class DriverAttendanceGeneralApiImpl implements DriverAttendanceGeneralApi {


    @Resource
    private HrmsDriverAttendanceDetailService hrmsDriverAttendanceDetailService;

    /**
     * 查询薪资司机用户考勤信息(CBMS调用)
     *
     * @param param
     */
    @Override
    public RpcResult<List<Map<String, String>>> selectSalaryDriverUserAttendanceInfo(SalaryDriverAttendanceApiParam param) {
        log.info("selectSalaryDriverUserAttendanceInfo|{}", JSON.toJSONString(param));
        List<Map<String, String>> resultList = Lists.newArrayList();
        if (ObjectUtil.isNull(param)) {
            return RpcResult.ok(resultList);
        }
        DriverAttendanceDetailMonthParam driverAttendanceDetailMonthParam = new DriverAttendanceDetailMonthParam();
        driverAttendanceDetailMonthParam.setUserCodeList(param.getUserCodeList());
        driverAttendanceDetailMonthParam.setMonthStartDate(param.getStartTime());
        driverAttendanceDetailMonthParam.setMonthEndDate(param.getEndTime());

        List<Map<String, String>> targetListMap = Lists.newArrayList();

        try {
            targetListMap = hrmsDriverAttendanceDetailService.queryDriverAttendanceForSalary(resultList, driverAttendanceDetailMonthParam);
        }catch (Exception e){
            log.error("薪资获取司机考勤数据报错", e);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.SALARY_GET_DRIVER_ATTENDANCE_DATA_ERROR);
        }

        if (CollUtil.isNotEmpty(targetListMap)) {
            log.info("selectSalaryDriverUserAttendanceInfo targetListMap:{}", JSON.toJSON(targetListMap));
        }
        return RpcResult.ok(targetListMap);
    }
}
