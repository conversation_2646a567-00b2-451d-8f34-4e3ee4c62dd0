package com.imile.hrms.service.newAttendance.punchConfig.dto;

import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceDept;
import com.imile.hrms.service.newAttendance.common.dto.AttendanceUser;
import lombok.Data;

import java.util.function.Function;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@Data
public class PunchConfigRangeDTO {

    /**
     * 业务ID 部门id、用户ID
     */
    private Long bizId;

    /**
     * 编码no
     */
    private String punchConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    private String rangeType;

    /**
     * 是否需要打卡 0:false,1:true
     */
    private Integer isNeedPunch;

    /**
     * 部门名称
     */
    private String bizNameByLang;

    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;

    /**
     * 打卡配置名称
     */
    private String attendancePunchConfigName;


    public static <T> PunchConfigRangeDTO buildUserRangeDTO(AttendanceUser attendanceUser) {
        return getPunchConfigRangeDTO(
                RangeTypeEnum.USER,
                t -> attendanceUser.getId(),
                t -> RequestInfoHolder.isChinese() ? attendanceUser.getUserName() : attendanceUser.getUserNameEn(),
                attendanceUser
        );
    }

    public static <T> PunchConfigRangeDTO buildDeptRangeDTO(AttendanceDept attendanceDept) {
        return getPunchConfigRangeDTO(
                RangeTypeEnum.DEPT,
                t -> attendanceDept.getId(),
                t -> RequestInfoHolder.isChinese() ? attendanceDept.getDeptNameCn() : attendanceDept.getDeptNameEn(),
                attendanceDept);
    }


    private static <T> PunchConfigRangeDTO getPunchConfigRangeDTO(RangeTypeEnum rangeType,
                                                                  Function<T, Long> idGetter,
                                                                  Function<T, String> nameGetter,
                                                                  T record) {
        PunchConfigRangeDTO rangeDTO = new PunchConfigRangeDTO();
        rangeDTO.setBizId(idGetter.apply(record));
        rangeDTO.setBizNameByLang(nameGetter.apply(record));
        rangeDTO.setRangeType(rangeType.getCode());
        return rangeDTO;
    }
}
