package com.imile.hrms.service.user;

import com.imile.hrms.api.user.result.UserContractDTO;
import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.user.model.UserContractDO;
import com.imile.hrms.service.user.param.UserContractSaveParam;
import com.imile.hrms.service.user.result.UserContractBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
public interface UserContractService {

    /**
     * 获取人员合同差异
     *
     * @param userId    人员ID
     * @param paramList 人员合同列表
     * @return DataDifferHolder<UserContractDO>
     */
    DataDifferHolder<UserContractDO> differ(Long userId, List<UserContractSaveParam> paramList);

    /**
     * 获取人员合同列表
     *
     * @param userId 人员ID
     * @return List<UserContractBO>
     */
    List<UserContractBO> getUserContractList(Long userId);

    /**
     * 获取人员合同列表（对外dubbo接口）
     *
     * @param userCode 人员编码
     * @return List<UserContractDTO>
     */
    List<UserContractDTO> getUserContractList(String userCode);
}
