package com.imile.hrms.service.recruitment.param;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfferListExportParam extends ResourceQuery {

    /**
     * 查询的部门ids
     */
    private String deptIds;

    /**
     * 单据编号-就是审批编号
     */
    private String approveCode;

    /**
     * 国家 - 对应Offer流程操作中的国家/所属国家
     */
    private String country;

    /**
     * 岗位名称 - 对应Offer流程操作中的HC的岗位名称
     */
    private String positionName;

    private Long postId;

    /**
     * 职级序列（classification） - 对应Offer流程操作中的HC的职级序列
     */
    private String gradeId;

    /**
     * 职级（jobLevel） - 对应Offer流程操作中的HC的职级
     */
    private String gradeNo;

    /**
     * 候选人姓名 - 对应Offer流程操作中的候选人姓名
     */
    private String candidateName;

    /**
     * 申请人（applicant） - 对应Offer流程中的申请人
     */
    private String applyUserName;

    /**
     * 申请时间 applicationDate - 对应流程中的申请时间
     */
    private String createStartTime;

    private String createEndTime;


    /**
     * 期望入职日期 date of join
     */
    private String expectStartTime;

    private String expectEndTime;

    /**
     * 流程状态 applicationStatus - 以下列格式：1审批中 2 通过 -1拒绝 -2驳回 -3撤回
     */
    private String approveStatus;

    /**
     * hc id
     */
    private String hcIds;

    /**
     * Offer状态
     * 单据状态(0: 草稿；10: 进行中；20: 取消Offer；30: 拒绝Offer；40: 接受Offer; 50: 超期未处理 )
     */
    private String status;

    /**
     * 候选人是否已入职，1=已入职，0=未入职;
     */
    private Boolean isCandidateOnboarding;

    private Integer isChineseInitiating;

    /**
     * 招聘类型
     * (0: 社会招聘；1: 校园招聘；2: 实习生招聘)
     */
    private String recruitmentTypes;
}
