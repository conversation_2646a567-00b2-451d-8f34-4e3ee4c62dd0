package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.service.salary.dto.SalaryReportConfigInfoDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/9 15:41
 * @version: 1.0
 */
@Data
public class SalaryReportConfigUpdateParam {

    /**
     * 薪资报表配置ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long salaryReportConfigId;

    /**
     * 生效月份(202306这种格式)
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String effectMonth;

    /**
     * 薪资报表配置明细
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<SalaryReportConfigInfoDTO> salaryReportConfigInfoDTOList;

    /**
     * 实付金额科目
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long paymentItemConfigId;

}
