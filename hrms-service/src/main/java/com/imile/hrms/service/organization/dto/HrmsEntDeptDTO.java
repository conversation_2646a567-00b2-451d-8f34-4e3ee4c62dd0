package com.imile.hrms.service.organization.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class HrmsEntDeptDTO implements Serializable {
    private static final long serialVersionUID = -9007325373498109712L;

    /**
     * 部门id
     */
    private Long id;
    /**
     * 网点id（作业部门）
     */
    private Long ocId;
    /**
     * 网点中心编码
     */
    private String ocCenterCode;
    /**
     * 国家
     */
    private String country;

    /**
     * 上级部门id
     */
    private Long parentId;


    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;
    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 负责人
     */
    private Long leaderCode;

    /**
     * 负责人名称
     */
    private String leaderName;

    /**
     * 是否作业部门
     */
    private Integer isOperationDept;
    /**
     * 状态
     */
    private String status;

    /**
     * 描述
     */
    private String remark;
}
