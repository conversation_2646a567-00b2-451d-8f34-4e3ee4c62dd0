package com.imile.hrms.service.user.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.genesis.api.model.component.Phone;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Data
public class UserEntryAddParam {

    /**
     * 姓名
     */
    @NotNull(message = ValidCodeConstant.NOT_BLANK)
    @Length(max = 50, message = BusinessConstant.ValidateConstant.USER_NAME_LENGTH_RANGE)
    private String userName;

    /**
     * 联系电话
     */
    @Valid
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Phone contactPhone;

    /**
     * 个人邮箱(非公司邮箱)
     */
    @Email(message = ValidCodeConstant.EMAIL)
    private String personalEmail;

    /**
     * 国籍编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String nationalityCode;

    /**
     * 国籍名称
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String nationalityName;

    /**
     * 预计入职时间
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedEntryDate;

    /**
     * 是否是司机
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer isDriver;

    /**
     * 是否DTL
     */
    private Integer isDtl;

    /**
     * 所属部门ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long deptId;

    /**
     * 所属网点编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String ocCode;

    /**
     * 用工类型
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String employeeType;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 岗位ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long postId;

    /**
     * 汇报上级
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long leaderId;

    /**
     * 职级序列ID
     */
    private Long gradeId;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 所属业务节点ID
     */
    private Long bizModelId;

    /**
     * 项目编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String projectCode;

    /**
     * 常驻地国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCountry;

    /**
     * 常驻地省份
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationProvince;

    /**
     * 常驻地城市
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCity;
}
