package com.imile.hrms.service.user.driver;

import cn.hutool.core.bean.BeanUtil;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.common.enums.EmploymentTypeEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.WorkStatusEnum;
import com.imile.hrms.common.enums.account.AccountBizTypeEnum;
import com.imile.hrms.common.enums.account.AccountStatusEnum;
import com.imile.hrms.common.enums.account.AccountTypeHandleEnum;
import com.imile.hrms.dao.account.dao.HrmsAccountRecordDao;
import com.imile.hrms.dao.account.dao.HrmsAccountTypeDao;
import com.imile.hrms.dao.account.model.HrmsAccountRecordDO;
import com.imile.hrms.dao.account.model.HrmsAccountTypeDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.primary.entity.UserCertificateDO;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dto.OutSourceDriverRegisterParam;
import com.imile.hrms.dao.user.dto.UserCertificateParamDTO;
import com.imile.hrms.dao.user.model.HrmsLaborContractInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserEducationInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserExtendInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.VendorService;
import com.imile.hrms.manage.organization.HrmsDeptNewManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.primary.converter.UserCertificateConverter;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/28
 */
@Slf4j
public abstract class AbstractDriverHandler {

    @Autowired
    protected HrmsEntDeptDao hrmsEntDeptDao;

    @Autowired
    protected HrmsUserInfoDao hrmsUserInfoDao;

    @Autowired
    private HrmsProperties hrmsProperties;

    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    @Autowired
    private VendorService vendorService;

    @Autowired
    protected HrmsUserInfoManage hrmsUserInfoManage;

    @Autowired
    private HrmsAccountTypeDao hrmsAccountTypeDao;

    @Autowired
    protected HrmsAccountRecordDao hrmsAccountRecordDao;

    @Autowired
    private UserCertificateService userCertificateService;

    @Autowired
    private HrmsDeptNewManage deptManage;
    @Resource
    private UserManage userManage;


    public abstract HrmsUserInfoDO driverHandler(OutSourceDriverRegisterParam driverRegisterParam);

    protected HrmsUserEntryRecordDO handlerEntry(HrmsUserInfoDO userInfoDO) {
        return hrmsUserInfoManage.handleUserEntryRecord(userInfoDO);
    }

    protected HrmsLaborContractInfoDO handlerLaborContract(HrmsUserInfoDO userInfoDO) {
        return hrmsUserInfoManage.handlerLaborContractInfo(userInfoDO);
    }

    protected HrmsUserEducationInfoDO handlerEducation(HrmsUserInfoDO userInfoDO) {
        return hrmsUserInfoManage.handlerUserEducationInfo(userInfoDO);
    }

    protected HrmsUserExtendInfoDO handlerExtendInfo(HrmsUserInfoDO userInfoDO, String email) {
        return hrmsUserInfoManage.handleUserExtendInfo(userInfoDO, email);
    }


    protected List<HrmsAccountRecordDO> handlerOpenAccount(HrmsUserInfoDO userInfoDO) {
        List<HrmsAccountTypeDO> hrmsAccountTypeDOS = hrmsAccountTypeDao.allRecords(AccountBizTypeEnum.USER_ENTRY_OPEN.name());
        List<HrmsAccountRecordDO> res = new ArrayList<>();
        hrmsAccountTypeDOS.forEach(item -> {
            HrmsAccountRecordDO entity = new HrmsAccountRecordDO();
            entity.setId(iHrmsIdWorker.nextId());
            entity.setBizId(String.valueOf(userInfoDO.getId()));
            entity.setBizType(AccountBizTypeEnum.USER_ENTRY_OPEN.name());
            entity.setAccountTypeCode(item.getAccountTypeCode());
            // 是否需要处理
            AccountTypeHandleEnum accountTypeHandleEnum = AccountTypeHandleEnum.getInstance(item.getOpenHandleMethod());
            if (accountTypeHandleEnum == null) {
                log.warn("非法的accountTypeCode:{}", item.getAccountTypeCode());
                return;
            }
            entity.setIsNeedHandle(accountTypeHandleEnum.getCheck() ? BusinessConstant.Y : BusinessConstant.N);
            entity.setIsLatest(BusinessConstant.Y);
            // 默认为未开通
            entity.setIsSuccess(BusinessConstant.N);
            entity.setAccountStatus(BusinessConstant.Y.equals(entity.getIsNeedHandle()) ? AccountStatusEnum.CREATE_FAILED.name() : AccountStatusEnum.NOT_CREATE.name());
            res.add(entity);
        });
        return res;
    }

    protected DataDifferHolder<UserCertificateDO> userCertificateInfoHandler(HrmsUserInfoDO userInfoDO, List<UserCertificateParamDTO> userCertificateList) {
        List<UserCertificateSaveParam> paramList = UserCertificateConverter.convertParamList(userCertificateList);
        userCertificateService.checkUserCertificate(userInfoDO.getLocationCountry(), userInfoDO.getEmployeeType(),
                WhetherEnum.YES.getKey(), null, paramList, userInfoDO.getUserCode());
        return userCertificateService.differ(userInfoDO.getId(), paramList);

    }


    /**
     * 处理司机信息
     *
     * @param driverRegisterParam
     * @return
     */
    protected HrmsUserInfoDO userInfoHandler(OutSourceDriverRegisterParam driverRegisterParam) {

        HrmsUserInfoDO userInfoDO = BeanUtil.copyProperties(driverRegisterParam, HrmsUserInfoDO.class);
        userInfoDO.setIsDriver(BusinessConstant.Y);
        getUserInfo(driverRegisterParam, userInfoDO);

        //状态
        userInfoDO.setStatus(StatusEnum.ACTIVE.getCode());

        userInfoDO.setEmail(driverRegisterParam.getPersonalEmail());

        userInfoDO.setId(Optional.ofNullable(driverRegisterParam.getId()).orElse(iHrmsIdWorker.nextId()));
        //获取公司对应的国家，拿到国家编码，拿到区号
        userInfoDO.setWorkStatus(WorkStatusEnum.ON_JOB.getCode());
        userInfoDO.setCountryCode(driverRegisterParam.getCountryCode());
        userInfoDO.setBirthday(driverRegisterParam.getBirthday());

        //设置职能信息
        //改造后 这里不在设置默认值，界面可录入，但是需要兼容 hrms 新增司机
        if (StringUtils.isBlank(driverRegisterParam.getFunctional())) {
            userInfoDO.setFunctional(BusinessConstant.DEFAULT_FUNCTION);
        }

        userInfoDO.setDataSource(OperationSceneEnum.TMS_DRIVER_MANAGE_NEW.getCode());
        userInfoDO.setIsFinish(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(userInfoDO);
        return userInfoDO;
    }

    protected void getUserInfo(OutSourceDriverRegisterParam driverRegisterParam, HrmsUserInfoDO userInfoDO) {
//        if (StringUtils.isBlank(driverRegisterParam.getDaType())) {
            //为null 说明是 hrms-新增司机场景

            // 网点查询
            HrmsEntDeptDO ocDept = deptManage.getDeptByOcCode(driverRegisterParam.getOcCode());
            userInfoDO.setDeptId(ocDept.getId());
            userInfoDO.setOcId(ocDept.getId());
            userInfoDO.setOcCode(ocDept.getOcCode());
            userInfoDO.setOriginCountry(ocDept.getCountry());
            userInfoDO.setLocationCountry(ocDept.getCountry());
            userInfoDO.setLocationProvince(ocDept.getProvince());
            userInfoDO.setLocationCity(ocDept.getCity());
//        }
//        else {
//            //tms 非干线司机 场景
//            // 没有部门字段，  ocId  传入的 本来就是 deptId
//        }


        //设置岗位 司机默认放在 1295岗位
        if (userInfoDO.getPostId() == null) {
            userInfoDO.setPostId(hrmsProperties.getEntry().getPostId());
        }

        userInfoDO.setDataSource(OperationSceneEnum.TMS_DRIVER_MANAGE_NEW.getCode());
        userInfoDO.setIsDtl(BusinessConstant.N);

        if (StringUtils.isNotBlank(driverRegisterParam.getVehicleModel())) {
            userInfoDO.setVehicleModel(driverRegisterParam.getVehicleModel());
        }

        //供应商信息
        if (EmploymentTypeEnum.TYPE_OF_REQUIRED_VENDOR.contains(driverRegisterParam.getEmployeeType())
                && Objects.nonNull(driverRegisterParam.getVendorCode())) {
            VendorInfoApiDTO vendor = vendorService.getByVendorCode(driverRegisterParam.getVendorCode());
            if (Objects.isNull(vendor)) {
                throw BusinessException.get(HrmsErrorCodeEnums.VENDOR_NOT_EXITS.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.VENDOR_NOT_EXITS.getDesc()));
            }
            userInfoDO.setVendorId(vendor.getVendorId());
            userInfoDO.setVendorOrgId(vendor.getOrgId());
            userInfoDO.setVendorCode(vendor.getVendorCode());
            userInfoDO.setVendorName(vendor.getVendorShortName());
            if (StringUtils.equalsIgnoreCase(vendor.getDriverManage(), "vendor")) {
                userInfoDO.setLeaderId(null);
                userInfoDO.setLeaderName("vendor");
                return;
            }
        }

        //dtl
        if (StringUtils.isNotBlank(driverRegisterParam.getLeaderUserCode())) {
            HrmsUserInfoDO leader = userManage.getUserByUserCode(driverRegisterParam.getLeaderUserCode());
            userInfoDO.setLeaderId(leader.getId());
            userInfoDO.setLeaderName(leader.getUserName());
        }
    }


    /**
     * 获取枚举类
     *
     * @return
     */
    protected abstract String getDriverHandlerType();


}
