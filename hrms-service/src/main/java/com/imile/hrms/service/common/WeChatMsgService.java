package com.imile.hrms.service.common;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WeChatMsgService {

    void weChatMsgSend(String url, String topic, List<String> titleList, List<List<String>> messageList, boolean newLine, String country, Date date, boolean isWeek);

    void sendWeChatMsgText(String url, String topic, Map<String, String> map);

    /**
     * 上传文件并推送到企业微信群
     */
    void sendFileToWeChatGroup(File file, String groupKey);
}
