package com.imile.hrms.service.handler.strategy.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.hrms.common.enums.attendance.AttendanceTypeEnum;
import com.imile.hrms.common.enums.user.TransferTypeEnum;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigDO;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserTransferRecordDO;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDaoFacade;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.PunchConfigDaoFacade;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.user.HrmsUserInfoManage;
import com.imile.hrms.mq.basic.ProducerBasicService;
import com.imile.hrms.service.attendance.dto.AttendanceAndPunchHandlerDTO;
import com.imile.hrms.service.handler.strategy.AbstractTransferHandler;
import com.imile.hrms.service.newAttendance.calendar.adapter.CalendarServiceAdapter;
import com.imile.hrms.service.salary.HrmsSalaryBaseService;
import com.imile.hrms.service.salary.dto.SalaryCountryMessageNoticeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/3/7
 */
@Component
@Slf4j
public class DeptTransferHandler extends AbstractTransferHandler {


    @Autowired
    private ProducerBasicService producerBasicService;
    @Resource
    private CalendarConfigDaoFacade calendarConfigDaoFacade;
//    @Autowired
//    private HrmsAttendancePunchConfigDao punchConfigDao;
    @Resource
    private CalendarServiceAdapter calendarServiceAdapter;
    @Autowired
    private HrmsSalaryBaseService hrmsSalaryBaseService;
    @Autowired
    private HrmsDeptManage hrmsDeptManage;
    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;

    @Resource
    private PunchConfigDaoFacade punchConfigDaoFacade;

    @Value("${rocket.mq.hr.topic}")
    private String hrProducerTopic;


    /**
     * 对于部门间的调动，只需要修改员工信息表中的部门信息
     *
     * @param userInfoDO
     * @param hrmsUserEntryRecordDO
     * @param model
     */
    @Override
    public void handlerTransfer(HrmsUserInfoDO userInfoDO, HrmsUserEntryRecordDO hrmsUserEntryRecordDO, HrmsUserTransferRecordDO model) {
        //具体调动及数据落地
        doTransfer(userInfoDO, hrmsUserEntryRecordDO, model);
        //考勤日历，打卡规则更新
        changeAttendanceConfigAndPunchConfig(userInfoDO, model);
        //常规国是否发生变动，需要薪资消息提示
        salaryMessageNotice(model);
    }

    @Resource
    private HrmsUserInfoManage hrmsUserInfoManage;

    @Override
    public String TransferType() {
        return TransferTypeEnum.DEPT.name();
    }


    private void salaryMessageNotice(HrmsUserTransferRecordDO model) {
        List<Long> ocIcList = new ArrayList<>();
        if (model.getAfterOcId() != null) {
            ocIcList.add(model.getAfterOcId());
        }
        if (model.getBeforeOcId() != null) {
            ocIcList.add(model.getBeforeOcId());
        }
        List<HrmsEntDeptDO> stationDOList = hrmsDeptManage.selectDeptByIds(ocIcList);
        if (CollectionUtils.isEmpty(stationDOList)) {
            return;
        }
        Map<Long, List<HrmsEntDeptDO>> stationMaps = stationDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));
        List<HrmsEntDeptDO> beforeDeptList = stationMaps.get(model.getBeforeOcId());
        if (CollectionUtils.isEmpty(beforeDeptList)) {
            return;
        }
        List<HrmsEntDeptDO> afterDeptList = stationMaps.get(model.getAfterOcId());
        if (CollectionUtils.isEmpty(afterDeptList)) {
            return;
        }
        SalaryCountryMessageNoticeDTO noticeDTO = new SalaryCountryMessageNoticeDTO();
        noticeDTO.setBeforeCountry(beforeDeptList.get(0).getCountry());
        noticeDTO.setAfterCountry(afterDeptList.get(0).getCountry());
        noticeDTO.setUserId(model.getUserId());
        hrmsSalaryBaseService.salaryCountryMessageNoticeHandler(Arrays.asList(noticeDTO));
    }

    private void changeAttendanceConfigAndPunchConfig(HrmsUserInfoDO userInfoDO, HrmsUserTransferRecordDO model) {
        log.info("userAttendanceAndPunchHandler | userInfoDO:{} | model:{}", JSON.toJSON(userInfoDO), JSON.toJSON(model));
        //不能用userInfoDO中的部门国家，在一个事物中，没有更新
        List<Long> ocIcList = new ArrayList<>();
        if (model.getAfterOcId() != null) {
            ocIcList.add(model.getAfterOcId());
        }
        if (model.getBeforeOcId() != null) {
            ocIcList.add(model.getBeforeOcId());
        }
        List<HrmsEntDeptDO> stationDOList = hrmsDeptManage.selectDeptByIds(ocIcList);
        if (CollectionUtils.isEmpty(stationDOList)) {
            return;
        }

        // 查询新的部门信息
        HrmsEntDeptDO newDeptInfo = hrmsEntDeptDao.getById(model.getAfterDeptId());
        if (ObjectUtil.isNull(newDeptInfo)) {
            log.info("userAttendanceAndPunchHandler | newDeptInfo is null | newDeptInfo:{}", userInfoDO.getDeptId());
            return;
        }
        Map<Long, List<HrmsEntDeptDO>> stationMaps = stationDOList.stream().collect(Collectors.groupingBy(HrmsEntDeptDO::getId));

        List<AttendanceAndPunchHandlerDTO> attendanceAndPunchHandlerDTOList = new ArrayList<>();
        AttendanceAndPunchHandlerDTO attendanceAndPunchHandlerDTO = new AttendanceAndPunchHandlerDTO();
        attendanceAndPunchHandlerDTO.setUserId(userInfoDO.getId());
        attendanceAndPunchHandlerDTO.setUserCode(userInfoDO.getUserCode());
        attendanceAndPunchHandlerDTO.setNewDeptId(model.getAfterDeptId());
        if (CollectionUtils.isNotEmpty(stationMaps.get(model.getAfterOcId()))) {
            //attendanceAndPunchHandlerDTO.setNewCountry(stationMaps.get(model.getAfterOcId()).get(0).getCountry());
            // 部门变动不会修改常驻国，所以这边常驻国家设置为一样的
            attendanceAndPunchHandlerDTO.setNewCountry(userInfoDO.getLocationCountry());
        }

        attendanceAndPunchHandlerDTO.setOldDeptId(model.getBeforeDeptId());
        if (CollectionUtils.isNotEmpty(stationMaps.get(model.getBeforeOcId()))) {
            //attendanceAndPunchHandlerDTO.setOldCountry(stationMaps.get(model.getBeforeOcId()).get(0).getCountry());
            // 部门变动不会修改常驻国，所以这边常驻国家设置为一样的
            attendanceAndPunchHandlerDTO.setOldCountry(userInfoDO.getLocationCountry());
        }

        // 获取新部门的地理国
        String country = newDeptInfo.getCountry();
        log.info("userAttendanceAndPunchHandler | beforeDeptId:{} | afterDeptId:{} | afterDeptCountry：{}", model.getBeforeDeptId(), model.getAfterDeptId(), country);
        //用户没有变更国家和部门(这个不需要判断，因为部门切换不会影响常驻国)，只需要判断部门就好了
        if (ObjectUtil.equal(model.getBeforeDeptId(), model.getAfterDeptId())) {
            log.info("userAttendanceAndPunchHandler : 调动：不符合条件，不需要处理");
            return;
        }
        attendanceAndPunchHandlerDTOList.add(attendanceAndPunchHandlerDTO);
//        hrmsAttendanceConfigService.userAttendanceAndPunchHandler(attendanceAndPunchHandlerDTOList);
        calendarServiceAdapter.userCalendarAndPunchHandler(attendanceAndPunchHandlerDTOList);
    }

    /**
     * 匹配对应的考勤日历
     *
     * @param originCountry
     * @param deptId
     * @return
     */
    private HrmsAttendanceConfigDO matchAttendanceConfig(String originCountry, Long deptId) {
        //先根据用户所在部门查找对应的考勤日历和打卡规则
//        List<HrmsAttendanceConfigDO> attendanceConfigList = attendanceConfigDao.listByCountry(originCountry);
        List<HrmsAttendanceConfigDO> attendanceConfigList = calendarConfigDaoFacade.getCalendarConfigAdapter().listByCountry(originCountry);
        if (CollectionUtils.isEmpty(attendanceConfigList)) {
            return null;
        }
        List<HrmsAttendanceConfigDO> defaultConfigList = attendanceConfigList.stream().filter(item -> item.getType().equals(AttendanceTypeEnum.DEFAULT.name())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultConfigList)) {
            return null;
        }
        HrmsAttendanceConfigDO defaultConfig = defaultConfigList.get(0);

        List<HrmsAttendanceConfigDO> customList = attendanceConfigList.stream().filter(o -> StringUtils.equalsIgnoreCase("CUSTOM", o.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customList)) {
            return defaultConfig;
        }
        for (HrmsAttendanceConfigDO item : customList) {
            if (StringUtils.isEmpty(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(item.getDeptIds().split(","), Long.class));
            if (deptIds.contains(deptId)) {
                return item;
            }
        }
        return defaultConfig;
    }

    /**
     * 匹配对应的打卡规则
     *
     * @param originCountry
     * @param deptId
     * @return
     */
    private HrmsAttendancePunchConfigDO matchPunchConfig(String originCountry, Long deptId) {
        //先根据用户所在部门查找对应的考勤日历和打卡规则
//        List<HrmsAttendancePunchConfigDO> punchConfigList = punchConfigDao.selectConfigByCompanyAndType(originCountry, null);
        List<HrmsAttendancePunchConfigDO> punchConfigList = punchConfigDaoFacade.getConfigAdapter().selectConfigByCompanyAndType(originCountry, null);
        if (CollectionUtils.isEmpty(punchConfigList)) {
            return null;
        }
        List<HrmsAttendancePunchConfigDO> defaultAttendancePunchConfigDOList = punchConfigList.stream().filter(o -> o.getIsDefault() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultAttendancePunchConfigDOList)) {
            return null;
        }
        HrmsAttendancePunchConfigDO defaultConfig = defaultAttendancePunchConfigDOList.get(0);

        List<HrmsAttendancePunchConfigDO> customList = punchConfigList.stream().filter(o -> o.getIsDefault() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customList)) {
            return defaultConfig;
        }
        for (HrmsAttendancePunchConfigDO item : customList) {
            if (StringUtils.isEmpty(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.asList((Long[]) ConvertUtils.convert(item.getDeptIds().split(","), Long.class));
            if (deptIds.contains(deptId)) {
                return item;
            }
        }
        return defaultConfig;
    }
}
