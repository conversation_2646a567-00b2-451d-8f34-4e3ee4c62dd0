package com.imile.hrms.service.salary;

import com.imile.hrms.service.salary.param.HrmsSalaryHomePageReminderParam;
import com.imile.hrms.service.salary.vo.HrmsSalaryCalendarReminderVO;
import com.imile.hrms.service.salary.vo.HrmsSalaryHomeMessageNoticeVO;
import com.imile.hrms.service.salary.vo.HrmsSalaryToDoReminderVO;

import java.util.List;

/**
 * 薪酬系统首页
 *
 * <AUTHOR>
 */
public interface HrmsSalaryHomePageService {

    /**
     * 日历icon提醒
     *
     * @param param
     * @return
     */
    List<HrmsSalaryCalendarReminderVO> calendarReminder(HrmsSalaryHomePageReminderParam param);

    /**
     * 待办提醒
     *
     * @param param
     * @return
     */
    List<HrmsSalaryToDoReminderVO> toDoReminder(HrmsSalaryHomePageReminderParam param);

    /**
     * 单据状态变更提醒
     *
     * @param country
     * @return
     */
    List<HrmsSalaryHomeMessageNoticeVO> messageNotice(String country);

}
