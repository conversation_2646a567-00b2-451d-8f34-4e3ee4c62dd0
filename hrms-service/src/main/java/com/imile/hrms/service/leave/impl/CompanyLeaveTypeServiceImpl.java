package com.imile.hrms.service.leave.impl;

import com.imile.hrms.dao.leave.CompanyLeaveTypeDao;
import com.imile.hrms.dao.leave.model.CompanyLeaveTypeDO;
import com.imile.hrms.dao.leave.query.CompanyLeaveTypeQuery;
import com.imile.hrms.service.attendance.dto.CompanyLeaveTypeDTO;
import com.imile.hrms.service.attendance.param.CompanyLeaveTypeParam;
import com.imile.hrms.service.leave.CompanyLeaveTypeService;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * {@code @author:} han.wang
 * {@code @className:} CompanyLeaveTypeServiceImpl
 * {@code @since:} 2025-2-17 16:42
 * {@code @description:}
 */
@Service
@Slf4j
public class CompanyLeaveTypeServiceImpl implements CompanyLeaveTypeService {

    @Autowired
    private CompanyLeaveTypeDao companyLeaveTypeDao;

    @Override
    public List<CompanyLeaveTypeDTO> queryByCondition(CompanyLeaveTypeParam param) {
        CompanyLeaveTypeQuery query = BeanUtils.convert(param, CompanyLeaveTypeQuery.class);
        List<CompanyLeaveTypeDO> leaveTypeDOList = companyLeaveTypeDao.queryByCondition(query);
        return BeanUtils.convert(CompanyLeaveTypeDTO.class, leaveTypeDOList);
    }
}
