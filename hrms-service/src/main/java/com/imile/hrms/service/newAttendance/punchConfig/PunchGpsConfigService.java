package com.imile.hrms.service.newAttendance.punchConfig;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.util.IpepUtils;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchGpsConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchGpsConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchGpsConfigQuery;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.service.base.BaseService;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchGpsConfigAddCommand;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchGpsConfigDeleteCommand;
import com.imile.hrms.service.newAttendance.punchConfig.command.PunchGpsConfigUpdateCommand;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchGpsConfigDTO;
import com.imile.hrms.service.newAttendance.punchConfig.dto.PunchGpsConfigImportDTO;
import com.imile.hrms.service.newAttendance.punchConfig.factory.PunchGpsConfigFactory;
import com.imile.hrms.service.newAttendance.punchConfig.mapstruct.PunchGpsConfigMapstruct;
import com.imile.util.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/2/19
 * @Description
 */
@Service
public class PunchGpsConfigService extends BaseService {

    @Autowired
    private PunchGpsConfigDao punchGpsConfigDao;
    @Autowired
    private PunchGpsConfigFactory punchGpsConfigFactory;
    @Autowired
    private CountryService countryService;


    /**
     * GPS打卡配置列表
     */
    public PaginationResult<PunchGpsConfigDTO> list(PunchGpsConfigQuery query) {
        PageInfo<PunchGpsConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> punchGpsConfigDao.list(query));

        List<PunchGpsConfigDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<PunchGpsConfigDTO> gpsConfigDTOS = PunchGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(list);
        return getPageResult(gpsConfigDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    /**
     * 新增GPS配置
     */
    public void add(PunchGpsConfigAddCommand addCommand) {
        punchGpsConfigFactory.add(addCommand);
    }

    /**
     * 更新GPS配置
     */
    public PunchGpsConfigDTO update(PunchGpsConfigUpdateCommand updateCommand) {
        return punchGpsConfigFactory.update(updateCommand);
    }


    /**
     * 删除GPS配置
     */
    public PunchGpsConfigDTO delete(PunchGpsConfigDeleteCommand deleteCommand) {
        return punchGpsConfigFactory.delete(deleteCommand);
    }

    /**
     * 导入GPS配置
     */
    public List<PunchGpsConfigImportDTO> importGpsConfig(List<PunchGpsConfigImportDTO> param) {
        List<PunchGpsConfigImportDTO> failImportList = new ArrayList<>();
        List<PunchGpsConfigImportDTO> params = checkImportParam(param, failImportList);
        for (PunchGpsConfigImportDTO item : params) {
            try {
                PunchGpsConfigDO gpsConfigDO = BeanUtils.convert(item, PunchGpsConfigDO.class);
                BaseDOUtil.fillDOInsert(gpsConfigDO);
                punchGpsConfigDao.save(gpsConfigDO);
            } catch (Exception e) {
                IpepUtils.putFail(item, e.getMessage());
                failImportList.add(item);
            }
        }
        return failImportList;
    }

    /**
     * gps批量查询
     */
    public List<PunchGpsConfigDTO> selectList(PunchGpsConfigQuery query) {
        List<PunchGpsConfigDO> list = punchGpsConfigDao.list(query);
        return PunchGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(list);
    }


    private List<PunchGpsConfigImportDTO> checkImportParam(List<PunchGpsConfigImportDTO> params,
                                                           List<PunchGpsConfigImportDTO> failImportList) {
        List<PunchGpsConfigImportDTO> normal = new ArrayList<>();
        // 获取系统国家
        List<String> importCountryNames = params.stream()
                .map(PunchGpsConfigImportDTO::getCountry)
                .collect(Collectors.toList());
        List<String> countryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(importCountryNames)) {
            CountryApiQuery countryQuery = new CountryApiQuery();
            countryQuery.setCountryNames(importCountryNames);
            countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
            List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
            if (CollectionUtils.isNotEmpty(countryConfigList)) {
                countryList = countryConfigList.stream()
                        .map(CountryConfigDTO::getCountryName)
                        .collect(Collectors.toList());
            }
        }
        for (PunchGpsConfigImportDTO param : params) {
            if (StringUtils.isBlank(param.getCountry()) || StringUtils.isBlank(param.getLocationCity())
                    || StringUtils.isBlank(param.getAddressName()) || StringUtils.isBlank(param.getAddressDetail())
                    || Objects.isNull(param.getEffectiveRange()) || Objects.isNull(param.getLongitude()) || Objects.isNull(param.getLatitude())) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ?
                        "必填项不能为空" :
                        "The required fields cannot be blank");
                failImportList.add(param);
                continue;
            }

            if (!countryList.contains(param.getCountry())) {
                IpepUtils.putFail(param, HrmsErrorCodeEnums.COUNTRY_CODE_NOT_EXISTS.getMessage());
                failImportList.add(param);
                continue;
            }

//            List<BusCityResultDTO> cityListByCountry = zoneService.getCityListByCountry(param.getCountry());
//            if (CollectionUtils.isEmpty(cityListByCountry)) {
//                IpepUtils.putFail(param, HrmsErrorCodeEnums.CITY_NOT_EXIST_ERROR.getMessage());
//                failImportList.add(param);
//                continue;
//            }
//
//            List<String> cityNameList = cityListByCountry.stream().map(BusCityResultDTO::getCityName).collect(Collectors.toList());
//            if (!cityNameList.contains(param.getLocationCity())) {
//                IpepUtils.putFail(param, HrmsErrorCodeEnums.CITY_NOT_EXIST_ERROR.getMessage());
//                failImportList.add(param);
//                continue;
//            }

            PunchGpsConfigQuery query = PunchGpsConfigQuery.builder()
                    .country(param.getCountry())
                    .longitude(param.getLongitude())
                    .latitude(param.getLatitude())
                    .build();
            List<PunchGpsConfigDO> list = punchGpsConfigDao.list(query);
            if (CollectionUtils.isNotEmpty(list)) {
                IpepUtils.putFail(param, HrmsErrorCodeEnums.GPS_ADDRESS_DUPLICATE_ERROR.getMessage());
                failImportList.add(param);
                continue;
            }
            normal.add(param);
        }
        return normal;
    }
}
