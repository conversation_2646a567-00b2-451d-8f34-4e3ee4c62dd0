package com.imile.hrms.service.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/18 15:43
 * @version: 1.0
 */
@Data
public class SalaryUserInfoExportParam {

    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String salaryType;

    /**
     * 结算时间-年月(202308)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long settlementDate;
}
