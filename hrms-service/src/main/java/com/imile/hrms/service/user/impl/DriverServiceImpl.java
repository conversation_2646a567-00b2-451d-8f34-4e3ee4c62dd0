package com.imile.hrms.service.user.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Maps;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hrms.api.user.enums.DriverDynamicFieldEnum;
import com.imile.hrms.api.user.param.DriverFilterParam;
import com.imile.hrms.api.user.result.DriverDynamicInfoDTO;
import com.imile.hrms.api.user.result.UserCertificateDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.CommonUtils;
import com.imile.hrms.common.util.DateFormatUtils;
import com.imile.hrms.common.validator.ParamValidator;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.primary.entity.condition.DriverConditionBuilder;
import com.imile.hrms.dao.primary.entity.condition.UserConditionBuilder;
import com.imile.hrms.dao.user.dao.UserDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.hrms.manage.user.UserManage;
import com.imile.hrms.service.primary.UserCertificateService;
import com.imile.hrms.service.user.DriverService;
import com.imile.hrms.service.user.context.UserDynamicQueryContext;
import com.imile.hrms.service.user.helper.ExternalDependencyHelper;
import com.imile.hrms.service.user.result.UserCertificateBO;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Service
public class DriverServiceImpl implements DriverService {

    @Resource
    private UserDao userDao;
    @Resource
    private UserManage userManage;
    @Resource
    private PostManage postManage;
    @Resource
    private UserCertificateService userCertificateService;
    @Resource
    private ExternalDependencyHelper externalDependencyHelper;

    @Override
    public PaginationResult<Long> getDriverIdPageList(DriverFilterParam param) {
        ParamValidator.checkPageSize(param.getShowCount());
        PageInfo<Long> pageInfo = PageMethod.startPage(param.getCurrentPage(), param.getShowCount())
                .doSelectPageInfo(() -> userDao.selectDriverIdByCondition(DriverConditionBuilder.builder()
                        .driverCode(param.getDriverCode())
                        .driverName(param.getDriverName())
                        .vendorCode(param.getVendorCode())
                        .ocCode(param.getOcCode())
                        .ocCodeList(param.getOcCodeList())
                        .functional(param.getFunctional())
                        .accountStatus(param.getAccountStatus())
                        .postCode(param.getPostCode())
                        .employeeTypeList(param.getEmployeeTypeList())
                        .locationCountry(param.getLocationCountry())
                        .auditStatus(param.getAuditStatus())
                        .build()));
        param.setTotalResult((int) pageInfo.getTotal());
        param.setTotalPage(pageInfo.getPages());
        if (pageInfo.getList().isEmpty()) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        return PaginationResult.get(pageInfo.getList(), param);
    }

    @Override
    public List<DriverDynamicInfoDTO> listDriverDynamicInfo(List<Long> idList, List<DriverDynamicFieldEnum> dynamicFieldList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        ParamValidator.checkSize(idList);
        List<HrmsUserInfoDO> userList = userManage.getUserByCondition(UserConditionBuilder.builder()
                .idList(idList)
                .isDriver(WhetherEnum.YES.getKey())
                .build());
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }
        UserDynamicQueryContext context = new UserDynamicQueryContext();
        this.buildUserDynamicQueryContext(context, userList, dynamicFieldList);
        return userList.stream()
                .map(user -> DriverDynamicInfoDTO.builder()
                        .id(user.getId())
                        .driverCode(user.getUserCode())
                        .driverName(user.getUserName())
                        .employeeType(user.getEmployeeType())
                        .accountStatus(user.getStatus())
                        .dynamicFieldMap(this.buildUserDynamicFieldMap(user, dynamicFieldList, context))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<UserCertificateDTO> getDriverCertificateList(Long id) {
        List<UserCertificateBO> userCertificateList = userCertificateService.getUserCertificateList(id);
        return BeanUtils.convert(UserCertificateDTO.class, userCertificateList);
    }

    private void buildUserDynamicQueryContext(UserDynamicQueryContext context, List<HrmsUserInfoDO> userList,
                                              List<DriverDynamicFieldEnum> dynamicFieldList) {
        if (dynamicFieldList.contains(DriverDynamicFieldEnum.LEADER_CODE)
                || dynamicFieldList.contains(DriverDynamicFieldEnum.LEADER_NAME)) {
            List<Long> leaderIdList = userList.stream()
                    .filter(user -> Objects.nonNull(user.getLeaderId())
                            && !BusinessConstant.DEFAULT_ID.equals(user.getLeaderId()))
                    .map(HrmsUserInfoDO::getLeaderId)
                    .distinct()
                    .collect(Collectors.toList());
            context.setLeaderMap(userManage.getUserMapById(leaderIdList));
        }

        if (dynamicFieldList.contains(DriverDynamicFieldEnum.VENDOR_CODE)
                || dynamicFieldList.contains(DriverDynamicFieldEnum.VENDOR_NAME)) {
            List<String> vendorCodeList = userList.stream()
                    .map(HrmsUserInfoDO::getVendorCode)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            context.setVendorMap(externalDependencyHelper.getVendorMap(vendorCodeList));
        }

        if (dynamicFieldList.contains(DriverDynamicFieldEnum.OC_CODE)
                || dynamicFieldList.contains(DriverDynamicFieldEnum.OC_NAME)) {
            List<String> ocCodeList = userList.stream()
                    .map(HrmsUserInfoDO::getOcCode)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            context.setOcMap(externalDependencyHelper.getOcMap(ocCodeList));
        }

        if (dynamicFieldList.contains(DriverDynamicFieldEnum.POST_CODE)
                || dynamicFieldList.contains(DriverDynamicFieldEnum.POST_NAME)) {
            List<Long> postIdList = userList.stream()
                    .filter(user -> Objects.nonNull(user.getPostId())
                            && !BusinessConstant.DEFAULT_ID.equals(user.getPostId()))
                    .map(HrmsUserInfoDO::getPostId)
                    .distinct()
                    .collect(Collectors.toList());
            context.setPostMap(postManage.getPostMap(postIdList));
        }
    }

    private Map<String, String> buildUserDynamicFieldMap(HrmsUserInfoDO user, List<DriverDynamicFieldEnum> dynamicFieldList, UserDynamicQueryContext context) {
        if (CollectionUtils.isEmpty(dynamicFieldList)) {
            return Collections.emptyMap();
        }
        Map<String, String> result = Maps.newHashMap();
        dynamicFieldList.forEach(field -> {
            if (result.containsKey(field.getKey())) {
                return;
            }
            this.fillDynamicFieldValue(user, field, context, result);
        });
        List<String> keyList = dynamicFieldList.stream()
                .map(DriverDynamicFieldEnum::getKey)
                .collect(Collectors.toList());
        return result.entrySet().stream()
                // 返回传参中指定的字段
                .filter(s -> keyList.contains(s.getKey()))
                // 过滤value为null的字段 否则toMap时会报空指针
                .filter(s -> Objects.nonNull(s.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private void fillDynamicFieldValue(HrmsUserInfoDO user, DriverDynamicFieldEnum field, UserDynamicQueryContext context, Map<String, String> result) {
        switch (field) {
            case EMAIL:
                result.put(DriverDynamicFieldEnum.EMAIL.getKey(), CommonUtils.getDefaultStringValue(user.getEmail()));
                break;
            case PHONE:
                result.put(DriverDynamicFieldEnum.PHONE.getKey(), CommonUtils.getDefaultStringValue(user.getPhone()));
                break;
            case FUNCTIONAL:
                result.put(DriverDynamicFieldEnum.FUNCTIONAL.getKey(), CommonUtils.getDefaultStringValue(user.getFunctional()));
                break;
            case VEHICLE_MODEL:
                result.put(DriverDynamicFieldEnum.VEHICLE_MODEL.getKey(), CommonUtils.getDefaultStringValue(user.getVehicleModel()));
                break;
            case LEADER_CODE:
            case LEADER_NAME:
                if (Objects.isNull(user.getLeaderId()) || BusinessConstant.DEFAULT_ID.equals(user.getLeaderId())) {
                    return;
                }
                HrmsUserInfoDO leader = context.getLeaderMap().get(user.getLeaderId());
                if (Objects.isNull(leader)) {
                    return;
                }
                result.put(DriverDynamicFieldEnum.LEADER_CODE.getKey(), leader.getUserCode());
                result.put(DriverDynamicFieldEnum.LEADER_NAME.getKey(),
                        BusinessFieldUtils.getUnifiedUserName(leader.getUserName(), leader.getUserNameEn()));
                break;
            case VENDOR_CODE:
            case VENDOR_NAME:
                if (StringUtils.isBlank(user.getVendorCode())) {
                    return;
                }
                VendorInfoApiDTO vendor = context.getVendorMap().get(user.getVendorCode());
                if (Objects.isNull(vendor)) {
                    return;
                }
                result.put(DriverDynamicFieldEnum.VENDOR_CODE.getKey(), vendor.getVendorCode());
                result.put(DriverDynamicFieldEnum.VENDOR_NAME.getKey(), vendor.getVendorName());
                break;
            case OC_CODE:
            case OC_NAME:
                if (StringUtils.isBlank(user.getOcCode())) {
                    return;
                }
                EntOcApiDTO oc = context.getOcMap().get(user.getOcCode());
                if (Objects.isNull(oc)) {
                    return;
                }
                result.put(DriverDynamicFieldEnum.OC_CODE.getKey(), oc.getOcCode());
                result.put(DriverDynamicFieldEnum.OC_NAME.getKey(), oc.getOcName());
                break;
            case POST_CODE:
            case POST_NAME:
                if (Objects.isNull(user.getPostId()) || BusinessConstant.DEFAULT_ID.equals(user.getPostId())) {
                    return;
                }
                PostDO post = context.getPostMap().get(user.getPostId());
                if (Objects.isNull(post)) {
                    return;
                }
                result.put(DriverDynamicFieldEnum.POST_CODE.getKey(), post.getId().toString());
                result.put(DriverDynamicFieldEnum.POST_NAME.getKey(), post.getPostNameEn());
                break;
            case LOCATION_COUNTRY:
                result.put(DriverDynamicFieldEnum.LOCATION_COUNTRY.getKey(), user.getLocationCountry());
                break;
            case LOCATION_PROVINCE:
                result.put(DriverDynamicFieldEnum.LOCATION_PROVINCE.getKey(), user.getLocationProvince());
                break;
            case LOCATION_CITY:
                result.put(DriverDynamicFieldEnum.LOCATION_CITY.getKey(), user.getLocationCity());
                break;
            case CREATE_TIME:
                result.put(DriverDynamicFieldEnum.CREATE_TIME.getKey(), DateFormatUtils.format(user.getCreateDate()));
                break;
            case CREATE_USER_NAME:
                result.put(DriverDynamicFieldEnum.CREATE_USER_NAME.getKey(), user.getCreateUserName());
                break;
            case UPDATE_TIME:
                result.put(DriverDynamicFieldEnum.UPDATE_TIME.getKey(), DateFormatUtils.format(user.getLastUpdDate()));
                break;
            case UPDATE_USER_NAME:
                result.put(DriverDynamicFieldEnum.UPDATE_USER_NAME.getKey(), user.getLastUpdUserName());
                break;
            default:
        }
    }
}
