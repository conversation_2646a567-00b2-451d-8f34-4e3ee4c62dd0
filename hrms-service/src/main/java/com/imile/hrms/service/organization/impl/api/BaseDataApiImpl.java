package com.imile.hrms.service.organization.impl.api;

import com.google.common.collect.Lists;
import com.imile.hrms.api.base.api.BaseDataApi;
import com.imile.hrms.api.base.param.CountryConditionParam;
import com.imile.hrms.api.base.param.BizAreaConditionParam;
import com.imile.hrms.api.base.param.CountryConditionParam;
import com.imile.hrms.api.base.param.ProjectConditionParam;
import com.imile.hrms.api.base.result.CountryDTO;
import com.imile.hrms.api.base.result.BizAreaDTO;
import com.imile.hrms.api.base.result.CountryDTO;
import com.imile.hrms.api.base.result.GradeDTO;
import com.imile.hrms.api.base.result.ProjectDTO;
import com.imile.hrms.service.organization.HrmsCountryConfigService;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.service.business.area.HrmsBizeAreaBaseConfigService;
import com.imile.hrms.service.business.area.vo.HrmsBizeAreaBaseConfigListVO;
import com.imile.hrms.service.organization.HrmsCountryConfigService;
import com.imile.hrms.service.organization.HrmsEntGradeService;
import com.imile.hrms.service.organization.HrmsEntProjectService;
import com.imile.rpc.common.RpcResult;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Service(version = "1.0.0")
public class BaseDataApiImpl implements BaseDataApi {

    @Resource
    private HrmsEntProjectService projectService;
    @Resource
    private HrmsEntGradeService gradeService;

    @Resource
    private HrmsBizeAreaBaseConfigService bizAreaBaseConfigService;

    @Resource
    private HrmsCountryConfigService countryConfigService;

    @Override
    public RpcResult<List<ProjectDTO>> listProjectByCondition(ProjectConditionParam param) {
        return RpcResult.ok(projectService.listProjectByCondition(param));
    }

    @Override
    public RpcResult<List<GradeDTO>> getGradeActiveList() {
        return RpcResult.ok(gradeService.getGradeActiveList());
    }

    @Override
    public RpcResult<List<BizAreaDTO>> getBizAreaList(BizAreaConditionParam param) {
        if (param == null || CollectionUtils.isEmpty(param.getBizAreaIdList())) {
            param = new BizAreaConditionParam();
            param.setBizAreaIdList(Lists.newArrayList());
        }
        List<BizAreaDTO> result = Lists.newArrayList();
        List<HrmsBizeAreaBaseConfigListVO> list = bizAreaBaseConfigService.listAllByIdList(param.getBizAreaIdList());
        if (CollectionUtils.isEmpty(list)) {
            return RpcResult.ok(result);
        }
        for (HrmsBizeAreaBaseConfigListVO bizArea : list) {
            BizAreaDTO dto = new BizAreaDTO();
            dto.setBizAreaId(bizArea.getId());
            dto.setBizAreaNameCn(bizArea.getBusinessAreaNameCn());
            dto.setBizAreaNameEn(bizArea.getBusinessAreaNameEn());
            result.add(dto);
        }
        return RpcResult.ok(result);
    }

    @Override
    public RpcResult<List<CountryDTO>> getCountryList(CountryConditionParam param) {
        return RpcResult.ok(countryConfigService.getCountryList(param));
    }
}
