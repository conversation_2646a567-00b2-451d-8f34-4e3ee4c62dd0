package com.imile.hrms.manage.newAttendance.punchConfig.adapter.convert;

import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchWifiConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceWifiConfigDO;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.mapstruct.PunchWifiConfigMapstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/2/19
 * @Description
 */
@Component
public class PunchWifiConfigConverter implements DataConverter<PunchWifiConfigDO, HrmsAttendanceWifiConfigDO> {

    @Override
    public HrmsAttendanceWifiConfigDO convertFromNew(PunchWifiConfigDO newObj) {
        return PunchWifiConfigMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public PunchWifiConfigDO convertFromOld(HrmsAttendanceWifiConfigDO oldObj) {
        return PunchWifiConfigMapstruct.INSTANCE.mapToNew(oldObj);
    }

    @Override
    public Class<PunchWifiConfigDO> getNewType() {
        return PunchWifiConfigDO.class;
    }

    @Override
    public Class<HrmsAttendanceWifiConfigDO> getOldType() {
        return HrmsAttendanceWifiConfigDO.class;
    }
}
