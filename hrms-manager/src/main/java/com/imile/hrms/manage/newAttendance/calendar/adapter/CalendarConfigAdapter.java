package com.imile.hrms.manage.newAttendance.calendar.adapter;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.hrms.common.adapter.AbstractPairAdapter;
import com.imile.hrms.common.adapter.DaoAdapter;
import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.common.enums.attendance.AttendanceTypeEnum;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigQuery;
import com.imile.hrms.dao.newAttendance.calendar.dao.CalendarConfigDao;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDO;
import com.imile.hrms.manage.newAttendance.calendar.adapter.mapstruct.CalendarConfigMapstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/21
 * @Description
 */
@Component
public class CalendarConfigAdapter extends AbstractPairAdapter<CalendarConfigDO, HrmsAttendanceConfigDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    @Resource
    private HrmsAttendanceConfigDao hrmsAttendanceConfigDao;
    @Resource
    private CalendarConfigDao calendarConfigDao;

    public CalendarConfigAdapter(List<DataConverter<CalendarConfigDO, HrmsAttendanceConfigDO>> dataConverters) {
        super(dataConverters);
    }


    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnableCalendar();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getCalendarDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    // 具体查询方法
    public HrmsAttendanceConfigDO getById(Long id) {
        return readWrapper(
                () -> calendarConfigDao.getById(id),
                () -> hrmsAttendanceConfigDao.getById(id)
        );
    }

    public CalendarConfigDO getByIdNew(Long id) {
        return readNewWrapper(
                () -> calendarConfigDao.getById(id),
                () -> hrmsAttendanceConfigDao.getById(id)
        );
    }

    /**
     * 根据日历id批量查询
     */
    public List<HrmsAttendanceConfigDO> listByIds(List<Long> ids) {
        return readBatchWrapper(
                () -> calendarConfigDao.listByIds(ids),
                () -> hrmsAttendanceConfigDao.listByIds(ids)
        );
    }


    public void updateById(HrmsAttendanceConfigDO hrmsAttendanceConfigDO) {
        saveOrUpdateOneWrapper(
                hrmsAttendanceConfigDO,
                calendarConfigDO -> calendarConfigDao.updateById(calendarConfigDO),
                oldData -> hrmsAttendanceConfigDao.updateById(oldData)
        );
    }

    public void updateByIdNew(CalendarConfigDO calendarConfigDO) {
        saveOrUpdateOneNewWrapper(
                calendarConfigDO,
                newData -> calendarConfigDao.updateById(calendarConfigDO),
                oldData -> hrmsAttendanceConfigDao.updateById(oldData)
        );
    }


    public void updateDeptIds(HrmsAttendanceConfigDO entity, LambdaUpdateWrapper<HrmsAttendanceConfigDO> updateWrapper) {
        saveOrUpdateOneWrapper(
                entity,
                newData -> {
                    LambdaUpdateWrapper<CalendarConfigDO> newUpdateWrapper = Wrappers.lambdaUpdate(CalendarConfigDO.class);
                    newUpdateWrapper.set(CalendarConfigDO::getDeptIds, newData.getDeptIds());
                    newUpdateWrapper.eq(CalendarConfigDO::getId, newData.getId());
                    calendarConfigDao.update(newData, newUpdateWrapper);
                },
                oldData -> hrmsAttendanceConfigDao.update(oldData, updateWrapper)
        );
    }

    public void updateDeptIdsNew(CalendarConfigDO calendarConfigDO, LambdaUpdateWrapper<CalendarConfigDO> updateWrapper) {
        saveOrUpdateOneNewWrapper(
                calendarConfigDO,
                newData -> calendarConfigDao.update(newData, updateWrapper),
                oldData -> {
                    LambdaUpdateWrapper<HrmsAttendanceConfigDO> oldUpdateWrapper = Wrappers.lambdaUpdate(HrmsAttendanceConfigDO.class);
                    oldUpdateWrapper.set(HrmsAttendanceConfigDO::getDeptIds, calendarConfigDO.getDeptIds());
                    oldUpdateWrapper.eq(HrmsAttendanceConfigDO::getId, calendarConfigDO.getId());
                    hrmsAttendanceConfigDao.update(oldData, oldUpdateWrapper);
                }
        );
    }

    /**
     * 查询国家下的所有日历
     *
     * @param countryList 国家列表
     * @return 国家的所有日历
     */
    public List<HrmsAttendanceConfigDO> selectByCountryList(List<String> countryList) {
        return readBatchWrapper(
                () -> calendarConfigDao.selectByCountryList(countryList),
                () -> hrmsAttendanceConfigDao.selectByCountryList(countryList)
        );
    }

    /**
     * 根据ID查询最新启用的日历
     */
    public HrmsAttendanceConfigDO getActiveById(Long id) {
        return readWrapper(
                () -> calendarConfigDao.getActiveById(id),
                () -> hrmsAttendanceConfigDao.getActiveById(id)
        );
    }

    public CalendarConfigDO getActiveByIdNew(Long id) {
        return readNewWrapper(
                () -> calendarConfigDao.getActiveById(id),
                () -> hrmsAttendanceConfigDao.getActiveById(id)
        );
    }

    /**
     * 统计国家下默认方案数量
     */
    public Integer countDefaultAttendanceConfig(String country) {
        return commonQuery(
                () -> calendarConfigDao.countDefaultCalendarConfig(country),
                () -> hrmsAttendanceConfigDao.countDefaultAttendanceConfig(country)
        );
    }

    /**
     * 日历配置列表查询
     */
    public List<HrmsAttendanceConfigDO> list(AttendanceConfigQuery query) {
        return readBatchWrapper(
                () -> calendarConfigDao.listByQuery(
                        CalendarConfigMapstruct.INSTANCE.mapToNewQuery(query)),
                () -> hrmsAttendanceConfigDao.list(query)
        );
    }

    /**
     * 根据日历编号批量查询
     */
    public List<HrmsAttendanceConfigDO> listByNos(Collection<String> attendanceConfigNos) {
        return readBatchWrapper(
                () -> calendarConfigDao.listByNos(attendanceConfigNos),
                () -> hrmsAttendanceConfigDao.listByNos(attendanceConfigNos)
        );
    }

    /**
     * 根据配置类型返回对应的考勤方案
     *
     * @param configType
     * @param country
     * @return
     */
    public List<HrmsAttendanceConfigDO> config(String configType, String country) {
        return readBatchWrapper(
                () -> calendarConfigDao.getCountryCalendarByType(
                        StringUtils.isBlank(configType) ? null : AttendanceTypeEnum.valueOf(configType), country),
                () -> hrmsAttendanceConfigDao.config(configType, country)
        );
    }

    public List<CalendarConfigDO> configNew(String configType, String country) {
        return readNewBatchWrapper(
                () -> calendarConfigDao.getCountryCalendarByType(
                        StringUtils.isBlank(configType) ? null : AttendanceTypeEnum.valueOf(configType), country),
                () -> hrmsAttendanceConfigDao.config(configType, country)
        );
    }


    /**
     * 根据country查询
     *
     * @param country
     * @return
     */
    public List<HrmsAttendanceConfigDO> listByCountry(String country) {
        return readBatchWrapper(
                () -> calendarConfigDao.listByCountry(country),
                () -> hrmsAttendanceConfigDao.listByCountry(country)
        );
    }


    public void saveOne(HrmsAttendanceConfigDO oldData) {
        super.saveOrUpdateOneWrapper(
                oldData,
                calendarConfigDO -> {
                    //save new data
                    calendarConfigDao.save(calendarConfigDO);
                },
                // save old data
                oldDataHrmsAttendanceConfigDO -> hrmsAttendanceConfigDao.save(oldDataHrmsAttendanceConfigDO)
        );
    }

    public void saveOneNew(CalendarConfigDO newData) {
        super.saveOrUpdateOneNewWrapper(
                newData,
                calendarConfigDO -> {
                    //save new data
                    calendarConfigDao.save(calendarConfigDO);
                },
                // save old data
                oldDataHrmsAttendanceConfigDO -> hrmsAttendanceConfigDao.save(oldDataHrmsAttendanceConfigDO)
        );
    }

    public void updateOne(HrmsAttendanceConfigDO oldData) {
        super.saveOrUpdateOneWrapper(
                oldData,
                calendarConfigDO -> {
                    //update new data
                    calendarConfigDao.updateById(calendarConfigDO);
                },
                // save old data
                oldData1 -> hrmsAttendanceConfigDao.updateById(oldData1)
        );
    }


}
