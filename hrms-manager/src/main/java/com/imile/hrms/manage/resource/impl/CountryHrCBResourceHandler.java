package com.imile.hrms.manage.resource.impl;

import com.imile.hrms.common.enums.AttributeTypeEnum;
import com.imile.hrms.common.enums.ResourceTypeEnum;
import com.imile.hrms.manage.resource.ResourceHandlerAbstract;
import org.springframework.stereotype.Component;

/**
 * COUNTRY HR（薪资）管理员数据权限处理
 *
 * <AUTHOR>
 */
@Component
public class CountryHrCBResourceHandler extends ResourceHandlerAbstract {

    @Override
    public String getResourceHandler() {
        return AttributeTypeEnum.COUNTRY_HR_C_B.getCode();
    }

    @Override
    public ResourceTypeEnum getResourceType() {
        return ResourceTypeEnum.DEPT_ID;
    }
}
