package com.imile.hrms.manage.salary;

import com.imile.hrms.dao.salary.dto.ConfigItemDTO;
import com.imile.hrms.dao.salary.dto.SalaryEmployeeConfigDetailDTO;
import com.imile.hrms.dao.salary.model.HrmsSalaryConfigDO;
import com.imile.hrms.dao.salary.param.SalaryConfigEmployeeAddParam;
import com.imile.hrms.dao.salary.param.SalaryConfigEmployeeUpdateParam;
import com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeDetailDO;
import com.imile.hrms.dao.salary.query.SalaryConfigEmployeeInfoQuery;
import com.imile.hrms.dao.salary.query.SalaryEmployeeConfigQuery;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/5
 */
public interface HrmsSalaryEmployeeConfigManage {

    /**
     * 查询薪酬配置
     *
     * @param query
     * @return
     */
    List<HrmsSalaryEmployeeConfigDO> listSalaryEmployeeConfig(SalaryEmployeeConfigQuery query);


    /**
     * 查询用户当前最新的生效的薪资方案
     */
    List<HrmsSalaryEmployeeConfigDO> listSalaryEmployeeConfigByUserIdList(List<Long> userIdList);


    /**
     * 获取详情,第一条为最新记录，第二条为上次修改记录
     *
     * @param id
     * @return
     */
    List<SalaryEmployeeConfigDetailDTO> getDetailAndHistory(Long id);

    /**
     * 根据employeeConfigDO获取薪酬配置详情
     *
     * @param employeeConfigDO
     * @return
     */
    SalaryEmployeeConfigDetailDTO detail(HrmsSalaryEmployeeConfigDO employeeConfigDO);

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    SalaryEmployeeConfigDetailDTO detail(Long id);

    /**
     * 计算社保公积金
     *
     * @param hrmsSalaryEmployeeDetailDO
     * @param configItem
     * @param finalValue
     */
    void calculatorValue(HrmsSalaryEmployeeDetailDO hrmsSalaryEmployeeDetailDO, ConfigItemDTO configItem, BigDecimal finalValue, boolean isMex, String calculationType, HrmsUserEntryRecordDO userEntryRecordDO, BigDecimal absentDay);


    /**
     * 员工薪酬方案配置 新增
     *
     * @param addParam
     */
    void add(SalaryConfigEmployeeAddParam addParam);

    /**
     * 员工薪酬方案配置 修改
     *
     * @param updateParam
     */
    void update(SalaryConfigEmployeeUpdateParam updateParam);

    /**
     * 社保方案发生变化
     *
     * @param oldConfigId 旧记录配置ID
     * @param record      计薪方案记录
     * @return 影响数
     */
    Integer salaryConfigChange(Long oldConfigId, HrmsSalaryConfigDO record);

    /**
     * 社保方案发生变化
     *
     * @param oldConfigId 旧配置ID
     * @param newConfigId 新配置ID
     * @return
     */
    Integer socialConfigChange(Long oldConfigId, Long newConfigId);

    /**
     * 公积金方案发生变化
     *
     * @param oldConfigId 旧配置ID
     * @param newConfigId 新配置ID
     * @return
     */
    Integer accumulationConfigChange(Long oldConfigId, Long newConfigId);

    /**
     * 批量插入数据
     *
     * @param employeeConfigDOList
     */
    boolean batchSave(List<HrmsSalaryEmployeeConfigDO> employeeConfigDOList);
}
