package com.imile.hrms.manage.newAttendance.punchConfig.adapter;

import com.imile.hrms.common.adapter.AbstractPairAdapter;
import com.imile.hrms.common.adapter.DaoAdapter;
import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchConfigRangeDao;
import com.imile.hrms.dao.newAttendance.punchConfig.mapper.PunchConfigRangeMapper;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigRangeDO;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchConfigRangeByDateQuery;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchRangeConfigQuery;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchConfigRangeDao;
import com.imile.hrms.dao.punch.mapper.HrmsAttendancePunchConfigRangeMapper;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigRangeByDateQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchRangeConfigQuery;
import com.imile.hrms.dao.punch.query.AttendancePunchRangeQuery;
import com.imile.hrms.dao.punch.query.PunchConfigRangeQuery;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.mapstruct.PunchConfigRangeMapstruct;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/17
 * @Description
 */
@Component
public class PunchConfigRangeAdapter extends AbstractPairAdapter<PunchConfigRangeDO, HrmsAttendancePunchConfigRangeDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    @Resource
    private HrmsAttendancePunchConfigRangeDao attendancePunchConfigRangeDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private HrmsAttendancePunchConfigRangeMapper attendancePunchConfigRangeMapper;
    @Resource
    private PunchConfigRangeMapper punchConfigRangeMapper;


    public PunchConfigRangeAdapter(List<DataConverter<PunchConfigRangeDO, HrmsAttendancePunchConfigRangeDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }


    //====================adapterToOld========================================

    /**
     * 更新旧数据
     */
    public Boolean updateToOld(Long configId) {
        return commonWriteWithResult(
                () -> attendancePunchConfigRangeDao.updateToOld(configId),
                () -> punchConfigRangeDao.updateToOld(configId)
        );
    }

    /**
     * 根据用户id dept_id
     */
    public HrmsAttendancePunchConfigRangeDO selectRangeByUserIdOrDeptId(Long userId, RangeTypeEnum user) {
        return readWrapper(
                () -> punchConfigRangeDao.getRangeByUserIdOrDeptId(userId, user),
                () -> attendancePunchConfigRangeDao.selectRangeByUserIdOrDeptId(userId, user)
        );
    }

    /**
     * 获取当前最新的range
     */
    public List<HrmsAttendancePunchConfigRangeDO> getConfigRange(PunchConfigRangeQuery userQuery) {
        return readBatchWrapper(
                () -> {
                    PunchRangeConfigQuery punchRangeConfigQuery = PunchConfigRangeMapstruct.INSTANCE.mapToQuery(userQuery);
                    return punchConfigRangeDao.listPunchConfigRangeByQuery(punchRangeConfigQuery);
                },
                () -> attendancePunchConfigRangeDao.getConfigRange(userQuery)
        );
    }

    /**
     * 根据打卡配置id查询已关联的配置信息
     */
    public List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByPunchConfigId(Long punchConfigId) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listPunchConfigRangesByPunchConfigId(punchConfigId),
                () -> attendancePunchConfigRangeDao.selectPunchConfigRangeByPunchConfigId(punchConfigId)
        );
    }

    public List<PunchConfigRangeDO> selectPunchConfigRangeByPunchConfigIdNew(Long punchConfigId) {
        return readNewBatchWrapper(
                () -> punchConfigRangeDao.listPunchConfigRangesByPunchConfigId(punchConfigId),
                () -> attendancePunchConfigRangeDao.selectPunchConfigRangeByPunchConfigId(punchConfigId)
        );
    }

    public List<HrmsAttendancePunchConfigRangeDO> selectConfigRangeByBizId(List<Long> userIdList) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listPunchConfigRangesByBizIds(userIdList),
                () -> attendancePunchConfigRangeDao.selectConfigRangeByBizId(userIdList)
        );
    }

    /**
     * 获取用户所有的打卡规则
     */
    public List<HrmsAttendancePunchConfigRangeDO> selectUserAllConfigRangeByBizId(List<Long> userIdList) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listUserAllPunchConfigRangesByBizIds(userIdList),
                () -> attendancePunchConfigRangeDao.selectUserAllConfigRangeByBizId(userIdList)
        );
    }


    public List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByDate(AttendancePunchConfigRangeByDateQuery query) {
        return readBatchWrapper(
                () -> {
                    PunchConfigRangeByDateQuery dateQuery = PunchConfigRangeMapstruct.INSTANCE.mapToDateQuery(query);
                    return punchConfigRangeDao.listPunchConfigRangesByDate(dateQuery);
                },
                () -> attendancePunchConfigRangeDao.selectPunchConfigRangeByDate(query)
        );
    }

    public List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByIds(List<Long> idList) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listPunchConfigRangesByPunchConfigIds(idList),
                () -> attendancePunchConfigRangeDao.selectPunchConfigRangeByIds(idList)
        );
    }

    /**
     * 根据打卡配置ids查询已关联的配置信息
     */
    List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByPunchConfigIds(List<Long> punchConfigIds) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listPunchConfigRangesByPunchConfigIds(punchConfigIds),
                () -> attendancePunchConfigRangeDao.selectPunchConfigRangeByPunchConfigIds(punchConfigIds)
        );
    }

    /**
     * 查询是否免打卡
     */
    public List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByNeedPunch(Long punchConfigId, List<Long> userOrDeptIdList, Integer isNeedPunch) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listPunchConfigRangeByNeedPunch(punchConfigId, userOrDeptIdList, isNeedPunch),
                () -> attendancePunchConfigRangeDao.selectPunchConfigRangeByNeedPunch(punchConfigId, userOrDeptIdList, isNeedPunch)
        );
    }

    public List<HrmsAttendancePunchConfigRangeDO> listByBizIds(List<Long> bizIds) {
        return readBatchWrapper(
                () -> punchConfigRangeDao.listByBizIds(bizIds),
                () -> attendancePunchConfigRangeDao.listByBizIds(bizIds)
        );
    }

    public void save(HrmsAttendancePunchConfigRangeDO attendancePunchConfigRangeDO) {
        saveOrUpdateOneWrapper(
                attendancePunchConfigRangeDO,
                newData -> punchConfigRangeDao.save(newData),
                oldData -> attendancePunchConfigRangeDao.save(oldData)
        );
    }
    public void saveNew(PunchConfigRangeDO punchConfigRangeDO) {
        saveOrUpdateOneNewWrapper(
                punchConfigRangeDO,
                newData -> punchConfigRangeDao.save(newData),
                oldData -> attendancePunchConfigRangeDao.save(oldData)
        );
    }

    public void saveBatch(List<HrmsAttendancePunchConfigRangeDO> attendancePunchConfigRangeDOList) {
        saveOrUpdateBatchWrapper(
                attendancePunchConfigRangeDOList,
                newData -> punchConfigRangeDao.saveBatch(newData),
                oldData -> attendancePunchConfigRangeDao.saveBatch(oldData)
        );
    }

    public void updateBatchById(List<HrmsAttendancePunchConfigRangeDO> attendancePunchConfigRangeDOList) {
        saveOrUpdateBatchWrapper(
                attendancePunchConfigRangeDOList,
                newData -> punchConfigRangeDao.updateBatchById(newData),
                oldData -> attendancePunchConfigRangeDao.updateBatchById(oldData)
        );
    }

    public void updateById(HrmsAttendancePunchConfigRangeDO attendancePunchConfigRangeDO) {
        saveOrUpdateOneWrapper(
                attendancePunchConfigRangeDO,
                newData -> punchConfigRangeDao.updateById(newData),
                oldData -> attendancePunchConfigRangeDao.updateById(oldData)
        );
    }

    //======================HrmsAttendancePunchConfigRangeMapper===============================

    /**
     * 查找符合时间范围内的非班次打卡记录
     */
    public List<HrmsAttendancePunchConfigRangeDO> listExistRangeRecord(AttendancePunchRangeConfigQuery queryParam) {
        return readBatchWrapper(
                () -> {
                    PunchRangeConfigQuery punchRangeConfigQuery = PunchConfigRangeMapstruct.INSTANCE.toNewPunchRangeConfigQuery(queryParam);
                    return punchConfigRangeMapper.listExistRangeRecord(punchRangeConfigQuery);
                },
                () -> attendancePunchConfigRangeMapper.listExistRangeRecord(queryParam)
        );
    }


    /**
     * 查找部门是班次 但是配置非班次打卡的用户
     */
    public Collection<Long> selectNotClassWorkRange(AttendancePunchRangeQuery notClassRangeQuery) {
        return commonQuery(
                () -> {
                    PunchRangeConfigQuery punchRangeConfigQuery = PunchRangeConfigQuery.builder()
                            .startTime(notClassRangeQuery.getStartTime())
                            .endTime(notClassRangeQuery.getEndTime())
                            .punchNos(notClassRangeQuery.getPunchNos())
                            .build();
                    return punchConfigRangeMapper.selectNotClassWorkRange(punchRangeConfigQuery);
                },
                () -> attendancePunchConfigRangeMapper.selectNotClassWorkRange(notClassRangeQuery)
        );
    }

}
