package com.imile.hrms.manage.user;

import com.imile.hrms.dao.user.model.HrmsUserDimissionRecordDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/10
 */
public interface UserDimissionRecordManage {

    /**
     * 根据人员ID批量查询离职记录
     *
     * @param userIdList 人员ID列表
     * @return Map
     */
    Map<Long, HrmsUserDimissionRecordDO> getLatestDimissionByUserId(List<Long> userIdList);

    /**
     * 根据人员ID查询离职记录
     *
     * @param userId 人员ID
     * @return HrmsUserDimissionRecordDO
     */
    HrmsUserDimissionRecordDO getUserDimissionRecord(Long userId);
}
