package com.imile.hrms.manage.punch;

import com.imile.hrms.dao.punch.model.HrmsAttendanceCycleShiftConfigDO;

import java.util.List;

public interface HrmsAttendanceCycleShiftConfigManage {

    /**
     * 查询用户的循环排班规则
     */
    List<HrmsAttendanceCycleShiftConfigDO> selectShiftConfigByUserIdList(List<Long> userIdList);

    /**
     * 查询所有有效的循环排班规则
     */
    List<HrmsAttendanceCycleShiftConfigDO> selectAllShiftConfigByUserIdList();

    /**
     * 更新员工的循环排班
     */
    void batchUpdateShiftConfig(List<HrmsAttendanceCycleShiftConfigDO> shiftConfigDOS);

    void deleteByUserId(Long userId);

}
