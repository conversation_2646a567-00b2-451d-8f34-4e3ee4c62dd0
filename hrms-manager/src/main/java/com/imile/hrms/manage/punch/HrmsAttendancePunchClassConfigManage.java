package com.imile.hrms.manage.punch;

import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassConfigDO;

import java.util.List;

public interface HrmsAttendancePunchClassConfigManage {

    List<HrmsAttendancePunchClassConfigDO> selectClassByPunchConfigId(Long punchConfigId);

    List<HrmsAttendancePunchClassConfigDO> selectClassByPunchConfigIdList(List<Long> punchConfigIdList);

    List<HrmsAttendancePunchClassConfigDO> selectClassByIdList(List<Long> idList);

    void batchUpdate(List<HrmsAttendancePunchClassConfigDO> updateClassConfigList);

}
