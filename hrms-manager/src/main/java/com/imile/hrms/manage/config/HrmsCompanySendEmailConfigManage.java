package com.imile.hrms.manage.config;

import com.imile.hrms.dao.config.model.HrmsCompanyConfigDO;
import com.imile.hrms.dao.config.model.HrmsUserChangeEmailConfigDO;
import com.imile.hrms.manage.config.bo.HrmsCompanyEmailConfigBO;

import java.util.List;

public interface HrmsCompanySendEmailConfigManage {

    boolean add(HrmsCompanyConfigDO companyConfigDO, List<HrmsUserChangeEmailConfigDO> emailConfigDOList);

    boolean edit(HrmsCompanyConfigDO companyConfigDO, List<HrmsUserChangeEmailConfigDO> emailConfigDOList);

    HrmsCompanyEmailConfigBO getByCountry(String country);
}
