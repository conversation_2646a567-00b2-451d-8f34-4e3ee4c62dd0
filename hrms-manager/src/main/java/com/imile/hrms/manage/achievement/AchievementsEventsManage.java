package com.imile.hrms.manage.achievement;


import com.imile.hrms.dao.achievement.dto.AchievementsEventsCheckDTO;
import com.imile.hrms.dao.achievement.model.AchievementsEventsDO;

import java.util.List;

/**
 * achievementsEvents manage
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
public interface AchievementsEventsManage {


    /**
     * 根据用户ID和事件名称查询
     *
     * @param checkDTO
     * @return
     */
    Integer checkAeNumByUserIdAndName(AchievementsEventsCheckDTO checkDTO);

    List<AchievementsEventsDO> selectByIdList(List<Long> idList);

}
