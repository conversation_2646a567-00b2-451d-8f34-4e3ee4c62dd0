package com.imile.hrms.manage.primary;

import com.imile.hrms.dao.organization.query.PostConditionBuilder;
import com.imile.hrms.dao.primary.entity.PostDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
public interface PostManage {

    /**
     * 根据岗位ID获取岗位
     *
     * @param id 岗位ID
     * @return PostDO
     */
    PostDO getPostById(Long id);

    /**
     * 根据岗位ID获取岗位(结果可为null)
     *
     * @param id 岗位ID
     * @return PostDO
     */
    PostDO getPostNullableById(Long id);

    /**
     * 获取岗位ID与岗位对象映射
     *
     * @param idList 岗位ID列表
     * @return Map
     */
    Map<Long, PostDO> getPostMap(List<Long> idList);

    /**
     * 根据动态条件查询岗位
     *
     * @param condition PostConditionBuilder
     * @return List<PostDO>
     */
    List<PostDO> getPostByCondition(PostConditionBuilder condition);
}
