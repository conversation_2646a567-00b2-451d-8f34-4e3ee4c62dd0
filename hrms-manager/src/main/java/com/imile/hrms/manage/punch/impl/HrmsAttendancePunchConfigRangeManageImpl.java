package com.imile.hrms.manage.punch.impl;

import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigRangeDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchConfigRangeDao;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigRangeByDateQuery;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDaoFacade;
import com.imile.hrms.manage.punch.HrmsAttendancePunchConfigRangeManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-12
 * @version: 1.0
 */
@Service
public class HrmsAttendancePunchConfigRangeManageImpl implements HrmsAttendancePunchConfigRangeManage {

    @Autowired
    private HrmsAttendancePunchConfigRangeDao hrmsAttendancePunchConfigRangeDao;

    @Autowired
    private HrmsAttendanceConfigRangeDao hrmsAttendanceConfigRangeDao;

    @Resource
    private CalendarConfigDaoFacade calendarConfigDaoFacade;

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> selectConfigRangeByBizId(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendancePunchConfigRangeDao.selectConfigRangeByBizId(userIdList);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> selectUserAllConfigRangeByBizId(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendancePunchConfigRangeDao.selectUserAllConfigRangeByBizId(userIdList);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> selectConfigRangeByPunchConfigId(List<Long> punchConfigIdList) {
        if (CollectionUtils.isEmpty(punchConfigIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendancePunchConfigRangeDao.selectPunchConfigRangeByPunchConfigIds(punchConfigIdList);
    }

    @Override
    public List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByDate(AttendancePunchConfigRangeByDateQuery query) {
        return hrmsAttendancePunchConfigRangeDao.selectPunchConfigRangeByDate(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void attendancePunchRangeCheckUpdate(List<HrmsAttendancePunchConfigRangeDO> addPunchConfigRangeList, List<HrmsAttendanceConfigRangeDO> addConfigRangeList) {
        if (CollectionUtils.isNotEmpty(addPunchConfigRangeList)) {
            hrmsAttendancePunchConfigRangeDao.saveBatch(addPunchConfigRangeList);
        }
        if (CollectionUtils.isNotEmpty(addConfigRangeList)) {
            hrmsAttendanceConfigRangeDao.saveBatch(addConfigRangeList);
            //todo 考勤组兼容时，此处无需再适配
//            calendarConfigDaoFacade.getCalendarConfigRangeAdapter().saveBatch(addConfigRangeList);
        }
    }
}
