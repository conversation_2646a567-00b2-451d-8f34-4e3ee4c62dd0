package com.imile.hrms.manage.salary;

import com.imile.hrms.dao.salary.model.HrmsSalaryUserRoleDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryUserRoleQuery;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/11/15 17:39
 * @version: 1.0
 */
public interface HrmsSalaryUserRoleManage {

    List<HrmsSalaryUserRoleDO> listByQuery(HrmsSalaryUserRoleQuery query);


    List<HrmsSalaryUserRoleDO> selectRoleByUserIdList(List<Long> userIdList);

    void save(HrmsSalaryUserRoleDO salaryUserRoleDO);

    void update(HrmsSalaryUserRoleDO salaryUserRoleDO);

}
