package com.imile.hrms.manage.salary.impl;

import com.imile.hrms.dao.salary.dao.HrmsSalaryAttendanceSchemeDao;
import com.imile.hrms.dao.salary.model.HrmsSalaryAttendanceSchemeDO;
import com.imile.hrms.dao.salary.query.SalaryAttendanceSchemeQuery;
import com.imile.hrms.manage.salary.HrmsSalaryAttendanceSchemeManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/11 17:07
 * @version: 1.0
 */
@Service
public class HrmsSalaryAttendanceSchemeManageImpl implements HrmsSalaryAttendanceSchemeManage {

    @Autowired
    private HrmsSalaryAttendanceSchemeDao hrmsSalaryAttendanceSchemeDao;

    @Override
    public List<HrmsSalaryAttendanceSchemeDO> selectAttendanceScheme(SalaryAttendanceSchemeQuery query) {
        return hrmsSalaryAttendanceSchemeDao.selectAttendanceScheme(query);
    }

    @Override
    public List<HrmsSalaryAttendanceSchemeDO> selectAttendanceSchemeByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryAttendanceSchemeDao.listByIds(idList);
    }
}
