package com.imile.hrms.manage.salary.behavior;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.manage.salary.behavior.BO.EmployeeAttendanceImportBO;
import com.imile.hrms.manage.salary.behavior.BO.ExternalAttendanceDataBO;
import com.imile.hrms.manage.salary.behavior.BO.ExternalAttendanceDayBO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
public interface IEmployeeAttendanceImportStrategy {

    void importEmployeeAttendance(Map<String, List<EmployeeAttendanceImportBO>> employeeAttendanceGroup, List<ExternalAttendanceDayBO> allDayBOList, ExternalAttendanceDataBO attendanceDataBO, List<HrmsAttendanceEmployeeDetailDO> attendanceRecordList, Map<Long, HrmsAttendanceConfigDetailDO> dayIdMaps, List<EmployeeAttendanceImportBO> failList);
}
