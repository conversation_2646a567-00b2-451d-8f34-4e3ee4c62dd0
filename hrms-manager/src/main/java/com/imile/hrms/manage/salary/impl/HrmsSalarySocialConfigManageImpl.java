package com.imile.hrms.manage.salary.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.salary.BasePaymentTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.BigDecimalUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.salary.dao.HrmsSalarySocialConfigDao;
import com.imile.hrms.dao.salary.dto.*;
import com.imile.hrms.dao.salary.model.HrmsSalarySocialConfigDO;
import com.imile.hrms.dao.salary.param.SalaryBaseConfigParam;
import com.imile.hrms.dao.salary.param.ConfigItemParam;
import com.imile.hrms.dao.salary.query.SocialAndAccumulationQuery;
import com.imile.hrms.manage.converter.ConverterService;
import com.imile.hrms.manage.salary.HrmsSalarySocialConfigManage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Service
public class HrmsSalarySocialConfigManageImpl implements HrmsSalarySocialConfigManage {

    @Autowired
    private HrmsSalarySocialConfigDao hrmsSalarySocialConfigDao;

    @Autowired
    private ConverterService converterService;

    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    @Override
    public AbstractSalaryConfigDetailDTO detail(SocialAndAccumulationQuery query) {

        AbstractSalaryConfigDetailDTO abstractSalaryConfigDetailDTO = hrmsSalarySocialConfigDao.detailDTO(query);
        IBaseConfigItem baseConfigItem = abstractSalaryConfigDetailDTO.getBaseConfigItem();
        if (baseConfigItem instanceof MexSalaryBaseConfigItemDTO) {
            // 墨西哥
            converterService.withAnnotation(MexSalaryBaseConfigItemDTO.class).accept((MexSalaryBaseConfigItemDTO) baseConfigItem);
        } else if (baseConfigItem instanceof SalaryBaseConfigItemDTO) {
            converterService.withAnnotation(SalaryBaseConfigItemDTO.class).accept((SalaryBaseConfigItemDTO) baseConfigItem);
            converterService.withAnnotation(abstractSalaryConfigDetailDTO.getConfigItems());
        }

        return abstractSalaryConfigDetailDTO;
    }

    @Override
    public AbstractSalaryConfigDetailDTO detail(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        //查询类
        return this.detail(SocialAndAccumulationQuery.builder().configNo(configNo).isLatest(BusinessConstant.Y).build());
    }

    /**
     * Do->configSelectDTO
     *
     * @return
     */
    @Override
    public <T> Function<T, ConfigSelectDTO> configSelectDTO(Function<T, String> configGetter, Function<T, String> configNameGetter) {
        return bean -> {
            ConfigSelectDTO configSelectDTO = BeanUtil.copyProperties(bean, ConfigSelectDTO.class);
            configSelectDTO.setConfigNo(configGetter.apply(bean));
            configSelectDTO.setConfigName(configNameGetter.apply(bean));
            return configSelectDTO;
        };
    }

    @Override
    public void baseConfigParamValidate(SalaryBaseConfigParam salaryBaseConfigParam) {
        //是否统一设置
        BusinessLogicException.checkTrue(salaryBaseConfigParam.getIsUniformSet() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "isUniform");
        //最小缴纳基数
        BusinessLogicException.checkTrue(salaryBaseConfigParam.getBaseMinValue() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "min value");
        //最大缴纳基数
        BusinessLogicException.checkTrue(salaryBaseConfigParam.getBaseMaxValue() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "max value");

    }

    @Override
    public void socialConfigItemParamValidate(List<ConfigItemParam> configItems) {
        for (ConfigItemParam configItem : configItems) {
            //项目名称非空
            BusinessLogicException.checkTrue(configItem.getConfigName() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "config item name");
            checkRate(configItem);
        }
    }

    @Override
    public void checkRate(ConfigItemParam configItem) {
        //单位缴纳比例
        BusinessLogicException.checkTrue(configItem.getOrgPaymentRate() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "org rate");
        //个人缴纳比例
        BusinessLogicException.checkTrue(configItem.getPersonPaymentRate() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "person rate");
        //单位缴纳比例
        BusinessLogicException.checkTrue(!this.range(configItem.getOrgPaymentRate()), HrmsErrorCodeEnums.RATE_RANGE_ERROR.getCode(), HrmsErrorCodeEnums.RATE_RANGE_ERROR.getDesc(), configItem.getOrgPaymentRate());
        //个人缴纳比例
        BusinessLogicException.checkTrue(!this.range(configItem.getOrgPaymentRate()), HrmsErrorCodeEnums.RATE_RANGE_ERROR.getCode(), HrmsErrorCodeEnums.RATE_RANGE_ERROR.getDesc(), configItem.getPersonPaymentRate());
    }

    @Override
    public void checkConfigItem(List<ConfigItemParam> configItems, SalaryBaseConfigParam baseConfigItem, String basePaymentType) {

        for (ConfigItemParam item : configItems) {
            BusinessLogicException.checkTrue(item.getBasePaymentType() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "baseTypeEnum");
            //校验缴纳基数的类型
            BasePaymentTypeEnum basePaymentTypeEnum = BasePaymentTypeEnum.getBasePaymentTypeEnum(item.getBasePaymentType());
            BusinessLogicException.checkTrue(basePaymentTypeEnum == null, HrmsErrorCodeEnums.BASE_PAYMENT_TYPE_ERROR.getCode(), HrmsErrorCodeEnums.BASE_PAYMENT_TYPE_ERROR.getDesc());
            //给每个item设置唯一标识
            item.setId(item.getId() == null ? iHrmsIdWorker.nextId() : item.getId());
            //若是统一设置，则需要判断类型是不是一致
            if (BusinessConstant.N.equals(baseConfigItem.getIsUniformSet())) {
                continue;
            }
            //判断类型是不是一致
            BusinessLogicException.checkTrue(!basePaymentType.equals(item.getBasePaymentType()), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "base Value Type ");
            //判断参数值是否一致
            BusinessLogicException.checkTrue(!BigDecimalUtil.checkSame(baseConfigItem.getBaseValue(), item.getBaseValue()), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "base Value");

        }
    }

    @Override
    public void checkBaseConfigItem(SalaryBaseConfigParam baseConfigItem) {
        //basePaymentType非空校验
        BusinessLogicException.checkTrue(baseConfigItem.getBasePaymentType() == null, MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "baseTypeEnum");

        BasePaymentTypeEnum basePaymentTypeEnum = BasePaymentTypeEnum.getBasePaymentTypeEnum(baseConfigItem.getBasePaymentType());
        //校验缴纳基数的类型
        BusinessLogicException.checkTrue(basePaymentTypeEnum == null, HrmsErrorCodeEnums.BASE_PAYMENT_TYPE_ERROR.getCode(), HrmsErrorCodeEnums.BASE_PAYMENT_TYPE_ERROR.getDesc());

        if (BasePaymentTypeEnum.FIXED_VALUE.equals(basePaymentTypeEnum) || BasePaymentTypeEnum.BASE_SALARY_RATE.equals(basePaymentTypeEnum)) {
            BusinessLogicException.checkTrue(baseConfigItem.getBaseValue() == null, HrmsErrorCodeEnums.BASE_VALUE_EMPTY_ERROR.getCode(), HrmsErrorCodeEnums.BASE_VALUE_EMPTY_ERROR.getDesc());
        }
        if (BasePaymentTypeEnum.FIXED_VALUE.getCode().equals(baseConfigItem.getBasePaymentType())) {
            BusinessLogicException.checkTrue(!BigDecimalUtil.range(baseConfigItem.getBaseValue(), baseConfigItem.getBaseMinValue(), baseConfigItem.getBaseMaxValue()), MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "base Value");
        }
    }

    @Override
    public List<HrmsSalarySocialConfigDO> selectSocialConfigByNames(List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalarySocialConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsSalarySocialConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsSalarySocialConfigDO::getSocialConfigName, nameList);
        return hrmsSalarySocialConfigDao.list(queryWrapper);
    }


    /**
     * 查询是否在区间范围内 缴纳税率
     *
     * @param rate
     * @return
     */
    private boolean range(BigDecimal rate) {
        return BigDecimalUtil.range(rate, BigDecimal.ZERO, new BigDecimal("100"));
    }

}
