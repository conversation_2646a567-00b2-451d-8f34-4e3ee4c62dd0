package com.imile.hrms.manage.attendance.impl;

import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.hrms.manage.attendance.HrmsEmployeeAbnormalOperationRecordManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-28
 * @version: 1.0
 */
@Service
public class HrmsEmployeeAbnormalOperationRecordManageImpl implements HrmsEmployeeAbnormalOperationRecordManage {
    @Autowired
    private HrmsEmployeeAbnormalOperationRecordDao hrmsEmployeeAbnormalOperationRecordDao;

    @Override
    public List<HrmsEmployeeAbnormalOperationRecordDO> selectByAbnormalList(List<Long> abnormalList) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return new ArrayList<>();
        }
        return hrmsEmployeeAbnormalOperationRecordDao.selectByAbnormalList(abnormalList);
    }
}
