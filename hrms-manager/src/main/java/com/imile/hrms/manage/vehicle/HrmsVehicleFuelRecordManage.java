package com.imile.hrms.manage.vehicle;

import com.imile.hrms.dao.vehicle.model.HrmsVehicleFuelRecordDO;
import com.imile.hrms.dao.vehicle.query.VehicleFuelRecordQuery;

import java.util.List;

public interface HrmsVehicleFuelRecordManage {

    List<HrmsVehicleFuelRecordDO> selectVehicleFuelRecord(VehicleFuelRecordQuery query);

    void batchInsert(List<HrmsVehicleFuelRecordDO> recordDOList);

    void batchUpdate(List<HrmsVehicleFuelRecordDO> recordDOList);
}
