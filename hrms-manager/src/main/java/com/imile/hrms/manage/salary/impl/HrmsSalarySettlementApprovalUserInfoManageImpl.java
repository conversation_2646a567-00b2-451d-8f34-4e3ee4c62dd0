package com.imile.hrms.manage.salary.impl;

import com.imile.hrms.dao.salary.dao.HrmsSalarySettlementApprovalUserInfoDao;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalUserInfoDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementApprovalUserInfoQuery;
import com.imile.hrms.manage.salary.HrmsSalarySettlementApprovalUserInfoManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/1 14:27
 * @version: 1.0
 */
@Service
public class HrmsSalarySettlementApprovalUserInfoManageImpl implements HrmsSalarySettlementApprovalUserInfoManage {
    @Autowired
    private HrmsSalarySettlementApprovalUserInfoDao hrmsSalarySettlementApprovalUserInfoDao;

    @Override
    public List<HrmsSalarySettlementApprovalUserInfoDO> listByQuery(HrmsSalarySettlementApprovalUserInfoQuery query) {
        return hrmsSalarySettlementApprovalUserInfoDao.listByQuery(query);
    }
}
