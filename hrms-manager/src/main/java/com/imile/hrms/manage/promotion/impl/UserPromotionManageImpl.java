package com.imile.hrms.manage.promotion.impl;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.promotion.PromotionStatusEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.promotion.condition.UserPromotionConditionBuilder;
import com.imile.hrms.dao.promotion.dao.UserPromotionDao;
import com.imile.hrms.dao.promotion.dao.UserPromotionReviewDao;
import com.imile.hrms.dao.promotion.model.UserPromotionDO;
import com.imile.hrms.dao.promotion.model.UserPromotionReviewDO;
import com.imile.hrms.manage.common.CommonManage;
import com.imile.hrms.manage.promotion.UserPromotionManage;
import com.imile.hrms.manage.promotion.UserPromotionReviewManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/26
 */
@Component
@Slf4j
public class UserPromotionManageImpl implements UserPromotionManage {

    @Resource
    private UserPromotionDao userPromotionDao;
    @Resource
    private UserPromotionReviewDao userPromotionReviewDao;
    @Resource
    private UserPromotionReviewManage userPromotionReviewManage;
    @Resource
    private CommonManage commonManage;

    @Override
    public UserPromotionDO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        UserPromotionDO promotion = userPromotionDao.getById(id);
        if (Objects.isNull(promotion) || IsDeleteEnum.YES.getCode().equals(promotion.getIsDelete())) {
            log.error("晋升记录不存在, promotionId:{}", id);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.DATA_NOT_EXITS);
        }
        return promotion;
    }


    @Override
    public void update(UserPromotionDO update) {
        if (Objects.nonNull(update)) {
            userPromotionDao.updateById(update);
        }
    }

    @Override
    public Map<Long, Date> getUserLatestPromotionDateMap(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        List<UserPromotionDO> userPromotionList = userPromotionDao.selectByCondition(
                UserPromotionConditionBuilder.builder()
                        .userIdList(userIdList)
                        .promotionStatusList(Collections.singletonList(PromotionStatusEnum.EFFECTIVE.getStatus()))
                        .build());
        return userPromotionList.stream()
                .filter(it -> Objects.nonNull(it.getEffectDate()))
                .collect(Collectors.toMap(
                        UserPromotionDO::getUserId, UserPromotionDO::getEffectDate,
                        (existingDate, newDate) -> newDate.after(existingDate) ? newDate : existingDate
                ));
    }

    @Override
    public List<UserPromotionDO> getUserPromotionOngoingList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        List<UserPromotionDO> userPromotionList = userPromotionDao.selectByCondition(
                UserPromotionConditionBuilder.builder()
                        .userIdList(userIdList)
                        .build());
        return userPromotionList.stream()
                .filter(s -> PromotionStatusEnum.inProcess(s.getPromotionStatus()))
                .collect(Collectors.toList());
    }

    @Override
    public List<UserPromotionDO> getUserPromotionOngoingListByBatchId(Long batchId) {
        if (Objects.isNull(batchId) || BusinessConstant.DEFAULT_ID.equals(batchId)) {
            return Collections.emptyList();
        }
        List<UserPromotionDO> userPromotionList = userPromotionDao.selectByCondition(
                UserPromotionConditionBuilder.builder()
                        .batchId(batchId)
                        .build());
        return userPromotionList.stream()
                .filter(s -> PromotionStatusEnum.inProcess(s.getPromotionStatus()))
                .collect(Collectors.toList());
    }

    @Override
    public List<UserPromotionDO> getUserPromotionListByBatchId(Long batchId) {
        if (Objects.isNull(batchId) || BusinessConstant.DEFAULT_ID.equals(batchId)) {
            return Collections.emptyList();
        }
        return userPromotionDao.selectByCondition(UserPromotionConditionBuilder.builder()
                .batchId(batchId)
                .build());
    }

    @Override
    public UserPromotionDO getUserPromotionByBatchId(Long batchId) {
        if (Objects.isNull(batchId) || BusinessConstant.DEFAULT_ID.equals(batchId)) {
            return null;
        }
        List<UserPromotionDO> userPromotionList = userPromotionDao.selectByCondition(
                UserPromotionConditionBuilder.builder()
                        .batchId(batchId)
                        .build());
        List<UserPromotionDO> filterPromotionList = userPromotionList.stream()
                .filter(s -> !PromotionStatusEnum.CANCELLED.getStatus().equals(s.getPromotionStatus()))
                .collect(Collectors.toList());
        if (filterPromotionList.isEmpty()) {
            log.error("晋升记录不存在, batchId:{}", batchId);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.DATA_NOT_EXITS);
        }
        return filterPromotionList.get(0);
    }

    @Override
    public void doAnnualBatchUpdate(List<UserPromotionDO> userPromotionList, List<UserPromotionReviewDO> userPromotionReviewList) {
        if (CollectionUtils.isNotEmpty(userPromotionList)) {
            userPromotionDao.updateBatchById(userPromotionList);
        }
        if (CollectionUtils.isNotEmpty(userPromotionReviewList)) {
            userPromotionReviewDao.updateBatchById(userPromotionReviewList);
        }
    }

    @Override
    public void doAnnualSave(DataDifferHolder<UserPromotionDO> promotionDifferHolder,
                             DataDifferHolder<UserPromotionReviewDO> reviewDifferHolder) {
        commonManage.differSave(promotionDifferHolder, userPromotionDao);
        userPromotionReviewManage.doSave(reviewDifferHolder);
    }
}
