package com.imile.hrms.manage.newAttendance.punchConfig.adapter;

import com.imile.hrms.common.adapter.AbstractPairAdapter;
import com.imile.hrms.common.adapter.DaoAdapter;
import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.common.adapter.config.EnableNewAttendanceConfig;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchClassItemConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassItemConfigDO;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchClassItemConfigDao;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassItemConfigDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/17
 * @Description
 */
@Component
public class PunchClassItemConfigAdapter extends AbstractPairAdapter<PunchClassItemConfigDO, HrmsAttendancePunchClassItemConfigDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig config;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private HrmsAttendancePunchClassItemConfigDao hrmsAttendancePunchClassItemConfigDao;


    public PunchClassItemConfigAdapter(List<DataConverter<PunchClassItemConfigDO,
            HrmsAttendancePunchClassItemConfigDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isEnableNewModule() {
        return config.getIsEnablePunchConfig();
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return config.getPunchConfigDoubleWriteEnabled();
    }

    public List<HrmsAttendancePunchClassItemConfigDO> selectItemConfigByClassId(List<Long> classIds) {
        return readBatchWrapper(
                () -> punchClassItemConfigDao.listPunchClassItemsByClassId(classIds),
                () -> hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(classIds)
        );
    }

    public List<PunchClassItemConfigDO> selectItemConfigByClassIdNew(List<Long> classIds) {
        return readNewBatchWrapper(
                () -> punchClassItemConfigDao.listPunchClassItemsByClassId(classIds),
                () -> hrmsAttendancePunchClassItemConfigDao.selectItemConfigByClassId(classIds)
        );
    }
}
