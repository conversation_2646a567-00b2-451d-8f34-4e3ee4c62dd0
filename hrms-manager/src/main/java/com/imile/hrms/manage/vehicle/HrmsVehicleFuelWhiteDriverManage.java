package com.imile.hrms.manage.vehicle;

import com.imile.hrms.dao.vehicle.model.HrmsVehicleFuelWhiteDriverDO;
import com.imile.hrms.dao.vehicle.query.WhiteDriverQuery;

import java.util.List;

public interface HrmsVehicleFuelWhiteDriverManage {

    List<HrmsVehicleFuelWhiteDriverDO> selectWhiteDriver(WhiteDriverQuery query);

    void updateWhiteDriver(HrmsVehicleFuelWhiteDriverDO whiteDriverDO);

    void addWhiteDriver(HrmsVehicleFuelWhiteDriverDO whiteDriverDO);

    void batchUpdateWhiteDriver(List<HrmsVehicleFuelWhiteDriverDO> updateList);

    void batchAddWhiteDriver(List<HrmsVehicleFuelWhiteDriverDO> addList);

}
