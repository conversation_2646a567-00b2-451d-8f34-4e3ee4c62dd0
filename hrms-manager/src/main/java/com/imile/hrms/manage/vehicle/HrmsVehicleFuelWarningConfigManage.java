package com.imile.hrms.manage.vehicle;

import com.imile.hrms.dao.vehicle.model.HrmsVehicleFuelWarningConfigDO;
import com.imile.hrms.dao.vehicle.query.WarningConfigQuery;

import java.util.List;

public interface HrmsVehicleFuelWarningConfigManage {

    List<HrmsVehicleFuelWarningConfigDO> selectWarningConfig(WarningConfigQuery query);

    List<HrmsVehicleFuelWarningConfigDO> selectWarningConfigByIdList(List<Long> idList);

    void addWarningConfig(HrmsVehicleFuelWarningConfigDO addDO);

    void updateWarningConfig(HrmsVehicleFuelWarningConfigDO updateDO);

    void batchAddWarningConfig(List<HrmsVehicleFuelWarningConfigDO> addDOList);

    void batchUpdateWarningConfig(List<HrmsVehicleFuelWarningConfigDO> updateList);

}
