package com.imile.hrms.manage.training.bo;

import com.imile.hrms.dao.training.model.HrmsUserTrainingRecordDO;
import com.imile.hrms.dao.user.model.HrmsAttachmentDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TrainingInfoDetailBO implements Serializable {
    private static final long serialVersionUID = -2549275270420687690L;

    private Long id;

    /**
     * 培训主题
     */
    private String trainingTopic;

    /**
     * 培训类型
     */
    private String trainingType;

    /**
     * 培训场次
     */
    private String trainingSession;

    /**
     * 培训日期
     */
    private Date date;

    /**
     * dayid 示例：20220124
     */
    private String dayId;

    /**
     * 培训开始时间
     */
    private Date trainingStartTime;

    /**
     * 培训结束时间
     */
    private Date trainingEndTime;

    /**
     * 培训网点id
     */
    private Long deptId;

    /**
     * 培训讲师编码
     */
    private String trainerCode;

    /**
     * 培训讲师名称
     */
    private String trainerName;

    /**
     * 附件
     */
    private List<HrmsAttachmentDO> attachments;

    /**
     * 状态 未开始、签到完成、结果录入中、已完成
     */
    private String status;

    /**
     * 参与培训人员信息
     */
    List<HrmsUserTrainingRecordDO> trainingRecordList;
}
