package com.imile.hrms.manage.newAttendance.calendar.adapter.mapstruct;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigDetailQuery;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDetailDO;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarConfigDetailQuery;
import com.imile.hrms.manage.newAttendance.common.BaseMapstruct;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/2/6 
 * @Description
 */
@Mapper
public interface CalendarConfigDetailMapstruct extends BaseMapstruct {

    CalendarConfigDetailMapstruct INSTANCE = Mappers.getMapper(CalendarConfigDetailMapstruct.class);


    @Mapping(target = "calendarConfigId", source = "attendanceConfigId")
    CalendarConfigDetailQuery mapToQuery(AttendanceConfigDetailQuery configDetailQuery);

    @Mapping(target = "id", expression = "java(setModelId(calendarConfigDetailDO))")
    HrmsAttendanceConfigDetailDO mapToOld(CalendarConfigDetailDO calendarConfigDetailDO);

    List<HrmsAttendanceConfigDetailDO> mapToOld(List<CalendarConfigDetailDO> calendarConfigDetailDOs);

    @InheritInverseConfiguration
    @Mapping(target = "id", expression = "java(setModelId(hrmsAttendanceConfigDetailDO))")
    CalendarConfigDetailDO mapToNew(HrmsAttendanceConfigDetailDO hrmsAttendanceConfigDetailDO);

    List<CalendarConfigDetailDO> mapToNew(List<HrmsAttendanceConfigDetailDO> hrmsAttendanceConfigDetailDOs);


    default Map<Long, HrmsAttendanceConfigDetailDO> mapToOldMap(Map<Long, CalendarConfigDetailDO> newMap) {
        if (newMap == null) {
            return Collections.emptyMap();
        }
        return newMap.entrySet()
                .stream()
                .filter(entry -> entry.getKey() != null && entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> mapToOld(entry.getValue()),
                        (existing, replacement) -> existing,
                        HashMap::new
                ));
    }

    default Long setModelId(CalendarConfigDetailDO module) {
        return setModelId(module,
                CalendarConfigDetailDO::getId,
                CalendarConfigDetailDO::setId);
    }


    default Long setModelId(HrmsAttendanceConfigDetailDO module) {
        return setModelId(module,
                HrmsAttendanceConfigDetailDO::getId,
                HrmsAttendanceConfigDetailDO::setId);
    }
}
