package com.imile.hrms.manage.promotion;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.promotion.model.UserPromotionReviewDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/26
 */
public interface UserPromotionReviewManage {

    /**
     * 保存晋升评议结果
     *
     * @param differHolder 晋升评议结果差异保持器
     */
    void doSave(DataDifferHolder<UserPromotionReviewDO> differHolder);

    /**
     * 根据人员晋升ID批量获取
     *
     * @param userPromotionIdList 人员晋升ID
     * @return List<UserPromotionReviewDO>
     */
    List<UserPromotionReviewDO> getUserPromotionReviewList(List<Long> userPromotionIdList);

    /**
     * 根据人员晋升ID获取
     *
     * @param userPromotionId 人员晋升ID
     * @return UserPromotionReviewDO
     */
    UserPromotionReviewDO getUserPromotionReview(Long userPromotionId);
}
