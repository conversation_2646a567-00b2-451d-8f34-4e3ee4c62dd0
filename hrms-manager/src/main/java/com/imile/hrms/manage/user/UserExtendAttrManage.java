package com.imile.hrms.manage.user;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.user.dto.UserExtendAttrDTO;
import com.imile.hrms.dao.user.model.UserExtendAttrDO;
import com.imile.hrms.dao.user.query.UserExtendAttrQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
public interface UserExtendAttrManage {

    /**
     * 保存人员扩展属性
     *
     * @param differHolder 人员扩展属性差异保持器
     */
    void doSave(DataDifferHolder<UserExtendAttrDO> differHolder);

    /**
     * 保存人员扩展属性
     *
     * @param userId    人员ID
     * @param attrKey   扩展属性健
     * @param attrValue 扩展属性值
     */
    void saveUserExtendAttr(Long userId, String attrKey, String attrValue);

    /**
     * 获取人员扩展属性列表
     *
     * @param userId 人员ID
     * @return List<UserExtendAttrDO>
     */
    List<UserExtendAttrDO> getUserExtendAttrList(Long userId);

    /**
     * 批量获取人员指定扩展信息
     *
     * @param userIdList  人员ID列表
     * @param attrKeyList 属性键列表
     * @return List<UserExtendAttrDO>
     */
    List<UserExtendAttrDO> getUserExtendAttrList(List<Long> userIdList, List<String> attrKeyList);

    /**
     * 根据属性Key相关条件查询
     *
     * @param query UserExtendAttrQuery
     * @return UserExtendAttrDTO
     */
    List<UserExtendAttrDTO> selectByAttrKeyCondition(UserExtendAttrQuery query);
}
