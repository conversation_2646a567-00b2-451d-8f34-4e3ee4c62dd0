package com.imile.hrms.manage.attendance.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-5-28
 * @version: 1.0
 */
@Data
public class UserWorkHoursDTO {

    private BigDecimal recordAttendanceHours = BigDecimal.ZERO;

    private BigDecimal recordOvertimeHours = BigDecimal.ZERO;

    private BigDecimal recordLeaveHours = BigDecimal.ZERO;


}
