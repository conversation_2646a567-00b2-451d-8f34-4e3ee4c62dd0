package com.imile.hrms.manage.leave.impl;

import cn.hutool.core.collection.CollUtil;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceConfigDetailDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDetailDO;
import com.imile.hrms.dao.leave.HrmsCompanyLegalLeaveConfigDao;
import com.imile.hrms.dao.leave.model.HrmsCompanyLegalLeaveConfigDO;
import com.imile.hrms.manage.leave.HrmsCompanyLegalLeaveConfigManage;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyLegalLeaveConfigManageImpl
 * {@code @since:} 2024-03-08 14:44
 * {@code @description:}
 */
@Service
@Slf4j
public class HrmsCompanyLegalLeaveConfigManageImpl implements HrmsCompanyLegalLeaveConfigManage {

    @Autowired
    private HrmsCompanyLegalLeaveConfigDao hrmsCompanyLegalLeaveConfigDao;
    @Autowired
    private HrmsAttendanceConfigDetailDao hrmsAttendanceConfigDetailDao;
    //@Resource
    //private CalendarConfigDaoFacade calendarConfigDaoFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLegalLeaveConfigAndAttendanceDetail(List<HrmsCompanyLegalLeaveConfigDO> legalLeaveConfigList, List<HrmsAttendanceConfigDetailDO> needUpdateAttendanceConfigDetailList, List<HrmsAttendanceConfigDetailDO> needSaveAttendanceConfigDetailList) {
        if (CollUtil.isNotEmpty(legalLeaveConfigList)) {
            // 保存法定假期配置
            hrmsCompanyLegalLeaveConfigDao.saveBatch(legalLeaveConfigList);
            //calendarConfigDaoFacade.getCalendarLegalLeaveConfigAdapter().saveBatch(legalLeaveConfigList);
        }
        if (CollUtil.isNotEmpty(needUpdateAttendanceConfigDetailList)) {
            // 更新考勤配置
            hrmsAttendanceConfigDetailDao.updateBatchById(needUpdateAttendanceConfigDetailList);
            //calendarConfigDaoFacade.getCalendarConfigDetailAdapter().updateBatchById(needUpdateAttendanceConfigDetailList);
        }
        if (CollUtil.isNotEmpty(needSaveAttendanceConfigDetailList)) {
            // 保存考勤配置
            hrmsAttendanceConfigDetailDao.saveBatch(needSaveAttendanceConfigDetailList);
            //calendarConfigDaoFacade.getCalendarConfigDetailAdapter().saveBatch(needSaveAttendanceConfigDetailList);
        }
    }
}
