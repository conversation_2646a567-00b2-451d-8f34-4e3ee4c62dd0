package com.imile.hrms.manage.common;

import com.imile.hrms.common.entity.DataOverrideHolder;
import com.imile.hrms.common.enums.DraftTypeEnum;
import com.imile.hrms.dao.user.model.HrmsDraftRecordDO;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
public interface DraftRecordManage {

    /**
     * 获取草稿记录
     *
     * @param draftTypeEnum 草稿类型
     * @param foreignKey    业务键值
     * @return HrmsDraftRecordDO
     */
    HrmsDraftRecordDO getDraftRecord(DraftTypeEnum draftTypeEnum, String foreignKey);

    /**
     * 清理草稿记录
     *
     * @param draftTypeEnum 草稿类型
     * @param foreignKey    业务键值
     */
    void remove(DraftTypeEnum draftTypeEnum, String foreignKey);

    /**
     * 保存草稿记录
     *
     * @param holder 草稿覆写保持器
     */
    void doSave(DataOverrideHolder<HrmsDraftRecordDO> holder);
}
