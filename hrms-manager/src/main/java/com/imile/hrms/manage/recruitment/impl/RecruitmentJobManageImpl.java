package com.imile.hrms.manage.recruitment.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.approval.ApprovalNoPrefixEnum;
import com.imile.hrms.common.enums.recruitment.HcApprovalInfoStatusEnum;
import com.imile.hrms.common.enums.recruitment.JobStatusEnum;
import com.imile.hrms.common.util.IdWorkUtils;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.organization.model.HrmsEntGradeDO;
import com.imile.hrms.dao.recruitment.dao.HrmsRecruitmentJobDao;
import com.imile.hrms.dao.recruitment.dao.HrmsRecruitmentJobHeadcountDao;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobDO;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountDO;
import com.imile.hrms.dao.recruitment.params.JobQuery;
import com.imile.hrms.dao.recruitment.params.JobStatusUpdateQuery;
import com.imile.hrms.manage.recruitment.RecruitmentJobManage;
import com.imile.idwork.IdWorkerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Slf4j
@Service
public class RecruitmentJobManageImpl implements RecruitmentJobManage {

    @Resource
    private HrmsRecruitmentJobDao jobDao;

    @Resource
    private HrmsRecruitmentJobHeadcountDao jobHeadcountDao;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveJob(Long jobId, HrmsRecruitmentJobDO jobDO, List<HrmsRecruitmentJobHeadcountDO> headcountDOList) {
        // 批量插入 headcount 表
        if (jobId != null && jobId > 0) {
            jobDao.updateById(jobDO);
        } else {
            jobDao.save(jobDO);
        }
        if (!CollectionUtil.isEmpty(headcountDOList)) {
            // 删除hc
            LambdaQueryWrapper<HrmsRecruitmentJobHeadcountDO> removeHcWrapper = new LambdaQueryWrapper<>();
            removeHcWrapper.eq(HrmsRecruitmentJobHeadcountDO::getRecruitmentJobId, jobId).
                    eq(HrmsRecruitmentJobHeadcountDO::getIsDelete, BusinessConstant.N);
            jobHeadcountDao.remove(removeHcWrapper);
            jobHeadcountDao.saveBatch(headcountDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeBackJob(Long jobId) {
        if (jobId == null || jobId <= 0) {
            return;
        }
        // 查询job
        LambdaQueryWrapper<HrmsRecruitmentJobDO> jobWrapper = new LambdaQueryWrapper<>();
        jobWrapper.eq(HrmsRecruitmentJobDO::getId, jobId).
                eq(HrmsRecruitmentJobDO::getIsDelete, BusinessConstant.N).
                eq(HrmsRecruitmentJobDO::getApproveStatus, HcApprovalInfoStatusEnum.BACK.getCode());
        HrmsRecruitmentJobDO jobDO = jobDao.getOne(jobWrapper);
        if (jobDO == null) {
            return;
        }
        // 删除job
        LambdaUpdateWrapper<HrmsRecruitmentJobDO> removeJobWrapper = new LambdaUpdateWrapper<>();
        removeJobWrapper.eq(HrmsRecruitmentJobDO::getId, jobDO.getId()).
                set(HrmsRecruitmentJobDO::getIsDelete, BusinessConstant.Y);
        jobDao.update(removeJobWrapper);
        // 删除hc
        LambdaUpdateWrapper<HrmsRecruitmentJobHeadcountDO> removeHcWrapper = new LambdaUpdateWrapper<>();
        removeHcWrapper.eq(HrmsRecruitmentJobHeadcountDO::getRecruitmentJobId, jobDO.getId()).
                set(HrmsRecruitmentJobHeadcountDO::getIsDelete, BusinessConstant.Y);
        jobHeadcountDao.update(removeHcWrapper);
    }


    @Override
    public List<HrmsRecruitmentJobDO> selectList(JobQuery query) {
        LambdaQueryWrapper<HrmsRecruitmentJobDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsRecruitmentJobDO::getIsDelete, BusinessConstant.N);
        queryWrapper.orderByDesc(HrmsRecruitmentJobDO::getCreateDate);
        if (query.getIsSysAdmin() != null && !query.getIsSysAdmin()) {
            // AND ( createUserCode = ? OR deptId in (?,?) OR bizCountry like “?”)
            queryWrapper.and(param -> param.
                    eq(HrmsRecruitmentJobDO::getCreateUserCode, query.getCreateUserCode()).
                    or(!CollectionUtil.isEmpty(query.getPermissionDeptIds()), i -> i.in(HrmsRecruitmentJobDO::getDeptId, query.getPermissionDeptIds())).
                    or(!StringUtils.isBlank(query.getPermissionCountry()), i -> i.like(HrmsRecruitmentJobDO::getBizCountry, query.getPermissionCountry()))
            );
        }
        if (!CollectionUtil.isEmpty(query.getIds())) {
            queryWrapper.in(HrmsRecruitmentJobDO::getId, query.getIds());
        }
        if (!CollectionUtil.isEmpty(query.getStatus())) {
            queryWrapper.in(HrmsRecruitmentJobDO::getStatus, query.getStatus());
        }
        if (!CollectionUtil.isEmpty(query.getRecruitmentTypes())) {
            queryWrapper.in(HrmsRecruitmentJobDO::getRecruitmentType, query.getRecruitmentTypes());
        }
        if (!StringUtils.isBlank(query.getQueryCreateUserCode())) {
            queryWrapper.eq(HrmsRecruitmentJobDO::getCreateUserCode, query.getQueryCreateUserCode());
        }
        if (!StringUtils.isBlank(query.getCountry())) {
            queryWrapper.like(HrmsRecruitmentJobDO::getBizCountry, query.getCountry());
        }
        if (!CollectionUtil.isEmpty(query.getDeptIds())) {
            queryWrapper.in(HrmsRecruitmentJobDO::getDeptId, query.getDeptIds());
        }
        if (!StringUtils.isBlank(query.getApproveCode())) {
            queryWrapper.like(HrmsRecruitmentJobDO::getApplicationCode, query.getApproveCode());
        }
        if (!StringUtils.isEmpty(query.getPositionName())) {
            queryWrapper.like(HrmsRecruitmentJobDO::getJobName, query.getPositionName());
        }
        if (!StringUtils.isEmpty(query.getApplyUserName())) {
            if (RequestInfoHolder.isChinese()) {
                queryWrapper.like(HrmsRecruitmentJobDO::getApplyUserName, query.getApplyUserName());
            } else {
                queryWrapper.like(HrmsRecruitmentJobDO::getApplyUserNameEn, query.getApplyUserName());
            }
        }
        if (!CollectionUtil.isEmpty(query.getApproveStatus())) {
            queryWrapper.in(HrmsRecruitmentJobDO::getApproveStatus, query.getApproveStatus());
        }
        if (query.getApplicationCreateStartTime() != null && query.getApplicationCreateEndTime() != null) {
            queryWrapper.between(HrmsRecruitmentJobDO::getApproveCreateTime, query.getApplicationCreateStartTime(), query.getApplicationCreateEndTime());
        }
        if (query.getApprovedStartDate() != null && query.getApprovedEndDate() != null) {
            queryWrapper.between(HrmsRecruitmentJobDO::getApprovedTime, query.getApprovedStartDate(), query.getApprovedEndDate());
        }
        return jobDao.list(queryWrapper);
    }

    @Override
    public HrmsRecruitmentJobDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return jobDao.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusById(JobStatusUpdateQuery query) {
        if (query == null || query.getJobId() == null || query.getApprovalStatus() == null) {
            return false;
        }
        LambdaUpdateWrapper<HrmsRecruitmentJobDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(HrmsRecruitmentJobDO::getApproveStatus, query.getApprovalStatus());
        queryWrapper.set(HrmsRecruitmentJobDO::getLastUpdDate, new Date());
        queryWrapper.set(HrmsRecruitmentJobDO::getLastUpdUserCode, RequestInfoHolder.getUserCode());
        queryWrapper.set(HrmsRecruitmentJobDO::getLastUpdUserName, RequestInfoHolder.getUserName());
        queryWrapper.eq(HrmsRecruitmentJobDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(HrmsRecruitmentJobDO::getId, query.getJobId());
        return jobDao.update(queryWrapper);
    }

    @Override
    public Boolean updateFeishuJobIdById(Long jobId, String feishuJobId) {
        log.info("createFeishuJob,  jobId:{}, feishuJobId: {}", jobId, feishuJobId);
        if (jobId == null || StringUtils.isBlank(feishuJobId)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<HrmsRecruitmentJobDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(HrmsRecruitmentJobDO::getFsJobId, feishuJobId);
        queryWrapper.set(HrmsRecruitmentJobDO::getLastUpdDate, new Date());
        queryWrapper.eq(HrmsRecruitmentJobDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(HrmsRecruitmentJobDO::getId, jobId);
        return jobDao.update(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateJobAndHc(HrmsRecruitmentJobDO jobDO, List<HrmsRecruitmentJobHeadcountDO> headcountDOList) {
        if (jobDO == null || jobDO.getId() == null) {
            return Boolean.FALSE;
        }
        if (headcountDOList.stream().anyMatch(e -> e == null || e.getId() == null)) {
            return Boolean.FALSE;
        }
        return jobDao.updateById(jobDO) && jobHeadcountDao.updateBatchById(headcountDOList);
    }

}
