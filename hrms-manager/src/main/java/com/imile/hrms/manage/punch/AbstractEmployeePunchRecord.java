package com.imile.hrms.manage.punch;

import com.imile.hrms.common.enums.attendance.AttendanceDayTypeEnum;
import com.imile.hrms.common.enums.punch.LocationResultEnum;
import com.imile.hrms.common.enums.punch.TimeResultEnum;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.punch.model.HrmsAttendanceEmployeePunchDayDO;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/7
 */
public abstract class AbstractEmployeePunchRecord {

    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;

    /**
     * 员工打卡
     */
    //public abstract void employeePunch(UserPunchParam punchParam, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO);

//    /**
//     * 补卡
//     *
//     * @param attendanceHelpDTO
//     * @param userId
//     */
//    public abstract void supplement(EffectiveAttendanceHelpDTO attendanceHelpDTO, Long userId, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, Date supplementDate);

    /**
     * 打卡类型，上班，下班
     *
     * @return
     */
    public abstract String punchType();

    /**
     * 是否工作日
     *
     * @param attendanceDayTypeEnum
     * @return
     */
    protected boolean isPresentDay(AttendanceDayTypeEnum attendanceDayTypeEnum) {
        return AttendanceDayTypeEnum.PRESENT.equals(attendanceDayTypeEnum);
    }


    /**
     * 校验是否符合可以打卡的条件
     */
   /* protected boolean checkPunchCondition(UserPunchParam punchParam, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, HrmsAttendanceEmployeePunchRecordDO model) {
        if (hrmsAttendancePunchConfigDetailDTO == null) {
            return Boolean.TRUE;
        }
        //若为外勤打卡，则无需校验，直接通过即可
        if (Boolean.FALSE.equals(punchParam.getOutsideOffice())) {
            List<HrmsAttendancePunchWayConfigDTO> punchWayDetail = hrmsAttendancePunchConfigDetailDTO.getPunchWayDetail();
            //遍历
            for (HrmsAttendancePunchWayConfigDTO punchWayConfigDTO : punchWayDetail) {
                //获取wifi打卡地点打卡处理器
                HrmsPunchWayStrategy punchWayStrategy = HrmsPunchWayFactory.getPunchWayStrategy(punchWayConfigDTO.getPunchWayType());
                if (punchWayStrategy != null && punchWayStrategy.checkPunchRange(punchParam, punchWayConfigDTO, model)) {
                    model.setLocationResult(LocationResultEnum.NORMAL.name());
                    return Boolean.TRUE;
                }
            }
            model.setLocationResult(LocationResultEnum.OUTSIDE.name());
            //校验不通过可以直接返回false
            return Boolean.FALSE;
        }
        //外勤打卡无需校验距离或wifi
        model.setLocationResult(LocationResultEnum.OUTSIDE.name());
        return Boolean.TRUE;

    }*/

//    /**
//     * 获取基础每日打卡类
//     *
//     * @param attendanceHelpDTO
//     * @param userId
//     * @param model
//     * @param punchDayDO
//     */
//    protected void getPunchDayBaseDO(EffectiveAttendanceHelpDTO attendanceHelpDTO, Long userId, HrmsAttendanceEmployeePunchRecordDO model, HrmsAttendanceEmployeePunchDayDO punchDayDO) {
//
//        punchDayDO.setUserId(userId);
//        punchDayDO.setDayId(model.getDayId());
//        punchDayDO.setDayType(Optional.ofNullable(attendanceHelpDTO).map(EffectiveAttendanceHelpDTO::getDayType).orElse(null));
//        punchDayDO.setPunchConfigId(model.getPunchConfigId());
//        punchDayDO.setCompanyId(RequestInfoHolder.getCompanyId());
//
//
//    }

    /**
     * 封装员工落库数据
     */
   /* protected HrmsAttendanceEmployeePunchRecordDO getPunchRecordDO(UserPunchParam punchParam, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, EffectiveAttendanceHelpDTO dayId, HrmsAttendanceEmployeePunchRecordDO model) {

        BeanUtil.copyProperties(hrmsAttendancePunchConfigDetailDTO, model);
        //用户实际打卡地址
        model.setUserAddress(punchParam.getPlaceName());
        //打卡方式
        if (hrmsAttendancePunchConfigDetailDTO != null) {
            List<HrmsAttendancePunchWayConfigDTO> punchWayDetail = hrmsAttendancePunchConfigDetailDTO.getPunchWayDetail();
            HrmsAttendancePunchWayConfigDTO punchWayConfigDTO = punchWayDetail.get(0);
            model.setPunchWayConfigId(Optional.ofNullable(punchWayConfigDTO).map(HrmsAttendancePunchWayConfigDTO::getId).orElse(null));
        }
        //用户实际打卡时间
        model.setUserPunchTime(DateUtil.offsetHour(punchParam.getPunchTime(), BusinessConstant.DEFAULT_TIMEZONE - RequestInfoHolder.getTimeZoneOffset()));
        //来源
        model.setSourceType(SourceTypeEnum.DRIVER_APP.name());
        model.setPunchConfigId(Optional.ofNullable(hrmsAttendancePunchConfigDetailDTO).map(HrmsAttendancePunchConfigDetailDTO::getId).orElse(null));
        //dayId
        model.setDayId(Optional.ofNullable(dayId).map(EffectiveAttendanceHelpDTO::getDayId).orElse(null));
        model.setUserId(punchParam.getUserId());
        model.setDeviceId(punchParam.getDeviceId());
        //是否最新
        model.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(model);
        model.setId(iHrmsIdWorker.nextId());
        model.setCompanyId(RequestInfoHolder.getCompanyId());

        return model;
    }*/

    /**
     * 获取该员工改天的属性及日期
     */
    /*protected EffectiveAttendanceHelpDTO getDayId(HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, UserPunchParam punchParam) {
        if (hrmsAttendancePunchConfigDetailDTO == null) {
            EffectiveAttendanceHelpDTO effectiveAttendanceHelpDTO = getEffectiveAttendanceHelpDTO(punchParam.getPunchTime(), punchParam.getUserId());
            return effectiveAttendanceHelpDTO;
        }
        String punchConfigType = hrmsAttendancePunchConfigDetailDTO.getPunchConfigType();
        AbstractAttendancePunchTypeStrategy punchTypeStrategy = AttendancePunchTypeFactory.getInstance(punchConfigType);
        if (punchTypeStrategy == null) {
            EffectiveAttendanceHelpDTO effectiveAttendanceHelpDTO = getEffectiveAttendanceHelpDTO(punchParam.getPunchTime(), punchParam.getUserId());
            return effectiveAttendanceHelpDTO;
        }
        return punchTypeStrategy.getDayId(hrmsAttendancePunchConfigDetailDTO, punchParam.getPunchTime(), punchParam.getUserId());
    }*/

    /**
     * 为空时返回当前dayId
     *
     * @param date
     * @param userId
     * @return
     */
    /*private EffectiveAttendanceHelpDTO getEffectiveAttendanceHelpDTO(Date date, Long userId) {
        EffectiveAttendanceHelpDTO effectiveAttendanceHelpDTO = new EffectiveAttendanceHelpDTO();
        //dayId
        effectiveAttendanceHelpDTO.setDayId(Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN)));
        //查询当天的日期属性
        HrmsAttendanceConfigDetailDO attendanceConfig = hrmsAttendanceConfigManage.getLatestAttendanceConfig(userId, date,RequestInfoHolder.getCompanyId());
        effectiveAttendanceHelpDTO.setIsNoPunchConfig(Boolean.FALSE);
        //日期属性
        effectiveAttendanceHelpDTO.setDayType(Optional.ofNullable(attendanceConfig).map(HrmsAttendanceConfigDetailDO::getDayType).orElse(AttendanceDayTypeEnum.PRESENT.name()));
        return effectiveAttendanceHelpDTO;
    }
*/
//    /**
//     * 获取BaseRecordDO
//     *
//     * @param attendanceHelpDTO
//     * @param userId
//     * @param hrmsAttendancePunchConfigDetailDTO
//     * @return
//     */
//    protected HrmsAttendanceEmployeePunchRecordDO getHrmsBaseRecordDO(EffectiveAttendanceHelpDTO attendanceHelpDTO, Long userId, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, Date supplementDate) {
//        //新增一条打卡数据
//        HrmsAttendanceEmployeePunchRecordDO model = new HrmsAttendanceEmployeePunchRecordDO();
//        model.setPunchConfigId(hrmsAttendancePunchConfigDetailDTO.getId());
//        model.setId(iHrmsIdWorker.nextId());
//        model.setUserPunchTime(DateUtil.offsetHour(supplementDate, BusinessConstant.DEFAULT_TIMEZONE - RequestInfoHolder.getTimeZoneOffset()));
//        model.setUserId(userId);
//        model.setDayId(attendanceHelpDTO.getDayId());
//        model.setIsLatest(BusinessConstant.Y);
//        model.setCompanyId(RequestInfoHolder.getCompanyId());
//        model.setPunchConfigName(hrmsAttendancePunchConfigDetailDTO.getPunchConfigName());
//        model.setSourceType(SourceTypeEnum.SYSTEM.name());
//        //打卡方式
//        List<HrmsAttendancePunchWayConfigDTO> punchWayDetail = hrmsAttendancePunchConfigDetailDTO.getPunchWayDetail();
//        HrmsAttendancePunchWayConfigDTO punchWayConfigDTO = punchWayDetail.get(0);
//
//        model.setLocationResult(TimeResultEnum.NORMAL.name());
//        model.setPunchWayConfigId(Optional.ofNullable(punchWayConfigDTO).map(HrmsAttendancePunchWayConfigDTO::getId).orElse(null));
//        BaseDOUtil.fillDOInsert(model);
//        return model;
//    }

    protected boolean isNormal(HrmsAttendanceEmployeePunchDayDO punchDayDO) {
        boolean punchOutTimeResult = TimeResultEnum.NORMAL.name().equals(punchDayDO.getPunchOutTimeResult());
        boolean punchInTimeResult = TimeResultEnum.NORMAL.name().equals(punchDayDO.getPunchInTimeResult());
        boolean punchOutLocationResult = LocationResultEnum.NORMAL.name().equals(punchDayDO.getPunchOutLocationResult());
        boolean punchInLocationResult = LocationResultEnum.NORMAL.name().equals(punchDayDO.getPunchInLocationResult());

        boolean isNormal = punchOutTimeResult && punchInTimeResult && punchOutLocationResult && punchInLocationResult;
        return isNormal;
    }


}
