package com.imile.hrms.manage.punch.impl;

import com.imile.hrms.dao.punch.dao.HrmsAttendanceUserCardConfigDao;
import com.imile.hrms.dao.punch.model.HrmsAttendanceUserCardConfigDO;
import com.imile.hrms.manage.punch.HrmsAttendanceUserCardConfigManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-19
 * @version: 1.0
 */
@Service
public class HrmsAttendanceUserCardConfigManageImpl implements HrmsAttendanceUserCardConfigManage {
    @Autowired
    private HrmsAttendanceUserCardConfigDao hrmsAttendanceUserCardConfigDao;

    @Override
    public List<HrmsAttendanceUserCardConfigDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendanceUserCardConfigDao.selectByUserIdList(userIdList);
    }

    @Override
    public void batchUpdateById(List<HrmsAttendanceUserCardConfigDO> userCardConfigDOList) {
        if (CollectionUtils.isEmpty(userCardConfigDOList)) {
            return;
        }
        hrmsAttendanceUserCardConfigDao.updateBatchById(userCardConfigDOList);
    }

    @Override
    public void batchSave(List<HrmsAttendanceUserCardConfigDO> userCardConfigDOList) {
        if (CollectionUtils.isEmpty(userCardConfigDOList)) {
            return;
        }
        hrmsAttendanceUserCardConfigDao.saveBatch(userCardConfigDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initCardUpdate(List<HrmsAttendanceUserCardConfigDO> addUserCardConfigDOList, List<HrmsAttendanceUserCardConfigDO> updateUserCardConfigDOList) {
        if (CollectionUtils.isNotEmpty(addUserCardConfigDOList)) {
            hrmsAttendanceUserCardConfigDao.saveBatch(addUserCardConfigDOList);
        }
        if (CollectionUtils.isNotEmpty(updateUserCardConfigDOList)) {
            hrmsAttendanceUserCardConfigDao.updateBatchById(updateUserCardConfigDOList);
        }
    }
}
