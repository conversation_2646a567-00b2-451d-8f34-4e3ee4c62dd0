package com.imile.hrms.manage.resource;

import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.enums.AttributeTypeEnum;
import com.imile.hrms.dao.organization.query.CompanyQuery;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * 处理系统用户的数据权限
 * <AUTHOR>
 */
public interface SystemResourceManage {
    /**
     * 设置查询资源权限
     * @param t
     * @param setterType
     * @param setOrg
     * @param <T>
     */
    <T> void setResource(T t, BiConsumer<T, String> setterType, BiConsumer<T, List<Long>> setOrg);

    /**
     * 设置资源权限
     * @param attributeTypeEnum
     * @param userContext
     */
    void setResource( AttributeTypeEnum attributeTypeEnum, UserContext userContext);

    /**
     * 设置权限，从权限系统获取对应权限，设置到 organizationIds 中
     */
    void setResource();

}
