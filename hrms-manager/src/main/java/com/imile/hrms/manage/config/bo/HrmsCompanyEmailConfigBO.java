package com.imile.hrms.manage.config.bo;

import com.imile.hrms.dao.config.model.HrmsUserChangeEmailConfigDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HrmsCompanyEmailConfigBO implements Serializable {
    private static final long serialVersionUID = -7301527020467320254L;

    private Integer id;

    /**
     * 国家
     */
    private String country;

    /**
     * 计薪天数
     */
    private Long salaryDays;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;


    List<HrmsUserChangeEmailConfigDO> emailConfigDOList;
}
