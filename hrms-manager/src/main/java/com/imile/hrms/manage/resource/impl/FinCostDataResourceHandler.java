package com.imile.hrms.manage.resource.impl;

import com.imile.hrms.common.enums.AttributeTypeEnum;
import com.imile.hrms.common.enums.ResourceTypeEnum;
import com.imile.hrms.manage.resource.ResourceHandlerAbstract;
import org.springframework.stereotype.Component;

/**
 * FIN-Cost data管理员数据权限处理
 *
 * <AUTHOR>
 */
@Component
public class FinCostDataResourceHandler extends ResourceHandlerAbstract {

    @Override
    public String getResourceHandler() {
        return AttributeTypeEnum.FIN_COST_DATA.getCode();
    }

    @Override
    public ResourceTypeEnum getResourceType() {
        return ResourceTypeEnum.DEPT_ID;
    }
}
