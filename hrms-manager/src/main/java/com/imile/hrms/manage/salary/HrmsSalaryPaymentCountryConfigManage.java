package com.imile.hrms.manage.salary;

import com.imile.hrms.dao.salary.model.HrmsSalaryPaymentCountryConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryUserRoleDO;
import com.imile.hrms.dao.salary.query.SalaryPaymentCountryQuery;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/4/1 14:40
 * @version: 1.0
 */
public interface HrmsSalaryPaymentCountryConfigManage {

    List<HrmsSalaryPaymentCountryConfigDO> selectPaymentCountryConfigList(SalaryPaymentCountryQuery query);

    HrmsSalaryPaymentCountryConfigDO getById(Long id);

    void saveBatch(List<HrmsSalaryPaymentCountryConfigDO> addList);

    void updateBatch(List<HrmsSalaryPaymentCountryConfigDO> updateList);

    void paymentCountryDisabledUpdate(HrmsSalaryPaymentCountryConfigDO updatePaymentCountry, List<HrmsSalaryUserRoleDO> updateUserRoleDOList);

}
