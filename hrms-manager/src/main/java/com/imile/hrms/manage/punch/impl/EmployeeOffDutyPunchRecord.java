package com.imile.hrms.manage.punch.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.entity.DateDO;
import com.imile.hrms.common.enums.attendance.AttendanceDayTypeEnum;
import com.imile.hrms.common.enums.punch.AttendancePunchTypeEnum;
import com.imile.hrms.common.enums.punch.LocationResultEnum;
import com.imile.hrms.common.enums.punch.PunchTypeEnum;
import com.imile.hrms.common.enums.punch.TimeResultEnum;
import com.imile.hrms.common.util.BigDecimalUtil;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.punch.dao.HrmsAttendanceEmployeePunchDayDao;
import com.imile.hrms.dao.punch.dao.HrmsAttendanceEmployeePunchRecordDao;
import com.imile.hrms.dao.punch.dto.EffectiveAttendanceHelpDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchClassConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigDetailDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchOverTimeConfigDTO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceEmployeePunchDayDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceEmployeePunchRecordDO;
import com.imile.hrms.dao.punch.param.UserPunchParam;
import com.imile.hrms.manage.punch.AbstractEmployeePunchRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/7
 */
@Service
public class EmployeeOffDutyPunchRecord extends AbstractEmployeePunchRecord {
    @Autowired
    protected IHrmsIdWorker iHrmsIdWorker;

    @Autowired
    private HrmsAttendanceEmployeePunchRecordDao hrmsAttendanceEmployeePunchRecordDao;

    @Autowired
    private HrmsAttendanceEmployeePunchDayDao hrmsAttendanceEmployeePunchDayDao;

    @Autowired
    private HrmsAttendanceEmployeeDetailDao hrmsAttendanceEmployeeDetailDao;

    //默认小数点位数
    private static final int DEFAULT_SCARE = 4;
    //计算出勤基数
    private static final int BASE_VALUE = 4;


/*    @Override
    @Transactional(rollbackFor = Exception.class)
    public void employeePunch(UserPunchParam punchParam, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO) {

        //判断打卡落在哪一天
        EffectiveAttendanceHelpDTO dayId = getDayId(hrmsAttendancePunchConfigDetailDTO, punchParam);
        //获取这一天的打卡记录
        HrmsAttendanceEmployeePunchDayDO punchDayDO = hrmsAttendanceEmployeePunchDayDao.getAttendanceEmployeePunchDayDO(punchParam.getUserId(), Optional.ofNullable(dayId).map(EffectiveAttendanceHelpDTO::getDayId).orElse(null),RequestInfoHolder.getCompanyId());

        AttendancePunchRecordQuery punchRecordQuery = AttendancePunchRecordQuery.builder().userId(punchParam.getUserId()).companyId(RequestInfoHolder.getCompanyId()).dayId(Optional.ofNullable(dayId).map(EffectiveAttendanceHelpDTO::getDayId).orElse(null)).build();
        List<HrmsAttendanceEmployeePunchRecordDO> punchRecordDOList = hrmsAttendanceEmployeePunchRecordDao.getRecordList(punchRecordQuery);
        //判断是不是能进行下班打卡
        //1、已经有上班卡记录
        //2、已经到了配置的下班时间
        BusinessLogicException.checkTrue(!checkPunchOutPermission(punchParam, dayId, punchDayDO, punchRecordDOList, hrmsAttendancePunchConfigDetailDTO), HrmsErrorCodeEnums.PUNCH_OUT_NOT_PERMISSION.getCode(), HrmsErrorCodeEnums.PUNCH_OUT_NOT_PERMISSION.getDesc());
        //生成记录落库类
        HrmsAttendanceEmployeePunchRecordDO model = BeanUtil.copyProperties(punchParam, HrmsAttendanceEmployeePunchRecordDO.class);
        model.setCheckType(PunchTypeEnum.OUT_DUTY.getCode());
        //校验员工是否符合打卡条件
        BusinessLogicException.checkTrue(!checkPunchCondition(punchParam, hrmsAttendancePunchConfigDetailDTO, model), HrmsErrorCodeEnums.PUNCH_RANGE_ERROR.getCode(), HrmsErrorCodeEnums.PUNCH_RANGE_ERROR.getDesc());
        //判断员工是否是否早退
        checkLateOrEarly(punchParam, model, dayId, hrmsAttendancePunchConfigDetailDTO, punchDayDO);
        //封装员工打卡数据
        getPunchRecordDO(punchParam, hrmsAttendancePunchConfigDetailDTO, dayId, model);
        //打卡数据落库
        hrmsAttendanceEmployeePunchRecordDao.save(model);
        //更新该员工该天打卡数据
        punchDayDO = getHrmsAttendanceEmployeePunchDayDO(punchParam, dayId, punchDayDO, model, punchRecordDOList, hrmsAttendancePunchConfigDetailDTO);
        hrmsAttendanceEmployeePunchDayDao.saveOrUpdate(punchDayDO);
    }*/

//    @Override
//    public void supplement(EffectiveAttendanceHelpDTO attendanceHelpDTO, Long userId, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, Date supplementDate) {
//        HrmsAttendanceEmployeePunchRecordDO model = getHrmsBaseRecordDO(attendanceHelpDTO, userId, hrmsAttendancePunchConfigDetailDTO, supplementDate);
//        model.setCheckType(PunchTypeEnum.OUT_DUTY.getCode());
//        HrmsAttendanceEmployeePunchDayDO punchDayDO = hrmsAttendanceEmployeePunchDayDao.getAttendanceEmployeePunchDayDO(userId, Optional.ofNullable(attendanceHelpDTO).map(EffectiveAttendanceHelpDTO::getDayId).orElse(null),RequestInfoHolder.getCompanyId());
//        if(AttendancePunchTypeEnum.FREE_WORK.name().equals(hrmsAttendancePunchConfigDetailDTO.getPunchConfigType())){
//            //判断是否早退
//            Date punchInTime = punchDayDO.getPunchInTime();
//            BigDecimal diffHour = HrmsDateUtil.diffHour(supplementDate, punchInTime);
//            BigDecimal punchTimeInterval = hrmsAttendancePunchConfigDetailDTO.getPunchTimeConfig().get(0).getClassItemConfigList().get(0).getPunchTimeInterval();
//            model.setTimeResult(diffHour.compareTo(punchTimeInterval)>=0 ? TimeResultEnum.NORMAL.name() : TimeResultEnum.EARLY.name());
//        }else{
//            model.setTimeResult(supplementDate.after(attendanceHelpDTO.getConfigPunchOutTime()) ? TimeResultEnum.NORMAL.name() : TimeResultEnum.EARLY.name());
//        }
//
//        //获取每日打卡的封装类
//        HrmsAttendanceEmployeePunchDayDO punchDayModel = getHrmsAttendanceEmployeePunchDayDO(attendanceHelpDTO, userId, model, supplementDate,punchDayDO);
//        //打卡数据落库
//        hrmsAttendanceEmployeePunchRecordDao.save(model);
//        hrmsAttendanceEmployeePunchDayDao.saveOrUpdate(punchDayModel);
//    }

//    /**
//     * 封装每日打卡结果类
//     *
//     * @param attendanceHelpDTO
//     * @param userId
//     * @param model
//     * @return
//     */
//    private HrmsAttendanceEmployeePunchDayDO getHrmsAttendanceEmployeePunchDayDO(EffectiveAttendanceHelpDTO attendanceHelpDTO, Long userId, HrmsAttendanceEmployeePunchRecordDO model, Date supplementDate,HrmsAttendanceEmployeePunchDayDO punchDayDO) {
//        //获取这一天的打卡记录
//
//        if (punchDayDO == null) {
//            punchDayDO = new HrmsAttendanceEmployeePunchDayDO();
//            getPunchDayBaseDO(attendanceHelpDTO, userId, model, punchDayDO);
//            //本日打卡结果
//            punchDayDO.setPunchResult(PunchResultEnum.ABNORMAL.name());
//            //配置下班打卡时间
//            punchDayDO.setConfigPunchOutTime(DateUtil.offsetHour(attendanceHelpDTO.getConfigPunchOutTime(), BusinessConstant.DEFAULT_TIMEZONE - RequestInfoHolder.getTimeZoneOffset()));
//            //本次id
//            punchDayDO.setId(iHrmsIdWorker.nextId());
//
//            setPunchOutResult(model, punchDayDO);
//
//            BaseDOUtil.fillDOInsert(punchDayDO);
//
//        } else {
//            getPunchDayBaseDO(attendanceHelpDTO, userId, model, punchDayDO);
//            //配置上班打卡时间
//            punchDayDO.setConfigPunchOutTime(Optional.ofNullable(attendanceHelpDTO).map(HrmsDateUtil.getOffsetDate(EffectiveAttendanceHelpDTO::getConfigPunchOutTime)).orElse(null));
//
//            //配置下班打卡时间
//            if (punchDayDO.getPunchOutTime() == null || supplementDate.after(punchDayDO.getPunchOutTime())) {
//                //更新数据
//                setPunchOutResult(model, punchDayDO);
//            }
//
//            BaseDOUtil.fillDOUpdate(punchDayDO);
//        }
//        //本日打卡结果,查看下班卡是否正常，若下班卡正常，则为正常数据
//        boolean isNormal = isNormal(punchDayDO);
//        punchDayDO.setPunchResult(isNormal ? PunchResultEnum.NORMAL.name() : PunchResultEnum.ABNORMAL.name());
//        return punchDayDO;
//    }

    /**
     * 设置打卡状态
     *
     * @param model
     * @param punchDayDO
     */
    private void setPunchOutResult(HrmsAttendanceEmployeePunchRecordDO model, HrmsAttendanceEmployeePunchDayDO punchDayDO) {
        //下班打卡id
        punchDayDO.setPunchInRecordId(model.getId());
        //下班打卡时间
        punchDayDO.setPunchOutTime(DateUtil.offsetHour(model.getUserPunchTime(), BusinessConstant.DEFAULT_TIMEZONE - RequestInfoHolder.getTimeZoneOffset()));
        //s上班打卡结果
        punchDayDO.setPunchOutTimeResult(TimeResultEnum.NORMAL.name().equals(punchDayDO.getPunchOutTimeResult()) ? TimeResultEnum.NORMAL.name() : model.getTimeResult());
        punchDayDO.setPunchOutLocationResult(model.getLocationResult());
    }

    /**
     * 考勤类
     *
     * @param punchParam
     * @param hrmsAttendancePunchConfigDetailDTO
     * @param dayId
     * @param punchDayDO
     */
    private HrmsAttendanceEmployeeDetailDO attendanceUpdate(UserPunchParam punchParam, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, EffectiveAttendanceHelpDTO dayId, HrmsAttendanceEmployeePunchDayDO punchDayDO) {
        //计算当日工作时长
        BigDecimal workTime = HrmsDateUtil.diffHour(punchDayDO.getPunchOutTime(), punchDayDO.getPunchInTime());
        //获取当日打卡为多少p
        BigDecimal presentRate = getPresentRate(workTime, dayId);
        //获取加班时长
        BigDecimal overtime = overtime(punchParam, hrmsAttendancePunchConfigDetailDTO, dayId, punchDayDO);
        return getHrmsAttendanceEmployeeDetailDO(presentRate, punchParam, dayId, overtime, punchDayDO, workTime);

    }

    /**
     * 封装考勤落库数据
     *
     * @param presentRate
     * @param punchParam
     * @param dayId
     * @param overtime
     * @param attendanceEmployeePunchDayDO
     */
    private HrmsAttendanceEmployeeDetailDO getHrmsAttendanceEmployeeDetailDO(BigDecimal presentRate, UserPunchParam punchParam, EffectiveAttendanceHelpDTO dayId, BigDecimal overtime, HrmsAttendanceEmployeePunchDayDO attendanceEmployeePunchDayDO, BigDecimal workTime) {
        HrmsAttendanceEmployeeDetailDO model = hrmsAttendanceEmployeeDetailDao.getHrmsAttendanceEmployeeDetailDO(punchParam.getUserId(), dayId.getDayId());
        if (model == null) {
            //获取改天的年月日
            DateDO dateByDayId = HrmsDateUtil.getDateByDayId(dayId.getDayId());
            model = BeanUtil.copyProperties(dateByDayId, HrmsAttendanceEmployeeDetailDO.class);
            model.setUserId(punchParam.getUserId());
            model.setDayId(dayId.getDayId());

        } else {
            BaseDOUtil.fillDOUpdate(model);
        }
        //考勤开始时间
        model.setAttendanceStartTime(attendanceEmployeePunchDayDO.getPunchInTime());
        model.setIsAttendance(BusinessConstant.Y);
        //考勤结束时间
        model.setAttendanceEndTime(attendanceEmployeePunchDayDO.getPunchOutTime());
        //工作时长
        model.setAttendanceHours(workTime);
        //加班小时数
        model.setOvertimeHours(overtime);
        //当天出勤率
        model.setAttendanceRate(presentRate);
        return model;
    }

    /**
     * 出勤时长计算
     *
     * @param workTime
     * @param dayId
     * @return
     */
    private BigDecimal getPresentRate(BigDecimal workTime, EffectiveAttendanceHelpDTO dayId) {
        if (dayId == null) {
            return BigDecimal.ZERO;
        }
        //原定工作时长
        BigDecimal beDivided = HrmsDateUtil.diffHour(dayId.getConfigPunchOutTime(), dayId.getConfigPunchInTime());
        //计算p
        BigDecimal presentRate = BigDecimalUtil.divide(workTime, beDivided, DEFAULT_SCARE);
        presentRate = presentRate.compareTo(BigDecimal.ONE) > 0 ? BigDecimal.ONE : presentRate;
        //向下取整 presentRate 值只能取0、0.25、0.5、0.75、1
        return BigDecimal.valueOf(Math.floor(presentRate.doubleValue() * BASE_VALUE) / BASE_VALUE);

    }


    /**
     * @param punchParam
     * @param dayId
     * @param punchDayDO
     * @param model
     * @param punchRecordDOList
     * @return
     */
  /*  private HrmsAttendanceEmployeePunchDayDO getHrmsAttendanceEmployeePunchDayDO(UserPunchParam punchParam, EffectiveAttendanceHelpDTO dayId, HrmsAttendanceEmployeePunchDayDO punchDayDO, HrmsAttendanceEmployeePunchRecordDO model, List<HrmsAttendanceEmployeePunchRecordDO> punchRecordDOList, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO) {
        //上班卡是否缺勤

        if (punchDayDO == null) {
            punchDayDO = new HrmsAttendanceEmployeePunchDayDO();
            punchDayDO.setId(iHrmsIdWorker.nextId());
            punchDayDO.setUserId(punchParam.getUserId());
            if (hrmsAttendancePunchConfigDetailDTO != null) {
                punchDayDO.setPunchConfigId(hrmsAttendancePunchConfigDetailDTO.getId());
            }
            //日期属性
            punchDayDO.setDayType(Optional.ofNullable(dayId).map(EffectiveAttendanceHelpDTO::getDayType).orElse(null));
            punchDayDO.setDayId(model.getDayId());
            //配置上班时间
            punchDayDO.setConfigPunchInTime(Optional.ofNullable(dayId).map(HrmsDateUtil.getOffsetDate(EffectiveAttendanceHelpDTO::getConfigPunchInTime)).orElse(null));

            punchDayDO.setPunchInTimeResult(TimeResultEnum.NOT_SIGNED.name());
            punchDayDO.setPunchInLocationResult(LocationResultEnum.NOT_SIGNED.name());

            BaseDOUtil.fillDOInsert(punchDayDO);
        } else {



            BaseDOUtil.fillDOUpdate(punchDayDO);
        }
        punchDayDO.setIsLatest(BusinessConstant.Y);
        //本次下班打卡结果
        punchDayDO.setPunchOutTimeResult(model.getTimeResult());
        punchDayDO.setPunchOutLocationResult(model.getLocationResult());
        //配置下班打卡时间
        punchDayDO.setConfigPunchOutTime(Optional.ofNullable(dayId).map(HrmsDateUtil.getOffsetDate(EffectiveAttendanceHelpDTO::getConfigPunchOutTime)).orElse(null));
        //下班打卡时间
        punchDayDO.setPunchOutTime(DateUtil.offsetHour(model.getUserPunchTime(), BusinessConstant.DEFAULT_TIMEZONE - RequestInfoHolder.getTimeZoneOffset()));
        //下班打卡recordid
        punchDayDO.setPunchOutRecordId(model.getId());
        //公司id
        punchDayDO.setCompanyId(RequestInfoHolder.getCompanyId());
        //本日打卡结果
        if(punchDayDO == null){
            punchDayDO.setPunchResult(PunchResultEnum.ABNORMAL.name());
        }else{
            boolean isNormal = isNormal(punchDayDO);
            punchDayDO.setPunchResult(isNormal ? PunchResultEnum.NORMAL.name() : PunchResultEnum.ABNORMAL.name());
        }

        //位置结果
        //punchDayDO.setPunchResult();
        return punchDayDO;
    }*/



    private boolean isNormal(HrmsAttendanceEmployeePunchRecordDO model) {
        return LocationResultEnum.NORMAL.name().equals(model.getLocationResult()) && TimeResultEnum.NORMAL.name().equals(model.getTimeResult());
    }


    /**
     * 获取加班时长
     *
     * @param punchParam
     * @param hrmsAttendancePunchConfigDetailDTO
     * @param dayId
     * @return
     */
    private BigDecimal overtime(UserPunchParam punchParam, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO, EffectiveAttendanceHelpDTO dayId, HrmsAttendanceEmployeePunchDayDO attendanceEmployeePunchDayDO) {

        //加班配置
        HrmsAttendancePunchOverTimeConfigDTO overtimeConfig = hrmsAttendancePunchConfigDetailDTO.getOvertimeConfig();
        if (dayId == null) {
            return BigDecimal.ZERO;
        }
        //若今天是工作日
        if (AttendanceDayTypeEnum.PRESENT.name().equals(dayId.getDayType())) {
            Date date = HrmsDateUtil.offsetHour(dayId.getConfigPunchOutTime(), overtimeConfig.getWorkingOutStartTime());
            //获取下班时间判断是否加班
            BigDecimal overtimeHour = HrmsDateUtil.diffHour(punchParam.getPunchTime(), date);
            //返回休息日加班时间
            return overtimeHour.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : overtimeHour.compareTo(overtimeConfig.getWorkingEffectiveTime()) > 0 ? overtimeConfig.getWorkingEffectiveTime() : overtimeHour;
        } else {
            //若今天不是休息天
            //获取上班打卡时间
            DateTime punchInTime = DateUtil.offsetDay(attendanceEmployeePunchDayDO.getPunchInTime(), RequestInfoHolder.getTimeZoneOffset() - BusinessConstant.DEFAULT_TIMEZONE);
            BigDecimal overtimeHour = HrmsDateUtil.diffHour(punchParam.getPunchTime(), punchInTime);
            return overtimeHour.compareTo(overtimeConfig.getRestEffectiveTime()) > 0 ? overtimeConfig.getRestEffectiveTime() : overtimeHour;
        }

    }


    /**
     * 判断是否符合以下几种不能打卡的情况
     * //还没有打过上班卡，并且下班卡时间在配置下班卡时间之前
     * //已经打过上班卡，下班卡时间在上班卡之前
     * //已经打过下班卡，且下班卡不是早退，并且本次打卡为外勤打卡
     *
     * @param punchParam
     * @param dayId
     * @param attendanceEmployeePunchDayDO
     */
    private boolean checkPunchOutPermission(UserPunchParam punchParam, EffectiveAttendanceHelpDTO dayId, HrmsAttendanceEmployeePunchDayDO attendanceEmployeePunchDayDO, List<HrmsAttendanceEmployeePunchRecordDO> punchRecordDOList, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO) {
        //EffectiveAttendanceHelpDTO为空，或者没有配置打卡规则
        //可以进行下班打卡
        if (dayId == null || dayId.getNoPunchConfig()) {
            return Boolean.TRUE;
        }
        //如果是自由时间打卡
        if (AttendancePunchTypeEnum.FREE_WORK.name().equals(hrmsAttendancePunchConfigDetailDTO.getPunchConfigType())) {
            //自由时间上下班不允许直接打下班卡
            return !CollectionUtil.isEmpty(punchRecordDOList);

        } else {
            if (CollectionUtil.isEmpty(punchRecordDOList)) {
                if (punchParam.getPunchTime().before(dayId.getConfigPunchOutTime())) {
                    return Boolean.FALSE;
                }
            }
            //还没有打过上班卡，并且下班卡时间在配置下班卡时间之前
            else if (PunchTypeEnum.ON_DUTY.name().equals(punchRecordDOList.get(0).getCheckType()) && punchParam.getPunchTime().before(punchRecordDOList.get(0).getUserPunchTime())) {
                return Boolean.FALSE;
                //最后一条是下班卡，并且本次打卡是外勤下班打卡，且最后一次下班打卡正常
            } else if (PunchTypeEnum.OUT_DUTY.name().equals(punchRecordDOList.get(punchRecordDOList.size() - 1).getCheckType()) && punchRecordDOList.get(punchRecordDOList.size() - 1).getTimeResult().equals(TimeResultEnum.NORMAL.name()) && punchParam.getOutsideOffice()) {
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;

    }


    @Override
    public String punchType() {
        return PunchTypeEnum.OUT_DUTY.getCode();
    }

    /**
     * 下班早退判断
     *
     * @param punchParam
     * @param model
     * @param dayId
     * @param punchConfigDetailDTO
     * @param punchDayDO
     * @return
     */
    private boolean checkLateOrEarly(UserPunchParam punchParam, HrmsAttendanceEmployeePunchRecordDO model, EffectiveAttendanceHelpDTO dayId, HrmsAttendancePunchConfigDetailDTO punchConfigDetailDTO, HrmsAttendanceEmployeePunchDayDO punchDayDO) {
        String configType = Optional.ofNullable(punchConfigDetailDTO).map(HrmsAttendancePunchConfigDetailDTO::getPunchConfigType).orElse(null);
        List<HrmsAttendancePunchClassConfigDTO> hrmsAttendancePunchClassConfigDTOS = Optional.ofNullable(punchConfigDetailDTO).map(HrmsAttendancePunchConfigDetailDTO::getPunchTimeConfig).orElse(null);
        if (dayId == null || StringUtils.isEmpty(configType)) {
            model.setTimeResult(TimeResultEnum.NORMAL.name());
            model.setLocationResult(LocationResultEnum.NORMAL.name());
            return Boolean.TRUE;
        }
        //自由
        if (AttendancePunchTypeEnum.FREE_WORK.name().equals(configType)) {
            if (CollectionUtil.isEmpty(hrmsAttendancePunchClassConfigDTOS)) {
                model.setTimeResult(TimeResultEnum.NORMAL.name());
                model.setLocationResult(LocationResultEnum.NORMAL.name());
                return Boolean.TRUE;
            } else {
                //上下班打卡时间间隔
                BigDecimal punchTimeInterval = hrmsAttendancePunchClassConfigDTOS.get(0).getClassItemConfigList().get(0).getPunchTimeInterval();
                //当前打卡时间和上班打卡时间隔
                BigDecimal diffHour = HrmsDateUtil.diffHour(punchParam.getPunchTime(), punchDayDO.getPunchInTime());

                int compare = BigDecimalUtil.compare(diffHour, punchTimeInterval);
                //早退或正常
                model.setTimeResult(compare < 0 ? TimeResultEnum.EARLY.name() : TimeResultEnum.NORMAL.name());
                model.setLocationResult(punchParam.getOutsideOffice() ? LocationResultEnum.OUTSIDE.name() : LocationResultEnum.NORMAL.name());
                return compare <= 0;
            }
        }
        //固定或班次
        //下班早退判断
        Date punchTime = punchParam.getPunchTime();
        Date configPunchOutTime = dayId.getConfigPunchOutTime();
        boolean isEarly = HrmsDateUtil.before(punchTime, configPunchOutTime);
        //早退或正常打卡
        model.setTimeResult(isEarly ? TimeResultEnum.EARLY.name() : TimeResultEnum.NORMAL.name());
        model.setLocationResult(punchParam.getOutsideOffice() ? LocationResultEnum.OUTSIDE.name() : LocationResultEnum.NORMAL.name());
        return isEarly;


    }


}
