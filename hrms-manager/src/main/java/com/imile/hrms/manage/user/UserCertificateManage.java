package com.imile.hrms.manage.user;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.primary.entity.UserCertificateDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3
 */
public interface UserCertificateManage {

    /**
     * 保存人员证件
     *
     * @param differHolder 人员证件差异保持器
     */
    void doSave(DataDifferHolder<UserCertificateDO> differHolder);

    /**
     * 根据人员ID获取
     *
     * @param userId 人员ID
     * @return List<UserCertificateDO>
     */
    List<UserCertificateDO> getUserCertificateList(Long userId);
}
