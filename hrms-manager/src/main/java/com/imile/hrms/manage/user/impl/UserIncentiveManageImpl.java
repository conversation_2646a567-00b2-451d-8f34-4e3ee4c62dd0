package com.imile.hrms.manage.user.impl;

import com.imile.hrms.dao.user.dao.UserIncentiveDao;
import com.imile.hrms.dao.user.model.UserIncentiveDO;
import com.imile.hrms.manage.user.UserIncentiveManage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/11/10
 */
@Component
public class UserIncentiveManageImpl implements UserIncentiveManage {

    @Resource
    private UserIncentiveDao userIncentiveDao;


    @Override
    public void insert(UserIncentiveDO userIncentive) {
        userIncentiveDao.save(userIncentive);
    }
}
