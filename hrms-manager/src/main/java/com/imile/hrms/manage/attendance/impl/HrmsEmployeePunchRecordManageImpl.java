package com.imile.hrms.manage.attendance.impl;

import com.imile.hrms.dao.punch.dto.HrmsPunchCardRecordDTO;
import com.imile.hrms.dao.punch.mapper.EmployeePunchRecordMapper;
import com.imile.hrms.dao.punch.query.UserPunchCardRecordQuery;
import com.imile.hrms.manage.attendance.HrmsEmployeePunchRecordManage;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDaoFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-09
 */
@Service
public class HrmsEmployeePunchRecordManageImpl implements HrmsEmployeePunchRecordManage {

    @Autowired
    private EmployeePunchRecordMapper employeePunchRecordMapper;
    @Resource
    private CalendarConfigDaoFacade calendarConfigDaoFacade;

    @Override
//    @DS(HrmsProperties.Database.HRMS_RO_URL)
    public List<HrmsPunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query) {
//        return employeePunchRecordMapper.listRecord(query);
        return calendarConfigDaoFacade.getCalendarConfigJoinRelateAdapter().listEmployeePunchRecord(query);
    }
}
