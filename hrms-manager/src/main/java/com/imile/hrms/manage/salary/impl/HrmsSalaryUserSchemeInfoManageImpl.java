package com.imile.hrms.manage.salary.impl;

import com.imile.hrms.dao.salary.dao.HrmsSalaryOperationRecordDao;
import com.imile.hrms.dao.salary.dao.HrmsSalarySettlementUserInfoDao;
import com.imile.hrms.dao.salary.dao.HrmsSalaryUserSchemeInfoDao;
import com.imile.hrms.dao.salary.model.HrmsSalaryOperationRecordDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserInfoDO;
import com.imile.hrms.dao.salary.model.HrmsSalaryUserSchemeInfoDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryUserSchemeInfoQuery;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.salary.HrmsSalaryUserSchemeInfoManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/24 11:03
 * @version: 1.0
 */
@Service
public class HrmsSalaryUserSchemeInfoManageImpl implements HrmsSalaryUserSchemeInfoManage {

    @Autowired
    private HrmsSalaryUserSchemeInfoDao hrmsSalaryUserSchemeInfoDao;
    @Autowired
    private HrmsSalaryOperationRecordDao hrmsSalaryOperationRecordDao;
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsSalarySettlementUserInfoDao hrmsSalarySettlementUserInfoDao;

    @Override
    public List<HrmsSalaryUserSchemeInfoDO> selectUserSchemeBySchemeIdList(List<Long> schemeConfigIdList) {
        if (CollectionUtils.isEmpty(schemeConfigIdList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryUserSchemeInfoDao.selectUserSchemeBySchemeIdList(schemeConfigIdList);
    }

    @Override
    public List<HrmsSalaryUserSchemeInfoDO> selectUserSchemeBySchemeIdListNoLatest(List<Long> schemeConfigIdList) {
        if (CollectionUtils.isEmpty(schemeConfigIdList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryUserSchemeInfoDao.selectUserSchemeBySchemeIdListNoLatest(schemeConfigIdList);
    }

    @Override
    public List<HrmsSalaryUserSchemeInfoDO> selectUserSchemeByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryUserSchemeInfoDao.selectUserSchemeByIdList(idList);
    }

    @Override
    public List<HrmsSalaryUserSchemeInfoDO> selectUserSchemeBySchemeNoList(List<String> schemeNoList) {
        if (CollectionUtils.isEmpty(schemeNoList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryUserSchemeInfoDao.selectUserSchemeBySchemeNoList(schemeNoList);
    }

    @Override
    public List<HrmsSalaryUserSchemeInfoDO> selectUserSchemeByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryUserSchemeInfoDao.selectUserSchemeByUserIdList(userIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userSchemeAdd(List<HrmsSalaryUserSchemeInfoDO> addUserSchemeInfoDOList, List<HrmsUserInfoDO> updateUserInfoDOList,
                              List<HrmsSalaryOperationRecordDO> addOperationRecordDOList, List<HrmsSalarySettlementUserInfoDO> addSettlementUserList) {
        if (CollectionUtils.isNotEmpty(addUserSchemeInfoDOList)) {
            hrmsSalaryUserSchemeInfoDao.saveBatch(addUserSchemeInfoDOList);
        }
        if (CollectionUtils.isNotEmpty(updateUserInfoDOList)) {
            hrmsUserInfoDao.updateBatchById(updateUserInfoDOList);
        }
        if (CollectionUtils.isNotEmpty(addOperationRecordDOList)) {
            hrmsSalaryOperationRecordDao.saveBatch(addOperationRecordDOList);
        }
        if (CollectionUtils.isNotEmpty(addSettlementUserList)) {
            hrmsSalarySettlementUserInfoDao.saveBatch(addSettlementUserList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userSchemeUpdate(HrmsUserInfoDO updateUserInfoDO, List<HrmsSalaryUserSchemeInfoDO> addUserSchemeInfoDOList, List<HrmsSalaryUserSchemeInfoDO> updateUserSchemeInfoDOList, List<HrmsSalaryOperationRecordDO> addOperationRecordDOList) {
        if (updateUserInfoDO != null) {
            hrmsUserInfoDao.updateById(updateUserInfoDO);
        }
        if (CollectionUtils.isNotEmpty(addUserSchemeInfoDOList)) {
            hrmsSalaryUserSchemeInfoDao.saveBatch(addUserSchemeInfoDOList);
        }
        if (CollectionUtils.isNotEmpty(updateUserSchemeInfoDOList)) {
            hrmsSalaryUserSchemeInfoDao.updateBatchById(updateUserSchemeInfoDOList);
        }
        if (CollectionUtils.isNotEmpty(addOperationRecordDOList)) {
            hrmsSalaryOperationRecordDao.saveBatch(addOperationRecordDOList);
        }
    }

    @Override
    public List<HrmsSalaryUserSchemeInfoDO> listByQuery(HrmsSalaryUserSchemeInfoQuery query) {
        return hrmsSalaryUserSchemeInfoDao.listByQuery(query);
    }
}
