package com.imile.hrms.manage.base.impl;

import com.google.common.collect.Lists;
import com.imile.hrms.common.Interface.ObjectValueProcessorInterface;
import com.imile.hrms.common.entity.SimpleObjectInfo;
import com.imile.hrms.dao.base.dao.CnRegionDao;
import com.imile.hrms.dao.base.model.CnRegionDO;
import com.imile.hrms.manage.base.CnRegionManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/21
 */
@Service
public class CnRegionManageImpl implements CnRegionManage, ObjectValueProcessorInterface<Long> {

    @Resource
    private CnRegionDao cnRegionDao;

    @Override
    public Map<Long, String> getRegionNameMap(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<CnRegionDO> regionList = cnRegionDao.selectByIdList(idList);
        return regionList.stream()
                .collect(Collectors.toMap(CnRegionDO::getId, CnRegionDO::getName));
    }

    @Override
    public List<SimpleObjectInfo<Long>> batchGetSimpleObjectInfosByIds(List<Long> bizIdList) {
        if (CollectionUtils.isEmpty(bizIdList)) {
            return Lists.newArrayList();
        }
        List<CnRegionDO> regionList = cnRegionDao.selectByIdList(bizIdList);
        return regionList.stream()
                .map(it -> new SimpleObjectInfo<>(it.getId(), it.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public SimpleObjectInfo<Long> getSimpleObjectInfosById(Long bizId) {
        if (Objects.isNull(bizId)) {
            return null;
        }
        CnRegionDO region = cnRegionDao.getById(bizId);
        if (Objects.isNull(region)) {
            return null;
        }
        return new SimpleObjectInfo<>(region.getId(), region.getName());
    }
}
