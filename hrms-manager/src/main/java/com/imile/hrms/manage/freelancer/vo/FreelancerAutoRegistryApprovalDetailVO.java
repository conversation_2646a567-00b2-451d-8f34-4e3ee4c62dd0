package com.imile.hrms.manage.freelancer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.freelancer.dto.*;
import com.imile.hrms.dao.user.dto.DriverZipCodeDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FreelancerAutoRegistryApprovalDetailVO {

    /**
     * firstName
     */
    private String firstName;

    /**
     * middleName
     */
    private String middleName;

    /**
     * lastName
     */
    private String lastName;

    /**
     * 生日
     */
    private String birthDay;

    /**
     * 工作经验 1：三年以下，2：三年-5年，3：五年以上
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WORK_EXPERIENCE, ref = "workExperienceDesc")
    private Integer workExperience;
    private String workExperienceDesc;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 区号
     */
    private String countryCallingId;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省
     */
    private String state;

    /**
     * 市
     */
    private String city;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * cpfNumber
     */
    private String cpfNumber;

    /**
     * cnpjNumber
     */
    private String cnpjNumber;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 驾驶证等级
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.DRIVER_LICENSE_LEVEL, ref = "driverLicenseLevelDesc")
    private String driverLicenseLevel;
    private String driverLicenseLevelDesc;

    /**
     * EAR 1：yes / 0：no
     */
    private String ear;

    /**
     * 驾驶证到期日
     */
    private String driverLicenseExpireDate;

    /**
     * 开户银行
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_BANK_NAME, ref = "accountBankNameDesc")
    private String accountBankName;
    private String accountBankNameDesc;

    /**
     * 开户支行
     */
    private String agency;

    /**
     * 银行账号
     */
    private String accountNumber;

    /**
     * 是否电子银行
     */
    private String digitalBank;

    /**
     * 签名
     */
    private String signature;

    /**
     * 签名时间
     */
    private String signatureDate;

    /**
     * 车辆行驶证信息
     */
    private List<FreelancerVehicleInfoDTO> vehicleInfoList;

    /**
     * 派送偏好 -- 自主注册
     */
    private String autoDeliveryPreference;

    /**
     * 附件信息
     */
    private List<FreelancerAutoRegistryAttachmentDTO> attachmentList;

    /**
     * 网点id
     */
    private Long ocId;

}
