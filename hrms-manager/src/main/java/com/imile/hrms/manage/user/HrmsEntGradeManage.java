package com.imile.hrms.manage.user;

import com.imile.hrms.dao.organization.model.HrmsEntGradeDO;
import com.imile.hrms.dao.organization.query.HrmsEntGradeQuery;

import java.util.List;

/**
 * 职级序列
 *
 * <AUTHOR>
 * @since 2024/5/13
 */
public interface HrmsEntGradeManage {

    /**
     * 根据参数查询
     *
     * @param query
     * @return
     */
    List<HrmsEntGradeDO> listByQuery(HrmsEntGradeQuery query);

}
