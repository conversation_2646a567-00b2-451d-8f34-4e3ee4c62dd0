package com.imile.hrms.manage.newAttendance.punchConfig.adapter.convert;

import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassItemConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassItemConfigDO;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.mapstruct.PunchClassItemConfigMapstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/2/17
 * @Description
 */
@Component
public class PunchClassItemConfigConverter implements DataConverter<PunchClassItemConfigDO, HrmsAttendancePunchClassItemConfigDO> {

    @Override
    public HrmsAttendancePunchClassItemConfigDO convertFromNew(PunchClassItemConfigDO newObj) {
        return PunchClassItemConfigMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public PunchClassItemConfigDO convertFromOld(HrmsAttendancePunchClassItemConfigDO oldObj) {
        return PunchClassItemConfigMapstruct.INSTANCE.mapToNew(oldObj);
    }

    @Override
    public Class<PunchClassItemConfigDO> getNewType() {
        return PunchClassItemConfigDO.class;
    }

    @Override
    public Class<HrmsAttendancePunchClassItemConfigDO> getOldType() {
        return HrmsAttendancePunchClassItemConfigDO.class;
    }
}
