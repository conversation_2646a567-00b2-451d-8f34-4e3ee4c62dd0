package com.imile.hrms.manage.punch.impl;

import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.dao.attendance.dao.HrmsAttendancePunchStatisticsDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendancePunchStatisticsDO;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.manage.punch.HrmsAttendancePunchStatisticsManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HrmsAttendancePunchStatisticsManageImpl implements HrmsAttendancePunchStatisticsManage {

    @Autowired
    private IHrmsIdWorker hrmsIdWorker;
    @Autowired
    private HrmsAttendancePunchStatisticsDao attendancePunchStatisticsDao;

    @Override
    public boolean batchSave(List<HrmsAttendancePunchStatisticsDO> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return true;
        }
        statisticsList.stream().forEach(o -> {
            if (o.getId() == null) {
                o.setId(hrmsIdWorker.nextId());
            }
            BaseDOUtil.fillDOInsert(o);
        });
        return attendancePunchStatisticsDao.saveBatch(statisticsList);
    }

    @Override
    public boolean batchUpdate(List<HrmsAttendancePunchStatisticsDO> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return true;
        }
        statisticsList.forEach(BaseDOUtil::fillDOUpdate);

        return attendancePunchStatisticsDao.updateBatchById(statisticsList);
    }
}
