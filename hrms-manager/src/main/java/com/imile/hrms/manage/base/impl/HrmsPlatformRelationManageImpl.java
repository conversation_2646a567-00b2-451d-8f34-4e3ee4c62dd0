package com.imile.hrms.manage.base.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.common.enums.StatusEnum;
import com.imile.hermes.business.dto.CompanyInfoApiDTO;
import com.imile.hermes.business.query.CompanyInfoApiQuery;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.constants.KingdeeConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.RelationBizTypeEnum;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.common.enums.organization.DeptStatusEnum;
import com.imile.hrms.common.enums.punch.BizTypeEnum;
import com.imile.hrms.common.enums.punch.PlatFormTypeEnum;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.common.util.BusinessFieldUtils;
import com.imile.hrms.common.util.HrmsStringUtil;
import com.imile.hrms.common.util.KingdeeUtil;
import com.imile.hrms.common.util.SplitUtil;
import com.imile.hrms.dao.organization.enums.DeptBizAreaEnum;
import com.imile.hrms.dao.organization.model.HrmsEntDeptDO;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.sys.dao.HrmsPlatformRelationDao;
import com.imile.hrms.dao.sys.model.HrmsPlatformRelationDO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.integration.hermes.service.CompanyService;
import com.imile.hrms.integration.hermes.service.EntOcService;
import com.imile.hrms.integration.kingdee.KingdeeIntegration;
import com.imile.hrms.integration.kingdee.dto.KingDeeCostDeptInfoDTO;
import com.imile.hrms.integration.kingdee.dto.KingDeeUserInfoDTO;
import com.imile.hrms.integration.kingdee.dto.UnAuditDTO;
import com.imile.hrms.integration.platform.constant.PlatFormApiConstant;
import com.imile.hrms.integration.platform.service.impl.FeishuPlatFormSyncServiceImpl;
import com.imile.hrms.integration.platform.service.impl.WechatWorkPlatFormSyncServiceImpl;
import com.imile.hrms.integration.platform.service.param.FeishuDeptParam;
import com.imile.hrms.integration.platform.service.param.PLatFormDeptParam;
import com.imile.hrms.integration.platform.service.param.UserParam;
import com.imile.hrms.integration.platform.service.param.WecomExtAttr;
import com.imile.hrms.manage.base.HrmsPlatformRelationManage;
import com.imile.hrms.manage.organization.HrmsDeptManage;
import com.imile.hrms.manage.organization.PostFamilyManage;
import com.imile.hrms.manage.primary.PostManage;
import com.imile.openapi.api.kingdee.KingdeeResult;
import com.lark.oapi.service.contact.v3.model.Department;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/18
 */
@Slf4j
@Service
public class HrmsPlatformRelationManageImpl implements HrmsPlatformRelationManage {

    @Resource
    private HrmsPlatformRelationDao hrmsPlatformRelationDao;
    @Resource
    private HrmsDeptManage hrmsDeptManage;
    @Resource
    private PostManage postManage;
    @Resource
    private PostFamilyManage postFamilyManage;
    @Resource
    private HrmsProperties hrmsProperties;
    @Resource
    private CompanyService companyService;
    @Resource
    private KingdeeIntegration kingdeeIntegration;
    @Resource
    private WechatWorkPlatFormSyncServiceImpl wecomBaseService;
    @Resource
    private FeishuPlatFormSyncServiceImpl feishuPlatFormSyncService;
    @Resource
    private EntOcService entOcService;

    @Override
    public void syncDept2Kingdee(HrmsEntDeptDO dept) {
        long startTime = System.currentTimeMillis();
        // 判断是否为测试部门 测试部门无需同步
        if (hrmsDeptManage.isTestDept(dept) || hrmsDeptManage.isTestDept(dept.getParentId())) {
            log.info("syncDept2Kingdee | deptId:{} 测试部门无需同步", dept.getId());
            return;
        }
        // 获取结算主体列表
        List<CompanyInfoApiDTO> companyList = this.getCompanyList();
        if (CollectionUtils.isEmpty(companyList)) {
            log.error("syncDept2Kingdee | deptId:{} 结算主体列表为空", dept.getId());
            return;
        }
        String bizCountry = BusinessFieldUtils.getOneBizCountry(dept.getBizCountry());
        String deptNameCn = dept.getDeptNameCn() + "(" + bizCountry + ")";
        String deptNameEn = dept.getDeptNameEn() + "(" + bizCountry + ")";
        List<HrmsPlatformRelationDO> insertList = Lists.newArrayList();
        companyList.forEach(s -> {
            long relationId = 0L;
            String bizId = dept.getId() + "," + s.getCompanyOrgId();
            HrmsPlatformRelationDO relation = hrmsPlatformRelationDao.getPlatFormRelation(bizId,
                    RelationBizTypeEnum.KINGDEE_DEPT.getCode(), "", BusinessConstant.PLAT_FORM);
            // 若不为空说明已经是审核通过了，需要先做反审核操作
            if (Objects.nonNull(relation) && StringUtils.isNotBlank(relation.getRelationId())) {
                relationId = Long.parseLong(relation.getRelationId());
                UnAuditDTO unAuditDTO = new UnAuditDTO();
                unAuditDTO.setId(relationId);
                kingdeeIntegration.unAudit(unAuditDTO, KingdeeConstant.BusKey.DEPT_KEY);
            }
            // 组装同步金蝶参数
            KingDeeCostDeptInfoDTO kingDeeDept = new KingDeeCostDeptInfoDTO();
            kingDeeDept.setId(relationId);
            kingDeeDept.setDeptCode(dept.getDeptCode());
            kingDeeDept.setDeptName(KingdeeUtil.convertKingDeeName(deptNameCn, deptNameEn));
            kingDeeDept.setDeptAttr("");
            kingDeeDept.setCreateOrgId(s.getCompanyOrgId());
            kingDeeDept.setUseOrgId(s.getCompanyOrgId());
            KingdeeResult kingdeeResult = kingdeeIntegration.syncOfCostDeptInfo(kingDeeDept);
            // 已有关联关系或金蝶返回同步结果中id为空则返回
            if (Objects.nonNull(relation) || Objects.isNull(kingdeeResult.getId())) {
                return;
            }
            // 保存关联关系
            HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
            record.setBizId(bizId);
            record.setBizType(RelationBizTypeEnum.KINGDEE_DEPT.getCode());
            record.setRelationId(kingdeeResult.getId().toString());
            record.setPlatformType(BusinessConstant.PLAT_FORM);
            record.setStatus(StatusEnum.ACTIVE.getCode());
            record.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsert(record);
            insertList.add(record);
        });
        if (insertList.isEmpty()) {
            log.info("syncDept2Kingdee | deptId:{} 耗时:{}", dept.getId(), System.currentTimeMillis() - startTime);
            return;
        }
        hrmsPlatformRelationDao.saveBatch(insertList);
        log.info("syncDept2Kingdee | deptId:{} 耗时:{}", dept.getId(), System.currentTimeMillis() - startTime);
    }

    /**
     * 同步新建/更新飞书的通讯录
     */
    @Override
    public void syncDept2Feishu(HrmsEntDeptDO dept) {
        long startTime = System.currentTimeMillis();
        // 根节点也无需处理，会报错“process root dept error”
        if (this.isRootDept(dept)) {
            log.info("syncDept2Feishu | deptId:{} 飞书根部门不能编辑", dept.getId());
            return;
        }
        String bizId = dept.getId().toString();
        HrmsPlatformRelationDO relation = this.getDeptRelation(bizId, PlatFormTypeEnum.FEI_SHU);

        // 判断是否已有关联关系 有且启用中则更新企微部门信息 无则调企微接口创建部门并保存关联关系
        if (Objects.nonNull(relation)) {
            String relationId = relation.getRelationId();
            if (StringUtils.isNotBlank(relationId) && this.existInFeishu(relationId)) {
                this.doUpdateFeishuDept(dept, relationId);
            } else {
                // 这条数据就有问题，
                log.info("feishu relation date exception data: {}", relationId);
                this.doCreateFeishuDept(dept);
            }
            log.info("syncDept2Feishu | deptId:{} 耗时:{}", dept.getId(), System.currentTimeMillis() - startTime);
            return;
        }
        this.doCreateFeishuDept(dept);
    }

    /**
     * 同步启用/停用飞书的通讯录
     */
    @Override
    public void syncDeptStatus2Feishu(String status, HrmsEntDeptDO dept) {
        Long deptId = dept.getId();
        if (this.isRootDept(dept)) {
            log.info("syncDeptStatus2Feishu | deptId:{} 飞书根部门不能编辑", deptId);
            return;
        }
        HrmsPlatformRelationDO relation = this.getDeptRelation(String.valueOf(deptId), PlatFormTypeEnum.FEI_SHU);
        if (Objects.isNull(relation) || relation.getStatus().equals(status)) {
            log.info("syncDeptStatus2Feishu dept is not relation, deptId:{}", deptId);
            return;
        }
        //飞书通讯录
        if (status.equals(StatusEnum.ACTIVE.getCode()) || status.equals(StatusEnum.DISABLED.getCode())) {
            feishuPlatFormSyncService.updateDeptStatus(Long.parseLong(relation.getRelationId()), DeptStatusEnum.ACTIVE.getCode().equals(status));
        } else {
            log.info("syncDeptStatus2Feishu dept status is not ACTIVE or DISABLED");
        }
        HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
        record.setId(relation.getId());
        record.setStatus(status);
        BaseDOUtil.fillDOUpdate(record);
        hrmsPlatformRelationDao.updateById(record);
    }

    @Override
    public void deleteFeishuDept(Long deptId) {
        HrmsPlatformRelationDO relation = this.getDeptRelation(String.valueOf(deptId), PlatFormTypeEnum.FEI_SHU);
        if (Objects.isNull(relation)) {
            log.info("deleteFeishuDept dept is not relation, deptId:{}", deptId);
            return;
        }
        String relationId = relation.getRelationId();
        feishuPlatFormSyncService.deleteDeptById(relationId);
        // 移除关联关系
        hrmsPlatformRelationDao.removeById(relation.getId());
    }

    @Override
    public Boolean existInFeishu(String relationId) {
        return feishuPlatFormSyncService.deptExist(relationId);
    }


    @Override
    public Department detailFeishuDeptById(Long deptId) {
        HrmsPlatformRelationDO relation = this.getDeptRelation(deptId.toString(), PlatFormTypeEnum.FEI_SHU);
        if (Objects.isNull(relation)) {
            log.info("detailById dept is not relation, deptId:{}", deptId);
            return null;
        }
        return feishuPlatFormSyncService.detailById(Long.parseLong(relation.getRelationId()));
    }

    @Override
    public void syncDept2Wecom(HrmsEntDeptDO dept) {
        long startTime = System.currentTimeMillis();
        // 判断是否为测试部门 测试部门无需同步
        if (hrmsDeptManage.isTestDept(dept) || hrmsDeptManage.isTestDept(dept.getParentId())) {
            log.info("syncDept2Wecom | deptId:{} 测试部门无需同步", dept.getId());
            return;
        }
        String bizId = dept.getId().toString();
        HrmsPlatformRelationDO relation = getDeptRelation(bizId, PlatFormTypeEnum.WECHAT_WORK);
        // 若有关联关系且已停用 则无需处理
        if (Objects.nonNull(relation) && StatusEnum.DISABLED.getCode().equals(dept.getStatus())) {
            log.info("syncDept2Wecom | deptId:{} 已停用部门无需同步", dept.getId());
            return;
        }
        // 判断是否已有关联关系 有且启用中则更新企微部门信息 无则调企微接口创建部门并保存关联关系
        if (Objects.nonNull(relation)) {
            this.doUpdateWecomDept(dept);
        } else {
            this.doCreateWecomDept(dept);
            HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
            record.setBizId(bizId);
            record.setBizType(BizTypeEnum.DEPT.name());
            record.setRelationId(bizId);
            record.setPlatformType(PlatFormTypeEnum.WECHAT_WORK.name());
            record.setStatus(StatusEnum.ACTIVE.getCode());
            record.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsert(record);
            hrmsPlatformRelationDao.save(record);
        }
        log.info("syncDept2Wecom | deptId:{} 耗时:{}", dept.getId(), System.currentTimeMillis() - startTime);
    }

    @Override
    public void syncDeptStatus2Wecom(Long deptId, String status) {
        HrmsPlatformRelationDO relation = getDeptRelation(deptId.toString(), PlatFormTypeEnum.WECHAT_WORK);
        if (Objects.isNull(relation) || relation.getStatus().equals(status)) {
            return;
        }
        if (StatusEnum.DISABLED.getCode().equals(status)) {
            // 停用时删除企微部门 将关联关系状态改为DISABLED
            wecomBaseService.deleteDept(deptId);
        } else {
            // 启用时重新添加企微部门 将关联关系状态改为ACTIVE
            HrmsEntDeptDO dept = hrmsDeptManage.selectById(deptId);
            this.doCreateWecomDept(dept);
        }
        HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
        record.setId(relation.getId());
        record.setStatus(status);
        BaseDOUtil.fillDOUpdate(record);
        hrmsPlatformRelationDao.updateById(record);
    }

    @Override
    public void syncUser2Kingdee(HrmsUserInfoDO user) {
        if (StringUtils.isBlank(user.getUserCode())) {
            log.info("syncUser2Kingdee | userId:{} 人员账号还未创建，暂时不同步", user.getId());
            return;
        }
        // 特定用工类型才需要同步到金蝶
        List<String> employeeTypeList = Arrays.asList(hrmsProperties.getKingdeeSync().getEmployeeType().split(","));
        if (!employeeTypeList.contains(user.getEmployeeType())) {
            log.info("syncUser2Kingdee | userId:{} 非特定用工类型无需同步", user.getId());
            return;
        }
        HrmsEntDeptDO dept = hrmsDeptManage.selectById(user.getDeptId());
        // 判断是否为测试部门 测试部门下的人员无需同步
        if (hrmsDeptManage.isTestDept(dept)) {
            log.info("syncUser2Kingdee | userId:{} 测试人员无需同步", user.getId());
            return;
        }
        long startTime = System.currentTimeMillis();
        long relationId = 0L;
        String bizId = user.getId().toString();
        HrmsPlatformRelationDO relation = hrmsPlatformRelationDao.getPlatFormRelation(bizId,
                RelationBizTypeEnum.KINGDEE_USER.getCode(), "", BusinessConstant.PLAT_FORM);
        // 若不为空说明已经是审核通过了，需要先做反审核操作
        if (Objects.nonNull(relation) && StringUtils.isNotBlank(relation.getRelationId())) {
            relationId = Long.parseLong(relation.getRelationId());
            UnAuditDTO unAuditDTO = new UnAuditDTO();
            unAuditDTO.setId(relationId);
            kingdeeIntegration.unAudit(unAuditDTO, KingdeeConstant.BusKey.USER_KEY);
        }
        // 组装同步金蝶参数
        HrmsEntDeptDO ocDept = hrmsDeptManage.selectById(user.getOcId());
        EntOcApiDTO oc = entOcService.getOcById(BusinessConstant.DEFAULT_ORG_ID, ocDept.getOcId());
        String userNameCn = user.getUserName();
        String userNameEn = StringUtils.isBlank(user.getUserNameEn()) ? user.getUserName() : user.getUserNameEn();
        KingDeeUserInfoDTO kingDeeUser = new KingDeeUserInfoDTO();
        kingDeeUser.setId(relationId);
        kingDeeUser.setUserCode(user.getUserCode());
        kingDeeUser.setUserName(KingdeeUtil.convertKingDeeName(userNameCn, userNameEn));
        kingDeeUser.setDeptCode(dept.getDeptCode());
        kingDeeUser.setOcCode(Optional.ofNullable(oc).map(EntOcApiDTO::getOcCode).orElse(""));
        this.fillOrgInfo4User(kingDeeUser, user.getSettlementCenterCode());
        KingdeeResult kingdeeResult = kingdeeIntegration.syncOfUserInfo(kingDeeUser);
        // 已有关联关系或金蝶返回同步结果中id为空则返回
        if (Objects.nonNull(relation) || Objects.isNull(kingdeeResult.getId())) {
            return;
        }
        // 保存关联关系
        HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
        record.setBizId(bizId);
        record.setBizType(RelationBizTypeEnum.KINGDEE_USER.getCode());
        record.setRelationId(kingdeeResult.getId().toString());
        record.setPlatformType(BusinessConstant.PLAT_FORM);
        record.setStatus(StatusEnum.ACTIVE.getCode());
        record.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(record);
        hrmsPlatformRelationDao.save(record);
        log.info("syncUser2Kingdee | userId:{} 耗时:{}", user.getId(), System.currentTimeMillis() - startTime);
    }

    @Override
    public void syncUser2Wecom(HrmsUserInfoDO user, Boolean isAdd) {
        if (StringUtils.isBlank(user.getUserCode())) {
            log.info("syncUser2Wecom | userId:{} 人员账号还未创建，暂时不同步", user.getId());
            return;
        }
        // 判断是否为测试部门 测试部门下的人员无需同步
        if (hrmsDeptManage.isTestDept(user.getDeptId())) {
            log.info("syncUser2Wecom | userId:{} 测试人员无需同步", user.getId());
            return;
        }
        // 判断企微关联关系是否存在 存在则更新
        HrmsPlatformRelationDO relation = hrmsPlatformRelationDao.getPlatFormRelation(user.getUserCode(),
                BizTypeEnum.USER.name(), "", PlatFormTypeEnum.WECHAT_WORK.name());
        if (Objects.isNull(relation) && !isAdd) {
            return;
        }
        PostDO post = postManage.getPostNullableById(user.getPostId());
        HrmsEntDeptDO dept = hrmsDeptManage.selectById(user.getDeptId());
        // 组装同步企微参数
        UserParam param = new UserParam();
        param.setName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()));
        param.setDepartment(Collections.singletonList(user.getDeptId()));
        if (Objects.nonNull(dept) && Objects.nonNull(dept.getLeaderCode())) {
            param.setIsLeaderInDept(Collections.singletonList(dept.getLeaderCode().equals(user.getId())
                    ? WhetherEnum.YES.getKey()
                    : WhetherEnum.NO.getKey()));
        }
        param.setGender(user.getSex());
        param.setEmail(user.getEmail());
        List<WecomExtAttr.Attr> attrList = Lists.newArrayList();
        attrList.add(this.buildExtAttr("ID", user.getUserCode()));
        if (Objects.nonNull(post)) {
            param.setPosition(post.getPostNameCn());
            String postFamilyPathName = postFamilyManage.getPostFamilyPathName(
                    Lists.newArrayList(post.getJobFamilyId(), post.getJobCategoryId(), post.getJobSubCategoryId()),
                    true);
            attrList.add(this.buildExtAttr("职位族类", postFamilyPathName));
        }
        // 组装扩展属性
        param.setExtAttr(WecomExtAttr.builder()
                .attrList(attrList)
                .build());
        if (Objects.isNull(relation)) {
            param.setUserId(user.getUserCode());
            param.setMobile(user.getPhone());
            wecomBaseService.createUser(param);
            // 保存关联关系
            HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
            record.setBizId(user.getUserCode());
            record.setBizType(BizTypeEnum.USER.name());
            record.setRelationId(user.getUserCode());
            record.setPlatformType(PlatFormTypeEnum.WECHAT_WORK.name());
            record.setStatus(StatusEnum.ACTIVE.getCode());
            record.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsert(record);
            hrmsPlatformRelationDao.save(record);
        } else {
            param.setUserId(relation.getRelationId());
            wecomBaseService.updateUser(param);
        }
    }

    @Override
    public List<HrmsPlatformRelationDO> getPlatformRelationList(List<String> bizIdList, String bizType, String platformType) {
        if (CollectionUtils.isEmpty(bizIdList)) {
            return Collections.emptyList();
        }
        return hrmsPlatformRelationDao.selectByBizIdAndType(bizIdList, bizType, platformType);
    }

    @Override
    public HrmsPlatformRelationDO getPlatformRelation(String bizId, String bizType, String platformType) {
        return hrmsPlatformRelationDao.selectByBizIdAndType(bizId, bizType, platformType);
    }

    @Override
    public void bind(String bizId, String bizType, String platformType, String platformId) {
        HrmsPlatformRelationDO relation
                = hrmsPlatformRelationDao.selectByPlatformIdAndType(bizType, platformType, platformId);
        // 判断是否被其他账号绑定
        if (Objects.nonNull(relation) && !relation.getBizId().equals(bizId)) {
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.PLATFORM_ACCOUNT_BOUND);
        }
        // 绑定账号为自身 则无需处理
        if (Objects.nonNull(relation)) {
            return;
        }
        HrmsPlatformRelationDO entity = new HrmsPlatformRelationDO();
        entity.setBizId(bizId);
        entity.setBizType(bizType);
        entity.setPlatformType(platformType);
        entity.setRelationId(platformId);
        entity.setStatus(StatusEnum.ACTIVE.name());
        entity.setIsLatest(WhetherEnum.YES.getKey());
        BaseDOUtil.fillDOInsert(entity);
        hrmsPlatformRelationDao.save(entity);
    }

    private void fillOrgInfo4User(KingDeeUserInfoDTO kingDeeUser, String settlementCenterCode) {
        if (StringUtils.isNotBlank(settlementCenterCode)) {
            kingDeeUser.setCreateOrgId(Long.parseLong(settlementCenterCode));
            kingDeeUser.setUseOrgId(Long.parseLong(settlementCenterCode));
            return;
        }
        List<CompanyInfoApiDTO> companyList = this.getCompanyList();
        if (CollectionUtils.isNotEmpty(companyList)) {
            kingDeeUser.setCreateOrgId(companyList.get(0).getCompanyOrgId());
            kingDeeUser.setUseOrgId(companyList.get(0).getCompanyOrgId());
        }
    }

    private List<CompanyInfoApiDTO> getCompanyList() {
        CompanyInfoApiQuery query = new CompanyInfoApiQuery();
        query.setIsSettleCenter(BusinessConstant.Y);
        return companyService.getSettleCenterList(query);
    }

    private void doCreateWecomDept(HrmsEntDeptDO dept) {
        this.handleDeptName(dept);
        PLatFormDeptParam param = new PLatFormDeptParam();
        param.setId(dept.getId());
        // 企微部门名称长度最大64个字符，超出需要截取
        param.setName(StringUtils.left(dept.getDeptNameCn(), 64));
        // 创建部门时中英文不允许相同 但更新时没有这个限制，所以这里创建时先加空格，创建成功后再改名规避这个限制
        // 企微中文名限制更严格 无法通过加空格绕过上述限制 所以这里选用英文名加空格
        param.setNameEn(StringUtils.left(dept.getDeptNameEn(), 63) + " ");
        param.setParentId(BusinessConstant.DEFAULT_ID.equals(dept.getParentId())
                ? PlatFormApiConstant.WechatWork.ROOT_DEPT_ID.toString()
                : dept.getParentId().toString());
        wecomBaseService.createDept(param);
        // 创建成功后将英文名更新为实际值
        param.setNameEn(StringUtils.left(dept.getDeptNameEn(), 64));
        wecomBaseService.updateDept(param);
    }

    private void doUpdateWecomDept(HrmsEntDeptDO dept) {
        this.handleDeptName(dept);
        PLatFormDeptParam param = new PLatFormDeptParam();
        param.setId(dept.getId());
        // 企微部门名称长度最大64个字符，超出需要截取
        param.setName(StringUtils.left(dept.getDeptNameCn(), 64));
        param.setNameEn(StringUtils.left(dept.getDeptNameEn(), 64));
        param.setParentId(BusinessConstant.DEFAULT_ID.equals(dept.getParentId())
                ? PlatFormApiConstant.WechatWork.ROOT_DEPT_ID.toString()
                : dept.getParentId().toString());
        wecomBaseService.updateDept(param);
    }

    private WecomExtAttr.Attr buildExtAttr(String attrName, String attrValue) {
        return WecomExtAttr.Attr.builder()
                .type(0)
                .name(attrName)
                .text(WecomExtAttr.Text.builder()
                        .value(StringUtils.left(attrValue, 64))
                        .build())
                .build();
    }

    /**
     * 同步创建飞书部门
     */
    private void doCreateFeishuDept(HrmsEntDeptDO dept) {
        this.handleDeptName(dept);
        FeishuDeptParam param = this.buildFeishuDeptParam(dept);
        if (Objects.isNull(param)) {
            return;
        }
        feishuPlatFormSyncService.createDept(param);
        HrmsPlatformRelationDO record = new HrmsPlatformRelationDO();
        record.setBizId(String.valueOf(dept.getId()));
        record.setBizType(BizTypeEnum.DEPT.name());
        //部门id保持一致
        record.setRelationId(String.valueOf(dept.getId()));
        record.setPlatformType(PlatFormTypeEnum.FEI_SHU.name());
        record.setStatus(StatusEnum.ACTIVE.getCode());
        record.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(record);
        log.info("feishuPlatFormSyncService createDept save platformRelation record {}", JSON.toJSONString(record));
        hrmsPlatformRelationDao.save(record);
    }

    /**
     * 同步编辑飞书部门
     */
    private void doUpdateFeishuDept(HrmsEntDeptDO dept, String relationId) {
        this.handleDeptName(dept);
        FeishuDeptParam param = this.buildFeishuDeptParam(dept);
        if (Objects.isNull(param)) {
            return;
        }
        param.setRelationId(relationId);
        feishuPlatFormSyncService.updateDept(param);
    }

    /**
     * 构建飞书同步参数
     */
    private FeishuDeptParam buildFeishuDeptParam(HrmsEntDeptDO dept) {
        FeishuDeptParam param = new FeishuDeptParam();
        //部门id 不可包含斜杠
        param.setId(dept.getId());
        param.setName(dept.getDeptNameCn());
        param.setNameEn(dept.getDeptNameEn());
        if (StringUtils.isNotBlank(dept.getBizArea())) {
            List<String> bizAreaList = SplitUtil.splitNew(dept.getBizArea(), HrmsStringUtil.COMMA).stream()
                    .filter(bizArea -> !Objects.equals(DeptBizAreaEnum.DEFAULT.getValue(), Integer.valueOf(bizArea)))
                    .collect(Collectors.toList());
            List<String> bizAreaNameList = bizAreaList.stream()
                    .map(Integer::valueOf)
                    .map(DeptBizAreaEnum::getInstance)
                    .filter(biz -> !Objects.equals(biz, DeptBizAreaEnum.DEFAULT))
                    .map(DeptBizAreaEnum::getDesc)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(bizAreaNameList)) {
                param.setBizArea(bizAreaNameList.get(0));
            }
        }
        if (!dept.getStatus().equals(DeptStatusEnum.ACTIVE.getCode())) {
            log.info("组织同步外部系统不能存在非启用的部门, dept: {}", JSON.toJSONString(dept));
        }
        param.setEnableStatus(DeptStatusEnum.ACTIVE.getCode().equals(dept.getStatus()));
        param.setOrgLevel(String.valueOf(dept.getLevel()));

        //hrms部门树的根节点的parentId默认是0, 飞书组织架构根节点的parentId默认也是0
        /*
         * 根节点需要特殊处理
         * 1. 飞书的通讯录组织树的根节点是不能CRUD（默认企业开通的时候企业就是根部门）
         * 2. 非根节点的部门需要父级部门在飞书已经存在
         */
        //这里需要查询父部门的id
        HrmsPlatformRelationDO parentDeptRelation
                = this.getDeptRelation(String.valueOf(dept.getParentId()), PlatFormTypeEnum.FEI_SHU);
        if (Objects.isNull(parentDeptRelation) || StringUtils.isBlank(parentDeptRelation.getRelationId())) {
            //上级组织必须在飞书存在
            log.info("组织同步飞书失败, 上级组织未同步飞书, dept: {}", JSON.toJSONString(dept));
            return null;
        }
        param.setParentId(parentDeptRelation.getRelationId());
        return param;
    }

    private void handleDeptName(HrmsEntDeptDO dept) {
        // 由于目前的产品逻辑部门英文名必填 中文名非必填 所以若部门中文名为空 则中文名赋值为英文名
        if (StringUtils.isNotBlank(dept.getDeptNameCn())) {
            return;
        }
        dept.setDeptNameCn(dept.getDeptNameEn());
    }

    private HrmsPlatformRelationDO getDeptRelation(String deptId, PlatFormTypeEnum platFormTypeEnum) {
        if (StringUtils.isBlank(deptId) || Objects.isNull(platFormTypeEnum)) {
            return null;
        }
        return hrmsPlatformRelationDao.getPlatFormRelation(deptId, BizTypeEnum.DEPT.name(), "", platFormTypeEnum.name());
    }

    private Boolean isRootDept(HrmsEntDeptDO dept) {
        if (Objects.isNull(dept)) {
            return false;
        }
        return dept.getParentId().equals(0L) && dept.getLevel() == 0 && dept.getTopId() != 0;
    }
}
