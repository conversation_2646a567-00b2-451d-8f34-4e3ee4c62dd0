package com.imile.hrms.manage.newAttendance.calendar.adapter;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 日历配置dao模块统一调用入口
 * <AUTHOR> chen
 * @Date 2025/2/10 
 * @Description
 */
@Component
@RequiredArgsConstructor
@Getter
public class CalendarConfigDaoFacade {

    private final CalendarConfigAdapter calendarConfigAdapter;
    private final CalendarConfigDetailAdapter calendarConfigDetailAdapter;
    private final CalendarConfigRangeAdapter calendarConfigRangeAdapter;
    private final CalendarLegalLeaveConfigAdapter calendarLegalLeaveConfigAdapter;
    private final CalendarConfigJoinRelateAdapter calendarConfigJoinRelateAdapter;

}
