package com.imile.hrms.manage.base;

import com.imile.hrms.dao.base.model.CountryDO;
import com.imile.hrms.dao.base.model.condition.CountryConditionBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
public interface CountryManage {

    /**
     * 根据动态条件获取国家列表
     *
     * @param condition 条件构造器
     * @return List<CountryDO>
     */
    List<CountryDO> getCountryList(CountryConditionBuilder condition);

    /**
     * 根据国际区号获取国家ID（临时兼容 未来废弃）
     *
     * @param callingCode 国际区号
     * @return CountryDO
     */
    Long getCountryIdByCallingCode(String callingCode);

    /**
     * 根据国家名称获取国家二字码
     *
     * @param countryNameList 国家名称列表
     * @return Map
     */
    Map<String, String> getCountryCodeMap(List<String> countryNameList);

    /**
     * 根据国家二字码获取国家名称
     *
     * @param shortNameList 国家二字码列表
     * @return Map
     */
    Map<String, String> getCountryNameMap(List<String> shortNameList);

    /**
     * 根据国家二字码获取国家对象
     *
     * @param shortNameList 国家二字码列表
     * @return Map
     */
    Map<String, CountryDO> getCountryMap(List<String> shortNameList);

    /**
     * 根据国家二字码获取国家名称
     *
     * @param shortName 国家二字码
     * @return 国家配置
     */
    CountryDO getCountryByShortName(String shortName);

    /**
     * 根据国家三字码获取国家名称
     *
     * @param countryCode 国家三字码
     * @return 国家配置
     */
    CountryDO getCountryByCode(String countryCode);

    /**
     * 国家三字码转换为二字码
     *
     * @param countryCode 国家三字码
     * @return String
     */
    String convert2ShortName(String countryCode);
}
