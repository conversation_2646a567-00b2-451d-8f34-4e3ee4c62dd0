package com.imile.hrms.manage.attendance.impl;

import com.google.common.collect.Lists;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceUserLeaveConfigHistoryDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceUserLeaveConfigHistoryDO;
import com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO;
import com.imile.hrms.manage.attendance.HrmsAttendanceUserLeaveConfigHistoryManage;
import com.imile.hrms.manage.company.HrmsCompanyLeaveConfigManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-22
 */
@Service
@Slf4j
public class HrmsAttendanceUserLeaveConfigHistoryManageImpl implements HrmsAttendanceUserLeaveConfigHistoryManage {

    @Autowired
    private HrmsAttendanceUserLeaveConfigHistoryDao userLeaveConfigHistoryDao;
    @Autowired
    private HrmsCompanyLeaveConfigManage companyLeaveConfigManage;

    @Override
    public List<HrmsCompanyLeaveConfigDO> selectHistoryLeaveConfigByUser(Long userId) {
        if (Objects.isNull(userId)) {
            return Lists.newArrayList();
        }
        List<HrmsAttendanceUserLeaveConfigHistoryDO> userLeaveConfigHistory = userLeaveConfigHistoryDao.selectLeaveHistoryInfoByUserId(userId);
        if (CollectionUtils.isEmpty(userLeaveConfigHistory)) {
            log.info("selectHistoryLeaveConfigByUser | userLeaveConfigHistory is empty, userId{} ", userId);
            return Lists.newArrayList();
        }
        List<Long> LeaveIds = userLeaveConfigHistory.stream().map(item -> item.getLeaveId()).collect(Collectors.toList());
        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.getByIdList(LeaveIds);
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            log.info("selectHistoryLeaveConfigByUser | companyLeaveConfigList is empty, userId{} ", userId);
            return Lists.newArrayList();
        }
        return companyLeaveConfigList;
    }
}
