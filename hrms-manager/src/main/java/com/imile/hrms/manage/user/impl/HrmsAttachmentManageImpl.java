package com.imile.hrms.manage.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.user.dto.AttachmentParamDTO;
import com.imile.hrms.dao.user.mapper.HrmsAttachmentMapper;
import com.imile.hrms.dao.user.model.HrmsAttachmentDO;
import com.imile.hrms.manage.user.HrmsAttachmentManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 附件管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/18
 */
@Service
public class HrmsAttachmentManageImpl implements HrmsAttachmentManage {

    @Autowired
    private IHrmsIdWorker hrmsIdWorker;
    @Autowired
    private HrmsAttachmentMapper hrmsAttachmentMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSave(List<AttachmentParamDTO> attachments, String foreignKey, String foreignTable) {
        List<HrmsAttachmentDO> attachmentDos = getAttachmentDos(attachments, foreignKey, foreignTable);
        if (!CollUtil.isEmpty(attachmentDos)) {
            hrmsAttachmentMapper.insertBatchSomeColumn(attachmentDos);
        }
    }

    /**
     * 获取落库类封装
     *
     * @param attachments
     * @return
     */
    private List<HrmsAttachmentDO> getAttachmentDos(List<AttachmentParamDTO> attachments, String foreignKey, String foreignTable) {
        if (CollUtil.isEmpty(attachments)) {
            return new ArrayList<>();
        }
        // attachments
        return attachments.stream().map(this.transferDO(foreignKey, foreignTable)).collect(Collectors.toList());
    }

    public Function<AttachmentParamDTO, HrmsAttachmentDO> transferDO(String foreignKey, String foreignTable) {
        return bean -> {
            //校验附件信息是不是符合规范
            HrmsAttachmentDO target = new HrmsAttachmentDO();
            BeanUtil.copyProperties(bean, target);
            String attachmentName = bean.getAttachmentName();
            int lastPostIndex = attachmentName.lastIndexOf(BusinessConstant.POINT);
            if (lastPostIndex < attachmentName.length() - 1) {
                target.setAttachmentSuffix(attachmentName.substring(lastPostIndex + 1));
            }
            target.setId(hrmsIdWorker.nextId());
            target.setStatus(StatusEnum.ACTIVE.getCode());
            target.setForeignKey(foreignKey);
            target.setForeignTable(foreignTable);
            BaseDOUtil.fillDOInsert(target);
            return target;
        };
    }
}
