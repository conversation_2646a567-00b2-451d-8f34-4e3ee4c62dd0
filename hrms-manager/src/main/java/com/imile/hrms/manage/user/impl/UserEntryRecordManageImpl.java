package com.imile.hrms.manage.user.impl;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.user.dao.UserEntryRecordDao;
import com.imile.hrms.dao.user.model.HrmsUserEntryRecordDO;
import com.imile.hrms.manage.user.UserEntryRecordManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
@Slf4j
@Service
public class UserEntryRecordManageImpl implements UserEntryRecordManage {

    @Resource
    private UserEntryRecordDao userEntryRecordDao;

    @Override
    public HrmsUserEntryRecordDO getUserEntryRecord(Long userId) {
        HrmsUserEntryRecordDO userEntryRecord = userEntryRecordDao.selectByUserId(userId);
        if (Objects.isNull(userEntryRecord) || IsDeleteEnum.YES.getCode().equals(userEntryRecord.getIsDelete())) {
            log.error("人员入职记录不存在或已删除,userId:{}", userId);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ENTRY_DATE_NOT_EXITS);
        }
        return userEntryRecord;
    }

    @Override
    public HrmsUserEntryRecordDO getUserEntryRecordNullable(Long userId) {
        return userEntryRecordDao.selectByUserId(userId);
    }

    @Override
    public Map<Long, HrmsUserEntryRecordDO> getUserEntryRecordMap(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        List<HrmsUserEntryRecordDO> recordList = userEntryRecordDao.selectByUserIdList(userIdList);
        return recordList.stream()
                .collect(Collectors.toMap(HrmsUserEntryRecordDO::getUserId, Function.identity()));
    }
}
