package com.imile.hrms.manage.probation.impl;

import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.probation.dao.UserProbationCycleSummaryDao;
import com.imile.hrms.dao.probation.model.UserProbationBaseDO;
import com.imile.hrms.dao.probation.model.UserProbationCycleSummaryDO;
import com.imile.hrms.manage.probation.UserProbationCycleSummaryManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/17
 */
@Repository
@Slf4j
public class UserProbationCycleSummaryManageImpl implements UserProbationCycleSummaryManage {

    @Resource
    private UserProbationCycleSummaryDao userProbationCycleSummaryDao;

    @Override
    public List<UserProbationCycleSummaryDO> findByProbationId(Long probationId) {
        if (Objects.isNull(probationId)) {
            return Lists.newArrayList();
        }
        return userProbationCycleSummaryDao.selectByProbationId(probationId);
    }

    @Override
    public UserProbationCycleSummaryDO findById(Long id) {
        UserProbationCycleSummaryDO cycleSummary = userProbationCycleSummaryDao.getById(id);
        if (Objects.isNull(cycleSummary) || IsDeleteEnum.YES.getCode().equals(cycleSummary.getIsDelete())) {
            log.error("试用期周期总结不存在, id:{}", id);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.DATA_NOT_EXITS);
        }
        return cycleSummary;
    }

    @Override
    public Map<Long, Integer> countSubmitNumber(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<UserProbationCycleSummaryDO> cycleSummaryList = userProbationCycleSummaryDao.selectByProbationIdList(idList);
        cycleSummaryList = cycleSummaryList.stream()
                .filter(it -> it.getIsSubmit().equals(1))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cycleSummaryList)) {
            return Collections.emptyMap();
        }
        return cycleSummaryList.stream()
                .collect(Collectors.groupingBy(
                        UserProbationBaseDO::getUserProbationId,
                        Collectors.collectingAndThen(Collectors.toList(), List::size)
                ));
    }
}
