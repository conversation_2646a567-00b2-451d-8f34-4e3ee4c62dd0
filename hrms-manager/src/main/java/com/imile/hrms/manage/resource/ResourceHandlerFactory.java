package com.imile.hrms.manage.resource;

import com.imile.hrms.common.util.SpringApplicationUtil;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限类工厂类
 *
 * <AUTHOR>
 */
@Component
public class ResourceHandlerFactory {
    private ResourceHandlerFactory() {
    }

    private static final Map<String, ResourceHandlerAbstract> cacheService = new HashMap<>();

    public static ResourceHandlerAbstract getInstance(String resourceType) {

        if (cacheService.isEmpty()) {
            init();
        }
        return cacheService.get(resourceType);
    }

    /**
     * 初始化
     */
    public static void init() {
        synchronized (cacheService) {
            if (!cacheService.isEmpty()) {
                return;
            }
            List<ResourceHandlerAbstract> resourceHandlerAbstracts = SpringApplicationUtil.getBeansOfType(ResourceHandlerAbstract.class);
            cacheService.putAll(resourceHandlerAbstracts.stream().filter(service -> service.getResourceHandler() != null).collect(Collectors.toMap(ResourceHandlerAbstract::getResourceHandler, x -> x, (a1, a2) -> a1)));

        }
    }
}
