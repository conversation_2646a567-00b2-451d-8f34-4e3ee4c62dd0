package com.imile.hrms.manage.punch;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceUserLeaveConfigHistoryDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigDateQuery;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigRangDO;
import com.imile.hrms.dao.newAttendance.calendar.dto.CalendarRangeCountDTO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;

import java.util.List;

public interface HrmsAttendanceConfigRangeManage {

    /**
     * 根据bizIds和公司id查询员工配置的考勤日历
     */
    List<HrmsAttendanceConfigRangeDO> selectConfigRange(List<Long> bizIds);


    /**
     * 查询员工指定时间内的考勤日历
     */
    List<HrmsAttendanceConfigRangeDO> selectAttendanceConfigByDate(AttendanceConfigDateQuery query);

    List<HrmsAttendanceConfigRangeDO> selectAttendanceConfigByIds(List<Long> configIdList);

    void attendanceConfigUpdate(List<HrmsAttendanceConfigRangeDO> updateList, List<HrmsAttendanceConfigRangeDO> addList);

    void userEditUpdate(HrmsAttendanceConfigRangeDO updateConfigRangeDO, HrmsAttendanceConfigRangeDO addConfigRangeDO,
                        HrmsAttendancePunchConfigRangeDO updatePunchConfigRangeDO, HrmsAttendancePunchConfigRangeDO addPunchConfigRangeDO);

    /**
     * todo 临时适配
     */
    void userAttendanceAndPunchUpdate(List<HrmsAttendanceConfigRangeDO> addAttendanceConfigRangeDOList,
                                      List<HrmsAttendancePunchConfigRangeDO> addAttendancePunchConfigRangeDOList,
                                      List<HrmsAttendanceConfigRangeDO> updateAttendanceConfigRangeDOList,
                                      List<HrmsAttendancePunchConfigRangeDO> updateAttendancePunchConfigRangeDOList,
                                      List<HrmsCompanyLeaveConfigRangDO> addLeaveRang,
                                      List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang,
                                      List<HrmsAttendanceUserLeaveConfigHistoryDO> addUserLeaveHistory,
                                      List<HrmsAttendanceUserLeaveConfigHistoryDO> updateUserLeaveHistory);


    List<CalendarRangeCountDTO> countCalendarRange(List<Long> calendarConfigIds);
}

