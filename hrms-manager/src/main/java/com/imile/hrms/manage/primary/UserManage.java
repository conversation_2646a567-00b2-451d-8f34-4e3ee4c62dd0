package com.imile.hrms.manage.primary;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.primary.entity.UserCertificateDO;
import com.imile.hrms.dao.primary.entity.UserDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface UserManage {

    /**
     * 根据人员编码获取人员
     *
     * @param userCode 人员编码
     * @return UserDO
     */
    UserDO getUserByUserCode(String userCode);

    /**
     * 根据人员编码获取人员（可为空）
     *
     * @param userCode 人员编码
     * @return UserDO
     */
    UserDO getUserByUserCodeNullable(String userCode);

    /**
     * 根据人员ID获取人员
     *
     * @param id 人员ID
     * @return UserDO
     */
    UserDO getUserById(Long id);

    /**
     * 获取人员ID及人员编码映射
     *
     * @param idList 人员ID列表
     * @return Map
     */
    Map<Long, String> getUserCodeMap(List<Long> idList);

    /**
     * 获取人员ID及人员对象映射
     *
     * @param idList 人员ID列表
     * @return Map
     */
    Map<Long, UserDO> getUserMap(List<Long> idList);

    /**
     * 获取人员编码及人员对象映射
     *
     * @param userCodeList 人员编码列表
     * @return Map
     */
    Map<String, UserDO> getUserMapByCode(List<String> userCodeList);

    /**
     * 保存人员及相关信息
     *
     * @param user                    人员
     * @param certificateDifferHolder 人员证件差异保持器
     */
    void doSave(UserDO user, DataDifferHolder<UserCertificateDO> certificateDifferHolder);
}
