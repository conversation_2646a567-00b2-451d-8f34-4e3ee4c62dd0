package com.imile.hrms.manage.newAttendance.calendar;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceUserLeaveConfigHistoryDO;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigRangDO;
import com.imile.hrms.dao.newAttendance.calendar.dto.CalendarRangeCountDTO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDetailDO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigRangeDO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.hrms.dao.newAttendance.calendar.query.CalendarConfigDateQuery;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigRangeDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/2/6
 * @Description
 */
public interface CalendarManage {

    //=====================calendarConfig============================================

    /**
     * 根据ID查询最新启用的日历配置
     */
    CalendarConfigDO getActiveCalendarConfigById(Long id);

    /**
     * 获取员工指定年份的考勤日历明细模版
     * 注意，需要按照考勤模版的生效时间来拼装
     * 举例，A模版生效时间为2021年1月1日-2021年3月1日，3月2日切换到B模版，那么2021年全年的考勤模版为二者的拼接
     */
    List<CalendarConfigDetailDO> getCalendarRangeRecords(Long userId, Long year, String country);

    /**
     * 获取员工指定年月的考勤日历明细模版
     */
    List<CalendarConfigDetailDO> getCalendarRangeRecords(Long userId, Long year, Long month, String country);

    /**
     * 获取用户指定年的考勤日历明细模版map
     */
    @Deprecated
    Map<Long, CalendarConfigDetailDO> getCalendarDetailConfigMap(Long userId, Long year);

    /**
     * 获取用户在指定周期内的日历明细列表
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   开始时间
     * @param country   国家
     * @return 用户在指定周期内的日历明细列表
     */
    List<CalendarConfigDetailDO> getUserCalendarDetailsWithinPeriod(Long userId, Date startTime, Date endTime, String country);

    /**
     * 获取周期内用户日历明细
     */
    List<CalendarConfigDetailDO> getMultiUserCalendarDetailsWithinPeriod(List<Long> userIdList, Date startTime, Date endTime, String country);


    /**
     * 获取当前员工打卡时间生效的当日的日历配置明细
     */
    CalendarConfigDetailDO getLatestCalendarConfigDetail(Long userId, Date punchTime);


    /**
     * 根据日历配置id批量获取日历配置
     */
    List<CalendarConfigDO> getByCalendarConfigIds(List<Long> calendarConfigIds);

    /**
     * 批量查询国家下的所有日历配置
     */
    List<CalendarConfigDO> getCalendarConfigsByCountries(List<String> countries);

    /**
     * 根据日历配置id批量获取最新的日历配置
     */
    List<CalendarConfigDO> getLatestCalendarConfigsByIds(List<Long> calendarConfigIds);

    /**
     * 移动打卡查询休息日历明细
     */
    CalendarConfigDetailDO getMobileLatestCalendarConfigDetail(Long userId, Date punchTime);


    //=====================calendarConfigDetail============================================

    /**
     * 根据日历配置id查询日历明细
     */
    List<CalendarConfigDetailDO> selectCalendarDetailsByConfigIds(List<Long> calendarConfigIds);

    /**
     * 根据日历配置ids + 年份查询日历配置明细
     */
    List<CalendarConfigDetailDO> selectCalendarDetailsByIdsAndYear(List<Long> calendarConfigIds, List<Integer> years);


    //=====================calendarConfigRange============================================

    /**
     * 根据bizIds查询员工的日历配置适用范围
     */
    List<CalendarConfigRangeDO> selectCalendarConfigRange(List<Long> bizIds);

    /**
     * 根据日历配置id批量查询日历适用范围
     */
    List<CalendarConfigRangeDO> selectCalendarConfigRangeByConfigIds(List<Long> calendarConfigIds);

    /**
     * 查询员工指定时间内的考勤日历配置适用范围
     */
    List<CalendarConfigRangeDO> selectCalendarConfigByDate(CalendarConfigDateQuery query);

    /**
     * 批量修改或新增日历适用范围
     */
    void calendarConfigRangeUpdate(List<CalendarConfigRangeDO> updateList, List<CalendarConfigRangeDO> addList);

    /**
     * todo 不能使用，非日历模块的表无法适配开关行为，直接在原方法双写，等到所有的表都完成适配后，在整体替换
     */
    void userCalendarAndPunchUpdate(List<CalendarConfigRangeDO> addCalendarConfigRangeDOList,
                                    List<PunchConfigRangeDO> addPunchConfigRangeDOList,
                                    List<CalendarConfigRangeDO> updateCalendarConfigRangeDOList,
                                    List<PunchConfigRangeDO> updatePunchConfigRangeDOList,
                                    List<HrmsCompanyLeaveConfigRangDO> addLeaveRang,
                                    List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang,
                                    List<HrmsAttendanceUserLeaveConfigHistoryDO> addUserLeaveHistory,
                                    List<HrmsAttendanceUserLeaveConfigHistoryDO> updateUserLeaveHistory);

    void saveLegalLeaveConfigAndAttendanceDetail(List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList,
                                                 List<CalendarConfigDetailDO> needUpdateCalendarConfigDetailDetailList,
                                                 List<CalendarConfigDetailDO> needSaveCalendarConfigDetailDetailList);

    List<CalendarRangeCountDTO> countCalendarRange(List<Long> calendarConfigIds);

    void deleteRangeByBizId(Long bizId);

}
