package com.imile.hrms.manage.achievement.impl;

import com.imile.hrms.dao.achievement.dao.AchievementsEventsDao;
import com.imile.hrms.dao.achievement.dto.AchievementsEventsCheckDTO;
import com.imile.hrms.dao.achievement.mapper.AchievementsEventsMapper;
import com.imile.hrms.dao.achievement.model.AchievementsEventsDO;
import com.imile.hrms.manage.achievement.AchievementsEventsManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AchievementsEventsManageImpl implements AchievementsEventsManage {

    @Autowired
    private AchievementsEventsMapper achievementsEventsMapper;
    @Autowired
    private AchievementsEventsDao achievementsEventsDao;

    @Override
    public Integer checkAeNumByUserIdAndName(AchievementsEventsCheckDTO checkDTO) {
        return achievementsEventsMapper.checkAeNumByUserIdAndName(checkDTO);
    }

    @Override
    public List<AchievementsEventsDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return achievementsEventsDao.listByIds(idList);
    }
}
