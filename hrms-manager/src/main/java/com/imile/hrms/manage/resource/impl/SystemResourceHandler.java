package com.imile.hrms.manage.resource.impl;

import com.imile.hrms.common.context.UserContext;
import com.imile.hrms.common.enums.AttributeTypeEnum;
import com.imile.hrms.common.enums.ResourceTypeEnum;
import com.imile.hrms.manage.resource.ResourceHandlerAbstract;
import org.springframework.stereotype.Component;

/**
 * 系统管理员数据权限处理
 *
 * <AUTHOR>
 */
@Component
public class SystemResourceHandler extends ResourceHandlerAbstract {


    @Override
    public void handlerResource(UserContext userContext) {
    }

    @Override
    public String getResourceHandler() {
        return AttributeTypeEnum.SYSTEM.getCode();
    }

    @Override
    public ResourceTypeEnum getResourceType() {
        return null;
    }
}
