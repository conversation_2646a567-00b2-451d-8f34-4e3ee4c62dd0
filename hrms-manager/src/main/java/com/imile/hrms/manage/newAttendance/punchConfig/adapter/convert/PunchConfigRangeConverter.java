package com.imile.hrms.manage.newAttendance.punchConfig.adapter.convert;

import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigRangeDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.manage.newAttendance.punchConfig.adapter.mapstruct.PunchConfigRangeMapstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/2/17
 * @Description
 */
@Component
public class PunchConfigRangeConverter implements DataConverter<PunchConfigRangeDO, HrmsAttendancePunchConfigRangeDO> {

    @Override
    public HrmsAttendancePunchConfigRangeDO convertFromNew(PunchConfigRangeDO newObj) {
        return PunchConfigRangeMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public PunchConfigRangeDO convertFromOld(HrmsAttendancePunchConfigRangeDO oldObj) {
        return PunchConfigRangeMapstruct.INSTANCE.mapToNew(oldObj);
    }

    @Override
    public Class<PunchConfigRangeDO> getNewType() {
        return PunchConfigRangeDO.class;
    }

    @Override
    public Class<HrmsAttendancePunchConfigRangeDO> getOldType() {
        return HrmsAttendancePunchConfigRangeDO.class;
    }
}
