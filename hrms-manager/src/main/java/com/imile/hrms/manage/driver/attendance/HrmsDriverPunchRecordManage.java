package com.imile.hrms.manage.driver.attendance;

import com.imile.hrms.dao.driver.attendance.model.HrmsDriverPunchRecordDO;
import com.imile.hrms.dao.driver.attendance.model.HrmsDriverPunchRecordDetailDO;
import com.imile.hrms.dao.driver.attendance.query.DriverPunchRecordDetailQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HrmsDriverPunchRecordManage {
    void saveOrUpdate(HrmsDriverPunchRecordDO driverPunchRecord);

    List<HrmsDriverPunchRecordDO> selectPunchRecordDetail(DriverPunchRecordDetailQuery driverPunchRecordDetailQuery);

    void savePunchRecordAndDetail(HrmsDriverPunchRecordDO driverPunchRecord, HrmsDriverPunchRecordDetailDO driverPunchRecordDetail);

    void updatePunchRecordAndDetail(List<HrmsDriverPunchRecordDO> driverPunchRecordList, List<HrmsDriverPunchRecordDetailDO> driverPunchRecordDetailList);
}
