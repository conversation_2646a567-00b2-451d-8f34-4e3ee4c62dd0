package com.imile.hrms.manage.achievement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.achievement.dto.TargetItemBoardListDTO;
import com.imile.hrms.dao.achievement.model.AchievementsOrgTargetDO;

import java.util.List;

/**
 * <p>
 * 组织目标 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface AchievementsOrgTargetManage{


    /**
     * 根据父类id查询指标看板
     * @param parentId
     * @return
     */
    List<TargetItemBoardListDTO> boardList(Long parentId);

}
