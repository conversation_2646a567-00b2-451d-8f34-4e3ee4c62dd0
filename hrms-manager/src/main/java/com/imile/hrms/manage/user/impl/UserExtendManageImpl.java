package com.imile.hrms.manage.user.impl;

import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.dao.user.dao.HrmsUserExtendInfoDao;
import com.imile.hrms.dao.user.model.HrmsUserExtendInfoDO;
import com.imile.hrms.manage.user.UserExtendManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/3d
 */
@Slf4j
@Service
public class UserExtendManageImpl implements UserExtendManage {

    @Resource
    private HrmsUserExtendInfoDao userExtendDao;

    @Override
    public HrmsUserExtendInfoDO getUserExtendNullableByUserId(Long userId) {
        return userExtendDao.getByUserId(userId);
    }

    @Override
    public Map<Long, HrmsUserExtendInfoDO> getUserExtendMap(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        List<HrmsUserExtendInfoDO> recordList = userExtendDao.listByUserIds(userIdList);
        return recordList.stream()
                .collect(Collectors.toMap(HrmsUserExtendInfoDO::getUserId, Function.identity()));
    }

    @Override
    public void updateBatchById(List<HrmsUserExtendInfoDO> userExtendInfoList) {
        if (CollectionUtils.isEmpty(userExtendInfoList)) {
            return;
        }
        // 处理批量更新字段 (理论上不应该手动去填充这些信息，应该由MyBatis的拦截器去统一处理这些通用字段)
        for (HrmsUserExtendInfoDO userExtendInfo : userExtendInfoList) {
            BaseDOUtil.fillDOUpdate(userExtendInfo);
        }
        userExtendDao.updateBatchById(userExtendInfoList);
    }
}
