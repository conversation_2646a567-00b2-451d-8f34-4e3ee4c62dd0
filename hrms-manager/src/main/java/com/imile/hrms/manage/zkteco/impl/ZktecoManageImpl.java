package com.imile.hrms.manage.zkteco.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.dao.zkteco.DO.ZktecoAreasDO;
import com.imile.hrms.dao.zkteco.DO.ZktecoEmployeeAreaDO;
import com.imile.hrms.dao.zkteco.DO.ZktecoEmployeeDO;
import com.imile.hrms.dao.zkteco.mapper.ZktecoMapper;
import com.imile.hrms.manage.zkteco.ZktecoManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DS(HrmsProperties.Database.ZKT_URL)
public class ZktecoManageImpl implements ZktecoManage {

    @Autowired
    private ZktecoMapper zktecoMapper;

    @Override
    public List<ZktecoAreasDO> selectSnByArea() {
        return zktecoMapper.selectSnByArea();
    }

    @Override
    public List<ZktecoEmployeeDO> selectEmployee(List<String> empCodes) {
        return zktecoMapper.selectEmployee(empCodes);
    }

    @Override
    public List<ZktecoAreasDO> selectAreasByName(String areaName) {
        return zktecoMapper.selectAreasByName(areaName);
    }

    @Override
    public List<ZktecoAreasDO> selectAreasByAreaName(String areaName) {
        return zktecoMapper.selectAreasByAreaName(areaName);
    }

    @Override
    public List<ZktecoEmployeeAreaDO> selectEmployeeAreas(List<Integer> areaIdList) {
        if (CollectionUtils.isEmpty(areaIdList)) {
            return new ArrayList<>();
        }
        return zktecoMapper.selectEmployeeAreas(areaIdList);
    }

    @Override
    public List<ZktecoEmployeeDO> selectEmployeeByIds(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return zktecoMapper.selectEmployeeByIds(idList);
    }

    @Override
    public List<Integer> selectIClockEmployeeIdByIds(List<Integer> employeeIdList) {
        if (CollectionUtils.isEmpty(employeeIdList)) {
            return new ArrayList<>();
        }
        return zktecoMapper.selectEmployeeIdByIds(employeeIdList);
    }

}
