package com.imile.hrms.manage.training;

import com.imile.hrms.dao.training.model.HrmsTrainingManageDO;
import com.imile.hrms.dao.training.model.HrmsUserTrainingRecordDO;
import com.imile.hrms.dao.training.model.HrmsUserTrainingStatisticalDO;
import com.imile.hrms.dao.training.query.TrainingUserQuery;
import com.imile.hrms.dao.training.query.UserTrainingStatisticalQuery;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.manage.training.bo.TrainingInfoDetailBO;
import com.imile.hrms.manage.training.query.TrainingManageQuery;
import com.imile.hrms.manage.training.query.UserTrainingRecordPageQuery;
import com.imile.hrms.manage.training.query.UserTrainingRecordQuery;

import java.util.List;

public interface HrmsTrainingManagementManage {

    /**
     * 新增培训信息
     * @return
     */
    boolean addTrainingInfo(HrmsTrainingManageDO manageDO, List<HrmsUserTrainingRecordDO> recordDOList);


    /**
     * 编辑培训信息
     * @return
     */
    boolean updateTrainingInfo(HrmsTrainingManageDO manageDO, List<HrmsUserTrainingRecordDO> addRecordList, List<HrmsUserTrainingRecordDO> updateRecordList);


    /**
     * 获取培训具体信息
     * @param id
     * @return
     */
    TrainingInfoDetailBO getTrainingInfo(Long id);


    /**
     * 根据条件查询员工培训记录
     * @return
     */
    List<HrmsUserTrainingRecordDO> selectUserTrainingRecordPage(UserTrainingRecordPageQuery query);


    /**
     * 根据id查询培训场次信息
     * @param id
     * @return
     */
    HrmsTrainingManageDO getTraining(Long id);

    /**
     * 根据条件查询数据不分页
     * @param query
     * @return
     */
    List<HrmsTrainingManageDO> selectTraining(TrainingManageQuery query);


    List<HrmsUserTrainingRecordDO> selectTrainingRecord(UserTrainingRecordQuery query);

    /**
     * 获取员工培训次数统计信息
     */
    List<HrmsUserTrainingStatisticalDO> getStatisticalList(UserTrainingStatisticalQuery query);

    /**
     * 新增or编辑
     */
    void saveOrUpdStatisticalDO(List<HrmsUserTrainingStatisticalDO> statisticalList, Integer type);



}
