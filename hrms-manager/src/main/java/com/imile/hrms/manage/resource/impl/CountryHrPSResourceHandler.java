package com.imile.hrms.manage.resource.impl;

import com.imile.hrms.common.enums.AttributeTypeEnum;
import com.imile.hrms.common.enums.ResourceTypeEnum;
import com.imile.hrms.manage.resource.ResourceHandlerAbstract;
import org.springframework.stereotype.Component;

/**
 * Country HR（人事+系统）数据权限处理
 *
 * <AUTHOR>
 */
@Component
public class CountryHrPSResourceHandler extends ResourceHandlerAbstract {

    @Override
    public String getResourceHandler() {
        return AttributeTypeEnum.COUNTRY_HR_P_S.getCode();
    }

    @Override
    public ResourceTypeEnum getResourceType() {
        return ResourceTypeEnum.DEPT_ID;
    }
}
