package com.imile.hrms.manage.punch;

import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import com.imile.hrms.dao.punch.query.AttendancePunchConfigRangeByDateQuery;

import java.util.Date;
import java.util.List;

public interface HrmsAttendancePunchConfigRangeManage {

    /**
     * 获取用户当前的最新打卡规则
     */
    List<HrmsAttendancePunchConfigRangeDO> selectConfigRangeByBizId(List<Long> userIdList);

    /**
     * 获取用户所有的打卡规则(包括过期的)
     */
    List<HrmsAttendancePunchConfigRangeDO> selectUserAllConfigRangeByBizId(List<Long> userIdList);

    List<HrmsAttendancePunchConfigRangeDO> selectConfigRangeByPunchConfigId(List<Long> punchConfigIdList);

    /**
     * 查询员工指定时间内的打卡规则
     */
    List<HrmsAttendancePunchConfigRangeDO> selectPunchConfigRangeByDate(AttendancePunchConfigRangeByDateQuery query);


    /**
     * 用户打卡规则/日历范围检查
     */
    void attendancePunchRangeCheckUpdate(List<HrmsAttendancePunchConfigRangeDO> addPunchConfigRangeList, List<HrmsAttendanceConfigRangeDO> addConfigRangeList);

}
