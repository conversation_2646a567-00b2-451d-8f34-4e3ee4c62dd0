package com.imile.hrms.manage.recruitment.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.recruitment.HcCooperatorStatusEnum;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.recruitment.dao.HrmsRecruitmentJobDao;
import com.imile.hrms.dao.recruitment.dao.HrmsRecruitmentJobHeadcountCooperatorDao;
import com.imile.hrms.dao.recruitment.dao.HrmsRecruitmentJobHeadcountDao;
import com.imile.hrms.dao.recruitment.mapper.HrmsRecruitmentJobHeadcountCooperatorDOMapper;
import com.imile.hrms.dao.recruitment.mapper.HrmsRecruitmentJobHeadcountDOMapper;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobDO;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountCooperatorDO;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountCooperatorListQuery;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountDO;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountListQuery;
import com.imile.hrms.dao.recruitment.params.HcQuery;
import com.imile.hrms.dao.recruitment.params.HcStatusUpdateQuery;
import com.imile.hrms.dao.recruitment.params.HcStatusUpdateQuery;
import com.imile.hrms.dao.recruitment.params.JobStatusUpdateQuery;
import com.imile.hrms.dao.recruitment.params.OfferQuery;
import com.imile.hrms.dao.salary.model.HrmsEmployeeSalaryInfoDO;
import com.imile.hrms.manage.recruitment.RecruitmentHcManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/21
 */
@Slf4j
@Service
public class RecruitmentHcManageImpl implements RecruitmentHcManage {

    @Resource
    private HrmsRecruitmentJobHeadcountDao headcountDao;

    @Resource
    private HrmsRecruitmentJobHeadcountCooperatorDao hrmsRecruitmentJobHeadcountCooperatorDao;

    @Override
    public List<HrmsRecruitmentJobHeadcountDO> selectList(HcQuery query) {
        LambdaQueryWrapper<HrmsRecruitmentJobHeadcountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsRecruitmentJobHeadcountDO::getIsDelete, BusinessConstant.N);
        queryWrapper.orderByDesc(HrmsRecruitmentJobHeadcountDO::getCreateDate);
        if (query.getOrPermissionIsChineseInitiating() != null) {
            queryWrapper.or(true, param -> param.eq(HrmsRecruitmentJobHeadcountDO::getIsChineseInitiating, query.getOrPermissionIsChineseInitiating()));
        }
        if (query.getAndPermissionIsChineseInitiating() != null) {
            queryWrapper.eq(HrmsRecruitmentJobHeadcountDO::getIsChineseInitiating, query.getAndPermissionIsChineseInitiating());
        }
        if (!CollectionUtil.isEmpty(query.getIds())) {
            queryWrapper.in(HrmsRecruitmentJobHeadcountDO::getId, query.getIds());
        }
        if (!CollectionUtil.isEmpty(query.getJobIds())) {
            queryWrapper.in(HrmsRecruitmentJobHeadcountDO::getRecruitmentJobId, query.getJobIds());
        }
        if (!CollectionUtil.isEmpty(query.getStatus())) {
            queryWrapper.in(HrmsRecruitmentJobHeadcountDO::getStatus, query.getStatus());
        }
        if (!CollectionUtil.isEmpty(query.getGradeId())) {
            queryWrapper.in(HrmsRecruitmentJobHeadcountDO::getGradeId, query.getGradeId());
        }
        if (!CollectionUtil.isEmpty(query.getGradeNo())) {
            queryWrapper.in(HrmsRecruitmentJobHeadcountDO::getGradeNo, query.getGradeNo());
        }
        if (query.getIsChineseInitiating() != null) {
            queryWrapper.eq(HrmsRecruitmentJobHeadcountDO::getIsChineseInitiating, query.getIsChineseInitiating());
        }

        return headcountDao.list(queryWrapper);
    }

    @Override
    public HrmsRecruitmentJobHeadcountDO getById(Long id) {
        return headcountDao.getById(id);
    }

    @Override
    public boolean updateById(HrmsRecruitmentJobHeadcountDO entity) {
        if (entity == null || entity.getId() == null) {
            return false;
        }
        return headcountDao.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusBy(HcStatusUpdateQuery query) {
        if (query == null || (query.getJobId() == null && query.getId() == null) || query.getStatus() == null) {
            return false;
        }
        LambdaUpdateWrapper<HrmsRecruitmentJobHeadcountDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(HrmsRecruitmentJobHeadcountDO::getStatus, query.getStatus());
        queryWrapper.set(HrmsRecruitmentJobHeadcountDO::getLastUpdDate, new Date());
        queryWrapper.set(HrmsRecruitmentJobHeadcountDO::getLastUpdUserCode, RequestInfoHolder.getUserCode());
        queryWrapper.set(HrmsRecruitmentJobHeadcountDO::getLastUpdUserName, RequestInfoHolder.getUserName());
        queryWrapper.eq(HrmsRecruitmentJobHeadcountDO::getIsDelete, BusinessConstant.N);
        if (query.getId() != null) {
            queryWrapper.eq(HrmsRecruitmentJobHeadcountDO::getId, query.getId());
        } else if (query.getJobId() != null) {
            queryWrapper.eq(HrmsRecruitmentJobHeadcountDO::getRecruitmentJobId, query.getJobId());
        }
        return headcountDao.update(queryWrapper);
    }

    @Override
    public List<HrmsRecruitmentJobHeadcountDO> selectHcListBy(HrmsRecruitmentJobHeadcountListQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        if (!StringUtils.isEmpty(query.getApplyUserName())) {
            if (RequestInfoHolder.isChinese()) {
                query.setApplyUserName(query.getApplyUserName());
            } else {
                query.setApplyUserNameEn(query.getApplyUserName());
            }
        }
        return headcountDao.selectHcListBy(query);
    }

    @Override
    public Boolean insertHcCooperators(List<HrmsRecruitmentJobHeadcountCooperatorDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        return hrmsRecruitmentJobHeadcountCooperatorDao.saveBatch(records);
    }

    public List<HrmsRecruitmentJobHeadcountCooperatorDO> selectCooperatorsBy(HrmsRecruitmentJobHeadcountCooperatorListQuery query) {
        if (query == null || CollectionUtil.isEmpty(query.getHcIds())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HrmsRecruitmentJobHeadcountCooperatorDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsRecruitmentJobHeadcountCooperatorDO::getIsDelete, BusinessConstant.N);
        queryWrapper.in(HrmsRecruitmentJobHeadcountCooperatorDO::getRecruitmentJobHeadcountId, query.getHcIds());
        queryWrapper.orderByDesc(HrmsRecruitmentJobHeadcountCooperatorDO::getCreateDate);
        if (query.getCooperatorStatus() != null) {
            queryWrapper.eq(HrmsRecruitmentJobHeadcountCooperatorDO::getCooperatorStatus, query.getCooperatorStatus());
        }
        return hrmsRecruitmentJobHeadcountCooperatorDao.list(queryWrapper);
    }


    @Override
    public Boolean updateCooperatorStatusBy(List<Long> hcIds, List<Long> userIds, HcCooperatorStatusEnum status) {
        if (CollectionUtil.isEmpty(hcIds) || CollectionUtil.isEmpty(userIds) || status == null) {
            return false;
        }
        return hrmsRecruitmentJobHeadcountCooperatorDao.updateCooperatorStatusBy(hcIds, userIds, status);
    }

    @Override
    public Boolean removeCooperator(List<Long> hcIds) {
        if (CollectionUtil.isEmpty(hcIds)) {
            return false;
        }
        LambdaUpdateWrapper<HrmsRecruitmentJobHeadcountCooperatorDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HrmsRecruitmentJobHeadcountCooperatorDO::getIsDelete, BusinessConstant.Y);
        updateWrapper.eq(HrmsRecruitmentJobHeadcountCooperatorDO::getIsDelete, BusinessConstant.N);
        updateWrapper.in(HrmsRecruitmentJobHeadcountCooperatorDO::getRecruitmentJobHeadcountId, hcIds);
        return hrmsRecruitmentJobHeadcountCooperatorDao.update(updateWrapper);
    }
}
