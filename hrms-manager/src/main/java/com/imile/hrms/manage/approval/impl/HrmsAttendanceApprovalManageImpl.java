package com.imile.hrms.manage.approval.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.approval.dao.HrmsApplicationFormAttrDao;
import com.imile.hrms.dao.approval.dao.HrmsApplicationFormDao;
import com.imile.hrms.dao.approval.dao.HrmsApplicationFormRelationDao;
import com.imile.hrms.dao.approval.dao.HrmsApprovalFormDao;
import com.imile.hrms.dao.approval.dao.HrmsApprovalFormUserInfoDao;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormAttrDO;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormDO;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormRelationDO;
import com.imile.hrms.dao.approval.model.HrmsApprovalFormDO;
import com.imile.hrms.dao.approval.model.HrmsApprovalFormUserInfoDO;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalAttendanceDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.hrms.dao.punch.dao.EmployeePunchRecordDao;
import com.imile.hrms.dao.punch.dao.HrmsAttendanceUserCardConfigDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailAbnormalDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailDao;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseRecordDao;
import com.imile.hrms.dao.punch.model.EmployeePunchRecordDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceUserCardConfigDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailAbnormalDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseRecordDO;
import com.imile.hrms.dao.user.dao.HrmsUserLeaveRecordDao;
import com.imile.hrms.dao.user.dao.HrmsUserLeaveStageDetailDao;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.manage.approval.HrmsAttendanceApprovalManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-25
 * @version: 1.0
 */
@Service
public class HrmsAttendanceApprovalManageImpl implements HrmsAttendanceApprovalManage {
    @Autowired
    private HrmsApplicationFormDao hrmsApplicationFormDao;
    @Autowired
    private HrmsApplicationFormAttrDao hrmsApplicationFormAttrDao;
    @Autowired
    private HrmsEmployeeAbnormalOperationRecordDao hrmsEmployeeAbnormalOperationRecordDao;
    @Autowired
    private HrmsApplicationFormRelationDao hrmsApplicationFormRelationDao;
    @Autowired
    private HrmsAttendanceUserCardConfigDao hrmsAttendanceUserCardConfigDao;
    @Autowired
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Autowired
    private HrmsEmployeeAbnormalAttendanceDao hrmsEmployeeAbnormalAttendanceDao;
    @Autowired
    private HrmsUserLeaveRecordDao hrmsUserLeaveRecordDao;
    @Autowired
    private HrmsUserLeaveStageDetailDao hrmsUserLeaveStageDetailDao;
    @Autowired
    private HrmsAttendanceEmployeeDetailDao hrmsAttendanceEmployeeDetailDao;
    @Autowired
    private HrmsApprovalFormDao hrmsApprovalFormDao;
    @Autowired
    private HrmsApprovalFormUserInfoDao hrmsApprovalFormUserInfoDao;

    @Autowired
    private HrmsWarehouseRecordDao hrmsWarehouseRecordDao;

    @Autowired
    private HrmsWarehouseDetailDao hrmsWarehouseDetailDao;

    @Autowired
    private HrmsWarehouseDetailAbnormalDao hrmsWarehouseDetailAbnormalDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaveAdd(HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.save(hrmsApplicationFormDO);
        }
        if (CollectionUtils.isNotEmpty(hrmsApplicationFormAttrDOArrayList)) {
            hrmsApplicationFormAttrDao.saveBatch(hrmsApplicationFormAttrDOArrayList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaveUpdate(HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.updateById(hrmsApplicationFormDO);
        }
        if (CollectionUtils.isNotEmpty(hrmsApplicationFormAttrDOArrayList)) {
            hrmsApplicationFormAttrDao.updateBatchById(hrmsApplicationFormAttrDOArrayList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(HrmsApplicationFormDO hrmsApplicationFormDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                       HrmsAttendanceUserCardConfigDO userCardConfigDO, List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                       HrmsUserLeaveRecordDO userLeaveRecord) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.updateById(hrmsApplicationFormDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (userCardConfigDO != null) {
            hrmsAttendanceUserCardConfigDao.updateById(userCardConfigDO);
        }
        if (CollUtil.isNotEmpty(userLeaveStageDetailInfoList)) {
            hrmsUserLeaveStageDetailDao.updateBatchById(userLeaveStageDetailInfoList);
        }
        if (ObjectUtil.isNotNull(userLeaveRecord)) {
            hrmsUserLeaveRecordDao.save(userLeaveRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(HrmsApplicationFormDO formDO, List<HrmsApplicationFormRelationDO> relationDOS, List<HrmsApplicationFormAttrDO> attrDOS) {
        if (formDO != null) {
            hrmsApplicationFormDao.updateById(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            hrmsApplicationFormRelationDao.updateBatchById(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(attrDOS)) {
            hrmsApplicationFormAttrDao.updateBatchById(attrDOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void formAdd(HrmsApplicationFormDO hrmsApplicationFormDO,
                        List<HrmsApplicationFormRelationDO> relationDOS,
                        List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList,
                        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO,
                        HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList,
                        HrmsUserLeaveRecordDO userLeaveRecord) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.save(hrmsApplicationFormDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            hrmsApplicationFormRelationDao.saveBatch(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(hrmsApplicationFormAttrDOArrayList)) {
            hrmsApplicationFormAttrDao.saveBatch(hrmsApplicationFormAttrDOArrayList);
        }
        if (hrmsEmployeeAbnormalOperationRecordDO != null) {
            hrmsEmployeeAbnormalOperationRecordDao.save(hrmsEmployeeAbnormalOperationRecordDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
            hrmsUserLeaveStageDetailDao.updateBatchById(userLeaveStageDetailList);
        }
        if (ObjectUtil.isNotNull(userLeaveRecord)) {
            hrmsUserLeaveRecordDao.save(userLeaveRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void formUpdate(HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> relationDOS, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.updateById(hrmsApplicationFormDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            hrmsApplicationFormRelationDao.updateBatchById(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(hrmsApplicationFormAttrDOArrayList)) {
            hrmsApplicationFormAttrDao.updateBatchById(hrmsApplicationFormAttrDOArrayList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reissueFormAdd(HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> relationDOS, List<HrmsApplicationFormAttrDO> hrmsApplicationFormAttrDOArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsAttendanceUserCardConfigDO userCardConfigDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.save(hrmsApplicationFormDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOS)) {
            hrmsApplicationFormRelationDao.saveBatch(relationDOS);
        }
        if (CollectionUtils.isNotEmpty(hrmsApplicationFormAttrDOArrayList)) {
            hrmsApplicationFormAttrDao.saveBatch(hrmsApplicationFormAttrDOArrayList);
        }
        if (hrmsEmployeeAbnormalOperationRecordDO != null) {
            hrmsEmployeeAbnormalOperationRecordDao.save(hrmsEmployeeAbnormalOperationRecordDO);
        }
        if (userCardConfigDO != null) {
            hrmsAttendanceUserCardConfigDao.updateById(userCardConfigDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDurationAdd(HrmsApplicationFormDO hrmsApplicationFormDO, List<HrmsApplicationFormRelationDO> relationList, List<HrmsApplicationFormAttrDO> applicationFormAttrArrayList, HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        if (hrmsApplicationFormDO != null) {
            hrmsApplicationFormDao.save(hrmsApplicationFormDO);
        }
        if (CollectionUtils.isNotEmpty(relationList)) {
            hrmsApplicationFormRelationDao.saveBatch(relationList);
        }
        if (CollectionUtils.isNotEmpty(applicationFormAttrArrayList)) {
            hrmsApplicationFormAttrDao.saveBatch(applicationFormAttrArrayList);
        }
        if (hrmsEmployeeAbnormalOperationRecordDO != null) {
            hrmsEmployeeAbnormalOperationRecordDao.save(hrmsEmployeeAbnormalOperationRecordDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDurationUpdate(HrmsApplicationFormDO formDO, HrmsAttendanceEmployeeDetailDO attendanceEmployeeDetailDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO, HrmsWarehouseDetailDO warehouseDetailDO, HrmsWarehouseDetailAbnormalDO warehouseDetailAbnormalDO) {
        if (formDO != null) {
            hrmsApplicationFormDao.updateById(formDO);
        }
        if (Objects.nonNull(attendanceEmployeeDetailDO)) {
            hrmsAttendanceEmployeeDetailDao.save(attendanceEmployeeDetailDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (warehouseDetailDO != null) {
            hrmsWarehouseDetailDao.updateById(warehouseDetailDO);
        }
        if (warehouseDetailAbnormalDO != null) {
            hrmsWarehouseDetailAbnormalDao.updateById(warehouseDetailAbnormalDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reissueCardMqPassUpdate(HrmsApplicationFormDO formDO, EmployeePunchRecordDO employeePunchRecordDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO, HrmsWarehouseRecordDO warehouseRecordDO) {
        if (formDO != null) {
            hrmsApplicationFormDao.updateById(formDO);
        }
        if (employeePunchRecordDO != null) {
            employeePunchRecordDao.save(employeePunchRecordDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (warehouseRecordDO != null) {
            hrmsWarehouseRecordDao.save(warehouseRecordDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void leaveMqPassUpdate(HrmsApplicationFormDO formDO, HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        if (formDO != null) {
            hrmsApplicationFormDao.updateById(formDO);
        }
        if (abnormalAttendanceDO != null) {
            hrmsEmployeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeReissueCardMqPassUpdate(List<HrmsApplicationFormDO> formDOList, HrmsApplicationFormAttrDO attrDO, List<EmployeePunchRecordDO> employeePunchRecordDOS, HrmsAttendanceUserCardConfigDO cardConfigDO) {
        if (CollectionUtils.isNotEmpty(formDOList)) {
            hrmsApplicationFormDao.updateBatchById(formDOList);
        }
        if (attrDO != null) {
            hrmsApplicationFormAttrDao.updateById(attrDO);
        }
        if (CollectionUtils.isNotEmpty(employeePunchRecordDOS)) {
            employeePunchRecordDao.updateBatchById(employeePunchRecordDOS);
        }
        if (cardConfigDO != null) {
            hrmsAttendanceUserCardConfigDao.updateById(cardConfigDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeLeaveMqPassUpdate(List<HrmsApplicationFormDO> formDOList, HrmsApplicationFormAttrDO attrDO, List<HrmsUserLeaveStageDetailDO> updateStageList, List<HrmsUserLeaveRecordDO> addLeaveRecordList, List<HrmsAttendanceEmployeeDetailDO> employeeDetailDOS) {
        if (CollectionUtils.isNotEmpty(formDOList)) {
            hrmsApplicationFormDao.updateBatchById(formDOList);
        }
        if (attrDO != null) {
            hrmsApplicationFormAttrDao.updateById(attrDO);
        }
        if (CollectionUtils.isNotEmpty(updateStageList)) {
            hrmsUserLeaveStageDetailDao.updateBatchById(updateStageList);
        }
        if (CollectionUtils.isNotEmpty(addLeaveRecordList)) {
            hrmsUserLeaveRecordDao.saveBatch(addLeaveRecordList);
        }
        if (CollectionUtils.isNotEmpty(employeeDetailDOS)) {
            hrmsAttendanceEmployeeDetailDao.updateBatchById(employeeDetailDOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormAdd(HrmsApprovalFormDO hrmsApprovalFormDO, List<HrmsApprovalFormUserInfoDO> hrmsApprovalFormUserInfoList) {
        if (ObjectUtil.isNotNull(hrmsApprovalFormDO)) {
            hrmsApprovalFormDao.save(hrmsApprovalFormDO);
        }
        if (CollUtil.isNotEmpty(hrmsApprovalFormUserInfoList)) {
            hrmsApprovalFormUserInfoDao.saveBatch(hrmsApprovalFormUserInfoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormAddAndUpdate(HrmsApprovalFormDO approvalForm, List<HrmsApprovalFormUserInfoDO> approvalFormUserInfoList) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            hrmsApprovalFormDao.updateById(approvalForm);
        }
        List<HrmsApprovalFormUserInfoDO> deleteList = approvalFormUserInfoList.stream().filter(item -> IsDeleteEnum.YES.getCode() == item.getIsDelete()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deleteList)) {
            hrmsApprovalFormUserInfoDao.updateBatchById(deleteList);
        }
        List<HrmsApprovalFormUserInfoDO> insertList = approvalFormUserInfoList.stream().filter(item -> IsDeleteEnum.YES.getCode() != item.getIsDelete()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(insertList)) {
            hrmsApprovalFormUserInfoDao.saveBatch(insertList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormUpdate(HrmsApprovalFormDO approvalForm, List<HrmsApprovalFormUserInfoDO> approvalFormUserInfoList) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            hrmsApprovalFormDao.updateById(approvalForm);
        }
        if (CollUtil.isNotEmpty(approvalFormUserInfoList)) {
            hrmsApprovalFormUserInfoDao.updateBatchById(approvalFormUserInfoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalFormOrLeaveUpdate(HrmsApprovalFormDO approvalForm, List<HrmsApprovalFormUserInfoDO> approvalFormUserInfoList, List<HrmsUserLeaveStageDetailDO> updateUserLeaveStageDetail, List<HrmsUserLeaveRecordDO> addUserLeaveRecord) {
        if (ObjectUtil.isNotNull(approvalForm)) {
            hrmsApprovalFormDao.updateById(approvalForm);
        }
        if (CollUtil.isNotEmpty(approvalFormUserInfoList)) {
            hrmsApprovalFormUserInfoDao.updateBatchById(approvalFormUserInfoList);
        }
        if (CollUtil.isNotEmpty(updateUserLeaveStageDetail)) {
            hrmsUserLeaveStageDetailDao.updateBatchById(updateUserLeaveStageDetail);
        }
        if (CollUtil.isNotEmpty(addUserLeaveRecord)) {
            hrmsUserLeaveRecordDao.saveBatch(addUserLeaveRecord);
        }
    }
}
