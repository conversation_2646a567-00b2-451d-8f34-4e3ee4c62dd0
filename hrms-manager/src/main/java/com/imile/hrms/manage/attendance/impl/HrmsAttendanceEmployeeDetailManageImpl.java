package com.imile.hrms.manage.attendance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.enums.attendance.ImportTypeEnum;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.common.util.PattenUtil;
import com.imile.hrms.dao.attendance.bo.UserAttendanceBO;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceEmployeeDetailSnapshotDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalAttendanceDao;
import com.imile.hrms.dao.attendance.dao.HrmsEmployeeAbnormalAttendanceSnapshotDao;
import com.imile.hrms.dao.attendance.dto.ValidateDTO;
import com.imile.hrms.dao.attendance.mapper.HrmsAttendanceEmployeeDetailMapper;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailSnapshotDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.hrms.dao.attendance.query.UserAttendanceQuery;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntCompanyDao;
import com.imile.hrms.dao.organization.dao.HrmsEntDeptDao;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.user.dao.HrmsUserInfoDao;
import com.imile.hrms.dao.user.dao.HrmsUserLeaveRecordDao;
import com.imile.hrms.dao.user.dao.HrmsUserLeaveStageDetailDao;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.manage.attendance.HrmsAttendanceEmployeeDetailManage;
import com.imile.hrms.manage.attendance.dto.AttendanceImportDTO;
import com.imile.hrms.manage.attendance.dto.AttendanceImportErrorDTO;
import com.imile.hrms.manage.attendance.dto.UserWorkHoursDTO;
import com.imile.hrms.manage.common.CommonManage;
import com.imile.hrms.manage.logrecord.LogRecord;
import com.imile.hrms.manage.newAttendance.calendar.adapter.CalendarConfigDaoFacade;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 员工考勤明细manage
 *
 * <AUTHOR>
 */
@Service
public class HrmsAttendanceEmployeeDetailManageImpl implements HrmsAttendanceEmployeeDetailManage {
    @Autowired
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Autowired
    private HrmsAttendanceEmployeeDetailDao hrmsAttendanceEmployeeDetailDao;
    @Autowired
    private CommonManage commonManage;
    @Autowired
    private HrmsAttendanceEmployeeDetailMapper hrmsAttendanceEmployeeDetailMapper;
    @Resource
    private CalendarConfigDaoFacade calendarConfigDaoFacade;
    @Autowired
    private IHrmsIdWorker iHrmsIdWorker;
    @Autowired
    private LogRecord logRecord;
    @Autowired
    private HrmsEntCompanyDao hrmsEntCompanyDao;
    @Autowired
    private HrmsEntDeptDao hrmsEntDeptDao;
    @Autowired
    private HrmsEntPostDao hrmsEntPostDao;
    @Autowired
    private HrmsUserLeaveRecordDao hrmsUserLeaveRecordDao;
    @Autowired
    private HrmsUserLeaveStageDetailDao hrmsUserLeaveStageDetailDao;
    @Autowired
    private HrmsEmployeeAbnormalAttendanceDao hrmsEmployeeAbnormalAttendanceDao;
    @Autowired
    private HrmsAttendanceEmployeeDetailSnapshotDao hrmsAttendanceEmployeeDetailSnapshotDao;
    @Autowired
    private HrmsEmployeeAbnormalAttendanceSnapshotDao hrmsEmployeeAbnormalAttendanceSnapshotDao;

    @Autowired
    private HrmsProperties hrmsProperties;

    private static final String DEFAULT_DATE_FORMAT_STR = "yyyy/MM/dd";

    private static final String NEED_PRESENT = "PRESENT";

    private static final String PATTERN_STR = "^(0.[1-9]+)?[P]$";

    private static final String DATE_FORMAT = "^\\d{4}/\\d{1,2}/\\d{1,2}";

    private static final String WORK_NO_EN = "Work No";
    private static final String WORK_NO_CN = "工号";

    private static final String USER_CODE_EN = "USER CODE";
    private static final String USER_CODE_CN = "用户账号";
    private static final String USER_NAME_EN = "Employee name";
    private static final String USER_NAME_CN = "姓名";
    private static final String ATTENDANCE_TIME_CN = "出勤时间";
    private static final String ATTENDANCE_TIME_EN = "attendance time";
    private static final String ATTENDANCE_HOURS_CN = "出勤小时数";
    private static final String ATTENDANCE_HOURS_EN = "attendance hours";

    //具体的考勤时间，最大值为8小时
    private static final double ATTENDANCE_HOURS_MAX = 8;

    //具体的考勤时间，最小值为0小时
    private static final double ATTENDANCE_HOURS_MIN = 0;

    //判断一个字符串是不是浮点数类型
    private static final String doubleMatch = "^[-\\+]?[.\\d]*$";

    //导入模板的语言，默认使用中文，根据具体的模板来判断
    private static String languageType = "CN";

    private static final BigDecimal MAX_DAY_HOURS = new BigDecimal("24");
    private static final BigDecimal MAX_ATTENDANCE_HOURS = new BigDecimal("8");


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void attendanceGenerateUpdate(List<HrmsAttendanceEmployeeDetailDO> addEmployeeDetailDOList, List<HrmsAttendanceEmployeeDetailDO> updateEmployeeDetailDOList, List<HrmsEmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList, List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList, List<HrmsUserLeaveStageDetailDO> updateUserLeaveStageDetailDOList, List<HrmsUserLeaveRecordDO> addUserLeaveRecordDOList) {
//        // 假期扣减新方案--->定时任务不需要操作假期余额了。操作假期余额放在各个入口处
//        updateUserLeaveStageDetailDOList = Lists.newArrayList();
//        // 假期记录表，不需要落了，改到具体操作的地方落
//        addUserLeaveRecordDOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(addEmployeeDetailDOList)) {
            hrmsAttendanceEmployeeDetailDao.saveBatch(addEmployeeDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(updateEmployeeDetailDOList)) {
            hrmsAttendanceEmployeeDetailDao.updateBatchById(updateEmployeeDetailDOList);
            //由于移动打卡每次都计算异常，避免数据冗余太多，逻辑删除改为物理删除
            //List<HrmsAttendanceEmployeeDetailDO> deleteDOList = updateEmployeeDetailDOList.stream().filter(item -> Objects.nonNull(item.getIsDelete())
            //        && item.getIsDelete() == IsDeleteEnum.YES.getCode()).collect(Collectors.toList());
            //if (CollectionUtils.isNotEmpty(deleteDOList)) {
            //    hrmsAttendanceEmployeeDetailDao.removeByIds(deleteDOList.stream().map(HrmsAttendanceEmployeeDetailDO::getId).collect(Collectors.toList()));
            //}
        }
        if (CollectionUtils.isNotEmpty(addAbnormalAttendanceDOList)) {
            hrmsEmployeeAbnormalAttendanceDao.saveBatch(addAbnormalAttendanceDOList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalAttendanceDOList)) {
            hrmsEmployeeAbnormalAttendanceDao.updateBatchById(updateAbnormalAttendanceDOList);
            //由于移动打卡每次都计算异常，避免数据冗余太多，逻辑删除改为物理删除
            //List<HrmsEmployeeAbnormalAttendanceDO> deleteDOList = updateAbnormalAttendanceDOList.stream().filter(item -> Objects.nonNull(item.getIsDelete())
            //        && item.getIsDelete() == IsDeleteEnum.YES.getCode()).collect(Collectors.toList());
            //if (CollectionUtils.isNotEmpty(deleteDOList)) {
            //    hrmsEmployeeAbnormalAttendanceDao.removeByIds(deleteDOList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList()));
            //}
        }
//        if (CollectionUtils.isNotEmpty(updateUserLeaveStageDetailDOList)) {
//            hrmsUserLeaveStageDetailDao.updateBatchById(updateUserLeaveStageDetailDOList);
//        }
//        if (CollectionUtils.isNotEmpty(addUserLeaveRecordDOList)) {
//            hrmsUserLeaveRecordDao.saveBatch(addUserLeaveRecordDOList);
//        }
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectAttendanceByDayId(Long userId, Long startDayId, Long endDayId) {
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryAttendanceWrapper = Wrappers.lambdaQuery();
        queryAttendanceWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryAttendanceWrapper.eq(HrmsAttendanceEmployeeDetailDO::getUserId, userId);
        queryAttendanceWrapper.ge(HrmsAttendanceEmployeeDetailDO::getDayId, startDayId);
        queryAttendanceWrapper.le(HrmsAttendanceEmployeeDetailDO::getDayId, endDayId);
        List<HrmsAttendanceEmployeeDetailDO> recordList = hrmsAttendanceEmployeeDetailDao.list(queryAttendanceWrapper);
        return recordList;
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectAttendanceByCycleDay(List<Long> userIdList, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryAttendanceWrapper = Wrappers.lambdaQuery();
        queryAttendanceWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryAttendanceWrapper.in(HrmsAttendanceEmployeeDetailDO::getUserId, userIdList);
        queryAttendanceWrapper.ge(HrmsAttendanceEmployeeDetailDO::getDayId, startDayId);
        queryAttendanceWrapper.le(HrmsAttendanceEmployeeDetailDO::getDayId, endDayId);
        List<HrmsAttendanceEmployeeDetailDO> recordList = hrmsAttendanceEmployeeDetailDao.list(queryAttendanceWrapper);
        return recordList;
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectAttendanceByDayIdList(Long userId, List<Long> dayIdList) {
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryAttendanceWrapper = Wrappers.lambdaQuery();
        queryAttendanceWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryAttendanceWrapper.eq(HrmsAttendanceEmployeeDetailDO::getUserId, userId);
        queryAttendanceWrapper.in(HrmsAttendanceEmployeeDetailDO::getDayId, dayIdList);
        List<HrmsAttendanceEmployeeDetailDO> recordList = hrmsAttendanceEmployeeDetailDao.list(queryAttendanceWrapper);
        return recordList;
    }

//    // todo 组织架构优化: 引用的方法没有用到
//    @Override
//    public List<Map<String, Object>> doImport(List<Map<String, Object>> importList, String type) {
//
//        //importList的每个map可能存在usercode相同的，即同一个用户导入多条数据
//        if (CollUtil.isEmpty(importList) || StringUtils.isEmpty(type)) {
//            return new ArrayList<>();
//        }
//        ImportTypeEnum importTypeEnum = ImportTypeEnum.parserEnum(type);
//        if (importTypeEnum == null) {
//            return new ArrayList<>();
//        }
//
//        //转换成DTO
//        List<AttendanceImportDTO> attendanceImportDTOList = convertFromImportMap(importList, type);
//
//        List<AttendanceImportErrorDTO> failList = new ArrayList<>();
//
//        //基础数据格式判断
//        List<AttendanceImportDTO> filterImportList = importDataValidate(attendanceImportDTOList, failList, importTypeEnum);
//
//        //获取工号对应员工的员工信息,生成map
//        //userCodeList的值可能存在重复的，值usercode重复，即同一个用户导入多条数据
//        List<String> userCodeList = filterImportList.stream()
//                .map(entry -> entry.getUserCode())
//                .collect(Collectors.toList());
//        List<HrmsUserInfoDO> userInfoDOList = new ArrayList<>();
//        if (!CollUtil.isEmpty(userCodeList)) {
//            userInfoDOList = hrmsUserInfoDao.getByUserCodes(userCodeList, RequestInfoHolder.getCompanyId());
//        }
//        //员工编码userCode->员工 map
//        Map<String, HrmsUserInfoDO> userCodeMaps = userInfoDOList
//                .stream()
//                .collect(Collectors.toMap(HrmsUserInfoDO::getUserCode, item -> item, (oldValue, newValue) -> oldValue));
//        //员工ID集合
//        List<Long> userIds = userInfoDOList.stream().map(HrmsUserInfoDO::getId).collect(Collectors.toList());
//        //（userId$dayId）-> HrmsAttendanceEmployeeDetailDO map
///*        Map<String, HrmsAttendanceEmployeeDetailDO> doMaps = hrmsAttendanceEmployeeDetailDao.listByUserId(userIds)
//                .stream()
//                .collect(Collectors.toMap(keyGet(), item -> item, (oldValue, newValue) -> newValue));*/
//
//        Map<String, List<HrmsAttendanceEmployeeDetailDO>> doMaps = hrmsAttendanceEmployeeDetailDao.listByUserId(userIds)
//                .stream().collect(Collectors.groupingBy(o -> o.getUserId() + "$" + o.getDayId()));
//
//        //hrmsAttendanceConfigManage.getRangeRecords()
//        //校验数据并生成落库类
//        List<HrmsAttendanceEmployeeDetailDO> saveList = new ArrayList<>();
//        List<HrmsAttendanceEmployeeDetailDO> updateList = new ArrayList<>();
//
//        for (AttendanceImportDTO item : filterImportList) {
//            try {
//                ValidateDTO validateDTO = new ValidateDTO();
//                //参数校验
//                if (!this.doValidate(item, userCodeMaps, validateDTO, failList, importTypeEnum, doMaps)) {
//                    continue;
//                }
//                HrmsUserInfoDO userInfoDO = validateDTO.getHrmsUserInfoDO();
//                Integer year = validateDTO.getYear();
//                //查询该员工所有休息日，节假日 (dayId)->HrmsAttendanceConfigDetailDO
//                Map<Long, HrmsAttendanceConfigDetailDO> dayIdMaps = hrmsAttendanceConfigManage.getRangeRecords(userInfoDO.getId(), Long.valueOf(year), RequestInfoHolder.getCompanyId())
//                        .stream()
//                        .collect(Collectors.toMap(HrmsAttendanceConfigDetailDO::getDayId, config -> config, (oldValue, newValue) -> oldValue));
//                //根据传入的类型来导入相应的数据
//                switch (importTypeEnum) {
//                    case ATTENDANCE:
//                        importAttendance(doMaps, dayIdMaps, saveList, item, userInfoDO, updateList);
//                        break;
//                    case OVERTIME:
//                        importOvertime(doMaps, dayIdMaps, saveList, item, userInfoDO, updateList);
//                        break;
//                    default:
//                        break;
//                }
//                //item.remove("rowNum");
//
//
//            } catch (Exception e) {
//                AttendanceImportErrorDTO errorDTO = new AttendanceImportErrorDTO();
//                errorDTO.setUserCode(item.getUserCode());
//                errorDTO.setUserName(item.getUserName());
//                errorDTO.setAttendanceDate(item.getAttendanceDate());
//                errorDTO.setAttendanceHours(item.getAttendanceHours());
//                errorDTO.setErrorMessage(e.getMessage());
//                failList.add(errorDTO);
//            }
//
//        }
//        if (!CollUtil.isEmpty(saveList)) {
//            commonManage.persistence(saveList, hrmsAttendanceEmployeeDetailMapper);
//        }
//        if (!CollUtil.isEmpty(updateList)) {
//            commonManage.persistence(updateList, hrmsAttendanceEmployeeDetailMapper);
//        }
//
//
//        return convertToFailMap(failList);
//
//    }

    /*@Override
    public List<AttendanceDTO> list(AttendanceEmployeeQuery query) {
        List<AttendanceDTO> attendanceList = hrmsAttendanceEmployeeDetailMapper.list(query);
        if (CollectionUtils.isEmpty(attendanceList)) {
            return null;
        }
        //查询部门信息
        List<Long> deptIds = attendanceList.stream().map(o -> o.getDeptId()).collect(Collectors.toList());
        Map<Long, HrmsEntDeptDO> deptMap = hrmsEntDeptDao.listByDeptIds(deptIds).stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
        //查询岗位信息
        List<Long> postIds = attendanceList.stream().map(o -> o.getPostId()).collect(Collectors.toList());
        Map<Long, HrmsEntPostDO> postMap = hrmsEntPostDao.listByPostList(postIds).stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
        //查询公司信息
        List<Long> companyIds = attendanceList.stream().map(o -> o.getCompanyId()).collect(Collectors.toList());
        Map<Long, HrmsEntCompanyDO> companyMap = hrmsEntCompanyDao.listByIds(companyIds).stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
        for (AttendanceDTO attendanceDTO : attendanceList) {
            HrmsEntDeptDO deptDO = deptMap.get(attendanceDTO.getDeptId());
            HrmsEntPostDO postDO = postMap.get(attendanceDTO.getPostId());
            HrmsEntCompanyDO companyDO = companyMap.get(attendanceDTO.getCompanyId());
            if (deptDO != null) {
                attendanceDTO.setDeptNameCn(deptDO.getDeptNameCn());
                attendanceDTO.setDeptNameEn(deptDO.getDeptNameEn());
            }
            if (postDO != null) {
                attendanceDTO.setPostNameCn(postDO.getPostNameCn());
                attendanceDTO.setPostNameEn(postDO.getPostNameEn());
            }
            if (companyDO != null) {
                attendanceDTO.setCompanyNameCn(companyDO.getCompanyNameCn());
                attendanceDTO.setCompanyNameEn(companyDO.getCompanyNameEn());
            }
        }
        return attendanceList;
    }*/

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> userAttendanceList(UserAttendanceQuery query) {
        return hrmsAttendanceEmployeeDetailDao.userAttendanceList(query);
    }

    @Override
    public List<UserAttendanceBO> attendanceUserList(UserAttendanceQuery query) {
//        return hrmsAttendanceEmployeeDetailMapper.attendanceUserList(query);
        return calendarConfigDaoFacade.getCalendarConfigJoinRelateAdapter().attendanceUserList(query);
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByYear(Long userId, Long year) {
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getUserId, userId);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getYear, year);
        List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS = hrmsAttendanceEmployeeDetailDao.list(queryWrapper);
        return hrmsAttendanceEmployeeDetailDOS;
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByYearAndMonth(Long userId, Long year, Long month) {
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getUserId, userId);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getYear, year);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getMonth, month);
        List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS = hrmsAttendanceEmployeeDetailDao.list(queryWrapper);
        return hrmsAttendanceEmployeeDetailDOS;
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByDayId(Long userId, Long dayId) {
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getUserId, userId);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getDayId, dayId);
        List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS = hrmsAttendanceEmployeeDetailDao.list(queryWrapper);
        return hrmsAttendanceEmployeeDetailDOS;
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByPostId(List<Long> postIdList) {
        if (CollectionUtils.isEmpty(postIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsAttendanceEmployeeDetailDO::getPostId, postIdList);
        List<HrmsAttendanceEmployeeDetailDO> hrmsAttendanceEmployeeDetailDOS = hrmsAttendanceEmployeeDetailDao.list(queryWrapper);
        return hrmsAttendanceEmployeeDetailDOS;
    }

    @Override
    public boolean batchUpdateById(List<HrmsAttendanceEmployeeDetailDO> entityList) {
        return hrmsAttendanceEmployeeDetailDao.updateBatchById(entityList);
    }

    @Override
    public boolean batchSave(List<HrmsAttendanceEmployeeDetailDO> recordDOList) {
        return hrmsAttendanceEmployeeDetailDao.saveBatch(recordDOList);
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByUserId(List<Long> userIds, Long dayId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsAttendanceEmployeeDetailDO::getUserId, userIds);
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getDayId, dayId);

        return hrmsAttendanceEmployeeDetailDao.list(queryWrapper);
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByIdList(List<Long> employeeDetailIdList) {
        if (CollectionUtils.isEmpty(employeeDetailIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendanceEmployeeDetailDao.listByIds(employeeDetailIdList);
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendanceEmployeeDetailDao.selectByFormIdList(formIdList);
    }

    @Override
    public List<HrmsAttendanceEmployeeDetailDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdLongList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsAttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CollUtil.isNotEmpty(userIdList), HrmsAttendanceEmployeeDetailDO::getUserId, userIdList);
        queryWrapper.in(CollUtil.isNotEmpty(dayIdLongList),HrmsAttendanceEmployeeDetailDO::getDayId, dayIdLongList);

        return hrmsAttendanceEmployeeDetailDao.list(queryWrapper);
    }

    /**
     * 考勤生成落库快照
     *
     * @param targetEmployeeDetailSnapshot     正常考勤快照
     * @param targetAbnormalAttendanceSnapshot 异常考勤快照
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void attendanceGenerateSnapshotSaveOrUpdate(List<HrmsAttendanceEmployeeDetailSnapshotDO> targetEmployeeDetailSnapshot,
                                                       List<HrmsEmployeeAbnormalAttendanceSnapshotDO> targetAbnormalAttendanceSnapshot) {
        if (CollUtil.isNotEmpty(targetEmployeeDetailSnapshot)) {
            hrmsAttendanceEmployeeDetailSnapshotDao.saveBatch(targetEmployeeDetailSnapshot);
        }
        if (CollUtil.isNotEmpty(targetAbnormalAttendanceSnapshot)) {
            hrmsEmployeeAbnormalAttendanceSnapshotDao.saveBatch(targetAbnormalAttendanceSnapshot);
        }
    }


    private List<AttendanceImportDTO> convertFromImportMap(List<Map<String, Object>> importList, String type) {
        List<AttendanceImportDTO> attendanceImportDTOList = new ArrayList<>();
        importList.forEach(item -> {
            //获取本次导入模板的语言类型
            if (item.get(USER_CODE_EN) != null) {
                languageType = "EN";
            }
            //获取用户编码
            Object userCode = item.get(USER_CODE_EN) != null ? String.valueOf(item.get(USER_CODE_EN)) : String.valueOf(item.get(USER_CODE_CN));
            //获取用户姓名
            Object userName = item.get(USER_NAME_EN) != null ? String.valueOf(item.get(USER_NAME_EN)) : String.valueOf(item.get(USER_NAME_CN));
            //获取考勤时间
            Object attendanceDate = item.get(ATTENDANCE_TIME_CN) != null ? String.valueOf(item.get(ATTENDANCE_TIME_CN)) : String.valueOf(item.get(ATTENDANCE_TIME_EN));
            //获取具体的一天的考勤小时数
            Object attendanceHours = item.get(ATTENDANCE_HOURS_CN) != null ? String.valueOf(item.get(ATTENDANCE_HOURS_CN)) : String.valueOf(item.get(ATTENDANCE_HOURS_EN));

            AttendanceImportDTO attendanceImportDTO = new AttendanceImportDTO();
            attendanceImportDTO.setUserCode(String.valueOf(userCode).equals("null") ? null : String.valueOf(userCode));
            attendanceImportDTO.setUserName(String.valueOf(userName).equals("null") ? null : String.valueOf(userName));
            attendanceImportDTO.setAttendanceDate(String.valueOf(attendanceDate).equals("null") ? null : String.valueOf(attendanceDate));
            attendanceImportDTO.setAttendanceHours(String.valueOf(attendanceHours).equals("null") ? null : String.valueOf(attendanceHours));
            attendanceImportDTO.setType(type);
            attendanceImportDTOList.add(attendanceImportDTO);
        });
        return attendanceImportDTOList;
    }


    private List<Map<String, Object>> convertToFailMap(List<AttendanceImportErrorDTO> failList) {
        if (CollectionUtils.isEmpty(failList)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> failMap = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(languageType, "EN")) {
            for (AttendanceImportErrorDTO errorDTO : failList) {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put(USER_CODE_EN, errorDTO.getUserCode());
                map.put(USER_NAME_EN, errorDTO.getUserName());
                map.put(ATTENDANCE_TIME_EN, errorDTO.getAttendanceDate());
                map.put(ATTENDANCE_HOURS_EN, errorDTO.getAttendanceHours());
                map.put("errorMessage", errorDTO.getErrorMessage());
                failMap.add(map);
            }
            return failMap;
        }
        for (AttendanceImportErrorDTO errorDTO : failList) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(USER_CODE_CN, errorDTO.getUserCode());
            map.put(USER_NAME_CN, errorDTO.getUserName());
            map.put(ATTENDANCE_TIME_CN, errorDTO.getAttendanceDate());
            map.put(ATTENDANCE_HOURS_CN, errorDTO.getAttendanceHours());
            map.put("errorMessage", errorDTO.getErrorMessage());
            failMap.add(map);
        }
        return failMap;

    }


    /**
     * 判断一个字符串是不是纯浮点数据类型
     */
    private boolean judgeDouble(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile(doubleMatch);
        return pattern.matcher(str).matches();
    }

    /**
     * 通过hrmsProperties来进行是否允许导入多天数据的判断,默认为false，不支持
     * 简单数据格式判断
     * 不允许导入重复员工数据
     */
    private List<AttendanceImportDTO> importDataValidate(List<AttendanceImportDTO> importList, List<AttendanceImportErrorDTO> failList, ImportTypeEnum importTypeEnum) {
        //初次校验，数据非空，格式校验
        List<AttendanceImportDTO> firstFilterList = new ArrayList<>();
        for (AttendanceImportDTO item : importList) {
            //校验编码是否为空，用户没有传userCode
            String userCode = item.getUserCode();
            if (StringUtils.isEmpty(userCode)) {
                convertToErrorDto(item, failList, HrmsErrorCodeEnums.IMPORT_USER_CODE_EMPTY_ERROR);
                continue;
            }
            //获取考勤时间
            String attendanceDate = item.getAttendanceDate();
            //日期输入的格式是否正确,这里有日期为空的判断，为空也属于格式不正确的错误
//            if (!PattenUtil.pattern(attendanceDate, DATE_FORMAT)) {
//                convertToErrorDto(item, failList, HrmsErrorCodeEnums.DATE_FORMAT_ERROR);
//                continue;
//            }
            //获取具体的一天的考勤小时数
            String attendanceHours = item.getAttendanceHours();
            //看具体的考勤数据格式是否正确，要为浮点数类型，这里也有为空的判断
            if (!judgeDouble(attendanceHours)) {
                convertToErrorDto(item, failList, HrmsErrorCodeEnums.ATTENDANCE_HOURS_FORMAT_ERROR);
                continue;
            }

            //看是考勤还是加班，这2中情况输入的小时数规范不一样
            BigDecimal importHours = new BigDecimal(attendanceHours);
            if (ImportTypeEnum.ATTENDANCE.getCode().equals(importTypeEnum.getCode())) {
                //考勤时间只能为4,8 不允许出现其他的时间
                if (importHours.compareTo(new BigDecimal("4")) != 0 && importHours.compareTo(new BigDecimal("8")) != 0) {
                    convertToErrorDto(item, failList, HrmsErrorCodeEnums.IMPORT_ATTENDANCE_HOURS_ONLY_BE_4HOURS_OR_8HOURS);
                    continue;
                }
            }
            if (ImportTypeEnum.OVERTIME.getCode().equals(importTypeEnum.getCode())) {
                //加班时间不允许小于等于0，也不允许大于24小时
                if (importHours.compareTo(new BigDecimal(0)) < 1 || importHours.compareTo(new BigDecimal(24)) > 0) {
                    convertToErrorDto(item, failList, HrmsErrorCodeEnums.IMPORT_OVERTIME_HOURS_NOT_BE_LESS_THAN_OR_EQUAL_TO_0HOURS_OR_MORE_THAN_24HOURS);
                    continue;
                }
            }

            firstFilterList.add(item);
        }

        //不管是否允许导入多天数据还是只能导入一天数据，一个用户在指定的一天，只有一条数据
        List<AttendanceImportDTO> secondFilterList = new ArrayList<>();
        checkSingleUser(firstFilterList, secondFilterList, failList);

        return secondFilterList;
    }


    /**
     * 不管是否允许导入多天数据，一个用户在指定的一天，只有一条数据
     */
    private void checkSingleUser(List<AttendanceImportDTO> firstFilterList, List<AttendanceImportDTO> secondFilterList, List<AttendanceImportErrorDTO> failList) {
        //key为userCode+attendanceDate 由这2个拼接才是唯一
        Map<String, List<AttendanceImportDTO>> singleGroup = firstFilterList.stream().collect(Collectors.groupingBy(o -> o.getUserCode() + o.getAttendanceDate()));
        for (AttendanceImportDTO item : firstFilterList) {
            List<AttendanceImportDTO> importUserList = singleGroup.get(item.getUserCode() + item.getAttendanceDate());
            if (importUserList.size() > 1) {
                convertToErrorDto(item, failList, HrmsErrorCodeEnums.IMPORT_RECORD_ONLY_ONE_PER_DAY);
                continue;
            }
            secondFilterList.add(item);
        }
    }


    /**
     * 校验数据准确性并赋值
     *
     * @param item        每个item都是一个对象，map有多个key，对应表格中输入的一个用户的考勤信息
     * @param validateDTO
     * @param failList
     * @return
     */
    private boolean doValidate(AttendanceImportDTO item, Map<String, HrmsUserInfoDO> userCodeMaps, ValidateDTO validateDTO, List<AttendanceImportErrorDTO> failList, ImportTypeEnum importTypeEnum, Map<String, List<HrmsAttendanceEmployeeDetailDO>> doMaps) {
        //获取用户编码
        String userCode = item.getUserCode();

        HrmsUserInfoDO userInfoDO = userCodeMaps.get(userCode);
        if (userInfoDO == null) {
            convertToErrorDto(item, failList, HrmsErrorCodeEnums.IMPORT_WORK_NO_NOT_EXISTS);
            return Boolean.FALSE;
        }

        validateDTO.setHrmsUserInfoDO(userInfoDO);

        //获取考勤时间/加班时间()
        //日期输入的格式是否正确
        Date attendanceDateFormat = null;
        try {
            attendanceDateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(item.getAttendanceDate());
        } catch (Exception e) {
            convertToErrorDto(item, failList, HrmsErrorCodeEnums.DATE_FORMAT_ERROR);
            return false;
        }
        //DateTime parse = DateUtil.parse(attendanceDate, DEFAULT_DATE_FORMAT_STR);
        Long dayId = Long.valueOf(DateUtil.format(attendanceDateFormat, DatePattern.PURE_DATE_PATTERN));
        Long userId = userInfoDO.getId();
        String doMapKey = String.valueOf(new StringBuilder().append(userId).append("$").append(dayId));

        //根据配置来，只允许导入当日和昨日的数据
        if (!hrmsProperties.getAttendance().isImportMultitude()) {
            //获取昨天的时间
            Date yesterday = DateUtil.offsetDay(new Date(), -1);
            if (HrmsDateUtil.getDateDiff(attendanceDateFormat, new Date()) > 1 || HrmsDateUtil.getDateDiff(yesterday, attendanceDateFormat) > 1) {
                //不是当天或者昨天的数据，不允许导入，提示错误
                convertToErrorDto(item, failList, HrmsErrorCodeEnums.IMPORT_TODAY_OR_YESTERDAY_INFO);
                return Boolean.FALSE;
            }
        }


        //校验该天出勤或缺勤
        if (doMaps.containsKey(doMapKey)) {
            //表示该员工改天有过记录，可能是出勤，也可能是加班
            List<HrmsAttendanceEmployeeDetailDO> recordList = doMaps.get(doMapKey);

            //获取历史考勤的考勤时间，加班时间，请假时间
            UserWorkHoursDTO userWorkHoursDTO = getRecordHours(recordList);
            BigDecimal recordAttendanceHours = userWorkHoursDTO.getRecordAttendanceHours();
            BigDecimal recordOvertimeHours = userWorkHoursDTO.getRecordOvertimeHours();
            BigDecimal recordLeaveHours = userWorkHoursDTO.getRecordLeaveHours();

            //出勤时间+请假时间不能大于8小时
            //出勤时间+加班时间+请假时间不能大于24小时
            //有出勤记录，直接覆盖
            if (ImportTypeEnum.ATTENDANCE.getCode().equals(importTypeEnum.getCode())) {
                //出勤时间+请假时间不能大于8小时
                if (recordLeaveHours.add(recordAttendanceHours).compareTo(MAX_ATTENDANCE_HOURS) > 0) {
                    convertToErrorDto(item, failList, HrmsErrorCodeEnums.ATTENDANCE_HOURS_ADD_LEAVE_HOURS_NOT_MORE_THAN_8_HOURS);
                    return Boolean.FALSE;
                }
                if (recordLeaveHours.add(recordAttendanceHours).add(new BigDecimal(item.getAttendanceHours())).compareTo(MAX_DAY_HOURS) > 0) {
                    //历史考勤时间+历史加班时间+历史请假时间+本次修改请假时间>24  报错
                    convertToErrorDto(item, failList, HrmsErrorCodeEnums.ATTENDANCE_HOURS_ADD_LEAVE_HOURS_ADD_OVERTIME_NOT_MORE_THAN_24_HOURS);
                    return Boolean.FALSE;
                }
            }
            //有加班记录，不允许再次导入
            if (ImportTypeEnum.OVERTIME.getCode().equals(importTypeEnum.getCode())) {
                if (recordLeaveHours.add(new BigDecimal(item.getAttendanceHours())).compareTo(MAX_ATTENDANCE_HOURS) > 0) {
                    convertToErrorDto(item, failList, HrmsErrorCodeEnums.ATTENDANCE_HOURS_ADD_LEAVE_HOURS_NOT_MORE_THAN_8_HOURS);
                    return Boolean.FALSE;
                }
                if (recordLeaveHours.add(recordOvertimeHours).add(new BigDecimal(item.getAttendanceHours())).compareTo(MAX_DAY_HOURS) > 0) {
                    //历史考勤时间+历史加班时间+历史请假时间+本次修改请假时间>24  报错
                    convertToErrorDto(item, failList, HrmsErrorCodeEnums.ATTENDANCE_HOURS_ADD_LEAVE_HOURS_ADD_OVERTIME_NOT_MORE_THAN_24_HOURS);
                    return Boolean.FALSE;
                }
            }
        }
        //在出勤表没有找到记录
        //设置年份
        if (validateDTO.getYear() == null) {
            validateDTO.setYear(DateUtil.year(attendanceDateFormat));
        }
        return Boolean.TRUE;
    }


    /**
     * 获取该用户该天的历史考勤的考勤时间，加班时间，请假时间
     */
    private UserWorkHoursDTO getRecordHours(List<HrmsAttendanceEmployeeDetailDO> recordList) {
        UserWorkHoursDTO userWorkHoursDTO = new UserWorkHoursDTO();
        //获取该用户该天的历史考勤的考勤时间，加班时间，请假时间
        List<BigDecimal> recordAttendanceHoursList = recordList.stream().filter(record -> record.getAttendanceHours() != null && record.getAttendanceHours().compareTo(BigDecimal.ZERO) > 0).map(record -> record.getAttendanceHours()).collect(Collectors.toList());
        for (BigDecimal bigDecimal : recordAttendanceHoursList) {
            userWorkHoursDTO.setRecordAttendanceHours(userWorkHoursDTO.getRecordAttendanceHours().add(bigDecimal));
        }

        List<BigDecimal> recordOvertimeHoursList = recordList.stream().filter(record -> record.getOvertimeHours() != null && record.getOvertimeHours().compareTo(BigDecimal.ZERO) > 0).map(record -> record.getOvertimeHours()).collect(Collectors.toList());
        for (BigDecimal bigDecimal : recordOvertimeHoursList) {
            userWorkHoursDTO.setRecordOvertimeHours(userWorkHoursDTO.getRecordOvertimeHours().add(bigDecimal));
        }

        List<BigDecimal> recordLeaveHoursList = recordList.stream().filter(record -> record.getLeaveHours() != null && record.getLeaveHours().compareTo(BigDecimal.ZERO) > 0).map(record -> record.getLeaveHours()).collect(Collectors.toList());
        for (BigDecimal bigDecimal : recordLeaveHoursList) {
            userWorkHoursDTO.setRecordLeaveHours(userWorkHoursDTO.getRecordLeaveHours().add(bigDecimal));
        }
        return userWorkHoursDTO;
    }

    private boolean isRight(ImportTypeEnum importTypeEnum, Object value) {
        return ImportTypeEnum.ATTENDANCE.getCode().equals(importTypeEnum.getCode()) && (value == null || (!PattenUtil.pattern(String.valueOf(value), PATTERN_STR) && !"A".equals(String.valueOf(value))));
    }

//    /**
//     * 行壮列 加班落库类封装
//     *
//     * @param doMaps
//     * @param dayIdMaps
//     * @param saveList
//     * @param item
//     * @param userInfoDO
//     * @param updateList
//     */
//    private void importOvertime(Map<String, List<HrmsAttendanceEmployeeDetailDO>> doMaps, Map<Long, HrmsAttendanceConfigDetailDO> dayIdMaps, List<HrmsAttendanceEmployeeDetailDO> saveList, AttendanceImportDTO item, HrmsUserInfoDO userInfoDO, List<HrmsAttendanceEmployeeDetailDO> updateList) {
//        //获取考勤时间
//        String attendanceDate = item.getAttendanceDate();
//        //获取具体的一天的加班小时数
//        String attendanceHours = item.getAttendanceHours();
//        Date attendanceDateFormat = null;
//        try {
//            attendanceDateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(item.getAttendanceDate());
//        } catch (Exception e) {
//            return;
//        }
//        Long dayId = Long.valueOf(DateUtil.format(attendanceDateFormat, DatePattern.PURE_DATE_PATTERN));
//        Long userId = userInfoDO.getId();
//        String doMapKey = String.valueOf(new StringBuilder().append(userId).append("$").append(dayId));
//        //这种情况只能发生在先导入了考勤记录，在导入加班记录，如果已有加班记录，是无法导入的
//        if (doMaps.containsKey(doMapKey)) {
//            //该员工已经有考勤记录，对该记录做修改
//            List<HrmsAttendanceEmployeeDetailDO> recordList = doMaps.get(doMapKey).stream().filter(record -> !StringUtils.equalsIgnoreCase(record.getAttendanceType(), AttendanceDayTypeEnum.LEAVE.name())).collect(Collectors.toList());
//            HrmsAttendanceEmployeeDetailDO target = recordList.get(0);
//
//            HrmsAttendanceEmployeeDetailDO model = BeanUtil.copyProperties(target, HrmsAttendanceEmployeeDetailDO.class);
//            model.setIsAttendance(BusinessConstant.Y);
//            model.setOvertimeHours(new BigDecimal(attendanceHours));
//            model.setDeptId(userInfoDO.getDeptId());
//            model.setPostId(userInfoDO.getPostId());
//            HrmsAttendanceConfigDetailDO configDetailDO = dayIdMaps.get(dayId);
//            model.setCompanyId(RequestInfoHolder.getCompanyId());
//            BaseDOUtil.fillDOUpdate(model);
//            model.setAttendanceType(configDetailDO == null ? NEED_PRESENT : configDetailDO.getDayType());
//            updateList.add(model);
//            return;
//        }
//        HrmsAttendanceEmployeeDetailDO target = getHrmsAttendanceEmployeeDetailDO(dayIdMaps, new BigDecimal(attendanceHours), attendanceDateFormat, dayId, userId, Boolean.FALSE, userInfoDO);
//        saveList.add(target);
//    }

//    /**
//     * 封装动态数据，行转列，生成动态长度的落库列表 考勤落库类封装
//     * 该方法针对的是考勤，不考虑加班
//     *
//     * @param doMaps
//     * @param dayIdMaps
//     * @param saveList
//     * @param item
//     * @param userInfoDO
//     */
//    private void importAttendance(Map<String, List<HrmsAttendanceEmployeeDetailDO>> doMaps, Map<Long, HrmsAttendanceConfigDetailDO> dayIdMaps, List<HrmsAttendanceEmployeeDetailDO> saveList, AttendanceImportDTO item, HrmsUserInfoDO userInfoDO, List<HrmsAttendanceEmployeeDetailDO> updateList) {
//        //获取考勤时间
//        String attendanceDate = item.getAttendanceDate();
//        //获取具体的一天的考勤小时数
//        String attendanceHours = item.getAttendanceHours();
//
//        Date attendanceDateFormat = null;
//        try {
//            attendanceDateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US).parse(item.getAttendanceDate());
//        } catch (Exception e) {
//            return;
//        }
//        Long dayId = Long.valueOf(DateUtil.format(attendanceDateFormat, DatePattern.PURE_DATE_PATTERN));
//        Long userId = userInfoDO.getId();
//        String doMapKey = String.valueOf(new StringBuilder().append(userId).append("$").append(dayId));
//        //该员工已经有考勤记录，则做更改
//        //这种情况只能发生在先导入了加班记录，在导入考勤记录，如果已有考勤记录，是无法导入的
//        if (doMaps.containsKey(doMapKey)) {
//            //该员工已经有考勤记录，对该记录做修改
//            //过滤掉请假的记录
//            List<HrmsAttendanceEmployeeDetailDO> recordList = doMaps.get(doMapKey).stream().filter(record -> !StringUtils.equalsIgnoreCase(record.getAttendanceType(), AttendanceDayTypeEnum.LEAVE.name())).collect(Collectors.toList());
//            HrmsAttendanceEmployeeDetailDO target = recordList.get(0);
//            HrmsAttendanceEmployeeDetailDO model = BeanUtil.copyProperties(target, HrmsAttendanceEmployeeDetailDO.class);
//            //这里必须设置为Y，因为可能存在出勤/加班时间都为0的记录
//            model.setIsAttendance(BusinessConstant.Y);
//            model.setAttendanceHours(new BigDecimal(attendanceHours));
//            model.setDeptId(userInfoDO.getDeptId());
//            model.setPostId(userInfoDO.getPostId());
//            HrmsAttendanceConfigDetailDO configDetailDO = dayIdMaps.get(dayId);
//            model.setCompanyId(RequestInfoHolder.getCompanyId());
//            BaseDOUtil.fillDOUpdate(model);
//            model.setAttendanceType(configDetailDO == null ? NEED_PRESENT : configDetailDO.getDayType());
//            updateList.add(model);
//            return;
//        }
//        HrmsAttendanceEmployeeDetailDO target = getHrmsAttendanceEmployeeDetailDO(dayIdMaps, new BigDecimal(attendanceHours), attendanceDateFormat, dayId, userId, Boolean.FALSE, userInfoDO);
//        saveList.add(target);
//    }

//    /**
//     * 用户考勤落库类封装
//     *
//     * @param dayIdMaps
//     * @param value
//     * @param attendanceDate
//     * @param dayId
//     * @param userId
//     * @return
//     */
//    private HrmsAttendanceEmployeeDetailDO getHrmsAttendanceEmployeeDetailDO(Map<Long, HrmsAttendanceConfigDetailDO> dayIdMaps, BigDecimal hours, Date attendanceDate, Long dayId, Long userId, boolean isOvertime, HrmsUserInfoDO userInfoDO) {
//        //年
//        int year = DateUtil.year(attendanceDate);
//        //月
//        int month = DateUtil.month(attendanceDate) + 1;
//        //日
//        int day = DateUtil.dayOfMonth(attendanceDate);
//        //获取本日是不是节假日或休息日
//        HrmsAttendanceConfigDetailDO configDetailDO = dayIdMaps.get(dayId);
//
//        //落库类封装
//        HrmsAttendanceEmployeeDetailDO target = new HrmsAttendanceEmployeeDetailDO();
//        target.setId(iHrmsIdWorker.nextId());
//        target.setUserId(userId);
//        target.setYear(Long.valueOf(year));
//        target.setDay(day);
//        target.setMonth(Long.valueOf(month));
//        target.setDayId(dayId);
//        target.setDate(attendanceDate);
//        //判断出勤类型 PRESENT WEEKEND HOLIDAY
//        target.setAttendanceType(configDetailDO == null ? NEED_PRESENT : configDetailDO.getDayType());
//        target.setIsAttendance(BusinessConstant.Y);
//        target.setAttendanceHours(hours);
//        target.setOvertimeHours(BigDecimal.ZERO);
//        if (isOvertime) {
//            target.setOvertimeHours(hours);
//            target.setAttendanceHours(BigDecimal.ZERO);
//        }
//        target.setLeaveHours(BigDecimal.ZERO);
//        //派件数为0
//        target.setDeliveredCount(0);
//        target.setCompanyId(RequestInfoHolder.getCompanyId());
//        BaseDOUtil.fillDOInsert(target);
//        target.setDeptId(userInfoDO.getDeptId());
//        target.setPostId(userInfoDO.getPostId());
//        return target;
//    }

    /**
     * 获取map key
     *
     * @return
     */
    private Function<HrmsAttendanceEmployeeDetailDO, String> keyGet() {
        return bean -> {
            Function<HrmsAttendanceEmployeeDetailDO, Long> userIdGetter = HrmsAttendanceEmployeeDetailDO::getUserId;
            Function<HrmsAttendanceEmployeeDetailDO, Long> dayIdGetter = HrmsAttendanceEmployeeDetailDO::getDayId;
            Long userId = userIdGetter.apply(bean);
            Long dayId = dayIdGetter.apply(bean);
            return String.valueOf(new StringBuilder().append(userId).append("$").append(dayId));
        };

    }

    /**
     * 问题编码封装
     *
     * @param failList
     * @param entry
     * @param codeEnums
     */
    private void errorCodePut(List<Map<String, Object>> failList, Map<String, Object> entry, HrmsErrorCodeEnums codeEnums) {
        entry.put("errorMessage", I18nUtils.getMessage(codeEnums.getDesc()));
        entry.put("success", false);
        failList.add(entry);
    }

    private void convertToErrorDto(AttendanceImportDTO importDto, List<AttendanceImportErrorDTO> errorDtoList, HrmsErrorCodeEnums codeEnums) {
        AttendanceImportErrorDTO errorDTO = new AttendanceImportErrorDTO();
        errorDTO.setUserCode(importDto.getUserCode());
        errorDTO.setUserName(importDto.getUserName());
        errorDTO.setAttendanceDate(importDto.getAttendanceDate());
        errorDTO.setAttendanceHours(importDto.getAttendanceHours());
        errorDTO.setErrorMessage(I18nUtils.getMessage(codeEnums.getDesc()));
        errorDtoList.add(errorDTO);
    }

}
