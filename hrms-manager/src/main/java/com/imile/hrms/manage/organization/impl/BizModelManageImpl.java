package com.imile.hrms.manage.organization.impl;

import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.Interface.ObjectValueProcessorInterface;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.DataMappingHolder;
import com.imile.hrms.common.entity.SimpleObjectInfo;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.dao.organization.dao.HrmsBizModelDao;
import com.imile.hrms.dao.organization.model.HrmsBizModelDO;
import com.imile.hrms.manage.organization.BizModelManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Slf4j
@Service("bizModelManageImpl")
public class BizModelManageImpl implements BizModelManage, ObjectValueProcessorInterface<Long> {

    @Autowired
    private HrmsBizModelDao hrmsBizModelDao;

    @Override
    public HrmsBizModelDO getBizModelById(Long id) {
        HrmsBizModelDO bizModel = hrmsBizModelDao.getById(id);
        if (Objects.isNull(bizModel) || IsDeleteEnum.YES.getCode().equals(bizModel.getIsDelete())) {
            log.error("业务节点不存在或已删除,id:{}", id);
            throw BusinessLogicException.getException(HrmsErrorCodeEnums.BIZ_MODEL_NOT_EXITS);
        }
        return bizModel;
    }

    @Override
    public HrmsBizModelDO getBizModelNullableById(Long id) {
        if (Objects.isNull(id) || BusinessConstant.DEFAULT_ID.equals(id)) {
            return null;
        }
        HrmsBizModelDO bizModel = hrmsBizModelDao.getById(id);
        if (Objects.isNull(bizModel) || IsDeleteEnum.YES.getCode().equals(bizModel.getIsDelete())) {
            log.info("业务节点不存在或已删除,id:{}", id);
            return null;
        }
        return bizModel;
    }

    @Override
    public Map<String, List<HrmsBizModelDO>> getBizModelName2DataListMap(List<String> bizModelNameList) {
        if (CollectionUtils.isEmpty(bizModelNameList)) {
            return Collections.emptyMap();
        }
        List<HrmsBizModelDO> bizModelList = hrmsBizModelDao.selectByBizModelNameList(bizModelNameList);
        if (CollectionUtils.isEmpty(bizModelList)) {
            return Collections.emptyMap();
        }
        // 兼容一个名称对应多条数据的情况 bizModelName -> List<HrmsBizModelDO>
        List<DataMappingHolder<HrmsBizModelDO>> holderList = Lists.newArrayList();
        bizModelList.forEach(item -> {
            holderList.add(DataMappingHolder.of(item.getBizModelNameEn(), item));
            if (!item.getBizModelNameCn().equals(item.getBizModelNameEn())) {
                holderList.add(DataMappingHolder.of(item.getBizModelNameCn(), item));
            }
        });
        return holderList.stream()
                .collect(Collectors.groupingBy(DataMappingHolder::getMappingKey,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> list.stream()
                                        .map(DataMappingHolder::getMappingValue)
                                        .collect(Collectors.toList()))));
    }

    @Override
    public Map<Long, HrmsBizModelDO> getBizModelMap(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        List<HrmsBizModelDO> bizModelList = hrmsBizModelDao.listByIds(idList);
        return bizModelList.stream()
                .filter(s -> IsDeleteEnum.NO.getCode().equals(s.getIsDelete()))
                .collect(Collectors.toMap(HrmsBizModelDO::getId, Function.identity()));
    }

    @Override
    public List<HrmsBizModelDO> getBizModelList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        List<HrmsBizModelDO> bizModelList = hrmsBizModelDao.listByIds(idList);
        return bizModelList.stream()
                .filter(s -> IsDeleteEnum.NO.getCode().equals(s.getIsDelete()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SimpleObjectInfo<Long>> batchGetSimpleObjectInfosByIds(List<Long> bizIdList) {
        if (CollectionUtils.isEmpty(bizIdList)) {
            return Lists.newArrayList();
        }
        List<HrmsBizModelDO> bizModelList = hrmsBizModelDao.listByIds(bizIdList);
        if (CollectionUtils.isEmpty(bizModelList)) {
            return Lists.newArrayList();
        }
        return bizModelList.stream()
                .map(it -> new SimpleObjectInfo<>(it.getId(), it.getBizModelNameCn(), it.getBizModelNameEn()))
                .collect(Collectors.toList());
    }

    @Override
    public SimpleObjectInfo<Long> getSimpleObjectInfosById(Long bizId) {
        if (Objects.isNull(bizId)) {
            return null;
        }
        List<SimpleObjectInfo<Long>> simpleObjectInfoList = this.batchGetSimpleObjectInfosByIds(Lists.newArrayList(bizId));
        if (CollectionUtils.isEmpty(simpleObjectInfoList)) {
            return null;
        }
        return simpleObjectInfoList.get(0);
    }
}
