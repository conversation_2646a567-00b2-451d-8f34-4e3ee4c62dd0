package com.imile.hrms.manage.punch.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.attendance.AttendanceDayTypeEnum;
import com.imile.hrms.common.enums.punch.PunchTypeEnum;
import com.imile.hrms.common.util.BigDecimalUtil;
import com.imile.hrms.common.util.HrmsDateUtil;
import com.imile.hrms.dao.punch.dao.HrmsAttendanceEmployeePunchRecordDao;
import com.imile.hrms.dao.punch.dto.EffectiveAttendanceHelpDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchClassConfigDTO;
import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigDetailDTO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceEmployeePunchRecordDO;
import com.imile.hrms.manage.punch.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/18
 */
@Service
public class HrmsAttendancePunchMangeImpl implements HrmsAttendancePunchManage {


    @Autowired
    private HrmsAttendanceEmployeePunchRecordDao hrmsAttendanceEmployeePunchRecordDao;


//    @Override
//    public void supplement(Long userId, Date supplementDate, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO) {
//        //配置为空则不能
//        if (hrmsAttendancePunchConfigDetailDTO == null) {
//            return;
//        }
//        String punchConfigType = hrmsAttendancePunchConfigDetailDTO.getPunchConfigType();
//        AbstractAttendancePunchTypeStrategy punchTypeStrategy = AttendancePunchTypeFactory.getInstance(punchConfigType);
//        if (punchTypeStrategy == null) {
//            return;
//        }
//        EffectiveAttendanceHelpDTO attendanceHelpDTO = punchTypeStrategy.getDayId(hrmsAttendancePunchConfigDetailDTO, supplementDate, userId);
//
//        //判断本次补卡是上班时间还是下班时间
//        String punchType = getPunchType(supplementDate, attendanceHelpDTO, userId);
//        AbstractEmployeePunchRecord punchRecord = PunchFactory.getInstance(punchType);
//        if (punchRecord == null) {
//            return;
//        }
//        //补卡
//        punchRecord.supplement(attendanceHelpDTO, userId, hrmsAttendancePunchConfigDetailDTO, supplementDate);
//
//
//    }

//    /**
//     * 获取本次补卡类型为上班卡还是下班卡
//     *
//     * @param supplementDate
//     * @param attendanceHelpDTO
//     */
//    // todo 组织架构优化: 这里不用管
//    private String getPunchType(Date supplementDate, EffectiveAttendanceHelpDTO attendanceHelpDTO, Long userId) {
//        Long dayId = attendanceHelpDTO.getDayId();
//        //若有已经有下班卡记录，并且，本次打卡时间晚于最早的下班打卡时间，则一定是下班卡
//        HrmsAttendanceEmployeePunchRecordDO punchOutRecord = hrmsAttendanceEmployeePunchRecordDao.getRecord(userId, dayId, PunchTypeEnum.OUT_DUTY.getCode(), RequestInfoHolder.getCompanyId());
//        if (punchOutRecord != null && HrmsDateUtil.before(punchOutRecord.getUserPunchTime(), supplementDate)) {
//            return PunchTypeEnum.OUT_DUTY.name();
//        }
//        //若有已经有下班卡记录，并且，本次打卡时间早于最晚的上班打卡时间，则一定是上班卡
//        HrmsAttendanceEmployeePunchRecordDO punchInRecord = hrmsAttendanceEmployeePunchRecordDao.getRecord(userId, dayId, PunchTypeEnum.ON_DUTY.getCode(),RequestInfoHolder.getCompanyId());
//        if (punchInRecord != null && HrmsDateUtil.before(supplementDate, punchOutRecord.getUserPunchTime())) {
//            return PunchTypeEnum.ON_DUTY.name();
//        }
//
//        //获取上班卡
//        Date configPunchInTime = attendanceHelpDTO.getConfigPunchInTime();
//        //获取下班打卡时间
//        Date configPunchOutTime = attendanceHelpDTO.getConfigPunchOutTime();
//        BigDecimal baseValue = BigDecimalUtil.divide(HrmsDateUtil.diffHour(configPunchOutTime, configPunchInTime), new BigDecimal(2), 2);
//
//        //如果不
//        if (BigDecimalUtil.compareWithSource(baseValue, HrmsDateUtil.diffHour(supplementDate, configPunchInTime))) {
//            return PunchTypeEnum.ON_DUTY.name();
//        }
//
//        if (BigDecimalUtil.compareWithSource(baseValue, HrmsDateUtil.diffHour(supplementDate, configPunchOutTime))) {
//            return PunchTypeEnum.OUT_DUTY.name();
//        }
//        return null;
//    }


}
