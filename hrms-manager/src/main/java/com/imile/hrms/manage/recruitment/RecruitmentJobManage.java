package com.imile.hrms.manage.recruitment;

import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobDO;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountDO;
import com.imile.hrms.dao.recruitment.params.JobQuery;
import com.imile.hrms.dao.recruitment.params.JobStatusUpdateQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
public interface RecruitmentJobManage {


    /**
     * 储存数据库，储存草稿时，会先删除用户之前的草稿
     *
     * @param jobDO
     */
    void saveJob(Long jobId, HrmsRecruitmentJobDO jobDO, List<HrmsRecruitmentJobHeadcountDO> headcountDOList);

    /**
     * 删除已撤回单据
     */
    void removeBackJob(Long jobId);

    List<HrmsRecruitmentJobDO> selectList(JobQuery query);

    HrmsRecruitmentJobDO getById(Long id);

    Boolean updateStatusById(JobStatusUpdateQuery query);

    Boolean updateFeishuJobIdById(Long jobId, String feishuJobId);
    /**
     * 更新job和对应的Hc
     *
     * @param jobDO
     * @return
     */
    Boolean updateJobAndHc(HrmsRecruitmentJobDO jobDO, List<HrmsRecruitmentJobHeadcountDO> headcountDOList);

}
