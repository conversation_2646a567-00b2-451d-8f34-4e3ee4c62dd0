package com.imile.hrms.manage.salary;

import com.imile.hrms.dao.salary.model.HrmsSalaryFrameConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryFrameConfigListQuery;

import java.util.List;

/**
 * 薪资框架配置
 *
 * <AUTHOR>
 * @since 2024/5/13
 */
public interface HrmsSalaryFrameConfigManage {

    /**
     * 根据条件查询
     *
     * @param query
     * @return
     */
    List<HrmsSalaryFrameConfigDO> listByQuery(HrmsSalaryFrameConfigListQuery query);


    /**
     * saveOrUpdateBatch
     *
     * @param list
     */
    void saveOrUpdateBatch(List<HrmsSalaryFrameConfigDO> list);

}
