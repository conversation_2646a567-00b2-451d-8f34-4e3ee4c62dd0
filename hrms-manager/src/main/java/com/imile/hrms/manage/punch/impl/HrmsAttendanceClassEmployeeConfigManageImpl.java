package com.imile.hrms.manage.punch.impl;

import com.imile.hrms.dao.punch.dao.HrmsAttendanceClassEmployeeConfigDao;
import com.imile.hrms.dao.punch.dao.HrmsAttendanceClassRecordDao;
import com.imile.hrms.dao.punch.dao.HrmsAttendanceCycleShiftConfigDao;
import com.imile.hrms.dao.punch.model.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceClassRecordDO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceCycleShiftConfigDO;
import com.imile.hrms.manage.punch.HrmsAttendanceClassEmployeeConfigManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-12
 * @version: 1.0
 */
@Service
public class HrmsAttendanceClassEmployeeConfigManageImpl implements HrmsAttendanceClassEmployeeConfigManage {

    @Autowired
    private HrmsAttendanceClassEmployeeConfigDao hrmsAttendanceClassEmployeeConfigDao;
    @Autowired
    private HrmsAttendanceClassRecordDao hrmsAttendanceClassRecordDao;
    @Autowired
    private HrmsAttendanceCycleShiftConfigDao hrmsAttendanceCycleShiftConfigDao;

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendanceClassEmployeeConfigDao.selectBatchUserRecord(userIdList, dayIdList);
    }

    @Override
    public List<HrmsAttendanceClassEmployeeConfigDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return hrmsAttendanceClassEmployeeConfigDao.selectRecordByUserIdList(userIdList, startDayId, endDayId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchShiftSave(List<HrmsAttendanceClassEmployeeConfigDO> oldAttendanceClassEmployeeConfigDOS, List<HrmsAttendanceClassRecordDO> oldClassRecordDOList,
                               List<HrmsAttendanceClassEmployeeConfigDO> newAttendanceClassEmployeeConfigDOS, List<HrmsAttendanceClassRecordDO> newClassRecordList) {
        if (CollectionUtils.isNotEmpty(oldAttendanceClassEmployeeConfigDOS)) {
            hrmsAttendanceClassEmployeeConfigDao.updateBatchById(oldAttendanceClassEmployeeConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(oldClassRecordDOList)) {
            hrmsAttendanceClassRecordDao.updateBatchById(oldClassRecordDOList);
        }
        if (CollectionUtils.isNotEmpty(newAttendanceClassEmployeeConfigDOS)) {
            hrmsAttendanceClassEmployeeConfigDao.saveBatch(newAttendanceClassEmployeeConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(newClassRecordList)) {
            hrmsAttendanceClassRecordDao.saveBatch(newClassRecordList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cycleShiftSave(List<HrmsAttendanceClassEmployeeConfigDO> oldAttendanceClassEmployeeConfigDOS, List<HrmsAttendanceClassRecordDO> oldClassRecordDOList, List<HrmsAttendanceClassEmployeeConfigDO> newAttendanceClassEmployeeConfigDOS, List<HrmsAttendanceClassRecordDO> newClassRecordList, List<HrmsAttendanceCycleShiftConfigDO> updateCycleShiftConfigDOS, List<HrmsAttendanceCycleShiftConfigDO> addCycleShiftConfigDOS) {
        if (CollectionUtils.isNotEmpty(oldAttendanceClassEmployeeConfigDOS)) {
            hrmsAttendanceClassEmployeeConfigDao.updateBatchById(oldAttendanceClassEmployeeConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(oldClassRecordDOList)) {
            hrmsAttendanceClassRecordDao.updateBatchById(oldClassRecordDOList);
        }
        if (CollectionUtils.isNotEmpty(newAttendanceClassEmployeeConfigDOS)) {
            hrmsAttendanceClassEmployeeConfigDao.saveBatch(newAttendanceClassEmployeeConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(newClassRecordList)) {
            hrmsAttendanceClassRecordDao.saveBatch(newClassRecordList);
        }
        if (CollectionUtils.isNotEmpty(updateCycleShiftConfigDOS)) {
            hrmsAttendanceCycleShiftConfigDao.updateBatchById(updateCycleShiftConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(addCycleShiftConfigDOS)) {
            hrmsAttendanceCycleShiftConfigDao.saveBatch(addCycleShiftConfigDOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCycleShift(List<HrmsAttendanceCycleShiftConfigDO> updateCycleShiftConfigDOS, List<HrmsAttendanceClassEmployeeConfigDO> oldAttendanceClassEmployeeConfigDOS, List<HrmsAttendanceClassRecordDO> oldClassRecordDOList) {
        if (CollectionUtils.isNotEmpty(oldAttendanceClassEmployeeConfigDOS)) {
            hrmsAttendanceClassEmployeeConfigDao.updateBatchById(oldAttendanceClassEmployeeConfigDOS);
        }
        if (CollectionUtils.isNotEmpty(oldClassRecordDOList)) {
            hrmsAttendanceClassRecordDao.updateBatchById(oldClassRecordDOList);
        }
        if (CollectionUtils.isNotEmpty(updateCycleShiftConfigDOS)) {
            hrmsAttendanceCycleShiftConfigDao.updateBatchById(updateCycleShiftConfigDOS);
        }
    }

    @Override
    public void updateToOld(Long userId, Date date) {
        hrmsAttendanceClassEmployeeConfigDao.updateToOld(userId, date);
    }
}
