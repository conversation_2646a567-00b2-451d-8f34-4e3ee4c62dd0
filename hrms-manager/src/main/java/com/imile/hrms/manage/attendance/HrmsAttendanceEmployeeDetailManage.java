package com.imile.hrms.manage.attendance;

import com.imile.hrms.dao.attendance.bo.UserAttendanceBO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailSnapshotDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.hrms.dao.attendance.query.UserAttendanceQuery;
import com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;

import java.util.List;

/**
 * 员工考勤明细manage
 *
 * <AUTHOR>
 */
public interface HrmsAttendanceEmployeeDetailManage {

    /**
     * 每日考勤计算落库:未排班
     */
    void attendanceGenerateUpdate(List<HrmsAttendanceEmployeeDetailDO> addEmployeeDetailDOList, List<HrmsAttendanceEmployeeDetailDO> updateEmployeeDetailDOList, List<HrmsEmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList, List<HrmsEmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList, List<HrmsUserLeaveStageDetailDO> updateUserLeaveStageDetailDOList, List<HrmsUserLeaveRecordDO> addUserLeaveRecordDOList);

    /**
     * 查询用户指定时间段内的考勤记录
     */
    List<HrmsAttendanceEmployeeDetailDO> selectAttendanceByDayId(Long userId, Long startDayId, Long endDayId);

    /**
     * 查询用户指定时间段内的考勤记录
     */
    List<HrmsAttendanceEmployeeDetailDO> selectAttendanceByCycleDay(List<Long> userIdList, Long startDayId, Long endDayId);

    /**
     * 查询用户指定时间的考勤记录
     */
    List<HrmsAttendanceEmployeeDetailDO> selectAttendanceByDayIdList(Long userId, List<Long> dayIdList);


//    /**
//     * 导入
//     *
//     * @param importList
//     * @param type
//     * @return
//     */
//    List<Map<String, Object>> doImport(List<Map<String, Object>> importList, String type);


    //List<AttendanceDTO> list(AttendanceEmployeeQuery query);

    List<HrmsAttendanceEmployeeDetailDO> userAttendanceList(UserAttendanceQuery query);

    List<UserAttendanceBO> attendanceUserList(UserAttendanceQuery query);


    /**
     * 查询员工该年的所有出勤明细
     */
    List<HrmsAttendanceEmployeeDetailDO> selectByYear(Long userId, Long year);

    /**
     * 查询员工该月的所有出勤明细
     */
    List<HrmsAttendanceEmployeeDetailDO> selectByYearAndMonth(Long userId, Long year, Long month);

    /**
     * 查询员工某天的考勤记录
     */
    List<HrmsAttendanceEmployeeDetailDO> selectByDayId(Long userId, Long dayId);

    /**
     * 根据post_id查询所有考勤记录
     */
    List<HrmsAttendanceEmployeeDetailDO> selectByPostId(List<Long> postIdList);

    /**
     * 批量修改数据
     *
     * @param entityList
     * @return
     */
    boolean batchUpdateById(List<HrmsAttendanceEmployeeDetailDO> entityList);

    /**
     * 批量保存数据
     *
     * @param recordDOList
     * @return
     */
    boolean batchSave(List<HrmsAttendanceEmployeeDetailDO> recordDOList);

    /**
     * 根据user_id查询某天所有考勤记录
     */
    List<HrmsAttendanceEmployeeDetailDO> selectByUserId(List<Long> userIds, Long dayId);


    List<HrmsAttendanceEmployeeDetailDO> selectByIdList(List<Long> employeeDetailIdList);

    List<HrmsAttendanceEmployeeDetailDO> selectByFormIdList(List<Long> formIdList);

    List<HrmsAttendanceEmployeeDetailDO> selectByUserIdListAndDayIdList(List<Long> userIdList, List<Long> dayIdLongList);

    /**
     * 考勤生成落库快照
     *
     * @param targetEmployeeDetailSnapshot     正常考勤快照
     * @param targetAbnormalAttendanceSnapshot 异常考勤快照
     */
    void attendanceGenerateSnapshotSaveOrUpdate(List<HrmsAttendanceEmployeeDetailSnapshotDO> targetEmployeeDetailSnapshot, List<HrmsEmployeeAbnormalAttendanceSnapshotDO> targetAbnormalAttendanceSnapshot);
}
