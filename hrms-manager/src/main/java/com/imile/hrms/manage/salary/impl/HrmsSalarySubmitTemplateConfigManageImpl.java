package com.imile.hrms.manage.salary.impl;

import com.imile.hrms.dao.salary.dao.HrmsSalarySubmitTemplateConfigDao;
import com.imile.hrms.dao.salary.dao.HrmsSalarySubmitTemplateItemConfigDao;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateConfigDO;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateItemConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateConfigPageQuery;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateConfigQuery;
import com.imile.hrms.manage.salary.HrmsSalarySubmitTemplateConfigManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 薪资数据提报设置
 *
 * <AUTHOR>
 * @since 2024/3/4
 */
@Service

public class HrmsSalarySubmitTemplateConfigManageImpl implements HrmsSalarySubmitTemplateConfigManage {

    @Autowired
    private HrmsSalarySubmitTemplateConfigDao hrmsSalarySubmitTemplateConfigDao;

    @Autowired
    private HrmsSalarySubmitTemplateItemConfigDao hrmsSalarySubmitTemplateItemConfigDao;

    @Override
    public List<HrmsSalarySubmitTemplateConfigDO> listByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return hrmsSalarySubmitTemplateConfigDao.listByIdList(idList);
    }

    @Override
    public HrmsSalarySubmitTemplateConfigDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return hrmsSalarySubmitTemplateConfigDao.getById(id);
    }

    @Override
    public List<HrmsSalarySubmitTemplateConfigDO> listByQuery(HrmsSalarySubmitTemplateConfigQuery query) {
        return hrmsSalarySubmitTemplateConfigDao.listByQuery(query);
    }

    @Override
    public List<HrmsSalarySubmitTemplateConfigDO> pageListByQuery(HrmsSalarySubmitTemplateConfigPageQuery query) {
        return hrmsSalarySubmitTemplateConfigDao.pageListByQuery(query);
    }

    @Override
    public String getMaxTemplateNo() {

        List<HrmsSalarySubmitTemplateConfigDO> isLatestTemplateList = hrmsSalarySubmitTemplateConfigDao.getIsLatestTemplateList();
        if (CollectionUtils.isEmpty(isLatestTemplateList)) {
            return "PBD000001";
        }

        Integer maxPbdNo = isLatestTemplateList.stream().map(item -> Integer.parseInt(item.getTemplateNo().replace("PBD", "")))
                .max(Comparator.comparing(Integer::longValue)).orElse(null);
        //根据maxPbdNo生成新的PBDNo，规则为PBD+x+maxPbdNo，x和maxPbdNo的位数相加为6
        if (maxPbdNo == null) {
            return "PBD000001";
        }
        String maxPbdNoStr = String.valueOf(maxPbdNo + 1);
        int length = maxPbdNoStr.length();
        int x = 6 - length;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < x; i++) {
            sb.append("0");
        }
        return "PBD" + sb + maxPbdNoStr;
    }

    @Override
    public void saveOrUpdate(List<HrmsSalarySubmitTemplateConfigDO> modelList, List<HrmsSalarySubmitTemplateItemConfigDO> itemConfigDOList) {
        if (CollectionUtils.isNotEmpty(modelList)) {
            hrmsSalarySubmitTemplateConfigDao.saveOrUpdateBatch(modelList);
        }
        if (CollectionUtils.isNotEmpty(itemConfigDOList)) {
            hrmsSalarySubmitTemplateItemConfigDao.saveOrUpdateBatch(itemConfigDOList);
        }
    }
}
