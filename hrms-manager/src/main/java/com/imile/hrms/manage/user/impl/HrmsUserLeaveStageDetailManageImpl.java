package com.imile.hrms.manage.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.enums.attendance.AttendanceInvalidEnum;
import com.imile.hrms.dao.user.dao.HrmsUserLeaveStageDetailDao;
import com.imile.hrms.dao.user.model.HrmsUserLeaveStageDetailDO;
import com.imile.hrms.dao.user.query.UserLeaveStageDetailQuery;
import com.imile.hrms.manage.user.HrmsUserLeaveStageDetailManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-13
 * @version: 1.0
 */
@Service
@Slf4j
public class HrmsUserLeaveStageDetailManageImpl implements HrmsUserLeaveStageDetailManage {

    @Autowired
    private HrmsUserLeaveStageDetailDao hrmsUserLeaveStageDetailDao;

    @Override
    public List<HrmsUserLeaveStageDetailDO> selectByLeaveId(List<Long> leaveIdList) {
        if (CollectionUtils.isEmpty(leaveIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserLeaveStageDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserLeaveStageDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsUserLeaveStageDetailDO::getIsInvalid, AttendanceInvalidEnum.NO.getCode());
        queryWrapper.in(HrmsUserLeaveStageDetailDO::getLeaveId, leaveIdList);
        return hrmsUserLeaveStageDetailDao.list(queryWrapper);
    }

    @Override
    public List<HrmsUserLeaveStageDetailDO> selectById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserLeaveStageDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserLeaveStageDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserLeaveStageDetailDO::getId, idList);
        return hrmsUserLeaveStageDetailDao.list(queryWrapper);
    }

    /**
     * 通过条件查询
     *
     * @param userLeaveStageDetailQuery 查询条件
     * @return 查询结果
     */
    @Override
    public List<HrmsUserLeaveStageDetailDO> selectByCondition(UserLeaveStageDetailQuery userLeaveStageDetailQuery) {
        if (ObjectUtil.isNull(userLeaveStageDetailQuery) || CollUtil.isEmpty(userLeaveStageDetailQuery.getLeaveIdList())) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserLeaveStageDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserLeaveStageDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CollUtil.isNotEmpty(userLeaveStageDetailQuery.getLeaveIdList()), HrmsUserLeaveStageDetailDO::getLeaveId, userLeaveStageDetailQuery.getLeaveIdList());
        queryWrapper.eq(ObjectUtil.isNotNull(userLeaveStageDetailQuery.getLeaveMark()), HrmsUserLeaveStageDetailDO::getLeaveMark, userLeaveStageDetailQuery.getLeaveMark());
        queryWrapper.eq(ObjectUtil.isNotNull(userLeaveStageDetailQuery.getIsInvalid()), HrmsUserLeaveStageDetailDO::getIsInvalid, userLeaveStageDetailQuery.getIsInvalid());
        return hrmsUserLeaveStageDetailDao.list(queryWrapper);
    }
}
