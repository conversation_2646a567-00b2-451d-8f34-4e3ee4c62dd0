package com.imile.hrms.manage.newAttendance.punchConfig.adapter.mapstruct;

import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchGpsConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchGpsConfigQuery;
import com.imile.hrms.dao.punch.model.HrmsAttendanceGpsConfigDO;
import com.imile.hrms.dao.punch.query.AttendanceGpsConfigQuery;
import com.imile.hrms.manage.newAttendance.common.BaseMapstruct;
import com.imile.hrms.manage.newAttendance.common.MapperConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/19
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface PunchGpsConfigMapstruct extends BaseMapstruct {

    PunchGpsConfigMapstruct INSTANCE = Mappers.getMapper(PunchGpsConfigMapstruct.class);

    default Long setModelId(PunchGpsConfigDO module) {
        return setModelId(module,
                PunchGpsConfigDO::getId,
                PunchGpsConfigDO::setId);
    }


    default Long setModelId(HrmsAttendanceGpsConfigDO module) {
        return setModelId(module,
                HrmsAttendanceGpsConfigDO::getId,
                HrmsAttendanceGpsConfigDO::setId);
    }

    @Mapping(target = "id", expression = "java(setModelId(punchGpsConfigDO))")
    HrmsAttendanceGpsConfigDO mapToOld(PunchGpsConfigDO punchGpsConfigDO);

    List<HrmsAttendanceGpsConfigDO> mapToOld(List<PunchGpsConfigDO> punchGpsConfigDOList);

    @Mapping(target = "id", expression = "java(setModelId(attendanceGpsConfigDO))")
    PunchGpsConfigDO mapToNew(HrmsAttendanceGpsConfigDO attendanceGpsConfigDO);

    List<PunchGpsConfigDO> mapToNew(List<HrmsAttendanceGpsConfigDO> attendanceGpsConfigDOList);

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum", ignore = true)
    PunchGpsConfigQuery toNewPunchGpsConfigQuery(AttendanceGpsConfigQuery attendanceGpsConfigQuery);

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum", ignore = true)
    AttendanceGpsConfigQuery toOldPunchGpsConfigQuery(PunchGpsConfigQuery punchGpsConfigQuery);

}
