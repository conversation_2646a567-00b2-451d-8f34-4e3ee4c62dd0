package com.imile.hrms.manage.user.impl;

import com.imile.hrms.common.entity.DataDifferHolder;
import com.imile.hrms.dao.user.dao.UserLanguageAbilityDao;
import com.imile.hrms.dao.user.model.UserLanguageAbilityDO;
import com.imile.hrms.manage.common.CommonManage;
import com.imile.hrms.manage.user.UserLanguageAbilityManage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/4/3
 */
@Service
public class UserLanguageAbilityManageImpl implements UserLanguageAbilityManage {

    @Autowired
    private UserLanguageAbilityDao userLanguageAbilityDao;

    @Autowired
    private CommonManage commonManage;

    @Override
    public void doSave(DataDifferHolder<UserLanguageAbilityDO> differHolder) {
        commonManage.differSave(differHolder, userLanguageAbilityDao);
    }
}
