package com.imile.hrms.manage.salary.impl;

import com.imile.hrms.dao.salary.dao.HrmsSalaryTaskConfigDao;
import com.imile.hrms.dao.salary.dto.HrmsSalaryTaskConfigPageDTO;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryTaskConfigMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryTaskConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryTaskConfigPageQuery;
import com.imile.hrms.dao.salary.query.HrmsSalaryTaskConfigQuery;
import com.imile.hrms.manage.salary.HrmsSalaryTaskConfigManage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/6 14:07
 * @version: 1.0
 */
@Service
public class HrmsSalaryTaskConfigManageImpl implements HrmsSalaryTaskConfigManage {

    @Autowired
    private HrmsSalaryTaskConfigDao hrmsSalaryTaskConfigDao;

    @Autowired
    private HrmsSalaryTaskConfigMapper hrmsSalaryTaskConfigMapper;

    @Override
    public List<HrmsSalaryTaskConfigDO> listByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryTaskConfigDao.listByIdList(idList);
    }

    @Override
    public List<HrmsSalaryTaskConfigDO> listByQuery(HrmsSalaryTaskConfigQuery query) {
        return hrmsSalaryTaskConfigDao.listByQuery(query);
    }

    @Override
    public List<HrmsSalaryTaskConfigDO> selectByCountryList(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return new ArrayList<>();
        }
        return hrmsSalaryTaskConfigDao.selectByCountryList(countryList);
    }

    @Override
    public List<HrmsSalaryTaskConfigDO> selectByAllCountryList() {
        return hrmsSalaryTaskConfigDao.selectByAllCountryList();
    }

    @Override
    public void saveOrUpdateBatch(List<HrmsSalaryTaskConfigDO> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        hrmsSalaryTaskConfigDao.saveOrUpdateBatch(modelList);
    }

    @Override
    public List<HrmsSalaryTaskConfigPageDTO> pageList(HrmsSalaryTaskConfigPageQuery query) {
        return hrmsSalaryTaskConfigMapper.pageList(query);
    }


}
