package com.imile.hrms.manage.config.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.dao.config.dao.HrmsCompanyConfigDao;
import com.imile.hrms.dao.config.dao.HrmsUserChangeEmailConfigDao;
import com.imile.hrms.dao.config.model.HrmsCompanyConfigDO;
import com.imile.hrms.dao.config.model.HrmsUserChangeEmailConfigDO;
import com.imile.hrms.manage.config.HrmsCompanySendEmailConfigManage;
import com.imile.hrms.manage.config.bo.HrmsCompanyEmailConfigBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公司人事变动邮件通知配置
 *
 * <AUTHOR>
 * @date 2022/09/05
 */
@Service
public class HrmsCompanySendEmailConfigManageImpl implements HrmsCompanySendEmailConfigManage {

    @Autowired
    private HrmsCompanyConfigDao hrmsCompanyConfigDao;
    @Autowired
    private HrmsUserChangeEmailConfigDao userChangeEmailConfigDao;

    @Override
    public boolean add(HrmsCompanyConfigDO companyConfigDO, List<HrmsUserChangeEmailConfigDO> emailConfigDOList) {
        hrmsCompanyConfigDao.save(companyConfigDO);
        userChangeEmailConfigDao.saveBatch(emailConfigDOList);
        return true;
    }

    @Override
    public boolean edit(HrmsCompanyConfigDO companyConfigDO, List<HrmsUserChangeEmailConfigDO> emailConfigDOList) {
        hrmsCompanyConfigDao.updateById(companyConfigDO);
        userChangeEmailConfigDao.updateBatchById(emailConfigDOList);
        return true;
    }

    @Override
    public HrmsCompanyEmailConfigBO getByCountry(String country) {
        if (StringUtils.isBlank(country)) {
            return null;
        }
        LambdaQueryWrapper<HrmsCompanyConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsCompanyConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsCompanyConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(HrmsCompanyConfigDO::getCountry, country);
        List<HrmsCompanyConfigDO> companyConfigDOList = hrmsCompanyConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(companyConfigDOList)) {
            return null;
        }
        HrmsCompanyConfigDO companyConfigDO = companyConfigDOList.get(0);
        HrmsCompanyEmailConfigBO emailBO = new HrmsCompanyEmailConfigBO();
        BeanUtils.copyProperties(companyConfigDO, emailBO);

        LambdaQueryWrapper<HrmsUserChangeEmailConfigDO> query = new LambdaQueryWrapper<>();
        query.eq(HrmsUserChangeEmailConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        query.eq(HrmsUserChangeEmailConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        query.eq(HrmsUserChangeEmailConfigDO::getCompanyConfigId, companyConfigDO.getId());
        List<HrmsUserChangeEmailConfigDO> list = userChangeEmailConfigDao.list(query);
        emailBO.setEmailConfigDOList(list);

        return emailBO;
    }
}
