package com.imile.hrms.manage.driver.attendance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.hrms.dao.driver.attendance.dao.HrmsDriverPunchRecordDao;
import com.imile.hrms.dao.driver.attendance.dao.HrmsDriverPunchRecordDetailDao;
import com.imile.hrms.dao.driver.attendance.model.HrmsDriverPunchRecordDO;
import com.imile.hrms.dao.driver.attendance.model.HrmsDriverPunchRecordDetailDO;
import com.imile.hrms.dao.driver.attendance.query.DriverPunchRecordDetailQuery;
import com.imile.hrms.manage.driver.attendance.HrmsDriverPunchRecordManage;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HrmsDriverPunchRecordManageImpl implements HrmsDriverPunchRecordManage {
    @Autowired
    private HrmsDriverPunchRecordDao hrmsDriverPunchRecordDao;

    @Autowired
    private HrmsDriverPunchRecordDetailDao hrmsDriverPunchRecordDetailDao;

    @Override
    public void saveOrUpdate(HrmsDriverPunchRecordDO driverPunchRecord) {
        if(ObjectUtil.isNotNull(driverPunchRecord)){
            hrmsDriverPunchRecordDao.save(driverPunchRecord);
        }
    }

    @Override
    public List<HrmsDriverPunchRecordDO> selectPunchRecordDetail(DriverPunchRecordDetailQuery driverPunchRecordDetailQuery) {
        return hrmsDriverPunchRecordDao.selectPunchRecordDetail(driverPunchRecordDetailQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePunchRecordAndDetail(HrmsDriverPunchRecordDO driverPunchRecord, HrmsDriverPunchRecordDetailDO driverPunchRecordDetail) {
        if(ObjectUtil.isNotNull(driverPunchRecord)){
            hrmsDriverPunchRecordDao.save(driverPunchRecord);
        }
        if(ObjectUtil.isNotNull(driverPunchRecordDetail)){
            hrmsDriverPunchRecordDetailDao.save(driverPunchRecordDetail);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePunchRecordAndDetail(List<HrmsDriverPunchRecordDO> driverPunchRecordList, List<HrmsDriverPunchRecordDetailDO> driverPunchRecordDetailList) {
        if(CollUtil.isNotEmpty(driverPunchRecordList)){
            hrmsDriverPunchRecordDao.updateBatchById(driverPunchRecordList);
        }
        if(CollUtil.isNotEmpty(driverPunchRecordDetailList)){
            hrmsDriverPunchRecordDetailDao.updateBatchById(driverPunchRecordDetailList);
        }
    }
}
