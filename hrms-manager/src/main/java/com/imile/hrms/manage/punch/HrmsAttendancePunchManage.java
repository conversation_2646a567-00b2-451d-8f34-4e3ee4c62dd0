package com.imile.hrms.manage.punch;

import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigDetailDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/18
 */
public interface HrmsAttendancePunchManage {

//    /**
//     * 补卡
//     * @param userId
//     * @param supplementDate
//     */
//    void supplement(Long userId, Date supplementDate, HrmsAttendancePunchConfigDetailDTO hrmsAttendancePunchConfigDetailDTO);
}
