package com.imile.hrms.manage.newAttendance.calendar.adapter.converter;

import com.imile.hrms.common.adapter.DataConverter;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigRangeDO;
import com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigRangeDO;
import com.imile.hrms.manage.newAttendance.calendar.adapter.mapstruct.CalendarConfigRangeMapstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/2/8 
 * @Description
 */
@Component("calendarConfigRangeConverter")
public class CalendarConfigRangeConverter implements DataConverter<CalendarConfigRangeDO, HrmsAttendanceConfigRangeDO> {


    @Override
    public HrmsAttendanceConfigRangeDO convertFromNew(CalendarConfigRangeDO newObj) {
        return CalendarConfigRangeMapstruct.INSTANCE.mapToOld(newObj);
    }

    @Override
    public CalendarConfigRangeDO convertFromOld(HrmsAttendanceConfigRangeDO oldObj) {
        return CalendarConfigRangeMapstruct.INSTANCE.mapToNew(oldObj);
    }

    @Override
    public Class<CalendarConfigRangeDO> getNewType() {
        return CalendarConfigRangeDO.class;
    }

    @Override
    public Class<HrmsAttendanceConfigRangeDO> getOldType() {
        return HrmsAttendanceConfigRangeDO.class;
    }
}
