package com.imile.hrms.common.enums.user;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
@Getter
public enum UserInfoFieldEnum {
    /**
     * 人员信息字段枚举
     */
    USER_NAME("userName", "姓名全称", Boolean.TRUE),
    USER_NAME_EN("userNameEn", "英文名", Boolean.TRUE),
    SEX("sex", "性别", Boolean.TRUE),
    PHONE("phone", "联系电话", Boolean.TRUE),
    EMAIL("email", "企业邮箱", Boolean.TRUE),
    EMPLOYEE_TYPE("employeeType", "用工类型", Boolean.TRUE),
    SETTLEMENT_CENTER_CODE("settlementCenterCode", "签约主体编码", Boolean.TRUE),
    VENDOR_CODE("vendorCode", "供应商编码", Boolean.TRUE),
    POST_ID("postId", "岗位ID", Boolean.TRUE),
    IS_DRIVER("isDriver", "是否为司机", Boolean.TRUE),
    IS_DTL("isDtl", "是否为DTL", Boolean.TRUE),
    DEPT_ID("deptId", "部门ID", Boolean.TRUE),
    OC_ID("ocId", "网点ID", Boolean.TRUE),
    OC_CODE("ocCode", "网点编码", Boolean.TRUE),
    ORIGIN_COUNTRY("originCountry", "所属国", Boolean.TRUE),
    LEADER_ID("leaderId", "汇报上级ID", Boolean.TRUE),
    LOCATION_COUNTRY("locationCountry", "常驻地国家", Boolean.TRUE),
    LOCATION_PROVINCE("locationProvince", "常驻地省份", Boolean.TRUE),
    LOCATION_CITY("locationCity", "常驻地城市", Boolean.TRUE),
    WORK_STATUS("workStatus", "工作状态", Boolean.TRUE),
    STATUS("status", "账号状态", Boolean.TRUE),
    COUNTRY_CODE("countryCode", "国籍", Boolean.TRUE),
    WORK_SENIORITY("workSeniority", "工龄（月）", Boolean.TRUE),
    ;

    private final String key;

    private final String desc;

    private final Boolean hasSyncLogic;

    UserInfoFieldEnum(String key, String desc, Boolean hasSyncLogic) {
        this.key = key;
        this.desc = desc;
        this.hasSyncLogic = hasSyncLogic;
    }

    public static boolean hasSyncLogic(String key) {
        return Arrays.stream(values())
                .filter(item -> item.getKey().equals(key))
                .findAny()
                .map(UserInfoFieldEnum::getHasSyncLogic)
                .orElse(false);
    }
}
