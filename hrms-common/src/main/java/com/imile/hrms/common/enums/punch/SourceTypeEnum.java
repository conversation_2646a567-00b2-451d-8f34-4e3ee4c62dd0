package com.imile.hrms.common.enums.punch;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/10
 */
public enum SourceTypeEnum {
    /**
     * 司机APP
     */
    DRIVER_APP,
    /**
     * 考勤机打卡（指纹/人脸打卡
     */
    ATM,
    /**
     * 用户打卡
     */
    USER,
    /**
     * 钉钉考勤机（考勤机蓝牙打卡
     */
    IBeaconDING_ATM,
    /**
     * 老板改签
     */
    BOSS,
    /**
     * 审批系统
     */
    APPROVE,
    /**
     * 考勤系统
     */
    SYSTEM,
    /**
     * 自动打卡
     */
    AUTO_CHECK,
    /**
     * 中控考勤机
     */
    ZKTECO,

    /**
     * WPM：仓内打卡来源
     */
    WPM,

    /**
     * 补卡申请
     */
    REISSUE_CARD;
}
