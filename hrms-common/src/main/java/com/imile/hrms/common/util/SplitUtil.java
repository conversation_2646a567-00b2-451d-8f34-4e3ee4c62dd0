package com.imile.hrms.common.util;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


public class SplitUtil {

    public static List<String> split(String str, String regex) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = str.split(regex);
            for (String s : split) {
                if (StringUtils.isNotBlank(s)) {
                    list.add(s);
                }
            }
        }
        return list;
    }


    public static List<Integer> split2Integer(String listStr, String regex) {
        if (StringUtils.isBlank(listStr)) {
            return Lists.newArrayList();
        }
        List<String> collect = Arrays.stream(listStr.split(regex))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        return collect.stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    public static List<Long> split2Long(String listStr, String regex) {
        if (StringUtils.isBlank(listStr)) {
            return Lists.newArrayList();
        }
        List<String> collect = Arrays.stream(listStr.split(regex))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        return collect.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    public static List<String> splitNew(String listStr, String regex) {
        if (StringUtils.isBlank(listStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(listStr.split(regex))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    public static Boolean containsIgnoreCase(String listStr, String regex, String containsKey) {
        List<String> list = splitNew(listStr, regex);
        return list.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, containsKey));
    }
}
