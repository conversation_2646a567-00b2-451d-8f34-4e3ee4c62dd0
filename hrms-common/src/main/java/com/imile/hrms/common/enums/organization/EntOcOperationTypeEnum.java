package com.imile.hrms.common.enums.organization;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/20
 */
public enum EntOcOperationTypeEnum {

    /**
     * 操作类型（1:新增网点 2:停用网点 3:启用网点 4:属性变更）
     */
    ADD(1, "新增", "Add"),

    /**
     * 停用网点
     */
    DISABLE(2, "停用", "Disable"),

    /**
     * 启用网点
     */
    ACTIVE(3, "启用", "Enable"),

    /**
     * 直营转加盟
     */
    DIRECT2JOIN(4, "直营转加盟", "Company Owned to CSP"),

    /**
     * 删除
     */
    DELETE(5, "删除", "Delete"),

    /**
     * 加盟转直营
     */
    JOIN2DIRECT(6, "加盟转直营", "CSP to Company Owned"),
    ;

    @Getter
    private final Integer type;

    @Getter
    private final String desc;

    @Getter
    private final String descEn;


    EntOcOperationTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static String takeDesc(Integer type) {
        if (type == null) {
            return "";
        }
        for (EntOcOperationTypeEnum e : EntOcOperationTypeEnum.values()) {
            if (e.type.equals(type)) {
                return RequestInfoHolder.isChinese() ? e.desc : e.descEn;
            }
        }
        return "";
    }
}
