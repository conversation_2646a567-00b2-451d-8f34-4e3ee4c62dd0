package com.imile.hrms.common.enums.recruitment;

import com.imile.common.exception.BusinessException;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
public enum JobStatusEnum {
    DRAFT(0, "草稿", "draft"),
    OFFICIAL(10, "正式", "Official"),
    ;

    public static HcStatusEnum convertJobToHcStatus(JobStatusEnum job) {
        if (job == null) {
            return null;
        }
        switch (job) {
            case DRAFT:
                return HcStatusEnum.DRAFT;
            case OFFICIAL:
                return HcStatusEnum.APPROVING;
            default:
                return null;
        }
    }
    private final int value;
    private final String description;

    private final String descriptionEn;

    JobStatusEnum(int value, String description, String descriptionEn) {
        this.value = value;
        this.description = description;
        this.descriptionEn = descriptionEn;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public String getDescriptionEn() {
        return descriptionEn;
    }
}
