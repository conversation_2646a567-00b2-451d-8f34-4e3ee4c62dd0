package com.imile.hrms.common.enums.organization;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@Getter
public enum DeptActiveTypeEnum {
    /**
     * ADD: 新增组织, ACTIVE: 启用组织, DISABLED: 停用组织
     */
    ADD("ADD", "新增", "Add"),
    ACTIVE("ACTIVE", "启用", "Enable"),
    DISABLED("DISABLED", "停用", "Disable"),
    ;

    /**
     * 0: 缺省值 1: 对应生效时间 2: 对应停用时间
     *
     * @return
     */
    public static TriggerTimeTypeEnum getTriggerTimeType(String code) {
        if (DeptActiveTypeEnum.ADD.code.equals(code) || DeptActiveTypeEnum.ACTIVE.code.equals(code)) {
            return TriggerTimeTypeEnum.ACTIVE;
        }
        if (DeptActiveTypeEnum.DISABLED.code.equals(code)) {
            return TriggerTimeTypeEnum.DISABLED;
        }
        return TriggerTimeTypeEnum.DEFAULT;
    }

    private final String code;

    private final String descCn;

    private final String descEn;

    DeptActiveTypeEnum(String code, String descCn, String descEn) {
        this.code = code;
        this.descCn = descCn;
        this.descEn = descEn;
    }

    public static String descOfCode(String code) {
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findAny()
                .map(s -> RequestInfoHolder.isChinese() ? s.descCn : s.descEn)
                .orElse("");
    }
}

