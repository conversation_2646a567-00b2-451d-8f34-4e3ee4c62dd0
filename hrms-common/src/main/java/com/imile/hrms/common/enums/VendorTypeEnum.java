package com.imile.hrms.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/6
 */
@Getter
public enum VendorTypeEnum {
    /**
     * 供应商类型
     */
    SERVICE("Service", "仓外派送供应商", "Out of Hub delivery"),
    HEADHUNTING("Headhunting", "招聘/猎头公司", "Headhunting"),
    INTERNATIONAL_LAND_TRANSPORT("InternationalLandTransport", "国际陆运", "International Land Transport"),
    TRANSIT("Transit", "干支线运输", "Line-haul Partner"),
    HUB("Hub", "网点加盟商", "Channel Service Partners(CSP)"),
    HUB_SERVICE("HubService", "仓内外包员工", "Warehouse Outsourcing Employee（Hub OS）"),
    OTHERS("Others", "其他", "Others"),
    ;

    private final String type;

    private final String descCn;

    private final String descEn;

    VendorTypeEnum(String type, String descCn, String descEn) {
        this.type = type;
        this.descCn = descCn;
        this.descEn = descEn;
    }
}
