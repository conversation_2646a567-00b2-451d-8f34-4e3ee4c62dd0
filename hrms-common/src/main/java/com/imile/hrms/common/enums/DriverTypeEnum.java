package com.imile.hrms.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum DriverTypeEnum {
    EMPLOYEE("Employee", "INTERNAL", "员工"),
    SUB_EMPLOYEE("SubEmployee", "INTERNA<PERSON>","租赁员工"),
    OS_PER_DELIVERED("OSPerdelivered", "EXTERNAL","外包-按单收费"),
    OS_FIXED_SALARY("OSFixedsalary", "EXTERNAL","外包-固定工资"),
    OUT_SOURCE("OutSource", "EXTERNAL","外包"),
    FREELANCER("Freelancer", "EXTERNAL", "众包"),
    ;
    private String code;

    private String employeeType;

    private String desc;

    DriverTypeEnum(String code, String employeeType, String desc) {
        this.code = code;
        this.employeeType = employeeType;
        this.desc = desc;
    }

    public static DriverTypeEnum getInstance(String code) {
        for (DriverTypeEnum value : DriverTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<DriverTypeEnum> getExternalOrSubEmployeeList() {
        return Arrays.asList(SUB_EMPLOYEE,OS_PER_DELIVERED, OS_FIXED_SALARY, OUT_SOURCE);
    }
}
