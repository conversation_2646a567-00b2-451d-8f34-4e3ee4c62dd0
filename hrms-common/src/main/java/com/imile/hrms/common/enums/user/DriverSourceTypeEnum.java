package com.imile.hrms.common.enums.user;


import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/28
 */
public enum DriverSourceTypeEnum {
    /**
     * 司机页面新增
     */
    ADD,
    /**
     * 从员工转化
     */
    USER_ADD,
    ;

    private static final Map<String, DriverSourceTypeEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (DriverSourceTypeEnum codeEnum : values()) {
            cacheMap.put(codeEnum.name(), codeEnum);
        }
    }


    public static DriverSourceTypeEnum parserEnum(String code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
