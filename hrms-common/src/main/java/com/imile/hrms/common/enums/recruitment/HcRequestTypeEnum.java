package com.imile.hrms.common.enums.recruitment;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Getter
public enum HcRequestTypeEnum {
    NEW(1, "新增", "New"),
    SUBSTITUTE(2, "替代", "Replacement"),
    /**
     * 对应数据库默认值，无意义
     */
    OTHER(0, "", ""),
    ;

    private final int value;
    private final String description;

    private final String descriptionEn;

    HcRequestTypeEnum(int value, String description, String descriptionEn) {
        this.value = value;
        this.description = description;
        this.descriptionEn = descriptionEn;
    }

    public static HcRequestTypeEnum getInstance(Integer code) {
        for (HcRequestTypeEnum value : HcRequestTypeEnum.values()) {
            if (value.getValue() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException(code + "");
    }
}
