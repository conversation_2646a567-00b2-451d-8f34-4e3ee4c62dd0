package com.imile.hrms.common.enums.achievement;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TargetStatusEnum {

    NOCOMMITTED("0", "未提交", "No committed"),
    BECONFIRMED("4", "待确认目标", "To be confirmed"),
    COMMITTED("1", "已提交目标", "Confirmed"),
    PENDING("2", "待确认结果", "Pending"),
    COMPLETED("3", "已确认结果", "Completed"),
    ;

    private String code;

    private String desc;

    private String descEn;

    TargetStatusEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static TargetStatusEnum getInstance(String code) {
        for (TargetStatusEnum value : TargetStatusEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findAny()
                .map(s -> RequestInfoHolder.isChinese() ? s.getDesc() : s.getDescEn())
                .orElse(null);
    }
}
