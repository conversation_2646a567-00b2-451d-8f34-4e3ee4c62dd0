package com.imile.hrms.common.entity;

import java.io.Serializable;

/**
 * excel公共交互对象
 *
 * <AUTHOR>
 * @Company 杭州艾麦科技有限公司
 * @className: ExcelImport.java
 * @date 2017年8月9日 下午12:33:47
 */
public abstract class ExcelExport implements Serializable {
    /**
     * 是否成功 true成功false失败
     */
    private Boolean success;


    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
