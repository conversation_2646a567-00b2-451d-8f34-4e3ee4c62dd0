package com.imile.hrms.common.enums.achievement;

import lombok.Getter;

@Getter
public enum CompleteTypeEnum {

    PRISM("1", "是prism读取"),
    ARTIFICIAL("0", "手动录入"),
    OPERATE("2", "运营读取"),
    ;

    private String code;

    private String desc;

    CompleteTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CompleteTypeEnum getInstance(String code) {
        for (CompleteTypeEnum value : CompleteTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
