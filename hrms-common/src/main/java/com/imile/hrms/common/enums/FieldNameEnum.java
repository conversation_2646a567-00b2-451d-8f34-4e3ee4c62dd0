package com.imile.hrms.common.enums;

import lombok.Getter;

/**
 * 薪资类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/29
 */
@Getter
public enum FieldNameEnum {
    /**
     * id
     */
    ID("id"),
    USER_ID("userId"),
    /**
     * 计薪方案编码
     */
    SALARY_CONFIG_NO("salaryConfigNo"),
    /**
     * 社保方案编码
     */
    SOCIAL_CONFIG_NO("socialConfigNo"),
    /**
     * 公积金编码
     */
    ACCUMULATION_CONFIG_NO("accumulationConfigNo"),
    ;

    private String code;

    FieldNameEnum(String code) {
        this.code = code;
    }

}
