package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/24 14:39
 * @version: 1.0
 */
@Getter
public enum SalaryItemValueTypeEnum {

    MANUAL_INPUT("MANUAL_INPUT", "手工输入", "Manual input"),
    FIXED_VALUE("FIXED_VALUE", "固定值", "fixed value"),
    ATTENDANCE_REPORT("ATTENDANCE_REPORT", "取值于考勤系统", "value source attendance system"),
    FORMULA_CALCULATION("FORMULA_CALCULATION", "公式计算", "Formula calculation"),
    VALUE_SOURCE_SALARY_SCHEME("VALUE_SOURCE_SALARY_SCHEME", "取值逻辑来源于计薪方案", "Value Source Salary Scheme"),

    ;

    private String code;

    private String desc;

    private String descEn;


    SalaryItemValueTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static SalaryItemValueTypeEnum getInstance(String code) {
        for (SalaryItemValueTypeEnum value : SalaryItemValueTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
