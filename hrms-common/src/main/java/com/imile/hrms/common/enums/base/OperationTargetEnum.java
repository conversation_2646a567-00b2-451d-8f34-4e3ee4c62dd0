package com.imile.hrms.common.enums.base;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/17
 */
@Getter
public enum OperationTargetEnum {

    /**
     * 操作对象枚举
     */
    UNKNOWN("UNKNOWN", "未知"),
    DEPT("DEPT", "部门"),
    DEPT_BIZ_LEADER("DEPT_BIZ_LEADER", "部门业务负责人"),
    USER("USER", "人员"),
    USER_CERTIFICATE("USER_CERTIFICATE", "人员证件"),
    USER_VISA("USER_VISA", "人员签证"),
    USER_DEPENDENT("USER_DEPENDENT", "人员依赖人"),
    USER_EDUCATION("USER_EDUCATION", "人员教育经历"),
    USER_LANGUAGE_ABILITY("USER_LANGUAGE_ABILITY", "人员语言能力"),
    USER_QUALIFICATION("USER_QUALIFICATION", "人员执业资格"),
    USER_CONTRACT("USER_CONTRACT", "人员合同"),
    ;

    private final String code;

    private final String desc;

    OperationTargetEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
