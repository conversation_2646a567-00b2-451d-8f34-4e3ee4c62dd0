package com.imile.hrms.common.enums.leave;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIsInvalidEnum
 * {@code @since:} 2024-04-15 16:49
 * {@code @description:}
 */
@Getter
public enum LeaveConfigIsInvalidEnum {
    DEFAULT(0, "", ""),
    YES(1, "永久有效", "Valid forever"),
    NO(2, "非永久有效", "Not permanent"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigIsInvalidEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigIsInvalidEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigIsInvalidEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigIsInvalidEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
