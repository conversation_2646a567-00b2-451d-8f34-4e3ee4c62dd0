package com.imile.hrms.common.enums;

/**
 * 组织枚举类
 */
public enum UserCountDimensionEnum {
    /**
     * 统计用户数维度
     */
    COMPANY("company", "子公司"),
    DEPT("dept", "部门"),
    POST("post", "岗位");

    UserCountDimensionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
