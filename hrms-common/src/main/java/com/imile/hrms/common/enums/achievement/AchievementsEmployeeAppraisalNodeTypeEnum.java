package com.imile.hrms.common.enums.achievement;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/5/29
 */
public enum AchievementsEmployeeAppraisalNodeTypeEnum {

    /**
     * 员工考核节点类型（10:考核准备 21:目标制定 22:目标审核 30:中期辅导 41:考核结果评估 42:考核结果审核 43:绩效面谈 44:绩效结果确认）
     */
    PREPARE(10, "考核准备"),
    SUBMIT_TARGET(21, "目标制定"),
    AUDIT_TARGET(22, "目标审核"),
    COACHING(30, "中期辅导"),
    EMPLOYEE_EVALUATE(41, "员工自评"),
    LEADER_EVALUATE(42, "考核责任人评价"),
    EXERCISER_EVALUATE(43, "行业行权人评价"),
    RESULT_FIRST(44, "绩效结果初评"),
    RESULT_final(45, "绩效结果终评"),
    RESULT_INTERVIEW(46, "绩效面谈"),
    RESULT_CONFIRM(47, "绩效结果确认"),
    ;

    @Getter
    private final Integer type;

    @Getter
    private final String desc;

    AchievementsEmployeeAppraisalNodeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
