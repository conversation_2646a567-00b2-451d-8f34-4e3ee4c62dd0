package com.imile.hrms.common.enums.user;

import lombok.Getter;

/**
 * 人员权限类型枚举
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
@Getter
public enum UserPermissionTypeEnum {
    /**
     * 全量数据权限
     */
    ALL(0, "全量数据权限"),
    /**
     * 部门主数据权限=权限系统-数据权限-HRMS-部门主数据
     */
    DEPT(1, "部门主数据权限"),
    /**
     * 主管数据权限=作为汇报上级的人员管辖权限+作为部门主管的部门管辖权限
     */
    LEADER(2, "主管数据权限"),
    ;

    private final Integer type;

    private final String desc;

    UserPermissionTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
