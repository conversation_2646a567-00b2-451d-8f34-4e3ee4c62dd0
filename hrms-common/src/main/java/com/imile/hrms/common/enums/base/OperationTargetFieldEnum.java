package com.imile.hrms.common.enums.base;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/7/4
 */
@Getter
public enum OperationTargetFieldEnum {
    /**
     * 操作对象字段枚举类
     */
    UNKNOWN("", "未知", "unknown", OperationTargetEnum.UNKNOWN, Boolean.FALSE, OperationFieldParseMethodEnum.IGNORE),
    // 部门基础信息
    DEPT_PARENT_ID("parentId", "上级组织", "Upper-level Organization", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_DEPT),
    DEPT_ORG_TYPE("deptOrgType", "组织类型", "Organization Type", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_DEPT_TYPE),
    DEPT_NAME_CN("deptNameCn", "名称CN", "Org Name CN", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    DEPT_NAME_EN("deptNameEn", "名称EN", "Org Name EN", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    ORGANIZATION_CODE("organizationCode", "Org Code", "Org Code", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    DEPT_LEADER_ID("leaderCode", "负责人", "Head of Org", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_USER),
    COUNTRY("country", "国家", "Country", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    PROVINCE("province", "省份", "Province", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    CITY("city", "城市", "City", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    REGION("region", "区域", "District", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    ADDRESS("address", "详细地址", "Detailed Address", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    DEPT_BIZ_AREA_ID("bizArea", "业务领域", "Professional Function", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_BIZ_AREA),
    DEPT_POSITION("deptPosition", "组织定位", "Org Orientation", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    DEPT_DUTY("deptDuty", "组织职责", "Org Responsibility", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    BIZ_COUNTRY("bizCountry", "业务覆盖国家", "Business Coverage Country", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    DEPT_BIZ_MODEL_IDS("bizModelId", "业务节点", "Business Node", OperationTargetEnum.DEPT, Boolean.TRUE, OperationFieldParseMethodEnum.REFER_BIZ_MODEL),
    // 部门业务负责人信息
    BIZ_LEADER_INFO("bizLeaderInfo", "业务负责人信息", "Business Responsible Info", OperationTargetEnum.DEPT, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    LEADER_PROPERTY("leaderProperty", "负责人类型", "Responsible Type", OperationTargetEnum.DEPT_BIZ_LEADER, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_LEADER_PROPERTY),
    DEPT_BIZ_LEADER_IDS("leaderId", "业务负责人", "Business Head of Org", OperationTargetEnum.DEPT_BIZ_LEADER, Boolean.TRUE, OperationFieldParseMethodEnum.REFER_USER),
    // 人员基础信息
    IS_DRIVER("isDriver", "是否为司机", "Driver", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.ENUM),
    IS_DTL("isDtl", "是否DTL", "DTL", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.ENUM),
    USER_DEPT_ID("deptId", "部门", "Department", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_DEPT),
    OC_CODE("ocCode", "核算单元", "Accounting Unit", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_OC),
    ORIGIN_COUNTRY("originCountry", "核算组织", "Accounting Organization", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    EMPLOYEE_TYPE("employeeType", "用工类型", "Workforce Type", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_EMPLOYMENT_TYPE),
    VENDOR_CODE("vendorCode", "供应商", "Vendor", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_VENDOR),
    CONTRACT_ENTITY_CODE("settlementCenterCode", "签约主体", "Contract Entity", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_COMPANY),
    USER_POST_ID("postId", "岗位", "Designation", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_POST),
    USER_LEADER_ID("leaderId", "汇报上级", "Reporting Supervisor", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_USER),
    USER_GRADE_ID("gradeId", "职级序列", "Job Sequence", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_GRADE),
    JOB_LEVEL("gradeNo", "职级", "Job Level", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DECRYPT),
    JOB_GRADE("jobGrade", "职等", "Job Grade", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DECRYPT),
    USER_BIZ_MODEL_ID("bizModelId", "业务节点", "Business Node", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_BIZ_MODEL),
    PROJECT_CODE("projectCode", "项目", "Project", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_PROJECT),
    LOCATION_COUNTRY("locationCountry", "常驻国", "Base Location(Country) ", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    LOCATION_PROVINCE("locationProvince", "常驻省", "Base Location(Province) ", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    LOCATION_CITY("locationCity", "常驻市", "Base Location(City)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    IS_GLOBAL_RELOCATION("isGlobalRelocation", "是否派遣", "Expat", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.ENUM),
    USER_NAME("userName", "姓名全称", "Full Name", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    USER_NAME_EN("userNameEn", "英文名", "English Name", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    COUNTRY_CODE("countryCode", "国籍", "Nationality", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    SEX("sex", "性别", "Gender", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.ENUM),
    BIRTHDAY("birthday", "出生日期", "Date of Birth", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    PHONE("phone", "联系电话", "Contact Number", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    // 人员扩展信息
    USER_NAME_PINYIN("userNamePinyin", "姓名全拼", "Full Spelling", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    WORK_SENIORITY("workSeniority", "工龄（月）", "Working Duration(Months)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    PERSONAL_EMAIL("personalEmail", "个人邮箱", "Personal Email", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    NATION("nation", "民族", "Ethnic", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_NATION),
    HUKOU_TYPE("hukouType", "户口类型", "Registered Residence Type", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_RESIDENCE_TYPE),
    RESIDENCE_PROVINCE_ID("residenceProvinceId", "户口所在地（省份）", "Registered Residence Location(Province)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_REGION),
    RESIDENCE_CITY_ID("residenceCityId", "户口所在地（城市）", "Registered Residence Location(City)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_REGION),
    RESIDENCE_DISTRICT_ID("residenceDistrictId", "户口所在地（县区）", "Registered Residence Location(District)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_REGION),
    NATIVE_PLACE_PROVINCE_ID("nativePlaceProvinceId", "籍贯（省份）", "Place of Origin(Province)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_REGION),
    NATIVE_PLACE_CITY_ID("nativePlaceCityId", "籍贯（城市）", "Place of Origin(City)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_REGION),
    NATIVE_PLACE_DISTRICT_ID("nativePlaceDistrictId", "籍贯（县区）", "Place of Origin(District)", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_REGION),
    POST_ADDRESS("postAddress", "居住地址（稳定的）", "Residential Address（Stable）", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    MARITAL_STATUS("maritalStatus", "婚姻状况", "Marital Status", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_MARITAL_STATUS),
    POLITICS_STATUS("politicsStatus", "政治面貌", "Political Status", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_POLITICAL_AFFILIATION),
    // 证件信息
    USER_CERTIFICATE_INFO("userCertificateInfo", "证件信息", "Certificate Information", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    CERTIFICATE_TYPE_CODE("certificateTypeCode", "证件类型", "Certificate Type", OperationTargetEnum.USER_CERTIFICATE, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_CERTIFICATE_TYPE),
    CERTIFICATE_CODE("certificateCode", "证件号", "ID No.", OperationTargetEnum.USER_CERTIFICATE, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    CERTIFICATE_RECEIPT_DATE("certificateReceiptDate", "证件生效日期", "Issue Date", OperationTargetEnum.USER_CERTIFICATE, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    CERTIFICATE_EXPIRE_DATE("certificateExpireDate", "证件失效日期", "Expiry Date", OperationTargetEnum.USER_CERTIFICATE, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    // 签证信息
    USER_VISA_INFO("userVisaInfo", "签证信息", "Visa Information", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    VISA_TYPE("visaType", "签证类型", "Visa Type", OperationTargetEnum.USER_VISA, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_VISA_TYPE),
    VISA_NO("visaNo", "签证号", "VISA No.", OperationTargetEnum.USER_VISA, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    VISA_START_DATE("startDate", "签证生效日期", "Issue Date", OperationTargetEnum.USER_VISA, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    VISA_END_DATE("endDate", "签证失效日期", "Expiry Date", OperationTargetEnum.USER_VISA, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    ISSUE_ORGANIZATION("issueOrganization", "签发组织", "Establisment", OperationTargetEnum.USER_VISA, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    ISSUE_PLACE("issuePlace", "签发地点", "Issue Place", OperationTargetEnum.USER_VISA, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    // 依赖人
    USER_DEPENDENT_INFO("userDependentInfo", "依赖人", "Dependentes de Imposto de Renda", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    DEPENDENT_TYPE("dependentType", "关系", "Relationship", OperationTargetEnum.USER_DEPENDENT, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_DEPENDENT_TYPE),
    TAX_ID("taxId", "税号", "Tax ID No", OperationTargetEnum.USER_DEPENDENT, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    // 教育经历
    USER_EDUCATION_INFO("userEducationInfo", "教育经历", "Education Background", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    EDUCATION("education", "学历", "Academic Degree", OperationTargetEnum.USER_EDUCATION, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_EDUCATION),
    SCHOOL_NAME("schoolName", "院校名称", "Institution Name", OperationTargetEnum.USER_EDUCATION, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    SCHOOL_START_DATE("startDate", "入学时间", "Date of Entry", OperationTargetEnum.USER_EDUCATION, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    SCHOOL_END_DATE("endDate", "毕业时间", "Graduation Time", OperationTargetEnum.USER_EDUCATION, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    MAJOR("major", "专业", "Major", OperationTargetEnum.USER_EDUCATION, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    IS_FULL_TIME("isFullTime", "是否全日制", "Is Full-time", OperationTargetEnum.USER_EDUCATION, Boolean.FALSE, OperationFieldParseMethodEnum.ENUM),
    // 语言能力
    USER_LANGUAGE_ABILITY_INFO("userLanguageAbilityInfo", "语言能力", "Language Skills", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    LANGUAGE_TYPE("languageType", "语言类型", "Language Type", OperationTargetEnum.USER_LANGUAGE_ABILITY, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_LANGUAGE_TYPE),
    LANGUAGE_LEVEL_ID("languageLevelId", "语言等级", "Language Level", OperationTargetEnum.USER_LANGUAGE_ABILITY, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_LANGUAGE_LEVEL),
    LANGUAGE_SCORE("languageScore", "语言分数", "Score", OperationTargetEnum.USER_LANGUAGE_ABILITY, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    // 执业证书和职业资格
    USER_QUALIFICATION_INFO("userQualificationInfo", "执业证书和职业资格", "Skill Certificate", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    QUALIFICATION_NAME("qualificationName", "证书名称", "Certificate Name", OperationTargetEnum.USER_QUALIFICATION, Boolean.FALSE, OperationFieldParseMethodEnum.DEFAULT),
    QUALIFICATION_RECEIVE_DATE("receiveDate", "获取日期", "Issue Date", OperationTargetEnum.USER_QUALIFICATION, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    // 合同信息
    USER_CONTRACT_INFO("userContractInfo", "合同信息", "Contract Information", OperationTargetEnum.USER, Boolean.FALSE, OperationFieldParseMethodEnum.JSON),
    CONTRACT_TYPE("contractType", "合同类型", "Contract Type", OperationTargetEnum.USER_CONTRACT, Boolean.FALSE, OperationFieldParseMethodEnum.DICT_CONTRACT_TYPE),
    CONTRACT_COMPANY_TYPE("contractCompanyType", "合同公司类型", "Contract Company Type", OperationTargetEnum.USER_CONTRACT, Boolean.FALSE, OperationFieldParseMethodEnum.ENUM),
    CONTRACT_COMPANY_CODE("contractCompanyCode", "合同公司编码", "Contract Company Code", OperationTargetEnum.USER_CONTRACT, Boolean.FALSE, OperationFieldParseMethodEnum.REFER_VENDOR_OR_COMPANY),
    CONTRACT_START_DATE("startDate", "当前合同开始日期", "Issue Date", OperationTargetEnum.USER_CONTRACT, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    CONTRACT_END_DATE("endDate", "当前合同终止日期", "Expiry Date", OperationTargetEnum.USER_CONTRACT, Boolean.FALSE, OperationFieldParseMethodEnum.DATE),
    ;

    private final String key;

    private final String descCn;

    private final String descEn;

    private final OperationTargetEnum target;

    private final Boolean isMultipleValue;

    private final OperationFieldParseMethodEnum parseMethod;

    OperationTargetFieldEnum(String key, String descCn, String descEn, OperationTargetEnum target,
                             Boolean isMultipleValue, OperationFieldParseMethodEnum parseMethod) {
        this.key = key;
        this.descCn = descCn;
        this.descEn = descEn;
        this.target = target;
        this.isMultipleValue = isMultipleValue;
        this.parseMethod = parseMethod;
    }

    public static OperationTargetFieldEnum valueOfKey(String key) {
        return Arrays.stream(values())
                .filter(item -> item.getKey().equals(key))
                .findAny()
                .orElse(OperationTargetFieldEnum.UNKNOWN);
    }

    public static OperationTargetFieldEnum valueOfKeyAndTarget(String key, OperationTargetEnum targetEnum) {
        return Arrays.stream(values())
                .filter(item -> item.getKey().equals(key) && item.getTarget().equals(targetEnum))
                .findAny()
                .orElse(OperationTargetFieldEnum.UNKNOWN);
    }

    public static OperationFieldParseMethodEnum parseMethodOfKey(String key) {
        return Arrays.stream(values())
                .filter(item -> item.getKey().equals(key))
                .findAny()
                .map(OperationTargetFieldEnum::getParseMethod)
                .orElse(OperationTargetFieldEnum.UNKNOWN.getParseMethod());
    }

    public String getDesc() {
        return RequestInfoHolder.isChinese() ? this.descCn : this.descEn;
    }
}
