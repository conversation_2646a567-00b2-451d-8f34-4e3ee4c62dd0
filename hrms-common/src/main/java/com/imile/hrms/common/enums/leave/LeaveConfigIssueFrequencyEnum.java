package com.imile.hrms.common.enums.leave;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIssueFrequency
 * {@code @since:} 2024-04-15 16:15
 * {@code @description:}
 */
@Getter
public enum LeaveConfigIssueFrequencyEnum {
    DEFAULT(0, "", ""),
    PERIODICAL_ISSUANCE(1, "周期性发放", "Periodical issuance"),
    ONE_TIME_ISSUANCE(2, "一次性发放", "One time issuance"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigIssueFrequencyEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigIssueFrequencyEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigIssueFrequencyEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigIssueFrequencyEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
