package com.imile.hrms.common.enums.recruitment;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
public enum HcCooperatorStatusEnum {
    PENDING_ACTIVE(0, "待生效", "pending activation"),
    ACTIVE(10, "生效", "active"),
    ;

    private final int value;
    private final String description;

    private final String descriptionEn;

    HcCooperatorStatusEnum(int value, String description, String descriptionEn) {
        this.value = value;
        this.description = description;
        this.descriptionEn = descriptionEn;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public String getDescriptionEn() {
        return descriptionEn;
    }
}
