package com.imile.hrms.common.enums.punch;

import lombok.Getter;

/**
 * 仓内考勤供应商班次确认结果状态枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum WarehouseClassConfirmStatusEnum {

    /**
     * 初始化
     */
    INIT(0, "初始值", "initialize"),
    /**
     * 未配置
     */
    NOT_CONFIG(1, "未配置", "not configured"),
    /**
     * 未确认
     */
    UNCONFIRMED(2, "未确认", "unconfirmed"),
    /**
     * 已确认
     */
    CONFIRMED(3, "已确认", "Confirmed"),
    ;

    private final Integer code;

    private final String desc;

    private final String descEn;

    WarehouseClassConfirmStatusEnum(Integer code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }
}
