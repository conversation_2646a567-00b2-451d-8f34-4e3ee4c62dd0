package com.imile.hrms.common.enums.organization;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
@Getter
public enum TriggerTimeTypeEnum {
    /**
     * ADD: 新增组织, ACTIVE: 启用组织, DISABLED: 停用组织
     */
    DEFAULT(0, "缺省值"),
    ACTIVE(1, "生效时间"),
    DISABLED(2, "停用时间"),
    ;

    private final Integer code;

    private final String value;

    TriggerTimeTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }
}

