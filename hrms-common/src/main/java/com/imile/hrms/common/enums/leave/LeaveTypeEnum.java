package com.imile.hrms.common.enums.leave;

import lombok.Getter;

/**
 * 请假类型
 * <AUTHOR>
 * @date 2022-05-24
 */
@Getter
public enum LeaveTypeEnum {

    LEAVE("leave", "请假", ""),
    CANCEL("cancel", "销假", ""),
    ADMIN_OPERATION_LEAVE("admin_operation_leave","管理员操作(请假)", ""),
    ADMIN_OPERATION_CANCEL("admin_operation_cancel","管理员操作(销假)", ""),
    ADMIN_ANNUAL_LEAVE_OPERATION("admin_annual_leave_operation", "管理员年假结余导入", ""),
    ADMIN_NORMAL_LEAVE_OPERATION("admin_normal_leave_operation", "管理员普通假期导入", ""),
    REISSUE("reissue", "在每次派遣结束时对非派遣假进行补发", "Reissue non dispatch leave at the end of each dispatch"),
    DEDUCTION("deduction", "在连续派遣或回国时系统回收多发额度", "The system recovers the excess quota during continuous dispatch or return to motherland"),
    RECALCULATE("recalculate","符合跨层级新规则时重新计算补发假期", "Replacement leave is recalculated when the new cross-tier rules are met"),
    ;

    private String code;

    private String desc;

    private String descEn;

    LeaveTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveTypeEnum getInstance(String sortType) {
        for (LeaveTypeEnum value : LeaveTypeEnum.values()) {
            if (value.name().equalsIgnoreCase(sortType)) {
                return value;
            }
        }
        return null;
    }
}
