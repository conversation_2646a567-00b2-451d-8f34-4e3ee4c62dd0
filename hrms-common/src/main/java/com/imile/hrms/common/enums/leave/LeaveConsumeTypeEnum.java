package com.imile.hrms.common.enums.leave;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请假限制消耗类型枚举
 */
@Getter
public enum LeaveConsumeTypeEnum {
    DEFAULT("0", "工作日消耗假期", "Working day"),
    OFF("1", "休息日消耗假期", "OFF"),
    PH("2", "法定节假日消耗假期", "PH");

    private final String type;

    private final String desc;

    private final String descEn;

    private static final Map<String, LeaveConsumeTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConsumeTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConsumeTypeEnum(String type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConsumeTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
