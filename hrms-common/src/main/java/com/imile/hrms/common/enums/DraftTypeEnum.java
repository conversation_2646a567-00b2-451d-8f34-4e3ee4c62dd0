package com.imile.hrms.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Title: 员工类型
 * @date 2021/11/3 11:15 上午
 */
@Getter
public enum DraftTypeEnum {

    ENTRY_DRAFT("ENTRY_DRAFT","入职草稿类型"),
    PROMOTION_ANNUAL_APPLY("PROMOTION_ANNUAL_APPLY","年度晋升申请"),
    PROMOTION_ANNUAL_REVIEW("PROMOTION_ANNUAL_REVIEW","年度晋升评议"),
    ;

    private final String code;

    private final String desc;

    DraftTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
