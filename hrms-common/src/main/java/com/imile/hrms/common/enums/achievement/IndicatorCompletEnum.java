package com.imile.hrms.common.enums.achievement;

import lombok.Getter;

@Getter
public enum IndicatorCompletEnum {

    /**
     * 是否来自prism
     */

    YES("1", "是"),
    NO("0", "否"),



    ;

    private String code;

    private String desc;

    IndicatorCompletEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static IndicatorCompletEnum getInstance(String code) {
        for (IndicatorCompletEnum value : IndicatorCompletEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
