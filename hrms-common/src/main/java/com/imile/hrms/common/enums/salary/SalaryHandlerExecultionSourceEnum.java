package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/4 17:09
 * @version: 1.0
 */
@Getter
public enum SalaryHandlerExecultionSourceEnum {

    AUTO("AUTO", "自动", "auto"),
    MANUAL("MANUAL", "手动", "manual"),

    ;

    private String code;

    private String desc;

    private String descEn;


    SalaryHandlerExecultionSourceEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static SalaryHandlerExecultionSourceEnum getInstance(String code) {
        for (SalaryHandlerExecultionSourceEnum value : SalaryHandlerExecultionSourceEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
