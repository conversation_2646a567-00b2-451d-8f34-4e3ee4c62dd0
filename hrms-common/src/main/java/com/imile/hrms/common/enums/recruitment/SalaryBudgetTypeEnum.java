package com.imile.hrms.common.enums.recruitment;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
public enum SalaryBudgetTypeEnum {
    BELOW_3K(1, "低于3k"),
    BETWEEN_3K_AND_10K(2, "3k-10k(包括10k)"),
    BETWEEN_10K_AND_20K(3, "10k-20k(包括20k)"),
    ABOVE_20K(4, "大于20k"),
    /**
     * 对应数据库默认值，无意义
     */
    OTHER(0, ""),;

    public static SalaryBudgetTypeEnum getInstance(Integer code) {
        for (SalaryBudgetTypeEnum value : SalaryBudgetTypeEnum.values()) {
            if (value.getValue() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException(code + "");
    }

    private final int value;
    private final String description;

    SalaryBudgetTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
