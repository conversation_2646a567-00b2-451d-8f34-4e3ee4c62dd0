package com.imile.hrms.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Getter
public enum WorkStatusEnum {
    ON_JOB("ON_JOB", "在职", "Active"),
    TRA<PERSON><PERSON><PERSON>("T<PERSON><PERSON><PERSON><PERSON>", "调离", "Transfer"),
    DIMISSION("DIMISSION", "离职", "OffBoarding"),
    ;


    private String code;

    private String desc;

    private String descEn;

    WorkStatusEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static WorkStatusEnum getInstance(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (WorkStatusEnum value : WorkStatusEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isOnJob(String code) {
        if(StringUtils.isBlank(code)){
            return false;
        }
        return ON_JOB.getCode().equals(code);
    }
}
