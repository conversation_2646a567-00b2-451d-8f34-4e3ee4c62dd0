package com.imile.hrms.common.enums.user;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum DriverLevelAffectFunctionEnum {

    pickupAssign("pickupAssign", "分配取件员", "Assign DA(Pickup)"),
    reverseAssign("reverseAssign", "分配收派员(逆向)", "Assign DA(Reverse)"),
    ofd("ofd", "派件扫描", "Out For Delivery"),
    ;

    private String code;

    private String desc;

    private String descEn;

    DriverLevelAffectFunctionEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static DriverLevelAffectFunctionEnum getInstanceByCode(String code) {
        for (DriverLevelAffectFunctionEnum value : DriverLevelAffectFunctionEnum.values()) {
            if (StringUtils.equalsIgnoreCase(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
