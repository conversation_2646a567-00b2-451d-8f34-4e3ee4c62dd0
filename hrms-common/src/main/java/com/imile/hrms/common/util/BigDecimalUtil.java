package com.imile.hrms.common.util;


import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * 金额换算
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/6/30 18:52
 */
public class BigDecimalUtil {

    private BigDecimalUtil() {
    }

    /**
     * 四舍五入保留2位小数
     *
     * @param arg
     * @return
     */
    public static BigDecimal setScale(BigDecimal arg) {

        return BigDecimalUtil.setScale(arg, 2);
    }

    /**
     * 四舍五入保留N位小数
     *
     * @param arg
     * @param scare 保留位数
     * @return
     */
    public static BigDecimal setScale(BigDecimal arg, int scare) {
        if (arg == null) {
            return null;
        }
        return arg.setScale(scare, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal setDoubleScale(Double arg, int scare) {
        if (arg == null) {
            return null;
        }
        if (arg == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal bigDecimal = BigDecimal.valueOf(arg);
        return BigDecimalUtil.setScale(bigDecimal, scare);

    }

    /**
     * 四舍五入保留N位小数
     *
     * @param arg
     * @param scare 保留位数
     * @return
     */
    public static String setScare(BigDecimal arg, int scare) {
        BigDecimal bl = arg.setScale(scare, BigDecimal.ROUND_HALF_UP);
        return String.valueOf(bl);
    }


    /**
     * 元转化成分
     *
     * @param arg
     * @return
     */
    public static BigDecimal setFenScale(BigDecimal arg) {
        return (arg == null ? BigDecimal.ZERO : arg).multiply(BigDecimal.valueOf(100L)).setScale(4, BigDecimal.ROUND_HALF_UP);

    }

    /**
     * 分转化成元
     *
     * @param arg
     * @return
     */
    public static BigDecimal setYuanScale(BigDecimal arg) {
        return (arg == null ? BigDecimal.ZERO : arg).divide(BigDecimal.valueOf(100L), 4, BigDecimal.ROUND_HALF_UP);

    }


    /**
     * 小数位数校验,无异常，返回true/false
     *
     * @param arg
     * @return
     */
    public static boolean checkDecimalPointWithoutException(BigDecimal arg, Integer min, Integer max) {
        return Pattern.matches("\\d*(\\.\\d{" + min + "," + max + "})?", arg.abs().toString());
    }

    public static int compare(BigDecimal from, BigDecimal to) {
        from = from == null ? BigDecimal.ZERO : from;
        to = to == null ? BigDecimal.ZERO : to;
        return from.compareTo(to);
    }

    public static BigDecimal divide(BigDecimal num1, BigDecimal num2) {
        return divide(num1, num2, 2);
    }

    public static BigDecimal divide(BigDecimal num1, BigDecimal num2, int scare) {
        if (num1 == null || num2 == null || BigDecimal.ZERO.compareTo(num2) == 0) {
            return BigDecimal.ZERO;
        }
        return num1.divide(num2, scare, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * baseValue值是否相同
     *
     * @param source
     * @param target
     * @return
     */
    public static Boolean checkSame(BigDecimal source, BigDecimal target) {
        if (source == null && target == null) {
            return Boolean.TRUE;
        }
        return source != null && target != null && source.equals(target);
    }

    /**
     * 查询是否在区间范围内
     *
     * @param target
     * @param minValue
     * @param maxValue
     * @return
     */
    public static boolean range(BigDecimal target, BigDecimal minValue, BigDecimal maxValue) {
        return target.compareTo(minValue) >= 0 && target.compareTo(maxValue) <= 0;
    }

    /**
     * 比较大小,并返回较大值
     *
     * @param value1
     * @param value2
     * @return
     */
    public static BigDecimal returnMax(BigDecimal value1, BigDecimal value2) {
        return value1 != null ? (value2 != null ? (value1.compareTo(value2) > 0 ? value1 : value2) : value1) : value2;
    }

    /**
     * 比较大小
     * 实际值是否超出最大值或小于最小值,超过最大值则取最大值，小于最小值则取最小值，否则取实际值
     *
     * @param actualValue 实际值
     * @param minValue    最小值
     * @param maxValue    最大值
     * @return
     */
    public static BigDecimal compareTo(BigDecimal actualValue, BigDecimal minValue, BigDecimal maxValue) {
        return maxValue != null && actualValue.compareTo(maxValue) > 0 ? maxValue : returnMax(actualValue, minValue);
    }

    /**
     * 两数相加
     *
     * @param var1
     * @param var2
     * @return
     */
    public static BigDecimal add(BigDecimal var1, Integer var2) {

        BigDecimal addValue = new BigDecimal(String.valueOf(var2));
        return var1.add(addValue);
    }

    /**
     * 两数相乘
     *
     * @param var1
     * @param var2
     * @return
     */
    public static BigDecimal multiply(BigDecimal var1, BigDecimal var2) {
        if (var1 == null || var2 == null) {
            return BigDecimal.ZERO;
        }
        return var1.multiply(var2);
    }

    /**
     * 两数相乘
     *
     * @param var1
     * @param var2
     * @return
     */

    public static BigDecimal multiply(BigDecimal var1, Integer var2) {

        return BigDecimalUtil.multiply(var1, new BigDecimal(var2));
    }

    /**
     * 两个数比较大小
     * @param source
     * @param target
     * @return
     */
    public static boolean compareWithSource(Number source, Number target){
        if(source == null || target==null){
            return false;
        }
        BigDecimal var1 = new BigDecimal(String.valueOf(source));
        BigDecimal var2 = new BigDecimal(String.valueOf(target));
        return var2.compareTo(var1)>=0;

    }






}
