package com.imile.hrms.common.util;

import com.google.common.math.IntMath;
import com.imile.util.reflect.BatchCallable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.CollectionUtils;

import java.math.RoundingMode;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.*;

/**
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/3/19 16:04
 */
public class CommonUtil {

    private CommonUtil(){}
    private static final Logger logger = LoggerFactory.getLogger(CommonUtil.class);

    /**
     * 批次数
     */
    public static final int BATCH_SIZE = 500;


    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date
     * @return Date
     */
    public static Date convertDateByTimeZone(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date 日期
     * @return Date
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0 ){
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * 将sourceDate转换成指定时区的时间
     *
     * @param sourceDate
     * @param sTimezone  8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param tTimezone  8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     *                   * @return
     */
    public static Date convertTimezone(Date sourceDate, String sTimezone, String tTimezone) {

        Calendar calendar = Calendar.getInstance();
        TimeZone sourceTimezone = TimeZone.getTimeZone("GMT+" + sTimezone);
        TimeZone targetTimezone = TimeZone.getTimeZone("GMT+" + tTimezone);
        // date.getTime() 为时间戳, 为格林尼治到系统现在的时间差,世界各个地方获取的时间戳是一样的,
        // 格式化输出时,因为设置了不同的时区,所以输出不一样
        long sourceTime = sourceDate.getTime();
        calendar.setTimeZone(sourceTimezone);
        calendar.setTimeInMillis(sourceTime);
        //获取源时区的到UTC的时区差
        int sourceZoneOffset = calendar.get(Calendar.ZONE_OFFSET);
        calendar.setTimeZone(targetTimezone);
        calendar.setTimeInMillis(sourceTime);

        int targetZoneOffset = calendar.get(Calendar.ZONE_OFFSET);
        int targetDaylightOffset = calendar.get(Calendar.DST_OFFSET); // 夏令时
        long targetTime = sourceTime + (targetZoneOffset + targetDaylightOffset) - sourceZoneOffset;
        return new Date(targetTime);
    }


    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date
     * @return calendar
     */
    public static Calendar convertCalendarByTimeZone(String timeZone, Date date) {
        Calendar calendar = Calendar.getInstance();
        Date convertDate = convertDateByTimeZone(timeZone, date);
        if (convertDate == null) {
            return null;
        }
        calendar.setTime(convertDate);
        return calendar;
    }

    /**
     *
     * @param list
     * @param count
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitListByCount(List<T> list, int count) {
        List<List<T>> result = new ArrayList<>();
        int totalCount = list.size();
        int size = totalCount / count;
        for (int i = 0; i < count; i++) {
            List<T> subList = new ArrayList<>();
            int index = 0;
            while (index < size) {
                subList.add(list.get(i * size + index));
                index++;
            }
            if (i == count - 1) {
                int tempIndex = count * size;
                while (tempIndex < totalCount) {
                    subList.add(list.get(tempIndex));
                    tempIndex++;
                }
            }
            result.add(subList);
        }
        return result;
    }

    //获取服务器ip
    public static String getHostIp() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface =  allNetInterfaces.nextElement();
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress ip =  addresses.nextElement();
                    if (ip instanceof Inet4Address
                        && !ip.isLoopbackAddress()
                        //loopback地址即本机地址，IPv4的loopback范围是********* ~ ***************
                        && ip.getHostAddress().indexOf(":") == -1) {

                        return ip.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param @return 设定文件
     * @return String    返回类型
     * @throws
     * @Title: getMacId
     * @Description: 获取服务器MAC地址
     */
    public static String getMacId() {
        String macId = "";
        InetAddress ip = null;
        NetworkInterface ni = null;
        try {
            boolean bFindIP = false;
            Enumeration<NetworkInterface> netInterfaces =  NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                if (bFindIP) {
                    break;
                }
                ni = netInterfaces.nextElement();

                Enumeration<InetAddress> ips = ni.getInetAddresses();
                while (ips.hasMoreElements()) {
                    ip =  ips.nextElement();
                    if (!ip.isLoopbackAddress() // 非127.0.0.1
                        && ip.getHostAddress().matches("(\\d{1,3}\\.){3}\\d{1,3}")) {
                        bFindIP = true;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        if (null != ip) {
            try {
                macId = getMacFromBytes(ni.getHardwareAddress());
            } catch (SocketException e) {
                logger.error(e.getMessage());
            }
        }
        return macId;
    }

    private static String getMacFromBytes(byte[] bytes) {
        StringBuilder mac = new StringBuilder();
        byte currentByte;
        boolean first = false;
        for (byte b : bytes) {
            if (first) {
                mac.append("-");
            }
            currentByte = (byte) ((b & 240) >> 4);
            mac.append(String.format("%02X",currentByte));
            currentByte = (byte) (b & 15);
            mac.append(String.format("%02X",currentByte));
            first = true;
        }
        return mac.toString().toUpperCase();
    }

    /**
     * 分批处理
     *
     * @param params
     * @param call
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> int batchExecuteMulti(final List<T> params,
                                            BatchCallable<List<T>> call) throws Exception {
        int executeNum = 0;

        if (!CollectionUtils.isEmpty(params)) {
            int executeTimes = 1;
            int paramsSize = params.size();
            if (paramsSize > BATCH_SIZE) {
                executeTimes = IntMath.divide(paramsSize, BATCH_SIZE, RoundingMode.CEILING);
            }

            for (int i = 0; i < executeTimes; i++) {
                int start = i * BATCH_SIZE;
                int end = (i + 1) * BATCH_SIZE;
                if (end > paramsSize) {
                    end = paramsSize;
                }
                call.call(params.subList(start, end));
            }

            executeNum = paramsSize;
        }

        return executeNum;
    }

    public static <T> int batchExecuteSubmit(final List<T> params, int batchSize,
                                             BatchCallable<List<T>> call) throws Exception {
        int executeNum = 0;

        if (!CollectionUtils.isEmpty(params)) {
            int executeTimes = 1;
            int paramsSize = params.size();
            executeTimes = IntMath.divide(paramsSize, batchSize, RoundingMode.CEILING);

            for (int i = 0; i < executeTimes; i++) {
                int start = i * batchSize;
                int end = (i + 1) * batchSize;
                if (end > paramsSize) {
                    end = paramsSize;
                }
                call.call(params.subList(start, end));
            }

            executeNum = paramsSize;
        }

        return executeNum;
    }


    public static int getBatchCount(int totalCount, int batchSize) {
        int result;
        if (totalCount % batchSize == 0) {
            result = totalCount / batchSize;
        } else {
            result = totalCount / batchSize + 1;
        }
        return result;

    }

    public static String subAfter(CharSequence string, CharSequence separator, boolean isLastSeparator) {
        if (isEmpty(string)) {
            return null == string ? null : string.toString();
        } else if (separator == null) {
            return "";
        } else {
            String str = string.toString();
            String sep = separator.toString();
            int pos = isLastSeparator ? str.lastIndexOf(sep) : str.indexOf(sep);
            return pos == -1 ? "" : str.substring(pos + separator.length());
        }
    }

    public static boolean isEmpty(CharSequence str) {
        return str == null || str.length() == 0;
    }

}
