package com.imile.hrms.common.enums.leave;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum LeaveUnitEnum {
    DAYS("DAYS", "天"),
    HOURS("HOURS", "小时"),
    MINUTES("MINUTES", "分钟"),
    ;
    private String code;

    private String desc;

    LeaveUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static LeaveUnitEnum getInstance(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (LeaveUnitEnum value : LeaveUnitEnum.values()) {
            if (value.name().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
