package com.imile.hrms.common.enums.audit;

import lombok.Getter;

@Getter
public enum ApprovalEmailTemplateEnum {
    APPROVAL_REJECTED_EMAIL_TEMPLATE("approvalRejectedEmailTemplate", "审批驳回邮件模板"),
    APPROVAL_ALL_PASS_EMAIL_TEMPLATE("approvalAllPassEmailTemplate", "审核全部通过邮件模板"),
    APPROVAL_ITEM_CHANGE_EMAIL_TEMPLATE("approvalItemChangeEmailTemplate", "审批节点变更邮件模板"),
    ;

    private String code;

    private String desc;

    ApprovalEmailTemplateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
