package com.imile.hrms.common.util;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.imile.hrms.common.constants.BusinessConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/26
 */
public class CommonUtils {

    private CommonUtils() {
        // ignored
    }

    private static final Map<Class<?>, Set<Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    public static <T> Predicate<T> repeatByKey(Function<? super T, Object> key) {
        Map<Object, Boolean> seen = Maps.newConcurrentMap();
        // != null 判断是否存在于map了，若存在则返回true，不存在则为false。以用来达到筛选重复值的目的。
        return s -> seen.putIfAbsent(key.apply(s), Boolean.FALSE) != null;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = Maps.newConcurrentMap();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    public static <T, R> Predicate<T> customEqualFilter(Function<T, R> function, R value) {
        return t -> function.apply(t).equals(value);
    }

    public static List<Long> splitIds2List(String ids) {
        if (StringUtils.isBlank(ids)) {
            return Collections.emptyList();
        }
        return Splitter.on(HrmsStringUtil.COMMA).splitToList(ids).stream()
                .filter(StringUtils::isNotBlank)
                .map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());
    }

    public static <M, N> List<N> getFieldValueList(List<M> dataList, Function<M, N> getter) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream()
                .filter(data -> {
                    N fieldValue = getter.apply(data);
                    if (Objects.isNull(fieldValue)) {
                        return false;
                    }
                    if (fieldValue instanceof Long) {
                        return !BusinessConstant.DEFAULT_ID.equals(fieldValue);
                    } else if (fieldValue instanceof String) {
                        return StringUtils.isNotBlank(fieldValue.toString());
                    }
                    return false;
                })
                .map(getter)
                .distinct()
                .collect(Collectors.toList());
    }

    public static String getDefaultStringValue(Object value) {
        return Objects.isNull(value) ? "" : value.toString();
    }

    public static <T> Map<String, List<Object>> extractFieldValues(List<T> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyMap();
        }
        Map<String, List<Object>> result = Maps.newHashMap();
        // 获取所有非静态字段（包括父类）
        Set<Field> fields = getAllNonStaticFields(dataList.get(0).getClass());

        for (Field field : fields) {
            String fieldName = field.getName();
            List<Object> valueList = new ArrayList<>(dataList.size());
            for (T item : dataList) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(item);
                    valueList.add(value);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("无法访问字段: " + fieldName, e);
                }
            }
            result.put(fieldName, valueList);
        }

        return result;
    }

    // 递归获取类及其父类的所有非静态字段
    private static Set<Field> getAllNonStaticFields(Class<?> clazz) {
        return FIELD_CACHE.computeIfAbsent(clazz, k -> {
            Set<Field> fields = new LinkedHashSet<>();
            Class<?> targetClass = clazz;
            while (targetClass != null && targetClass != Object.class) {
                for (Field field : targetClass.getDeclaredFields()) {
                    if (!Modifier.isStatic(field.getModifiers())) {
                        fields.add(field);
                    }
                }
                targetClass = targetClass.getSuperclass();
            }
            return Collections.unmodifiableSet(fields);
        });
    }
}
