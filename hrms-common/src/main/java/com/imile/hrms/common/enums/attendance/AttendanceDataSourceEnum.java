package com.imile.hrms.common.enums.attendance;

import lombok.Getter;

/**
 * 考勤数据来源    人工/系统
 */
@Getter
public enum AttendanceDataSourceEnum {
    IMPORT("IMPORT","界面表格批量导入"),
    ADD("ADD", "界面人工导入"),
    SYSTEM("SYSTEM", "系统定时任务生成"),
    APPROVAL("APPROVAL", "审批发起"),
    ABNORMAL_SINGLE_HANDLER("ABNORMAL_SINGLE_HANDLER", "单个异常考勤处理"),
    ABNORMAL_BATCH_HANDLER("ABNORMAL_BATCH_HANDLER", "批量异常考勤处理"),

    ;

    private String code;

    private String desc;

    AttendanceDataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
