package com.imile.hrms.common.enums.achievement;

import lombok.Getter;

@Getter
public enum AccumulationRuleTypeEnum {

    AVERAGE("01", "平均值"),
    SUM("02", "求和"),
    ;

    private String code;

    private String desc;

    AccumulationRuleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AccumulationRuleTypeEnum getInstance(String code) {
        for (AccumulationRuleTypeEnum value : AccumulationRuleTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
