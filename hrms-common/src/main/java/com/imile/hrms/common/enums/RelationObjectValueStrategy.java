package com.imile.hrms.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Getter
public enum RelationObjectValueStrategy {

    /**
     * 中英文根据当前的语言环境自动处理（此策略 配合 fieldId使用）
     * (设置了fieldId并且中英文根据当前的语言环境自动处理)
     */
    ADAPTIVE("adaptive", "中英文自适应"),

    /**
     * 中英文自适应,英文优先
     */
    ADAPTIVE_EN_PRIORITY("adaptive_en_priority", "中英文自适应,英文优先"),

    /**
     * 中英文自适应，中文优先
     */
    ADAPTIVE_CN_PRIORITY("adaptive_cn_priority", "中英文自适应，中文优先"),

    /**
     * 1. 设置了fieldId属性 不处理中英文
     * 2. 设置fieldCn、fieldEn 中英文对号入座
     */
    NORMAL("normal", "正常Cn的设置为xxxCn字段、En的设置到xxxEn字段"),

    /**
     * 英文优先
     */
    EN_PRIORITY("en_priority", "Cn是非必填的字段,En是必填字段,如果Cn为空, 用En覆盖cn字段"),

    /**
     * 中文优先
     */
    CN_PRIORITY("cn_priority", "En是非必填的字段,Cn是必填字段,如果En为空,用Cn覆盖En字段"),


    ;


    private final String code;

    private final String desc;


    RelationObjectValueStrategy(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
