package com.imile.hrms.common.util;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.imile.common.page.Pagination;
import com.imile.common.page.PaginationResult;

import java.util.List;

/**
 * 处理分页结果
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/3/19 16:04
 */
public class PageUtil {

    /**
     * 处理分页返回数据
     *
     * @param queryData
     * @param page
     * @param pagination
     * @return
     */
    public static PaginationResult get(List queryData, Page page,
                                       Pagination pagination) {
        PaginationResult result = new PaginationResult();
        if (page != null) {
            Long totalCount = pagination.getTotalResult() == 0 ? page.getTotal()
                : pagination.getTotalResult();
            pagination.setTotalResult(totalCount.intValue());
            pagination.setPageEnabled(true);
            pagination.reset();
        }
        result.setResults(queryData);
        result.setPagination(pagination);
        return result;
    }

    /**
     * 处理分页返回数据
     *
     * @param pageInfo
     * @return
     */
    public static PaginationResult get(PageInfo pageInfo, Pagination pagination) {
        PaginationResult result = new PaginationResult();
        pagination.setTotalResult(((Long) pageInfo.getTotal()).intValue());
        pagination.setPageEnabled(true);
        pagination.reset();
        result.setResults(pageInfo.getList());
        result.setPagination(pagination);
        return result;
    }


}
