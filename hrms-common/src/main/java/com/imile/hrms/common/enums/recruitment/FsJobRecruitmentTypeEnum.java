package com.imile.hrms.common.enums.recruitment;

import lombok.Getter;

import java.util.Objects;

/**
 * 招聘-飞书职位类型
 *
 * <AUTHOR>
 * @since 2025/1/10
 */
@Getter
public enum FsJobRecruitmentTypeEnum {

    NORMAL_FULL_TIME("101", 1, "社招-全职"),
    NORMAL_OUTSOURCING("102", 1, "社招-外包"),
    NORMAL_LABOR("103", 1, "社招-劳务"),
    SCHOOL_FULL_TIME("201", 2, "校招-正式"),
    SCHOOL_PRACTICE("201", 2, "校招-实习"),
    NORMAL_PRACTICE("301", 1, "社招-实习"),
    ;

    private final String value;

    /**
     * 1 社招 2校招
     */
    private final int type;
    private final String description;

    FsJobRecruitmentTypeEnum(String value, int type, String description) {
        this.value = value;
        this.type = type;
        this.description = description;
    }

    public static FsJobRecruitmentTypeEnum getInstance(String code) {
        for (FsJobRecruitmentTypeEnum value : FsJobRecruitmentTypeEnum.values()) {
            if (Objects.equals(value.getValue(), code)) {
                return value;
            }
        }
        return null;
    }
}
