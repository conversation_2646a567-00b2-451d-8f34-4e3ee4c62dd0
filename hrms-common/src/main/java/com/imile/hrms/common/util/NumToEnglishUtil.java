package com.imile.hrms.common.util;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Scanner;

/**
 * 阿拉伯数字转英文
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/4/7 14:22
 */
public class NumToEnglishUtil {
    public static final String ZERO = "zero";
    public static final String NEGATIVE = "negative";
    public static final String SPACE = " ";
    public static final String MILLION = "million";
    public static final String THOUSAND = "thousand";
    public static final String HUNDRED = "hundred";
    public static final String[] INDNUM = {"zero", "one", "two", "three", "four", "five", "six",
        "seven", "eight", "nine", "ten", "eleven", "twelve", "thirteen",
        "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen"};
    public static final String[] DECNUM = {"twenty", "thirty", "forty", "fifty", "sixty",
        "seventy", "eighty", "ninety"};

    //数字转换英文
    public String format(int i) {

        StringBuilder sb = new StringBuilder();

        if (i == 0) {
            return ZERO;
        }

        if (i < 0) {
            sb.append(NEGATIVE).append(SPACE);
            i *= -1;
        }


        if (i >= 1000000) {
            sb.append(numFormat(i / 1000000)).append(SPACE).append(MILLION).append(SPACE);
            i %= 1000000;

        }

        if (i >= 1000) {
            sb.append(numFormat(i / 1000)).append(SPACE).append(THOUSAND).append(SPACE);

            i %= 1000;
        }

        if (i < 1000) {
            sb.append(numFormat(i));
        }

        return sb.toString();
    }

    // 3位数转英文
    public String numFormat(int i) {

        StringBuilder sb = new StringBuilder();

        if (i >= 100) {
            sb.append(INDNUM[i / 100]).append(SPACE).append(HUNDRED).append(SPACE);
        }

        i %= 100;

        if (i != 0) {
            if (i >= 20) {
                sb.append(DECNUM[i / 10 - 2]).append(SPACE);
                if (i % 10 != 0) {
                    sb.append(INDNUM[i % 10]);
                }
            } else {
                sb.append(INDNUM[i]);
            }
        }

        return sb.toString();
    }

    /**
     * 英文转数字
     *
     * @param str
     * @return
     */
    public int parse(String str) {
        HashMap<String, Integer> hm = new HashMap<String, Integer>();
        hm.put("zero", 0);
        hm.put("one", 1);
        hm.put("two", 2);
        hm.put("three", 3);
        hm.put("four", 4);
        hm.put("five", 5);
        hm.put("six", 6);
        hm.put("seven", 7);
        hm.put("eight", 8);
        hm.put("nine", 9);
        hm.put("ten", 10);
        hm.put("eleven", 11);
        hm.put("twelve", 12);
        hm.put("thirteen", 13);
        hm.put("fourteen", 14);
        hm.put("fifteen", 15);
        hm.put("sixteen", 16);
        hm.put("seventeen", 17);
        hm.put("eighteen", 18);
        hm.put("nineteen", 19);
        hm.put("twenty", 20);
        hm.put("thirty", 30);
        hm.put("forty", 40);
        hm.put("fifty", 50);
        hm.put("sixty", 60);
        hm.put("seventy", 70);
        hm.put("eighty", 80);
        hm.put("ninety", 90);
        hm.put("hundred", 100);
        hm.put("thousand", 1000);
        hm.put("million", 1000000);
        int i = 0;
        int b = 0;
        int c = 0;
        String[] k = str.split(" ");
        for (String string : k) {
            if ("hundred".equals(string)) {
                i *= hm.get("hundred");
            } else if ("thousand".equals(string)) {
                b = i;
                b *= hm.get("thousand");
                i = 0;
            } else if ("million".equals(string)) {
                c = i;
                c *= hm.get("million");
                i = 0;
            } else if ("negative".equals(string)) {
                i = 0;
            } else {
                i += hm.get(string);
            }
        }
        i += c + b;
        for (String string2 : k) {
            if ("negative".equals(string2)) {
                i = -i;
            }
        }
        return i;
    }


    public static void main(String[] args) {

        Scanner scanner = new Scanner(System.in);
        BigDecimal num = BigDecimal.valueOf(385.86);
        String result = NumToEnglishStr(num);
        System.out.printf(result);
    }

    public static String NumToEnglishStr(BigDecimal num) {
        String numResult = null;
        String numStr = String.valueOf(num);
        NumToEnglishUtil numberWordFormat = new NumToEnglishUtil();

        numStr = subZeroAndDot(numStr);

        if (isInteger(numStr)) {
            // 整数
            int integer = Integer.parseInt(numStr);

            numStr = numberWordFormat.format(integer);

            numResult = numStr;

        } else {
            // 拆分成两部分，整数部分和小数部分

            String integerNum = splitInteger(num);

            int integer = Integer.parseInt(integerNum);

            numStr = numberWordFormat.format(integer);

            String decimalNum = splitNum(num);

            String decimal = decimalToEnglish(decimalNum);

            numResult = numStr + " point " + decimal;

        }
        return numResult;
    }

    /**
     * 小数部分转换成英文
     */
    public static String decimalToEnglish(String decimalNum) {

        String[] englishNum = {"zero", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine"};

        String decimal = "";
        int num = Integer.parseInt(decimalNum);
        int numLength = decimalNum.length();
        int[] numArr = new int[numLength];
        for (int i = 0; i < numLength; i++) {
            numArr[i] = (int) (num / (Math.pow(10d, numLength - 1d - i)));
            num = (int) (num % (Math.pow(10d, numLength - 1d - i)));
            if (numArr[i] == 0) {
                decimal += englishNum[0] + " ";
            } else if (numArr[i] == 1) {
                decimal += englishNum[1] + " ";
            } else if (numArr[i] == 2) {
                decimal += englishNum[2] + " ";
            } else if (numArr[i] == 3) {
                decimal += englishNum[3] + " ";
            } else if (numArr[i] == 4) {
                decimal += englishNum[4] + " ";
            } else if (numArr[i] == 5) {
                decimal += englishNum[5] + " ";
            } else if (numArr[i] == 6) {
                decimal += englishNum[6] + " ";
            } else if (numArr[i] == 7) {
                decimal += englishNum[7] + " ";
            } else if (numArr[i] == 8) {
                decimal += englishNum[8] + " ";
            } else if (numArr[i] == 9) {
                decimal += englishNum[9] + " ";
            }
        }

        return decimal;
    }

    /**
     * 取出整数部分
     */
    public static String splitInteger(BigDecimal num) {
        String str = String.valueOf(num);
        String result = str.substring(0, str.indexOf('.'));
        return result;
    }

    /**
     * 取出小数部分
     */
    public static String splitNum(BigDecimal num) {
        String str = String.valueOf(num);
        String result = str.substring(str.indexOf('.') + 1);
        return result;
    }

    /**
     * 功能：检查请求isInteger方法的参数是否为整数
     *
     * @param str String
     * @return 返回boolean类型，false表示不是整数，true表示是整数
     */
    public static boolean isInteger(String str) {
        int begin = 0;
        if (str == null || "".equals(str.trim())) {
            return false;
        }
        str = str.trim();
        if (str.startsWith("+") || str.startsWith("-")) {
            if (str.length() == 1) {
                return false;
            }
            begin = 1;
        }
        for (int i = begin; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 使用java正则表达式去掉多余的.与0
     *
     * @param s
     * @return
     */
    public static String subZeroAndDot(String s) {
        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");// 去掉多余的0
            s = s.replaceAll("[.]$", "");// 如最后一位是.则去掉
        }
        return s;
    }
}


