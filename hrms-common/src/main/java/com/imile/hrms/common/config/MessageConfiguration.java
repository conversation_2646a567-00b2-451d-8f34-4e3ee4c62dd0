package com.imile.hrms.common.config;

import com.imile.util.lang.I18nUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

/**
 * 国际化消息配置项
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/4/10 15:10
 */
@Configuration
public class MessageConfiguration {

    /**
     * 用于解析消息的策略接口，支持这些消息的参数化和国际化。
     * basename
     */
    @Value(value = "${spring.messages.basename}")
    private String basename;
    @Value(value = "${spring.messages.encoding}")
    private String encoding;

    @Bean(name = "reloadableResourceBundleMessageSource")
    public ReloadableResourceBundleMessageSource getMessageResource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setDefaultEncoding(encoding);
        String[] basenames = basename.split(",");
        messageSource.setUseCodeAsDefaultMessage(true);
        for (String name : basenames) {
            messageSource.addBasenames(name);
        }
        // 是否使用message的format
        messageSource.setAlwaysUseMessageFormat(true);
        // 如果找不到国际化值，则默认使用code值，不抛出异常NoSuchMessageException
        // 将messageSource放到I18nUtils中
        I18nUtils.setMessageSource(messageSource);
        return messageSource;
    }
}
