package com.imile.hrms.common.enums.achievement;

import lombok.Getter;

@Getter
public enum TargetPropertiesEnum {

    FORWARD("01", "正向指标"),
    REVERSE("02", "反向指标"),

    ;

    private String code;

    private String desc;

    TargetPropertiesEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TargetPropertiesEnum getInstance(String code) {
        for (TargetPropertiesEnum value : TargetPropertiesEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
