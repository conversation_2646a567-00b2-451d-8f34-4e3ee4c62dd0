package com.imile.hrms.common.enums.base;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/17
 */
@Getter
public enum OperationFieldParseMethodEnum {

    /**
     * 操作字段解析方式枚举
     */
    IGNORE("IGNORE", "IGNORE","忽略"),
    DEFAULT("DEFAULT", "DEFAULT","常规"),
    DATE("DATE", "DATE", "日期"),
    ENUM("ENUM", "ENUM", "枚举"),
    JSON("JSON", "JSON","JSON"),
    DECRYPT("DECRYPT", "DECRYPT","解密"),
    REFER_USER("REFER_USER", "REFER","人员"),
    REFER_DEPT("REFER_DEPT", "REFER","部门"),
    REFER_BIZ_AREA("REFER_BIZ_AREA", "REFER","业务领域"),
    REFER_BIZ_MODEL("REFER_BIZ_MODEL", "REFER","业务节点"),
    REFER_POST("REFER_POST", "REFER","岗位"),
    REFER_GRADE("REFER_GRADE", "REFER","职级"),
    REFER_PROJECT("REFER_PROJECT", "REFER","项目"),
    REFER_VENDOR("REFER_VENDOR", "REFER","供应商"),
    REFER_COMPANY("REFER_COMPANY", "REFER","公司"),
    REFER_OC("REFER_OC", "REFER","网点"),
    REFER_LANGUAGE_LEVEL("REFER_LANGUAGE_LEVEL", "REFER","语言等级"),
    REFER_REGION("REFER_REGION", "REFER","地区"),
    REFER_VENDOR_OR_COMPANY("REFER_VENDOR_OR_COMPANY", "REFER","供应商或公司"),
    DICT_DEPT_TYPE("deptType", "DICT","组织类型"),
    DICT_LEADER_PROPERTY("leaderProperty", "DICT","组织业务负责人类型"),
    DICT_EMPLOYMENT_TYPE("EmploymentType", "DICT","用工类型"),
    DICT_NATION("Nation", "DICT","民族"),
    DICT_RESIDENCE_TYPE("RegisteredResidenceType", "DICT","户口类型"),
    DICT_MARITAL_STATUS("maritalStatus", "DICT","婚姻状况"),
    DICT_POLITICAL_AFFILIATION("PoliticalAffiliation", "DICT","政治面貌"),
    DICT_CERTIFICATE_TYPE("certificateTypeCode", "DICT","证件类型"),
    DICT_VISA_TYPE("GlobalVisaType", "DICT","签证类型"),
    DICT_DEPENDENT_TYPE("DependentType", "DICT","依赖人类型"),
    DICT_EDUCATION("education", "DICT","学历"),
    DICT_LANGUAGE_TYPE("GlobalLanguageType", "DICT","语言类型"),
    DICT_CONTRACT_TYPE("UserContractType", "DICT","合同类型"),
    ;

    private final String code;

    private final String type;

    private final String desc;

    OperationFieldParseMethodEnum(String code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }
}
