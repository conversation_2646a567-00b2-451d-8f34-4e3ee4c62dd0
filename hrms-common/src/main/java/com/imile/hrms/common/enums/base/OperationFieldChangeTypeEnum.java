package com.imile.hrms.common.enums.base;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/17
 */
@Getter
public enum OperationFieldChangeTypeEnum {

    /**
     * 操作字段变更类型枚举
     */
    UNKNOWN("UNKNOWN", "未知", "unknown", "", ""),
    ADD("ADD", "新增", "Add", "【%s】为【%s】", "[%s] is [%s]"),
    UPDATE("UPDATE", "更新", "Update", "【%s】由【%s】改为【%s】", "[%s] changes from [%s] to [%s]"),
    DELETE("DELETE", "删除", "Delete", "【%s】为【%s】", "[%s] is [%s]"),
    ;

    private final String code;

    private final String descCn;

    private final String descEn;

    private final String contentCn;

    private final String contentEn;

    OperationFieldChangeTypeEnum(String code, String descCn, String descEn, String contentCn, String contentEn) {
        this.code = code;
        this.descCn = descCn;
        this.descEn = descEn;
        this.contentCn = contentCn;
        this.contentEn = contentEn;
    }

    public String getDesc() {
        return RequestInfoHolder.isChinese() ? this.descCn : this.descEn;
    }

    public String getContent() {
        return RequestInfoHolder.isChinese() ? this.contentCn : this.contentEn;
    }
}
