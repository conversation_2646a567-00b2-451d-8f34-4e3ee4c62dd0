package com.imile.hrms.common.enums.account;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/6
 */
@Getter
public enum AccountTypeHandleEnum {
    /**
     * 未知
     */
    UNKNOWN("", Boolean.FALSE, Boolean.FALSE),
    /**
     * 无需处理
     */
    NO_HANDLE("NO_HANDLE", Boolean.FALSE, Boolean.FALSE),
    /**
     * 默认处理,但是用户可以取消
     */
    DEFAULT_HANDLE("DEFAULT_HANDLE", Boolean.TRUE, Boolean.TRUE),
    /**
     * 默认不处理，但是用户可以选择处理
     */
    DEFAULT_NO_HANDLE("DEFAULT_NO_HANDLE", Boolean.TRUE, Boolean.FALSE),
    /**
     * 必须处理
     */
    MUST_HANDLE("MUST_HANDLE", Boolean.FALSE, Boolean.TRUE);

    private final String type;

    /**
     * 是否允许用户操作
     */
    private final Boolean allowOperation;

    /**
     * 是否默认勾选
     */
    private final Boolean check;

    AccountTypeHandleEnum(String type, Boolean allowOperation, Boolean check) {
        this.type = type;
        this.allowOperation = allowOperation;
        this.check = check;
    }

    public static AccountTypeHandleEnum getInstance(String accountType) {

        for (AccountTypeHandleEnum value : AccountTypeHandleEnum.values()) {
            if (value.getType().equalsIgnoreCase(accountType)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
