package com.imile.hrms.common.enums.user;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
@Getter
public enum IncentiveTypeEnum {

    /**
     * "奖励/Reward", "奖励", "Reward"
     */
    REWARD("Reward", "奖励", "Reward"),

    /**
     * "惩罚/Punishment", "惩罚", "Punishment"
     */
    PUNISHMENT("Punishment", "惩罚", "Punishment");


    private String code;

    private String desc;

    private String descEn;

    IncentiveTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static String takeCodeByDesc(String desc) {
        if (StringUtils.isBlank(desc)) {
            return "";
        }
        if (StringUtils.equalsAny(desc, "奖励/Reward", "奖励", "Reward")) {
            return REWARD.code;
        }
        return PUNISHMENT.code;
    }

}
