package com.imile.hrms.common.enums.user;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
@Getter
public enum FreelancerDriverAuditTypeEnum {
    /**
     * 众包司机审核类型枚举类
     */
    REGISTER(1, "注册"),
    ;

    private final Integer type;

    private final String desc;

    FreelancerDriverAuditTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
