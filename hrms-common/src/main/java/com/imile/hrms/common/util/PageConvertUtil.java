package com.imile.hrms.common.util;

import com.github.pagehelper.PageInfo;
import com.imile.common.page.Pagination;
import com.imile.common.page.PaginationResult;
import com.imile.common.query.BaseQuery;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;

public class PageConvertUtil {

    /**
     * 返回记录为空的分页参数
     *
     * @param pagination 分页信息
     * @return
     */
    public static <T> PaginationResult<T> getEmptyPageResult(Pagination pagination) {
        PaginationResult<T> result = new PaginationResult<>();
        result.setResults(Collections.emptyList());
        result.setPagination(pagination);
        return result;
    }

    /**
     * 返回分页参数
     *
     * @param list
     * @param pageQuery
     * @param total
     * @return
     */
    public static <T> PaginationResult<T> getPageResult(List<T> list, BaseQuery pageQuery, Integer total, Integer totalPage) {
        Pagination pagination = new Pagination();
        BeanUtils.copyProperties(pageQuery, pagination);
        pagination.setTotalResult(pageQuery.getShowCount() < 0 && total < 0 ? list.size() : total);
        pagination.setTotalPage(totalPage);
        PaginationResult<T> result = new PaginationResult<>();
        result.setResults(list);
        result.setPagination(pagination);
        return result;
    }

    public static <T> PaginationResult<T> getPageRes(List<T> list, BaseQuery pageQuery, PageInfo pageInfo) {
        return getPageResult(list, pageQuery, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 返回分页参数
     *
     * @param pageInfo
     * @param pageQuery
     * @return
     */
    public static <T> PaginationResult<T> getPageResult(PageInfo<T> pageInfo, BaseQuery pageQuery) {
        return getPageResult(pageInfo.getList(), pageQuery, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 分页对象转换
     *
     * @param originDatas
     * @param clazz
     * @param <R>
     * @param <T>
     * @return
     */
    public static <R, T> PaginationResult<T> convertPage(PaginationResult<R> originDatas, Class<T> clazz) {
        if (originDatas != null) {
            PaginationResult<T> paginationResult = new PaginationResult<>();
            paginationResult.setPagination(originDatas.getPagination());
            paginationResult.setResults(HrmsCollectionUtils.convert(originDatas.getResults(), clazz));
            return paginationResult;
        }
        return null;
    }
}
