package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/31 15:02
 * @version: 1.0
 */
@Getter
public enum SalaryBizTypeEnum {
    ITEM_CONFIG("ITEM_CONFIG", "薪资项", "itemConfig"),
    SCHEME_CONFIG("SCHEME_CONFIG", "计薪方案", "schemeConfig"),
    USER_SCHEME_CONFIG("USER_SCHEME_CONFIG", "员工薪资档案", "userSchemeConfig"),
    SALARY_ATTENDANCE_USER_INFO("SALARY_ATTENDANCE_USER_INFO", "考勤数据锁定", "salaryAttendanceUserInfo"),
    SALARY_CALCULATE_TASK("SALARY_CALCULATE_TASK", "薪资计算任务", "salaryCalculateTask"),
    SALARY_REPORT_CONFIG("SALARY_REPORT_CONFIG", "考勤报表配置", "salaryReportConfig"),

    ;

    private String code;

    private String desc;

    private String descEn;


    SalaryBizTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static SalaryBizTypeEnum getInstance(String code) {
        for (SalaryBizTypeEnum value : SalaryBizTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
