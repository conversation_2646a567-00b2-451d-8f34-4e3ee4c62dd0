package com.imile.hrms.common.util;

/**
 * <AUTHOR>
 * @date 2024/4/2
 */
public class WxUtils {

    public static String decryption(String secret) {
        String[] secretStr = secret.split("");
        StringBuilder res = new StringBuilder();
        int j = 0;
        for (int n = 0; n < secretStr.length / 2; n++) {
            StringBuilder middle = new StringBuilder();
            for (int i = 0; i < 2; i++) {
                middle.append(secretStr[j]);
                j++;
            }
            // 转换成10进制数后-1
            res.append((char) (Integer.parseInt(String.valueOf(middle), 16) - 1));
        }
        return res.toString();
    }

    public static String encryption(String secret) {
        String[] secretStr = secret.split("");
        StringBuilder res = new StringBuilder();
        for (String s : secretStr) {
            char b = s.charAt(0);
            // 伪装，每一个字符+1位
            int c = b + 1;
            // 转换成十六进制
            res.append(Long.toHexString(c));
        }
        return res.toString();
    }
}
