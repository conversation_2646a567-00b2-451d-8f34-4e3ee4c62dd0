package com.imile.hrms.common.enums.approval;

import lombok.Getter;

/**
 * 招聘岗位审批模板自定义的字段
 * <AUTHOR>
 */
@Getter
public enum RecruitmentJobCustomFieldEnum {

    PROVINCE("province", "省"),
    CITY("city", "城市"),
    DISTRICT("district", "街道"),
    DETAILED_ADDRESS("detailedAddress", "详细地址"),
    JOB_NAME("jobName", "岗位"),
    HEADCOUNT("headcount", "Headcount"),

    FIRST_ROUND_INTERVIEWER_USER_IDS("hcFirstRoundInterviewerUserIds", "一面面试官"),
    SECOND_ROUND_INTERVIEWER_USER_IDS("hcSecondRoundInterviewerUserIds", "二面面试官"),
    THIRD_ROUND_INTERVIEWER_USER_IDS("hcThirdRoundInterviewerUserIds", "三面面试官"),
    JOB_RESPONSIBILITIES("jobResponsibilities", "岗位职责"),
    JOB_QUALIFICATIONS("jobQualifications", "资格说明"),
    JOB_FILE_URLS("jobFileUrls", "JD附件"),
    IT_ASSETS_REQUIRED("itAssetsRequired", "所需IT资产"),
    REMARK("remark", "备注"),
    PROFESSIONAL_FUNCTION("professionalFunction", "行业线"),

    //多值展示
    GRADE_ID_SNAPSHOT("gradeIdSnapshot", "职位信息-职级序列（快照）"),
    GRADE_NO_SNAPSHOT("gradeNoSnapshot", "职位信息-职级（快照）"),
    EMPLOYMENT_TYPE("employmentType", "职位信息-用工类型"),
    REQUEST_TYPE("requestType", "职位信息-请求类型"),
    BUDGET_MIN("budgetMin", "职位信息-最小预算"),
    BUDGET_MAX("budgetMax", "职位信息-最大预算"),
    CURRENCY("currency", "职位信息-货币"),
    REPORT_TO_USER_ID("reportToUserId", "职位信息-汇报人"),
    VACANCY("vacancy", "职位信息-空缺"),
    IS_CHINESE_INITIATING("isChineseInitiating", "职位信息-是否中方"),
    //判断条件（不展示）
    GRADE_NO("gradeNo", "职位信息-职级"),
    JOB_SEQUENCE("jobSequence", "职级序列"),
    IS_CHINESE_INITIATING_JUDGE("isChineseInitiatingJudge", "职位信息-是否中方(判断)"),
    BIZ_COUNTRY("bizCountry", "业务所属国"),
    //判断条件（可展示）
    IS_HQ_RD("isHqRd", "是否属于HQ R&D Center部门"),
    //角色字段
    BIZE_AREA("bizeArea", "业务领域(行业线)"),
    DEPT_NAME("deptName", "部门"),
    RECRUITMENT_TYPE_DESC("RecruitmentTypeDesc", "招聘类型"),
    DEPT_ID("deptId", "部门"),
    DEPT_COUNTRY("deptCountry", "国家"),

    ;

    private String code;

    private String desc;

    RecruitmentJobCustomFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
