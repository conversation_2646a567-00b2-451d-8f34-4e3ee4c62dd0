package com.imile.hrms.common.enums.training;

import lombok.Getter;

@Getter
public enum TrainingUserStatusEnum {
    INVITED("INVITED", "已邀请", "Invited"),
    UNCOMMITTED("UNCOMMITTED", "未参与", "Uncommitted"),
    PARTICIPATED("PARTICIPATED", "已参与","Participated"),
    PASS("PASS", "通过","Pass"),
    NOT_PASS("NOT_PASS", "未通过","Not Pass"),
    CANCEL("CANCEL", "取消培训","Cancel"),
    ;
    private String code;

    private String desc;

    private String descEn;

    TrainingUserStatusEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static TrainingUserStatusEnum getInstance(String code) {
        for (TrainingUserStatusEnum value : TrainingUserStatusEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
