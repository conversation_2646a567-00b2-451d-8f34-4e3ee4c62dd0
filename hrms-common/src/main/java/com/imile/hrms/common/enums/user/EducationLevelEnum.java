package com.imile.hrms.common.enums.user;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 学历枚举
 *
 * <AUTHOR>
 * @since 2025/2/28
 */
@Getter
public enum EducationLevelEnum {
    UNKNOWN("", "", "", 0),

    /**
     * 博士
     */
    DOCTOR("DOCTOR", "PhD", "博士", 1),

    /**
     * 硕士
     */
    MASTERS("MASTERS", "Master", "硕士/MBA/EMBA", 2),

    /**
     * 本科
     */
    BACHELOR("BACHELOR", "Bachelor", "本科", 3),

    /**
     * 大专
     */
    VOCATIONAL("VOCATIONAL", "College", "大专/高职", 4),

    /**
     * 高中
     */
    HIGH_SCHOOL("HIGH_SCHOOL", "High School", "高中", 5),

    /**
     * 其它
     */
    OTHER("Other", "Other", "其他", 6),

    ;


    private final String code;

    private final String descEn;

    private final String desc;

    private final Integer sort;

    EducationLevelEnum(String code, String descEn, String desc, Integer sort) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
        this.sort = sort;
    }

    public static EducationLevelEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return UNKNOWN;
        }
        for (EducationLevelEnum e : EducationLevelEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getByLanguage(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        for (EducationLevelEnum e : EducationLevelEnum.values()) {
            if (e.getCode().equals(code)) {
                return RequestInfoHolder.isChinese() ? e.getDesc() : e.getDescEn();
            }
        }
        return "";
    }

    public static String getCn(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        for (EducationLevelEnum e : EducationLevelEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return "";
    }

    public static String getEn(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        for (EducationLevelEnum e : EducationLevelEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDescEn();
            }
        }
        return "";
    }
}
