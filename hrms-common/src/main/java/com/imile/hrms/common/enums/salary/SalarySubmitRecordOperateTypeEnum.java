package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * 薪资月结数据科目提报记录 操作类型
 *
 * <AUTHOR>
 */
@Getter
public enum SalarySubmitRecordOperateTypeEnum {
    MANUAL_SUBMISSION("MANUAL_SUBMISSION", "手动提报", "Manual Submission"),
    EDIT_DIRECTLY("EDIT_DIRECTLY", "直接编辑", "Edit Directly"),
    MANUAL_IMPORT("MANUAL_IMPORT", "手动导入", "Manual Import"),
    SYSTEM_COLLECTION("SYSTEM_COLLECTION", "系统采集", "System Collection"),
    ACQUIRED_FROM_LAST_MONTH("ACQUIRED_FROM_LAST_MONTH", "从上月获取", "Acquired From Last Month"),
    CALCULATE_SALARY_REJECT("CALCULATE_SALARY_REJECT", "算薪驳回", "Calculate Salary Reject"),


    ;
    private String code;

    private String desc;

    private String descEn;

    SalarySubmitRecordOperateTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static SalarySubmitRecordOperateTypeEnum getInstance(String code) {
        for (SalarySubmitRecordOperateTypeEnum value : SalarySubmitRecordOperateTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

}
