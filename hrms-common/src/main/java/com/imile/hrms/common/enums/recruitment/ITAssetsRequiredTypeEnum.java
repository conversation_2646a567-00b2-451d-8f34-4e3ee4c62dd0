package com.imile.hrms.common.enums.recruitment;

import com.imile.hrms.common.enums.approval.ApplicationRelationTypeEnum;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
public enum ITAssetsRequiredTypeEnum {

    NOTEBOOK(1, "笔记本"),
    BARCODE_SCANNER(2, "条形码扫描器"),
    SIM_CARD(3, "SIM卡"),
    NA(4, "NA"),
    /**
     * 对应数据库默认值，无意义
     */
    OTHER(0, ""),
    ;

    private final int value;
    private final String description;

    ITAssetsRequiredTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ITAssetsRequiredTypeEnum getInstance(Integer code) {
        for (ITAssetsRequiredTypeEnum value : ITAssetsRequiredTypeEnum.values()) {
            if (value.getValue() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException(code + "");
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
