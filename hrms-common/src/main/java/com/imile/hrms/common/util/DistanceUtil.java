package com.imile.hrms.common.util;


import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 计算两个坐标之间的距离
 * @email <EMAIL>
 * @date 2022/2/8
 */
public class DistanceUtil {


    private DistanceUtil() {
    }

    private static final double EARTH_RADIUS = 6371000;//赤道半径(单位m)

    /**
     * 转化为弧度(rad)
     */
    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 基于余弦定理求两经纬度距离
     *
     * @param lon1 第一点的精度
     * @param lat1 第一点的纬度
     * @param lon1 第二点的精度
     * @param lat2 第二点的纬度
     * @return 返回的距离，单位km
     */
    public static Double LantitudeLongitudeDist(BigDecimal lon1, BigDecimal lat1, BigDecimal lon2, BigDecimal lat2) {

        if (lon1 == null || lat1 == null || lon2 == null || lat2 == null) {
            return null;
        }

        double radLat1 = rad(lat1.doubleValue());
        double radLat2 = rad(lat2.doubleValue());

        double radLon1 = rad(lon1.doubleValue());
        double radLon2 = rad(lon2.doubleValue());

        if (radLat1 < 0)
            radLat1 = Math.PI / 2 + Math.abs(radLat1);// south
        if (radLat1 > 0)
            radLat1 = Math.PI / 2 - Math.abs(radLat1);// north
        if (radLon1 < 0)
            radLon1 = Math.PI * 2 - Math.abs(radLon1);// west
        if (radLat2 < 0)
            radLat2 = Math.PI / 2 + Math.abs(radLat2);// south
        if (radLat2 > 0)
            radLat2 = Math.PI / 2 - Math.abs(radLat2);// north
        if (radLon2 < 0)
            radLon2 = Math.PI * 2 - Math.abs(radLon2);// west
        double x1 = EARTH_RADIUS * Math.cos(radLon1) * Math.sin(radLat1);
        double y1 = EARTH_RADIUS * Math.sin(radLon1) * Math.sin(radLat1);
        double z1 = EARTH_RADIUS * Math.cos(radLat1);

        double x2 = EARTH_RADIUS * Math.cos(radLon2) * Math.sin(radLat2);
        double y2 = EARTH_RADIUS * Math.sin(radLon2) * Math.sin(radLat2);
        double z2 = EARTH_RADIUS * Math.cos(radLat2);

        double d = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2) + (z1 - z2) * (z1 - z2));
        //余弦定理求夹角
        double theta = Math.acos((EARTH_RADIUS * EARTH_RADIUS + EARTH_RADIUS * EARTH_RADIUS - d * d) / (2 * EARTH_RADIUS * EARTH_RADIUS));
        double dist = theta * EARTH_RADIUS;
        return dist;
    }


    /**
     * 查找一定范围内的经纬度值   * 传入值： 经度 纬度   查找半径(m)   * 返回值：最小经度、纬度，最大经度、纬度   113.957541,22.549392 朗峰大厦
     */
    public static Map<String, Double> getAround(Double lon, Double lat, Double raidus) {
        Double PI = 3.14159265;    // 圆周率
        Double EARTH_RADIUS = 6378137d;
        // 地球半径
        Double RAD = Math.PI / 180.0;
        // 弧度
        Double longitude = lon;
        //经度
        Double latitude = lat;
        //纬度
        Double degree = (24901 * 1609) / 360.0;
        Double raidusMile = raidus;
        //距离
        Double dpmLat = 1 / degree;
        Double radiusLat = dpmLat * raidusMile;
        Double minLat = latitude - radiusLat;
        //最小纬度
        Double maxLat = latitude + radiusLat;
        //最大纬度
        Double mpdLng = degree * Math.cos(latitude * (PI / 180));
        Double dpmLng = 1 / mpdLng;
        Double radiusLng = dpmLng * raidusMile;
        Double minLng = longitude - radiusLng;
        //最小经度
        Double maxLng = longitude + radiusLng;
        //最大经度
        Map<String, Double> m = new HashMap<String, Double>();
        m.put("minLng", minLng);
        //最小经度
        m.put("minLat", minLat);
        //最小纬度
        m.put("maxLng", maxLng);
        //最大经度
        m.put("maxLat", maxLat);
        //最大纬度
        System.err.println("最小经度:" + minLng);
        System.err.println("最小纬度:" + minLat);
        System.err.println("最大经度:" + maxLng);
        System.err.println("最大纬度:" + maxLat);
        return m;
    }


}
