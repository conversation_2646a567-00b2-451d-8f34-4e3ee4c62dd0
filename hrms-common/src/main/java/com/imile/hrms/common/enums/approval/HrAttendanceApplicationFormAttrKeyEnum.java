package com.imile.hrms.common.enums.approval;

import lombok.Getter;

@Getter
public enum HrAttendanceApplicationFormAttrKeyEnum {

    /**
     * 请假类型
     */
    leaveType("leaveType", "leavetype"),

    /**
     * 假期规则主键
     */
    configID("configId", "configid"),

    /**
     * 假期简称
     */
    leaveShortName("leaveShortName", "leaveshortname"),

    /**
     * 请假开始时间
     */
    leaveStartDate("leaveStartDate", "leavestartdate"),

    /**
     * 请假结束时间
     */
    leaveEndDate("leaveEndDate", "leaveenddate"),

    /**
     * 外勤开始时间
     */
    outOfOfficeStartDate("outOfOfficeStartDate", "outofofficestartdate"),

    /**
     * 外勤结束时间
     */
    outOfOfficeEndDate("outOfOfficeEndDate", "outofofficeenddate"),

    /**
     * 用户假期可用余额(分钟)
     */
    leaveResidueMinutes("leaveResidueMinutes", "leaveresidueminutes"),

    /**
     * 每天时长计算明细
     */
    dayDurationInfoDTOList("dayDurationInfoDTOList", "daydurationinfodtolist"),

    /**
     * 备注
     */
    remark("remark", "remark"),

    /**
     * 附件
     */
    attachmentList("attachmentList", "attachmentlist"),

    /**
     * 请假单位
     */
    leaveUnit("leaveUnit", "leaveunit"),

    /**
     * 撤销原因
     */
    revokeReason("revokeReason", "revokereason"),

    /**
     * 补卡类型
     */
    reissueCardType("reissueCardType", "reissuecardtype"),

    /**
     * 补卡时间
     */
    reissueCardDayId("reissueCardDayId", "reissuecarddayid"),

    /**
     * 剩余可用补卡次数
     */
    residueReissueCardCount("residueReissueCardCount", "residuereissuecardcount"),

    /**
     * 当前补卡日期对应的考勤周期起始时间
     */
    attendanceStartDate("attendanceStartDate", "attendancestartdate"),

    /**
     * 当前补卡日期对应的考勤周期截止时间
     */
    attendanceEndDate("attendanceEndDate", "attendanceenddate"),

    /**
     * 打卡规则对应的班次的所有的时刻信息
     */
    punchConfigClassItemInfo("punchConfigClassItemInfo", "punchconfigclassiteminfo"),

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    actualPunchTime("actualPunchTime", "actualpunchtime"),

    /**
     * 补卡后的时间(根据时刻时间来补)
     * 注意  correctPunchTime和reissueCardDayId可以不是同一天，如果打卡规则跨天，reissueCardDayId是异常考勤中的dayId,而correctPunchTime可以是第二天的下班时间
     */
    correctPunchTime("correctPunchTime", "correctpunchtime"),

    /**
     * 请假/外勤/补卡是否被成功撤销(一个申请单被成功撤回后，这个申请单其实已经没有任何作用了)
     */
    isRevoke("isRevoke", "isrevoke"),

    /**
     * 用户考勤单据确认周期监控  取消的单据
     */
    terminatedReason("terminatedReason","terminatedreason"),

    /**
     * 实际最早打卡时间
     */
    earlyPunchTime("earlyPunchTime", "earlypunchtime"),

    /**
     * 实际最晚打卡时间
     */
    latePunchTime("latePunchTime", "latepunchtime"),

    /**
     * 实际出勤时长
     */
    actualAttendanceTime("actualAttendanceTime", "actualattendancetime"),

    /**
     * 实际工作时长
     */
    actualWorkingHours("actualWorkingHours", "actualworkinghours"),

    /**
     * 更新后的实际出勤时长
     */
    newActualAttendanceTime("newActualAttendanceTime", "newactualattendancetime"),

    /**
     * 更新后的实际工作时长
     */
    newActualWorkingHours("newActualWorkingHours", "newactualworkinghours"),

    /**
     * 异常类型
     */
    abnormalType("abnormalType", "abnormaltype"),

    /**
     * 异常日期
     */
    abnormalDate("abnormalDate", "abnormaldate"),


    ;
    private String code;

    private String lowerCode;


    HrAttendanceApplicationFormAttrKeyEnum(String code, String lowerCode) {
        this.code = code;
        this.lowerCode = lowerCode;
    }

    public static HrAttendanceApplicationFormAttrKeyEnum getInstance(String code) {
        for (HrAttendanceApplicationFormAttrKeyEnum value : HrAttendanceApplicationFormAttrKeyEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static HrAttendanceApplicationFormAttrKeyEnum getInstanceByLowerCode(String lowerCode) {
        for (HrAttendanceApplicationFormAttrKeyEnum value : HrAttendanceApplicationFormAttrKeyEnum.values()) {
            if (value.getLowerCode().equalsIgnoreCase(lowerCode)) {
                return value;
            }
        }
        return null;
    }
}
