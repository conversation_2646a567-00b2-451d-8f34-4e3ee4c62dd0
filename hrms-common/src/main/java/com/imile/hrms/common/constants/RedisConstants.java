package com.imile.hrms.common.constants;

/**
 * 缓存常量
 *
 * <AUTHOR>
 * @Company 杭州艾麦科技有限公司
 * @className: RedisConstants.java
 * @date 2017年9月18日 下午5:51:16
 */
public class RedisConstants {
    private RedisConstants(){}
    /**
     * 缓存对象版本
     */
    public static final String CACHE_VERSION = "1";

    public static final int REDIS_DB_INDEX = 15;

    public static int getRedisDB() {
        return REDIS_DB_INDEX;
    }

    /**
     * redis 过期时间设置 (测试阶段设置为10秒，上线时候更新)
     */
    public static Long REDIS_EXPIRE_SECONDS_TIME = 900L;
    /**
     * 加盟商账单导入时，运单号过期时间(单位：秒)
     */
    public static final Long HRMS_VENDOR_BILL_WAYBILLNOS_TIME = 300L;

    /**
     * redis 一天 过期时间
     */
    public static final Long REDIS_DAY_EXPIRE_SECONDS_TIME = 60L * 60L * 24;

    /**
     * REDIS存储
     */
    public static class RedisPrefix {
        private RedisPrefix(){}
        //当前用户
        public static final String CUR_USER_INFO_PREFIX = "hrms:LOGIN:%s";
        //当前语言前缀
        public static final String CUR_LOCALE_INF_PREFIX = "hrms:SYS_LANGUAGE_";
        //groovy配置
        public static final String HRMS_GROOVY_CONFIG_KEY = "hrms:GROOVYCONFIG:";
        //VAT配置
        public static final String HRMS_VAT_CONFIG_KEY = "hrms:VATCONFIG:";
        public static final String COUNTRY_API_CONFIG = "hrms:COUNTRYCONFIG:";
        public static final String HRMS_BILL_CHANGE_CYCLE_DATE = "hrms:BILLCHANGECYCLEDATE:";

        public static final String HRMS_CLIENT_EXCHANGE_RATE = "hrms:CLIENTEXCHANGERATE:";
        public static final String HRMS_ORDER_BASE_KEY = "hrms:ORDERBASEINSERTORUPDATEKEY:";
        public static final String HRMS_ORDERCANCELOPTMEASURE_KEY = "hrms:ORDERCANCELOPTMEASUREKEY:";
        public static final String HRMS_CALCULATEMEASURE_KEY = "hrms:CALCULATEMEASUREKEY:";


        public static final String HRMS_CLIENT_FEE_CONFIG_KEY = "hrms:CLIENTFEECONFIGKEY:";
        public static final String HRMS_CLIENT_FEE_CONFIG_QUERY_KEY = "hrms:CLIENTFEECONFIGQUERYKEY:";

        public static final String HRMS_CONSIGNEE_FEE_CONFIG_QUERY_KEY = "hrms:CONSIGNEEFEECONFIGQUERYKEY:";


        public static final String HRMS_COST_FEE_CONFIG_KEY = "hrms:COSTFEECONFIGKEY:%s";


        public static final String HRMS_CLIENT_OTHER_FEE_CONFIG_KEY = "hrms:CLIENTOTHERFEECONFIGKEY:";
        public static final String HRMS_COST_OTHER_FEE_CONFIG_KEY = "hrms:COSTOTHERFEECONFIGKEY:";


        public static final String HRMS_TIME_ZONE_KEY = "hrms:TIMEZONEKEY:";
        public static final String HRMS_ORDER_BASE_IS_REMOTE_KEY = "hrms:ORDERBASEISREMOTEKEY:";
        public static final String HRMS_RULE_TYPE_KEY = "hrms:RULETYPEKEY:";
        public static final String HRMS_CLIENT_BILLING_REPEAT_KEY = "hrms:CLIENTBILLINGREPEATKEY:";
        public static final String HRMS_CLIENT_FEE_BILLING_RECORD_KEY = "hrms:CLIENTFEEBILLINGRECORD:%s:%s:%s";


        public static final String HRMS_CLIENT_IS_VAT_CONF_KEY = "hrms:CLIENTISVATCONFKEY:";
        public static final String HRMS_CHECK_FEE_CONFIG = "hrms:CHECK:FEE:CONFIG:";

        public static final String HRMS_CHECK_CONSIGNEEFEE_CONFIG = "hrms:CHECK:CONSIGNEEFEE:CONFIG:";


        public static final String HRMS_CHECK_BILL_CONFIG = "hrms:CHECK:BILL:CONFIG:";

        public static final String HRMS_CHECK_IMPORT_VAT_CONFIG = "hrms:CHECK:IMPORT:VAT:CONFIG:";

        public static final String HRMS_SNAPSHOT_CLIENT = "hrms:SNAPSHOTKEY:";

        public static final String HRMS_TIMEZONE_MAP = "hrms:TIMEZONEMAP:";
        public static final String HRMS_CURRENCY_MAP = "hrms:CURRENCYMAP:";

        // 初始化财务账户
        public static final String CLIENT_KEY = "clientInitMerchant";
        public static final String VENDOR_KEY = "vendorInitMerchant";

        /**
         * 加盟商账单导入时，运单号key
         */
        public static final String HRMS_VENDOR_BILL_WAYBILLNOS = "hrms:VENDOR_BILL:WAYBILLNOS:";

        public static final String REPEATED_LOCK_KEY = "hrms:REPEATED:LOCK:%s:%s";

        /**
         * 防重提交key
         */
        public static final String REPEATED_SUBMIT_KEY = "hrms:REPEATED:SUBMIT:KEY:%s:%s";

        /**
         * 提货费已免单次数
         */
        public static final String CLIENT_PICK_UP_FEE_COUNT = "hrms:CLIENT:BILL:PICKUP:FEE:COUNT:%s:%s:%s:%s:%s";

        /**
         * 提货费已免单次数
         */
        public static final String CLIENT_PICK_UP_COST_FEE_COUNT = "hrms:CLIENT:BILL:PICKUP:COST:FEE:COUNT:%s:%s:%s:%s:%s";

        /**
         * 提货费已免单次数删除
         */
        public static final String CLIENT_PICK_UP_FEE_COUNT_DELETE = "hrms:CLIENT:BILL:PICKUP:FEE:COUNT:%s:%s:%s";

        /**
         * 全局参数配置
         */
        public static final String HRMS_GLOBAL_SWITCH_CONFIG = "hrms:GLOBAL:SWITCH:CONFIG:";

        /**
         * xxljob模拟登录用户
         */
        public static final String XXLJOB_AUDIT_LOGIN_USER = "hrms:XXLJOB:AUDIT:LOGIN:USER";

        /**
         * 黑名单封禁信息
         */
        public static final String BLACKLIST_INFO_USER = "hrms:BLACKLIST:INFO:USER:";

        /**
         * 薪资审批状态变更信息
         */
        public static final String SALARY_FORM_STATUS_UPDATE_INFO = "hrms:SALARY:FORM:STATUS:UPDATE:INFO";

        /**
         * 人脸特征全量缓存信息
         */
        public static final String FACE_FEATURE_FULL_INFO = "recognition:FACE:FEATURE:FULL:INFO";

        /**
         * 雪花id自增key
         */
        public static final String HRMS_IDWORK_WORK_ID = "hrms:IDWORK:WORK:ID:";
    }

}
