package com.imile.hrms.common.entity;

import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/2
 */
public class BaseDOUtil {
    /**
     * 填充DO公共值
     *
     * @param baseDO
     */
    public static void fillDOUpdate(BaseDO baseDO) {
        baseDO.setLastUpdDate(new Date());
        baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
        baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
    }

    /**
     * 填充DO公共值
     *
     * @param baseDO
     */
    public static void fillDOInsert(BaseDO baseDO) {
        baseDO.setCreateDate(new Date());
        baseDO.setCreateUserCode(RequestInfoHolder.getUserCode());
        baseDO.setCreateUserName(RequestInfoHolder.getUserName());
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
        baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }

    public static void fillDOUpdateFromUCenter(BaseDO baseDO) {
        baseDO.setLastUpdDate(new Date());
        UserInfoDTO userInfo = UcenterUtils.getUserInfo();
        if (userInfo != null) {
            baseDO.setLastUpdUserCode(userInfo.getUserCode());
            baseDO.setLastUpdUserName(userInfo.getUserName());
        } else {
            // 兜底，避免userInfo为null的情况
            baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
        }


    }

    /**
     * 填充DO公共值
     *
     * @param baseDO
     */
    public static void fillDOInsertFromUCenter(BaseDO baseDO) {
        UserInfoDTO userInfo = UcenterUtils.getUserInfo();
        if (userInfo != null) {
            baseDO.setCreateUserCode(userInfo.getUserCode());
            baseDO.setCreateUserName(userInfo.getUserName());
            baseDO.setLastUpdUserCode(userInfo.getUserCode());
            baseDO.setLastUpdUserName(userInfo.getUserName());
        } else {
            // 兜底，避免userInfo为null的情况
            baseDO.setCreateUserCode(RequestInfoHolder.getUserCode());
            baseDO.setCreateUserName(RequestInfoHolder.getUserName());
            baseDO.setLastUpdUserCode(RequestInfoHolder.getUserCode());
            baseDO.setLastUpdUserName(RequestInfoHolder.getUserName());
        }
        baseDO.setCreateDate(new Date());
        baseDO.setLastUpdDate(baseDO.getCreateDate());
        baseDO.setIsDelete(IsDeleteEnum.NO.getCode());
        baseDO.setRecordVersion(1L);
    }
}
