package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/8 15:40
 * @version: 1.0
 */
@Getter
public enum SalaryItemTyepNewEnum {
    SALARY_ITEM("SALARY_ITEM", "薪资项目", "salary item"),
    ATTENDANCE_ITEM("ATTENDANCE_ITEM", "考勤项目", "attendance item"),

    ;

    private String code;

    private String desc;

    private String descEn;


    SalaryItemTyepNewEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static SalaryItemTyepNewEnum getInstance(String code) {
        for (SalaryItemTyepNewEnum value : SalaryItemTyepNewEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
