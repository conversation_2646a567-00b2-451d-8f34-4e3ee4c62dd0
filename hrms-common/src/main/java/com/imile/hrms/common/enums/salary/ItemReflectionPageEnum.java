package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/10 14:17
 * @version: 1.0
 */
@Getter
public enum ItemReflectionPageEnum {

    REQUIRE_CONFIGURATION_PAGE("REQUIRE_CONFIGURATION_PAGE", "待配置页面"),
    ACTIVE_PAGE("ACTIVE_PAGE", "启用页面"),
    DISABLED_PAGE("DISABLED_PAGE", "停用页面"),

    ;

    private String code;

    private String desc;

    private String descEn;


    ItemReflectionPageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ItemReflectionPageEnum getInstance(String code) {
        for (ItemReflectionPageEnum value : ItemReflectionPageEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
