package com.imile.hrms.common.enums;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Getter
public enum OperationTypeEnum {



    ROLE_SET("ROLE_SET","管理员设置",OperationCodeEnum.ACCOUNT_MANAGE),
    ACCOUNT_SWITCH_STATUS("ACCOUNT_SWITCH_STATUS","账号启用状态切换",OperationCodeEnum.ACCOUNT_MANAGE),
    SUBSIDIARY_ADD("SUBSIDIARY_ADD","新增子公司",OperationCodeEnum.SUBSIDIARY_MANAGE),
    SUBSIDIARY_UPDATE("SU<PERSON><PERSON>IARY_UPDATE","编辑子公司",OperationCodeEnum.SUBSIDIARY_MANAGE),
    SUBSIDIARY_SWITCH_STATUS("SUBSIDIARY_SWITCH_STATUS","子公司启用状态切换",OperationCodeEnum.SUBSIDIARY_MANAGE),
    DEPT_ADD("DEPT_ADD","新增部门",OperationCodeEnum.DEPT_MANAGE),
    DEPT_UPDATE("DEPT_UPDATE","编辑部门",OperationCodeEnum.DEPT_MANAGE),
    DEPT_SWITCH_STATUS("DEPT_SWITCH_STATUS","部门启用状态切换",OperationCodeEnum.DEPT_MANAGE),
    POST_ADD("POST_ADD","新增岗位",OperationCodeEnum.POST_MANAGE),
    POST_UPDATE("POST_UPDATE","编辑岗位",OperationCodeEnum.POST_MANAGE),
    POST_SWITCH_STATUS("POST_SWITCH_STATUS","岗位启用状态切换",OperationCodeEnum.POST_MANAGE),
    GRADE_ADD("GRADE_ADD","新增职级",OperationCodeEnum.GRADE_MANAGE),
    GRADE_UPDATE("GRADE_UPDATE","编辑职级",OperationCodeEnum.GRADE_MANAGE),
    GRADE_SWITCH_STATUS("GRADE_SWITCH_STATUS","职级启用状态切换",OperationCodeEnum.GRADE_MANAGE),
    EMPLOYEE_IMPORT("EMPLOYEE_IMPORT","员工导入",OperationCodeEnum.EMPLOYEE_MANAGE),
    INVITE_SEND("INVITE_SEND","发送邀请",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ENTRY_CONFIRM("ENTRY_CONFIRM","确认入职",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    REJECT_ENTRY("REJECT_ENTRY","驳回入职",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ENTRY_ADD("ENTRY_ADD","入职新增",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ENTRY_UPDATE("ENTRY_UPDATE","更新入职记录",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ENTRY_IMPORT("ENTRY_IMPORT","员工入职导入",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_ENTRY_INFO_UPDATE("EMPLOYEE_ENTRY_INFO_UPDATE","员工入职信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_BASE_INFO_UPDATE("EMPLOYEE_BASE_INFO_UPDATE","员工基本信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    USER_INFO_UPDATE("USER_INFO_UPDATE","人员信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_PERSONAL_INFO_UPDATE("EMPLOYEE_PERSON_INFO_UPDATE","员工私人信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_CERTIFICATE_INFO_UPDATE("EMPLOYEE_CERTIFICATE_INFO_UPDATE","员工证件信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_CERTIFICATE_INFO_ADD("EMPLOYEE_CERTIFICATE_INFO_ADD","员工证件信息新增",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_EDUCATION_INFO_UPDATE("EMPLOYEE_EDUCATION_INFO_UPDATE","员工学历信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_LABOUR_INFO_UPDATE("EMPLOYEE_LABOUR_INFO_UPDATE","员工合同信息编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_DEPT_AND_OC_UPDATE("EMPLOYEE_DEPT_AND_OC_UPDATE","员工所属部门及网点变更",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_IMPORT_UPDATE("EMPLOYEE_IMPORT_UPDATE","员工导入更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    USER_TRANSFORM_UPDATE("USER_TRANSFORM_UPDATE","员工调动更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    DRIVER_SWITCH_TRANSFORM_UPDATE("DRIVER_SWITCH_TRANSFORM_UPDATE","司机切换调动更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ORG_ADJUST_TRANSFORM_IMPORT_UPDATE("ORG_ADJUST_TRANSFORM_IMPORT_UPDATE","组织调整调动导入更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    SPECIAL_TRANSFORM_IMPORT_UPDATE("SPECIAL_TRANSFORM_IMPORT_UPDATE","特殊调动导入更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    USER_PROMOTION_UPDATE("USER_PROMOTION_UPDATE","员工晋升更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    EMPLOYEE_OUTSOURCING_IMPORT_UPDATE("EMPLOYEE_OUTSOURCING_IMPORT_UPDATE","外包员工导入更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    DRIVER_OUTSOURCING_IMPORT_UPDATE("DRIVER_OUTSOURCING_IMPORT_UPDATE","外包司机导入更新",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    TRANSFER_ADD("TRANSFER_ADD","员工调动",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    DIMMISION_ADD("DIMMISION_ADD","离职办理",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    DIMMISION_UPDATE("DIMMISION_UPDATE","离职编辑",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ENTRY_AGAIN("ENTRY_AGAIN","二次入职",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    DIMMISION_CONFIRM("DIMMISION_CONFIRM","离职确认",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    DIMMISION_CANCEL("DIMMISION_CANCEL","离职取消",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    ATTENDANCE_CONFIG_ADD("ATTENDANCE_CONFIG_ADD","新增考勤配置",OperationCodeEnum.ATTENDANCE_CONFIG_MANAGE),
    ATTENDANCE_CONFIG_SWITCH_STATUS("ATTENDANCE_CONFIG_SWITCH_STATUS","考勤配置启用状态切换",OperationCodeEnum.ATTENDANCE_CONFIG_MANAGE),
    ATTENDANCE_CONFIG_UPDATE("ATTENDANCE_CONFIG_UPDATE","编辑考勤配置",OperationCodeEnum.ATTENDANCE_CONFIG_MANAGE),
    ATTENDANCE_EMPLOYEE_IMPORT("ATTENDANCE_EMPLOYEE_IMPORT","导入员工考勤",OperationCodeEnum.EMPLOYEE_ATTENDANCE_MANAGE),
    ATTENDANCE_EMPLOYEE_UPDATE("ATTENDANCE_EMPLOYEE_UPDATE","员工考勤编辑",OperationCodeEnum.EMPLOYEE_ATTENDANCE_MANAGE),
    SALARY_CONFIG_ADD("SALARY_CONFIG_ADD","计薪方案新增",OperationCodeEnum.SALARY_CONFIG_MANAGE),
    SALARY_CONFIG_SWITCH_STATUS("SALARY_CONFIG_SWITCH_STATUS","计薪方案启用状态切换",OperationCodeEnum.SALARY_CONFIG_MANAGE),
    SALARY_CONFIG_UPDATE("SALARY_CONFIG_UPDATE","编辑计薪方案",OperationCodeEnum.SALARY_CONFIG_MANAGE),
    SOCIAL_CONFIG_ADD("SOCIAL_CONFIG_ADD","社保方案新增",OperationCodeEnum.WELFARE_MANAGE),
    SOCIAL_CONFIG_SWITCH_STATUS("SOCIAL_CONFIG_SWITCH_STATUS","社保方案状态切换",OperationCodeEnum.WELFARE_MANAGE),
    SOCIAL_CONFIG_DEFAULT_SET("SOCIAL_CONFIG_DEFAULT_SET","社保方案默认切换",OperationCodeEnum.WELFARE_MANAGE),
    SOCIAL_CONFIG_UPDATE("SOCIAL_CONFIG_UPDATE","编辑社保方案",OperationCodeEnum.WELFARE_MANAGE),
    ACCUMULATION_CONFIG_ADD("ACCUMULATION_CONFIG_ADD","公积金方案新增",OperationCodeEnum.WELFARE_MANAGE),
    ACCUMULATION_CONFIG_SWITCH_STATUS("ACCUMULATION_CONFIG_SWITCH_STATUS","公积金方案状态切换",OperationCodeEnum.WELFARE_MANAGE),
    ACCUMULATION_CONFIG_DEFAULT_SET("ACCUMULATION_CONFIG_DEFAULT_SET","公积金方案默认切换",OperationCodeEnum.WELFARE_MANAGE),
    ACCUMULATION_CONFIG_UPDATE("ACCUMULATION_CONFIG_UPDATE","编辑公积金方案",OperationCodeEnum.WELFARE_MANAGE),
    SALARY_EMPLOYEE_CONFIG_ADD("SALARY_EMPLOYEE_CONFIG_ADD","新增薪酬配置",OperationCodeEnum.SALARY_EMPLOYEE_CONFIG_MANAGE),
    SALARY_EMPLOYEE_CONFIG_UPDATE("SALARY_EMPLOYEE_CONFIG_UPDATE","薪酬配置编辑",OperationCodeEnum.SALARY_EMPLOYEE_CONFIG_MANAGE),
    SALARY_UPLOAD_ITEM_UPDATE("SALARY_UPLOAD_ITEM_UPDATE","上传项编辑",OperationCodeEnum.SALARY_UPLOAD_ITEM_MANAGE),
    SALARY_UPLOAD_ITEM_IMPORT("SALARY_UPLOAD_ITEM_IMPORT","上传项编辑",OperationCodeEnum.SALARY_UPLOAD_ITEM_MANAGE),
    VENDOR_EDIT_DRIVER("VENDOR_EDIT_DRIVER","供应商编辑司机信息",OperationCodeEnum.VENDOR_DRIVER_MANAGE),
    AUDIT_DRIVER_INFO("AUDIT_DRIVER_INFO","审核司机信息",OperationCodeEnum.VENDOR_DRIVER_MANAGE),
    DRIVER_CONVERT_EMPLOYEE("DRIVER_CONVERT_EMPLOYEE","司机账号转员工账号",OperationCodeEnum.HUMAN_RESOURCE_MANAGE),
    VENDOR_EDIT_EMPLOYEE("VENDOR_EDIT_EMPLOYEE","供应商编辑员工信息",OperationCodeEnum.VENDOR_EMPLOYEE_MANAGE),
    DRIVER_LEVEL_UPDATE("DRIVER_LEVEL_UPDATE","司机等级编辑",OperationCodeEnum.DRIVER_LEVEL),
    DEPT_BIZ_AREA("DEPT_BIZ_AREA","部门业务领域编辑",OperationCodeEnum.DEPT_BIZ_AREA),
    // 招聘
    COOPERATE("COOPERATE","Cooperate",OperationCodeEnum.RECRUITMENT_MANAGE_JOB),
    SUSPEND_POSITION_HRMS("SUSPEND_POSITION_HRMS","Suspend Position",OperationCodeEnum.RECRUITMENT_MANAGE_JOB),
    CANCEL_POSITION_HRMS("CANCEL_POSITION_HRMS","Cancel Position",OperationCodeEnum.RECRUITMENT_MANAGE_JOB),
    INITIATE_JOB_OFFER_APPROVAL("INITIATE_JOB_OFFER_APPROVAL","Initiate Job Offer Approval",OperationCodeEnum.RECRUITMENT_MANAGE_JOB),
    RECOVER_POSITION_HRMS("RECOVER_POSITION_HRMS","Recover Position",OperationCodeEnum.RECRUITMENT_MANAGE_JOB),
    WITHDRAW_JOB_CLOVER("WITHDRAW_JOB_CLOVER","Withdraw",OperationCodeEnum.RECRUITMENT_JOB_APPROVAL),
    SUSPEND_POSITION_CLOVER("SUSPEND_POSITION_CLOVER","Suspend Position",OperationCodeEnum.RECRUITMENT_JOB_APPROVAL),
    CANCEL_POSITION_CLOVER("CANCEL_POSITION_CLOVER","Cancel Position",OperationCodeEnum.RECRUITMENT_JOB_APPROVAL),
    RECOVER_POSITION_CLOVER("RECOVER_POSITION_CLOVER","Recover Position",OperationCodeEnum.RECRUITMENT_JOB_APPROVAL),
    WITHDRAW_OFFER("WITHDRAW_OFFER","Withdraw",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    ACCEPT_OFFER("ACCEPT_OFFER","Accept Offer",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    CANCEL_OFFER("CANCEL_OFFER","Cancel Offer",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    REJECT_OFFER("REJECT_OFFER","Reject Offer",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    REFERENCE_CHECK_PROFILE_UPLOAD("REFERENCE_CHECK_PROFILE_UPLOAD","Upload Reference Check Profile",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    CREATE_FEISHU_JOB("CREATE_FEISHU_JOB","Create Feishu Job",OperationCodeEnum.RECRUITMENT_JOB_FEISHU),
    UPDATE_FEISHU_JOB("UPDATE_FEISHU_JOB","Update Feishu Job",OperationCodeEnum.RECRUITMENT_JOB_FEISHU),
    CLOSE_FEISHU_JOB("CLOSE_FEISHU_JOB","Close Feishu Job",OperationCodeEnum.RECRUITMENT_JOB_FEISHU),
    // 入职管理-更新offer状态
    CANCEL_ONBOARDING_OFFER("CANCEL_ONBOARDING_OFFER","Cancel Onboarding",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    OFFER_INVITE_SEND("OFFER_INVITE_SEND","发送Offer邀请",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    EDIT_EXPECTED_JOINING_DATE("EDIT_EXPECTED_JOINING_DATE","Edit Expected Joining Date",OperationCodeEnum.RECRUITMENT_MANAGE_OFFER),
    ;
    private String code;

    private String desc;

    private OperationCodeEnum operationCodeEnum;


    OperationTypeEnum(String code, String desc,OperationCodeEnum operationCodeEnum) {
        this.code = code;
        this.desc = desc;
        this.operationCodeEnum = operationCodeEnum;
    }

    private static final Map<String, OperationTypeEnum> cacheMap = new ConcurrentHashMap<>();

    public static OperationTypeEnum getOperationType(String code) {
        return code == null ? null : cacheMap.get(code);
    }

    static {
        OperationTypeEnum[] attributes = values();
        int var1 = attributes.length;

        for (int index = 0; index < var1; ++index) {
            OperationTypeEnum codeEnum = attributes[index];
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }

    }


}
