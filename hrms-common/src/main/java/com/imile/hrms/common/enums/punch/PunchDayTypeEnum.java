package com.imile.hrms.common.enums.punch;

import com.imile.hrms.common.enums.attendance.AttendanceConcreteTypeEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 打卡排班的特殊情况，OFF和PH  即不属于排班，又不属于假期
 */
@Getter
public enum PunchDayTypeEnum {

    OFF("OFF", "休息日", "OFF"),
    PH("PH", "节假日", "PH"),
    /*AL("AL", "年假", "AL"),
    ML("ML", "医疗假", "ML"),
    UL("UL", "事假", "UL"),*/
    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 描述
     */
    private String descEn;

    PunchDayTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static PunchDayTypeEnum getInstanceByCode(String code) {
        for (PunchDayTypeEnum value : PunchDayTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
