package com.imile.hrms.common.enums.attendance;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Getter
public enum ImportTypeEnum {

    ATTENDANCE("ATTENDANCE", "出勤"),
    OVERTIME("OVERTIME", "加班"),
    ;

    private String code;

    private String desc;

    private static final Map<String, ImportTypeEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (ImportTypeEnum codeEnum : values()) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }
    }

    ImportTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImportTypeEnum parserEnum(String code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
