package com.imile.hrms.common.util.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * 金额格式化
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2020/04/13 19:33
 */
public class BigDecimal4Serialize extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jgen,
                          SerializerProvider provider) throws IOException {
        if (bigDecimal == null) {
            return;
        }
        BigDecimal bigDecimalNew = bigDecimal.setScale(4, BigDecimal.ROUND_HALF_UP);
        jgen.writeString(bigDecimalNew.stripTrailingZeros().toPlainString());
    }
}
