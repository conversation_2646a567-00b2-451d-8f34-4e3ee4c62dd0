package com.imile.hrms.common.enums.freelancer;

import lombok.Getter;

@Getter
public enum FreelancerBasicInfoEnum {
    /**
     * 国家
     */
    country("country", "国家"),
    /**
     * 省
     */
    state("state", "省"),
    /**
     * 市
     */
    city("city", "市"),
    /**
     * 详细地址
     */
    detailAddress("detailAddress", "详细地址"),

    /**
     * 邮编
     */
    postCode("postCode", "邮编"),

    /**
     * policeCheck文件地址
     */
    policeCheckFileUrlList("policeCheckFileUrlList","policeCheck文件地址"),

    /**
     * workAuthorization文件地址
     */
    workAuthorizationFileUrlList("workAuthorizationFileUrlList","workAuthorization文件地址workAuthorization文件地址"),

    /**
     *  driver license Type Australian/Overseas
     */
    licenseType("licenseType","driver license Type Australian/Overseas"),

    /**
     *  driver license Classification  Automatic/manual
     */
    licenseClassification("licenseClassification","driver license Classification  Automatic/manual"),

    /**
     * 签名是否完成
     */
    isAutograph("isAutograph", "签名是否完成"),

    /**
     * 签名
     */
    autograph("autograph", "签名"),

    /**
     * 签名时间
     */
    autographSignOnDate("autographSignOnDate", "签名时间"),

    /**
     * 签名
     */
    age("age", "年龄"),

    /**
     * 邮编id
     */
    zipCodeList("zipCodeList", "邮编id"),

    /**
     * CPF NUMBER
     */
     cpfNumber("cpfNumber", "CPF NUMBER"),

    /**
     * CNPJ NUMBER
     */
    cnpjNumber("cnpjNumber", "CNPJ NUMBER"),

    /**
     * CPF证件信息
     */
     cpfCertificatePath("cpfCertificatePath", "CPF证件信息"),

    /**
     * CNPJ证件信息
     */
    cnpjCertificatePath("cnpjCertificatePath", "CNPJ证件信息"),

    /**
     * 职能
     */
    functional("functional", "职能"),

    /**
     * 上级
     */
    leaderId("leaderId", "上级"),

    /**
     * 上级
     */
    leaderCode("leaderCode", "上级"),

    /**
     * 工作时段 开始
     */
    workingStartTime("workingStartTime", "工作时段 开始"),

    /**
     * 工作时段 结束
     */
    workingEndTime("workingEndTime", "工作时段 结束"),

    /**
     * 设备id
     */
    deviceId("deviceId", "设备id"),

    /**
     * 车牌号
     */
    vehicleNumber("vehicleNumber", "车牌号"),

    /**
     * IE
     */
    ie("ie", "IE"),

    /**
     * 税务ID
     */
    taxId("taxId", "税务ID"),

    /**
     * 税务ID来源
     */
    taxIdSource("taxIdSource", "税务ID来源"),

    /**
     * 税务ID来源
     */
    workExperience("workExperience", "工作经验 1：三年以下，2：三年-5年，3：五年以上"),

    /**
     * 公司名称
     */
    companyName("companyName", "公司名称"),

    /**
     * EAR 1：yes / 0：no
     */
    ear("ear", "EAR 1：yes / 0：no"),

    /**
     * 驾驶证等级
     */
    driverLicenseLevel("driverLicenseLevel", "驾驶证等级"),

    /**
     * 开户银行/银行编码
     */
    accountBankName("accountBankName", "开户银行/银行编码"),

    /**
     * 开户支行
     */
    agency("agency", "开户支行"),

    /**
     * 银行账号
     */
    accountNumber("accountNumber", "银行账号"),

    /**
     * 电子银行 1:yes / 0:no
     */
    digitalBank("digitalBank", "电子银行 1:yes / 0:no"),

    /**
     * 车辆行驶证信息，JSON
     */
    vehicleInfoList("vehicleInfoList", "车辆行驶证信息，JSON"),

    /**
     * 自主注册派送偏好信息，JSON
     */
    autoDeliveryPreferenceList("autoDeliveryPreferenceList", "自主注册派送偏好信息，JSON"),

    /**
     * 背景审查资料信息，JSON
     */
    freelancerVerificationInfo("freelancerVerificationInfo", "背景审查资料信息，JSON"),

    /**
     * 附件信息列表，JSON
     */
    attachmentList("attachmentList", "附件信息列表，JSON"),

    /**
     * cpf文件信息
     */
    cpfDocument("cpfDocument", "cpf文件信息，JSON"),

    /**
     * cnpj文件信息
     */
    cnpjDocument("cnpjDocument", "cnpj文件信息，JSON"),

    /**
     * 驾驶证证件信息
     */
    driverLicenseDocument("driverLicenseDocument", "驾驶证证件信息，JSON"),

    ;
    private String code;

    private String desc;


    FreelancerBasicInfoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FreelancerBasicInfoEnum getInstance(String code) {
        for (FreelancerBasicInfoEnum value : FreelancerBasicInfoEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

}
