package com.imile.hrms.common.config;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
@EnableApolloConfig(order = 1)
public class ApolloConfiguration {
    @Autowired
    private RefreshScope refreshScope;
    @Autowired
    private HrmsProperties hrmsProperties;
    @Autowired
    private FaceConfiguration faceConfiguration;

    @ApolloConfigChangeListener(interestedKeyPrefixes = {"imile.hrms","arcface."})
    public void onChange(ConfigChangeEvent changeEvent) {
        log.warn("before refresh {}", JSON.toJSONString(hrmsProperties));
        refreshScope.refresh("hrmsProperties");
        refreshScope.refresh("faceConfiguration");
        log.warn("after refresh {}", JSON.toJSONString(hrmsProperties));
    }
}
