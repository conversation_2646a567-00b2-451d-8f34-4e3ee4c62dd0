package com.imile.hrms.common.enums.salary;

import lombok.Getter;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/6 20:39
 * @version: 1.0
 */
@Getter
public enum SalaryTaskTypeEnum {

    ATTENDANCE_REPORT("ATTENDANCE_REPORT", "考勤报表自动生成", "attendance report"),
    CALCULATE_RULE("CALCULATE_RULE", "薪资计算任务自动生成", "calculate task"),

    ;

    private String code;

    private String desc;

    private String descEn;


    SalaryTaskTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static SalaryTaskTypeEnum getInstance(String code) {
        for (SalaryTaskTypeEnum value : SalaryTaskTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
