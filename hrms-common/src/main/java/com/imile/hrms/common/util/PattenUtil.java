package com.imile.hrms.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/4
 */
public class PattenUtil {
    private PattenUtil() {
    }

    private static final String DATE_FORMAT = "^\\d{4}/\\d{1,2}/\\d{1,2}";

    /**
     * 时间格式匹配
     * @param value
     * @param pattern
     * @return
     */
    public static boolean pattern(String value, String pattern) {
        if (StringUtils.isEmpty(value)) {
            return Boolean.FALSE;
        }

        Pattern compile = Pattern.compile(pattern);
        Matcher matcher = compile.matcher(value);
        return matcher.matches();
    }

    /**
     * 时间格式匹配 默认
     * @param value
     * @return
     */
    public static boolean pattern(String value) {
        return pattern(value, DATE_FORMAT);
    }

}
