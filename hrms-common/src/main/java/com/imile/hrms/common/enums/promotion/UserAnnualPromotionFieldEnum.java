package com.imile.hrms.common.enums.promotion;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Getter
public enum UserAnnualPromotionFieldEnum {
    FIRST_DEPT_ID("firstDeptId", "一级部门ID"),
    APPLY_CANDIDATE_NUMBER("applyCandidateNumber", "本次晋升人数"),
    FIRST_DEPT_USER_COUNT("firstDeptUserCount", "部门总人数"),
    APPLY_RATIO("applyRatio", "提报比例"),
    // 候选人列表（列表字段开始）
    USER_ID("userId", "实际申请人"),
    USER_NAME("userName", "姓名"),
    USER_CODE("userCode", "账号"),
    DEPT_ID("deptId", "部门"),
    AGE("age", "年龄"),
    SEX("sex", "性别"),
    HIGHEST_EDUCATION_LEVEL("highestEducationLevel", "最高学历"),
    LOCATION_COUNTRY("locationCountry", "常驻国"),
    SENIORITY("seniority", "司龄（月）"),
    PERFORMANCE_RESULT("performanceResult", "绩效结果"),
    LAST_PROMOTION_DATE("lastPromotionDate", "上一次晋升日期"),

    POST_ID("postId", "岗位ID"),
    JOB_SEQUENCE("jobSequence", "职级序列"),
    JOB_LEVEL("jobLevel", "职级"),
    JOB_GRADE("jobGrade", "职等"),

    IS_DEFENSE("isDefense", "是否答辩"),
    JUDGE_USER_IDS("judgeUserIds", "评委"),
    JUDGE_OPINIONS("judgeOpinions", "评委意见"),

    PROMOTION_POST_ID("promotionPostId", "晋升岗位ID"),
    PROMOTION_JOB_SEQUENCE("promotionJobSequence", "晋升职级序列"),
    PROMOTION_JOB_LEVEL("promotionJobLevel", "晋升职级"),
    PROMOTION_JOB_GRADE("promotionJobGrade", "晋升职等"),
    PROMOTION_REASON("promotionReason", "晋升原因"),

    REVIEW_RESULT("reviewResult", "评议结果"),
    // 候选人列表（列表字段结束）

    PROMOTION_FILES("promotionFileList", "晋升附件"),
    REVIEWER_USER_CODE("reviewerUserCode", "评议人"),
    REVIEW_OPINIONS("reviewOpinions", "评议意见"),
    REVIEW_FILES("reviewFiles", "评议附件"),
    ;

    private final String key;

    private final String desc;

    UserAnnualPromotionFieldEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
