package com.imile.hrms.common.util.concurrent;


import com.imile.util.concurrent.ExecutorUtils;

import java.util.concurrent.ExecutorService;

/**
 * 线程池定义
 *
 * <AUTHOR>
 * @company 杭州艾麦科技有限公司
 * @className:
 * @date 2019/11/12 17:57
 */
public class ExecutorServiceUtils {
    /**
     * 商家账单配置变更任务
     */
    public static final ExecutorService CLIENT_BILL_CONFIG_CHANGE_JOB = ExecutorUtils.createCustomExecutorService(
        10, "ClientBillConfigChangeJob");
    /**
     * groovy线程池
     */
    public static final ExecutorService GROOVY_JOB = ExecutorUtils.createCustomExecutorService(
        2, "GroovyJob");
    /**
     * rocketMq线程池
     */
    public static final ExecutorService ROCKETMQ_JOB = ExecutorUtils.createCustomExecutorService(
        10, "RocketMqJob");
    /**
     * 账单调度任务执行的线程
     */
    public static final ExecutorService BILL_SCHEDULER_EXECUTE_JOB = ExecutorUtils.createCustomExecutorService(
        30, "SchedulerExecuteJob");

    public static final ExecutorService STATEMESSAGE_JOB = ExecutorUtils.createCustomExecutorService(
        10, "StateMessage_Job");

    public static final ExecutorService MAIL_EXCEL_JOB = ExecutorUtils.createCustomExecutorService(
        10, "MailJob");
    /**
     * 业务日志线程池
     */
    public static final ExecutorService BUSINESS_LOG = ExecutorUtils.createCustomExecutorService(
        10, "BusinessLog");

    /**
     * 账单调度任务执行的线程
     */
    public static final ExecutorService BILL_FEE_CALCULATE_JOB = ExecutorUtils.createCustomExecutorService(
        3, "BillFeeCalculateJob");

    /**
     * 账单调度任务执行的线程
     */
    public static final ExecutorService BILL_FEE_DETAIL_CALCULATE_JOB = ExecutorUtils.createCustomExecutorService(
        3, "BillFeeDetailCalculateJob");

}
