package com.imile.hrms.common.enums.leave;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} han.wang
 * {@code @className:} LeaveConfigInvalidType
 * {@code @since:} 2025-04-02 20:30
 * {@code @description: 结转失效类型枚举}
 */
@Getter
public enum LeaveConfigInvalidTypeEnum {
    FIXED_DATE(0, "固定日期", "Fixed date"),
    ANNUAL_LAPSE(1, "按年失效", "Annual lapse"),
    EXPIRES_ON_THE_DATE_OF_ENTRY(2, "按入职日期设置", "Expires on the date of entry"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigInvalidTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigInvalidTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigInvalidTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigInvalidTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }

}
