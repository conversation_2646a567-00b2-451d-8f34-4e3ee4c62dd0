package com.imile.hrms.common.enums.promotion;

import com.imile.hrms.common.context.RequestInfoHolder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/2/26
 */
@Getter
public enum PromotionContributeTypeEnum {


    TALENT_FAST(1, "新领域敢于冒险创新并取得突破成绩", "Innovate in new areas and achieving breakthrough results"),
    BLUE_COLLAR(2, "临危受命，扭转劣势", "Taken on challenging tasks in crises and turned the tide"),
    SPECIAL_ADJUSTMENT(3, "勇赴艰苦地区挑战性岗位并做出突出贡献", "Challenging positions  and made outstanding contributions"),
    ANNUAL(4, "重大项目中的骨干成员且绩效突出等", "Key member in major projects with outstanding performance");

    private final Integer type;

    private final String desc;

    private final String descEn;

    PromotionContributeTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }


    public static String getByLanguage(Integer type) {
        for (PromotionContributeTypeEnum e : PromotionContributeTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return RequestInfoHolder.isChinese() ? e.getDesc() : e.getDescEn();
            }
        }
        return "";
    }

    public static String getCn(Integer type) {
        for (PromotionContributeTypeEnum e : PromotionContributeTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e.getDesc();
            }
        }
        return "";
    }

    public static String getEn(Integer type) {
        for (PromotionContributeTypeEnum e : PromotionContributeTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e.getDescEn();
            }
        }
        return "";
    }
}
