package com.imile.hrms.common.enums.punch;

import org.apache.commons.lang3.StringUtils;

public enum AttendancePunchUpdateTypeEnum {

    /**
     * 增加班次信息
     */
    ADD_SHIFT("ADD_SHIFT", 1, ""),

    /**
     * 关联员工
     */
    RELATION_USER("RELATION_USER", 2, ""),

    /**
     * 变更排班
     */
    CHANGE_SHIFT("CHANGE_SHIFT", 3,"");


    private String code;

    private Integer priority;

    private String desc;

    AttendancePunchUpdateTypeEnum(String code, Integer priority, String desc) {
        this.code = code;
        this.priority = priority;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public Integer getPriority() {
        return priority;
    }

    public String getDesc() {
        return desc;
    }

    public static AttendancePunchUpdateTypeEnum getInstance(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AttendancePunchUpdateTypeEnum value : AttendancePunchUpdateTypeEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
