package com.imile.hrms.common.util.tree;

import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
/**
 * 树操作工具类
 */
public class TreeUtils {
    /**
     * 查找子树
     *
     * @param trees        树结构
     * @param childsMapper 子节点映射
     * @param tester       判断函数
     * @param <T>          元素类型
     * @return 子树
     */
    public static <T> T findChildTree(List<T> trees, Function<T, List<T>> childsMapper, Predicate<T> tester) {
        for (T tree : trees) {
            if (tester.test(tree)) {
                return tree;
            }
            List<T> childs = childsMapper.apply(tree);
            if (CollectionUtils.isNotEmpty(childs)) {
                T child = findChildTree(childs, childsMapper, tester);
                if (child != null) {
                    return child;
                }
            }
        }
        return null;
    }

    /**
     * 遍历树结构进行查找
     *
     * @param trees        树集合
     * @param childsMapper 子节点映射函数
     * @param tester       比较器
     * @param callback     匹配回调
     * @param <T>          元素类型
     */
    public static <T> void lookup(List<T> trees, Function<T, List<T>> childsMapper, Predicate<T> tester, Consumer<T> callback) {
        for (T tree : trees) {
            // 检查通过进行回调
            if (tester.test(tree)) {
                callback.accept(tree);
            }
            List<T> childs = childsMapper.apply(tree);
            // 存在子节点递归调用
            if (CollectionUtils.isNotEmpty(childs)) {
                lookup(childs, childsMapper, tester, callback);
            }
        }
    }

    /**
     * 集合转树结构
     *
     * @param elements        元素集合
     * @param keyMapper       唯一标识获取函数
     * @param parentKeyMapper 父节点唯一标识获取函数
     * @param nlass           返回树结构类型
     * @param callback        回调函数,tree node初始化后的回调
     * @param <E>             元素类型
     * @param <K>             唯一标识类型
     * @param <N>             tree node类型
     * @return 集合
     */
    public static <E, K, N extends BaseTree<K, N>> List<N> listToTree(
            List<E> elements,
            @Nonnull Function<E, String> titleMapper,
            Function<E, String> orgCodeMapper,
            @Nonnull Function<E, K> keyMapper,
            @Nonnull Function<E, K> parentKeyMapper,
            @Nonnull Class<N> nlass,
            @Nullable TreeCallback<E, K, N> callback) {

        if (elements == null || elements.size() == 0) {
            return new ArrayList<>();
        }
        // 设置一个空函数
        callback = callback != null ? callback : (e, n) -> {
        };

        // 以nodeId作为key进行存储
        Map<K, N> nodeMap = new HashMap<>();
        // 以parentNodeId作为key暂存tree node
        Map<K, List<N>> neeParentMap = new LinkedHashMap<>();

        try {
            for (E element : elements) {
                K nodId = keyMapper.apply(element);
                K parentNodeId = parentKeyMapper.apply(element);

                N n = nlass.newInstance();
                n.setId(nodId);
                n.setParentId(parentNodeId);
                n.setNodeName(titleMapper.apply(element));
                n.setOrganizationCode("");
                // 查找父节点
                N myParent = nodeMap.get(parentNodeId);
                // 查找子节点
                List<N> myChildren = neeParentMap.get(nodId);
                if (myParent != null) {
                    // 父亲节点已出现
                    List<N> children = myParent.getChildren();
                    children = children == null ? new ArrayList<>() : children;
                    children.add(n);
                    myParent.setChildren(children);
                } else {
                    // 等待父节点
                    List<N> childs = neeParentMap.getOrDefault(parentNodeId, new ArrayList<>());
                    childs.add(n);
                    neeParentMap.put(parentNodeId, childs);
                }

                if (myChildren != null && myChildren.size() > 0) {
                    // 找到子节点, 子节点等到了父节点, 踢出等待列表
                    neeParentMap.remove(nodId);

                    List<N> children = n.getChildren();
                    children = children == null ? new ArrayList<>() : children;
                    children.addAll(myChildren);
                    n.setChildren(children);
                }
                nodeMap.put(nodId, n);
                // 调用回调,给与调用者加工的机会
                callback.run(n, element);
            }

            // 等待列表中未能等到父节点的tree node 为根节点
            return neeParentMap.values()
                    .stream()
                    .flatMap(Collection::stream)
                    // 整理hasChildren和path
                    .peek(n -> adjust(n, null))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static <K, N extends BaseTree<K, N>> void adjust(N curr, N parent) {
        // 计算访问路径
        List<K> path = new ArrayList<>();
        path.add(curr.getParentId());
        if (parent != null) {
            path = new ArrayList<>(parent.getPath());
        }
        path.add(curr.getId());
        curr.setPath(path);

        // 判断是否还有子节点
        List<N> children = curr.getChildren();
        if (children == null || children.size() == 0) {
            curr.setHasChild(false);
            return;
        }
        curr.setHasChild(true);

        // 递归调整所有子节点
        for (N child : children) {
            adjust(child, curr);
        }
    }

    /**
     * tree node 初始化完成后的回调接口,给与使用方加工节点机会
     *
     * @param <E> 元素类型
     * @param <K> 唯一标识类型
     * @param <N> 节点类型
     */
    @FunctionalInterface
    public static interface TreeCallback<E, K, N extends BaseTree<K, N>> {
        void run(N node, E element);
    }
}
