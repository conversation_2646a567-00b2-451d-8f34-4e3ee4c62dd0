package com.imile.hrms.common.enums.leave;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigRangRangeType
 * {@code @since:} 2024-04-15 15:59
 * {@code @description:}
 */
@Getter
public enum LeaveConfigRangRangeTypeEnum {
    DEFAULT(0, "", ""),
    USER(1, "用户级别", "User level"),
    DEPT(2, "部门级别", "Dept level"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, LeaveConfigRangRangeTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (LeaveConfigRangRangeTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    LeaveConfigRangRangeTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static LeaveConfigRangRangeTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
