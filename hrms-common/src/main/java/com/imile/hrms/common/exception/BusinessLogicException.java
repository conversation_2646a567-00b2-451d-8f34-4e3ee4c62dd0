package com.imile.hrms.common.exception;

import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * henry
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class BusinessLogicException extends BusinessException {

    public static void check(boolean expression, String errorCode, String i18nCode) {
        if (!expression) {
            throw BusinessException.ofI18nCode(errorCode, i18nCode);
        }
    }

    public static void check(boolean expression, HrmsErrorCodeEnums errorCodeEnums) {
        if (!expression) {
            throw BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc());
        }
    }

    public static void checkTrue(boolean expression, String errorCode, String i18nCode) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCode, i18nCode);
        }
    }

    public static void checkTrue(boolean expression, HrmsErrorCodeEnums errorCodeEnums) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc());
        }
    }

    public static void checkTrue(boolean expression, HrmsErrorCodeEnums errorCodeEnums, Object... params) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc(), params);
        }
    }

    public static void checkTrue(boolean expression, String errorCode, String i18nCode, Object... params) {
        if (expression) {
            throw BusinessException.ofI18nCode(errorCode, i18nCode, params);
        }
    }

    public static BusinessException getException(HrmsErrorCodeEnums errorCodeEnums, Object... params) {
        return BusinessException.ofI18nCode(errorCodeEnums.getCode(), errorCodeEnums.getDesc(), params);
    }

}
