package com.imile.hrms.common.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/3
 */
@Data
@AllArgsConstructor
public class DataDifferHolder<T> {

    /**
     * 待插入列表
     */
    private List<T> insertList;

    /**
     * 待更新列表
     */
    private List<T> updateList;

    /**
     * 待删除列表
     */
    private List<T> deleteList;

    /**
     * 原始列表
     */
    private List<T> originList;

    public static <T> DataDifferHolder<T> emptyHolder() {
        return new DataDifferHolder<>(Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
    }

    public static <T> DataDifferHolder<T> buildHolder(List<T> insertList, List<T> updateList, List<T> deleteList) {
        return new DataDifferHolder<>(insertList, updateList, deleteList, Collections.emptyList());
    }

    public static <T> DataDifferHolder<T> buildHolder(List<T> insertList, List<T> updateList, List<T> deleteList, List<T> originList) {
        return new DataDifferHolder<>(insertList, updateList, deleteList, originList);
    }
}
