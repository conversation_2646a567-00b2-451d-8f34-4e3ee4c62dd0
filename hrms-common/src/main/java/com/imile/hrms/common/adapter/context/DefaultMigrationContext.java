package com.imile.hrms.common.adapter.context;

import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.context.UserContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/2/8 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class DefaultMigrationContext implements MigrationContext {


    @Value("${newAttendance.enable.userCodes:888888,2103429201}")
    private String enableUserCodes;

    private static final String ENABLE_ALL = "all";


    @Override
    public String getCurrentUserCode() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (null == userContext) {
            return null;
        }
        return userContext.getUserCode();
    }

    @Override
    public String getCurrentDepartmentId() {
        return "";
    }


    @Override
    public Boolean isEnableNewModuleByUser(String userCode) {
        return StringUtils.isNotBlank(enableUserCodes) &&
                (StringUtils.equals(enableUserCodes, ENABLE_ALL) || enableUserCodes.contains(userCode));
    }

    @Override
    public Boolean isEnableNewModuleByDepartment(String departmentId) {
        return null;
    }
}
