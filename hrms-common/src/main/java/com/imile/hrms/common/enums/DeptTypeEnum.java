package com.imile.hrms.common.enums;

import lombok.Getter;

/**
 * 部门类型
 *
 * <AUTHOR>
 */
@Getter
public enum DeptTypeEnum {
    DEPARTMENT("DEPARTMENT", "部门"),
    STATION("STATION", "直营网点"),
    SUB_DEPARTMENT("SUB_DEPARTMENT", "网点下部门"),
    SUB_STATION("SUB_STATION", "部门下网点"),
    CENTER_STATION("CENTER_STATION", "区域中心网点"),
    JOIN_STATION("JOIN_STATION", "加盟网点"),
    ;

    private String code;

    private String desc;

    DeptTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
