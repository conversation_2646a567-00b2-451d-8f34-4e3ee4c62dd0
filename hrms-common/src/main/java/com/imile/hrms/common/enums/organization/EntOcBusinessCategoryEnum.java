package com.imile.hrms.common.enums.organization;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Getter
public enum EntOcBusinessCategoryEnum {

    /**
     * 经营类别（1:直营 2:加盟 3:3PL）
     */
    DIRECT("1", "直营"),
    JOIN("2", "加盟"),
    THREE_PL("3", "3PL"),
    ;

    private final String code;

    private final String desc;

    EntOcBusinessCategoryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
