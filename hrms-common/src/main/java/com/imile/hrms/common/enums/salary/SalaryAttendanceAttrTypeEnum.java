package com.imile.hrms.common.enums.salary;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/8 16:43
 * @version: 1.0
 */
@Getter
public enum SalaryAttendanceAttrTypeEnum {

    ATTENDANCE_SOURCE("ATTENDANCE_SOURCE", "考勤接口获取", "attendance source", 1),
    ITEM_RELATION_SOURCE("ITEM_RELATION_SOURCE", "薪资项映射", "item relation source", 2),
    ATTENDANCE_PAGE_EDIT_USER_ITEM_SOURCE("ATTENDANCE_PAGE_EDIT_USER_ITEM_SOURCE", "考勤数据锁定界面编辑&导入用户考勤科目信息", "edit user item source", 3),
    CALCULATE_RULE_PAGE_EDIT_USER_ITEM_SOURCE("CALCULATE_RULE_PAGE_EDIT_USER_ITEM_SOURCE", "薪资计算界面编辑&导入用户考勤科目信息", "calculate rule edit user item source", 4),

    ;

    private String code;

    private String desc;

    private String descEn;

    private Integer priority;

    SalaryAttendanceAttrTypeEnum(String code, String desc, String descEn, Integer priority) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
        this.priority = priority;
    }

    public static SalaryAttendanceAttrTypeEnum getPriorityFirst(Set<String> codeSet) {
        List<SalaryAttendanceAttrTypeEnum> enumList = new ArrayList<>();
        for (String code : codeSet) {
            SalaryAttendanceAttrTypeEnum attrTypeEnum = getInstance(code);
            enumList.add(attrTypeEnum);
        }
        if (CollectionUtils.isEmpty(enumList)) {
            return null;
        }
        enumList = enumList.stream().sorted(Comparator.comparing(SalaryAttendanceAttrTypeEnum::getPriority).reversed()).collect(Collectors.toList());
        return enumList.get(0);
    }

    public static SalaryAttendanceAttrTypeEnum getInstance(String code) {
        for (SalaryAttendanceAttrTypeEnum value : SalaryAttendanceAttrTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
