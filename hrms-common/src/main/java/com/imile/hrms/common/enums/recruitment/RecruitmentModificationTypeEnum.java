package com.imile.hrms.common.enums.recruitment;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Getter
public enum RecruitmentModificationTypeEnum {
    APPLY("APPLY", "原单"),
    RESUBMIT("RESUBMIT", "驳回重提");

    private final String value;
    private final String description;

    RecruitmentModificationTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static RecruitmentModificationTypeEnum getInstance(String code) {
        for (RecruitmentModificationTypeEnum value : RecruitmentModificationTypeEnum.values()) {
            if (Objects.equals(value.getValue(), code)) {
                return value;
            }
        }
        throw new IllegalArgumentException(code);
    }

}
