<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.newAttendance.punchConfig.mapper.PunchClassItemConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassItemConfigDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="punch_class_id" jdbcType="BIGINT" property="punchClassId" />
        <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
        <result column="punch_in_time" jdbcType="TIMESTAMP" property="punchInTime" />
        <result column="punch_out_time" jdbcType="TIMESTAMP" property="punchOutTime" />
        <result column="earliest_punch_in_time" jdbcType="TIMESTAMP" property="earliestPunchInTime"/>
        <result column="latest_punch_in_time" jdbcType="TIMESTAMP" property="latestPunchInTime"/>
        <result column="latest_punch_out_time" jdbcType="TIMESTAMP" property="latestPunchOutTime"/>
        <result column="is_across" jdbcType="TINYINT" property="isAcross" />
        <result column="punch_time_interval" jdbcType="DECIMAL" property="punchTimeInterval"/>
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="is_latest" jdbcType="TINYINT" property="isLatest" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
        <result column="orderby" jdbcType="DECIMAL" property="orderby" />
        <result column="punch_in_time_interval" jdbcType="DECIMAL" property="punchInTimeInterval"/>
        <result column="elastic_time" jdbcType="DECIMAL" property="elasticTime" />
        <result column="rest_start_time" jdbcType="TIMESTAMP" property="restStartTime"/>
        <result column="rest_end_time" jdbcType="TIMESTAMP" property="restEndTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id,
    punch_class_id,
    sort_no,
    punch_in_time,
    punch_out_time,
    earliest_punch_in_time,
    latest_punch_in_time,
    latest_punch_out_time,
    is_across,
    punch_time_interval,
    status,
    is_latest,
    is_delete,
    record_version,
    create_date,
    create_user_code,
    create_user_name,
    last_upd_date,
    last_upd_user_code,
    last_upd_user_name,
    orderby,
    punch_in_time_interval,
    elastic_time,
    rest_start_time,
    rest_end_time
    </sql>
</mapper>
