<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.newAttendance.calendar.mapper.CalendarConfigDetailMapper">
  <resultMap id="BaseResultMap" type="com.imile.hrms.dao.newAttendance.calendar.model.CalendarConfigDetailDO">
    <!--@mbg.generated-->
    <!--@Table attendance.calendar_config_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attendance_config_id" jdbcType="BIGINT" property="attendanceConfigId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="day" jdbcType="INTEGER" property="day" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="day_id" jdbcType="BIGINT" property="dayId" />
    <result column="day_type" jdbcType="VARCHAR" property="dayType" />
    <result column="is_latest" jdbcType="TINYINT" property="isLatest" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="orderby" jdbcType="DECIMAL" property="orderby" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, attendance_config_id, `year`, `month`, `day`, `date`, day_id, day_type, is_latest, 
    remark, is_delete, record_version, create_date, create_user_code, create_user_name, 
    last_upd_date, last_upd_user_code, last_upd_user_name, orderby
  </sql>
</mapper>