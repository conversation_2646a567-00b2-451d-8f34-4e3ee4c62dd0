<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.blacklist.mapper.HrmsBlacklistMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.blacklist.model.HrmsBlacklistDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="user_id" property="userId"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="end_date" property="endDate"/>
        <result column="reason" property="reason"/>
        <result column="ban_status" property="banStatus"/>
        <result column="record_date" property="recordDate"/>
        <result column="record_user_code" property="recordUserCode"/>
        <result column="record_user_name" property="recordUserName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete
        ,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_id,user_code,  source_system, end_date, reason,ban_status,record_date,record_user_code,record_user_name
    </sql>

    <select id="getPageList"
            parameterType="com.imile.hrms.dao.blacklist.query.BlacklistPageQuery"
            resultType="com.imile.hrms.dao.blacklist.dto.BlacklistPageDTO">
        SELECT
        hui.user_name AS employeeName,
        hui.location_country AS locationCountry,
        hui.employee_type AS employeeType,
        hui.is_driver,
        hui.country_code AS countryCode,
        hui.work_status,
        hb.id,
        hb.user_id,
        hb.user_code AS employeeId,
        hb.ban_status,
        hb.end_date,
        hb.reason,
        hb.source_system,
        hb.record_date,
        hb.record_user_name,
        count(DISTINCT hbp.id) AS phoneCount,
        count(DISTINCT huc.id) AS certificateTypeCount
        FROM hrms_blacklist AS hb
        INNER JOIN hrms_user_info AS hui ON hb.user_id = hui.id AND hui.is_delete = 0
        LEFT JOIN hrms_blacklist_phone AS hbp ON hbp.blacklist_id = hb.id AND hbp.is_delete = 0
        LEFT JOIN hrms_user_certificate AS huc ON hb.user_id = huc.user_id AND huc.is_delete = 0
        WHERE hb.is_delete = 0
        <if test="userNameOrCertificateCode != null and userNameOrCertificateCode != ''">
            AND (
                hui.user_name LIKE concat('%', #{userNameOrCertificateCode}, '%')
                OR hui.user_code LIKE concat('%', #{userNameOrCertificateCode}, '%')
                OR huc.certificate_code LIKE concat('%', #{userNameOrCertificateCode}, '%')
            )
        </if>
        <if test="workStatus != null and workStatus != ''">
            AND hui.work_status = #{workStatus}
        </if>
        <if test="banStatusList != null and banStatusList.size() > 0">
            AND hb.ban_status IN
            <foreach collection="banStatusList" item="banStatus" open=" (" close=")" separator=",">
                #{banStatus}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            AND hui.origin_country = #{country}
        </if>
        GROUP BY hui.id
        ORDER BY hb.record_date DESC
    </select>

    <select id="getLockdownInfo"
            parameterType="java.util.List"
            resultType="com.imile.hrms.dao.blacklist.dto.LockdownInfoDTO">
        select hb.user_id,hb.user_code,hb.end_date,hb.ban_status,hb.reason,hb.record_user_code,hb.record_user_name,huc.certificate_type_code, huc.certificate_code
        from hrms_blacklist hb
        join hrms_user_certificate huc on hb.user_id = huc.user_id
        <where>
            hb.is_delete = 0
            and huc.is_delete = 0
            <if test="certificateList!=null and certificateList.size()>0">
                and (huc.certificate_type_code, huc.certificate_code) in
                <foreach collection="certificateList" item="certificate" open=" (" close=")" separator=",">
                    (#{certificate.certificateTypeCode} ,#{certificate.certificateCode})
                </foreach>
            </if>
        </where>

    </select>


</mapper>
