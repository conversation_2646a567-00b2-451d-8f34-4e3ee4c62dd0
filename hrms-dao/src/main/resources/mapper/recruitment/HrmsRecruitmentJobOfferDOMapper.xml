<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.recruitment.mapper.HrmsRecruitmentJobOfferDOMapper">
  <resultMap id="BaseResultMap" type="com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobOfferDO">
    <!--@mbg.generated-->
    <!--@Table hrms_recruitment_job_headcount_offer-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="recruitment_job_id" jdbcType="BIGINT" property="recruitmentJobId" />
    <result column="recruitment_job_headcount_id" jdbcType="BIGINT" property="recruitmentJobHeadcountId" />
    <result column="application_code" jdbcType="VARCHAR" property="applicationCode" />
    <result column="apply_user_id" jdbcType="BIGINT" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_name_en" jdbcType="VARCHAR" property="applyUserNameEn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="current_status_change_reason" jdbcType="VARCHAR" property="currentStatusChangeReason" />
    <result column="offer_approval_id" jdbcType="BIGINT" property="offerApprovalId" />
    <result column="offer_approval_create_time" jdbcType="TIMESTAMP" property="offerApprovalCreateTime" />
    <result column="offer_approved_time" jdbcType="TIMESTAMP" property="offerApprovedTime" />
    <result column="offer_approval_status" jdbcType="TINYINT" property="offerApprovalStatus" />
    <result column="candidate_name" jdbcType="VARCHAR" property="candidateName" />
    <result column="nationality_code" jdbcType="VARCHAR" property="nationalityCode" />
    <result column="accept_user_id" jdbcType="BIGINT" property="acceptUserId" />
    <result column="is_management" jdbcType="TINYINT" property="isManagement" />
    <result column="probation" jdbcType="TINYINT" property="probation" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="detail_address" jdbcType="VARCHAR" property="detailAddress" />
    <result column="probationary_salary_json" jdbcType="LONGVARCHAR" property="probationarySalaryJson" />
    <result column="salary_budget" jdbcType="TINYINT" property="salaryBudget" />
    <result column="budget_allocation" jdbcType="TINYINT" property="budgetAllocation" />
    <result column="expect_date" jdbcType="TIMESTAMP" property="expectDate" />
    <result column="interview_evaluation_file_urls" jdbcType="VARCHAR" property="interviewEvaluationFileUrls" />
    <result column="offer_letter_file_urls" jdbcType="VARCHAR" property="offerLetterFileUrls" />
    <result column="offer_comparison_file_urls" jdbcType="VARCHAR" property="offerComparisonFileUrls" />
    <result column="payslip_file_urls" jdbcType="VARCHAR" property="payslipFileUrls" />
    <result column="step" jdbcType="INTEGER" property="step" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_country" jdbcType="VARCHAR" property="deptCountry" />
    <result column="dept_biz_country" jdbcType="VARCHAR" property="deptBizCountry" />
    <result column="grade_id" jdbcType="BIGINT" property="gradeId" />
    <result column="grade_no" jdbcType="VARCHAR" property="gradeNo" />
    <result column="resume_file_urls" jdbcType="VARCHAR" property="resumeFileUrls" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="reference_check_profile_urls" jdbcType="VARCHAR" property="referenceCheckProfileUrls" />
    <result column="report_to_user_id" jdbcType="VARCHAR" property="reportToUserId" />
    <result column="is_re_employment" jdbcType="VARCHAR" property="isReEmployment" />
    <result column="passport_file_urls" jdbcType="VARCHAR" property="passportFileUrls" />
    <result column="signature_file_urls" jdbcType="VARCHAR" property="signatureFileUrls" />
    <result column="other_file_urls" jdbcType="VARCHAR" property="otherFileUrls" />
    <result column="oc_id" jdbcType="VARCHAR" property="ocId" />
    <result column="oc_name" jdbcType="VARCHAR" property="ocName" />
    <result column="is_has_stock" jdbcType="VARCHAR" property="isHasStock" />
    <result column="old_version_data" jdbcType="VARCHAR" property="oldVersionData" />
    <result column="referrer_user_id" jdbcType="BIGINT" property="referrerUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date,
    last_upd_user_code, last_upd_user_name, recruitment_job_id, recruitment_job_headcount_id, application_code,
    apply_user_id,apply_user_name,apply_user_name_en, `status`, current_status_change_reason, offer_approval_id, offer_approval_create_time,offer_approved_time, offer_approval_status,
    candidate_name, nationality_code, accept_user_id, is_management, probation, country, province, city, district, detail_address,
    probationary_salary_json, salary_budget, budget_allocation, expect_date, interview_evaluation_file_urls,
    offer_letter_file_urls, offer_comparison_file_urls, payslip_file_urls, step, job_name, dept_id,dept_name, dept_country, dept_biz_country,
    grade_id, grade_no, resume_file_urls, remark, reference_check_profile_urls,report_to_user_id,is_re_employment, passport_file_urls, signature_file_urls,other_file_urls,
    oc_id, is_has_stock, oc_name, old_version_data, referrer_user_id
  </sql>

</mapper>