<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.recruitment.mapper.HrmsRecruitmentJobHeadcountDOMapper">
  <resultMap id="BaseResultMap" type="com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountDO">
    <!--@mbg.generated-->
    <!--@Table hrms_recruitment_job_headcount-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="recruitment_job_id" jdbcType="BIGINT" property="recruitmentJobId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="status_before_pause" jdbcType="TINYINT" property="statusBeforePause" />
    <result column="current_status_change_reason" jdbcType="VARCHAR" property="currentStatusChangeReason" />
    <result column="grade_id" jdbcType="BIGINT" property="gradeId" />
    <result column="grade_no" jdbcType="VARCHAR" property="gradeNo" />
    <result column="employment_type" jdbcType="VARCHAR" property="employmentType" />
    <result column="request_type" jdbcType="TINYINT" property="requestType" />
    <result column="budget_min" jdbcType="DECIMAL" property="budgetMin" />
    <result column="budget_max" jdbcType="DECIMAL" property="budgetMax" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="report_to_user_id" jdbcType="BIGINT" property="reportToUserId" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="is_chinese_initiating" jdbcType="TINYINT" property="isChineseInitiating" />
    <result column="pause_duration" jdbcType="BIGINT" property="pauseDuration" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete,record_version, create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code,
    last_upd_user_name, recruitment_job_id, `status`,status_before_pause,current_status_change_reason, grade_id, grade_no,
    employment_type, request_type, budget_min, budget_max, currency, report_to_user_id, effective_time, is_chinese_initiating, pause_duration
  </sql>

<!--auto generated by MybatisCodeHelper on 2023-12-29-->
  <select id="selectHcListBy" resultMap="BaseResultMap">
        select
        hc.*
        from hrms_recruitment_job_headcount hc
        inner join hrms_recruitment_job job
            on hc.recruitment_job_id = job.id
        where hc.is_delete = 0 and job.is_delete = 0
        <if test="param.isSysAdmin == null or !param.isSysAdmin">
            and (job.create_user_code = #{param.createUserCode}
            or hc.id in (
                select recruitment_job_headcount_id from hrms_recruitment_job_headcount_cooperator r
                where is_delete = 0 and cooperator_user_code = #{param.createUserCode} and cooperator_status = 10
            )
            <if test="param.permissionDeptIds != null and param.permissionDeptIds.size() != 0">
                or (
                    job.dept_id in <foreach collection="param.permissionDeptIds" item="item" close=")" open="(" separator=",">
                        #{item}
                     </foreach>
                    <if test="param.andPermissionIsChineseInitiating != null">
                        and hc.is_chinese_initiating = #{param.andPermissionIsChineseInitiating}
                    </if>
                )
            </if>
            <if test="param.permissionCountry != null and param.permissionCountry != ''">
                or job.biz_country like concat('%', #{param.permissionCountry}, '%')
            </if>
            <if test="param.orPermissionIsChineseInitiating != null">
                or hc.is_chinese_initiating = #{param.orPermissionIsChineseInitiating}
            </if>
            )
        </if>
        <if test="param.recruitmentType != null">
          and job.recruitment_type = #{param.recruitmentType}
        </if>
        <if test=" param.recruitmentTypes != null and param.recruitmentTypes.size() != 0">
          and job.recruitment_type in <foreach collection="param.recruitmentTypes" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        </if>
        <if test="param.approveCode != null and param.approveCode != ''">
          and job.application_code = #{param.approveCode}
        </if>
        <if test="param.queryCreateUserCode != null and param.queryCreateUserCode != ''">
            and job.create_user_code = #{param.queryCreateUserCode}
        </if>
        <if test="param.status != null and param.status.size() != 0">
          and hc.status in <foreach collection="param.status" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        </if>
        <if test="param.gradeId != null and param.gradeId.size() != 0">
          and hc.grade_id in <foreach collection="param.gradeId" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        </if>
        <if test="param.gradeNo != null and param.gradeNo.size() != 0">
          and hc.grade_no in <foreach collection="param.gradeNo" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        </if>
        <if test="param.deptIds != null and param.deptIds.size() != 0">
          and job.dept_id in <foreach collection="param.deptIds" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        </if>
      <if test="param.country != null and param.country != ''">
          and job.biz_country like concat('%', #{param.country}, '%')
      </if>
      <if test="param.positionName != null and param.positionName != ''">
          and job.job_name = #{param.positionName}
      </if>
      <if test="param.postId != null and param.postId != ''">
          and job.post_id = #{param.postId}
      </if>
      <if test="param.applyUserName != null and param.applyUserName != ''">
          and job.apply_user_id in (
            select id from hrms_user_info where is_delete = 0
            and (user_name like concat('%', #{param.applyUserName}, '%') or
                user_name_en like concat('%', #{param.applyUserName}, '%') or
                sys_account_name like concat('%', #{param.applyUserName}, '%'))
          )
      </if>
      <if test="param.createStartTime != null and param.createEndTime != null">
          and job.approve_create_time between #{param.createStartTime} and #{param.createEndTime}
      </if>
      <if test="param.approvedStartDate != null and param.approvedEndDate != null">
          and job.approved_time between #{param.approvedStartDate} and #{param.approvedEndDate}
      </if>
      <if test="param.approveStatus != null and param.approveStatus.size() != 0">
          and job.approve_status in <foreach collection="param.approveStatus" item="item" separator="," open="(" close=")">
          #{item}
      </foreach>
      </if>
      <if test="param.isChineseInitiating != null">
          and hc.is_chinese_initiating = #{param.isChineseInitiating}
      </if>
      <if test="param.jobId != null">
          and job.id = #{param.jobId}
      </if>
      ORDER BY
      IF(job.approve_status = 1, 0, 1),
      job.create_date DESC
    </select>
</mapper>