<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.organization.mapper.HrmsDeptSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.imile.hrms.dao.organization.model.HrmsDeptSnapshotDO">
    <!--@mbg.generated-->
    <!--@Table hrms_dept_snapshot-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="snapshot_version" jdbcType="VARCHAR" property="snapshotVersion" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="top_id" jdbcType="BIGINT" property="topId" />
    <result column="dept_name_cn" jdbcType="VARCHAR" property="deptNameCn" />
    <result column="dept_name_en" jdbcType="VARCHAR" property="deptNameEn" />
    <result column="dept_org_type" jdbcType="TINYINT" property="deptOrgType" />
    <result column="biz_area_id" jdbcType="BIGINT" property="bizAreaId" />
    <result column="biz_area_name_cn" jdbcType="VARCHAR" property="bizAreaNameCn" />
    <result column="biz_area_name_en" jdbcType="VARCHAR" property="bizAreaNameEn" />
    <result column="dept_position" jdbcType="VARCHAR" property="deptPosition" />
    <result column="dept_duty" jdbcType="VARCHAR" property="deptDuty" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="dept_level" jdbcType="TINYINT" property="deptLevel" />
    <result column="biz_country" jdbcType="VARCHAR" property="bizCountry" />
    <result column="biz_model_ids" jdbcType="VARCHAR" property="bizModelIds" />
    <result column="biz_model_names_cn" jdbcType="VARCHAR" property="bizModelNamesCn" />
    <result column="biz_model_names_en" jdbcType="VARCHAR" property="bizModelNamesEn" />
    <result column="leader_code" jdbcType="VARCHAR" property="leaderCode" />
    <result column="leader_name" jdbcType="VARCHAR" property="leaderName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="enabled_date" jdbcType="DATE" property="enabledDate" />
    <result column="disabled_date" jdbcType="DATE" property="disabledDate" />
    <result column="oc_id" jdbcType="BIGINT" property="ocId" />
    <result column="oc_type" jdbcType="VARCHAR" property="ocType" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="dept_biz_leader_config" jdbcType="VARCHAR" property="deptBizLeaderConfig" />
    <result column="dept_path" jdbcType="VARCHAR" property="deptPath" />
    <result column="oc_code" jdbcType="VARCHAR" property="ocCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, 
    last_upd_user_code, last_upd_user_name, snapshot_version, dept_id, dept_code, top_id, 
    dept_name_cn, dept_name_en, dept_org_type, biz_area_id, biz_area_name_cn,
    biz_area_name_en, dept_position, dept_duty, parent_id, dept_level, biz_country, biz_model_ids, 
    biz_model_names_cn, biz_model_names_en, leader_code, leader_name, `status`, enabled_date, 
    disabled_date, oc_id, oc_type, country, province, city, region, address, dept_biz_leader_config, dept_path, oc_code
  </sql>
</mapper>