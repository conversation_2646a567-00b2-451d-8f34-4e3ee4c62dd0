<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.recruitment.mapper.HrmsRecruitmentJobDOMapper">
  <resultMap id="BaseResultMap" type="com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobDO">
    <!--@mbg.generated-->
    <!--@Table hrms_recruitment_job-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="application_code" jdbcType="VARCHAR" property="applicationCode" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="biz_country" jdbcType="VARCHAR" property="bizCountry" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="apply_user_id" jdbcType="BIGINT" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_name_en" jdbcType="VARCHAR" property="applyUserNameEn" />
    <result column="approve_status" jdbcType="TIMESTAMP" property="approveStatus" />
    <result column="approve_create_time" jdbcType="TIMESTAMP" property="approveCreateTime" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="post_id" jdbcType="BIGINT" property="postId" />
    <result column="recruitment_job_approval_id" jdbcType="BIGINT" property="recruitmentJobApprovalId" />
    <result column="first_round_interviewer_user_ids" jdbcType="VARCHAR" property="firstRoundInterviewerUserIds" />
    <result column="second_round_interviewer_user_ids" jdbcType="VARCHAR" property="secondRoundInterviewerUserIds" />
    <result column="third_round_interviewer_user_ids" jdbcType="VARCHAR" property="thirdRoundInterviewerUserIds" />
    <result column="job_responsibilities" jdbcType="VARCHAR" property="jobResponsibilities" />
    <result column="job_qualifications" jdbcType="VARCHAR" property="jobQualifications" />
    <result column="job_file_urls" jdbcType="VARCHAR" property="jobFileUrls" />
    <result column="it_assets_required" jdbcType="VARCHAR" property="itAssetsRequired" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="step" jdbcType="INTEGER" property="step" />
    <result column="professional_function" jdbcType="TINYINT" property="professionalFunction" />
    <result column="old_version_data" jdbcType="VARCHAR" property="oldVersionData" />
    <result column="fs_job_id" jdbcType="VARCHAR" property="fsJobId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete,record_version, create_date, create_user_code, create_user_name, last_upd_date,
    last_upd_user_code, last_upd_user_name, application_code, dept_id, dept_name, biz_country, country, province, city, region, address,
    `status`, job_name, post_id, apply_user_id, apply_user_name,apply_user_name_en, approve_status, approve_create_time, approved_time, recruitment_job_approval_id,
    first_round_interviewer_user_ids, second_round_interviewer_user_ids, third_round_interviewer_user_ids,
    job_responsibilities, job_qualifications, job_file_urls, it_assets_required, remark, step,professional_function, old_version_data,fs_job_id
  </sql>

</mapper>