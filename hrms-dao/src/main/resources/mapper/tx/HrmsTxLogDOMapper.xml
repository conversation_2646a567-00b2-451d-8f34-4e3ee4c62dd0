<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.tx.mapper.HrmsTxLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.tx.model.HrmsTxLogDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="biz_id" property="bizId" />
        <result column="biz_type" property="bizType" />
        <result column="state" property="state" />
        <result column="check_count" property="checkCount" />
        <result column="next_check_time" property="nextCheckTime" />
        <result column="param" property="param" />
        <result column="remark" property="remark" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="orderby" property="orderby" />
        <result column="is_completed" property="isCompleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, biz_id, biz_type, state, check_count, next_check_time, param, remark, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name, orderby, is_completed
    </sql>

</mapper>
