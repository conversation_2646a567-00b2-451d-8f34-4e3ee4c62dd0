<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.organization.mapper.HrmsOrgTemplateDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.organization.model.HrmsOrgTemplateDeptDO">
        <id column="id" property="id" />
        <result column="record_version" property="recordVersion" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="template_id" property="templateId" />
        <result column="dept_name_cn" property="deptNameCn" />
        <result column="dept_name_en" property="deptNameEn" />
        <result column="parent_id" property="parentId" />
        <result column="dept_position" property="deptPosition" />
        <result column="dept_duty" property="deptDuty" />
        <result column="dept_org_type" property="deptOrgType" />
        <result column="biz_area" property="bizArea" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        record_version,
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, template_id, dept_name_cn, dept_name_en, parent_id, dept_position, dept_duty, dept_org_type, biz_area
    </sql>

</mapper>
