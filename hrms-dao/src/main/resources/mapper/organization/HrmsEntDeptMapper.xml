<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.organization.mapper.HrmsEntDeptMapper">
    <!--@Table hrms_ent_dept-->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.organization.model.HrmsEntDeptDO">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="top_id" property="topId"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="dept_name_cn" property="deptNameCn"/>
        <result column="dept_name_en" property="deptNameEn"/>
        <result column="dept_short_name" property="deptShortName"/>
        <result column="leader_code" property="leaderCode"/>
        <result column="leader_name" property="leaderName"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="orderby" property="orderby"/>
        <result column="oc_id" property="ocId"/>
        <result column="country" property="country"/>
        <result column="oc_center_code" property="ocCenterCode"/>
        <result column="settle_org_id" property="settleOrgId"/>
        <result column="vendor_org_id" property="vendorOrgId"/>
        <result column="dept_code" property="deptCode"/>
        <result column="type" property="type"/>
        <result column="dept_org_type" property="deptOrgType"/>
        <result column="region" property="region"/>
        <result column="city" property="city"/>
        <result column="province" property="province"/>
        <result column="biz_country" property="bizCountry"/>
        <result column="address" property="address"/>
        <result column="biz_area" property="bizArea"/>
        <result column="dept_position" property="deptPosition"/>
        <result column="dept_duty" property="deptDuty"/>
        <result column="biz_model_id" property="bizModelId"/>
        <result column="recent_active_time" property="recentActiveTime"/>
        <result column="recent_disabled_time" property="recentDisabledTime"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="is_delete" property="isDelete"/>
        <result column="record_version" property="recordVersion"/>
        <result column="oc_type" property="ocType"/>
        <result column="oc_code" property="ocCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        org_id,
        top_id,
        parent_id,
        `level`,
        dept_name_cn,
        dept_name_en,
        dept_short_name,
        organization_code,
        leader_code,
        leader_name,
        `status`,
        remark,
        orderby,
        oc_id,
        oc_code,
        country,
        oc_center_code,
        settle_org_id,
        vendor_org_id,
        dept_code,
        `type`,
        dept_org_type,
        region,
        city,
        province,
        biz_country,
        address,
        biz_area,
        dept_position,
        dept_duty,
        biz_model_id,
        recent_active_time,
        recent_disabled_time,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        is_delete,
        record_version,
        dept_path,
        oc_type
    </sql>
    <select id="selectValidBizCountryList" resultType="java.lang.String">
        SELECT DISTINCT biz_country
        FROM hrms_ent_dept
        WHERE is_delete = 0
        AND status = 'ACTIVE'
        AND biz_country != ''
    </select>

<!--auto generated by MybatisCodeHelper on 2024-02-29-->
    <select id="selectBy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hrms_ent_dept
        where is_delete = 0 and top_id != 0
        <if test="param.deptOrgTypeList != null and param.deptOrgTypeList.size() != 0">
            and dept_org_type in
            <foreach item="item" index="index" collection="param.deptOrgTypeList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.deptIdList != null and param.deptIdList.size() != 0">
            and id in
            <foreach item="item" index="index" collection="param.deptIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.filterCenterStation != null and param.filterCenterStation == 1">
            and type != 'CENTER_STATION'
        </if>
        <if test="param.status != null and param.status != ''">
            and `status`=#{param.status}
        </if>
        <if test="param.statusList != null and param.statusList.size() != 0">
            and `status` in
            <foreach collection="param.statusList" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.bizCountryList != null and param.bizCountryList.size() != 0">
            and
            <foreach collection="param.bizCountryList" close=")" open="(" separator=" or " item="item">
                find_in_set(#{item}, biz_country)
            </foreach>
        </if>
        <if test="param.country != null and param.country != ''">
            and country = #{param.country}
        </if>
    </select>
</mapper>
