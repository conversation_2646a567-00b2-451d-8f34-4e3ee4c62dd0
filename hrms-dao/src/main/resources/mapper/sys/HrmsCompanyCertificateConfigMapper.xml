<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.sys.mapper.HrmsCompanyCertificateConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.sys.model.HrmsCompanyCertificateConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="country" property="country" />
        <result column="employee_type" property="employeeType" />
        <result column="certificate_type_code" property="certificateTypeCode" />
        <result column="certificate_type_name_cn" property="certificateTypeNameCn" />
        <result column="certificate_type_name_en" property="certificateTypeNameEn" />
        <result column="driver" property="driver" />
        <result column="handle_method" property="handlerMethod" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, country,
        certificate_type_code,
        employee_type,certificate_type_name_cn,
        certificate_type_name_en,driver,handle_method
    </sql>

</mapper>
