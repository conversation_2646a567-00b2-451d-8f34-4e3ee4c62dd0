<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.approval.mapper.HrmsApprovalFormMapper">
  <resultMap id="BaseResultMap" type="com.imile.hrms.dao.approval.model.HrmsApprovalFormDO">
    <!--@mbg.generated-->
    <!--@Table hrms.hrms_approval_form-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
    <result column="application_code" jdbcType="VARCHAR" property="applicationCode" />
    <result column="form_status" jdbcType="VARCHAR" property="formStatus" />
    <result column="form_type" jdbcType="VARCHAR" property="formType" />
    <result column="approval_id" jdbcType="BIGINT" property="approvalId" />
    <result column="approval_process_info" jdbcType="LONGVARCHAR" property="approvalProcessInfo" />
    <result column="data_source" jdbcType="INTEGER" property="dataSource" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date, 
    last_upd_user_code, last_upd_user_name, apply_user_code, application_code, form_status, 
    form_type, approval_id, approval_process_info, data_source
  </sql>
</mapper>