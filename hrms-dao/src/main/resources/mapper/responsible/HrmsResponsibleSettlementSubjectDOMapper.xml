<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.responsible.mapper.HrmsResponsibleSettlementSubjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.responsible.model.HrmsResponsibleSettlementSubjectDO">
        <id column="id" property="id" />
        <result column="create_date" property="createDate" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_user_code" property="createUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="create_user_name" property="createUserName" />
        <result column="status" property="status" />
        <result column="settlement_short_name" property="settlementShortName" />
        <result column="settlement_center_name" property="settlementCenterName" />
        <result column="register_country" property="registerCountry" />
        <result column="company_org_id" property="companyOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_date,
        is_delete,
        record_version,
        create_user_code,
        last_upd_user_name,
        last_upd_user_code,
        last_upd_date,
        create_user_name,
        id, status, settlement_short_name, settlement_center_name, register_country, company_org_id
    </sql>

</mapper>
