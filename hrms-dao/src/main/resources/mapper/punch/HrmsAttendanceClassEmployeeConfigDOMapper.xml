<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.punch.mapper.HrmsAttendanceClassEmployeeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.punch.model.HrmsAttendanceClassEmployeeConfigDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="attendance_config_id" property="attendanceConfigId"/>
        <result column="punch_config_id" property="punchConfigId"/>
        <result column="class_time" property="classTime"/>
        <result column="class_id" property="classId"/>
        <result column="is_latest" property="isLatest"/>
        <result column="orderby" property="orderby"/>
        <result column="day_punch_type" property="dayPunchType"/>
        <result column="date_source" property="dataSource"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_id, attendance_config_id,  punch_config_id, class_time, class_id, is_latest, orderby, day_punch_type, dataSource
    </sql>

    <select id="selectUserPunchConfig" resultType="com.imile.hrms.dao.punch.dto.HrmsAttendanceClassEmployeeConfigDTO">
        select
        hui.id as userId,
        pc.country as country,
        hui.work_no as workNo,
        hui.user_code as userCode,
        hui.dept_id as deptId,
        hui.user_name as userName,
        hui.post_id as postId,
        hui.oc_id as ocId,
        hui.location_country as originCountry,
        pc.id as punchConfigId,
        pc.punch_config_no as punchConfigNo,
        pc.punch_config_name as punchConfigName,
        pc.punch_config_type as punchConfigType,
        hui.profile_photo_url as profilePhotoUrl
        from hrms_attendance_punch_config as pc
        inner join hrms_attendance_punch_config_range as cr on pc.id= cr.punch_config_id
        inner join hrms_user_info as hui on cr.biz_id=hui.id
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join hrms_attendance_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
            inner join hrms_attendance_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1 and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        where pc.is_delete=0 and pc.is_latest=1
        and cr.is_delete=0 and cr.is_latest=1
        and hui.is_delete=0 and hui.status="ACTIVE" and
        hui.work_status="ON_JOB" and hui.user_code is not null
        <if test="country!=null and country!=''">
            and pc.country = #{country}
        </if>
        <if test="workNo!=null and workNo!=''">
            and hui.work_no = #{workNo}
        </if>
        <if test="userNameOrEmail!=null and userNameOrEmail!=''">
            and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
            concat('%',#{userNameOrEmail},'%'))
        </if>
        <if test="postId !=null">
            and hui.post_id = #{postId}
        </if>
        <if test="userCode!=null and userCode!=''">
            and hui.user_code = #{userCode}
        </if>
        <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
            <if test="deptIds!=null and deptIds.size()>0">
                and (hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
                <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                    or hui.location_country in
                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                             separator=",">
                        #{locationCountry}
                    </foreach>
                </if>
                )
            </if>

            <if test="deptIds==null or deptIds.size()==0">
                <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                    and hui.location_country in
                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                             separator=",">
                        #{locationCountry}
                    </foreach>
                </if>
            </if>
        </if>

        <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
            and hui.dept_id in
            <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>

        <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
            and hui.location_country in
            <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                     separator=",">
                #{locationCountry}
            </foreach>
        </if>
        <if test="isChooseDept!=null and isChooseDept == true">
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                    #{deptId}
                </foreach>
            </if>
        </if>
        <if test="postIdList!=null and postIdList.size()>0">
            <foreach collection="postIdList" separator="," open="and hui.post_id in (" close=")" item="postId">
                #{postId}
            </foreach>
        </if>
        <if test="driver !=null">
            and hui.is_driver = #{driver}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" separator="," open="and hui.employee_type in (" close=")"
                     item="employeeType">
                #{employeeType}
            </foreach>
        </if>
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
            concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
            or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="punchConfigIdList!=null and punchConfigIdList.size()>0">
            <foreach collection="punchConfigIdList" separator="," open="and pc.id in (" close=")" item="configId">
                #{configId}
            </foreach>
        </if>
        <if test="punchConfigNo!=null and punchConfigNo!=''">
            and pc.punch_config_no = #{punchConfigNo}
        </if>
        <if test="isNeedPunch !=null">
            and cr.is_need_punch = #{isNeedPunch}
        </if>
        <if test="userIds!=null and userIds.size()>0">
            <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="vendorCodeList !=null and vendorCodeList.size()>0">

        union

            select
            hui.id as userId,
            pc.country as country,
            hui.work_no as workNo,
            hui.user_code as userCode,
            hui.dept_id as deptId,
            hui.user_name as userName,
            hui.post_id as postId,
            hui.oc_id as ocId,
            hui.location_country as originCountry,
            pc.id as punchConfigId,
            pc.punch_config_no as punchConfigNo,
            pc.punch_config_name as punchConfigName,
            pc.punch_config_type as punchConfigType,
            hui.profile_photo_url as profilePhotoUrl
            from hrms_attendance_punch_config as pc
            inner join hrms_attendance_punch_config_range as cr on pc.id= cr.punch_config_id
            inner join hrms_user_info as hui on cr.biz_id=hui.id
            <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
                inner join hrms_attendance_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
                inner join hrms_attendance_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1 and ac.attendance_config_no = #{attendanceConfigNo}
            </if>
            where pc.is_delete=0 and pc.is_latest=1
            and cr.is_delete=0 and cr.is_latest=1
            and hui.is_delete=0 and hui.status="ACTIVE" and
            hui.work_status="ON_JOB" and hui.user_code is not null and employee_type = 'OSFixedsalary'
            <foreach collection="vendorCodeList" separator="," open="and hui.vendor_code in (" close=")"
                     item="vendorCode">
                #{vendorCode}
            </foreach>
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="postId !=null">
                and hui.post_id = #{postId}
            </if>
            <if test="userCode!=null and userCode!=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="postIdList!=null and postIdList.size()>0">
                <foreach collection="postIdList" separator="," open="and hui.post_id in (" close=")" item="postId">
                    #{postId}
                </foreach>
            </if>
            <if test="driver !=null">
                and hui.is_driver = #{driver}
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="punchConfigIdList!=null and punchConfigIdList.size()>0">
                <foreach collection="punchConfigIdList" separator="," open="and pc.id in (" close=")" item="configId">
                    #{configId}
                </foreach>
            </if>
            <if test="punchConfigNo!=null and punchConfigNo!=''">
                and pc.punch_config_no = #{punchConfigNo}
            </if>
            <if test="isNeedPunch !=null">
                and cr.is_need_punch = #{isNeedPunch}
            </if>
        </if>
        <!--<include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
    </select>


    <select id="selectUserPunchConfigNewAttendance" resultType="com.imile.hrms.dao.punch.dto.HrmsAttendanceClassEmployeeConfigDTO">
        select
        hui.id as userId,
        pc.country as country,
        hui.work_no as workNo,
        hui.user_code as userCode,
        hui.dept_id as deptId,
        hui.user_name as userName,
        hui.post_id as postId,
        hui.oc_id as ocId,
        hui.location_country as originCountry,
        pc.id as punchConfigId,
        pc.punch_config_no as punchConfigNo,
        pc.punch_config_name as punchConfigName,
        pc.punch_config_type as punchConfigType,
        hui.profile_photo_url as profilePhotoUrl
        from punch_config as pc
        inner join punch_config_range as cr on pc.id= cr.punch_config_id
        inner join hrms_user_info as hui on cr.biz_id=hui.id
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join calendar_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
            inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1 and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        where pc.is_delete=0 and pc.is_latest=1
        and cr.is_delete=0 and cr.is_latest=1
        and hui.is_delete=0 and hui.status="ACTIVE" and
        hui.work_status="ON_JOB" and hui.user_code is not null
        <if test="country!=null and country!=''">
            and pc.country = #{country}
        </if>
        <if test="workNo!=null and workNo!=''">
            and hui.work_no = #{workNo}
        </if>
        <if test="userNameOrEmail!=null and userNameOrEmail!=''">
            and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
            concat('%',#{userNameOrEmail},'%'))
        </if>
        <if test="postId !=null">
            and hui.post_id = #{postId}
        </if>
        <if test="userCode!=null and userCode!=''">
            and hui.user_code = #{userCode}
        </if>
        <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
            <if test="deptIds!=null and deptIds.size()>0">
                and (hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
                <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                    or hui.location_country in
                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                             separator=",">
                        #{locationCountry}
                    </foreach>
                </if>
                )
            </if>

            <if test="deptIds==null or deptIds.size()==0">
                <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                    and hui.location_country in
                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                             separator=",">
                        #{locationCountry}
                    </foreach>
                </if>
            </if>
        </if>

        <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
            and hui.dept_id in
            <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>

        <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
            and hui.location_country in
            <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                     separator=",">
                #{locationCountry}
            </foreach>
        </if>
        <if test="isChooseDept!=null and isChooseDept == true">
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                    #{deptId}
                </foreach>
            </if>
        </if>
        <if test="postIdList!=null and postIdList.size()>0">
            <foreach collection="postIdList" separator="," open="and hui.post_id in (" close=")" item="postId">
                #{postId}
            </foreach>
        </if>
        <if test="driver !=null">
            and hui.is_driver = #{driver}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" separator="," open="and hui.employee_type in (" close=")"
                     item="employeeType">
                #{employeeType}
            </foreach>
        </if>
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
            concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
            or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="punchConfigIdList!=null and punchConfigIdList.size()>0">
            <foreach collection="punchConfigIdList" separator="," open="and pc.id in (" close=")" item="configId">
                #{configId}
            </foreach>
        </if>
        <if test="punchConfigNo!=null and punchConfigNo!=''">
            and pc.punch_config_no = #{punchConfigNo}
        </if>
        <if test="isNeedPunch !=null">
            and cr.is_need_punch = #{isNeedPunch}
        </if>
        <if test="userIds!=null and userIds.size()>0">
            <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="vendorCodeList !=null and vendorCodeList.size()>0">

            union

            select
            hui.id as userId,
            pc.country as country,
            hui.work_no as workNo,
            hui.user_code as userCode,
            hui.dept_id as deptId,
            hui.user_name as userName,
            hui.post_id as postId,
            hui.oc_id as ocId,
            hui.location_country as originCountry,
            pc.id as punchConfigId,
            pc.punch_config_no as punchConfigNo,
            pc.punch_config_name as punchConfigName,
            pc.punch_config_type as punchConfigType,
            hui.profile_photo_url as profilePhotoUrl
            from punch_config as pc
            inner join punch_config_range as cr on pc.id= cr.punch_config_id
            inner join hrms_user_info as hui on cr.biz_id=hui.id
            <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
                inner join calendar_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
                inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1 and ac.attendance_config_no = #{attendanceConfigNo}
            </if>
            where pc.is_delete=0 and pc.is_latest=1
            and cr.is_delete=0 and cr.is_latest=1
            and hui.is_delete=0 and hui.status="ACTIVE" and
            hui.work_status="ON_JOB" and hui.user_code is not null and employee_type = 'OSFixedsalary'
            <foreach collection="vendorCodeList" separator="," open="and hui.vendor_code in (" close=")"
                     item="vendorCode">
                #{vendorCode}
            </foreach>
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="postId !=null">
                and hui.post_id = #{postId}
            </if>
            <if test="userCode!=null and userCode!=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="postIdList!=null and postIdList.size()>0">
                <foreach collection="postIdList" separator="," open="and hui.post_id in (" close=")" item="postId">
                    #{postId}
                </foreach>
            </if>
            <if test="driver !=null">
                and hui.is_driver = #{driver}
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="punchConfigIdList!=null and punchConfigIdList.size()>0">
                <foreach collection="punchConfigIdList" separator="," open="and pc.id in (" close=")" item="configId">
                    #{configId}
                </foreach>
            </if>
            <if test="punchConfigNo!=null and punchConfigNo!=''">
                and pc.punch_config_no = #{punchConfigNo}
            </if>
            <if test="isNeedPunch !=null">
                and cr.is_need_punch = #{isNeedPunch}
            </if>
        </if>
        <!--<include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
    </select>

</mapper>
