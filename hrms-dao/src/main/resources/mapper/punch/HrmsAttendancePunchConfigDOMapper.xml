<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.punch.mapper.HrmsAttendancePunchConfigMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="record_version" property="recordVersion"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="punch_config_no" property="punchConfigNo"/>
        <result column="punch_config_name" property="punchConfigName"/>
        <result column="punch_config_type" property="punchConfigType"/>
        <result column="punch_card_type" property="punchCardType"/>
        <result column="is_default" property="isDefault"/>
        <result column="overtime_config" property="overtimeConfig"/>
        <result column="max_repunch_days" property="maxRepunchDays"/>
        <result column="max_repunch_number" property="maxRepunchNumber"/>
        <result column="effect_time" property="effectTime"/>
        <result column="expire_time" property="expireTime"/>
        <result column="status" property="status"/>
        <result column="dept_ids" property="deptIds"/>
        <result column="principal_user_code" property="principalUserCode"/>
        <result column="sub_user_codes" property="subUserCodes"/>
        <result column="is_latest" property="isLatest"/>
        <result column="orderby" property="orderby"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">

        is_delete
        ,
                record_version,
                create_date,
                create_user_code,
                create_user_name,
                last_upd_date,
                last_upd_user_code,
                last_upd_user_name,
                id, punch_config_no, punch_config_name, punch_config_type, punch_card_type, is_default, overtime_config, max_repunch_days,
                max_repunch_number, effect_time, expire_time, status, dept_ids, principal_user_code, sub_user_codes, is_latest, orderby

    </sql>

    <select id="selectPunchConfigName" resultMap="BaseResultMap">
        select punch_config_no,punch_config_name,is_default
        from hrms_attendance_punch_config
        <where>
            is_delete = 0 and is_latest = 1
            <if test="punchNoList != null and punchNoList.size()>0">
                <foreach collection="punchNoList" item="item" open="and punch_config_no in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <select id="selectPunchConfigList" resultMap="BaseResultMap">
        select *
        from hrms_attendance_punch_config
        where is_delete=0 and is_latest=1
        <if test="countryList != null and countryList.size()>0">
            <foreach collection="countryList" item="item" open="and country in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="isDefault != null">
            AND is_default = #{isDefault}
        </if>
        <if test="country != null and country != ''">
            AND country = #{country}
        </if>
        <if test="punchConfigName != null and punchConfigName != ''">
            AND punch_config_name like CONCAT('%',#{punchConfigName},'%')
        </if>
        and id in
        (
            select punch_config_id
            from hrms_attendance_punch_config_range
            where is_delete=0 and is_latest=1
            <if test="userIdList != null and userIdList.size()>0">
                <foreach collection="userIdList" item="item" open="and biz_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        )

        <if test="createDateSort == null and lastUpdDateSort == null">
            order by create_date asc
        </if>
        <if test="createDateSort != null and createDateSort == 0 and lastUpdDateSort == null">
            order by create_date asc
        </if>
        <if test="createDateSort != null and createDateSort == 1 and lastUpdDateSort == null">
            order by create_date desc
        </if>
        <if test="createDateSort == null and lastUpdDateSort != null and lastUpdDateSort == 0">
            order by last_upd_date asc
        </if>
        <if test="createDateSort == null and lastUpdDateSort != null and lastUpdDateSort == 1">
            order by last_upd_date desc
        </if>
        <if test="createDateSort != null and lastUpdDateSort != null and createDateSort == 0 and lastUpdDateSort == 0">
            order by create_date asc, last_upd_date asc
        </if>
        <if test="createDateSort != null and lastUpdDateSort != null and createDateSort == 1 and lastUpdDateSort == 0">
            order by create_date desc, last_upd_date asc
        </if>
        <if test="createDateSort != null and lastUpdDateSort != null and createDateSort == 0 and lastUpdDateSort == 1">
            order by create_date asc, last_upd_date desc
        </if>
        <if test="createDateSort != null and lastUpdDateSort != null and createDateSort == 1 and lastUpdDateSort == 1">
            order by create_date desc, last_upd_date desc
        </if>
    </select>



</mapper>
