<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.punch.mapper.AttendanceUserPunchRiskMapper">
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.punch.model.AttendanceUserPunchRiskDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_code" jdbcType="VARCHAR" property="userCode" />
        <result column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="post_id" jdbcType="BIGINT" property="postId" />
        <result column="analysis_start_day_id" jdbcType="BIGINT" property="analysisStartDayId" />
        <result column="analysis_end_day_id" jdbcType="BIGINT" property="analysisEndDayId" />
        <result column="lat_lng_repeat_times" jdbcType="INTEGER" property="latLngRepeatTimes" />
        <result column="lat_lng_repeat_days" jdbcType="VARCHAR" property="latLngRepeatDays" />
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_code, dept_id, post_id, analysis_start_day_id, analysis_end_day_id,
    lat_lng_repeat_times, lat_lng_repeat_days, risk_level, is_delete, record_version,
    create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code,
    last_upd_user_name
    </sql>
</mapper>