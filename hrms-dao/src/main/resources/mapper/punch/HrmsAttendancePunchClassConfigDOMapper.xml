<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.punch.mapper.HrmsAttendancePunchClassConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="punch_config_id" property="punchConfigId" />
        <result column="class_name" property="className" />
        <result column="class_type" property="classType" />
        <result column="punch_in_time" property="punchInTime" />
        <result column="punch_out_time" property="punchOutTime" />
        <result column="earliest_punch_in_time" property="earliestPunchInTime" />
        <result column="latest_punch_out_time" property="latestPunchOutTime" />
        <result column="is_across" property="isAcross" />
        <result column="punch_time_interval" property="punchTimeInterval" />
        <result column="status" property="status" />
        <result column="is_latest" property="isLatest" />
        <result column="orderby" property="orderby" />
        <result column="punch_in_time_interval" property="punchInTimeInterval" />
        <result column="attendance_hours" property="attendanceHours" />
        <result column="legal_working_hours" property="legalWorkingHours"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, punch_config_id, class_name, class_type, punch_in_time, punch_out_time, earliest_punch_in_time, latest_punch_out_time, is_across, punch_time_interval, status, is_latest, orderby, punch_in_time_interval, attendance_hours, legal_working_hours
    </sql>

</mapper>
