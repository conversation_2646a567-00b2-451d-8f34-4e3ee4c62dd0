<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.punch.mapper.HrmsAttendanceMobileConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.punch.model.HrmsAttendanceMobileConfigDO">
        <!--@mbg.generated-->
        <!--@Table hrms.hrms_attendance_mobile_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="user_code" jdbcType="VARCHAR" property="userCode"/>
        <result column="mobile_unicode" jdbcType="VARCHAR" property="mobileUnicode"/>
        <result column="mobile_model" jdbcType="VARCHAR" property="mobileModel"/>
        <result column="mobile_branch" jdbcType="VARCHAR" property="mobileBranch"/>
        <result column="mobile_version" jdbcType="VARCHAR" property="mobileVersion"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        user_code,
        mobile_unicode,
        mobile_model,
        mobile_branch,
        mobile_version
    </sql>

    <select id="queryAttendanceMobileConfig"
            parameterType="com.imile.hrms.dao.punch.query.AttendanceMobileConfigListQuery"
            resultType="com.imile.hrms.dao.punch.dto.AttendanceMobileConfigListDTO">
        select hapcr.punch_config_id,
               hapcr.punch_config_no,
               hapcr.biz_id as user_id,
               hui.user_code,
               hui.user_name,
               hui.location_country,
               hui.user_name_en,
               hui.dept_id,
               hamc.id      as mobile_config_id,
               hamc.mobile_unicode,
               hamc.mobile_model,
               hamc.mobile_branch,
               hamc.mobile_version,
               hamc.create_date,
               hamc.create_user_code,
               hamc.create_user_name,
               hamc.last_upd_date,
               hamc.last_upd_user_code,
               hamc.last_upd_user_name

        from hrms_attendance_punch_config hapc
                 inner join hrms_attendance_punch_config_range hapcr
                            on (hapc.id = hapcr.punch_config_id and hapcr.is_delete = 0 and hapcr.is_latest = 1)
                 left join hrms_user_info hui
                           on (hapcr.biz_id = hui.id and hui.user_code is not null and hui.is_delete = 0 and
                               hui.status = 'ACTIVE' and hui.work_status = 'ON_JOB')
                 left join hrms_attendance_mobile_config hamc on (hamc.is_delete = 0 and hamc.user_code = hui.user_code)
        where hapc.is_delete = 0
          and hapc.is_latest = 1
          and (FIND_IN_SET(#{loginUserCode}, hapc.principal_user_code) or
               FIND_IN_SET(#{loginUserCode}, hapc.sub_user_codes))
        <if test="country != null and country != ''">
            and hapc.country = #{country}
        </if>
        <if test="userCodeOrName != null and userCodeOrName != ''">
            and (hui.user_code like concat('%', #{userCodeOrName}, '%') or hui.user_name like
                                                                           concat('%', #{userCodeOrName}, '%') or
                 hui.user_name_en like concat('%', #{userCodeOrName}, '%')
                or hui.sys_account_name like concat('%', #{userCodeOrName}, '%'))
        </if>
    </select>

    <select id="queryMobileConfigCountry" resultType="java.lang.String">
        select distinct country
        from hrms_attendance_punch_config
        where is_delete = 0
        and is_latest = 1
        and (FIND_IN_SET(#{userCode}, principal_user_code) or
        FIND_IN_SET(#{userCode}, sub_user_codes))
    </select>


    <select id="queryAttendanceMobileConfigNew"
            parameterType="com.imile.hrms.dao.punch.query.AttendanceMobileConfigListQuery"
            resultType="com.imile.hrms.dao.punch.dto.AttendanceMobileConfigListDTO">
        select hapcr.punch_config_id,
        hapcr.punch_config_no,
        hapcr.biz_id as user_id,
        hui.user_code,
        hui.user_name,
        hui.location_country,
        hui.user_name_en,
        hui.dept_id,
        hamc.id      as mobile_config_id,
        hamc.mobile_unicode,
        hamc.mobile_model,
        hamc.mobile_branch,
        hamc.mobile_version,
        hamc.create_date,
        hamc.create_user_code,
        hamc.create_user_name,
        hamc.last_upd_date,
        hamc.last_upd_user_code,
        hamc.last_upd_user_name

        from punch_config hapc
        inner join punch_config_range hapcr
        on (hapc.id = hapcr.punch_config_id and hapcr.is_delete = 0 and hapcr.is_latest = 1)
        left join hrms_user_info hui
        on (hapcr.biz_id = hui.id and hui.user_code is not null and hui.is_delete = 0 and
        hui.status = 'ACTIVE' and hui.work_status = 'ON_JOB')
        left join hrms_attendance_mobile_config hamc on (hamc.is_delete = 0 and hamc.user_code = hui.user_code)
        where hapc.is_delete = 0
        and hapc.is_latest = 1
        and (FIND_IN_SET(#{loginUserCode}, hapc.principal_user_code) or
        FIND_IN_SET(#{loginUserCode}, hapc.sub_user_codes))
        <if test="country != null and country != ''">
            and hapc.country = #{country}
        </if>
        <if test="userCodeOrName != null and userCodeOrName != ''">
            and (hui.user_code like concat('%', #{userCodeOrName}, '%') or hui.user_name like
            concat('%', #{userCodeOrName}, '%') or
            hui.user_name_en like concat('%', #{userCodeOrName}, '%')
            or hui.sys_account_name like concat('%', #{userCodeOrName}, '%'))
        </if>
    </select>


    <select id="queryMobileConfigCountryNew" resultType="java.lang.String">
        select distinct country
        from punch_config
        where is_delete = 0
          and is_latest = 1
          and (FIND_IN_SET(#{userCode}, principal_user_code) or
               FIND_IN_SET(#{userCode}, sub_user_codes))
    </select>
</mapper>