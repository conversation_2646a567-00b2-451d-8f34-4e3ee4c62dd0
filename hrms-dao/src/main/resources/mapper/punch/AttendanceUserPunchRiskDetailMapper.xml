<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.punch.mapper.AttendanceUserPunchRiskDetailMapper">
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.punch.model.AttendanceUserPunchRiskDetailDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_punch_risk_id" jdbcType="BIGINT" property="userPunchRiskId" />
        <result column="user_code" jdbcType="VARCHAR" property="userCode" />
        <result column="day_id" jdbcType="BIGINT" property="dayId" />
        <result column="employee_punch_record_id" jdbcType="BIGINT" property="employeePunchRecordId" />
        <result column="punch_time" jdbcType="TIMESTAMP" property="punchTime" />
        <result column="longitude" jdbcType="DECIMAL" property="longitude" />
        <result column="latitude" jdbcType="DECIMAL" property="latitude" />
        <result column="gps_config_id" jdbcType="BIGINT" property="gpsConfigId" />
        <result column="gps_config_name" jdbcType="VARCHAR" property="gpsConfigName" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_punch_risk_id, user_code, day_id, employee_punch_record_id, punch_time,
    longitude, latitude, gps_config_id, gps_config_name, is_delete, record_version,
    create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code,
    last_upd_user_name
    </sql>
</mapper>