<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.probation.mapper.UserProbationOperationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.probation.model.HrmsUserProbationOperationRecordDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_id" property="userId" />
        <result column="operation_content_cn" property="operationContentCn" />
        <result column="operation_content_en" property="operationContentEn" />
        <result column="operation_code" property="operationCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_id, operation_content_cn, operation_content_en, operation_code
    </sql>


    <select id="selectByCustomCondition" resultType="com.imile.hrms.dao.probation.po.UserProbationOperationRecordPO"
            parameterType="com.imile.hrms.dao.probation.condition.ProbationOperationRecordListCondition">
        select
        hui.user_code as userCode,
        hui.user_name as userName,
        hupor.id,
        hupor.user_id,
        hupor.operation_content_cn,
        hupor.operation_content_en,
        hupor.operation_content_cn,
        hupor.operation_code,
        hupor.create_date as operationDate,
        hupor.create_user_name as operator
        from hrms_user_probation_operation_record hupor
        left join hrms_user_info hui on hui.id = hupor.user_id and hui.is_delete = 0
        where hupor.is_delete = 0
        <if test=" userCodeOrName != null and userCodeOrName != '' ">
            and (
            hui.user_code like CONCAT('%',#{userCodeOrName},'%')
            or
            hui.user_name like CONCAT('%',#{userCodeOrName},'%')
            )
        </if>
        <if test=" operationCode != null and operationCode != '' ">
            and hupor.operation_code = #{operationCode}
        </if>

        <if test="operationStartDate != null">
            and hupor.create_date &gt;= #{operationStartDate}
        </if>
        <if test="operationEndDate != null">
            and hupor.create_date &lt;= #{operationEndDate}
        </if>
        order by hupor.create_date desc
    </select>

</mapper>
