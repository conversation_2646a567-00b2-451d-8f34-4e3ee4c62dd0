<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.primary.mapper.OrganizationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.primary.entity.OrganizationDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_name" property="organizationName" />
        <result column="organization_alias" property="organizationAlias" />
        <result column="organization_type" property="organizationType" />
        <result column="leader_user_code" property="leaderUserCode" />
        <result column="organization_status" property="organizationStatus" />
        <result column="organization_country" property="organizationCountry" />
        <result column="organization_province" property="organizationProvince" />
        <result column="organization_city" property="organizationCity" />
        <result column="organization_district" property="organizationDistrict" />
        <result column="organization_address" property="organizationAddress" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, organization_code, organization_name, organization_alias, organization_type, leader_user_code, organization_status, organization_country, organization_province, organization_city, organization_district, organization_address
    </sql>

</mapper>
