<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.salary.mapper.HrmsSalaryEmployeeDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeDetailDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_id" property="userId" />
        <result column="salary_employee_config_id" property="salaryEmployeeConfigId" />
        <result column="attendance_day_cnt" property="attendanceDayCnt" />
        <result column="salary_day_cnt" property="salaryDayCnt" />
        <result column="base_salary" property="baseSalary" />
        <result column="salary" property="salary" />
        <result column="attendance_salary" property="attendanceSalary" />
        <result column="workday_overtime_pay" property="workdayOvertimePay" />
        <result column="offDay_overtime_pay" property="offDayOvertimePay" />
        <result column="holiday_overtime_pay" property="holidayOvertimePay" />
        <result column="dld_cnt" property="dldCnt" />
        <result column="dld_salary" property="dldSalary" />
        <result column="person_social_and_accumulation_pay" property="personSocialAndAccumulationPay" />
        <result column="org_social_and_accumulation_pay" property="orgSocialAndAccumulationPay" />
        <result column="incentive_salary" property="incentiveSalary" />
        <result column="other_increase_salary" property="otherIncreaseSalary" />
        <result column="other_decrease_salary" property="otherDecreaseSalary" />
        <result column="visa_pay" property="visaPay" />
        <result column="medicare_pay" property="medicarePay" />
        <result column="airTicket_pay" property="airticketPay" />
        <result column="service_pay" property="servicePay" />
        <result column="other_pay" property="otherPay" />
        <result column="taxation_pay" property="taxationPay" />
        <result column="employee_gross_pay" property="employeeGrossPay" />
        <result column="org_total_pay" property="orgTotalPay" />
        <result column="cycle_type" property="cycleType" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="orderby" property="orderby" />
        <result column="salary_date" property="salaryDate"/>
        <result column="need_attendance_day_cnt" property="needAttendanceDayCnt"/>
        <result column="currency" property="currency"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id,salary_date,user_id,currency, need_attendance_day_cnt,salary_employee_config_id, attendance_day_cnt, salary_day_cnt, base_salary, salary, attendance_salary, overTime_pay, dld_cnt, dld_salary, person_social_and_accumulation_pay, org_social_and_accumulation_pay, incentive_salary, other_increase_salary, other_decrease_salary, visa_pay, medicare_pay, airTicket_pay, service_pay, other_pay, employee_gross_pay, org_total_pay, cycle_type, start_time, end_time, orderby
    </sql>

    <select id="list"
            parameterType="com.imile.hrms.dao.salary.query.SalaryEmployeeDetailQuery"
            resultType="com.imile.hrms.dao.salary.dto.SalaryEmployeeDetailDTO">
        select
        hsed.id as id,
        hui.id as userId,
        hui.work_no as workNo,
        hui.user_code as userCode,
        hui.vendor_name as vendorName,
        hui.employee_type as employeeType,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.origin_country as country,
        hsed.attendance_day_cnt as attendanceDayCnt,
        hsed.salary_day_cnt as salaryDayCnt,
        hsed.base_salary as baseSalary,
        hsed.salary as salary,
        hsed.need_attendance_day_cnt as needAttendanceDayCnt,
        hsed.attendance_salary as attendanceSalary,
        hsed.workday_overtime_pay as workdayOvertimePay,
        hsed.offDay_overtime_pay as offDayOvertimePay,
        hsed.holiday_overtime_pay as holidayOvertimePay,
        hsed.dld_cnt as dldCnt,
        hsed.dld_salary as dldSalary,
        hsed.person_social_and_accumulation_pay as personSocialAndAccumulationPay,
        hsed.org_social_and_accumulation_pay as orgSocialAndAccumulationPay,
        hsed.incentive_salary as incentiveSalary,
        hsed.other_increase_salary as otherIncreaseSalary,
        hsed.other_decrease_salary as otherDecreaseSalary,
        hsed.visa_pay as visaPay,
        hsed.medicare_pay as medicarePay,
        hsed.airticket_pay as airticketPay,
        hsed.taxation_pay as taxationPay,
        hsed.service_pay as servicePay,
        hsed.other_pay as otherPay,
        hsed.employee_gross_pay as employeeGrossPay,
        hsed.org_total_pay as orgTotalPay,
        hsed.start_time as startTime,
        hsed.end_time as endTime,
        hsed.currency as currency,
        hsed.accrued_pay as accruedPay,
        hsed.taxation as taxation,
        hsed.last_upd_date as lastUpdDate,
        hsed.last_upd_user_name as lastUpdUserName
        from
        hrms_salary_employee_detail hsed
        inner join hrms_user_info hui on hsed.user_id = hui.id and hui.is_virtual = 0 and hui.employee_type in ('INTERNAL','Employee','SubEmployee','SubFormal')
        and hui.work_status is not null
        <where>
            hsed.is_delete = 0
            <if test="userCode!=null and userCode!=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and hui.employee_type = #{employeeType}
            </if>
            <if test="vendorId!=null">
                and hui.vendor_id = #{vendorId}
            </if>
            <if test="cycleType!=null and cycleType!=''">
                and hsed.cycle_type = #{cycleType}
            </if>
            <if test="country!=null and country!=''">
                and hui.origin_country = #{country}
            </if>
            <if test="deptIds !=null and deptIds.size()>0">
                <foreach collection="deptIds" item="deptId" open="and hui.dept_id in (" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.dataSearchTypeSql"></include>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        </where>
    </select>

    <sql id="base_sql">
        hsed.id as id,
        hui.id as userId,
        hui.work_no as workNo,
        hui.vendor_name as vendorName,
        hui.employee_type as employeeType,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hed.dept_name_cn as deptNameCn,
        hed.dept_name_en as deptNameEn,
        hep.post_name_cn as postNameCn,
        hep.post_name_en as postNameEn,
        hsed.attendance_day_cnt as attendanceDayCnt,
        hsed.salary_day_cnt as salaryDayCnt,
        hsed.base_salary as baseSalary,
        hsed.salary as salary,
        hsed.need_attendance_day_cnt as needAttendanceDayCnt,
        hsed.attendance_salary as attendanceSalary,
        hsed.workday_overtime_pay as workdayOvertimePay,
        hsed.offDay_overtime_pay as offDayOvertimePay,
        hsed.holiday_overtime_pay as holidayOvertimePay,
        hsed.dld_cnt as dldCnt,
        hsed.dld_salary as dldSalary,
        hsed.person_social_and_accumulation_pay as personSocialAndAccumulationPay,
        hsed.org_social_and_accumulation_pay as orgSocialAndAccumulationPay,
        hsed.incentive_salary as incentiveSalary,
        hsed.other_increase_salary as otherIncreaseSalary,
        hsed.other_decrease_salary as otherDecreaseSalary,
        hsed.visa_pay as visaPay,
        hsed.medicare_pay as medicarePay,
        hsed.airticket_pay as airticketPay,
        hsed.taxation_pay as taxationPay,
        hsed.service_pay as servicePay,
        hsed.other_pay as otherPay,
        hsed.employee_gross_pay as employeeGrossPay,
        hsed.org_total_pay as orgTotalPay,
        hsed.start_time as startTime,
        hsed.end_time as endTime,
        hsed.currency as currency,
        hsed.accrued_pay as accruedPay,
        hsed.taxation as taxation
    </sql>


    <select id="listBySalaryEmployeeConfigId"
            parameterType="com.imile.hrms.dao.salary.query.SalaryEmployeeDetailQuery"
            resultType="com.imile.hrms.dao.salary.dto.SalaryEmployeeDetailDTO">
        select
        <include refid="base_sql"></include>
        from
            hrms_salary_employee_detail hsed
                inner join hrms_user_info hui on hsed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null
                left join hrms_ent_dept hed on hui.dept_id = hed.id and hed.is_delete =0 and hed.status = 'ACTIVE'
                left join hrms_ent_post hep on hui.post_id = hep.id and hep.is_delete =0 and hep.status = 'ACTIVE'
        where
            hsed.is_delete = 0
            and hsed.start_time = #{startTime}
            and hsed.end_time = #{endTime}
            and hsed.salary_employee_config_id in
            <foreach collection="salaryEmployeeConfigIds" item="salaryEmployeeConfig" separator="," open="(" close=")">
                #{salaryEmployeeConfig}
            </foreach>
    </select>

    <select id="getTotalAccruedPay"
            parameterType="com.imile.hrms.dao.salary.query.SalaryEmployeeDetailQuery"
            resultType="java.math.BigDecimal">
        select
        sum(hsed.accrued_pay)
        from
        hrms_salary_employee_detail hsed
        inner join hrms_user_info hui on hsed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null
        <where>
            hsed.is_delete = 0
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" item="deptId" open="and hui.dept_id in (" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="postId!=null">
                and hui.post_id = #{postId}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and hui.employee_type = #{employeeType}
            </if>
            <if test="vendorId!=null">
                and hui.vendor_id = #{vendorId}
            </if>
            <if test="cycleType!=null and cycleType!=''">
                and hsed.cycle_type = #{cycleType}
            </if>



            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.dataSearchTypeSql"></include>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        </where>
    </select>

    <sql id="human_cost_sql">
        hsed.id as id,
        hui.id as userId,
        hui.work_no as workNo,
        hui.user_code as userCode,
        hui.origin_country as country,
        hui.vendor_name as vendorName,
        hui.employee_type as employeeType,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.company_id as companyId,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hsed.attendance_salary as attendanceSalary,
        hsed.workday_overtime_pay as workdayOvertimePay,
        hsed.offDay_overtime_pay as offDayOvertimePay,
        hsed.holiday_overtime_pay as holidayOvertimePay,
        hsed.dld_salary as dldSalary,
        hsed.org_social_and_accumulation_pay as orgSocialAndAccumulationPay,
        hsed.incentive_salary as incentiveSalary,
        hsed.other_increase_salary as otherIncreaseSalary,
        hsed.visa_pay as visaPay,
        hsed.medicare_pay as medicarePay,
        hsed.airticket_pay as airticketPay,
        hsed.service_pay as servicePay,
        hsed.taxation_pay as taxationPay,
        hsed.other_pay as otherPay,
        hsed.employee_gross_pay as employeeGrossPay,
        hsed.org_total_pay as orgTotalPay,
        hsed.start_time as startTime,
        hsed.end_time as endTime,
        hsed.currency as currency,
        hsed.last_upd_date as lastUpdDate,
        hsed.last_upd_user_name as lastUpdUserName
    </sql>

    <select id="listHumanCost"
            parameterType="com.imile.hrms.dao.salary.query.HumanCostStatisticsQuery"
            resultType="com.imile.hrms.dao.salary.dto.SalaryEmployeeDetailDTO">
        select * from (
        select
        <include refid="human_cost_sql"></include>
        from
        hrms_salary_employee_detail hsed
        inner join hrms_user_transfer_record hutr on hutr.user_id = hsed.user_id and hutr.is_delete = 0
        inner join hrms_user_info hui on hsed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null
        where hsed.is_delete = 0
        <if test="workNo!=null and workNo!=''">
            and hui.work_no = #{workNo}
        </if>
        <if test="userNameOrEmail!=null and userNameOrEmail!=''">
            and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
            concat('%',#{userNameOrEmail},'%'))
        </if>
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
            concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
            or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            <foreach collection="deptIds" item="deptId" open="and hutr.before_dept_id in (" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="postId!=null">
            and hutr.before_post_id = #{postId}
        </if>

        <if test="employeeType!=null and employeeType!=''">
            and hui.employee_type = #{employeeType}
        </if>
        <if test="vendorId!=null">
            and hui.vendor_id = #{vendorId}
        </if>
        <if test="cycleType!=null and cycleType!=''">
            and hsed.cycle_type = #{cycleType}
        </if>
        <if test="country!=null and country!=''">
            and hui.origin_country = #{country}
        </if>
        union

        select
        <include refid="human_cost_sql"></include>
        from
        hrms_salary_employee_detail hsed
        inner join hrms_user_info hui on hsed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null
        <where>
            hsed.is_delete = 0
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" item="deptId" open="and hui.dept_id in (" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="postId!=null">
                and hui.post_id = #{postId}
            </if>

            <if test="employeeType!=null and employeeType!=''">
                and hui.employee_type = #{employeeType}
            </if>
            <if test="vendorId!=null">
                and hui.vendor_id = #{vendorId}
            </if>
            <if test="cycleType!=null and cycleType!=''">
                and hsed.cycle_type = #{cycleType}
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        </where>
        ) hrms
        <where>
            <if test="startTime!=null">
                and hrms.startTime &gt;= #{startTime}
            </if>
            <if test="endTime!=null">
                and hrms.endTime &lt;= #{endTime}
            </if>
        </where>
    </select>


    <select id="getToatalHumanCost"
            parameterType="com.imile.hrms.dao.salary.query.HumanCostStatisticsQuery"
            resultType="java.math.BigDecimal">
        select
        sum(hsed.orgTotalPay)
        from (
        select * from (
        select
        hsed.org_total_pay as orgTotalPay,
        hsed.start_time as startTime,
        hsed.end_time as endTime
        from
        hrms_salary_employee_detail hsed
        inner join hrms_user_transfer_record hutr on hutr.user_id = hsed.user_id and hutr.before_company_id = hsed.company_id and hutr.is_delete = 0
        inner join hrms_user_info hui on hsed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null
        where hsed.is_delete = 0
        <if test="workNo!=null and workNo!=''">
            and hui.work_no = #{workNo}
        </if>
        <if test="userNameOrEmail!=null and userNameOrEmail!=''">
            and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like concat('%',#{userNameOrEmail},'%'))
        </if>
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
            concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
            or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            <foreach collection="deptIds" item="deptId" open="and hutr.before_dept_id in (" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="postId!=null">
            and hutr.before_post_id = #{postId}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and hui.employee_type = #{employeeType}
        </if>
        <if test="vendorId!=null">
            and hui.vendor_id = #{vendorId}
        </if>
        <if test="cycleType!=null and cycleType!=''">
            and hsed.cycle_type = #{cycleType}
        </if>

        union

        select
        hsed.org_total_pay as orgTotalPay,
        hsed.start_time as startTime,
        hsed.end_time as endTime
        from
        hrms_salary_employee_detail hsed
        inner join hrms_user_info hui on hsed.user_id = hui.id and hui.company_id = hsed.company_id and hui.is_delete = 0 and hui.work_status is not null
        <where>
            hsed.is_delete = 0
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" item="deptId" open="and hui.dept_id in (" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="postId!=null">
                and hui.post_id = #{postId}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and hui.employee_type = #{employeeType}
            </if>
            <if test="vendorId!=null">
                and hui.vendor_id = #{vendorId}
            </if>
            <if test="cycleType!=null and cycleType!=''">
                and hsed.cycle_type = #{cycleType}
            </if>
        </where>
        ) hrms
        <where>
            <if test="startTime!=null">
                and hrms.startTime &gt;= #{startTime}
            </if>
            <if test="endTime!=null">
                and hrms.endTime &lt;= #{endTime}
            </if>
        </where>
        ) hsed

    </select>

</mapper>
