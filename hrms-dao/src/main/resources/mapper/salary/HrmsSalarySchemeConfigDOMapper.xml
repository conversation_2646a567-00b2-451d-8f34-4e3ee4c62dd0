<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.salary.mapper.HrmsSalarySchemeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.salary.model.HrmsSalarySchemeConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="scheme_no" property="schemeNo" />
        <result column="scheme_name" property="schemeName" />
        <result column="country" property="country" />
        <result column="currency" property="currency" />
        <result column="cost_org_id" property="costOrgId" />
        <result column="status" property="status" />
        <result column="cycle_type" property="cycleType" />
        <result column="cycle_info" property="cycleInfo" />
        <result column="item_info" property="itemInfo" />
        <result column="effect_time" property="effectTime" />
        <result column="expire_time" property="expireTime" />
        <result column="is_latest" property="isLatest" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, scheme_no, scheme_name, country, currency, cost_org_id, status, cycle_type, cycle_info, item_info, effect_time, expire_time, is_latest
    </sql>


    <select id="selectNoticeSchemeConfigList"
            parameterType="com.imile.hrms.dao.salary.query.SalarySchemeConfigQuery"
            resultType="com.imile.hrms.dao.salary.model.HrmsSalarySchemeConfigDO">
        select
        ssc.*
        from hrms_salary_message_notice as smn
        left join hrms_salary_scheme_config as ssc on ssc.scheme_no=smn.message_value
        where ssc.is_delete=0 and ssc.is_latest=1 and smn.is_delete = 0 and smn.is_notice = 1
        <if test="country!=null and country!=''">
            and ssc.country = #{country}
        </if>
        <if test="status!=null and status!=''">
            and ssc.status = #{status}
        </if>
        <if test="cycleType!=null and cycleType!=''">
            and ssc.cycle_type = #{cycleType}
        </if>
        <if test="schemeNameOrNo!=null and schemeNameOrNo!=''">
            and (ssc.scheme_no like CONCAT('%',#{schemeNameOrNo},'%')
            OR ssc.scheme_name like CONCAT('%',#{schemeNameOrNo},'%'))
        </if>
        <if test="messageKeyList!=null and messageKeyList.size()>0">
            <foreach collection="messageKeyList" item="messageKey" open="and smn.message_key in (" close=")"
                     separator=",">
                #{messageKey}
            </foreach>
        </if>
        order by ssc.create_date desc
    </select>

</mapper>
