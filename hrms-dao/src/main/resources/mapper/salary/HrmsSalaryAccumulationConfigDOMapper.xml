<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.salary.mapper.HrmsSalaryAccumulationConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.salary.model.HrmsSalaryAccumulationConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="accumulation_config_no" property="accumulationConfigNo" />
        <result column="country" property="country" />
        <result column="currency" property="currency" />
        <result column="base_config_item" property="baseConfigItem"/>
        <result column="config_items" property="configItems" />
        <result column="is_default" property="isDefault" />
        <result column="effect_time" property="effectTime" />
        <result column="expire_time" property="expireTime" />
        <result column="status" property="status" />
        <result column="is_latest" property="isLatest" />
        <result column="remark" property="remark" />
        <result column="orderby" property="orderby" />
    </resultMap>

    <resultMap id="DTOResultMap" type="com.imile.hrms.dao.salary.dto.SalaryAccumulationConfigDetailDTO">
        <result column="config_items" property="configItems"
                typeHandler="com.imile.hrms.dao.mybatis.handler.ConfigItemCollectionTypeHandler"/>
        <result column="base_config_item" property="baseConfigItem"
                typeHandler="com.imile.hrms.dao.mybatis.handler.GenericObjectTypeHandler"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, company_id, accumulation_config_no, accumulation_config_name, country, currency, base_config_item, config_items, is_default, effect_time, expire_time, status, is_latest, remark, orderby
    </sql>

    <select id="getAccumulationConfig"
            parameterType="com.imile.hrms.dao.salary.query.SocialAndAccumulationQuery"
            resultMap="DTOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hrms_salary_accumulation_config hsac
        <where>
            hsac.is_delete = 0
            <if test="configNo!=null and configNo!=''">
                and hsac.accumulation_config_no = #{configNo}
            </if>
            <if test="isLatest!=null">
                and hsac.is_latest = #{isLatest}
            </if>
            <if test="country!=null">
                and hsac.country = #{country}
            </if>
            <if test="id != null">
                and hsac.id = #{id}
            </if>

        </where>
    </select>



</mapper>
