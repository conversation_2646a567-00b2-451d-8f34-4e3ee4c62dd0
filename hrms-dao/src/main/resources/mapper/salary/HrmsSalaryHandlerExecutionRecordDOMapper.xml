<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.salary.mapper.HrmsSalaryHandlerExecutionRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.salary.model.HrmsSalaryHandlerExecutionRecordDO">
        <id column="id" property="id" />
        <result column="salary_task_config_id" property="salaryTaskConfigId" />
        <result column="payment_month" property="paymentMonth" />
        <result column="scheme_no" property="schemeNo" />
        <result column="source" property="source" />
        <result column="payment_country" property="paymentCountry" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, salary_task_config_id, payment_month, scheme_no, source, payment_country
    </sql>

</mapper>
