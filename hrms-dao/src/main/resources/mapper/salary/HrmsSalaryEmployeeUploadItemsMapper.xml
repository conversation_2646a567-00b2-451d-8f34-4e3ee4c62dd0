<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.salary.mapper.HrmsSalaryEmployeeUploadItemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeUploadItemsDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="record_version" property="recordVersion"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="country" property="country"/>
        <result column="user_id" property="userId"/>
        <result column="salary_date" property="salaryDate"/>
        <result column="incentive_salary" property="incentiveSalary"/>
        <result column="other_increase_salary" property="otherIncreaseSalary"/>
        <result column="other_decrease_salary" property="otherDecreaseSalary"/>
        <result column="remark" property="remark"/>
        <result column="is_latest" property="isLatest"/>
        <result column="orderby" property="orderby"/>
        <result column="taxation" property="taxation"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete
        ,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,taxation,
        id, country, user_id, salary_date, incentive_salary, other_increase_salary, other_decrease_salary, remark, is_latest, orderby
    </sql>

    <select id="list"
            parameterType="com.imile.hrms.dao.salary.query.SalaryEmployeeUploadItemQuery"
            resultType="com.imile.hrms.dao.salary.dto.SalaryEmployeeUploadItemDTO">
        select
        hseui.id as id,
        hseui.salary_date as salaryDate,
        hui.work_no as workNo,
        hui.user_code as userCode,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.origin_country as country,
        hui.email as email,
        hed.dept_name_cn as deptNameCn,
        hed.dept_name_en as deptNameEn,
        hed.organization_code as organizationCode,
        hep.post_name_cn as postNameCn,
        hep.post_name_en as postNameEn,
        hseui.incentive_salary as incentiveSalary,
        hseui.other_increase_salary as otherIncreaseSalary,
        hseui.other_decrease_salary as otherDecreaseSalary,
        hseui.taxation as taxation,
        hseui.remark as remark,
        hseui.create_date as uploadDate,
        hseui.create_user_name as operatorUserName,
        hseui.last_upd_date as lastUpdDate,
        hseui.last_upd_user_name as lastUpdUserName

        from hrms_salary_employee_upload_items hseui
        inner join hrms_user_info hui on hseui.user_id = hui.id and hui.is_delete = 0 and hui.status = 'ACTIVE'
        left join hrms_ent_dept hed on hui.dept_id = hed.id and hed.is_delete = 0 and hed.status = 'ACTIVE'
        left join hrms_ent_post hep on hui.post_id = hep.id and hep.is_delete = 0 and hep.status = 'ACTIVE'
        <where>
            hseui.is_delete = 0
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" item="deptId" open="and hui.dept_id in (" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="postId!=null">
                and hui.post_id = #{postId}
            </if>
            <if test="uploadUserCode!=null and uploadUserCode!=''">
                and hseui.create_user_code = #{uploadUserCode}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="country!=null and country!=''">
                and hseui.country = #{country}
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.dataSearchTypeSql"></include>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        </where>


    </select>

</mapper>
