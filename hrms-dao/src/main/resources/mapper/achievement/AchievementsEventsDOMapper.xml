<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.achievement.mapper.AchievementsEventsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.achievement.model.AchievementsEventsDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="record_version" property="recordVersion" />
        <result column="event_name" property="eventName" />
        <result column="year" property="year" />
        <result column="cycle_type" property="cycleType" />
        <result column="effect_time" property="effectTime" />
        <result column="expire_time" property="expireTime" />
        <result column="event_scope" property="eventScope" />
        <result column="userIds" property="userIds" />
        <result column="status" property="status" />
        <result column="extend" property="extend" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, event_name, year, cycle_type, effect_time, expire_time, event_scope, userIds, status, extend
    </sql>
    <select id="checkAeNumByUserIdAndName" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            achievements_events ae
            INNER JOIN achievements_events_user aeu ON ae.id = aeu.achievements_events_id
        WHERE

            ae.event_name = #{dto.eventName}
            AND aeu.use_id = #{dto.userId}

            AND ae.is_delete = '0'
            AND aeu.is_delete = '0'
            <if test="dto.id != null and  dto.id != ''">
                and ae.id != #{dto.id}
            </if>
            <if test="dto.eventScope != null and  dto.eventScope != ''">
                and aeu.scope_type = #{dto.eventScope}
            </if>
            <if test="dto.cycleType != null and  dto.cycleType != ''">
                AND ae.cycle_type = #{dto.cycleType}
            </if>

    </select>
    <select id="listByUserId" resultType="com.imile.hrms.dao.achievement.dto.AchievementsEventsNameDTO">
        SELECT
            ae.id,
            aea.id employeeAppraisalId,
            ae.event_name,
            aea.current_appraisal_node,
            aea.appraisal_status,
            hui.user_code,
            ae.expire_time,
            ae.relate_achievement_org_id,
            ae.effect_time,
            ae.cycle_type,
            ae.year,
            ae.`status`
        FROM
            achievements_employee_appraisal aea
                INNER JOIN achievements_events ae ON ae.id = aea.event_id
                INNER JOIN hrms_user_info hui on hui.id = aea.user_id
        WHERE
            aea.is_delete = 0
          AND aea.user_id = #{userId}
          AND ae.is_delete = 0
          AND ae.is_examine = '01'
          AND aea.appraisal_status &gt;= 21
          order by ae.`status` asc,ae.last_upd_date desc
    </select>
    <select id="getEventIdByPrincipalUserId"
            resultType="com.imile.hrms.dao.achievement.model.AchievementsEventsDO">
        SELECT DISTINCT
            ae.*
        FROM
            `achievements_employee_appraisal_entrust` aeae
                INNER JOIN achievements_employee_appraisal aea ON aeae.employee_appraisal_id = aea.id AND aea.is_delete = '0'
                INNER JOIN achievements_events ae ON ae.id = aea.event_id AND ae.is_delete = '0'
        WHERE aeae.principal_user_id=#{userId} order by ae.`status` asc,ae.last_upd_date desc
    </select>

</mapper>
