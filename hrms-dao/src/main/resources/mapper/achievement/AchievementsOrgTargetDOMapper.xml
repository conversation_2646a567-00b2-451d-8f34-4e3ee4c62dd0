<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.achievement.mapper.AchievementsOrgTargetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.achievement.model.AchievementsOrgTargetDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="record_version" property="recordVersion" />
        <result column="event_id" property="eventId" />
        <result column="dept_id" property="deptId" />
        <result column="status" property="status" />
        <result column="effect_time" property="effectTime" />
        <result column="expire_time" property="expireTime" />
        <result column="extend" property="extend" />
        <result column="is_latest" property="isLatest" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, event_id, dept_id, status, effect_time, expire_time, extend, is_latest
    </sql>
    <select id="boardList" resultType="com.imile.hrms.dao.achievement.dto.TargetItemBoardListDTO">
        SELECT
            aoti.*,
            ( SELECT completion_rate FROM achievements_target_result WHERE target_item_id = aoti.id AND is_delete = '0' ORDER BY create_date DESC LIMIT 1 ) completionRate,
            ( SELECT completion_value FROM achievements_target_result WHERE target_item_id = aoti.id AND is_delete = '0' ORDER BY create_date DESC LIMIT 1 ) completion_value,
	        ( SELECT count( * ) FROM achievements_org_target_relation WHERE is_delete = '0' AND parent_id = aoti.id ) childCount
        FROM
            `achievements_org_target_relation` aotr
            INNER JOIN achievements_org_target_item aoti ON aotr.target_item_id = aoti.id
        WHERE
            aotr.is_delete = '0'
          AND aoti.is_delete = '0'
          AND aotr.parent_id = #{parentId}
    </select>

    <select id="deptByUser" resultType="com.imile.hrms.dao.achievement.dto.EntDeptDTO">
        SELECT
            hed.*
        FROM
            hrms_ent_dept hed
                INNER JOIN achievements_org_target aot ON aot.dept_id=hed.id
        WHERE
            hed.leader_code = #{userId}
          AND aot.event_id = #{eventId}
          AND aot.`status` = 1
          AND aot.is_delete = 0
          AND hed.is_delete =0
    </select>
    <select id="selectByDeptId" resultType="com.imile.hrms.dao.achievement.po.AchievementDeptPO">
        select t.id, e.id as eventId ,e.event_name
        from achievements_org_target t
                 join achievements_events e on t.event_id = e.id and e.is_delete = 0
        where t.dept_id = #{deptId}
          and t.is_delete = 0
          and e.status in (1, 2)
        order by t.create_date desc
        limit 1
    </select>
    <select id="selectListByEventId" resultType="com.imile.hrms.dao.achievement.model.AchievementsOrgTargetDO"
            parameterType="com.imile.hrms.dao.achievement.dto.AchievementsTargetCheckDTO">
        SELECT
            *
        FROM
            `achievements_org_target` aot
             INNER JOIN hrms_ent_dept hed ON hed.id = aot.dept_id
        WHERE
            aot.is_delete = 0
          AND hed.is_delete = 0
          AND aot.event_id = #{dto.eventId}
        <if test="dto.deptName != null and dto.deptName != ''">
            AND (
            hed.dept_name_cn LIKE concat('%',#{dto.deptName},'%')
            OR (hed.dept_name_en LIKE concat('%',#{dto.deptName},'%'))
            )
        </if>
        <if test="dto.deptIdList !=null and dto.deptIdList.size()>0">
            <foreach collection="dto.deptIdList" item="id" open="and hed.id in (" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
