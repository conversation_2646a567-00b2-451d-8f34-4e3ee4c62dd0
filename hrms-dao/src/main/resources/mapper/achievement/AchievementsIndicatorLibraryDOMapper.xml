<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.achievement.mapper.AchievementsIndicatorLibraryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.achievement.model.AchievementsIndicatorLibraryDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="record_version" property="recordVersion" />
        <result column="target_name" property="targetName" />
        <result column="target_name_en" property="targetNameEn" />
        <result column="target_type" property="targetType" />
        <result column="kpi_type" property="kpiType" />
        <result column="target_properties" property="targetProperties" />
        <result column="metrics" property="metrics" />
        <result column="unit" property="unit" />
        <result column="is_complet" property="isComplet" />
        <result column="mapped_code" property="mappedCode" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
        <result column="extend" property="extend" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, target_name, target_name_en, target_type, kpi_type, target_properties, metrics, unit, is_complet, mapped_code, remark, type, extend
    </sql>

    <select id="mapperCodes" resultType="com.imile.hrms.dao.achievement.dto.MapperCodeDTO">
        SELECT
            aoti.indicator_library_id,
            ail.target_properties,
            ail.unit,
            ail.mapped_code
        FROM
            achievements_org_target_item aoti
                INNER JOIN achievements_indicator_library ail ON aoti.indicator_library_id = ail.id
        WHERE
            aoti.is_delete = '0'
          AND ail.is_delete = '0'
          AND aoti.target_id=#{id}
    </select>
    <select id="mapperCodesByEmployeeAppraisalId"
            resultType="com.imile.hrms.dao.achievement.dto.MapperCodeDTO">
        SELECT
            aoti.indicator_library_id,
            ail.target_properties,
            ail.unit,
            ail.mapped_code
        FROM
            achievements_org_target_item aoti
                INNER JOIN achievements_indicator_library ail ON aoti.indicator_library_id = ail.id
        WHERE
            aoti.is_delete = '0'
          AND ail.is_delete = '0'
          AND aoti.employee_appraisal_id=#{id}
    </select>


</mapper>
