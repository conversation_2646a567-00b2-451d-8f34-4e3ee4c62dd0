<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.achievement.mapper.AchievementsEmployeeAppraisalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.achievement.model.AchievementsEmployeeAppraisalDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="event_id" property="eventId" />
        <result column="user_id" property="userId" />
        <result column="appraisal_stage" property="appraisalStage" />
        <result column="appraisal_status" property="appraisalStatus" />
        <result column="current_appraisal_node" property="currentAppraisalNode" />
        <result column="appraiser_user_id" property="appraiserUserId" />
        <result column="exerciser_user_id" property="exerciserUserId" />
        <result column="appraisal_remark" property="appraisalRemark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, event_id, user_id, appraisal_stage, appraisal_status, current_appraisal_node, appraiser_user_id, exerciser_user_id, appraisal_remark
    </sql>
    <select id="listByDept" resultType="com.imile.hrms.dao.achievement.dto.EmployeeAppraisalGradeDTO">
        SELECT
            aea.id,
            hui.id userId,
            hui.user_name,
            hui.user_name_en,
            hui.job_grade,
            hui.grade_id,
            hui.grade_no,
            aea.appraisal_dept_level,
            aec.leader_rate
        FROM
            hrms_user_info hui
                INNER JOIN achievements_employee_appraisal aea ON aea.user_id = hui.id and aea.is_delete = 0
                INNER JOIN achievement_employee_conclusion aec ON aec.employee_appraisal_id = aea.id and aec.is_delete = 0
        WHERE
	        aea.appraisal_status &gt;= #{dto.status} and aea.event_id = #{dto.eventId} and hui.grade_no != ''
            <if test="dto.deptIdStr != null and  dto.deptIdStr != ''">
                AND FIND_IN_SET(aea.appraiser_dept_id,#{dto.deptIdStr})
            </if>
            <if test="dto.level != null and  dto.level != ''">
                AND aec.leader_rate=#{dto.level}
            </if>
    </select>
    <select id="employeeAppraisalConclusionList"
            resultType="com.imile.hrms.dao.achievement.dto.AchievementsEmployeeAppraisalConclusionDTO">
        SELECT
            aea.*,
            aec.leader_rate,
            aec.exerciser_conclusion,
            aec.exerciser_rank,
            aec.final_leader_score,
            aec.exerciser_remark
        FROM
            achievements_employee_appraisal aea
            LEFT JOIN achievement_employee_conclusion aec ON aec.employee_appraisal_id = aea.id  AND aec.is_delete = 0
        WHERE
            aea.event_id = #{query.eventId}  and aea.is_delete = 0
        <if test="query.exerciserConclusion!=null and query.exerciserConclusion!=''">
            and aec.exerciser_conclusion=#{query.exerciserConclusion}
        </if>
        <if test="query.appraisalExecutorId!=null and query.appraisalExecutorId!=''">
            and aea.appraisal_executor_id=#{query.appraisalExecutorId}
        </if>
        <if test="query.userIdList !=null and query.userIdList.size()>0">
            <foreach collection="query.userIdList" item="userId" open="and aea.user_id in (" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="getAchievementsConclusionSalary"
            resultType="com.imile.hrms.dao.achievement.dto.AchievementsConclusionSalaryDTO">
        SELECT
        hui.user_code,
        aea.id as employeeAppraisalId,
        aea.create_date,
        aea.appraisal_status,
        ae.`year`,
        ae.cycle_type,
        aec.leader_rate
        FROM
        achievements_employee_appraisal aea
        LEFT JOIN achievements_events ae ON ae.id = aea.event_id
        AND ae.is_delete = 0
        LEFT JOIN achievement_employee_conclusion aec ON aec.employee_appraisal_id = aea.id
        AND aec.is_delete = 0
        INNER JOIN hrms_user_info hui ON hui.id = aea.user_id
        WHERE
        hui.is_delete = 0 and hui.`status`='active' and hui.work_status='on_job'
        <if test="dto.userCodes!=null and dto.userCodes.size()>0">
            <foreach collection="dto.userCodes" item="id" separator="," open="and hui.user_code in (" close=")">
                #{id}
            </foreach>
        </if>

        <if test="dto.eventIds!=null and dto.eventIds.size()>0">
            <foreach collection="dto.eventIds" item="id" separator="," open="and aea.event_id in (" close=")">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
