<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.mapper.AchievementsOrgTargetItemTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.achievement.model.AchievementsOrgTargetItemTemplateDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="record_version" property="recordVersion" />
        <result column="target_id" property="targetId" />
        <result column="employee_appraisal_id" property="employeeAppraisalId" />
        <result column="kpi_type" property="kpiType" />
        <result column="sort" property="sort" />
        <result column="target_type" property="targetType" />
        <result column="unit" property="unit" />
        <result column="visibility" property="visibility" />
        <result column="parent_target_id" property="parentTargetId" />
        <result column="indicator_library_id" property="indicatorLibraryId" />
        <result column="weight" property="weight" />
        <result column="base_value" property="baseValue" />
        <result column="target_name" property="targetName" />
        <result column="target_value" property="targetValue" />
        <result column="challenge_value" property="challengeValue" />
        <result column="completion_rate" property="completionRate" />
        <result column="data_source_dept" property="dataSourceDept" />
        <result column="lead_duty_dept" property="leadDutyDept" />
        <result column="target_value_yoy_rate" property="targetValueYoyRate" />
        <result column="measurement_standard" property="measurementStandard" />
        <result column="completion_time" property="completionTime" />
        <result column="item_description" property="itemDescription" />
        <result column="extend" property="extend" />
        <result column="is_latest" property="isLatest" />
        <result column="item_description_en" property="itemDescriptionEn" />
        <result column="statistical_description" property="statisticalDescription" />
        <result column="statistical_description_en" property="statisticalDescriptionEn" />
        <result column="target_name_en" property="targetNameEn" />
        <result column="result_remark" property="resultRemark" />
        <result column="status" property="status" />
        <result column="qualitative_weight" property="qualitativeWeight" />
        <result column="quantify_weight" property="quantifyWeight" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, target_id, employee_appraisal_id, kpi_type, sort, target_type, unit, visibility, parent_target_id, indicator_library_id, weight, base_value, target_name, target_value, challenge_value, completion_rate, data_source_dept, lead_duty_dept, target_value_yoy_rate, measurement_standard, completion_time, item_description, extend, is_latest, item_description_en, statistical_description, statistical_description_en, target_name_en, result_remark, status, qualitative_weight, quantify_weight
    </sql>

</mapper>
