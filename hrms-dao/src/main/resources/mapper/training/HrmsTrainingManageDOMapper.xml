<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.training.mapper.HrmsTrainingManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.training.model.HrmsTrainingManageDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="training_topic" property="trainingTopic" />
        <result column="training_type" property="trainingType" />
        <result column="training_session" property="trainingSession" />
        <result column="date" property="date" />
        <result column="day_id" property="dayId" />
        <result column="training_start_time" property="trainingStartTime" />
        <result column="training_end_time" property="trainingEndTime" />
        <result column="dept_id" property="deptId" />
        <result column="trainer_code" property="trainerCode" />
        <result column="trainer_name" property="trainerName" />
        <result column="files" property="files" />
        <result column="status" property="status" />
        <result column="is_latest" property="isLatest" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, company_id, training_topic, training_type, training_session, date, day_id, training_start_time, training_end_time, dept_id, trainer_code, trainer_name, files, status, is_latest
    </sql>

</mapper>
