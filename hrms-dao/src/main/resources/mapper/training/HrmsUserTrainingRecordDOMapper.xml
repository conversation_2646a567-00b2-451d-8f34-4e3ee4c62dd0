<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.training.mapper.HrmsUserTrainingRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.training.model.HrmsUserTrainingRecordDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="training_id" property="trainingId" />
        <result column="training_topic" property="trainingTopic" />
        <result column="training_type" property="trainingType" />
        <result column="training_session" property="trainingSession" />
        <result column="user_id" property="userId" />
        <result column="user_code" property="userCode" />
        <result column="user_name" property="userName" />
        <result column="dept_id" property="deptId" />
        <result column="post_id" property="postId" />
        <result column="date" property="date" />
        <result column="day_id" property="dayId" />
        <result column="vendor_id" property="vendorId" />
        <result column="vendor_code" property="vendorCode" />
        <result column="status" property="status" />
        <result column="is_latest" property="isLatest" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, training_id, training_topic, training_type, training_session, user_id, user_code, user_name, dept_id, post_id, date, day_id, vendor_id, vendor_code, status, is_latest
    </sql>

</mapper>
