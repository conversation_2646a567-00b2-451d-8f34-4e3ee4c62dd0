<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.user.mapper.HrmsUserInfoMapper">

    <select id="export"
            parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.UserListInfoDTO">
        select
        hui.id as id,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.user_code as userCode,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.grade_id as gradeId,
        hui.grade_no as gradeNo,
        hui.email as email,
        hui.country_code as countryName,
        hui.employee_type as employeeType,
        -- huer.entry_date as entryDate,
        hui.status as status,
        hui.work_status as workStatus,
        hui.profile_photo_url as profilePhotoUrl,
        hui.phone as phone,
        hui.vendor_code as vendorCode,
        hui.vendor_name as vendorName,
        hui.leader_id as leaderId,
        hui.leader_name as leaderName
        from hrms_user_info as hui
        left join hrms_entry_audit_record as auditRecord on auditRecord.user_id = hui.id
        where hui.is_delete=0 and auditRecord.is_latest = 1 and auditRecord.is_delete = 0
        <if test="auditStatus!=null and auditStatus!=''">
            and auditRecord.audit_status = #{auditStatus}
        </if>
        <if test="sourceType!=null and sourceType!=''">
            and auditRecord.source_type = #{sourceType}
        </if>
        <if test="userName!=null and userName!=''">
            and (hui.user_name like CONCAT('%',#{userName},'%')
            OR hui.sys_account_name like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and hui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and hui.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and hui.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="status!=null and status!=''">
            and hui.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and hui.work_status = #{workStatus}
        </if>
        <if test="isWarehouseStaff!=null">
            and hui.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="isDtl!=null">
            and hui.is_dtl = #{isDtl}
        </if>
        <if test="isDriver!=null">
            and hui.is_driver = #{isDriver}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and hui.employee_type = #{employeeType}
        </if>
        order by hui.work_no*1 desc
    </select>

    <select id="selectApprovalInfo"
            parameterType="com.imile.hrms.dao.user.query.UserApprovalInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.UserApprovalInfoDTO">
        select
        userInfo.id as userId,
        userInfo.user_code as userCode,
        userInfo.user_name as userName,
        userInfo.sys_account_name as sysAccountName,
        userInfo.sex as sex,
        userInfo.dept_id as deptId,
        userInfo.post_id as postId,
        userInfo.phone as phone,
        userInfo.email as email,
        userInfo.vendor_code as vendorCode,
        userInfo.vendor_name as vendorName,
        userInfo.create_date as createDate,
        userInfo.create_user_code as createUserCode,
        userInfo.create_user_name as createUserName,
        userInfo.last_upd_date as lastUpdDate,
        userInfo.last_upd_user_code as lastUpdUserCode,
        userInfo.last_upd_user_name as lastUpdUserName
        from hrms_user_info as userInfo
        left join hrms_entry_audit_record as auditRecord on auditRecord.user_id = userInfo.id
        where userInfo.is_delete=0 and auditRecord.is_latest = 1 and auditRecord.is_delete = 0
        <if test="auditStatus!=null and auditStatus!=''">
            and auditRecord.audit_status = #{auditStatus}
        </if>
        <if test="userName!=null and userName!=''">
            and (userInfo.user_name like CONCAT('%',#{userName},'%')
            OR userInfo.sys_account_name like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            and userInfo.dept_id in
            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and userInfo.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and userInfo.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="isWarehouseStaff!=null">
            and userInfo.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and userInfo.employee_type = #{employeeType}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and userInfo.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        order by createDate desc

    </select>

    <select id="selectExternalWarehouse"
            parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.ExternalWarehouseInfoDTO">
        select
        hui.id as userId,
        hui.user_code as userCode,
        hui.user_name as userName,
        hui.sex as sex,
        hui.dept_id as deptId,
        hui.oc_id as stationId,
        hui.phone as phone,
        hui.email as email,
        hui.vendor_code as vendorCode,
        hui.vendor_name as vendorName,
        hui.disabled_date as disabledDate,
        hui.is_dtl as isDtl,
        hui.employee_type as employeeType,
        bar.id as bpmApprovalRecordId,
        bar.approval_id as approvalId,
        bar.approval_code as approvalCode,
        bar.approval_type as approvalType,
        bar.approval_process_info as approvalProcessInfo,
        bar.create_date as createDate,
        bar.create_user_code as createUserCode,
        bar.create_user_name as createUserName,
        bar.last_upd_date as lastUpdDate,
        bar.last_upd_user_code as lastUpdUserCode,
        bar.last_upd_user_name as lastUpdUserName
        from hrms_user_info as hui
        inner join hrms_bpm_approval_record as bar on bar.biz_id=hui.id
        where hui.is_delete=0 and bar.is_latest = 1 and bar.is_delete = 0
        <if test="auditStatusList!=null and auditStatusList.size()>0">
            <foreach collection="auditStatusList" item="status" open="and bar.status in (" close=")"
                     separator=",">
                #{status}
            </foreach>
        </if>
        <if test="sourceTypeList!=null and sourceTypeList.size()>0">
            <foreach collection="sourceTypeList" item="sourceType" open="and bar.data_source in (" close=")"
                     separator=",">
                #{sourceType}
            </foreach>
        </if>
        <if test="approvalTypeList!=null and approvalTypeList.size()>0">
            <foreach collection="approvalTypeList" item="approvalType" open="and bar.approval_type in (" close=")"
                     separator=",">
                #{approvalType}
            </foreach>
        </if>
        <if test="userName!=null and userName!=''">
            and (hui.user_name like CONCAT('%',#{userName},'%')
            OR hui.sys_account_name like CONCAT('%',#{userName},'%')
            OR hui.user_code like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and hui.oc_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and hui.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and hui.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="status!=null and status!=''">
            and hui.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and hui.work_status = #{workStatus}
        </if>
        <if test="isWarehouseStaff!=null">
            and hui.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="isDtl!=null">
            and hui.is_dtl = #{isDtl}
        </if>
        <if test="isDriver!=null">
            and hui.is_driver = #{isDriver}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and hui.employee_type = #{employeeType}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and hui.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        order by lastUpdDate desc

    </select>
    <!--    <select id="selectExternalWarehouse"
                parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
                resultType="com.imile.hrms.dao.user.dto.ExternalWarehouseInfoDTO">
            select
            userInfo.id as userId,
            userInfo.user_code as userCode,
            userInfo.user_name as userName,
            userInfo.sex as sex,
            userInfo.dept_id as deptId,
            userInfo.phone as phone,
            userInfo.email as email,
            userInfo.vendor_code as vendorCode,
            userInfo.vendor_name as vendorName,
            userInfo.disabled_date as disabledDate,
            userInfo.is_dtl as isDtl,
            userInfo.employee_type as employeeType,
            processRecord.id as approvalProcessId,
            processRecord.last_upd_date as auditDate,
            userInfo.create_date as createDate,
            userInfo.create_user_code as createUserCode,
            userInfo.create_user_name as createUserName,
            userInfo.last_upd_date as lastUpdDate,
            userInfo.last_upd_user_code as lastUpdUserCode,
            userInfo.last_upd_user_name as lastUpdUserName
            from hrms_user_info as userInfo
            left join hrms_approval_process_record as processRecord on processRecord.biz_id = userInfo.id
            where userInfo.is_delete=0 and processRecord.is_latest = 1 and processRecord.is_delete = 0
            and processRecord.approval_type = "ENTRY"
            <if test="auditStatusList!=null and auditStatusList.size()>0">
                <foreach collection="auditStatusList" item="status" open="and processRecord.status in (" close=")"
                         separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="sourceTypeList!=null and sourceTypeList.size()>0">
                <foreach collection="sourceTypeList" item="sourceType" open="and processRecord.data_source in (" close=")"
                         separator=",">
                    #{sourceType}
                </foreach>
            </if>
            &lt;!&ndash;   <if test="auditStatus!=null and auditStatus!=''">
                   and processRecord.status = #{auditStatus}
               </if>&ndash;&gt;
            &lt;!&ndash; <if test="sourceType!=null and sourceType!=''">
                 and processRecord.data_source = #{sourceType}
             </if>&ndash;&gt;
            <if test="userName!=null and userName!=''">
                and (userInfo.user_name like CONCAT('%',#{userName},'%')
                OR userInfo.sys_account_name like CONCAT('%',#{userName},'%')
                OR userInfo.user_code like CONCAT('%',#{userName},'%'))
            </if>
            <if test="deptIdList!=null and deptIdList.size()>0">
                <foreach collection="deptIdList" item="deptId" open="and userInfo.dept_id in (" close=")"
                         separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="vendorCode!=null and vendorCode!=''">
                and userInfo.vendor_code = #{vendorCode}
            </if>
            <if test="phone!=null and phone!=''">
                and userInfo.phone like CONCAT('%',#{phone},'%')
            </if>
            <if test="status!=null and status!=''">
                and userInfo.status = #{status}
            </if>
            <if test="workStatus!=null and workStatus!=''">
                and userInfo.work_status = #{workStatus}
            </if>
            <if test="isWarehouseStaff!=null">
                and userInfo.is_warehouse_staff = #{isWarehouseStaff}
            </if>
            <if test="isDtl!=null">
                and userInfo.is_dtl = #{isDtl}
            </if>
            <if test="isDriver!=null">
                and userInfo.is_driver = #{isDriver}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and userInfo.employee_type = #{employeeType}
            </if>
            order by lastUpdDate desc

        </select>-->


    <select id="selectExternalWarehouseByUserInfo"
            parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.ExternalWarehouseInfoDTO">
        select
        userInfo.id as userId,
        userInfo.user_code as userCode,
        userInfo.user_name as userName,
        userInfo.sex as sex,
        userInfo.dept_id as deptId,
        userInfo.oc_id as stationId,
        userInfo.phone as phone,
        userInfo.email as email,
        userInfo.vendor_code as vendorCode,
        userInfo.vendor_name as vendorName,
        userInfo.disabled_date as disabledDate,
        userInfo.is_dtl as isDtl,
        userInfo.employee_type as employeeType,
        userInfo.create_date as createDate,
        userInfo.create_user_code as createUserCode,
        userInfo.create_user_name as createUserName,
        userInfo.last_upd_date as lastUpdDate,
        userInfo.last_upd_user_code as lastUpdUserCode,
        userInfo.last_upd_user_name as lastUpdUserName
        from hrms_user_info as userInfo
        where userInfo.is_delete=0
        <if test="userName!=null and userName!=''">
            and (userInfo.user_name like CONCAT('%',#{userName},'%')
            OR userInfo.sys_account_name like CONCAT('%',#{userName},'%')
            OR userInfo.user_code like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and userInfo.oc_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and userInfo.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and userInfo.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="status!=null and status!=''">
            and userInfo.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and userInfo.work_status = #{workStatus}
        </if>
        <if test="isWarehouseStaff!=null">
            and userInfo.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="isDtl!=null">
            and userInfo.is_dtl = #{isDtl}
        </if>
        <if test="isDriver!=null">
            and userInfo.is_driver = #{isDriver}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and userInfo.employee_type = #{employeeType}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and userInfo.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        order by lastUpdDate desc

    </select>

    <!--<select id="selectExternalWarehouse"
            parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.ExternalWarehouseInfoDTO">
        select
        userInfo.id as userId,
        userInfo.user_code as userCode,
        userInfo.user_name as userName,
        userInfo.sex as sex,
        userInfo.dept_id as deptId,
        userInfo.phone as phone,
        userInfo.email as email,
        userInfo.vendor_code as vendorCode,
        userInfo.vendor_name as vendorName,
        userInfo.disabled_date as disabledDate,
        userInfo.is_dtl as isDtl,
        userInfo.employee_type as employeeType,
        auditRecord.reject_reason as rejectReason,
        auditRecord.audit_date as auditDate,
        userInfo.create_date as createDate,
        userInfo.create_user_code as createUserCode,
        userInfo.create_user_name as createUserName,
        userInfo.last_upd_date as lastUpdDate,
        userInfo.last_upd_user_code as lastUpdUserCode,
        userInfo.last_upd_user_name as lastUpdUserName
        from hrms_user_info as userInfo
        left join hrms_entry_audit_record as auditRecord on auditRecord.user_id = userInfo.id
        where userInfo.is_delete=0 and auditRecord.is_latest = 1 and auditRecord.is_delete = 0
        <if test="auditStatus!=null and auditStatus!=''">
            and auditRecord.audit_status = #{auditStatus}
        </if>
        <if test="sourceType!=null and sourceType!=''">
            and auditRecord.source_type = #{sourceType}
        </if>
        <if test="userName!=null and userName!=''">
            and (userInfo.user_name like CONCAT('%',#{userName},'%')
            OR userInfo.sys_account_name like CONCAT('%',#{userName},'%')
            OR userInfo.user_code like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and userInfo.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and userInfo.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and userInfo.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="status!=null and status!=''">
            and userInfo.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and userInfo.work_status = #{workStatus}
        </if>
        <if test="isWarehouseStaff!=null">
            and userInfo.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="isDtl!=null">
            and userInfo.is_dtl = #{isDtl}
        </if>
        <if test="isDriver!=null">
            and userInfo.is_driver = #{isDriver}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and userInfo.employee_type = #{employeeType}
        </if>
        order by lastUpdDate desc

    </select>-->


    <select id="selectCrmUserInfo" parameterType="com.imile.hrms.dao.user.query.CrmUserQuery"
            resultType="com.imile.hrms.dao.user.dto.CrmUserInfoDTO">
        select
        id as id,
        user_code as userCode,
        user_name as userNameCn,
        user_name_en as userNameEn,
        phone as phone,
        email as email,
        post_id as postId,
        profile_photo_url as profilePhotoUrl,
        origin_country as country
        from hrms_user_info
        <where>
            <if test="nameOrCode != null">
                and (user_name like CONCAT('%',#{nameOrCode},'%')
                OR user_code like CONCAT('%',#{nameOrCode},'%')
                OR user_name_en like CONCAT('%',#{nameOrCode},'%'))
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <if test="deptIdList != null and deptIdList.size > 0">
                and dept_id in
                <foreach collection="deptIdList" open="(" close=")" separator="," item="deptId">
                    #{deptId}
                </foreach>
            </if>
            <if test="leaderId != null">
                and leader_id = #{leaderId}
            </if>
            <if test="originCountry != null and originCountry != ''">
                and origin_country = #{originCountry}
            </if>
            and is_delete = 0 and `status` = 'ACTIVE' and work_status = 'ON_JOB' and is_driver = 0
        </where>

    </select>

    <select id="selectCrmUserToPermission" resultType="com.imile.hrms.dao.user.dto.CrmUserInfoDTO">
        select
        id as id,
        user_code as userCode,
        user_name as userNameCn,
        user_name_en as userNameEn,
        phone as phone,
        email as email,
        post_id as postId,
        profile_photo_url as profilePhotoUrl,
        origin_country as country,
        status,
        work_status as workStatus
        from hrms_user_info
        <where>
            and  is_delete = 0
            and employee_type in ('Employee','SubEmployee')
            and is_driver = 0
            and is_warehouse_staff = 0
            and user_code is not null
        </where>
        limit 5000
    </select>

    <update id="updateVendorById">
        update hrms_user_info
        set vendor_id=#{vendorId},vendor_code=#{vendorCode},vendor_name=#{vendorName},vendor_org_id=#{vendorOrgId}
        where id=#{id}
    </update>

    <update id="updateByVendorCode">
        update hrms_user_info
        set leader_id = #{leaderId}, leader_name = #{leaderName}
        where vendor_code = #{vendorCode} and is_delete = 0 and is_driver = 1
    </update>


    <select id="registerList"
            parameterType="com.imile.hrms.dao.user.query.UserQuery"
            resultType="com.imile.hrms.dao.user.dto.RegisterUserInfoDTO">
        select
        hui.id as id,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.phone as phone,
        hui.user_name as userName,
        hui.user_name_en,
        hui.email,
        hui.origin_country as originCountry,
        hui.last_upd_date as lastUpdDate,
        hui.last_upd_user_name as lastUpdUserName,
        huer.entry_date as entryDate,
        huer.entry_status as entryStatus

        from hrms_user_info hui
        inner join hrms_user_entry_record huer
        on hui.id = huer.user_id and huer.is_delete = 0

        <where>
            hui.is_delete = 0 and huer.entry_status not in ('WAIT_AUDIT','IN_REVIEW')
            <if test="userName!=null and userName!=''">
                and hui.user_name like concat('%',#{userName},'%')
            </if>

            <if test="deptIds !=null and deptIds.size()>0">
                <foreach collection="deptIds" item="id" open="and hui.dept_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="userIds !=null and userIds.size()>0">
                <foreach collection="userIds" item="id" open="and hui.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <if test="postId!=null ">
                and hui.post_id = #{postId}
            </if>
            <if test="status!=null and status!=''">
                and huer.entry_status = #{status}
            </if>
            <if test="isolateAdminCode!=null and isolateAdminCode!=''">
                and (hui.user_code != #{isolateAdminCode} or hui.user_code is null)
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.dataSearchTypeSql"></include>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        </where>
        order by huer.create_date desc,hui.create_date desc
    </select>

    <select id="getMaxWorkNo"
            resultType="java.lang.Long">
        select max(work_no)
        from hrms_user_info
    </select>

    <select id="userList"
            parameterType="com.imile.hrms.dao.user.query.UserQuery"
            resultType="com.imile.hrms.dao.user.dto.UserListInfoDTO">
        select
        huer.confirm_date as entryDate,
        <!--绩效管理场景需要返回员工考核ID-->
        <if test="appraisalStatus != null and appraisalStatus != -1">
            aea.id AS employeeAppraisalId,
            aea.reject_dept_id AS rejectDeptId,
            aea.appraisal_status,
            aec.leader_rate,
            aec.exerciser_conclusion,
            aec.exerciser_rank,
            aec.final_leader_score,
            aec.exerciser_remark,
        </if>
        <include refid="USER_INFO_SQL"></include>
        from hrms_user_info hui
        inner join hrms_user_entry_record huer
        on hui.id = huer.user_id and huer.is_delete = 0
        <!--绩效管理场景需要关联员工考核表查询-->
        <if test="appraisalStatus != null and appraisalStatus != -1">
            INNER JOIN achievements_employee_appraisal AS aea
            ON aea.user_id = hui.id AND aea.is_delete = 0
            LEFT JOIN achievement_employee_conclusion AS aec
            ON aec.employee_appraisal_id = aea.id  AND aec.is_delete = 0
            <!--绩效管理待确认考核委托场景需要关联员工考核委托表查询-->
            <if test="appraisalStatus == 1">
                INNER JOIN achievements_employee_appraisal_entrust AS aeae
                ON aeae.employee_appraisal_id = aea.id AND aeae.is_delete = 0
            </if>
        </if>
        <where>
            hui.is_delete = 0 and hui.work_status is not null
            and huer.entry_status != "IN_REVIEW"
            <if test="userName!=null and userName!=''">
                and hui.user_name like concat('%',#{userName},'%')
            </if>
            <if test="offerIds !=null and offerIds.size()>0">
                <foreach collection="offerIds" item="id" open="and hui.recruitment_job_offer_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="hcIds !=null and hcIds.size()>0">
                <foreach collection="hcIds" item="id" open="and hui.recruitment_job_hc_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="workNo!=null and workNo!='' ">
                and hui.work_no = #{workNo}
            </if>
            <if test="country!=null and country!='' ">
                and FIND_IN_SET(hui.country_code,#{country})
            </if>
            <if test="email!=null and email!='' ">
                and hui.email like concat('%',#{email},'%')
            </if>
            <if test="userCode!=null and userCode !=''">
                and hui.user_code like concat('%',#{userCode},'%')
            </if>

            <if test="emailOrUserName!=null and emailOrUserName!=''">
                and (hui.user_name like concat('%',#{emailOrUserName},'%') or hui.email like
                concat('%',#{emailOrUserName},'%'))
            </if>
            <if test="ocIds !=null and ocIds.size()>0">
                <foreach collection="ocIds" item="id" open="and hui.oc_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="postId!=null ">
                and hui.post_id = #{postId}
            </if>
            <if test="postIdList !=null and postIdList.size()>0">
                <foreach collection="postIdList" item="id" open="and hui.post_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="workStatus!=null and workStatus!=''">
                and hui.work_status = #{workStatus}
            </if>
            <if test="status!=null and status!=''">
                and hui.status = #{status}
            </if>
            <if test="queryStatusList !=null and queryStatusList.size()>0">
                <foreach collection="queryStatusList" item="item" open="and hui.status in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="queryWorkStatusList !=null and queryWorkStatusList.size()>0">
                <foreach collection="queryWorkStatusList" item="item" open="and hui.work_status in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="employeeType!=null and employeeType!=''">
                and hui.employee_type = #{employeeType}
            </if>
            <if test="employeeTypes !=null and employeeTypes.size()>0">
                <foreach collection="employeeTypes" item="employeeType" open="and hui.employee_type in (" close=")" separator=",">
                    #{employeeType}
                </foreach>
            </if>
            <if test="isDriver!=null">
                and hui.is_driver = #{isDriver}
            </if>
            <if test="driverType!=null and driverType!=''">
                and hui.employee_type = #{driverType}
            </if>
            <if test="isolateAdminCode!=null and isolateAdminCode!=''">
                and (hui.user_code != #{isolateAdminCode} or hui.user_code is null)
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>

            <!--绩效管理场景筛选未设置状态时需要以员工考核数据是否存在作为查询条件-->
            <if test="appraisalStatus == -1">
                AND NOT EXISTS(SELECT 1 FROM achievements_employee_appraisal AS aea
                    WHERE aea.is_delete = 0
                    AND aea.event_id = #{eventId}
                    AND aea.user_id = hui.id
                )
            </if>
            <!--绩效管理场景筛选除未设置状态以外的其他状态时需要设置员工考核表的查询条件-->
            <if test="appraisalStatus != null and appraisalStatus != -1">
                AND aea.event_id = #{eventId}
                <if test="appraisalExecutorId != null">
                    AND aea.appraisal_executor_id = #{appraisalExecutorId}
                </if>
                <if test="noAppraisalExecutorId != null">
                    AND aea.appraisal_executor_id != #{noAppraisalExecutorId}
                </if>
                <if test="exerciserConclusion!=null and exerciserConclusion!=''">
                    and aec.exerciser_conclusion=#{exerciserConclusion}
                </if>
                <!--团队绩效-全部筛选-->
                <if test="appraisalStatus == 0">
                    AND aea.appraisal_status >= 13
                    <if test="appraiserUserId != null">
                        AND (aea.appraiser_user_id = #{appraiserUserId}
                            OR aea.exerciser_user_id = #{appraiserUserId}
                            <if test="examineDeptIds !=null and examineDeptIds.size()>0">
                                <foreach collection="examineDeptIds" item="deptId" open="or aea.appraiser_dept_id in (" close=")" separator=",">
                                    #{deptId}
                                </foreach>
                            </if>
                            <if test="deptIds !=null and deptIds.size()>0">
                                <foreach collection="deptIds" item="id" open="or hui.dept_id in (" close=")" separator=",">
                                    #{id}
                                </foreach>
                            </if>
                        )
                    </if>
                </if>

                <!--团队绩效待确认考核委托筛选-->
                <if test="appraisalStatus == 1">
                    AND aeae.principal_user_id = #{principalUserId}
                    AND aeae.entrust_status = 0
                </if>
                <!--员工考核已确认考核关系、已确认目标状态筛选-->
                <if test="appraisalStatus == 13">
                    AND aea.appraisal_status >= 13
                    <if test="(deptIds !=null and deptIds.size() > 0) or (examineDeptIds !=null and examineDeptIds.size() > 0)">
                        and(
                        <trim prefixOverrides="AND |OR ">
                            <if test="deptIds !=null and deptIds.size() > 0">
                                <foreach collection="deptIds" item="id" open="or hui.dept_id in (" close=")" separator=",">
                                    #{id}
                                </foreach>
                            </if>
                            <if test="examineDeptIds !=null and examineDeptIds.size() > 0">
                                <foreach collection="examineDeptIds" item="deptId" open="or aea.appraiser_dept_id in (" close=")" separator=",">
                                    #{deptId}
                                </foreach>
                            </if>
                        </trim>
                        )
                    </if>
                </if>

                <!--团队视图列表展示已确认目标的所有人员-->
                <if test="appraisalStatus == 60">
                    AND aea.appraisal_status >= 23
                    <if test="level !=null">
                        AND aea.appraisal_dept_level=#{level}
                    </if>
                    <if test="(deptIds !=null and deptIds.size() > 0) or (examineDeptIds !=null and examineDeptIds.size() > 0)">
                        and(
                        <trim prefixOverrides="AND |OR ">
                            <if test="deptIds !=null and deptIds.size() > 0">
                                <foreach collection="deptIds" item="id" open="or hui.dept_id in (" close=")" separator=",">
                                    #{id}
                                </foreach>
                            </if>
                            <if test="examineDeptIds !=null and examineDeptIds.size() > 0">
                                <foreach collection="examineDeptIds" item="deptId" open="or aea.appraiser_dept_id in (" close=")" separator=",">
                                    #{deptId}
                                </foreach>
                            </if>
                        </trim>
                        )
                    </if>
                </if>

                <!--hrms中绩效评估全部列表-->
                <if test="appraisalStatus == 65">
                    AND aea.appraisal_status >= 41
                    <if test="level !=null">
                        AND aea.appraisal_dept_level=#{level}
                    </if>
                    <if test="(deptIds !=null and deptIds.size() > 0) or (examineDeptIds !=null and examineDeptIds.size() > 0)">
                        and(
                        <trim prefixOverrides="AND |OR ">
                            <if test="deptIds !=null and deptIds.size() > 0">
                                <foreach collection="deptIds" item="id" open="or hui.dept_id in (" close=")" separator=",">
                                    #{id}
                                </foreach>
                            </if>
                            <if test="examineDeptIds !=null and examineDeptIds.size() > 0">
                                <foreach collection="examineDeptIds" item="deptId" open="or aea.appraiser_dept_id in (" close=")" separator=",">
                                    #{deptId}
                                </foreach>
                            </if>
                        </trim>
                        )
                    </if>
                </if>

                <!--筛选数据库考核状态-->
                <if test="appraisalStatus != 0 and appraisalStatus != 1 and appraisalStatus != 13 and appraisalStatus != 60 and appraisalStatus != 65">
                    AND aea.appraisal_status = #{appraisalStatus}

                    <if test="level !=null">
                        AND aea.appraisal_dept_level=#{level}
                    </if>
                    <!--oa侧需要有考核责任人和行权人的并集-->
                    <if test="appraiserUserId != null">
                        AND (aea.appraiser_user_id = #{appraiserUserId}
                        OR aea.exerciser_user_id = #{appraiserUserId}
                        <if test="examineDeptIds !=null and examineDeptIds.size()>0">
                            <foreach collection="examineDeptIds" item="deptId" open="or aea.appraiser_dept_id in (" close=")" separator=",">
                                #{deptId}
                            </foreach>
                        </if>
                        <if test="deptIds !=null and deptIds.size()>0">
                            <foreach collection="deptIds" item="id" open="or hui.dept_id in (" close=")" separator=",">
                                #{id}
                            </foreach>
                        </if>
                    )
                    </if>
                </if>
            </if>
            <if test="appraisalStatus == null or appraisalStatus == -1">
                <if test="deptIds !=null and deptIds.size()>0">
                    <foreach collection="deptIds" item="id" open="and hui.dept_id in (" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
            </if>

            <if test="appraiserFilter!=null">
                and aea.appraiser_user_id = #{appraiserFilter}
            </if>

            <if test="exerciserFilter!=null">
                and aea.exerciser_user_id = #{exerciserFilter}
            </if>

            <if test="selfStatus!=null">
                and aea.self_status = #{selfStatus}
            </if>

            <if test="examineCheckDeptIds !=null and examineCheckDeptIds.size() > 0">
                <foreach collection="examineCheckDeptIds" item="id" open="and aea.appraiser_dept_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="checkDeptIds !=null and checkDeptIds.size() > 0">
                <foreach collection="checkDeptIds" item="id" open="and hui.dept_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="excludeCountryList != null and excludeCountryList.size > 0">
                <foreach collection="excludeCountryList" item="country" open="and hui.origin_country not in (" close=")" separator=",">
                    #{country}
                </foreach>
            </if>
            <if test="userIdsByGrade != null and userIdsByGrade.size > 0">
                <foreach collection="userIdsByGrade" item="id" open="and hui.id  in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.dataSearchTypeSql"></include>
        </where>
        order by hui.work_no*1 desc
    </select>

    <select id="selectUserList"
            parameterType="com.imile.hrms.dao.user.query.SelectUserQuery"
            resultType="com.imile.hrms.dao.user.dto.SelectUserDTO">
        select
        hui.id as id,
        hui.user_code as userCode,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.email as email,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.employee_type as employeeType
        from hrms_user_info hui
        <where>
            hui.is_delete = 0 and hui.work_status = 'ON_JOB' and hui.status = 'ACTIVE'
            <if test="userNameOrEmail!=null and userNameOrEmail !=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%')
                or hui.user_name_en like concat('%',#{userNameOrEmail},'%')
                or hui.email like concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName !=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%')
                or hui.user_name like concat('%',#{userCodeOrName},'%')
                or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%')
                 )
            </if>
            <if test="isDtl">
                and hui.is_dtl = 1
            </if>
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="country!=null and country!=''">
                and hui.origin_country = #{country}
            </if>
            <if test="status!=null and status!=''">
                and hui.status = #{status}
            </if>
            <if test="workStatus!=null and workStatus!=''">
                and hui.work_status = #{workStatus}
            </if>
        </where>
    </select>

    <sql id="USER_INFO_SQL">
        hui.id as id,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.user_name_en as userNameEn,
        hui.user_code as userCode,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.grade_id as gradeId,
        hui.grade_no as gradeNo,
        hui.job_grade as jobGrade,
        hui.email as email,
        hui.country_code as countryName,
        hui.employee_type as employeeType,
        huer.entry_date as entryDate,
        hui.status as status,
        hui.work_status as workStatus,
        hui.profile_photo_url as profilePhotoUrl,
        hui.phone as phone,
        hui.vendor_code as vendorCode,
        hui.vendor_name as vendorName,
        hui.leader_id as leaderId,
        hui.leader_name as leaderName,
        hui.sex as sex,
        hui.birthday as birthDay,
        hui.oc_id as ocId,
        hui.project_id as projectId,
        hui.biz_model_id as bizModelId,
        hui.settlement_center_code as settlementCenterCode,
        hui.last_upd_date as lastUpdDate,
        hui.last_upd_user_name as lastUpdUserName,
        hui.origin_country as originCountry,
        hui.is_driver as isDriver,
        hui.recruitment_job_offer_id as recruitmentJobOfferId,
        hui.recruitment_job_offer_application_code as recruitmentJobOfferApplicationCode,
        hui.recruitment_job_hc_id as recruitmentJobHcId,
        hui.location_country AS locationCountry,
        hui.location_province AS locationProvince,
        hui.location_city AS locationCity
    </sql>


    <select id="listAccount"
            parameterType="com.imile.hrms.dao.user.query.AccountQuery"
            resultType="com.imile.hrms.dao.user.dto.AccountDTO">
        select
        hui.id as id,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.user_code as userCode,
        hui.employee_type as employeeType,
        hui.country_code as country,
        hui.email as email,
        hui.dept_id as deptId,
        hui.status as status
        from hrms_user_info hui
        <where>
            hui.is_delete = 0  and hui.user_code is not null
            and hui.work_status = 'ON_JOB'
<!--            <if test="userCode!=null and userCode!=''">-->
<!--                and hui.user_code like concat('%', #{userCode},'%')-->
<!--            </if>-->
<!--            <if test="userName!=null and userName!=''">-->
<!--                and hui.user_name like concat('%', #{userName},'%')-->
<!--            </if>-->

            <!-- 用工类型筛选 -->
            <if test="employeeTypeList != null and employeeTypeList.size() > 0 ">
                and hui.employee_type in
                <foreach collection="employeeTypeList" item="employeeType" open=" (" close=")" separator=",">
                    #{employeeType}
                </foreach>
            </if>

            <if test="email!=null and email!='' ">
                and hui.email like concat('%',#{email},'%')
            </if>
            <if test="status!=null and status!=''">
                and hui.status = #{status}
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="deptIdList!=null and deptIdList.size()>0">
                and hui.dept_id in
                <foreach collection="deptIdList" item="deptId" open=" (" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>

        </where>
        order by hui.work_no*1 desc
    </select>

    <select id="employeeCountDTOByIds"
            parameterType="com.imile.hrms.dao.organization.query.EmployeeCountQuery"
            resultType="com.imile.hrms.dao.organization.dto.EmployeeCountDTO">
        select
        reference_id_temp as referenceId,
        count(distinct id) as employeeCount
        from (
        select
        <choose>
            <when test="typeCode!=null and typeCode== 'company'">
                company_id as reference_id_temp,
            </when>
            <when test="typeCode!=null and typeCode== 'dept'">
                dept_id as reference_id_temp,
            </when>
            <when test="typeCode!=null and typeCode== 'post'">
                post_id as reference_id_temp,
            </when>
        </choose>
        id as id
        from hrms_user_info
        <where>
            is_delete = 0 and status = 'ACTIVE' and work_status = "ON_JOB"
            <if test="ids!=null and ids.size()>0">
                <choose>
                    <when test="typeCode!=null and typeCode== 'company'">
                        and company_id in
                    </when>
                    <when test="typeCode!=null and typeCode== 'dept'">
                        and dept_id in
                    </when>
                    <when test="typeCode!=null and typeCode== 'post'">
                        and post_id in
                    </when>
                </choose>
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ) hrms_ent_account_temp
        group by reference_id_temp
        order by reference_id_temp

    </select>

    <select id="getTotalEmployeeCount"
            resultType="java.lang.Integer">
        select count(distinct id)
        from hrms_user_info
        where is_delete = 0
        and status = 'ACTIVE'
        and work_status = "ON_JOB"
        <include refid="total_employee_count"/>
    </select>

    <sql id="total_employee_count">
        <if test="originCountry != null">
            AND origin_country = #{originCountry}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            <foreach collection="deptIds" item="id" open="and dept_id in (" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="excludeDeptIds != null and excludeDeptIds.size() != 0">
            <foreach collection="excludeDeptIds" item="id" open="and dept_id not in (" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="excludeUserIds != null and excludeUserIds.size() != 0">
            <foreach collection="excludeUserIds" item="id" open="and id not in (" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <sql id="Base_column">
        id,
        company_id,
        work_no,
        user_code,
        user_name,
        user_name_en,
        country_name,
        country_code,
        sex,
        birthday,
        employee_type,
        vendor_id,
        post_id,
        grade_id,
        grade_no,
        dept_id,
        leader_id,
        leader_name,
        email,
        phone,
        status,
        work_status,
        profile_photo_url,
        is_driver,
        administrator_attributes,
        is_sync,data_source,
        remark,
        orderby,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name
    </sql>


    <select id="getUserInfoInformation"
            resultType="com.imile.hrms.dao.user.dto.UserInfoInformationDTO">
        select hui.id                     as userId,
               hui.user_code              as userCode,
               hui.user_name              as userName,
               hui.user_name_en           as userNameEn,
               hui.work_status            as workStatus,
               hui.status                 as status,
               hui.location_country       as locationCountry,
               hui.vendor_code            as vendorCode,
               hui.vendor_name            as vendorName,
               hui.leader_id              as leaderId,
               hui.leader_name            as leaderName,
               hui.employee_type          as employeeType,
               huer.entry_date            as entryDate,
               hudr.actual_dimission_date as dimissionDate,
               hed.id                     as deptId,
               hed.dept_name_cn           as deptName,
               hed.dept_name_en           as deptNameEn,
               hep.id                     as postId,
               hep.post_name_cn           as postName,
               hep.post_name_en           as postNameEn
        from hrms_user_info hui
                 left join hrms_ent_dept hed
                           on hui.dept_id = hed.id and hed.is_delete = 0 and hed.status = 'ACTIVE'
                 left join hrms_ent_post hep
                           on hui.post_id = hep.id and hep.is_delete = 0 and hep.status = 'ACTIVE'
                 left join hrms_user_entry_record huer
                           on hui.id = huer.user_id and huer.is_delete = 0 and huer.entry_status = 'ENTRY'
                 left join hrms_user_dimission_record hudr
                           on hui.id = hudr.user_id and hudr.is_delete = 0 and hudr.dimission_status = 'DIMISSION'
        where hui.id = #{userId}
    </select>

    <select id="getUserInformationDTO"
            resultType="com.imile.hrms.dao.user.dto.UserInformationDTO">
        select hui.id              as userId,
        hui.work_no         as workNo,
        hui.user_name       as userName,
        hui.email           as email,
        hed.id              as deptId,
        hed.dept_name_cn    as deptNameCn,
        hed.dept_name_en    as deptNameEn,
        hed.organization_code as organizationCode,
        hep.id              as postId,
        hep.post_name_cn    as postNameCn,
        hep.post_name_en    as postNameEn
        from hrms_user_info hui
        left join hrms_ent_dept hed
        on hui.dept_id = hed.id and hed.is_delete = 0 and hed.status = 'ACTIVE'
        left join hrms_ent_post hep
        on hui.post_id = hep.id and hep.is_delete = 0 and hep.status = 'ACTIVE'
        where hui.id = #{userId}
    </select>

    <select id="listLeader"
            resultType="com.imile.hrms.dao.user.dto.CrmUserInfoDTO">
        SELECT hui_b.user_code    as userCode,
               hui_b.user_name    as userNameCn,
               hui_b.user_name_en as userNameEn,
               hui_b.origin_country        as country
        FROM hrms_user_info hui_a
                 LEFT JOIN hrms_user_info hui_b ON hui_a.leader_id = hui_b.id
            AND hui_b.is_delete = 0
        WHERE hui_a.user_code = #{userCode}
          AND hui_a.is_delete = 0
    </select>

    <select id="listUsers"

            resultType="com.imile.hrms.dao.user.dto.CrmUserInfoDTO">

        SELECT
        hui.user_code as userCode,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.id as userId,
        hui.phone as phone,
        hui.email as email,
        hui.post_id as postId,
        hui.profile_photo_url as profilePhotoUrl,
        hui.status as status,
        hui.work_status as workStatus,
        hui.work_status as workStatus,
        hui.dept_id as deptId,
        hui.oc_id as ocId,
        hui.origin_country as originCountry,
        hui.origin_country as country
        FROM hrms_user_info hui
        <where>
            hui.is_delete = 0
            <if test="leaderIds!=null and leaderIds.size()>0">
                <foreach collection="leaderIds" item="id" separator="," open="and hui.leader_id in (" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="userCode!=null and userCode!=''">
                and hui.user_code = #{userCode}
            </if>
        </where>

    </select>

    <select id="queryUsersByUserCodeAndLeader"
            resultType="com.imile.hrms.dao.user.dto.CrmUserInfoDTO"
            parameterType="com.imile.hrms.dao.user.query.UserDaoQuery">
        select
        hui.user_code as userCode,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.id as userId,
        hui.phone as phone,
        hui.email as email,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.profile_photo_url as profilePhotoUrl,
        hui.status as status,
        hui.work_status as workStatus,
        hui.origin_country as originCountry
        from
        hrms_user_info hui
        <where>
            hui.is_delete = 0
            <if test="leaderUserIds!=null and leaderUserIds.size()>0">
                <foreach collection="leaderUserIds" item="id" separator="," open="and hui.leader_id in (" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="userCodes!=null and userCodes.size()>0">
                <foreach collection="userCodes" item="userCode" separator="," open="and hui.user_code in (" close=")">
                    #{userCode}
                </foreach>
            </if>
            <if test="userCode!=null and userCode!=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="postIds!=null and postIds.size()>0">
                <foreach collection="postIds" item="postId" separator="," open="and hui.post_id in (" close=")">
                    #{postId}
                </foreach>
            </if>
            <if test="userName!=null and userName!=''">
                and (hui.user_name like concat('%',#{userName},'%') or hui.user_name_en like
                concat('%',#{userName},'%'))
            </if>
        </where>
    </select>

    <select id="queryAttendanceUsersByUserCodeAndLeader"
            resultType="com.imile.hrms.dao.user.dto.CrmUserInfoDTO"
            parameterType="com.imile.hrms.dao.user.query.UserDaoQuery">
        select
        hui.user_code as userCode,
        hui.user_name as userNameCn,
        hui.user_name_en as userNameEn,
        hui.id as userId,
        hui.phone as phone,
        hui.email as email,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.profile_photo_url as profilePhotoUrl,
        hui.status as status,
        hui.work_status as workStatus,
        hui.location_country as originCountry
        from
        hrms_user_info hui
        <where>
            hui.is_delete = 0
            <if test="leaderUserIds!=null and leaderUserIds.size()>0">
                <foreach collection="leaderUserIds" item="id" separator="," open="and hui.leader_id in (" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="userCodes!=null and userCodes.size()>0">
                <foreach collection="userCodes" item="userCode" separator="," open="and hui.user_code in (" close=")">
                    #{userCode}
                </foreach>
            </if>
            <if test="userCode!=null and userCode!=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="postIds!=null and postIds.size()>0">
                <foreach collection="postIds" item="postId" separator="," open="and hui.post_id in (" close=")">
                    #{postId}
                </foreach>
            </if>
            <if test="userName!=null and userName!=''">
                and (hui.user_name like concat('%',#{userName},'%') or hui.user_name_en like
                concat('%',#{userName},'%'))
            </if>
        </where>
    </select>

    <select id="selectSomeUserFromDept" resultType="java.lang.Long">
        SELECT id
        FROM hrms_user_info
        <where>
            is_delete = 0
            AND status = 'ACTIVE'
            AND work_status = 'ON_JOB'
            <if test="deptIds != null and deptIds.size() != 0">
                <foreach collection="deptIds" item="id" open="and dept_id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="excludeUserIds != null and excludeUserIds.size() != 0">
                <foreach collection="excludeUserIds" item="id" open="and id not in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>



    <select id="exportNotApprovalPage"
            parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.UserListInfoDTO">
        select
        userInfo.id as id,
        userInfo.work_no as workNo,
        userInfo.user_name as userName,
        userInfo.user_code as userCode,
        userInfo.post_id as postId,
        userInfo.dept_id as deptId,
        userInfo.grade_id as gradeId,
        userInfo.grade_no as gradeNo,
        userInfo.email as email,
        userInfo.country_code as countryName,
        userInfo.employee_type as employeeType,
        userInfo.status as status,
        userInfo.work_status as workStatus,
        userInfo.profile_photo_url as profilePhotoUrl,
        userInfo.phone as phone,
        userInfo.vendor_code as vendorCode,
        userInfo.vendor_name as vendorName,
        userInfo.leader_id as leaderId,
        userInfo.leader_name as leaderName
        from hrms_user_info as userInfo
        where userInfo.is_delete=0
        <if test="userName!=null and userName!=''">
            and (userInfo.user_name like CONCAT('%',#{userName},'%')
            OR userInfo.sys_account_name like CONCAT('%',#{userName},'%')
            OR userInfo.user_code like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and userInfo.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and userInfo.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and userInfo.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="status!=null and status!=''">
            and userInfo.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and userInfo.work_status = #{workStatus}
        </if>
        <if test="isWarehouseStaff!=null">
            and userInfo.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="isDtl!=null">
            and userInfo.is_dtl = #{isDtl}
        </if>
        <if test="isDriver!=null">
            and userInfo.is_driver = #{isDriver}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and userInfo.employee_type = #{employeeType}
        </if>
        order by userInfo.last_upd_date desc
    </select>


    <select id="exportApprovalPage"
            parameterType="com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery"
            resultType="com.imile.hrms.dao.user.dto.UserListInfoDTO">
        select
        userInfo.id as id,
        userInfo.work_no as workNo,
        userInfo.user_name as userName,
        userInfo.user_code as userCode,
        userInfo.post_id as postId,
        userInfo.dept_id as deptId,
        userInfo.grade_id as gradeId,
        userInfo.grade_no as gradeNo,
        userInfo.email as email,
        userInfo.country_code as countryName,
        userInfo.employee_type as employeeType,
        userInfo.status as status,
        userInfo.work_status as workStatus,
        userInfo.profile_photo_url as profilePhotoUrl,
        userInfo.phone as phone,
        userInfo.vendor_code as vendorCode,
        userInfo.vendor_name as vendorName,
        userInfo.leader_id as leaderId,
        userInfo.leader_name as leaderName
        from hrms_user_info as userInfo
        inner join hrms_bpm_approval_record as bar on bar.biz_id=userInfo.id
        where userInfo.is_delete=0 and bar.is_latest = 1 and bar.is_delete = 0
        <if test="auditStatusList!=null and auditStatusList.size()>0">
            <foreach collection="auditStatusList" item="status" open="and bar.status in (" close=")"
                     separator=",">
                #{status}
            </foreach>
        </if>
        <if test="sourceTypeList!=null and sourceTypeList.size()>0">
            <foreach collection="sourceTypeList" item="sourceType" open="and bar.data_source in (" close=")"
                     separator=",">
                #{sourceType}
            </foreach>
        </if>
        <if test="approvalTypeList!=null and approvalTypeList.size()>0">
            <foreach collection="approvalTypeList" item="approvalType" open="and bar.approval_type in (" close=")"
                     separator=",">
                #{approvalType}
            </foreach>
        </if>
        <if test="userName!=null and userName!=''">
            and (userInfo.user_name like CONCAT('%',#{userName},'%')
            OR userInfo.sys_account_name like CONCAT('%',#{userName},'%')
            OR userInfo.user_code like CONCAT('%',#{userName},'%'))
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and userInfo.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorCode!=null and vendorCode!=''">
            and userInfo.vendor_code = #{vendorCode}
        </if>
        <if test="phone!=null and phone!=''">
            and userInfo.phone like CONCAT('%',#{phone},'%')
        </if>
        <if test="status!=null and status!=''">
            and userInfo.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and userInfo.work_status = #{workStatus}
        </if>
        <if test="isWarehouseStaff!=null">
            and userInfo.is_warehouse_staff = #{isWarehouseStaff}
        </if>
        <if test="isDtl!=null">
            and userInfo.is_dtl = #{isDtl}
        </if>
        <if test="isDriver!=null">
            and userInfo.is_driver = #{isDriver}
        </if>
        <if test="employeeType!=null and employeeType!=''">
            and userInfo.employee_type = #{employeeType}
        </if>
        order by userInfo.last_upd_date desc
    </select>
    <select id="getOnJobUser" resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO"
            parameterType="com.imile.hrms.dao.organization.query.DeptUserQuery">

        select
        hui.id as id,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.user_code as userCode,
        hui.post_id as postId,
        hui.dept_id as deptId,
        hui.employee_type as employeeType,
        hui.status as status,
        hui.work_status as workStatus
        from hrms_user_info hui
        where hui.is_delete = 0 and hui.work_status = 'ON_JOB'
        <if test="deptId!=null">
            and hui.dept_id = #{deptId}
        </if>

    </select>

    <select id="selectByAssociateCondition"
            parameterType="com.imile.hrms.dao.user.query.UserAssociateConditionBuilder"
            resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO">
        SELECT
        id,
        user_code AS userCode,
        user_name AS userName,
        user_name_en AS userNameEn,
        email
        FROM hrms_user_info
        WHERE is_delete = 0
        AND user_code IS NOT NULL
        <if test="idList != null and idList.size() > 0">
            AND id IN
            <foreach collection="idList" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="userCodeList != null and userCodeList.size() > 0">
            AND user_code IN
            <foreach collection="userCodeList" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="isDriver != null">
            AND is_driver = #{isDriver}
        </if>
        <!--未传工作状态时默认过滤工作状态为空的数据-->
        <if test="workStatus == null">
            AND work_status IS NOT NULL AND work_status != ''
        </if>
        <if test="workStatus != null and workStatus != ''">
            AND work_status = #{workStatus}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <!--传keyword时先按user_code正序再按id倒序-->
        <if test="keyword != null and keyword != ''">
            AND (
                id = #{keyword}
                OR user_code LIKE CONCAT('%', #{keyword}, '%')
                OR user_name LIKE CONCAT('%', #{keyword}, '%')
                OR user_name_en LIKE CONCAT('%', #{keyword}, '%')
            )
            ORDER BY user_code, id DESC
        </if>
        <!--未传keyword时默认按id倒序-->
        <if test="keyword == null or keyword == ''">
            ORDER BY id DESC
        </if>
        LIMIT 50
    </select>


    <select id="selectNoticeUserSchemeList"
            parameterType="com.imile.hrms.dao.salary.query.SalaryUserSchemeInfoQuery"
            resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO">
        select ui.*
        from hrms_user_info ui
        inner join hrms_salary_message_notice as smn on ui.id = smn.message_value
        inner join (
            select distinct(h1.user_id)
            from hrms_salary_user_scheme_info h1
            <where> h1.is_delete = 0 and h1.is_latest = 1
                <if test="country!=null and country!=''">
                    and h1.country = #{country}
                </if>
                <if test="countryList!=null and countryList.size()>0">
                    <foreach collection="countryList" item="country" open="and h1.country in (" close=")"
                             separator=",">
                        #{country}
                    </foreach>
                </if>
            </where>
        ) h on h.user_id = ui.id
        where ui.is_delete = 0 and smn.is_delete = 0 and smn.is_notice = 1 and ui.origin_country != 'TEST'
        <if test="locationCountry!=null and locationCountry!=''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and ui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="postId!=null ">
            and ui.post_id = #{postId}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and ui.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        <if test="userNameOrCode!=null and userNameOrCode!=''">
            and (ui.user_name like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_name_en like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_code like CONCAT('%',#{userNameOrCode},'%'))
        </if>
        <if test="isSalaryMultinational!=null ">
            and ui.is_salary_multinational = #{isSalaryMultinational}
        </if>
        <if test="isDriver!=null ">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="isSalaryConfig!=null ">
            and ui.is_salary_config = #{isSalaryConfig}
        </if>
        <if test="status!=null and status!=''">
            and ui.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and ui.work_status = #{workStatus}
        </if>
        <if test="messageKeyList!=null and messageKeyList.size()>0">
            <foreach collection="messageKeyList" item="messageKey" open="and smn.message_key in (" close=")"
                     separator=",">
                #{messageKey}
            </foreach>
        </if>
        <if test="vendorIdList!=null and vendorIdList.size()>0">
            <foreach collection="vendorIdList" item="item" open="and ui.vendor_id in (" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        order by ui.last_upd_date desc
    </select>


    <select id="selectDimissionUserSchemeList"
            parameterType="com.imile.hrms.dao.salary.query.SalaryUserSchemeInfoQuery"
            resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO">
        select
        ui.*
        from hrms_user_info as ui
        inner join (
            select h1.user_id
            from hrms_salary_user_scheme_info h1
            where h1.is_delete = 0 and h1.is_latest = 1
            <if test="country!=null and country!=''">
                and h1.country = #{country}
            </if>
            <if test="schemeConfigIdList!=null and schemeConfigIdList.size()>0">
                <foreach collection="schemeConfigIdList" item="item" open="and h1.id in (" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
        ) h on h.user_id = ui.id
        left join hrms_user_dimission_record as udr on ui.id=udr.user_id and udr.is_delete = 0
        where ui.is_delete = 0 and (ui.status = "DISABLED" OR ui.work_status = "DIMISSION")
        <if test="locationCountry!=null and locationCountry!=''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and ui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="postId!=null ">
            and ui.post_id = #{postId}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and ui.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        <if test="userNameOrCode!=null and userNameOrCode!=''">
            and (ui.user_name like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_name_en like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_code like CONCAT('%',#{userNameOrCode},'%'))
        </if>
        <if test="isSalaryMultinational!=null ">
            and ui.is_salary_multinational = #{isSalaryMultinational}
        </if>
        <if test="isDriver!=null ">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="isSalaryConfig!=null ">
            and ui.is_salary_config = #{isSalaryConfig}
        </if>
        <if test="dimissionStartDate != null and dimissionEndDate != null">
            and udr.plan_dimission_date >= #{dimissionStartDate}  and udr.plan_dimission_date  &lt;= #{dimissionEndDate}
        </if>
        <if test="vendorIdList!=null and vendorIdList.size()>0">
            <foreach collection="vendorIdList" item="item" open="and ui.vendor_id in (" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        order by ui.last_upd_date desc
    </select>


    <select id="selectSalaryRoleUser" resultType="com.imile.hrms.dao.user.dto.SalaryUserRoleDTO"
            parameterType="com.imile.hrms.dao.user.query.SalaryRoleUserQuery">
        SELECT
        ui.id AS userId,
        ui.user_code AS userCode,
        ui.user_name AS userName,
        ui.origin_country AS country,
        ui.location_country AS locationCountry,
        ui.location_province AS locationProvince,
        ui.location_city AS locationCity,
        ui.dept_id AS deptId,
        ui.post_id AS postId,
        ui.status AS status,
        ui.work_status AS workStatus,
        sur.country AS roleCountry,
        sur.last_upd_date AS lastUpdDate,
        sur.last_upd_user_code AS lastUpdUserCode,
        sur.last_upd_user_name AS lastUpdUserName
        FROM hrms_user_info as ui
            left join hrms_salary_user_role as sur on ui.id=sur.user_id
        WHERE ui.is_delete = 0
        <if test="isAuth != null">
            AND sur.is_auth = #{isAuth}
        </if>
        <if test="userIdList!=null and userIdList.size()>0">
            <foreach collection="userIdList" item="userId" open="and ui.id in (" close=")"
                     separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="userCodeList!=null and userCodeList.size()>0">
            <foreach collection="userCodeList" item="userCode" open="and ui.user_code in (" close=")"
                     separator=",">
                #{userCode}
            </foreach>
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and ui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="userNameOrCode!=null and userNameOrCode!=''">
            and (ui.user_name like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_name_en like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_code like CONCAT('%',#{userNameOrCode},'%'))
        </if>
        <if test="country != null and country != ''">
            AND ui.origin_country = #{country}
        </if>
        <if test="locationCountry!=null and locationCountry!=''">
            and ui.location_country = #{locationCountry}
        </if>
    </select>

    <select id="selectUserInfoList" resultType="com.imile.hrms.dao.user.dto.UserListInfoDTO">
        select hui.id                     as id,
               hui.work_no                as workNo,
               hui.user_name              as userName,
               hui.user_name_en           as userNameEn,
               hui.user_code              as userCode,
               hui.post_id                as postId,
               hui.dept_id                as deptId,
               hui.grade_id               as gradeId,
               hui.grade_no               as gradeNo,
               hui.job_grade              as jobGrade,
               hui.email                  as email,
               hui.country_code           as countryName,
               hui.employee_type          as employeeType,
               hui.status                 as status,
               hui.work_status            as workStatus,
               hui.profile_photo_url      as profilePhotoUrl,
               hui.phone                  as phone,
               hui.vendor_code            as vendorCode,
               hui.vendor_name            as vendorName,
               hui.leader_id              as leaderId,
               hui.leader_name            as leaderName,
               hui.sex                    as sex,
               hui.birthday               as birthDay,
               hui.oc_id                  as ocId,
               hui.project_id             as projectId,
               hui.project_code             as projectCode,
               hui.biz_model_id           as bizModelId,
               hui.settlement_center_code as settlementCenterCode,
               hui.last_upd_date          as lastUpdDate,
               hui.last_upd_user_name     as lastUpdUserName,
               hui.origin_country         as originCountry,
               hui.is_driver              as isDriver,
               post.post_name_cn  as postName,
               dept.organization_code as organizationCode,
               dept.dept_name_cn as deptNameCn,
               dept.dept_name_en as deptNameEn
        from hrms_user_info hui
                 left join hrms_ent_post post on hui.post_id = post.id
                 left join hrms_ent_dept dept on hui.dept_id = dept.id
        WHERE hui.is_delete = 0
          and hui.user_code is not null
        <if test="userCodeOrName!=null and userCodeOrName!=''">
            and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
            concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
            or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
        </if>
        <if test="workStatus!=null and workStatus != ''">
            and hui.work_status = #{workStatus,jdbcType=VARCHAR}
        </if>
        <if test="workStatusList!=null and workStatusList.size()>0">
            <foreach collection="workStatusList" separator="," open="and hui.work_status in (" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="employeeTypes !=null and employeeTypes.size()>0">
            <foreach collection="employeeTypes" item="employeeType" open="and hui.employee_type in (" close=")" separator=",">
                #{employeeType}
            </foreach>
        </if>
        <if test="postId!=null ">
            and hui.post_id = #{postId}
        </if>
        <if test="userIds !=null and userIds.size()>0">
            <foreach collection="userIds" item="id" open="and hui.id in (" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by hui.work_no * 1 desc
    </select>
    <select id="selectOnJobUserSchemeList" resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO"
            parameterType="com.imile.hrms.dao.salary.query.SalaryUserSchemeInfoQuery">

        select ui.*
        from hrms_user_info ui
        inner join (
            select h1.user_id
            from hrms_salary_user_scheme_info h1
            where h1.is_delete = 0 and h1.is_latest = 1
            <if test="country!=null and country!=''">
                and h1.country = #{country}
            </if>
            <if test="schemeConfigIdList!=null and schemeConfigIdList.size()>0">
                <foreach collection="schemeConfigIdList" item="item" open="and h1.id in (" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
        ) h on h.user_id = ui.id
        where ui.is_delete=0 and ui.work_status = 'ON_JOB' and ui.status='ACTIVE'
        <if test="locationCountry!=null and locationCountry!=''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and ui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="postId!=null ">
            and ui.post_id = #{postId}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and ui.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        <if test="userNameOrCode!=null and userNameOrCode!=''">
            and (ui.user_name like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_name_en like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_code like CONCAT('%',#{userNameOrCode},'%'))
        </if>
        <if test="isSalaryMultinational!=null ">
            and ui.is_salary_multinational = #{isSalaryMultinational}
        </if>
        <if test="isDriver!=null ">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="isSalaryConfig!=null ">
            and ui.is_salary_config = #{isSalaryConfig}
        </if>
        <if test="vendorIdList!=null and vendorIdList.size()>0">
            <foreach collection="vendorIdList" item="item" open="and ui.vendor_id in (" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        order by ui.last_upd_date desc

    </select>



    <select id="selectUserSchemeExcludeDs" resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO"
            parameterType="com.imile.hrms.dao.salary.query.SalaryUserSchemeInfoQuery">
        select ui.*
        from hrms_user_info as ui
        left join hrms_user_entry_record as uer on ui.id = uer.user_id
        where ui.is_delete = 0 and ui.work_status = 'ON_JOB' and ui.status='ACTIVE' and ui.origin_country != 'TEST'
        and (ui.is_salary_config = '' or ui.is_salary_config is null)
        and uer.is_delete = 0 and uer.entry_date is not null
        <if test="locationCountry!=null and locationCountry!=''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and ui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="postId!=null ">
            and ui.post_id = #{postId}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and ui.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        <if test="userNameOrCode!=null and userNameOrCode!=''">
            and (ui.user_name like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_name_en like CONCAT('%',#{userNameOrCode},'%')
            OR ui.user_code like CONCAT('%',#{userNameOrCode},'%'))
        </if>
        <if test="isDriver!=null ">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="vendorIdList!=null and vendorIdList.size()>0">
            <foreach collection="vendorIdList" item="item" open="and ui.vendor_id in (" close=")"
                     separator=",">
                #{item}
            </foreach>
        </if>
        order by ui.last_upd_date desc
    </select>
    <select id="queryByDeptId" resultType="com.imile.hrms.dao.user.dto.UserInfoListDTO">
        select id, user_code, user_name, user_name_en, dept_id, post_id, origin_country,status,work_status,location_country
        from hrms_user_info
        where is_delete = 0
        <if test="deptIdList != null and deptIdList.size() > 0">
            and dept_id in
            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>

    <select id="selectNeedUpdateTaxIdUser" resultType="com.imile.hrms.dao.user.model.NeedUpdateTaxIdUserDO">
        select ui.user_id as userId, ui.certificate_code as cpfNumber, a2.attr_value as zipCodeList
        from hrms_user_certificate as ui
             left join hrms_user_extend_attr as a on ui.user_id = a.user_id and a.attr_key = 'taxIdSource'
             left join hrms_user_extend_attr as a2 on ui.user_id = a2.user_id and a2.attr_key = 'zipCodeList'
             left join hrms_user_info as u on ui.user_id = u.id
        where ui.is_delete = 0 and a.is_delete = 0 and a2.is_delete = 0 and
            (ui.certificate_type_code = 'ID_CARD' and ui.certificate_code is not null) and
            (a.attr_value != 'FINANCE') and a2.attr_key is not null and u.location_country = 'BRA'
        order by ui.last_upd_date
    </select>

    <select id="selectUserByName" resultType="com.imile.hrms.dao.user.dto.UserListInfoDTO">
        select
        userInfo.id as id,
        userInfo.work_no as workNo,
        userInfo.user_name as userName,
        userInfo.user_code as userCode,
        userInfo.post_id as postId,
        userInfo.dept_id as deptId,
        userInfo.grade_id as gradeId,
        userInfo.grade_no as gradeNo,
        userInfo.email as email,
        userInfo.country_code as countryName,
        userInfo.employee_type as employeeType,
        userInfo.status as status,
        userInfo.work_status as workStatus,
        userInfo.profile_photo_url as profilePhotoUrl,
        userInfo.phone as phone,
        userInfo.vendor_code as vendorCode,
        userInfo.vendor_name as vendorName,
        userInfo.leader_id as leaderId,
        userInfo.leader_name as leaderName
        from hrms_user_info as userInfo
        where userInfo.is_delete=0
        and (userInfo.user_name like CONCAT('%',#{userName},'%')
        OR userInfo.sys_account_name like CONCAT('%',#{userName},'%')
        OR userInfo.user_code like CONCAT('%',#{userName},'%'))
    </select>


    <select id="selectWaitUserSchemeList" resultType="com.imile.hrms.dao.user.model.HrmsUserInfoDO"
            parameterType="com.imile.hrms.dao.salary.query.SalaryWaitUserSchemeInfoQuery">
        select ui.*
        from hrms_user_info as ui
        left join hrms_user_entry_record as uer on ui.id = uer.user_id
        where ui.is_delete = 0 and uer.is_delete = 0 and uer.entry_date is not null and ui.origin_country != 'TEST'
        <if test="locationCountry!=null and locationCountry!=''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="locationCountryList!=null and locationCountryList.size()>0">
            <foreach collection="locationCountryList" item="locationCountry" open="and ui.location_country in (" close=")"
                     separator=",">
                #{locationCountry}
            </foreach>
        </if>
        <if test="deptIdList!=null and deptIdList.size()>0">
            <foreach collection="deptIdList" item="deptId" open="and ui.dept_id in (" close=")"
                     separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="vendorIdList!=null and vendorIdList.size()>0">
            <foreach collection="vendorIdList" item="vendorId" open="and ui.vendor_id in (" close=")"
                     separator=",">
                #{vendorId}
            </foreach>
        </if>
        <if test="postId!=null ">
            and ui.post_id = #{postId}
        </if>
        <if test="employeeTypeList!=null and employeeTypeList.size()>0">
            <foreach collection="employeeTypeList" item="employeeType" open="and ui.employee_type in (" close=")"
                     separator=",">
                #{employeeType}
            </foreach>
        </if>
        <if test="userCode!=null and userCode!=''">
            and ui.user_code = #{userCode}
        </if>
        <if test="userCodeList!=null and userCodeList.size()>0">
            <foreach collection="userCodeList" item="userCode" open="and ui.user_code in (" close=")"
                     separator=",">
                #{userCode}
            </foreach>
        </if>
        <if test="isDriver!=null ">
            and ui.is_driver = #{isDriver}
        </if>
        <if test="status!=null and status!=''">
            and ui.status = #{status}
        </if>
        <if test="workStatus!=null and workStatus!=''">
            and ui.work_status = #{workStatus}
        </if>
        <if test="existSalaryUserCodeList!=null and existSalaryUserCodeList.size()>0">
            <foreach collection="existSalaryUserCodeList" item="userCode" open="and ui.user_code not in (" close=")"
                     separator=",">
                #{userCode}
            </foreach>
        </if>
        <if test="isGlobalRelocation!=null ">
            and ui.is_global_relocation = #{isGlobalRelocation}
        </if>
        order by ui.last_upd_date desc
    </select>
</mapper>
