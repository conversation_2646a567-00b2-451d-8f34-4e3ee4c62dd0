<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.user.mapper.HrmsCompanyLeaveConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.user.model.HrmsCompanyLeaveConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="country" property="country" />
        <result column="leave_type" property="leaveType" />
        <result column="status" property="status" />
        <result column="use_sex" property="useSex" />
        <result column="employee_type" jdbcType="VARCHAR" property="employeeType" />
        <result column="location_province" jdbcType="VARCHAR" property="locationProvince" />
        <result column="leave_usage_restrictions" jdbcType="TINYINT" property="leaveUsageRestrictions" />
        <result column="use_start_date" property="useStartDate" />
        <result column="use_end_date" property="useEndDate" />
        <result column="use_cycle" property="useCycle" />
        <result column="leave_name" property="leaveName" />
        <result column="leave_short_name" property="leaveShortName" />
        <result column="consume_leave_type" property="consumeLeaveType" />
        <result column="is_upload_attachment" property="isUploadAttachment" />
        <result column="upload_attachment_condition" jdbcType="BIGINT" property="uploadAttachmentCondition" />
        <result column="attachment_unit" jdbcType="VARCHAR" property="attachmentUnit" />
        <result column="leave_unit" property="leaveUnit" />
        <result column="is_salary" jdbcType="TINYINT" property="isSalary" />
        <result column="dept_ids" jdbcType="VARCHAR" property="deptIds" />
        <result column="start_entry_date" jdbcType="VARCHAR" property="startEntryDate" />
        <result column="end_entry_date" jdbcType="VARCHAR" property="endEntryDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, country, leave_type, status, use_sex, employee_type, location_province,
        leave_usage_restrictions, use_cycle, use_start_date, use_end_date, stage, leave_name, leave_short_name,
        consume_leave_type,is_upload_attachment, upload_attachment_condition, leave_unit, is_salary, dept_ids,
        start_entry_date, end_entry_date
    </sql>

</mapper>
