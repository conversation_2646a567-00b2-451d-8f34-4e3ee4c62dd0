<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.user.mapper.UserExtendAttrMapper">

    <select id="selectByAttrKeyCondition"
            parameterType="com.imile.hrms.dao.user.query.UserExtendAttrQuery"
            resultType="com.imile.hrms.dao.user.dto.UserExtendAttrDTO">
        SELECT
        uea.id,uea.user_id,uea.attr_key,uea.attr_value,ui.user_code
        FROM hrms_user_extend_attr uea
        inner join hrms_user_info ui on uea.user_id = ui.id
        <where>
            uea.is_delete = 0 and ui.is_delete = 0 and ui.employee_type = 'OSFixedsalary'
            <if test="country!=null and country!=''">
                AND ui.location_country = #{country}
            </if>
            <if test="attrKey!=null and attrKey!=''">
                AND uea.attr_key = #{attrKey}
            </if>
            <if test="attrValue!=null and attrValue!=''">
                AND uea.attr_value = #{attrValue}
            </if>
            <if test="status!=null and status!=''">
                AND ui.status = #{status}
            </if>
            <if test="workStatus!=null and workStatus!=''">
                AND ui.work_status = #{workStatus}
            </if>
        </where>
    </select>
</mapper>
