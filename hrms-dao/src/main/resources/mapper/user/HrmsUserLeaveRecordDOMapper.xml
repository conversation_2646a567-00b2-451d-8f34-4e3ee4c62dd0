<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.user.mapper.HrmsUserLeaveRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.user.model.HrmsUserLeaveRecordDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="config_id" property="configId"/>
        <result column="user_id" property="userId"/>
        <result column="user_code" property="userCode"/>
        <result column="leave_name" property="leaveName"/>
        <result column="leave_type" property="leaveType"/>
        <result column="type" property="type"/>
        <result column="leave_start_day" property="leaveStartDay"/>
        <result column="leave_end_day" property="leaveEndDay"/>
        <result column="hours" property="hours"/>
        <result column="leave_residue_day" property="leaveResidueDay"/>
        <result column="operation_user_code" property="operationUserCode"/>
        <result column="operation_user_name" property="operationUserName"/>
        <result column="reduce_day" property="reduceDay"/>
        <result column="date" property="date"/>
        <result column="day_id" property="dayId"/>
        <result column="picture_path" property="picturePath"/>
        <result column="record_version" property="recordVersion" />
        <result column="remark" property="remark" />
        <result column="leave_minutes" property="leaveMinutes" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, config_id, user_id, user_code, leave_name, leave_type, type, leave_start_day,
        leave_end_day, hours, leave_residue_day, operation_user_code,
        operation_user_name, reduce_day , date, day_id, picture_path ,record_version,remark, leave_minutes
    </sql>

</mapper>
