<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.user.mapper.UserVisaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.user.model.UserVisaDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="user_id" property="userId" />
        <result column="visa_type" property="visaType" />
        <result column="visa_no" property="visaNo" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="issue_organization" property="issueOrganization" />
        <result column="issue_place" property="issuePlace" />
        <result column="visa_front_path" property="visaFrontPath" />
        <result column="visa_back_path" property="visaBackPath" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_id, visa_type, visa_no, start_date, end_date, issue_organization, issue_place, visa_front_path, visa_back_path
    </sql>

</mapper>
