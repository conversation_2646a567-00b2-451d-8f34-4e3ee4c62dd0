<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.vehicle.mapper.HrmsVehicleFuelWarningConfigMapper">

  <!--  &lt;!&ndash; 通用查询映射结果 &ndash;&gt;
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.vehicle.model.HrmsVehicleFuelWarningConfigDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="record_version" property="recordVersion" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
    </resultMap>

    &lt;!&ndash; 通用查询结果列 &ndash;&gt;
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, start_date, end_date
    </sql>-->

</mapper>
