<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.vehicle.mapper.HrmsVacantVehicleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.vehicle.model.HrmsVacantVehicleDO">
        <id column="id" property="id" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="date" property="date" />
        <result column="day_id" property="dayId" />
        <result column="oc_code" property="ocCode" />
        <result column="oc_name" property="ocName" />
        <result column="is_own" property="isOwn" />
        <result column="vehicle_num" property="vehicleNum" />
        <result column="work_vehicle_num" property="workVehicleNum" />
        <result column="free_vehicle_num" property="freeVehicleNum" />
        <result column="free_vehicle_rate" property="freeVehicleRate" />
        <result column="no_bind_vehicle_num" property="noBindVehicleNum" />
        <result column="no_attendance_vehicle_num" property="noAttendanceVehicleNum" />
        <result column="orderby" property="orderby" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, date, day_id, oc_code, oc_name, is_own, vehicle_num, work_vehicle_num, free_vehicle_num, free_vehicle_rate, no_bind_vehicle_num, no_attendance_vehicle_num, orderby
    </sql>

</mapper>
