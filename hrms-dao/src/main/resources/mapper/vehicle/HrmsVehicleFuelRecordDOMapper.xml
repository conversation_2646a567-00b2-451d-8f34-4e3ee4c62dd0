<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.vehicle.mapper.HrmsVehicleFuelRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.vehicle.model.HrmsVehicleFuelRecordDO">
        <id column="id" property="id"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_date" property="createDate"/>
        <result column="create_user_code" property="createUserCode"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="last_upd_user_code" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" property="lastUpdUserName"/>
        <result column="last_upd_date" property="lastUpdDate"/>
        <result column="record_version" property="recordVersion"/>
        <result column="country" property="country"/>
        <result column="dept_id" property="deptId"/>
        <result column="refueling_time" property="refuelingTime"/>
        <result column="user_id" property="userId"/>
        <result column="user_code" property="userCode"/>
        <result column="user_name" property="userName"/>
        <result column="sys_account_name" property="sysAccountName"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="kilometers" property="kilometers"/>
        <result column="fuel_quantity" property="fuelQuantity"/>
        <result column="price" property="price"/>
        <result column="fuel_cost" property="fuelCost"/>
        <result column="is_latest" property="isLatest"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id, country, company_id, dept_id, refueling_time, user_id, user_code, user_name, sys_account_name, vehicle_no, kilometers, fuel_quantity, price, fuel_cost, is_latest
    </sql>

</mapper>
