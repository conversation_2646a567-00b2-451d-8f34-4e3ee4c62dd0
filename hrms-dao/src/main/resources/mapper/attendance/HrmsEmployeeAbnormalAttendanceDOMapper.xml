<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.attendance.mapper.HrmsEmployeeAbnormalAttendanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.attendance.model.HrmsEmployeeAbnormalAttendanceDO">
        <id column="id" property="id" />
        <result column="dept_id" property="deptId" />
        <result column="post_id" property="postId" />
        <result column="date" property="date" />
        <result column="day_id" property="dayId" />
        <result column="leader_id" property="leaderId" />
        <result column="user_id" property="userId" />
        <result column="location_country" property="locationCountry" />
        <result column="staff_type" property="staffType" />
        <result column="employee_type" property="employeeType" />
        <result column="scan_count" property="scanCount" />
        <result column="scan_type" property="scanType" />
        <result column="abnormal_type" property="abnormalType" />
        <result column="attendance_type" property="attendanceType" />
        <result column="punch_config_id" property="punchConfigId" />
        <result column="attendance_duration" property="attendanceDuration" />
        <result column="absence_duration" property="absenceDuration" />
        <result column="status" property="status" />
        <result column="punch_class_config_id" property="punchClassConfigId" />
        <result column="punch_class_item_config_id" property="punchClassItemConfigId" />
        <result column="extend" property="extend" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_user_code,
        last_upd_user_name,
        last_upd_date,
        record_version,
        id,
        dept_id,
        post_id,
        date,
        day_id,
        leader_id,
        user_id,
        location_country,
        staff_type,
        employee_type,
        scan_count,
        scan_type, abnormal_type, attendance_type, punch_config_id, attendance_duration, absence_duration, status, punch_class_config_id, punch_class_item_config_id, extend
    </sql>

    <!--员工出勤异常列表查询-->
    <select id="list"
            parameterType="com.imile.hrms.dao.attendance.query.EmployeeAbnormalAttendancePageQuery"
            resultType="com.imile.hrms.dao.attendance.dto.EmployeeAbnormalAttendanceDTO">
        SELECT
        eaa.id as id,
        hui.id as userId,
        hui.user_code as userCode,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.sys_account_name as sysAccountName,
        hui.email as email,
        hui.profile_photo_url as profilePhotoUrl,
        eaa.dept_id as deptId,
        eaa.post_id as postId,
        eaa.date as date,
        eaa.day_id as dayId,
        eaa.leader_id as leaderId,
        eaa.staff_type as staffType,
        eaa.employee_type as employeetype,
        eaa.scan_count as scanCount,
        eaa.scan_type as sacnType,
        eaa.abnormal_type as abnormalType,
        eaa.status as status,
        eaa.attendance_type as attendanceType,
        eaa.punch_config_id as punchConfigId,
        eaa.punch_class_config_id as punchClassConfigId,
        eaa.punch_class_item_config_id as punchClassItemConfigId,
        eaa.last_upd_date as lastUpdDate,
        eaa.last_upd_user_name as lastUpdUserName,
        hui.location_country as country
        from hrms_employee_abnormal_attendance eaa
        inner join hrms_user_info hui on eaa.user_id = hui.id and eaa.is_delete = 0
        <if test="startDate!=null">
            and eaa.date >= #{startDate}
        </if>
        <if test="endDate!=null">
            and eaa.date &lt;#{endDate}
        </if>
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join hrms_attendance_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and
            acr.is_latest=1
            inner join hrms_attendance_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and
            ac.is_latest=1 and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        <if test="punchConfigNo!=null and punchConfigNo!=''">
            inner join hrms_attendance_punch_config_range as cr on cr.biz_id = hui.id and cr.is_delete=0 and
            cr.is_latest=1
            inner join hrms_attendance_punch_config as pc on pc.id = cr.punch_config_id and pc.is_delete=0 and
            pc.is_latest=1 and pc.punch_config_no = #{punchConfigNo}
        </if>
        <where>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="abnormalIds!=null and abnormalIds.size()>0">
                <foreach collection="abnormalIds" item="abnormalId" open="and eaa.id in (" close=")"
                         separator=",">
                    #{abnormalId}
                </foreach>
            </if>
            <!--            &lt;!&ndash;如果部门id不为空&ndash;&gt;-->
            <!--            <if test="hasDeptPermission!=null and hasDeptPermission == true">-->
            <!--                and (hui.dept_id in-->
            <!--                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">-->
            <!--                    #{deptId}-->
            <!--                </foreach>-->

            <!--                &lt;!&ndash;如果国家权限不为空 &ndash;&gt;-->
            <!--                <if test="hasCountryPermission != null and hasCountryPermission == true">-->
            <!--                    or hui.location_country in-->
            <!--                    <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"-->
            <!--                             separator=",">-->
            <!--                        #{locationCountry}-->
            <!--                    </foreach>-->
            <!--                </if>-->
            <!--                )-->
            <!--            </if>-->
            <!--            &lt;!&ndash;如果部门id为空 并且 国家不为空&ndash;&gt;-->
            <!--            <if test="(hasDeptPermission!=null and hasDeptPermission == false) and (hasCountryPermission != null and hasCountryPermission == true)">-->
            <!--                <foreach collection="authLocationCountryList" separator="," open="or hui.location_country in ("-->
            <!--                         close=")" item="locationCountry">-->
            <!--                    #{locationCountry}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="locationCountry!=null and locationCountry!=''">
                and hui.location_country = #{locationCountry}
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="email!=null and email!=''">
                and hui.email = #{email}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and eaa.employee_type = #{employeeType}
            </if>
            <if test="employeeTypeList!=null and employeeTypeList.size()>0">
                <foreach collection="employeeTypeList" item="employeeType" open="and eaa.employee_type in (" close=")"
                         separator=",">
                    #{employeeType}
                </foreach>
            </if>
            <if test="status!=null and status!=''">
                and eaa.status = #{status}
            </if>
            <if test="statusList!=null and statusList.size()>0">
                <foreach collection="statusList" separator="," open="and eaa.status in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="staffType!=null and staffType!=''">
                and eaa.staff_type = #{staffType}
            </if>
            <if test="staffTypes!=null and staffTypes.size()>0">
                <foreach collection="staffTypes" separator="," open="and eaa.staff_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="abnormalType!=null and abnormalType!=''">
                and eaa.abnormal_type = #{abnormalType}
            </if>
            <if test="abnormalTypeList!=null and abnormalTypeList.size()>0">
                <foreach collection="abnormalTypeList" separator="," open="and eaa.abnormal_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" item="userId" open="and eaa.user_id in (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="dayId!=null and dayId!=''">
                and eaa.day_id = #{dayId}
            </if>
            <if test="punchClassItemIdList!=null and punchClassItemIdList.size()>0">
                <foreach collection="punchClassItemIdList" item="punchClassItemId" open="and eaa.punch_class_item_config_id in (" close=")"
                         separator=",">
                    #{punchClassItemId}
                </foreach>
            </if>
            <if test="classId!=null and classId!=''">
                and eaa.punch_class_config_id = #{classId}
            </if>
            <!--            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
        </where>
        order by eaa.day_id desc, eaa.id desc
    </select>


    <!--员工出勤异常列表查询-->
    <select id="listNewAttendance"
            parameterType="com.imile.hrms.dao.attendance.query.EmployeeAbnormalAttendancePageQuery"
            resultType="com.imile.hrms.dao.attendance.dto.EmployeeAbnormalAttendanceDTO">
        SELECT
        eaa.id as id,
        hui.id as userId,
        hui.user_code as userCode,
        hui.work_no as workNo,
        hui.user_name as userName,
        hui.sys_account_name as sysAccountName,
        hui.email as email,
        hui.profile_photo_url as profilePhotoUrl,
        eaa.dept_id as deptId,
        eaa.post_id as postId,
        eaa.date as date,
        eaa.day_id as dayId,
        eaa.leader_id as leaderId,
        eaa.staff_type as staffType,
        eaa.employee_type as employeetype,
        eaa.scan_count as scanCount,
        eaa.scan_type as sacnType,
        eaa.abnormal_type as abnormalType,
        eaa.status as status,
        eaa.attendance_type as attendanceType,
        eaa.punch_config_id as punchConfigId,
        eaa.punch_class_config_id as punchClassConfigId,
        eaa.punch_class_item_config_id as punchClassItemConfigId,
        eaa.last_upd_date as lastUpdDate,
        eaa.last_upd_user_name as lastUpdUserName,
        hui.location_country as country
        from hrms_employee_abnormal_attendance eaa
        inner join hrms_user_info hui on eaa.user_id = hui.id and eaa.is_delete = 0
        <if test="startDate!=null">
            and eaa.date >= #{startDate}
        </if>
        <if test="endDate!=null">
            and eaa.date &lt;#{endDate}
        </if>
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join calendar_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and
            acr.is_latest=1
            inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and
            ac.is_latest=1 and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        <if test="punchConfigNo!=null and punchConfigNo!=''">
            inner join punch_config_range as cr on cr.biz_id = hui.id and cr.is_delete=0 and
            cr.is_latest=1
            inner join punch_config as pc on pc.id = cr.punch_config_id and pc.is_delete=0 and
            pc.is_latest=1 and pc.punch_config_no = #{punchConfigNo}
        </if>
        <where>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="abnormalIds!=null and abnormalIds.size()>0">
                <foreach collection="abnormalIds" item="abnormalId" open="and eaa.id in (" close=")"
                         separator=",">
                    #{abnormalId}
                </foreach>
            </if>
            <if test="locationCountry!=null and locationCountry!=''">
                and hui.location_country = #{locationCountry}
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="email!=null and email!=''">
                and hui.email = #{email}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and eaa.employee_type = #{employeeType}
            </if>
            <if test="employeeTypeList!=null and employeeTypeList.size()>0">
                <foreach collection="employeeTypeList" item="employeeType" open="and eaa.employee_type in (" close=")"
                         separator=",">
                    #{employeeType}
                </foreach>
            </if>
            <if test="status!=null and status!=''">
                and eaa.status = #{status}
            </if>
            <if test="statusList!=null and statusList.size()>0">
                <foreach collection="statusList" separator="," open="and eaa.status in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="staffType!=null and staffType!=''">
                and eaa.staff_type = #{staffType}
            </if>
            <if test="staffTypes!=null and staffTypes.size()>0">
                <foreach collection="staffTypes" separator="," open="and eaa.staff_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="abnormalType!=null and abnormalType!=''">
                and eaa.abnormal_type = #{abnormalType}
            </if>
            <if test="abnormalTypeList!=null and abnormalTypeList.size()>0">
                <foreach collection="abnormalTypeList" separator="," open="and eaa.abnormal_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" item="userId" open="and eaa.user_id in (" close=")"
                         separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="dayId!=null and dayId!=''">
                and eaa.day_id = #{dayId}
            </if>
            <if test="punchClassItemIdList!=null and punchClassItemIdList.size()>0">
                <foreach collection="punchClassItemIdList" item="punchClassItemId" open="and eaa.punch_class_item_config_id in (" close=")"
                         separator=",">
                    #{punchClassItemId}
                </foreach>
            </if>
            <if test="classId!=null and classId!=''">
                and eaa.punch_class_config_id = #{classId}
            </if>
            <!--            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
        </where>
        order by eaa.day_id desc, eaa.id desc
    </select>


    <!--员工出勤异常列表查询-->
    <select id="listCount"
            parameterType="com.imile.hrms.dao.attendance.query.EmployeeAbnormalAttendancePageQuery"
            resultType="java.lang.Integer">
        SELECT
        count(eaa.id)
        from hrms_employee_abnormal_attendance eaa
        inner join hrms_user_info hui on eaa.user_id = hui.id and eaa.is_delete = 0
        <if test="startDate!=null">
            and eaa.date >= #{startDate}
        </if>
        <if test="endDate!=null">
            and eaa.date &lt;#{endDate}
        </if>
        <where>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>
            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="locationCountry!=null">
                and hui.location_country = #{locationCountry}
            </if>
            <if test="email!=null and email!=''">
                and hui.email = #{email}
            </if>
            <if test="employeeType!=null and employeeType!=''">
                and eaa.employee_type = #{employeeType}
            </if>
            <if test="status!=null and status!=''">
                and eaa.status = #{status}
            </if>
            <if test="staffType!=null and staffType!=''">
                and eaa.staff_type = #{staffType}
            </if>
            <if test="staffTypes!=null and staffTypes.size()>0">
                <foreach collection="staffTypes" separator="," open="and eaa.staff_type in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="abnormalType!=null and abnormalType!=''">
                and eaa.abnormal_type = #{abnormalType}
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" separator="," open="and eaa.user_id in (" close=")" item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="statusList!=null and statusList.size()>0">
                <foreach collection="statusList" separator="," open="and eaa.status in (" close=")" item="status">
                    #{status}
                </foreach>
            </if>
            <!--<include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
        </where>
    </select>

</mapper>
