<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.attendance.mapper.HrmsAttendanceEmployeeDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="location_country" property="locationCountry" />
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="date" property="date"/>
        <result column="day_id" property="dayId"/>
        <result column="data_source" property="dataSource"/>
        <result column="attendance_type" property="attendanceType"/>
        <result column="concrete_type" property="concreteType"/>
        <result column="is_attendance" property="isAttendance"/>
        <result column="overtime_hours" property="overtimeHours"/>
        <result column="attendance_hours" property="attendanceHours"/>
        <result column="attendance_start_time" property="attendanceStartTime"/>
        <result column="attendance_end_time" property="attendanceEndTime"/>
        <result column="delivery_count" property="deliveryCount"/>
        <result column="delivered_count" property="deliveredCount"/>
        <result column="pick_up_count" property="pickUpCount"/>
        <result column="remark" property="remark"/>
        <result column="orderby" property="orderby"/>
        <result column="attendance_rate" property="attendanceRate"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
        <result column="leave_type" property="leaveType"/>
        <result column="leave_percent_salary" property="leavePercentSalary"/>
        <result column="leave_hours" property="leaveHours"/>
        <result column="stage" property="stage"/>
        <result column="scan_type" property="scanType"/>
        <result column="picture_path" property="picturePath"/>
        <result column="leave_minutes" property="leaveMinutes"/>
        <result column="attendance_minutes" property="attendanceMinutes"/>
        <result column="overtime_minutes" property="overtimeMinutes"/>
        <result column="legal_working_hours" property="legalWorkingHours"/>
        <result column="form_id" property="formId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_delete ,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        id, user_id, location_country, year, month, day, date, day_id, data_source, attendance_type, concrete_type, is_attendance, overtime_hours, attendance_hours, attendance_start_time, attendance_end_time, delivery_count,delivered_count,pick_up_count, remark, orderby,
        leave_type,leave_percent_salary,leave_hours, stage, scan_type, picture_path, leave_minutes, attendance_minutes, overtime_minutes, legal_working_hours, form_id
    </sql>

    <!--员工出勤列表查询-->
    <select id="list"
            parameterType="com.imile.hrms.dao.attendance.query.AttendanceEmployeeQuery"
            resultType="com.imile.hrms.dao.attendance.dto.AttendanceDTO">
        SELECT
        hui.id as id,
        hui.id as userId,
        hui.profile_photo_url as profilePhotoUrl,
        hui.user_name as userName,
        hui.work_no as workNo,
        hui.email as email,
        hui.dept_id as deptId,
        hui.post_id as postId,
        <include refid="Base_Attendance_Sql"></include>
        from hrms_user_info hui
        left join hrms_attendance_employee_detail haed on haed.user_id = hui.id
        <if test="startTime!=null">
            and haed.date >= #{startTime}
        </if>
        <if test="endTime!=null">
            and haed.date &lt;#{endTime}
        </if>
        <where>
            hui.is_delete = 0 and hui.work_status is not null
            <if test="workNo!=null and workNo!=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>
            <if test="postId !=null">
                and hui.post_id = #{postId}
            </if>
            <if test="deptIds!=null and deptIds.size()>0">
                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                    #{deptId}
                </foreach>
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="userId">
                    #{userId}
                </foreach>
            </if>

            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>

        </where>
        group by hui.id
        order by hui.id asc
    </select>

    <sql id="Base_Attendance_Sql">

        sum(case when attendance_type = 'PRESENT' and haed.is_attendance =1 then haed.attendance_rate end) as
        attendanceDays,
        sum(case when haed.is_attendance=1 and attendance_type = 'PRESENT' and haed.overtime_hours>0 then 1 end) as
        overtimeDaysOnPresentDay,
        sum(case when haed.is_attendance=1 and attendance_type = 'PRESENT' and haed.overtime_hours>0 then overtime_hours
        end)
        as overtimeHoursOnPresentDay,
        sum(case when haed.is_attendance=1 and attendance_type = 'WEEKEND' then haed.attendance_rate end) as
        overtimeDaysOnWeekend,
        sum(case when haed.is_attendance=1 and attendance_type = 'WEEKEND' then haed.overtime_hours end) as
        overtimeHoursOnWeekend,
        sum(case when attendance_type = 'WEEKEND' then 1 end)
        as weekendCnt,
        sum(case when haed.is_attendance=1 and attendance_type = 'HOLIDAY' then haed.attendance_rate end) as
        overtimeDaysOnHoliday,
        sum(case when haed.is_attendance=1 and attendance_type = 'HOLIDAY' then haed.overtime_hours end) as
        overtimeHoursOnHoliday,
        sum(case when attendance_type = 'HOLIDAY' then 1 end)
        as holidayCnt,
        sum(haed.delivered_count) as dldCnt
    </sql>

<!--    <select id="listAttendance"-->
<!--            parameterType="com.imile.hrms.dao.attendance.query.UserAttendanceQuery"-->
<!--            resultType="com.imile.hrms.dao.attendance.dto.AttendanceDTO">-->

<!--        select-->
<!--        hui_temp.user_id_temp as id,-->
<!--        hui_temp.user_id_temp as userId,-->
<!--        hui_temp.profile_photo_url_temp as profilePhotoUrl,-->
<!--        hui_temp.user_name_temp as userName,-->
<!--        hui_temp.work_no_temp as workNo,-->
<!--        hui_temp.email as email,-->
<!--        hed.dept_name_cn as deptNameCn,-->
<!--        hed.dept_name_en as deptNameEn,-->
<!--        hep.post_name_cn as postNameCn,-->
<!--        hep.post_name_en as postNameEn,-->
<!--        hec.company_name_cn as companyNameCn,-->
<!--        hec.company_name_en as companyNameEn,-->
<!--        <include refid="Base_Attendance_Sql"></include>-->
<!--        from(-->
<!--        select-->
<!--        hui.id as user_id_temp,-->
<!--        hui.profile_photo_url as profile_photo_url_temp,-->
<!--        hui.user_name as user_name_temp,-->
<!--        hui.work_no as work_no_temp,-->
<!--        hui.email as email,-->
<!--        hui.dept_id as dept_id_temp,-->
<!--        &lt;!&ndash;hudr.actual_dimission_date as time_temp,&ndash;&gt;-->
<!--        hui.post_id as post_id_temp,-->
<!--        hui.company_id as company_id_temp-->
<!--        from-->
<!--        hrms_user_info hui-->
<!--        left join hrms_user_dimission_record hudr-->
<!--        on hudr.user_id =hui.id and hudr.dimission_status = 'DIMISSION'-->
<!--        <if test="startTime!=null">-->
<!--            and hudr.actual_dimission_date >= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime!=null">-->
<!--            and hudr.actual_dimission_date &lt;#{endTime}-->
<!--        </if>-->
<!--        <where>-->
<!--            hui.is_delete = 0 and (hui.work_status = 'ON_JOB' or hudr.id is not null and hui.work_status = 'DIMISSION')-->
<!--            <if test="workNo!=null and workNo!=''">-->
<!--                and hui.work_no = #{workNo}-->
<!--            </if>-->
<!--            <if test="userNameOrEmail!=null and userNameOrEmail!=''">-->
<!--                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like-->
<!--                concat('%',#{userNameOrEmail},'%'))-->
<!--            </if>-->
<!--            <if test="deptId!=null">-->
<!--                and hui.dept_id = #{deptId}-->
<!--            </if>-->
<!--            <if test="postId !=null">-->
<!--                and hui.post_id = #{postId}-->
<!--            </if>-->
<!--            <if test="deptIds!=null and deptIds.size()>0">-->
<!--                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">-->
<!--                    #{deptId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="country!=null">-->
<!--                and hui.origin_country = #{country}-->
<!--            </if>-->
<!--            <if test="userIds!=null and userIds.size()>0">-->
<!--                <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="userId">-->
<!--                    #{userId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->

<!--        union-->

<!--        select-->
<!--        hui.id as user_id_temp,-->
<!--        hui.profile_photo_url as profile_photo_url_temp,-->
<!--        hui.user_name as user_name_temp,-->
<!--        hui.work_no as work_no_temp,-->
<!--        hui.email as email,-->
<!--        hutr.before_dept_id as dept_id_temp,-->
<!--        &lt;!&ndash;hutr.transfer_time as time_temp,&ndash;&gt;-->
<!--        hutr.before_post_id as post_id_temp,-->
<!--        hutr.before_company_id as company_id_temp-->
<!--        from-->
<!--        hrms_user_info hui-->
<!--        inner join hrms_user_transfer_record hutr-->
<!--        on hutr.user_id =hui.id and hutr.execute_status = 'TRANSFER_SUCCESS'-->
<!--        and hutr.company_id != hutr.before_company_id-->
<!--        <if test="startTime!=null">-->
<!--            and hutr.transfer_time >= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime!=null">-->
<!--            and hutr.transfer_time &lt;#{endTime}-->
<!--        </if>-->
<!--        <where>-->
<!--            hui.is_delete = 0-->
<!--            <if test="workNo!=null and workNo!=''">-->
<!--                and hui.work_no = #{workNo}-->
<!--            </if>-->
<!--            <if test="userNameOrEmail!=null and userNameOrEmail!=''">-->
<!--                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like-->
<!--                concat('%',#{userNameOrEmail},'%'))-->
<!--            </if>-->
<!--            <if test="deptId!=null">-->
<!--                and hutr.before_dept_id = #{deptId}-->
<!--            </if>-->
<!--            <if test="postId !=null">-->
<!--                and hutr.before_post_id = #{postId}-->
<!--            </if>-->
<!--            <if test="deptIds!=null and deptIds.size()>0">-->
<!--                <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">-->
<!--                    #{deptId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="companyId!=null">-->
<!--                and hutr.before_company_id = #{companyId}-->
<!--            </if>-->
<!--            <if test="userIds!=null and userIds.size()>0">-->
<!--                <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="userId">-->
<!--                    #{userId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        ) hui_temp-->
<!--        left join hrms_attendance_employee_detail haed-->
<!--        on haed.user_id = hui_temp.user_id_temp and haed.company_id = #{companyId}-->
<!--        <if test="startTime!=null">-->
<!--            and haed.date >= #{startTime}-->
<!--        </if>-->
<!--        <if test="endTime!=null">-->
<!--            and haed.date &lt;#{endTime}-->
<!--        </if>-->

<!--        left join hrms_ent_dept hed on hui_temp.dept_id_temp = hed.id and hed.is_delete = 0-->
<!--        left join hrms_ent_company hec on hui_temp.company_id_temp = hec.id and hec.is_delete = 0-->
<!--        left join hrms_ent_post hep on hui_temp.post_id_temp = hep.id and hep.is_delete = 0-->

<!--        group by hui_temp.user_id_temp-->
<!--        order by hui_temp.user_id_temp asc-->

<!--    </select>-->


    <!--按月查询员工每日出勤明细-->
    <select id="listAttendanceDetail"
            parameterType="com.imile.hrms.dao.attendance.query.AttendanceEmployeeDetailQuery"
            resultType="com.imile.hrms.dao.attendance.dto.AttendanceDetailDTO">
        SELECT
        haed.id as id,
        haed.attendance_type as attendanceType,
        haed.is_attendance as isAttendance,
        haed.attendance_rate as attendanceRate,
        if(haed.attendance_type ='PRESENT',1,0) as isNeedAttendance,
        haed.date as date,
        haed.day as day,
        haed.day_id as dayId,
        haed.delivered_count as dldCnt,
        overtime_hours as overtimeHours
        FROM
        hrms_attendance_employee_detail haed
        <where>
            haed.is_delete = 0
            <if test="userId!=null">
                and haed.user_id = #{userId}
            </if>

            <if test="year!=null">
                and haed.year = #{year}
            </if>
        </where>
        order by haed.date asc
    </select>

    <select id="listDrivers"
            parameterType="com.imile.hrms.dao.attendance.query.XxlDriverQuery"
            resultType="com.imile.hrms.dao.attendance.dto.DriverCodeDTO">
        select
        hui.user_code as driverCode,
        <include refid="SELECT_COLUMN_SQL"></include>
        from
        hrms_attendance_employee_detail haed
        inner join hrms_user_info hui
        on haed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null and hui.is_driver = 1
        <where>
            haed.is_delete = 0


            <if test="startDate!=null">
                and haed.date >=#{startDate}
            </if>
        </where>
        order by hui.id,haed.id asc
        limit #{limit}
    </select>

    <select id="listAttendanceDrivers"
            parameterType="com.imile.hrms.dao.attendance.query.DriverAttendanceQuery"
            resultType="com.imile.hrms.dao.attendance.dto.DriverCodeDTO">
        select
        hui.user_code as driverCode,
        <include refid="SELECT_COLUMN_SQL"></include>
        from
        hrms_attendance_employee_detail haed
        inner join hrms_user_info hui
        on haed.user_id = hui.id and hui.is_delete = 0 and hui.work_status is not null and hui.is_driver = 1
        <where>
            haed.is_delete = 0
            <if test="dayId!=null">
                and haed.day_id = #{dayId}
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" item="userId" open="and haed.user_id in (" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>

        </where>
    </select>


    <sql id="SELECT_COLUMN_SQL">
        haed.is_delete,
        haed.record_version,
        haed.create_date,
        haed.create_user_code,
        haed.create_user_name,
        haed.last_upd_date,
        haed.last_upd_user_code,
        haed.last_upd_user_name,
        haed.id,
        haed.user_id,
        haed.year,
        haed.month,
        haed.day,
        haed.date,
        haed.day_id,
        haed.attendance_type,
        haed.concrete_type,
        haed.is_attendance,
        haed.overtime_hours,
        haed.attendance_hours,
        haed.attendance_start_time,
        haed.attendance_end_time,
        haed.delivery_count,
        haed.delivered_count,
        haed.pick_up_count,
        haed.remark,
        haed.orderby,
        haed.attendance_rate,
        haed.company_id,
        haed.dept_id,
        haed.post_id,
        haed.leave_type,
        haed.leave_percent_salary,
        haed.leave_hours,
        haed.scan_type
    </sql>


    <select id="userAttendanceList"
            parameterType="com.imile.hrms.dao.attendance.query.AttendanceEmployeeQuery"
            resultType="com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO">
        select
        <include refid="SELECT_COLUMN_SQL"></include>
        from hrms_attendance_employee_detail haed
        <where>
            haed.is_delete = 0
            <if test="startTime!=null">
                and haed.date &gt;= #{startTime}
            </if>
            <if test="endTime!=null">
                and haed.date &lt;= #{endTime}
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" item="userId" open="and haed.user_id in (" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="attendanceUserList"
            parameterType="com.imile.hrms.dao.attendance.query.UserAttendanceQuery"
            resultType="com.imile.hrms.dao.attendance.bo.UserAttendanceBO">
        select
        hui.id as userId,
        hui.dept_id as deptId,
        hui.post_id as postId
        from hrms_user_info hui
        <!--去掉关联考勤正常表，因为外面已经通过user_id查询了-->
        <!--left join hrms_attendance_employee_detail haed on haed.user_id = hui.id and haed.is_delete = 0 and haed.date &lt;= #{endTime} and haed.date &gt;= #{startTime}-->
        <if test="(attendanceConfigNo!=null and attendanceConfigNo!='') || (attendanceConfigNoList!=null and attendanceConfigNoList.size()>0)">
            inner join hrms_attendance_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
            inner join hrms_attendance_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1
        </if>
        <if test="attendanceConfigNo != null and attendanceConfigNo !=''">
            and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        <if test="attendanceConfigNoList!=null and attendanceConfigNoList.size()>0">
            <foreach collection="attendanceConfigNoList" separator="," open="and ac.attendance_config_no in (" close=")" item="attendanceConfigNo">
                #{attendanceConfigNo}
            </foreach>
        </if>
        <if test="(punchConfigNo!=null and punchConfigNo!='') || (punchConfigNoList!=null and punchConfigNoList.size()>0)">
            inner join hrms_attendance_punch_config_range as cr on cr.biz_id = hui.id and cr.is_delete=0 and cr.is_latest=1
            inner join hrms_attendance_punch_config as pc on pc.id = cr.punch_config_id and pc.is_delete=0 and pc.is_latest=1
        </if>
        <if test="punchConfigNo != null and punchConfigNo !=''">
            and pc.punch_config_no = #{punchConfigNo}
        </if>
        <if test="punchConfigNoList!=null and punchConfigNoList.size()>0">
            <foreach collection="punchConfigNoList" separator="," open="and pc.punch_config_no in (" close=")" item="punchConfigNo">
                #{punchConfigNo}
            </foreach>
        </if>
        <!--去掉关联考勤正常表，因为外面已经通过user_id查询了-->
        <!--<include refid="com.imile.hrms.dao.attendance.mapper.HrmsAttendanceEmployeeDetailMapper.resourceSql"></include>-->
        left join hrms_user_dimission_record dr on dr.user_id = hui.id
        inner join hrms_user_entry_record huer on huer.user_id = hui.id
        <where>
            hui.is_delete = 0
            and hui.is_virtual = 0
            and hui.user_code is not null
            and huer.is_delete = 0
            and huer.entry_date is not null
            <if test="country != null and country !=''">
                and hui.location_country = #{country}
            </if>
            <if test="countryList!=null and countryList.size()>0">
                <foreach collection="countryList" separator="," open="and hui.location_country in (" close=")" item="country">
                    #{country}
                </foreach>
            </if>
            <if test="userCode != null and userCode !=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="workNo!=null and workNo !=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>
            <if test="postId !=null">
                and hui.post_id = #{postId}
            </if>
            <if test="postIds!=null and postIds.size()>0">
                <foreach collection="postIds" separator="," open="and hui.post_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status !=''">
                and hui.status = #{status}
            </if>
            <if test="workStatus!=null and workStatus.size()>0">
                <foreach collection="workStatus" separator="," open="and hui.work_status in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="isDriver!=null">
                and hui.is_driver = #{isDriver}
            </if>
            <if test="isWarehouseStaff!=null">
                and hui.is_warehouse_staff = #{isWarehouseStaff}
            </if>
            <if test="employeeTypes!=null and employeeTypes.size()>0">
                <foreach collection="employeeTypes" separator="," open="and hui.employee_type in (" close=")"
                         item="employeeType">
                    #{employeeType}
                </foreach>
            </if>
            <if test="locationCityList!=null and locationCityList.size()>0">
                <foreach collection="locationCityList" separator="," open="and hui.location_city in (" close=")"
                         item="locationCity">
                    #{locationCity}
                </foreach>
            </if>
            <if test="startTime!=null and endTime!=null">
                <!--                and (dr.id is null or dr.dimission_status != 'DIMISSION' or (dr.dimission_status = 'DIMISSION' and dr.actual_dimission_date &lt;= #{endTime} and dr.actual_dimission_date &gt;= #{startTime}))-->
                and (dr.id is null or dr.dimission_status != 'DIMISSION' or (dr.dimission_status = 'DIMISSION' and dr.actual_dimission_date &gt;= #{startTime}))
            </if>
            <!--<include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
            group by hui.id,hui.dept_id,hui.post_id
        </where>
    </select>


    <select id="newAttendanceUserList"
            parameterType="com.imile.hrms.dao.attendance.query.UserAttendanceQuery"
            resultType="com.imile.hrms.dao.attendance.bo.UserAttendanceBO">
        select
        hui.id as userId,
        hui.dept_id as deptId,
        hui.post_id as postId
        from hrms_user_info hui
        <!--去掉关联考勤正常表，因为外面已经通过user_id查询了-->
        <!--left join hrms_attendance_employee_detail haed on haed.user_id = hui.id and haed.is_delete = 0 and haed.date &lt;= #{endTime} and haed.date &gt;= #{startTime}-->
        <if test="(attendanceConfigNo!=null and attendanceConfigNo!='') || (attendanceConfigNoList!=null and attendanceConfigNoList.size()>0)">
            inner join calendar_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
            inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1
        </if>
        <if test="attendanceConfigNo != null and attendanceConfigNo !=''">
            and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        <if test="attendanceConfigNoList!=null and attendanceConfigNoList.size()>0">
            <foreach collection="attendanceConfigNoList" separator="," open="and ac.attendance_config_no in (" close=")" item="attendanceConfigNo">
                #{attendanceConfigNo}
            </foreach>
        </if>
        <if test="(punchConfigNo!=null and punchConfigNo!='') || (punchConfigNoList!=null and punchConfigNoList.size()>0)">
            inner join punch_config_range as cr on cr.biz_id = hui.id and cr.is_delete=0 and cr.is_latest=1
            inner join punch_config as pc on pc.id = cr.punch_config_id and pc.is_delete=0 and pc.is_latest=1
        </if>
        <if test="punchConfigNo != null and punchConfigNo !=''">
            and pc.punch_config_no = #{punchConfigNo}
        </if>
        <if test="punchConfigNoList!=null and punchConfigNoList.size()>0">
            <foreach collection="punchConfigNoList" separator="," open="and pc.punch_config_no in (" close=")" item="punchConfigNo">
                #{punchConfigNo}
            </foreach>
        </if>
        <!--去掉关联考勤正常表，因为外面已经通过user_id查询了-->
        <!--<include refid="com.imile.hrms.dao.attendance.mapper.HrmsAttendanceEmployeeDetailMapper.resourceSql"></include>-->
        left join hrms_user_dimission_record dr on dr.user_id = hui.id
        inner join hrms_user_entry_record huer on huer.user_id = hui.id
        <where>
            hui.is_delete = 0
            and hui.is_virtual = 0
            and hui.user_code is not null
            and huer.is_delete = 0
            and huer.entry_date is not null
            <if test="country != null and country !=''">
                and hui.location_country = #{country}
            </if>
            <if test="countryList!=null and countryList.size()>0">
                <foreach collection="countryList" separator="," open="and hui.location_country in (" close=")" item="country">
                    #{country}
                </foreach>
            </if>
            <if test="userCode != null and userCode !=''">
                and hui.user_code = #{userCode}
            </if>
            <if test="workNo!=null and workNo !=''">
                and hui.work_no = #{workNo}
            </if>
            <if test="userIds!=null and userIds.size()>0">
                <foreach collection="userIds" separator="," open="and hui.id in (" close=")" item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="deptId!=null">
                and hui.dept_id = #{deptId}
            </if>
            <if test="postId !=null">
                and hui.post_id = #{postId}
            </if>
            <if test="postIds!=null and postIds.size()>0">
                <foreach collection="postIds" separator="," open="and hui.post_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status !=''">
                and hui.status = #{status}
            </if>
            <if test="workStatus!=null and workStatus.size()>0">
                <foreach collection="workStatus" separator="," open="and hui.work_status in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIds==null or deptIds.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIds!=null and deptIds.size()>0">
                    <foreach collection="deptIds" separator="," open="and hui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="isDriver!=null">
                and hui.is_driver = #{isDriver}
            </if>
            <if test="isWarehouseStaff!=null">
                and hui.is_warehouse_staff = #{isWarehouseStaff}
            </if>
            <if test="employeeTypes!=null and employeeTypes.size()>0">
                <foreach collection="employeeTypes" separator="," open="and hui.employee_type in (" close=")"
                         item="employeeType">
                    #{employeeType}
                </foreach>
            </if>
            <if test="locationCityList!=null and locationCityList.size()>0">
                <foreach collection="locationCityList" separator="," open="and hui.location_city in (" close=")"
                         item="locationCity">
                    #{locationCity}
                </foreach>
            </if>
            <if test="startTime!=null and endTime!=null">
                <!--                and (dr.id is null or dr.dimission_status != 'DIMISSION' or (dr.dimission_status = 'DIMISSION' and dr.actual_dimission_date &lt;= #{endTime} and dr.actual_dimission_date &gt;= #{startTime}))-->
                and (dr.id is null or dr.dimission_status != 'DIMISSION' or (dr.dimission_status = 'DIMISSION' and dr.actual_dimission_date &gt;= #{startTime}))
            </if>
            <!--<include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
            group by hui.id,hui.dept_id,hui.post_id
        </where>
    </select>





    <select id="selectAttendanceMissList"
            parameterType="com.imile.hrms.dao.attendance.query.StaffAttendanceMissQuery"
            resultType="com.imile.hrms.dao.attendance.bo.UserAttendanceMissBO">
        select
        distinct(hui.user_code) as userCode
        from hrms_user_info hui
        left join hrms_attendance_employee_detail haed on haed.user_id = hui.id and haed.is_delete = 0 and haed.date
        &gt;= #{startDate} and haed.date &lt;= #{endDate}
        <where>
            hui.is_delete = 0
            and (haed.company_id = #{companyId} or hui.company_id = #{companyId})
            and hui.user_code is not null
            <if test="isDriver!=null">
                and hui.is_driver = #{isDriver}
            </if>
            <if test="employeeType!=null and employeeType.size()>0">
                <foreach collection="employeeType" separator="," open="and hui.employee_type in (" close=")"
                         item="employeeType">
                    #{employeeType}
                </foreach>
            </if>
            <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>
        </where>
    </select>

<!--    <sql id="resourceSql">-->
<!--        <if test="resourceType!=null and (resourceType=='DEPT_SYS' or resourceType=='HO_DEPT_SYS' or resourceType=='ATTENDANCE_SYS' or resourceType=='HQ_HR_P_S' or resourceType=='HQ_HR_C_B' or resourceType=='HRD' or resourceType=='COUNTRY_HR_P_S' or resourceType=='COUNTRY_HR_C_B' or resourceType=='HRM' or resourceType=='HOD' or resourceType=='HUB_LEADER' or resourceType=='FIN_COST_DATA')">-->
<!--            <foreach collection="organizationIds" item = 'id' open="and haed.dept_id in (" close=")" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </sql>-->

    <sql id="resourceSql">
        <if test="resourceType!=null">
            <if test="resourceType=='NONE'">
                AND haed.dept_id = -1
            </if>

            <!--部门数据权限-->
            <if test="resourceType=='DEPT_ID'">
                <!--0，标识全部数据权限-->
                <if test="!organizationIds.contains(0L)">
                    and haed.dept_id in
                    <foreach collection="organizationIds" item = 'id' open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            </if>

            <!--全部数据权限，不做任何操作-->
            <if test="resourceType=='ALL'">

            </if>

<!--           <if test='resourceType!="SYSTEM" and resourceType!="SYSTEM_STAFF" and resourceType!="FINANCIAL_SYS" and resourceType!="SYSTEM_ITEM"'>-->
<!--               <choose>-->
<!--                   <when test="organizationIds != null and organizationIds.size() > 0">-->
<!--                       <if test="!organizationIds.contains(0L)">-->
<!--                           and haed.dept_id in-->
<!--                           <foreach collection="organizationIds" item = 'id' open="(" close=")" separator=",">-->
<!--                               #{id}-->
<!--                           </foreach>-->
<!--                       </if>-->
<!--                   </when>-->
<!--                   &lt;!&ndash;有角色但无实际权限部门时拼接不存在的数据条件达到无数据返回的效果&ndash;&gt;-->
<!--                   <otherwise>-->
<!--                       AND haed.dept_id = -1-->
<!--                   </otherwise>-->
<!--               </choose>-->
<!--           </if>-->
        </if>
    </sql>

</mapper>
