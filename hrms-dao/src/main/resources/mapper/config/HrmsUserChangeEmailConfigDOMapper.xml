<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.hrms.dao.config.mapper.HrmsUserChangeEmailConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.imile.hrms.dao.config.model.HrmsUserChangeEmailConfigDO">
        <id column="id" property="id" />
        <result column="create_date" property="createDate" />
        <result column="create_user_code" property="createUserCode" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_upd_date" property="lastUpdDate" />
        <result column="last_upd_user_code" property="lastUpdUserCode" />
        <result column="last_upd_user_name" property="lastUpdUserName" />
        <result column="is_delete" property="isDelete" />
        <result column="record_version" property="recordVersion" />
        <result column="company_config_id" property="companyConfigId" />
        <result column="operation_type" property="operationType" />
        <result column="channel_type" property="channelType" />
        <result column="staff_type" property="staffType" />
        <result column="employee_type" property="employeeType" />
        <result column="receive_email_post" property="receiveEmailPost" />
        <result column="receive_email_user_code" property="receiveEmailUserCode" />
        <result column="receive_email_leader" property="receiveEmailLeader" />
        <result column="status" property="status" />
        <result column="extend" property="extend" />
        <result column="orderby" property="orderby" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        is_delete,
        record_version,
        id, company_config_id, operation_type, channel_type, staff_type, employee_type, receive_email_post, receive_email_user_code, receive_email_leader, status, extend, orderby
    </sql>

</mapper>
