package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.FieldDiff;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 人员教育经历表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_education_info")
public class UserEducationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 学历
     */
    @FieldDiff(isCore = true, order = 1)
    private String education;

    /**
     * 学校名称
     */
    @FieldDiff
    private String schoolName;

    /**
     * 专业
     */
    @FieldDiff
    private String major;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @FieldDiff
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @FieldDiff
    private Date endDate;

    /**
     * 是否全日制（0:否 1:是）
     */
    @FieldDiff
    private Integer isFullTime;

    /**
     * 学历证书路径
     */
    private String educationPath;

    /**
     * 学位证书路径
     */
    private String degreePath;

    /**
     * 其他文件
     */
    private String otherFiles;

}
