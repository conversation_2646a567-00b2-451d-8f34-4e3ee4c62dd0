package com.imile.hrms.dao.punch.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.punch.dao.AttendanceUserPunchRiskDao;
import com.imile.hrms.dao.punch.mapper.AttendanceUserPunchRiskMapper;
import com.imile.hrms.dao.punch.model.AttendanceUserPunchRiskDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/6/5 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceUserPunchRiskDaoImpl extends ServiceImpl<AttendanceUserPunchRiskMapper, AttendanceUserPunchRiskDO> implements AttendanceUserPunchRiskDao {

}
