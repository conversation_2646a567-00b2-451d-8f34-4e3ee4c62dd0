package com.imile.hrms.dao.organization.dao;

import com.imile.hrms.dao.organization.model.HrmsDeptActiveTaskDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.organization.model.HrmsDeptActiveTaskPlusDO;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
public interface HrmsDeptActiveTaskDao extends IService<HrmsDeptActiveTaskDO> {

    List<HrmsDeptActiveTaskPlusDO> selectListBy(Collection<Long> deptIdList, Collection<String> activeTypeCollection,
                                                Collection<String> activeStatusCollection,
                                                Date minTriggerTime,
                                                Date maxTriggerTime,
                                                String searchName);
}
