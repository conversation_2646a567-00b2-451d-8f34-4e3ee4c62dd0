package com.imile.hrms.dao.user.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.user.condition.UserTransformCustomConditionBuilder;
import com.imile.hrms.dao.user.model.UserTransformDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/10
 */
public interface UserTransformDao extends IService<UserTransformDO> {


    /**
     * 通过条件查询调动记录
     *
     * @param condition UserTransformCustomConditionBuilder
     * @return List<UserTransformDO>
     */
    List<UserTransformDO> selectByCustomCondition(UserTransformCustomConditionBuilder condition);

    /**
     * 根据人员ID及调动状态查询
     *
     * @param userId              人员ID
     * @param transformStatusList 调动状态列表
     * @return List<UserTransformDO>
     */
    List<UserTransformDO> selectByUserId(Long userId, List<Integer> transformStatusList);
}
