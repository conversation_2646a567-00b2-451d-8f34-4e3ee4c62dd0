package com.imile.hrms.dao.recruitment.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Collection;

import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountCooperatorDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @since 2024/2/22
 */

@Mapper
public interface HrmsRecruitmentJobHeadcountCooperatorDOMapper extends com.imile.hrms.dao.common.HrmsBaseMapper<HrmsRecruitmentJobHeadcountCooperatorDO> {
    int updateCooperatorStatusBy(@Param("updatedCooperatorStatus") Integer updatedCooperatorStatus,
                                 @Param("recruitmentJobHeadcountIdCollection") Collection<Long> recruitmentJobHeadcountIdCollection,
                                 @Param("cooperatorUserIdCollection") Collection<Long> cooperatorUserIdCollection);
}