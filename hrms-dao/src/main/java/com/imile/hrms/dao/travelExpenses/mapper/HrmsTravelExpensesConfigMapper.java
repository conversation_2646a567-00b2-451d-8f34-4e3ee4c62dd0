package com.imile.hrms.dao.travelExpenses.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.travelExpenses.dto.HrmsTravelExpensesConfigExportDTO;
import com.imile.hrms.dao.travelExpenses.dto.HrmsTravelExpensesConfigListDTO;
import com.imile.hrms.dao.travelExpenses.model.HrmsTravelExpensesConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 差旅费用配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Mapper
public interface HrmsTravelExpensesConfigMapper extends HrmsBaseMapper<HrmsTravelExpensesConfigDO> {

    /**
     * 差率配置项导出
     * @return
     */
    List<HrmsTravelExpensesConfigExportDTO> exportList();

    List<HrmsTravelExpensesConfigListDTO> configList(@Param("countryCodes") String countryCodes);
}
