package com.imile.hrms.dao.user.po;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@Data
public class UserEntryListPO {

    /**
     * 入职记录ID
     */
    private Long id;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户英文名称
     */
    private String userNameEn;

    /**
     * 岗位
     */
    private Long postId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 计划入职
     */
    private Date expectedEntryDate;

    /**
     * 实际入职日期
     */
    private Date entryDate;

    /**
     * 入职状态 入职状态：待发送邀请、待员工登记、待确认入职、已入职、已放弃入职
     */
    private String entryStatus;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 报到地
     */
    private String country;
    private String province;
    private String city;

    /**
     * 汇报上级
     */
    private Long leaderId;

    /**
     * 入职渠道（入职方式）
     */
    private String onboardingChannel;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;
}
