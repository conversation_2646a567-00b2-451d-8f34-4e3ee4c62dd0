package com.imile.hrms.dao.travelExpenses.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 差旅费用配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
public class HrmsTravelExpensesConfigExportDTO {


    /**
     * 费用项
     */
    private String expense;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 报销金额
     */
    private BigDecimal reimbursementAmount;


}
