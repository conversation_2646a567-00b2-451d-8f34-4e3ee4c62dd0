package com.imile.hrms.dao.punch.query;

import com.imile.common.query.BaseQuery;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2022/1/26
 */
@Data
@Builder
public class AttendancePunchRangeConfigQuery extends BaseQuery {

    /**
     * 业务ID
     */
    private Long bizId;
    /**
     * 业务ID集合
     */

    private Collection<Long> bizIds;

    /**
     * 范围类型 DEPT,USER
     */
    private String rangeType;
    /**
     * 考勤配置ID
     */
    private Long attendancePunchConfigId;
    /**
     * 考勤配置状态
     */
    private String attendancePunchConfigStatus;

    /**
     * 排除的考勤配置No
     */
    private Long notEqAttendancePunchConfigId;

    /**
     * 查询起始时间
     */
    private Date startTime;
    /**
     * 查询结束时间
     */
    private Date endTime;

}
