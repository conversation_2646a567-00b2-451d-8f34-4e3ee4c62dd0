package com.imile.hrms.dao.common;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 方案配置状态切换参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/30
 */
@Data
public class ConfigNoStatusSwitchParamDTO implements Serializable {
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String configNo;
    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String status;
}
