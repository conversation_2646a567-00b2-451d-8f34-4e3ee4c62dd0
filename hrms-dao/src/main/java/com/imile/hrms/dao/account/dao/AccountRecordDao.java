package com.imile.hrms.dao.account.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.account.model.AccountRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
public interface AccountRecordDao extends IService<AccountRecordDO> {

    /**
     * 根据业务ID及业务类型查询
     *
     * @param bizId   业务ID
     * @param bizType 业务类型
     * @return List<AccountRecordPO>
     */
    List<AccountRecordDO> selectByBizIdAndType(String bizId, String bizType);

    /**
     * 查询最新账号记录
     *
     * @param bizId           业务ID
     * @param bizType         业务类型
     * @param accountTypeCode 账号类型编码
     * @return AccountRecordDO
     */
    AccountRecordDO selectLatestOne(String bizId, String bizType, String accountTypeCode);
}
