package com.imile.hrms.dao.attendance.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AbnormalAttendanceQuery implements Serializable {
    private static final long serialVersionUID = -8836002091073641792L;

    private List<Long> userIds;

    private Date startDate;

    private Date endDate;

    private String dayId;

    private List<String> dayIds;

    private String status;

    private List<String> statusList;
}
