package com.imile.hrms.dao.user.query;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-8-8
 * @version: 1.0
 */
@Data
public class ExternalWarehouseInfoQuery extends ResourceQuery{
    /**
     * 这里包含员工姓名和系统账号名称
     */
    private String userName;

    /**
     * 手机号，没有区号
     */
    private String phone;

    /**
     * 网点ID
     */
    private List<Long> deptIdList;

    /**
     * 供应商编码(全称)
     */
    private String vendorCode;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 审核状态 (待审核，审核不通过)
     */
    private String auditStatus;

    /**
     * 是否仓内员工
     */
    private Integer isWarehouseStaff;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 用工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 是否DTL  1是  0不是
     */
    private Integer isDtl;

    /**
     * 是否是司机
     */
    private Integer isDriver;

    /**
     * 数据来源:供应商，hubleader
     */
    private String sourceType;

    /**
     * 审核状态 (待审核，审核不通过)(审批流)
     */
    private List<String> auditStatusList;

    private List<String> sourceTypeList;

    /**
     * 审批类型集合
     */
    private List<String> approvalTypeList;
}
