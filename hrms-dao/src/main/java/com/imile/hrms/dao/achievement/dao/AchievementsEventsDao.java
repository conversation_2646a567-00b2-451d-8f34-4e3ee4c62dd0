package com.imile.hrms.dao.achievement.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.achievement.model.AchievementsEventsDO;
import com.imile.hrms.dao.achievement.query.AchievementsEventsQuery;

import java.util.List;

/**
 * <p>
 * 绩效活动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface AchievementsEventsDao extends IService<AchievementsEventsDO> {


    List<AchievementsEventsDO> selectById(AchievementsEventsQuery eventsQuery);
}
