package com.imile.hrms.dao.freelancer.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * 前端交互查询类
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverAutoRegistryQuery extends ResourceQuery {

    /**
     * 状态（区分菜单） Pending Setting菜单传PASS即可
     */
    private String menuAuthStatus;

    /**
     * 姓名/编码
     */
    private String nameOrCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国家
     */
    private String country;

    /**
     * 审核状态
     */
    private String approvalStatus;

    /**
     * 国家列表 不需前端传值
     */
    private List<String> countryList;

    /**
     * 是否自主注册 0:否 / 1：是  不需前端传值
     */
    private Integer autoRegistry;
}