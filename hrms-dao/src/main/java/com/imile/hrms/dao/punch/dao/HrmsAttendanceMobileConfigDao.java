package com.imile.hrms.dao.punch.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.punch.dto.AttendanceMobileConfigListDTO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceMobileConfigDO;
import com.imile.hrms.dao.punch.query.AttendanceMobileConfigListQuery;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceMobileConfigDao
 * {@code @since:} 2024-05-24 19:03
 * {@code @description:}
 */
public interface HrmsAttendanceMobileConfigDao extends IService<HrmsAttendanceMobileConfigDO> {
    List<AttendanceMobileConfigListDTO> queryAttendanceMobileConfig(AttendanceMobileConfigListQuery query);

    List<HrmsAttendanceMobileConfigDO> queryAttendanceMobileConfigByUserCode(String userCode);

    List<HrmsAttendanceMobileConfigDO> queryHistoryMobileConfigByUserCode(String userCode);

    List<String> queryMobileConfigCountry(String userCode);
}
