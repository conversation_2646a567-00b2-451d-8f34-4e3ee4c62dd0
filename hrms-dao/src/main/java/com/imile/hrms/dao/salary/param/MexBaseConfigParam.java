package com.imile.hrms.dao.salary.param;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础配置项目
 *
 * <AUTHOR>
 */
@Data
public class MexBaseConfigParam implements IWelfareBaseConfigParam {


    private static final long serialVersionUID = 1L;

    /**
     * 每月uma 墨西哥国家公布的uma数据
     */
    private BigDecimal currentUma;

    /**
     * 假期天数 假期天数
     */
    private Integer vacationDay;

    /**
     * 假期的溢价比例薪资计算类型 固定填写或输入填写
     */
    private String dailyVacationPremium;

    /**
     * 溢价比例 溢价比例
     */
    private BigDecimal premiumRate;

    /**
     * 圣诞节津贴计算类型 固定填写或输入填写
     */
    private String christmasBonusOrAguinaldo;

    /**
     * 圣诞津贴倍数 圣诞津贴倍数
     */
    private BigDecimal aguinaldoRate;

    /**
     * 圣诞节计入日期 圣诞节津贴计入日期
     */
    private Date aguinaldoDate;
    /**
     * 最大 BLS与current_uma之间倍数关系
     */
    private BigDecimal maxBlsRate;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


}
