package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 绩效员工结果等级历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
//@TableName("achievement_employee_result_score_history")
public class AchievementEmployeeResultScoreHistoryDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 员工考核ID
     */
    private Long employeeAppraisalId;

    /**
     * 实际完成结果
     */
    private String realResult;

    /**
     * 考核责任人评级
     */
    private BigDecimal leaderRate;

    /**
     * 行权人结论 01同意 02不同意
     */
    private String exerciserConclusion;


}
