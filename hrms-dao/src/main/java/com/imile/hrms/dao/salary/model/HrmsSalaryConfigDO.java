package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 计薪方案配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_config")
public class HrmsSalaryConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 计薪方案编码
     */
    private String salaryConfigNo;

    /**
     * 计薪方案名称
     */
    private String salaryConfigName;

    /**
     * 适用国家
     */
    private String country;
    /**
     * 币种 缺省方案、自定义方案
     */
    private String currency;

    /**
     * 计薪方式
     */
    private String salaryMethod;

    /**
     * 计薪周期类型 月、周
     */
    private String cycleType;

    /**
     * 周期开始
     */
    private String cycleStart;

    /**
     * 周期结束
     */
    private String cycleEnd;

    /**
     * 是否包含休息日
     */
    private Integer isContainsDayOff;

    /**
     * 基础薪资比例
     */
    private BigDecimal baseSalaryRatio;
    /**
     * 每件薪资类型,固定值、输入填写
     */
    private String pieceSalaryType;
    /**
     * 每件薪资,当计薪方式包含计件且计件方式为固定值的时候，需要填写该值
     */
    private BigDecimal pieceSalary;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间 失效时间，默认为2099-1-1
     */
    private Date expireTime;

    /**
     * 每月出勤天数
     */
    private BigDecimal monthAttendanceDays;

    /**
     * 每天出勤小时数
     */
    private BigDecimal dayAttendanceHours;

    /**
     * 薪资组成，JSON数组
     */
    private String salaryItems;

    /**
     * 加班配置 JSON
     */
    private String overtimeConfig;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 薪资组成类型 0:百分比 1:固定 2:按基础薪资百分比
     */
    private Integer salaryType;

}
