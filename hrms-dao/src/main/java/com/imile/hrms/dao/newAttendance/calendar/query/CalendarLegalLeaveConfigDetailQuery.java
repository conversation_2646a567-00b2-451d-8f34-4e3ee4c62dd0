package com.imile.hrms.dao.newAttendance.calendar.query;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarLegalLeaveConfigDetailQuery
 * {@code @since:} 2025-02-24 20:12
 * {@code @description:}
 */
@Data
@ApiModel(description = "日历法定假期记录表详情查询入参")
public class CalendarLegalLeaveConfigDetailQuery {
    /**
     * 常驻地国家
     */
    @ApiModelProperty(value="常驻地国家")
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private String locationCountry;

    /**
     * 年份
     */
    @ApiModelProperty(value="年份")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer year;

    /**
     * 日历id
     */
    @ApiModelProperty(value="日历id")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long attendanceConfigId;
}
