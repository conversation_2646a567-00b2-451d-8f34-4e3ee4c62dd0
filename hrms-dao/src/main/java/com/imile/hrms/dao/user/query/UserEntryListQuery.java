package com.imile.hrms.dao.user.query;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@Data
public class UserEntryListQuery extends ResourceQuery {

    /**
     * 编号、账号或邮箱
     */
    private String noOrUserName;

    /**
     * 精确的用户名
     */
    private String exactedUserName;

    /**
     * 报道地-国家
     */
    private String country;

    /**
     * 报道地-省
     */
    private String province;

    /**
     * 报道地-城市
     */
    private String city;

    /**
     * 部门
     */
    private List<Long> deptIds = new ArrayList<>();

    /**
     * 常驻国
     */
    private List<String> locationCountryList = new ArrayList<>();

    /**
     * 入职状态
     */
    private List<String> entryStatusList;

    /**
     * 预计入职起始时间
     */
    private Date expectStartDate;

    /**
     * 预计入职结束时间
     */
    private Date expectEndDate;

    /**
     * 实际入职起始时间
     */
    private Date confirmStartDate;

    /**
     * 实际入职结束时间
     */
    private Date confirmEndDate;

    /**
     * 用工类型
     */
    private List<String> employeeTypes;

}
