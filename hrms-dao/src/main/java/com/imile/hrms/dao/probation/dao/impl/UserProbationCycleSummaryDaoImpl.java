package com.imile.hrms.dao.probation.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.entity.BaseDO;
import com.imile.hrms.dao.probation.dao.UserProbationCycleSummaryDao;
import com.imile.hrms.dao.probation.mapper.UserProbationCycleSummaryMapper;
import com.imile.hrms.dao.probation.model.UserProbationCycleSummaryDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 员工试用期目标综合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class UserProbationCycleSummaryDaoImpl
        extends ServiceImpl<UserProbationCycleSummaryMapper, UserProbationCycleSummaryDO>
        implements UserProbationCycleSummaryDao {

    @Override
    public int countCycleNumberByProbationId(Long userProbationId) {
        return this.count(new LambdaQueryWrapper<UserProbationCycleSummaryDO>()
                .eq(UserProbationCycleSummaryDO::getUserProbationId, userProbationId)
                .eq(BaseDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );
    }

    @Override
    public List<UserProbationCycleSummaryDO> selectByProbationId(Long probationId) {
        return this.list(new LambdaQueryWrapper<>(UserProbationCycleSummaryDO.class)
                .eq(UserProbationCycleSummaryDO::getUserProbationId, probationId)
                .eq(BaseDO::getIsDelete, IsDeleteEnum.NO.getCode()));
    }

    @Override
    public List<UserProbationCycleSummaryDO> selectByProbationIdList(List<Long> probationIdList) {
        return this.list(new LambdaQueryWrapper<>(UserProbationCycleSummaryDO.class)
                .in(UserProbationCycleSummaryDO::getUserProbationId, probationIdList)
                .eq(BaseDO::getIsDelete, IsDeleteEnum.NO.getCode()));
    }
}
