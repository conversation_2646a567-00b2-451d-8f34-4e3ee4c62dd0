package com.imile.hrms.dao.organization.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
public enum DeptBizAreaEnum {

    /**
     * 部门业务领域
     */
    DEFAULT(0, "默认"),
    SALES(1, "销售"),
    CUSTOMER_SERVICE(2, "客服"),
    OPERATION(3, "运营"),
//    BUSINESS(4, "商务"),
    FINANCE(5, "财务"),
    HUMAN_RESOURCE(6, "人事"),
    RESEARCH(7, "产研"),
    PROCUREMENT(8, "采购"),
    CSP(9, "加盟"),
    MANAGEMENT(10, "综合管理"),
    MANAGEMENT_REGION(11, "综合管理-区域"),
    LEGAL(12, "法务"),
    OFFICE(13, "办公室"),
//    CEO(14, "CEO"),
    INTERNAL_AUDIT(15, "内部审计"),
    ACCOUNT_MANAGEMENT(16, "客户管理"),
    BUSINESS_PROCESS(17, "流程管理"),
    COMMERCIAL_SOLUTION(18, "解决方案"),
    CONTRACT_COMMERCIAL(19, "合同管理"),
    MARKETING_PR(20, "市场公关"),
    ;

    private final Integer value;

    private final String desc;

    DeptBizAreaEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static DeptBizAreaEnum getInstance(Integer value) {
        for (DeptBizAreaEnum statusEnum : DeptBizAreaEnum.values()) {
            if (Objects.equals(value, statusEnum.getValue())) {
                return statusEnum;
            }
        }
        return DEFAULT;
    }
}
