package com.imile.hrms.dao.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.HyperLink;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/29
 */
@Data
public class UserCertificateDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 证件类型编码（详见枚举类：CertificateTypeEnum）
     */
    private String certificateTypeCode;

    /**
     * 证件类型描述
     */
    private String certificateTypeCodeDesc;

    /**
     * 证件号码
     */
    private String certificateCode;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateReceiptDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateExpireDate;

    /**
     * 证件正面照路径
     */
    @HyperLink(ref = "certificateFrontUrl")
    private String certificateFrontPath;

    /**
     * 证件正面照链接
     */
    private String certificateFrontUrl;

    /**
     * 证件背面照路径
     */
    @HyperLink(ref = "certificateBackUrl")
    private String certificateBackPath;

    /**
     * 证件背面照链接
     */
    private String certificateBackUrl;

    /**
     * 扩展字段列表
     */
    private List<UserCertificateFieldDTO> extendFieldList;
}
