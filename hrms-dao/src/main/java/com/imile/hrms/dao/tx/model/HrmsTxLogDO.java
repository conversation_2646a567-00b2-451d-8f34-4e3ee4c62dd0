package com.imile.hrms.dao.tx.model;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <p>
 * 事物日志表 事物日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@TableName("hrms_tx_log")
public class HrmsTxLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 业务唯一标识 业务唯一标识
     */
    private String bizId;

    /**
     * 业务类型 业务类型 确定入职/离职等
     */
    private String bizType;

    /**
     * 业务模块 事务状态标识   开启事务(START)/已回滚(ROLLED_BACK)/失败(FAILED)/已提交(SUBMITTED)
     */
    private String state;

    /**
     * 检查次数 检查次数
     */
    private Integer checkCount;

    /**
     * 下一次检查时间 下一次检查时间
     */
    private Date nextCheckTime;

    /**
     * 补偿参数 补偿参数
     */
    private String param;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 事务是否完结，如果已完结，该事务将不会被定时任务扫描
     */
    private Integer isCompleted;


}
