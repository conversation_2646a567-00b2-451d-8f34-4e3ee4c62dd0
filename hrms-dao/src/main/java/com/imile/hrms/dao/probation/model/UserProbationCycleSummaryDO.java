package com.imile.hrms.dao.probation.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 员工试用期周期总结表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_probation_cycle_summary")
public class UserProbationCycleSummaryDO  extends UserProbationBaseDO  {

    private static final long serialVersionUID = 1L;

    /**
     * 总结详情
     */
    private String summaryDetail;

    /**
     * 相关附件（最多存储五个附件）
     */
    private String summaryFiles;

}
