package com.imile.hrms.dao.salary.query;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/10 14:50
 * @version: 1.0
 */
@Data
public class SalaryItemReflectionQuery {
    /**
     * 薪资项类型(薪资项目/考勤项目)
     */
    private String itemType;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 是否创建费用项映射
     */
    private Integer isReflect;

    /**
     * 薪资项属性
     */
    private List<String> itemAttributeList;
}
