package com.imile.hrms.dao.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 部门考勤统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_punch_statistics")
public class HrmsAttendancePunchStatisticsDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 日期
     */
    private Date date;

    private Long dayId;

    /**
     * 在职人数
     */
    private Integer activeEmployees;

    /**
     * 排班人数
     */
    private Integer schedulingEmployees;

    /**
     * 在职人数
     */
    private BigDecimal schedulingRate;

    /**
     * 应出勤人数
     */
    private Integer requiredAttendance;

    /**
     * 应出勤率
     */
    private BigDecimal requiredAttendanceRate;

    /**
     * 打卡人数
     */
    private Integer punchCardEmployees;

    /**
     * 打卡勤率
     */
    private BigDecimal punchCardRate;

    /**
     * 脸部掌纹录入数
     */
    private Integer registrationNum;
    /**
     * 脸部掌纹录入率
     */
    private BigDecimal registrationRate;

    /**
     * 已注册的排班员工数
     */
    private Integer schedulingRegistrationNum;

    /**
     * 总打卡员工数(包含未排班)
     */
    private Integer allPunchEmployees;

    /**
     * 异常员工数
     */
    private Integer abnormalEmployees;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;


}
