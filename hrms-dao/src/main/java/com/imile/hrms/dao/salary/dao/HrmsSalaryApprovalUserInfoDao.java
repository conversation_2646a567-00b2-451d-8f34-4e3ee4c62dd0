package com.imile.hrms.dao.salary.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalaryApprovalUserInfoDO;

import java.util.List;

/**
 * <p>
 * 薪资申请员工信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
public interface HrmsSalaryApprovalUserInfoDao extends IService<HrmsSalaryApprovalUserInfoDO> {

    List<HrmsSalaryApprovalUserInfoDO> selectByFormIdList(List<Long> formIdList);

    List<HrmsSalaryApprovalUserInfoDO> selectByEmployeeSalaryInfoIdList(List<Long> employeeUserIdList);

}
