package com.imile.hrms.dao.newAttendance.punchConfig.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.common.enums.account.RangeTypeEnum;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchConfigRangeDao;
import com.imile.hrms.dao.newAttendance.punchConfig.mapper.PunchConfigRangeMapper;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchConfigRangeDO;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchConfigRangeByDateQuery;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchRangeConfigQuery;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchConfigRangeDO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/2/13
 * @Description
 */
@Component
@RequiredArgsConstructor
public class PunchConfigRangeDaoImpl extends ServiceImpl<PunchConfigRangeMapper, PunchConfigRangeDO> implements PunchConfigRangeDao {

    private final PunchConfigRangeMapper mapper;


    @Override
    public Boolean updateToOld(Long configId) {
        ((PunchConfigRangeDaoImpl) AopContext.currentProxy()).updateRangeRecord(null, configId);
        return Boolean.TRUE;
    }

    @Override
    public PunchConfigRangeDO getRangeByUserIdOrDeptId(Long userId, RangeTypeEnum user) {
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(PunchConfigRangeDO::getBizId, userId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangeByQuery(PunchRangeConfigQuery userQuery) {
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        if (CollectionUtils.isNotEmpty(userQuery.getBizIds())) {
            queryWrapper.in(PunchConfigRangeDO::getBizId, userQuery.getBizIds());
        }
        if (userQuery.getRangeType() != null) {
            queryWrapper.eq(PunchConfigRangeDO::getRangeType, userQuery.getRangeType());
        }
        if (userQuery.getStartTime() != null && userQuery.getEndTime() != null) {
            queryWrapper.and(param ->
                    param.ge(PunchConfigRangeDO::getExpireTime, userQuery.getStartTime())
                            .and(i -> i.lt(PunchConfigRangeDO::getEffectTime, userQuery.getEndTime())));
        }
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangesByPunchConfigId(Long punchConfigId) {
        if (punchConfigId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getPunchConfigId, punchConfigId);
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangesByBizIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(PunchConfigRangeDO::getBizId, userIdList);
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listUserAllPunchConfigRangesByBizIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getBizId, userIdList);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangesByDate(PunchConfigRangeByDateQuery query) {
        return mapper.selectPunchConfigRangeByDate(query);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangeByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getPunchConfigId, idList);
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangesByPunchConfigIds(List<Long> punchConfigIds) {
        if (CollectionUtils.isEmpty(punchConfigIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getPunchConfigId, punchConfigIds);
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangeByNeedPunch(Long punchConfigId,
                                                                    List<Long> userOrDeptIdList,
                                                                    Integer isNeedPunch) {
        if (CollectionUtils.isEmpty(userOrDeptIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getPunchConfigId, punchConfigId);
        queryWrapper.in(PunchConfigRangeDO::getBizId, userOrDeptIdList);
        queryWrapper.eq(PunchConfigRangeDO::getIsNeedPunch, isNeedPunch);
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateRangeRecord(List<Long> bizIds, Long configId) {
        LambdaQueryWrapper<PunchConfigRangeDO> updateWrapper = Wrappers.lambdaQuery();

        if (CollectionUtils.isNotEmpty(bizIds)) {
            updateWrapper.in(PunchConfigRangeDO::getBizId, bizIds);
        }
        if (Objects.nonNull(configId)) {
            updateWrapper.eq(PunchConfigRangeDO::getPunchConfigId, configId);
        }
        updateWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        updateWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        PunchConfigRangeDO configRangeDO = new PunchConfigRangeDO();
        BaseDOUtil.fillDOUpdate(configRangeDO);
        configRangeDO.setExpireTime(new Date());
        configRangeDO.setIsLatest(BusinessConstant.N);
        this.baseMapper.update(configRangeDO, updateWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listByBizIds(List<Long> bizIds) {
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getBizId, bizIds);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listByPage(int currentPage, int pageSize) {
        PageInfo<PunchConfigRangeDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
