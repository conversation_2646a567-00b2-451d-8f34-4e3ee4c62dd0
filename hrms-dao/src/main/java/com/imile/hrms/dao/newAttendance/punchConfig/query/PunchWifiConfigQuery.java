package com.imile.hrms.dao.newAttendance.punchConfig.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchWifiConfigQuery extends ResourceQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id
     */
    private List<Long> ids;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * wifi名称
     */
    private String wifiName;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 国家列表
     */
    private List<String> countryList;

    /**
     * 考勤业务覆盖国
     */
    private String bizCountry;
}
