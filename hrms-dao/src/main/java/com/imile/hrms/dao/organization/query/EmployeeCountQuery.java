package com.imile.hrms.dao.organization.query;

import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * 各类组织下的员工数查询类
 */
@Data
public class EmployeeCountQuery<R> {
    /**
     * 组织类型
     */
    private String typeCode;

    private Collection<R> ids;

    public EmployeeCountQuery() {
    }

    public EmployeeCountQuery(String typeCode, Collection<R> ids) {
        this.typeCode = typeCode;
        this.ids = ids;
    }
}
