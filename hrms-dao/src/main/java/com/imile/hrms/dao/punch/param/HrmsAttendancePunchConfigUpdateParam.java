package com.imile.hrms.dao.punch.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 考勤打卡规则修改权限Param
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendancePunchConfigUpdateParam implements Serializable {

    /**
     * 更新的时候需要传递
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String configNo;

    /**
     * 主负责人
     */
    private String principalUserCode;

    /**
     * 子负责人
     */
    private String subUserCodes;

}
