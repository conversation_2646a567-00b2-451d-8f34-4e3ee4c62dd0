package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.annotation.FieldDiff;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 人员签证表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_visa")
public class UserVisaDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 签证类型（1:旅游签证 2:商务签证 3:落地签证 4:工作签证）
     */
    @FieldDiff(isCore = true, order = 1)
    private Integer visaType;

    /**
     * 签证号
     */
    @FieldDiff
    private String visaNo;

    /**
     * 生效日期
     */
    @FieldDiff
    private Date startDate;

    /**
     * 失效日期
     */
    @FieldDiff
    private Date endDate;

    /**
     * 签发组织
     */
    @FieldDiff
    private String issueOrganization;

    /**
     * 签发地点
     */
    @FieldDiff
    private String issuePlace;

    /**
     * 签证正面照路径
     */
    @FieldDiff
    private String visaFrontPath;

    /**
     * 签证背面照路径
     */
    @FieldDiff
    private String visaBackPath;


}
