package com.imile.hrms.dao.attendance.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} AttendanceCycleConfigDetailParam
 * {@code @since:} 2024-10-31 11:34
 * {@code @description:}
 */
@Data
public class AttendanceCycleConfigDetailParam implements Serializable {

    private static final long serialVersionUID = -6693569777395158036L;
    /**
     * 考勤周期id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;
}
