package com.imile.hrms.dao.salary.query;

import com.imile.common.query.BaseQuery;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Data
@Builder
public class HrmsSalaryFrameConfigListQuery extends BaseQuery {

    /**
     * id列表
     */
    private List<Long> idList;

    /**
     * 国家列表
     */
    List<String> countryList;

    /**
     * 职级id
     */
    private Long gradeId;
}
