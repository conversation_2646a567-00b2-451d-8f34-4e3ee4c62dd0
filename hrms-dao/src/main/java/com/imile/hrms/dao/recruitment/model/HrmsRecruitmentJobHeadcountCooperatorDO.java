package com.imile.hrms.dao.recruitment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/2/22
 */

/**
 * 招聘岗位HC协助人表
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "hrms_recruitment_job_headcount_cooperator")
public class HrmsRecruitmentJobHeadcountCooperatorDO extends BaseDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * hc id
     */
    @TableField(value = "recruitment_job_headcount_id")
    private Long recruitmentJobHeadcountId;

    /**
     * 协助人ID
     */
    @TableField(value = "cooperator_user_id")
    private Long cooperatorUserId;

    @TableField(value = "cooperator_user_code")
    private String cooperatorUserCode;

    @TableField(value = "cooperator_user_name")
    private String cooperatorUserName;

    @TableField(value = "cooperator_user_name_en")
    private String cooperatorUserNameEn;

    /**
     * 协助状态（0: 待生效；10: 生效）
     */
    @TableField(value = "cooperator_status")
    private Integer cooperatorStatus;

    /**
     * 生效时间
     */
    @TableField(value = "active_date")
    private Date activeDate;
}