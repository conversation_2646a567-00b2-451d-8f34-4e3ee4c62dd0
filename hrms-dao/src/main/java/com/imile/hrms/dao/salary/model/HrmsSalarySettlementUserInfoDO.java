package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 薪资结算人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_settlement_user_info")
public class HrmsSalarySettlementUserInfoDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 计薪国
     */
    private String paymentCountry;

    /**
     * 计薪月份(202308)
     */
    private String paymentMonth;

    /**
     * 计薪方案编码
     */
    private String salarySchemeConfigNo;

    /**
     * 用户薪资档案ID
     */
    private Long salaryUserSchemeInfoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编码/账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 用户结算状态
     */
    private String status;

    /**
     * 用户工作状态
     */
    private String userWorkStatus;

    /**
     * 薪资与上月相比是否变动  0无，1变动
     */
    private Integer isSalaryChange;

    /**
     * 是否最新
     */
    private Integer isLatest;


}
