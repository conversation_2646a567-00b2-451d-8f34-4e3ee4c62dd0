package com.imile.hrms.dao.attendance.dto;

import lombok.Data;

import java.util.Date;


/**
 * 存储循环过程中的时间范围和索引信息
 * <AUTHOR>
 */
@Data
public class CycleHelpDTO {
    /**
     * 开始时间
     */
    private Date searchStartTime;
    /**
     * 结束时间
     */
    private Date searchEndTime;
    /**
     * 列表索引
     */
    private Integer userIndex;
    /**
     * 列表索引
     */
    private Integer deptIndex;
}
