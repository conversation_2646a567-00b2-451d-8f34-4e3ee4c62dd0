package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalarySettlementUserDetailDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalarySettlementUserDetailMapper;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserDetailDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementUserDetailQuery;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 薪资结算人员数据项明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Service
public class HrmsSalarySettlementUserDetailDaoImpl extends ServiceImpl<HrmsSalarySettlementUserDetailMapper, HrmsSalarySettlementUserDetailDO> implements HrmsSalarySettlementUserDetailDao {

    @Override
    public List<HrmsSalarySettlementUserDetailDO> listByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalarySettlementUserDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsSalarySettlementUserDetailDO::getId, idList);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalarySettlementUserDetailDO> listByQuery(HrmsSalarySettlementUserDetailQuery query) {
        LambdaQueryWrapper<HrmsSalarySettlementUserDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());

        if (CollectionUtils.isNotEmpty(query.getSalarySettlementUserInfoIdList())) {
            queryWrapper.in(HrmsSalarySettlementUserDetailDO::getSalarySettlementUserInfoId, query.getSalarySettlementUserInfoIdList());
        }
        if (StringUtils.isNotBlank(query.getDataSource())) {
            queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getDataSource, query.getDataSource());
        }
        if (query.getIsLatest() != null) {
            queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getIsLatest, query.getIsLatest());
        }
        if (CollectionUtils.isNotEmpty(query.getDataSourceList())) {
            queryWrapper.in(HrmsSalarySettlementUserDetailDO::getDataSource, query.getDataSourceList());
        }
        if (query.getSalarySubmitTemplateConfigId() != null) {
            queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getSalarySubmitTemplateConfigId, query.getSalarySubmitTemplateConfigId());
        }
        if (CollectionUtils.isNotEmpty(query.getSalaryUserCalculateIdList())) {
            queryWrapper.in(HrmsSalarySettlementUserDetailDO::getSalaryUserCalculateId, query.getSalaryUserCalculateIdList());
        }
        if (StringUtils.isNotBlank(query.getDataSource())) {
            queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getDataSource, query.getDataSource());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getStatus, query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getSalarySubmitTemplateConfigNo())) {
            queryWrapper.eq(HrmsSalarySettlementUserDetailDO::getSalarySubmitTemplateConfigNo, query.getSalarySubmitTemplateConfigNo());
        }
        return list(queryWrapper);
    }
}
