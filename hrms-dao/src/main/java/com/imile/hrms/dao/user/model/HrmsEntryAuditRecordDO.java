package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_entry_audit_record")
public class HrmsEntryAuditRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 审核人编码
     */
    private String auditUserCode;

    /**
     * 审核人用户名
     */
    private String auditUserName;

    /**
     * 原部门ID
     */
    private Long originDeptId;

    /**
     * 审核通过后部门id(就是用户要变更的网点，但要看审核是否通过，可能不通过)
     */
    private Long afterAuditDeptId;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * 数据类型:创建新司机，变更网点
     */
    private String dataType;

    /**
     * 数据来源:供应商，hubLeader
     */
    private String sourceType;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 是否为最新
     */
    private Integer isLatest;


}
