package com.imile.hrms.dao.recruitment.dao.impl;

import com.imile.hrms.dao.recruitment.model.HrmsWechatMessageSendTaskDO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.recruitment.mapper.HrmsWechatMessageSendTaskDOMapper;
import com.imile.hrms.dao.recruitment.dao.HrmsWechatMessageSendTaskDao;
/**
 * <AUTHOR>
 * @since 2024/4/1
 */

@Service
public class HrmsWechatMessageSendDaoImpl extends ServiceImpl<HrmsWechatMessageSendTaskDOMapper, HrmsWechatMessageSendTaskDO> implements HrmsWechatMessageSendTaskDao {

}
