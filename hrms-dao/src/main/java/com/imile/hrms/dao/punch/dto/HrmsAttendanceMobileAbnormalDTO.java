package com.imile.hrms.dao.punch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 移动打卡员工异常考勤类型
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class HrmsAttendanceMobileAbnormalDTO implements Serializable {

    private static final long serialVersionUID = -1191195428514938493L;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 打卡班次id
     */
    private Long punchClassConfigId;

    /**
     * 打卡班次时段id
     */
    private Long punchClassItemConfigId;

}
