package com.imile.hrms.dao.punch.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.common.annotation.ApiModel;
import com.imile.hrms.common.entity.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/6/5 
 * @Description
 */
@ApiModel(description = "用户打卡风险明细表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_user_punch_risk_detail")
public class AttendanceUserPunchRiskDetailDO extends BaseDO {

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户打卡风险表id
     */
    @ApiModelProperty(value = "用户打卡风险表id")
    private Long userPunchRiskId;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 天
     */
    @ApiModelProperty(value = "天")
    private Long dayId;

    /**
     * 打卡表id
     */
    @ApiModelProperty(value = "打卡表id")
    private Long employeePunchRecordId;

    /**
     * 打卡时间
     */
    @ApiModelProperty(value = "打卡时间")
    private Date punchTime;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * gps配置id
     */
    @ApiModelProperty(value = "gps配置id")
    private Long gpsConfigId;

    /**
     * gps地址名称
     */
    @ApiModelProperty(value = "gps地址名称")
    private String gpsConfigName;
}
