package com.imile.hrms.dao.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import com.imile.hrms.dao.salary.dto.SalaryItemDTO;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/31
 */
@Data
public class SalaryConfigEmployeeAddParam implements Serializable {
    private static final long serialVersionUID = -1940306329532598955L;
    /**
     * 用户ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private Long userId;

    /**
     * 国家
     */
    private String country;

    /**
     * 计薪方案ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class, Groups.Update.class})
    private Long salaryConfigId;
    /**
     * 薪资,必须大于0，如果计薪方式是非计件，则必填
     */
    @DecimalMin(value = "0", message = ValidCodeConstant.MIN, groups = {Groups.Add.class, Groups.Update.class})
    private BigDecimal salary;

    /**
     * 社保方案ID
     */
    private Long socialConfigId;
    /**
     * 社保方案基础配置项 JSON对象字符串，如果socialConfigId不为空，则该项值不能为空，对应的参数结构与社保方案保持一致，但如果涉及到输入填写，则会相应多参数
     * 普通社保：{@link SalaryEmployeeBaseConfigParam}
     * 墨西哥社保：{@link SalaryEmployeeMexBaseConfigParam}
     */
    private String socialBaseConfigItem;
    /**
     * 社保方案项目配置项，JSON数组
     */
    private List<SalaryEmployeeConfigItemParam> socialConfigItems;

    /**
     * 社保具体项目缴纳基数对应的选项（社保方案是自选项方案时，该项值不能为空）
     */
    private List<SalarySocialEmployeeItemParam> salarySocialEmployeeItems;

    /**
     * 公积金方案ID
     */
    private Long accumulationConfigId;
    /**
     * 公积金方案基础配置项 JSON对象字符串，如果accumulationConfigId不为空，则该项值不能为空，对应的参数结构与公积金方案保持一致，但如果涉及到输入填写，则会相应多参数
     * {@link SalaryEmployeeBaseConfigParam}
     */
    private String accumulationBaseConfigItem;
    /**
     * 公积金方案项目配置项，JSON数组
     */
    private List<SalaryEmployeeConfigItemParam> accumulationConfigItems;
    /**
     * 每件费用,如果是计薪方式是计件 且计件方式是输入填写，则必填
     */
    @DecimalMin(value = "0", message = ValidCodeConstant.MIN, groups = {Groups.Add.class, Groups.Update.class})
    private BigDecimal pieceFee;

    /**
     * 签证费
     */
    private BigDecimal visaFee;

    /**
     * 医保费
     */
    private BigDecimal medicareFee;

    /**
     * 机票费用
     */
    private BigDecimal airTicketFee;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;
    /**
     * 税收
     */
    private BigDecimal taxationFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 薪资组成，基础薪资有且仅有一条。 JSON数组
     */
    private List<SalaryItemParam> salaryItems;

}
