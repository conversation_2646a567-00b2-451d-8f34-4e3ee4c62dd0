package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserQualificationDao;
import com.imile.hrms.dao.user.mapper.UserQualificationMapper;
import com.imile.hrms.dao.user.model.UserQualificationDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 人员资格证书表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Service
public class UserQualificationDaoImpl extends ServiceImpl<UserQualificationMapper, UserQualificationDO> implements UserQualificationDao {

    @Override
    public List<UserQualificationDO> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserQualificationDO> queryWrapper = Wrappers.lambdaQuery(UserQualificationDO.class);
        queryWrapper.eq(UserQualificationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserQualificationDO::getUserId, userId);
        return super.list(queryWrapper);
    }
}
