package com.imile.hrms.dao.organization.dao.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.entity.BaseDOUtil;
import com.imile.hrms.dao.idwork.IHrmsIdWorker;
import com.imile.hrms.dao.organization.dao.HrmsEntPostDao;
import com.imile.hrms.dao.organization.dto.EntPostDTO;
import com.imile.hrms.dao.organization.mapper.HrmsEntPostMapper;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.organization.model.HrmsEntPostFamilyDO;
import com.imile.hrms.dao.organization.query.PostQuery;
import com.imile.hrms.dao.primary.entity.PostDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Service
public class HrmsEntPostDaoImpl extends ServiceImpl<HrmsEntPostMapper, HrmsEntPostDO> implements HrmsEntPostDao {
    @Autowired
    private IHrmsIdWorker hrmsIdWorker;


    @Override
    public List<EntPostDTO> listPost(PostQuery query) {
        return this.baseMapper.listPostV2(query);
    }

    @Override
    public List<HrmsEntPostDO> checkName(String nameCn, String nameEn) {

        Collection<String> names = new HashSet<>();
        if (StringUtils.isNotBlank(nameCn)) {
            names.add(nameCn);
        }
        if (StringUtils.isNotBlank(nameEn)) {
            names.add(nameEn);
        }
        if (names.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (StringUtils.isNotBlank(nameCn)) {
            // AND ( company_name_cn in(?,?) OR company_name_en in (?,?))
            queryWrapper.and(param ->
                    param.in(HrmsEntPostDO::getPostNameCn, names)
                            .or(i -> i.in(HrmsEntPostDO::getPostNameEn, names)));
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> getPostList() {
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsEntPostDO::getStatus, StatusEnum.ACTIVE);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> getUnbindFamilyPostList() {
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsEntPostDO::getStatus, StatusEnum.ACTIVE);
        queryWrapper.eq(HrmsEntPostDO::getJobSubCategoryId, 0);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> getByPostName(String postName) {
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsEntPostDO::getStatus, StatusEnum.ACTIVE);
        queryWrapper.and(param ->
                param.eq(HrmsEntPostDO::getPostNameCn, postName)
                        .or(i -> i.eq(HrmsEntPostDO::getPostNameEn, postName)));
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> listByPostList(List<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsEntPostDO::getStatus, StatusEnum.ACTIVE);
        queryWrapper.in(HrmsEntPostDO::getId, postIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsEntPostDO::getId, idList);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> getAllPostList() {
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsEntPostDO> getPostNameList(String postName) {
        LambdaQueryWrapper<HrmsEntPostDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostDO.class);
        queryWrapper.eq(HrmsEntPostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.like(RequestInfoHolder.isChinese(), HrmsEntPostDO::getPostNameCn, postName);
        queryWrapper.like(RequestInfoHolder.isChinese(), HrmsEntPostDO::getPostNameEn, postName);
        return this.list(queryWrapper);
    }
}
