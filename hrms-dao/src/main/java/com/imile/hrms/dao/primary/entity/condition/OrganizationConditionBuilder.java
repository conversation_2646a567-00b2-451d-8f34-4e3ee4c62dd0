package com.imile.hrms.dao.primary.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationConditionBuilder {

    /**
     * 组织ID列表
     */
    private List<Long> idList;

    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 组织编码列表
     */
    private List<String> organizationCodeList;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 组织别名
     */
    private String organizationAlias;

    /**
     * 组织类型
     */
    private String organizationType;

    /**
     * 负责人人员编码
     */
    private String leaderUserCode;

    /**
     * 组织状态
     */
    private String organizationStatus;

    /**
     * 国家
     */
    private String organizationCountry;
}
