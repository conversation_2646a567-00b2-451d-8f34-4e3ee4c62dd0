package com.imile.hrms.dao.salary.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.salary.dto.HrmsSalaryTaskConfigPageDTO;
import com.imile.hrms.dao.salary.model.HrmsSalaryTaskConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryTaskConfigPageQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 薪资计算任务自动生成配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Mapper
public interface HrmsSalaryTaskConfigMapper extends HrmsBaseMapper<HrmsSalaryTaskConfigDO> {

    /**
     * 列表页
     *
     * @param query
     * @return
     */
    List<HrmsSalaryTaskConfigPageDTO> pageList(HrmsSalaryTaskConfigPageQuery query);

}
