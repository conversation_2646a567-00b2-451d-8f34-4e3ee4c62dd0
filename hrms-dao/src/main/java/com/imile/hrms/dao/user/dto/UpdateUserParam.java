package com.imile.hrms.dao.user.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/25
 */
@Data
public class UpdateUserParam {
    /**
     * 当前员工编码
     */
    @NotNull
    private String userCode;
    /**
     * 头像key
     */
    private String key;

    /**
     * 图片url
     */
    private String url;

    /**
     * 人脸库同步
     */
    private Boolean syncFaceFeature = Boolean.TRUE;

}
