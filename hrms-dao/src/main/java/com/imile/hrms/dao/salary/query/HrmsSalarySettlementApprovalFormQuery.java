package com.imile.hrms.dao.salary.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/6
 */
@Data
@Builder
public class HrmsSalarySettlementApprovalFormQuery {

    /**
     * 申请人编码
     */
    private List<String> applyUserCodeList;

    /**
     * 申请单号(模糊查询)
     */
    private String applicationCode;

    /**
     * 单据编号列表
     */
    private List<String> applicationCodeList;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 计薪国
     */
    private String paymentCountry;

    /**
     * 计薪月份(202308)
     */
    private String paymentMonth;

    /**
     * 提报方式(常规月度提报&非窗口期提报)
     */
    private String submitType;

    /**
     * 薪资代办结算记录ID
     */
    private List<Long> salarySettlementAgentRecordIdList;

    /**
     * 薪资代办结算记录ID 权限（前端不传）
     */
    private List<Long> roleAgentRecordIdList;
}
