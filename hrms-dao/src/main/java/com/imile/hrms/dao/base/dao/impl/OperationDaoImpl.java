package com.imile.hrms.dao.base.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.base.dao.OperationDao;
import com.imile.hrms.dao.base.mapper.OperationMapper;
import com.imile.hrms.dao.base.model.OperationDO;
import com.imile.hrms.dao.base.model.condition.OperationConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 操作表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-02
 */
@Service
public class OperationDaoImpl extends ServiceImpl<OperationMapper, OperationDO> implements OperationDao {

    @Override
    public List<OperationDO> selectByCondition(OperationConditionBuilder condition) {
        LambdaQueryWrapper<OperationDO> queryWrapper = Wrappers.lambdaQuery(OperationDO.class);
        queryWrapper.eq(OperationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (StringUtils.isNotBlank(condition.getOperationModuleCode())) {
            queryWrapper.eq(OperationDO::getOperationModuleCode, condition.getOperationModuleCode());
        }
        if (CollectionUtils.isNotEmpty(condition.getOperationSceneCodeList())) {
            queryWrapper.in(OperationDO::getOperationSceneCode, condition.getOperationSceneCodeList());
        }
        if (CollectionUtils.isNotEmpty(condition.getOperationCodeList())) {
            queryWrapper.in(OperationDO::getOperationCode, condition.getOperationCodeList());
        }
        return super.list(queryWrapper);
    }
}
