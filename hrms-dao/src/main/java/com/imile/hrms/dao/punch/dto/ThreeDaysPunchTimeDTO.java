package com.imile.hrms.dao.punch.dto;

import com.imile.hrms.common.enums.punch.PunchTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/9
 */
@Data
public class ThreeDaysPunchTimeDTO {
    /**
     * 昨天最早打卡时间
     */
    private Date yesterdayEarlyPunchInTime;
    /**
     * 昨天理论上班打卡时间
     */
    private Date yesterdayLaterPunchOutTime;
    /**
     * 昨天最晚打卡时间
     */
    private Date yesterdayPunchInTime;
    /**
     * 昨天理论下班打卡时间
     */
    private Date yesterdayPunchOutTime;
    /**
     * 今天最早打卡时间
     */
    private Date todayEarlyPunchInTime;
    /**
     * 今天理论上班打卡时间
     */
    private Date todayLaterPunchOutTime;
    /**
     * 今天最晚打卡时间
     */
    private Date todayPunchInTime;
    /**
     * 今天理论下班打卡时间
     */
    private Date todayPunchOutTime;
    /**
     * 明天最早打卡时间
     */
    private Date tomorrowEarlyPunchInTime;
    /**
     * 明天理论上班打卡时间
     */
    private Date tomorrowLaterPunchOutTime;
    /**
     * 明天最晚打卡时间
     */
    private Date tomorrowPunchInTime;
    /**
     * 明天理论下班打卡时间
     */
    private Date tomorrowPunchOutTime;
    /**
     * 是否自由时间工作
     */
    private Boolean isFreeWork;
    /**
     * 是否跨天
     */
    private Boolean isCross;
    /**
     * 打卡类型，上班/下班
     */
    private PunchTypeEnum punchTypeEnum;
}
