package com.imile.hrms.dao.salary.query;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/1/11 17:56
 * @version: 1.0
 */
@Data
public class SalaryUserDetailQuery extends ResourceQuery {

    /**
     * 申请国
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String applyCountry;

    /**
     * 结算时间-年月(202308)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long settlementDate;

    /**
     * 发薪类型
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String salaryType;

    /**
     * 该月薪资报表配置ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long salaryReportConfigId;

    /**
     * 姓名或邮箱
     */
    private String userNameOrEmail;
    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;
    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门ID
     */
    private String deptIdStringList;

    /**
     * 供应商id
     */
    private String vendorIdListString;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 费用承担组织ID
     */
    private Long costOrgId;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 员工状态(用户在审批单据中的状态)
     */
    private String status;


    /**
     * 看该国家改月下是否有员工的费用信息不全， 1：是 0：否
     */
    private Integer isUserInfoIsNotCompleteNotice;


    /**
     * 前端不传，后端转换
     */
    private List<Long> deptIdList;

    /**
     * 前端不传，后端转换
     */
    private List<Long> vendorIdList;


    /**
     * 员工薪资明细表ID列表，点击筛选的时候传(增减项之和校验) salary/info/notice
     */
    private List<Long> idList;

    /**
     * 员工薪资明细表ID列表，点击筛选的时候传（银行信息校验） salary/info/notice
     */
    private List<Long> idItemList;

}
