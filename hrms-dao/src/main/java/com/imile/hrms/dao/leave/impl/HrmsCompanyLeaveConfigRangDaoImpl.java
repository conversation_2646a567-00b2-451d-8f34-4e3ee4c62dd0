package com.imile.hrms.dao.leave.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.leave.HrmsCompanyLeaveConfigRangDao;
import com.imile.hrms.dao.leave.mapper.HrmsCompanyLeaveConfigRangMapper;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigRangDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyLeaveConfigRangDaoImpl
 * {@code @since:} 2024-04-09 11:11
 * {@code @description:}
 */
@Service
public class HrmsCompanyLeaveConfigRangDaoImpl extends ServiceImpl<HrmsCompanyLeaveConfigRangMapper, HrmsCompanyLeaveConfigRangDO> implements HrmsCompanyLeaveConfigRangDao {

    @Override
    public List<HrmsCompanyLeaveConfigRangDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList) {
        if (CollUtil.isEmpty(allCompanyLeaveConfigIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HrmsCompanyLeaveConfigRangDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsCompanyLeaveConfigRangDO::getLeaveId, allCompanyLeaveConfigIdList);
        queryWrapper.eq(HrmsCompanyLeaveConfigRangDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    /**
     * 获取假期配置范围
     *
     * @param userCodeList 用户code
     * @return 假期配置范围
     */
    @Override
    public List<HrmsCompanyLeaveConfigRangDO> selectRangByUserCode(List<String> userCodeList) {
        if (CollUtil.isEmpty(userCodeList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HrmsCompanyLeaveConfigRangDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsCompanyLeaveConfigRangDO::getUserCode, userCodeList);
        queryWrapper.eq(HrmsCompanyLeaveConfigRangDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerCompanyLeaveConfigRang(List<HrmsCompanyLeaveConfigRangDO> addLeaveRang, List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang) {
        if (CollUtil.isNotEmpty(addLeaveRang)) {
            saveBatch(addLeaveRang);
        }
        if (CollUtil.isNotEmpty(updateLeaveRang)) {
            updateBatchById(updateLeaveRang);
        }
    }
}
