package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 人员工资卡表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_extend_pay_info")
public class UserWagesCardDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 支付国家
     */
    private String paymentCountry;

    /**
     * 工资卡开户银行
     */
    private String wagesCardBank;

    /**
     * 工资卡开户网点
     */
    private String wagesCardBankBranch;

    /**
     * 工资卡号
     */
    private String wagesCardNo;

    /**
     * 持卡人姓名
     */
    private String cardholderName;

    /**
     * 银行同业密码
     */
    @TableField("Interbank_password")
    private String interbankPassword;

    /**
     * 路由代码
     */
    private String swiftCode;

    /**
     * 工资卡正面照路径（多值英文逗号分隔）
     */
    private String wagesCardFrontPath;

    /**
     * 银行卡信息变更证明地址
     */
    private String bankCardProveUrl;

    /**
     * 是否最新（0:否 1:是）
     */
    private Integer isLatest;

    /**
     * 原始ID
     */
    private Long originalId;
}
