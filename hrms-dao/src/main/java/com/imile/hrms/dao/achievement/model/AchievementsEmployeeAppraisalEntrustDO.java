package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 员工考核委托表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("achievements_employee_appraisal_entrust")
public class AchievementsEmployeeAppraisalEntrustDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 员工考核ID
     */
    private Long employeeAppraisalId;

    /**
     * 被考核者员工ID
     */
    private Long userId;

    /**
     * 考核委托人员工ID
     */
    private Long principalUserId;

    /**
     * 委托状态（0:待确认 1:已接受 2:已拒绝 3:已取消）
     */
    private Integer entrustStatus;


}
