package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserVisaDao;
import com.imile.hrms.dao.user.mapper.UserVisaMapper;
import com.imile.hrms.dao.user.model.UserVisaDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 人员签证表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Service
public class UserVisaDaoImpl extends ServiceImpl<UserVisaMapper, UserVisaDO> implements UserVisaDao {

    @Override
    public List<UserVisaDO> selectByUserIdList(List<Long> userIdList) {
        LambdaQueryWrapper<UserVisaDO> queryWrapper = Wrappers.lambdaQuery(UserVisaDO.class);
        queryWrapper.eq(UserVisaDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserVisaDO::getUserId, userIdList);
        return super.list(queryWrapper);
    }
}
