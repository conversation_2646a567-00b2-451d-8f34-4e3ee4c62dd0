package com.imile.hrms.dao.salary.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;


import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/6
 */
@Data
public class SalaryBaseQuery extends ResourceQuery {
    /**
     * 姓名或邮箱
     */
    private String userNameOrEmail;
    /**
     * 用户账号或姓名
     */
    private String userCodeOrName;
    /**
     * 部门id
     */
    private Long deptId;
    private List<Long> deptIds;
    /**
     * 岗位id
     */
    private Long postId;

    private String country;

    private List<String> countryList;
}
