package com.imile.hrms.dao.attendance.mapper;

import com.imile.hrms.dao.attendance.bo.UserAttendanceBO;
import com.imile.hrms.dao.attendance.bo.UserAttendanceMissBO;
import com.imile.hrms.dao.attendance.dto.AttendanceDTO;
import com.imile.hrms.dao.attendance.dto.AttendanceDetailDTO;
import com.imile.hrms.dao.attendance.dto.DriverCodeDTO;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.dao.attendance.query.*;
import com.imile.hrms.dao.common.HrmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 员工出勤明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Mapper
@Repository
public interface HrmsAttendanceEmployeeDetailMapper extends HrmsBaseMapper<HrmsAttendanceEmployeeDetailDO> {
    /**
     * 员工出勤列表查询
     *
     * @param query
     * @return
     */
    List<AttendanceDTO> list(AttendanceEmployeeQuery query);

    /**
     * 按月查询员工每日出勤明细
     *
     * @param query
     * @return
     */
    List<AttendanceDetailDTO> listAttendanceDetail(AttendanceEmployeeDetailQuery query);


    /**
     * 获取有考勤数据的司机
     *
     * @param query
     * @return
     */
    List<DriverCodeDTO> listDrivers(XxlDriverQuery query);


    /**
     * 获取有考勤数据的司机
     *
     * @param query
     * @return
     */
    List<DriverCodeDTO> listAttendanceDrivers(DriverAttendanceQuery query);

//    /**
//     * 员工出勤列表查询
//     *
//     * @param query
//     * @return
//     */
//    List<AttendanceDTO> listAttendance(AttendanceEmployeeQuery query);


    /**
     * 查询员工、司机、仓内作业人员考勤列表
     * @param query
     * @return
     */
    List<HrmsAttendanceEmployeeDetailDO> userAttendanceList(UserAttendanceQuery query);

    /**
     * 根据条件查询有出勤记录的用户id
     * @param query
     * @return
     */
    List<UserAttendanceBO> attendanceUserList(UserAttendanceQuery query);


    /**
     * 根据条件查询有出勤记录的用户id
     * @param query
     * @return
     */
    List<UserAttendanceBO> newAttendanceUserList(UserAttendanceQuery query);


    /**
     * 根据条件查询员工缺勤数据
     * @param query
     * @return
     */
    List<UserAttendanceMissBO> selectAttendanceMissList(StaffAttendanceMissQuery query);
}
