package com.imile.hrms.dao.approval.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 申请单属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_application_form_attr")
public class HrmsApplicationFormAttrDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请单id
     */
    private Long formId;

    /**
     * 申请单属性key
     */
    private String attrKey;

    /**
     * 申请单属性value
     */
    private String attrValue;


}
