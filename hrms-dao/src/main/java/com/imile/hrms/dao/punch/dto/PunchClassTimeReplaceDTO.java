package com.imile.hrms.dao.punch.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2022/2/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PunchClassTimeReplaceDTO {
    /**
     * 旧记录的id
     */
    private Long newClassId;

    /**
     * 新记录的id
     */
    private Long oldClassId;

}
