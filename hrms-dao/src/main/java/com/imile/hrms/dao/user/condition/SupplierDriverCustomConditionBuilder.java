package com.imile.hrms.dao.user.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierDriverCustomConditionBuilder {

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 账号状态（ACTIVE:生效中 DISABLED:已失效）
     */
    private String accountStatus;

    /**
     * 审核状态（WAIT_AUDIT:待审核 NO_THROUGH:审核不通过 PASS:审核通过）
     */
    private String auditStatus;

}
