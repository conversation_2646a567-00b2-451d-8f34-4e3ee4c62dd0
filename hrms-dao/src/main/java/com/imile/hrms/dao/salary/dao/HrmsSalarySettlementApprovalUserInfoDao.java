package com.imile.hrms.dao.salary.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalUserInfoDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementApprovalUserInfoQuery;

import java.util.List;

/**
 * <p>
 * 薪资结算数据申请员工信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
public interface HrmsSalarySettlementApprovalUserInfoDao extends IService<HrmsSalarySettlementApprovalUserInfoDO> {

    /**
     * 根据条件查询
     *
     * @param query
     * @return
     */
    List<HrmsSalarySettlementApprovalUserInfoDO> listByQuery(HrmsSalarySettlementApprovalUserInfoQuery query);

}
