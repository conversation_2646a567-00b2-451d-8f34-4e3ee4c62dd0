package com.imile.hrms.dao.punch.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Wifi配置DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
public class HrmsAttendanceWifiConfigDTO implements Serializable {


    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * wifi名称
     */
    private String wifiName;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 考勤业务覆盖国
     */
    private String bizCountry;

    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;
    /**
     * 最近修改日期
     */
    private Date lastUpdDate;
}
