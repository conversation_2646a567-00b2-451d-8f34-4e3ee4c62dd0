package com.imile.hrms.dao.recruitment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 微信消息发送任务表
 * <AUTHOR>
 * @since 2024/4/1
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "hrms_wechat_message_send_task")
public class HrmsWechatMessageSendTaskDO extends BaseDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企微消息类型
     */
    @TableField(value = "msg_type")
    private String msgType;

    /**
     * 消息子类型，对应各个枚举类型，如WechatTextCardMessageEnum
     */
    @TableField(value = "sub_msg_type")
    private String subMsgType;

    /**
     * 消息体
     */
    @TableField(value = "msg_content")
    private String msgContent;

    /**
     * 任务状态（0: 待发送；10: 发送成功；20: 发送失败）
     */
    @TableField(value = "task_status")
    private Integer taskStatus;

    @TableField(value = "trigger_time")
    private Date triggerTime;

    @TableField(value = "user_codes")
    private String userCodes;

    @TableField(value = "extend_info")
    private String extendInfo;
}