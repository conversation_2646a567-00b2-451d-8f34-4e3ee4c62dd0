package com.imile.hrms.dao.base.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.base.model.CountryDO;
import com.imile.hrms.dao.base.model.condition.CountryConditionBuilder;

import java.util.List;

/**
 * <p>
 * 国家表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface CountryDao extends IService<CountryDO> {

    /**
     * 动态查询
     *
     * @param condition 条件构造器
     * @return List<CountryDO>
     */
    List<CountryDO> selectByCondition(CountryConditionBuilder condition);

    /**
     * 根据国际区号查询
     *
     * @param callingCode 国际区号
     * @return CountryDO
     */
    CountryDO selectByCallingCode(String callingCode);

    /**
     * 根据国家名称查询
     *
     * @param countryNameList 国家名称列表
     * @return List<CountryDO>
     */
    List<CountryDO> selectByCountryNameList(List<String> countryNameList);

    /**
     * 根据国家二字码查询
     *
     * @param shortNameList 国家二字码列表
     * @return List<CountryDO>
     */
    List<CountryDO> selectByShortNameList(List<String> shortNameList);

    /**
     * 根据国家三字码查询
     *
     * @param countryCodeList 国家三字码列表
     * @return List<CountryDO>
     */
    List<CountryDO> selectByCountryCodeList(List<String> countryCodeList);
}
