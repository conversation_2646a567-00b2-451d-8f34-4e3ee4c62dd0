package com.imile.hrms.dao.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_no_punch_user")
public class HrmsNoPunchUserDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 日期
     */
    private Date date;

    /**
     * 日期
     */
    private Long dayId;

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 类型
     */
    private String type;

    /**
     * 排序
     */
    private BigDecimal orderby;

}
