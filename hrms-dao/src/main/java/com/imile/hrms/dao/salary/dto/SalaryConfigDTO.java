package com.imile.hrms.dao.salary.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/29
 */
@Data
public class SalaryConfigDTO implements Serializable {
    private static final long serialVersionUID = -2534709174195713506L;
    private Long id;

    /**
     * 计薪方案编码
     */
    private String salaryConfigNo;
    /**
     * 状态
     */
    private String status;
    /**
     * 计薪方案名称
     */
    private String salaryConfigName;
    /**
     * 适用国家
     */
    private String country;
    /**
     * 币种 缺省方案、自定义方案
     */
    private String currency;
    /**
     * 计薪方式枚举，前端展示取salaryMethodDesc
     */
    private String salaryMethod;

    /**
     * 已绑定员工数
     */
    private Integer employeeCount;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建日期
     */

    private Date createDate;
    /**
     * 最近修改人
     */
    private String lastUpdUserName;
    /**
     * 最近修改日期
     */
    private Date lastUpdDate;
}
