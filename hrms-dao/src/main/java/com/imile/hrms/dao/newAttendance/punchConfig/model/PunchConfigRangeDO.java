package com.imile.hrms.dao.newAttendance.punchConfig.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 打卡规则配置适用范围表
 * <AUTHOR> chen
 * @Date 2025/2/13
 * @Description
 */
@ApiModel(description = "打卡规则配置适用范围表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("punch_config_range")
public class PunchConfigRangeDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    @ApiModelProperty(value = "业务ID 部门id、用户ID")
    private Long bizId;

    /**
     * 打卡规则方案ID
     */
    @ApiModelProperty(value = "打卡规则方案ID")
    private Long punchConfigId;

    /**
     * 打卡规则no
     */
    @ApiModelProperty(value = "打卡规则no")
    private String punchConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    @ApiModelProperty(value = "范围类型 DEPT,USER")
    private String rangeType;

    /**
     * 是否需要打卡 0:false,1:true
     */
    @ApiModelProperty(value = "是否需要打卡 0:false,1:true")
    private Integer isNeedPunch;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private Date expireTime;

    /**
     * 是否为最新
     */
    @ApiModelProperty(value = "是否为最新")
    private Integer isLatest;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private BigDecimal orderby;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long companyId;
}
