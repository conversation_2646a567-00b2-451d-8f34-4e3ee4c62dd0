package com.imile.hrms.dao.leave.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 路途假发放规则表：针对该表【hrms_company_leave_config_issue_rule】按派遣国远近的发放类型才有该表数据。
 */
@ApiModel(description = "路途假发放规则表：针对该表【hrms_company_leave_config_issue_rule】按派遣国远近的发放类型才有该表数据")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_journey_config")
public class HrmsCompanyLeaveJourneyConfigDO extends BaseDO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家发放规则表主键id
     */
    @ApiModelProperty(value = "国家发放规则表主键id")
    private Long issueRuleId;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家集合（,拼接）")
    private String country;

    /**
     * 路程假天数
     */
    @ApiModelProperty(value = "路程假天数")
    private BigDecimal journeyDays;
}