package com.imile.hrms.dao.recruitment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * HC表
 * <AUTHOR>
 * @since 2023/11/15
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "hrms_recruitment_job")
public class HrmsRecruitmentJobDO extends BaseDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 单据编号
     */
    @TableField(value = "application_code")
    private String applicationCode;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 部门名称
     */
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 业务所属国
     */
    @TableField(value = "biz_country")
    private String bizCountry;

    /**
     * 国家
     */
    @TableField(value = "country")
    private String country;

    /**
     * 省份
     */
    @TableField(value = "province")
    private String province;

    /**
     * 城市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 区域
     */
    @TableField(value = "region")
    private String region;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 单据状态
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 岗位名称
     */
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 岗位id，从岗位库取
     */
    @TableField(value = "post_id")
    private Long postId;

    /**
     * 申请人ID
     */
    @TableField(value = "apply_user_id")
    private Long applyUserId;

    @TableField(value = "apply_user_name")
    private String applyUserName;

    @TableField(value = "apply_user_name_en")
    private String applyUserNameEn;

    /**
     * 审批状态
     */
    @TableField(value = "approve_status")
    private Integer approveStatus;

    /**
     * 审批创建时间
     */
    @TableField(value = "approve_create_time")
    private Date approveCreateTime;

    /**
     * 审批通过时间
     */
    @TableField(value = "approved_time")
    private Date approvedTime;

    /**
     * HC审批单id
     */
    @TableField(value = "recruitment_job_approval_id")
    private Long recruitmentJobApprovalId;

    /**
     * 第一面试官(英文逗号分割)
     */
    @TableField(value = "first_round_interviewer_user_ids")
    private String firstRoundInterviewerUserIds;

    /**
     * 第二面试官(英文逗号分割)
     */
    @TableField(value = "second_round_interviewer_user_ids")
    private String secondRoundInterviewerUserIds;

    /**
     * 第三面试官(英文逗号分割)
     */
    @TableField(value = "third_round_interviewer_user_ids")
    private String thirdRoundInterviewerUserIds;

    /**
     * JD技能说明
     */
    @TableField(value = "job_responsibilities")
    private String jobResponsibilities;

    /**
     * JD资格说明
     */
    @TableField(value = "job_qualifications")
    private String jobQualifications;

    /**
     * JD附件(英文逗号分割)
     */
    @TableField(value = "job_file_urls")
    private String jobFileUrls;

    /**
     * IT资产(英文逗号分割)
     */
    @TableField(value = "it_assets_required")
    private String itAssetsRequired;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 前端页面步骤
     */
    @TableField(value = "step")
    private Integer step;

    /**
     * 业务领域
     */
    @TableField(value = "professional_function")
    private Integer professionalFunction;

    /**
     * 旧版本数据
     */
    @TableField(value = "old_version_data")
    private String oldVersionData;

    /**
     * 招聘类型 0:社会招聘 1:校园招聘 3:实习生招聘
     */
    @TableField(value = "recruitment_type")
    private Integer recruitmentType;

    /**
     * 飞书职位id
     */
    @TableField(value = "fs_job_id")
    private String fsJobId;
}