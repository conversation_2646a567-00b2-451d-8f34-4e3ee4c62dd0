package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 薪资项目组和薪资项关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_item_group_relation")
public class HrmsSalaryItemGroupRelationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 薪资项目组ID
     */
    private Long itemGroupId;

    /**
     * 薪资项ID
     */
    private Long itemConfigId;

    /**
     * 薪资项编码
     */
    private String itemConfigNo;

    /**
     * 薪资项序号
     */
    private Integer itemConfigSort;

    /**
     * 薪资项配置值(公式计算 ，固定值等)
     */
    private String itemConfigValue;

    /**
     * 薪资项公式
     */
    private String itemGroupFormula;


}
