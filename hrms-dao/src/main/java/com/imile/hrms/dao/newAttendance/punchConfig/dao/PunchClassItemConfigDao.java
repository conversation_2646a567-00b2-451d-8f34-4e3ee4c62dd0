package com.imile.hrms.dao.newAttendance.punchConfig.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchClassItemConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/13
 * @Description
 */
public interface PunchClassItemConfigDao extends IService<PunchClassItemConfigDO> {

    List<PunchClassItemConfigDO> listPunchClassItemsByClassId(List<Long> punchClassIds);

    List<PunchClassItemConfigDO> listByPage(int currentPage, int pageSize);

}
