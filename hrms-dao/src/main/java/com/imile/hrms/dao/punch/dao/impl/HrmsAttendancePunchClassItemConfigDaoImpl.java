package com.imile.hrms.dao.punch.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.punch.dao.HrmsAttendancePunchClassItemConfigDao;
import com.imile.hrms.dao.punch.mapper.HrmsAttendancePunchClassItemConfigMapper;
import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassItemConfigDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 考勤打卡规则班次时间配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-27
 */
@Service
public class HrmsAttendancePunchClassItemConfigDaoImpl extends ServiceImpl<HrmsAttendancePunchClassItemConfigMapper, HrmsAttendancePunchClassItemConfigDO> implements HrmsAttendancePunchClassItemConfigDao {

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> selectItemConfigByClassId(List<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsAttendancePunchClassItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsAttendancePunchClassItemConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.in(HrmsAttendancePunchClassItemConfigDO::getPunchClassId, classIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsAttendancePunchClassItemConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<HrmsAttendancePunchClassItemConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
