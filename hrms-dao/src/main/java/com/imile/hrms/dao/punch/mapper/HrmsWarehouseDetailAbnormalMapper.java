package com.imile.hrms.dao.punch.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.punch.dto.HrmsWarehouseDetailAbnormalDTO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailAbnormalDO;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailDO;
import com.imile.hrms.dao.punch.param.WarehouseDetailAbnormalParam;
import com.imile.hrms.dao.punch.param.WarehouseDetailParam;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 仓内统计关联异常表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Mapper
@Repository
public interface HrmsWarehouseDetailAbnormalMapper extends HrmsBaseMapper<HrmsWarehouseDetailAbnormalDO> {

    List<HrmsWarehouseDetailAbnormalDTO> selectJoinWarehouseDetailList(WarehouseDetailAbnormalParam param);
}
