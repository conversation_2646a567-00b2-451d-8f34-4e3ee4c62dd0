package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 司机派件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_driver_delivery_info")
public class HrmsDriverDeliveryInfoDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日期
     */
    private Date date;

    /**
     * 日期
     */
    private Long dayId;

    /**
     * 司机编号
     */
    private String driverCode;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 任务量
     */
    private Integer taskQuantity;

    /**
     * 单据更新数
     */
    private Integer documentUpdateNum;

    /**
     * 排序
     */
    private BigDecimal orderby;

    /**
     * 国家
     */
    private String country;

}
