package com.imile.hrms.dao.leave.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国家法定假期记录表
 */
@ApiModel(description="国家法定假期记录表")
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("hrms_company_legal_leave_config")
public class HrmsCompanyLegalLeaveConfigDO extends BaseDO {
    /**
    * 主键ID
    */
    @ApiModelProperty(value="主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 常驻地国家
    */
    @ApiModelProperty(value="常驻地国家")
    private String locationCountry;

    /**
     * 日历id
     */
    @ApiModelProperty(value="日历id")
    private Long attendanceConfigId;

    /**
    * 年份
    */
    @ApiModelProperty(value="年份")
    private Integer year;

    /**
    * 法定假期名称
    */
    @ApiModelProperty(value="法定假期名称")
    private String legalLeaveName;

    /**
    * 法定假期开始时间：legal_leave_start_day_id 示例：20240124
    */
    @ApiModelProperty(value="法定假期开始时间：legal_leave_start_day_id 示例：20240124")
    private Long legalLeaveStartDayId;

    /**
    * 法定假期结束时间：legal_leave_end_day_id 示例：20240124
    */
    @ApiModelProperty(value="法定假期结束时间：legal_leave_end_day_id 示例：20240124")
    private Long legalLeaveEndDayId;

    /**
    * 法定假期时长
    */
    @ApiModelProperty(value="法定假期时长")
    private Integer legalLeaveDuration;
}