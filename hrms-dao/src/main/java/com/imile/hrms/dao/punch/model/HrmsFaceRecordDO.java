package com.imile.hrms.dao.punch.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 仓内人脸识别记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_face_record")
public class HrmsFaceRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 人员id
     */
    private Long userId;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 仓内日期
     */
    private Date warehouseDate;

    /**
     * 刷脸记录时间
     */
    private Date faceRecordTime;

    /**
     * 人脸照
     */
    private String facePhoto;

    /**
     * 识别照
     */
    private String recognitionPhoto;

    /**
     * 识别得分
     */
    private BigDecimal recognitionScore;

    /**
     * 是否可使用（0:可使用 1:不可使用）
     */
    private Integer faceRecordStatus;
    /**
     * 是否证件上传（0:人脸 1:证件）
     */
    private Integer isCertificatesUpload;



}
