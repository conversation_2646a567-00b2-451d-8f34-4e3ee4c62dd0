package com.imile.hrms.dao.salary.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalaryAttendanceUserInfoDO;
import com.imile.hrms.dao.salary.query.SalaryAttendanceUserQuery;

import java.util.List;

/**
 * <p>
 * 薪资用户考勤数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
public interface HrmsSalaryAttendanceUserInfoDao extends IService<HrmsSalaryAttendanceUserInfoDO> {

    List<HrmsSalaryAttendanceUserInfoDO> selectUserAttendanceList(SalaryAttendanceUserQuery query);

    List<HrmsSalaryAttendanceUserInfoDO> selectUserAttendanceByAttendanceSchemeIdList(List<Long> salaryAttendanceSchemeIdList);

}
