package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryItemGroupConfigDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryItemGroupConfigMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemGroupConfigDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/20 18:17
 * @version: 1.0
 */
@Service
public class HrmsSalaryItemGroupConfigDaoImpl extends ServiceImpl<HrmsSalaryItemGroupConfigMapper, HrmsSalaryItemGroupConfigDO> implements HrmsSalaryItemGroupConfigDao {

    @Override
    public List<HrmsSalaryItemGroupConfigDO> selectItemGroupConfigByTemplateIdList(List<Long> itemTemplateIdList) {
        if (CollectionUtils.isEmpty(itemTemplateIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalaryItemGroupConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryItemGroupConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsSalaryItemGroupConfigDO::getItemTemplateId, itemTemplateIdList);
        return list(queryWrapper);
    }
}
