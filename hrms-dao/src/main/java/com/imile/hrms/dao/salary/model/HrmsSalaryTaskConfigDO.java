package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 薪资计算任务自动生成配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_task_config")
public class HrmsSalaryTaskConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请国家
     */
    private String country;

    /**
     * 定时任务类型（定时任务 / 时间窗口）
     */
    private String handlerType;

    /**
     * 期间类型
     */
    private String periodType;

    /**
     * 时间基准
     */
    private String timeReference;

    /**
     * 距离计薪周期结束天数
     */
    private Integer diff;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型(考勤报表/计算任务)
     */
    private String type;

    /**
     * 数据模板id（时间窗口类型）
     */
    private Long salarySubmitTemplateConfigId;

    /**
     * 数据模板编码（时间窗口类型）
     */
    private String salarySubmitTemplateConfigNo;

    /**
     * 时间窗口开始时间：距离计薪周期结束天数
     */
    private Integer startDiff;

    /**
     * 时间窗口结束时间：距离计薪周期结束天数
     */
    private Integer endDiff;


}
