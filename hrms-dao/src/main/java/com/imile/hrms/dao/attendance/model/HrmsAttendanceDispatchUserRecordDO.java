package com.imile.hrms.dao.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 派遣人员记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_dispatch_user_record")
public class HrmsAttendanceDispatchUserRecordDO extends BaseDO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 派遣开始/结束日期
     */
    private Date dispatchDate;

    /**
     * 派遣地国家
     */
    private String dispatchCountry;

    /**
     * 派遣人员编码
     */
    private String userCode;

    /**
     * 国籍编码
     */
    private String countryCode;

    /**
     * 派遣是否结束标志
     */
    private Integer endFlag;

    /**
     * 调动类型
     */
    private Integer transformType;
}
