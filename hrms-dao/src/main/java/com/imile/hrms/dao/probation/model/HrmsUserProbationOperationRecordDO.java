package com.imile.hrms.dao.probation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户试用期操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_probation_operation_record")
public class HrmsUserProbationOperationRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id 用户ID
     * 被操作人
     */
    private Long userId;

    /**
     * 操作内容中文
     */
    private String operationContentCn;

    /**
     * 操作内容英文
     */
    private String operationContentEn;

    /**
     * 操作码
     */
    private String operationCode;

}
