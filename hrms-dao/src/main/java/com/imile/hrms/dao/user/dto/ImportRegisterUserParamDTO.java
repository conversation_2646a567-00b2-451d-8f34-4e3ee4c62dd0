package com.imile.hrms.dao.user.dto;


import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelImport;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;


/**
 *
 * <AUTHOR>
 */
@Data
public class ImportRegisterUserParamDTO extends ExcelImport {

    /**
     * 姓名
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String userName;
    /**
     * 所属部门id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String  deptId;
    /**
     * 工作岗位
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String postName;

    /**
     * 所属网点名称
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String ocName;

    /**
     * 所属业务节点名称
     */
    private String bizModelName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 结算主体名称
     */
    private String settlementCenterName;

    /**
     * 上级用户邮箱
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String leaderEmail;
    /**
     * 国际区号
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String countryCallingCode;
    /**
     * 手机号码
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @Length(min = 6, max = 17,message = BusinessConstant.ValidateConstant.MOBILE_LENGTH_RANGE)
    private String phone;

    /**
     * 个人邮箱(非公司邮箱)
     */
    @Email(message = ValidCodeConstant.EMAIL)
    private String personalEmail;

    /**
     * 计划入职
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String planEntryDate;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String employeeType;
    /**
     * 供应商id
     */
    private String vendorName;
    /**
     * 是否司机
     */
    private String isDriver;

}
