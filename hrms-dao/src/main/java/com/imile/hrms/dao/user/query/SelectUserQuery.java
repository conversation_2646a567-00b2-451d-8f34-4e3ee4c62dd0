package com.imile.hrms.dao.user.query;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/11
 */
@Data
public class SelectUserQuery {
    /**
     * 是否需要走部门全限
     */
    private Boolean isNeedSeparate;
    /**
     * 姓名，英文名，邮箱 模糊匹配
     */
    private String userNameOrEmail;

    private String userCodeOrName;
    /**
     * 本次拉取是否是dtl
     */
    private Boolean isDtl;
    /**
     * 部门id
     */
    private List<Long> deptIds;
    /**
     * 当前所选部门
     */
    private Long deptId;

    /**
     * 用户ID
     */
    private Long userId;

    private String country;

    private String status;

    private String workStatus;
}
