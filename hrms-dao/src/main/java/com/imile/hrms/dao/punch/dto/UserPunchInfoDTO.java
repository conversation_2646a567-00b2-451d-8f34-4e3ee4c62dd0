package com.imile.hrms.dao.punch.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/9
 */
@Data
public class UserPunchInfoDTO {
    private Long configId;
    /**
     * 员工名称
     */
    private String userName;

    private String userCode;
    /**
     * 员工id
     */
    private Long userId;
    /**
     * 日期属性
     */
    private String dayType;
    /**
     * 打卡规则类型
     */
    private String attendancePunchType;
    /**
     * 上班是否外勤
     */
    private boolean isPunchInAddressPattern;

    /**
     * 下班是否外勤
     */
    private boolean isPunchOutAddressPattern;
    /**
     * 实际上班卡打卡时间
     */
    private String punchInTime;
    /**
     * 配置上班卡打卡时间
     */
    private String configPunchInTime;

    public Boolean getPunchInLate() {
        return isPunchInLate == null ? Boolean.FALSE : isPunchInLate;

    }
    public Boolean getPunchOutEarly() {
        return isPunchOutEarly == null ? Boolean.FALSE : isPunchOutEarly;
    }

    /**
     * 是否上班迟到打卡
     */
    private Boolean isPunchInLate;
    /**
     * 实际下班卡打卡时间
     */
    private String punchOutTime;
    /**
     * 配置下班卡打卡时间
     */
    private String configPunchOutTime;
    /**
     * 是否下班早退打卡
     */
    private Boolean isPunchOutEarly;

    /**
     * 地点
     */
    private String placeName;


    public Boolean getIsAddressPattern() {
        return isAddressPattern == null ? Boolean.FALSE : isAddressPattern;
    }

    /**
     * 地点是否匹配
     */
    private Boolean isAddressPattern;
    /**
     * 若打卡规则为 class(排班),校验是否设置了班次
     */
    private Boolean isSetClass;
    /**
     * 日期
     */
    private long dayId;
    /**
     * 上下班类型
     */
    private String punchType;
    /**
     * 是否免打卡
     */
    private Boolean isNoNeedPunch;

    public Boolean getIsNoNeedPunch() {
        return isNoNeedPunch == null ? Boolean.FALSE : isNoNeedPunch;
    }


}
