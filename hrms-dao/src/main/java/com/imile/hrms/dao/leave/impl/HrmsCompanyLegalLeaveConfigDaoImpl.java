package com.imile.hrms.dao.leave.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.leave.HrmsCompanyLegalLeaveConfigDao;
import com.imile.hrms.dao.leave.mapper.HrmsCompanyLegalLeaveConfigMapper;
import com.imile.hrms.dao.leave.model.HrmsCompanyLegalLeaveConfigDO;
import com.imile.hrms.dao.leave.query.LegalLeaveConfigQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyLegalLeaveConfigDaoImpl
 * {@code @since:} 2024-03-08 14:38
 * {@code @description:}
 */
@Service
public class HrmsCompanyLegalLeaveConfigDaoImpl extends ServiceImpl<HrmsCompanyLegalLeaveConfigMapper, HrmsCompanyLegalLeaveConfigDO> implements HrmsCompanyLegalLeaveConfigDao {

    @Autowired
    private HrmsCompanyLegalLeaveConfigMapper hrmsCompanyLegalLeaveConfigMapper;

    @Override
    public List<HrmsCompanyLegalLeaveConfigDO> queryByCondition(LegalLeaveConfigQuery query) {

        LambdaQueryWrapper<HrmsCompanyLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getLocationCountry()), HrmsCompanyLegalLeaveConfigDO::getLocationCountry, query.getLocationCountry());
        queryWrapper.in(CollUtil.isNotEmpty(query.getLocationCountryList()), HrmsCompanyLegalLeaveConfigDO::getLocationCountry, query.getLocationCountryList());
        if (ObjectUtil.isNotNull(query.getNextYear()) || ObjectUtil.isNotNull(query.getLastYear())) {
            // 如果下一年参数不为null，表示需要查询多年的数据，所以需要查询大于等于当前年份，小于等于下一年份的数据
            queryWrapper.ge(ObjectUtil.isNotNull(query.getLastYear()), HrmsCompanyLegalLeaveConfigDO::getYear, query.getLastYear());
            queryWrapper.le(ObjectUtil.isNotNull(query.getNextYear()), HrmsCompanyLegalLeaveConfigDO::getYear, query.getNextYear());
        } else {
            // 查询当前年份的数据
            queryWrapper.eq(ObjectUtil.isNotNull(query.getYear()), HrmsCompanyLegalLeaveConfigDO::getYear, query.getYear());
        }
        queryWrapper.in(CollUtil.isNotEmpty(query.getYearList()), HrmsCompanyLegalLeaveConfigDO::getYear, query.getYearList());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getAttendanceConfigId()), HrmsCompanyLegalLeaveConfigDO::getAttendanceConfigId, query.getAttendanceConfigId());
        queryWrapper.like(ObjectUtil.isNotEmpty(query.getLegalLeaveName()), HrmsCompanyLegalLeaveConfigDO::getLegalLeaveName, query.getLegalLeaveName());
        queryWrapper.eq(HrmsCompanyLegalLeaveConfigDO::getIsDelete, BusinessConstant.N);
        queryWrapper.orderByAsc(HrmsCompanyLegalLeaveConfigDO::getLegalLeaveStartDayId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsCompanyLegalLeaveConfigDO> queryByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }
        LambdaQueryWrapper<HrmsCompanyLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsCompanyLegalLeaveConfigDO::getId, ids);
        queryWrapper.orderByDesc(HrmsCompanyLegalLeaveConfigDO::getId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsCompanyLegalLeaveConfigDO> queryByConditionGroup(LegalLeaveConfigQuery query) {
        LambdaQueryWrapper<HrmsCompanyLegalLeaveConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjectUtil.isNotEmpty(query.getLocationCountry()), HrmsCompanyLegalLeaveConfigDO::getLocationCountry, query.getLocationCountry());
        queryWrapper.in(CollUtil.isNotEmpty(query.getLocationCountryList()), HrmsCompanyLegalLeaveConfigDO::getLocationCountry, query.getLocationCountryList());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getYear()), HrmsCompanyLegalLeaveConfigDO::getYear, query.getYear());
        return hrmsCompanyLegalLeaveConfigMapper.selectCompanyConfigList(query);
    }

    @Override
    public List<HrmsCompanyLegalLeaveConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<HrmsCompanyLegalLeaveConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
