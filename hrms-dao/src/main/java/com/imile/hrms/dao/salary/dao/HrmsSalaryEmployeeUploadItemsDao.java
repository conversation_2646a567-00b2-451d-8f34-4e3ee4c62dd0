package com.imile.hrms.dao.salary.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalaryEmployeeUploadItemsDO;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 员工薪酬上传项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
public interface HrmsSalaryEmployeeUploadItemsDao extends IService<HrmsSalaryEmployeeUploadItemsDO> {
    /**
     * 根据用户ids获取上传项
     *
     * @param userIds
     * @return
     */
    List<HrmsSalaryEmployeeUploadItemsDO> listUploadItemsByUserIds(List<Long> userIds, Date stateTime, Date endTime, String country);

}
