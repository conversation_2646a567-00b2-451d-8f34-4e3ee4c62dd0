package com.imile.hrms.dao.user.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.common.CommonCountDO;
import com.imile.hrms.dao.primary.entity.condition.DriverConditionBuilder;
import com.imile.hrms.dao.primary.entity.condition.UserConditionBuilder;
import com.imile.hrms.dao.user.condition.FreelancerDriverCustomConditionBuilder;
import com.imile.hrms.dao.user.condition.SupplierDriverCustomConditionBuilder;
import com.imile.hrms.dao.user.condition.UserCountConditionBuilder;
import com.imile.hrms.dao.user.condition.UserCountryCustomConditionBuilder;
import com.imile.hrms.dao.user.condition.UserOutsourceCustomConditionBuilder;
import com.imile.hrms.dao.user.condition.UserOwnCustomConditionBuilder;
import com.imile.hrms.dao.user.condition.UserReportConditionBuilder;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.po.SupplierDriverPO;
import com.imile.hrms.dao.user.po.UserCountryListPO;
import com.imile.hrms.dao.user.po.UserOutsourceExportPO;
import com.imile.hrms.dao.user.po.UserOutsourceListPO;
import com.imile.hrms.dao.user.po.UserOwnExportPO;
import com.imile.hrms.dao.user.po.UserOwnListPO;
import com.imile.hrms.dao.user.po.UserPermissionReportListPO;
import com.imile.hrms.dao.user.query.UserAssociateConditionBuilder;

import java.util.List;

/**
 * <p>
 * 人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
public interface UserDao extends IService<HrmsUserInfoDO> {

    /**
     * 根据动态条件查询
     *
     * @param builder UserConditionBuilder
     * @return List<UserDO>
     */
    List<HrmsUserInfoDO> selectByCondition(UserConditionBuilder builder);

    /**
     * 根据动态条件查询人员编码
     *
     * @param builder UserConditionBuilder
     * @return List<String>
     */
    List<String> selectUserCodeByCondition(UserConditionBuilder builder);

    /**
     * 根据动态条件查询司机ID
     *
     * @param condition DriverConditionBuilder
     * @return List<String>
     */
    List<Long> selectDriverIdByCondition(DriverConditionBuilder condition);

    /**
     * 联想查询
     *
     * @param builder UserAssociateConditionBuilder
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectByAssociateCondition(UserAssociateConditionBuilder builder);

    /**
     * 众包司机自定义查询
     *
     * @param builder FreelancerDriverCustomConditionBuilder
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectFreelancerDriverByCustomCondition(FreelancerDriverCustomConditionBuilder builder);

    /**
     * 根据人员编码查询
     *
     * @param userCode 人员编码
     * @return UserDO
     */
    HrmsUserInfoDO selectByUserCode(String userCode);

    /**
     * 根据部门ID批量查询人员编码
     *
     * @param deptIdList 部门ID列表
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectUserCodeByDeptIdList(List<Long> deptIdList);

    /**
     * 根据人员编码查询ID
     *
     * @param userCodeList 人员编码列表
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectIdByUserCode(List<String> userCodeList);

    /**
     * 根据人员ID查询编码
     *
     * @param idList 人员ID列表
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectUserCodeById(List<Long> idList);

    /**
     * 根据人员ID查询人员名称
     *
     * @param idList 人员ID列表
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectUserNameById(List<Long> idList);

    /**
     * 根据企业邮箱查询
     *
     * @param email 企业邮箱
     * @return HrmsUserInfoDO
     */
    List<HrmsUserInfoDO> selectByEmail(String email);

    /**
     * 联系电话是否重复
     *
     * @param phone 联系电话
     * @param id    人员ID
     * @return Boolean
     */
    Boolean isPhoneRepeat(String phone, Long id);

    /**
     * 企业邮箱是否已存在
     *
     * @param email    邮箱
     * @param userCode 人员编码
     * @return Boolean
     */
    Boolean isEmailExist(String email, String userCode);

    /**
     * 查询缺少金蝶关联关系的人员编码
     *
     * @return List<Long>
     */
    List<String> selectMissingRelation4KingDee();

    /**
     * 根据动态条件计数
     *
     * @param builder UserCountConditionBuilder
     * @return Long
     */
    Long countByCondition(UserCountConditionBuilder builder);

    /**
     * 根据动态条件分组统计人员数
     *
     * @param groupColumn 分组字段
     * @param builder     UserCountConditionBuilder
     * @return List<CommonCountDO>
     */
    List<CommonCountDO> countGroupByCondition(String groupColumn, UserCountConditionBuilder builder);

    /**
     * 查询常驻地国家维度权限的人员列表
     *
     * @param query query
     * @return List<UserCountryListPO>
     */
    List<UserCountryListPO> selectByCountryCondition(UserCountryCustomConditionBuilder query);

    /**
     * 查询常驻地国家维度权限的人员列表
     *
     * @param query query
     * @return List<UserCountryListPO>
     */
    List<UserOwnExportPO> selectExportByCountryCondition(UserCountryCustomConditionBuilder query);

    /**
     * 查询外包人员列表
     *
     * @param query query
     * @return List<UserCountryListPO>
     */
    List<UserOutsourceListPO> selectOutsourceByCondition(UserOutsourceCustomConditionBuilder query);

    /**
     * 查询外包人员导出数据
     *
     * @param query
     * @return UserOutsourceCustomConditionBuilder
     */
    List<UserOutsourceExportPO> selectOutsourceExportByCondition(UserOutsourceCustomConditionBuilder query);

    /**
     * 自有人员列表查询
     *
     * @param query UserOwnCustomConditionBuilder
     * @return List<UserOwnListPO>
     */
    List<UserOwnListPO> selectOwnByCondition(UserOwnCustomConditionBuilder query);

    /**
     * 自有人员导出查询
     *
     * @param query
     * @return List<UserOwnExportPO>
     */
    List<UserOwnExportPO> selectOwnExportByCondition(UserOwnCustomConditionBuilder query);

    /**
     * 人员报表列表查询
     *
     * @param query UserReportConditionBuilder
     * @return List<UserPermissionReportListPO>
     */
    List<UserPermissionReportListPO> selectPermissionReportByCondition(UserReportConditionBuilder query);


    /**
     * 供应商司机列表查询
     *
     * @param condition SupplierDriverCustomConditionBuilder
     * @return List<SupplierDriverPO>
     */
    List<SupplierDriverPO> selectSupplierDriverByCondition(SupplierDriverCustomConditionBuilder condition);
}
