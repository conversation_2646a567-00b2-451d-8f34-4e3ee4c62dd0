package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 薪资考勤项目映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_attendance_item_config")
public class HrmsSalaryAttendanceItemConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 薪资项ID
     */
    private Long itemConfigId;

    /**
     * 薪资项编码
     */
    private String itemConfigNo;

    /**
     * 考勤国家
     */
    private String country;

    /**
     * 考勤字段(假期全称&计算项)
     */
    private String value;


}
