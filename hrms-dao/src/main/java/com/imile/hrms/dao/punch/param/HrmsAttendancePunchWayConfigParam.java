package com.imile.hrms.dao.punch.param;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 考勤打卡规则方式DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendancePunchWayConfigParam implements Serializable {

    /**
     * 打卡方式 位置打卡，WIFI打卡等
     * 地点打卡方式  PLACE_PUNCH_WAYS
     * WIFI打卡 WIFI_PUNCH_WAYS
     */
    private String punchWayType;
    

    /**
     * 打卡方式配置  JSON punchWayConfig
     */
    private List<String> punchWayConfigDetail;

}
