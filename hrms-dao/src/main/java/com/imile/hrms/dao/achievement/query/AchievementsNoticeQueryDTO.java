package com.imile.hrms.dao.achievement.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AchievementsNoticeQueryDTO extends ResourceQuery {

    /**
     * 处理状态（0:待处理 1:已处理）
     */
    @NotNull(message = "处理状态不能为空")
    private Integer handleStatus;

    /**
     * 通知类型（01:员工绩效 02:组织绩效）
     */
    private String type;

    private Long eventId;

    private List<String> sceneTypeList;

    private Long receiverUserId;
}
