package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.FreelancerDriverDeliveryPreferenceDao;
import com.imile.hrms.dao.user.mapper.FreelancerDriverDeliveryPreferenceMapper;
import com.imile.hrms.dao.user.model.FreelancerDriverDeliveryPreferenceDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 众包司机派送偏好表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Service
public class FreelancerDriverDeliveryPreferenceDaoImpl extends ServiceImpl<FreelancerDriverDeliveryPreferenceMapper, FreelancerDriverDeliveryPreferenceDO> implements FreelancerDriverDeliveryPreferenceDao {

    @Override
    public FreelancerDriverDeliveryPreferenceDO selectByUserId(Long userId) {
        LambdaQueryWrapper<FreelancerDriverDeliveryPreferenceDO> queryWrapper = Wrappers.lambdaQuery(FreelancerDriverDeliveryPreferenceDO.class);
        queryWrapper.eq(FreelancerDriverDeliveryPreferenceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(FreelancerDriverDeliveryPreferenceDO::getUserId, userId);
        return super.getOne(queryWrapper);
    }

    @Override
    public List<FreelancerDriverDeliveryPreferenceDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FreelancerDriverDeliveryPreferenceDO> queryWrapper = Wrappers.lambdaQuery(FreelancerDriverDeliveryPreferenceDO.class);
        queryWrapper.eq(FreelancerDriverDeliveryPreferenceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(FreelancerDriverDeliveryPreferenceDO::getUserId, userIdList);
        return super.list(queryWrapper);
    }
}
