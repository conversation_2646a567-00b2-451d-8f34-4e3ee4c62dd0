package com.imile.hrms.dao.organization.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.organization.dao.HrmsOrgTemplateDao;
import com.imile.hrms.dao.organization.mapper.HrmsOrgTemplateMapper;
import com.imile.hrms.dao.organization.model.HrmsOrgTemplateDO;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 组织模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-01
 */
@Service
public class HrmsOrgTemplateDaoImpl extends ServiceImpl<HrmsOrgTemplateMapper, HrmsOrgTemplateDO> implements HrmsOrgTemplateDao {

    @Override
    public List<HrmsOrgTemplateDO> selectList() {
        LambdaQueryWrapper<HrmsOrgTemplateDO> query = Wrappers.lambdaQuery(HrmsOrgTemplateDO.class);
        query.eq(HrmsOrgTemplateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        query.orderByDesc(HrmsOrgTemplateDO::getLastUpdDate);
        return super.list(query);
    }

    @Override
    public List<HrmsOrgTemplateDO> selectByTemplateName(String templateName) {
        if (Strings.isBlank(templateName)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsOrgTemplateDO> query = Wrappers.lambdaQuery(HrmsOrgTemplateDO.class);
        query.eq(HrmsOrgTemplateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        query.eq(HrmsOrgTemplateDO::getTemplateName, templateName);
        return list(query);
    }
}
