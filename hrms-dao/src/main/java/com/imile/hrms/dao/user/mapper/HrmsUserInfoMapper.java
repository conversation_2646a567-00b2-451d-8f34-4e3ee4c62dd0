package com.imile.hrms.dao.user.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.organization.dto.EmployeeCountDTO;
import com.imile.hrms.dao.organization.query.DeptUserQuery;
import com.imile.hrms.dao.organization.query.EmployeeCountQuery;
import com.imile.hrms.dao.salary.query.SalaryUserSchemeInfoQuery;
import com.imile.hrms.dao.salary.query.SalaryWaitUserSchemeInfoQuery;
import com.imile.hrms.dao.user.dto.AccountDTO;
import com.imile.hrms.dao.user.dto.CrmUserInfoDTO;
import com.imile.hrms.dao.user.dto.ExternalWarehouseInfoDTO;
import com.imile.hrms.dao.user.dto.RegisterUserInfoDTO;
import com.imile.hrms.dao.user.dto.SalaryUserRoleDTO;
import com.imile.hrms.dao.user.dto.SelectUserDTO;
import com.imile.hrms.dao.user.dto.UserApprovalInfoDTO;
import com.imile.hrms.dao.user.dto.UserInfoInformationDTO;
import com.imile.hrms.dao.user.dto.UserInfoListDTO;
import com.imile.hrms.dao.user.dto.UserInformationDTO;
import com.imile.hrms.dao.user.dto.UserListInfoDTO;
import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import com.imile.hrms.dao.user.model.NeedUpdateTaxIdUserDO;
import com.imile.hrms.dao.user.query.AccountQuery;
import com.imile.hrms.dao.user.query.CrmUserQuery;
import com.imile.hrms.dao.user.query.ExternalWarehouseInfoQuery;
import com.imile.hrms.dao.user.query.SalaryRoleUserQuery;
import com.imile.hrms.dao.user.query.SelectUserQuery;
import com.imile.hrms.dao.user.query.TotalEmployeeCountQuery;
import com.imile.hrms.dao.user.query.UserApprovalInfoQuery;
import com.imile.hrms.dao.user.query.UserAssociateConditionBuilder;
import com.imile.hrms.dao.user.query.UserDaoQuery;
import com.imile.hrms.dao.user.query.UserQuery;
import com.imile.hrms.dao.user.query.UserVendorQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 用户表 系统-员工 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Mapper
@Repository
public interface HrmsUserInfoMapper extends HrmsBaseMapper<HrmsUserInfoDO> {
    /**
     * 查询入职状态列表
     *
     * @param query
     * @return
     */
    List<RegisterUserInfoDTO> registerList(UserQuery query);

    /**
     * 获取最大工号
     *
     * @return
     */
    Long getMaxWorkNo();

    /**
     * 查询员工表
     *
     * @param query
     * @return
     */
    List<UserListInfoDTO> userList(UserQuery query);

    /**
     * 分页下拉接口
     *
     * @return
     */
    List<SelectUserDTO> selectUserList(SelectUserQuery userDaoQuery);


    /**
     * 账号查询
     *
     * @param query
     * @return
     */
    List<AccountDTO> listAccount(AccountQuery query);

    /**
     * 查询组织下的员工人数
     *
     * @param query
     * @return
     */
    List<EmployeeCountDTO> employeeCountDTOByIds(EmployeeCountQuery query);

    /**
     * 获取员工总数
     *
     * @param totalEmployeeCountQuery 查询条件
     * @return
     */
    Integer getTotalEmployeeCount(TotalEmployeeCountQuery totalEmployeeCountQuery);


    /**
     * 根据用户id获取用户的部门等属性
     *
     * @param userId
     * @return
     */
    UserInformationDTO getUserInformationDTO(Long userId);

    /**
     * 根据用户id获取用户的部门等属性
     *
     * @param userId
     * @return
     */
    UserInfoInformationDTO getUserInfoInformation(Long userId);

    /**
     * 获取用户上级
     *
     * @param userCode
     * @return
     */
    List<CrmUserInfoDTO> listLeader(String userCode);

    /**
     * 获取员工
     *
     * @param leaderIds
     * @param userCode
     * @return
     */
    List<CrmUserInfoDTO> listUsers(@Param("leaderIds") List<Long> leaderIds, @Param("userCode") String userCode);

    /**
     * 获取员工
     *
     * @param userDaoQuery
     * @return
     */
    List<CrmUserInfoDTO> queryUsersByUserCodeAndLeader(UserDaoQuery userDaoQuery);

    /**
     * 获取员工
     *
     * @param userDaoQuery
     * @return
     */
    List<CrmUserInfoDTO> queryAttendanceUsersByUserCodeAndLeader(UserDaoQuery userDaoQuery);


    /**
     * 剔除部门下某些员工
     *
     * @param totalEmployeeCountQuery
     * @return
     */
    Collection<Long> selectSomeUserFromDept(TotalEmployeeCountQuery totalEmployeeCountQuery);

    /**
     * 更新供应商相关字段
     */
    void updateVendorById(UserVendorQuery userVendorQuery);

    /**
     * CRM获取员工信息(模糊查询)
     */
    List<CrmUserInfoDTO> selectCrmUserInfo(CrmUserQuery query);

    /**
     * 目前被授权对象限定（正式+挂靠）
     * 1. 剔除 is-driver = 0  （司机）； is-warehouse = 0（仓内员工）
     *
     * @return
     */
    List<CrmUserInfoDTO> selectCrmUserToPermission();

    /**
     * TMS修改供应商信息(司机管理字段)，同步修改HR该供应商下的所有司机(lead_id同步)
     */
    void updateByVendorCode(@Param("vendorCode") String vendorCode, @Param("leaderId") Long leaderId, @Param("leaderName") String leaderName);

    /**
     * 仓内外包人员信息查询
     */
    List<ExternalWarehouseInfoDTO> selectExternalWarehouse(ExternalWarehouseInfoQuery query);

    /**
     * 仓内外包人员信息查询(在职，离职，冻结)
     */
    List<ExternalWarehouseInfoDTO> selectExternalWarehouseByUserInfo(ExternalWarehouseInfoQuery query);


    /**
     * 仓内外包员工审批页面分页查询
     */
    List<UserApprovalInfoDTO> selectApprovalInfo(UserApprovalInfoQuery query);

    /**
     * 仓内外包员工信息导出(审核中界面/已驳回界面/在职界面/冻结界面/离职界面  这5个界面都通过这个方法导出)
     */
    List<UserListInfoDTO> export(ExternalWarehouseInfoQuery query);

    /**
     * 仓内外包员工信息导出(在职界面/冻结界面/离职界面)
     */
    List<UserListInfoDTO> exportNotApprovalPage(ExternalWarehouseInfoQuery query);

    /**
     * * 仓内外包员工信息导出(审核中界面/已驳回界面)
     */
    List<UserListInfoDTO> exportApprovalPage(ExternalWarehouseInfoQuery query);

    /**
     * 获取在职员工
     */
    List<HrmsUserInfoDO> getOnJobUser(DeptUserQuery query);

    /**
     * 联想查询
     *
     * @param condition UserAssociateConditionBuilder
     * @return List<HrmsUserInfoDO>
     */
    List<HrmsUserInfoDO> selectByAssociateCondition(UserAssociateConditionBuilder condition);

    List<HrmsUserInfoDO> selectNoticeUserSchemeList(SalaryUserSchemeInfoQuery query);

    List<HrmsUserInfoDO> selectDimissionUserSchemeList(SalaryUserSchemeInfoQuery query);

    /**
     * 记薪国已配置记薪方案的在职员工
     *
     * @param query
     * @return
     */
    List<HrmsUserInfoDO> selectOnJobUserSchemeList(SalaryUserSchemeInfoQuery query);

    List<SalaryUserRoleDTO> selectSalaryRoleUser(SalaryRoleUserQuery query);


    List<UserListInfoDTO> selectUserInfoList(UserQuery query);

    List<HrmsUserInfoDO> selectUserSchemeExcludeDs(SalaryUserSchemeInfoQuery query);

    List<UserInfoListDTO> queryByDeptId(@Param("deptIdList") List<Long> deptIdList);

    List<NeedUpdateTaxIdUserDO> selectNeedUpdateTaxIdUser();

    List<HrmsUserInfoDO> selectWaitUserSchemeList(SalaryWaitUserSchemeInfoQuery query);

    List<UserListInfoDTO> selectUserByName(@Param("userName") String userName);
}
