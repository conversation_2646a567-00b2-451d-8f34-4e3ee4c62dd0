package com.imile.hrms.dao.organization.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 业务节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_biz_model")
public class HrmsBizModelDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业id
     */
    private Long orgId;

    /**
     * 业务节点名称（中文）
     */
    private String bizModelNameCn;

    /**
     * 业务节点名称（英文）
     */
    private String bizModelNameEn;

    /**
     * 是否用于财务费用结算
     */
    private Integer isCostSettlement;

    /**
     * 描述(中文)
     */
    private String describeCn;

    /**
     * 描述(英文)
     */
    private String describeEn;

    /**
     * 启用状态
     */
    private String status;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;
}
