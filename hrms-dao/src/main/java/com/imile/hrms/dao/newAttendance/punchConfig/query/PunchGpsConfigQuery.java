package com.imile.hrms.dao.newAttendance.punchConfig.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchGpsConfigQuery extends ResourceQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id
     */
    private List<Long> ids;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * 地址名称
     */
    private String addressName;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 国家列表
     */
    private List<String> countryList;
}
