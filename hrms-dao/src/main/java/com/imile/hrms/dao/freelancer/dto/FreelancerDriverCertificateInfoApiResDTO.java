package com.imile.hrms.dao.freelancer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 证件信息
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverCertificateInfoApiResDTO {

    /**
     * 证件类型：ID_CARD（身份证）/ DRIVING_LICENSE（驾照）
     */
    private String certificateTypeCode;
    /**
     * 证件号码
     */
    private String certificateCode;

    /**
     * 证件到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date certificateExpireDate;

    /**
     * 领证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date certificateReceiptDate;

    /**
     * 证件路径
     */
    private List<VerificationDetailInfoApiResDTO> certificatePathList;

    /**
     * 处理方法 MUST_HANDLER
     */
    private String handlerMethod;

    /**
     * 重复
     */
    private String duplicate;

}
