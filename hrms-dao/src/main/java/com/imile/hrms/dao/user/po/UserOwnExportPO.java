package com.imile.hrms.dao.user.po;

import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2024/11/7
 */
@Data
public class UserOwnExportPO {

    /**
     * userId
     */
    private Long userId;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 姓名全称
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 是否为司机
     */
    private Integer isDriver;

    /**
     * 是否DTL
     */
    private Integer isDtl;

    /**
     * 部门Id
     */
    private Long deptId;

    /**
     * 核算单元
     */
    private String ocCode;

    /**
     * 核算组织(所属国)
     */
    private String originCountry;

    /**
     * 业务节点
     */
    private Long bizModelId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 汇报上级
     */
    private Long leaderId;

    /**
     * 岗职体系id
     */
    private Long gradeId;

    /**
     * 职级
     */
    private String jobLevel;

    /**
     * 职等
     */
    private String jobGrade;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 是否全球派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 入职日期
     */
    private Date entryDate;

    /**
     * 离职日期日期
     */
    private Date dimissionDate;

    /**
     * 国籍
     */
    private String countryCode;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 出生日期
     */
    private Date birthDay;

    /**
     * 联系电话
     */
    private String phone;
}
