package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserEducationDao;
import com.imile.hrms.dao.user.mapper.UserEducationMapper;
import com.imile.hrms.dao.user.model.UserEducationDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3
 */
@Service
public class UserEducationDaoImpl extends ServiceImpl<UserEducationMapper, UserEducationDO> implements UserEducationDao {
    @Override
    public List<UserEducationDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserEducationDO> queryWrapper = Wrappers.lambdaQuery(UserEducationDO.class);
        queryWrapper.eq(UserEducationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserEducationDO::getUserId, userIdList);
        return super.list(queryWrapper);
    }
}
