package com.imile.hrms.dao.vehicle.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-27
 * @version: 1.0
 */
@Data
public class WarningConfigQuery extends ResourceQuery {
    private static final long serialVersionUID = 4328268281949139870L;

    /**
     * 国家
     */
    private List<String> countryList;


    /**
     * 网点ID
     */
    private List<Long> deptIdList;

    /**
     * 是否是模板0否 1是
     */
    private Integer isTemplate;

    /**
     * 所属的模板配置ID
     */
    private Long templateConfigId;

    /**
     * 查询时间
     */
    private Date queryDate;

    /**
     * 是否过期
     */
    private Integer isLatest;
}
