package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 薪资计算任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_calculate_task")
public class HrmsSalaryCalculateTaskDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 结算时间-年月(202308)
     */
    private Long settlementDate;

    /**
     * 申请国家
     */
    private String applyCountry;

    /**
     * 任务编码
     */
    private String taskNo;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 计薪方案编码
     */
    private String salarySchemeConfigNo;

    /**
     * 计薪方案名称
     */
    private String salarySchemeConfigName;

    /**
     * 计算次数
     */
    private Integer calculateCount;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 关联人数
     */
    private Integer associatedNumber;

    /**
     * 计算人数
     */
    private Integer calculateNumber;

    /**
     * 计算时间
     */
    private Date calculateDate;

    /**
     * 记薪周期
     */
    private String salaryConfigCycle;

    /**
     * 发薪日期
     */
    private String payDate;

    /**
     * 是否计算中：0否/1是
     */
    private Integer calculateStatus;

    /**
     * 计算选择的模板id集合
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String templateIdList;

}
