package com.imile.hrms.dao.salary.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigDO;
import com.imile.hrms.dao.salary.query.SalaryItemConfigQuery;
import com.imile.hrms.dao.salary.query.SalaryItemReflectionQuery;

import java.util.List;

/**
 * <p>
 * 薪资项配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
public interface HrmsSalaryItemConfigDao extends IService<HrmsSalaryItemConfigDO> {

    List<HrmsSalaryItemConfigDO> selectItemConfigList(SalaryItemConfigQuery query);

    List<HrmsSalaryItemConfigDO> selectItemConfigByReflectionList(SalaryItemReflectionQuery query);


    List<HrmsSalaryItemConfigDO> selectItemConfigByIdList(List<Long> idList);

    List<HrmsSalaryItemConfigDO> selectItemConfigByNoList(List<String> itemConfigNoList);

    List<HrmsSalaryItemConfigDO> selectAllItemConfigList();
}
