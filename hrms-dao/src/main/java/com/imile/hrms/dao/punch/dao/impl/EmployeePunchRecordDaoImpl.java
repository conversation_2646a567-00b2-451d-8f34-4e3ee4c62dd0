package com.imile.hrms.dao.punch.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.dao.punch.dao.EmployeePunchRecordDao;
import com.imile.hrms.dao.punch.dto.HrmsPunchCardRecordDTO;
import com.imile.hrms.dao.punch.mapper.EmployeePunchRecordMapper;
import com.imile.hrms.dao.punch.model.EmployeePunchRecordDO;
import com.imile.hrms.dao.punch.query.EmployeePunchCardRecordQuery;
import com.imile.hrms.dao.punch.query.UserPunchCardRecordQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
public class EmployeePunchRecordDaoImpl extends ServiceImpl<EmployeePunchRecordMapper, EmployeePunchRecordDO> implements EmployeePunchRecordDao {

    @Resource
    private EmployeePunchRecordMapper employeePunchRecordMapper;

    @Override
    public List<EmployeePunchRecordDO> getPunchList(Long dayId, String userCode, String punchType) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        if (dayId != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getDayId, dayId);
        }
        if (userCode != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getUserCode, userCode);
        }
        if (punchType != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getPunchType, punchType);
        }
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery query) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        if (query.getStartTime() != null) {
            queryWrapper.ge(EmployeePunchRecordDO::getPunchTime, query.getStartTime());
        }
        if (query.getEndTime() != null) {
            queryWrapper.lt(EmployeePunchRecordDO::getPunchTime, query.getEndTime());
        }
        if (StringUtils.isNotBlank(query.getUserCode())) {
            queryWrapper.eq(EmployeePunchRecordDO::getUserCode, query.getUserCode());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(EmployeePunchRecordDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getUserCodes())) {
            queryWrapper.in(EmployeePunchRecordDO::getUserCode, query.getUserCodes());
        }
        if (StringUtils.isNotBlank(query.getDayId())) {
            queryWrapper.eq(EmployeePunchRecordDO::getDayId, query.getDayId());
        }
        if (CollectionUtils.isNotEmpty(query.getDayIds())) {
            queryWrapper.in(EmployeePunchRecordDO::getDayId, query.getDayIds());
        }
        if (StringUtils.isNotBlank(query.getSourceType())) {
            queryWrapper.eq(EmployeePunchRecordDO::getSourceType, query.getSourceType());
        }
        if (query.getFormId() != null) {
            queryWrapper.eq(EmployeePunchRecordDO::getFormId, query.getFormId());
        }

        if (CollUtil.isNotEmpty(query.getGpsOrWifiConfigIds())) {
            // 拼接或的条件，gps配置id或wifi配置id在gpsOrWifiConfigIds里面即可
            queryWrapper.and(wrapper -> {
                wrapper.in(EmployeePunchRecordDO::getGpsConfigId, query.getGpsOrWifiConfigIds());
                wrapper.or();
                wrapper.in(EmployeePunchRecordDO::getWifiConfigId, query.getGpsOrWifiConfigIds());
            });
        }
        if (query.getCreateStartTime() != null) {
            queryWrapper.ge(EmployeePunchRecordDO::getCreateDate, query.getCreateStartTime());
        }
        if (query.getCreateEndTime() != null) {
            queryWrapper.lt(EmployeePunchRecordDO::getCreateDate, query.getCreateEndTime());
        }

        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(EmployeePunchRecordDO::getCreateDate);

        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeePunchRecordDO> selectListByCOndition(Long dayId, String country, List<String> userCodes) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(dayId != null, EmployeePunchRecordDO::getDayId, dayId);
        queryWrapper.in(CollectionUtils.isNotEmpty(userCodes), EmployeePunchRecordDO::getUserCode, userCodes);
        queryWrapper.eq(country != null, EmployeePunchRecordDO::getCountry, country);
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeePunchRecordDO> getPunchListByMobileConfigId(List<Long> mobileConfigId) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(mobileConfigId), EmployeePunchRecordDO::getMobileConfigId, mobileConfigId);
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(EmployeePunchRecordDO::getPunchTime);
        queryWrapper.last("limit 1");
        return this.list(queryWrapper);
    }

    /**
     * 根据条件获取打卡记录
     */
    @Override
    public List<EmployeePunchRecordDO> selectListByCondition(List<String> countryList, Date startTime, Date endTime) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(countryList), EmployeePunchRecordDO::getCountry, countryList);
        queryWrapper.ge(ObjectUtil.isNotNull(startTime), EmployeePunchRecordDO::getCreateDate, startTime);
        queryWrapper.le(ObjectUtil.isNotNull(endTime), EmployeePunchRecordDO::getCreateDate, endTime);
        queryWrapper.isNotNull(EmployeePunchRecordDO::getCountry);
        return this.list(queryWrapper);
    }

    @Override
    @DS(HrmsProperties.Database.HRMS_RO_URL)
    public List<HrmsPunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query) {
        return employeePunchRecordMapper.listRecord(query);
    }

    @Override
//    @DS(HrmsProperties.Database.HRMS_RO_URL)
    public List<HrmsPunchCardRecordDTO> listRecordNewAttendance(UserPunchCardRecordQuery query) {
        return employeePunchRecordMapper.listRecordNewAttendance(query);
    }

    @Override
    public List<EmployeePunchRecordDO> selectForRiskAnalysis(Long startDayId, Long endDayId, List<String> userCodes) {
        LambdaQueryWrapper<EmployeePunchRecordDO> queryWrapper = new LambdaQueryWrapper<>();

        // 时间范围条件
        if (ObjectUtil.isNotNull(startDayId)) {
            queryWrapper.ge(EmployeePunchRecordDO::getDayId, startDayId.toString());
        }
        if (ObjectUtil.isNotNull(endDayId)) {
            queryWrapper.le(EmployeePunchRecordDO::getDayId, endDayId.toString());
        }

        // 用户条件
        if (CollUtil.isNotEmpty(userCodes)) {
            queryWrapper.in(EmployeePunchRecordDO::getUserCode, userCodes);
        }

        // 基础条件：只查询有效的、有经纬度的、有打卡时间的记录
        queryWrapper.eq(EmployeePunchRecordDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .isNotNull(EmployeePunchRecordDO::getLongitude)
                .isNotNull(EmployeePunchRecordDO::getLatitude)
                .isNotNull(EmployeePunchRecordDO::getPunchTime)
                .orderByAsc(EmployeePunchRecordDO::getUserCode)
                .orderByAsc(EmployeePunchRecordDO::getPunchTime);

        return this.list(queryWrapper);
    }
}
