package com.imile.hrms.dao.probation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.probation.model.UserProbationCycleSummaryDO;

import java.util.List;

/**
 * <p>
 * 员工试用期目标综合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface UserProbationCycleSummaryDao extends IService<UserProbationCycleSummaryDO> {

    /**
     * 通过试用期ID统计周期总计次数
     *
     * @param userProbationId 试用期ID
     * @return 次数
     */
    int countCycleNumberByProbationId(Long userProbationId);

    /**
     * 通过试用期ID查询
     *
     * @param probationId 试用期ID
     * @return List<UserProbationCycleSummaryDO>
     */
    List<UserProbationCycleSummaryDO> selectByProbationId(Long probationId);

    /**
     * 通过试用期ID批量查询
     *
     * @param probationIdList probationIdList
     * @return List<UserProbationCycleSummaryDO>
     */
    List<UserProbationCycleSummaryDO> selectByProbationIdList(List<Long> probationIdList);
}
