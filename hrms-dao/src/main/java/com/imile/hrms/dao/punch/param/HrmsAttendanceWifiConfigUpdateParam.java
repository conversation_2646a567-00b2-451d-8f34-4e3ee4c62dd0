package com.imile.hrms.dao.punch.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * wifi配置传入对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
public class HrmsAttendanceWifiConfigUpdateParam extends HrmsAttendanceWifiConfigAddParam {

    /**
     * 主键id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

}
