package com.imile.hrms.dao.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserTenureInfoDTO {

    /**
     * 实际入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmEntryDate;
}
