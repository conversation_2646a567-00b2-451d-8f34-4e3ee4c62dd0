package com.imile.hrms.dao.salary.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalarySettlementApprovalUserInfoDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalarySettlementApprovalUserInfoMapper;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalUserInfoDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementApprovalUserInfoQuery;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 薪资结算数据申请员工信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Service
public class HrmsSalarySettlementApprovalUserInfoDaoImpl extends ServiceImpl<HrmsSalarySettlementApprovalUserInfoMapper, HrmsSalarySettlementApprovalUserInfoDO> implements HrmsSalarySettlementApprovalUserInfoDao {

    @Override
    public List<HrmsSalarySettlementApprovalUserInfoDO> listByQuery(HrmsSalarySettlementApprovalUserInfoQuery query) {
        LambdaQueryWrapper<HrmsSalarySettlementApprovalUserInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalarySettlementApprovalUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());

        if (CollectionUtils.isNotEmpty(query.getSalarySettlementUserInfoIdList())) {
            queryWrapper.in(HrmsSalarySettlementApprovalUserInfoDO::getSalarySettlementUserInfoId, query.getSalarySettlementUserInfoIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getSalarySettlementApprovalFormIdList())) {
            queryWrapper.in(HrmsSalarySettlementApprovalUserInfoDO::getSalarySettlementApprovalFormId, query.getSalarySettlementApprovalFormIdList());
        }
        return list(queryWrapper);
    }
}
