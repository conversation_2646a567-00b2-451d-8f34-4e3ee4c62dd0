package com.imile.hrms.dao.user.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.user.model.HrmsUserDimissionRecordDO;
import com.imile.hrms.dao.user.query.DimissionQuery;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户离职记录表 系统-员工 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface HrmsUserDimissionRecordDao extends IService<HrmsUserDimissionRecordDO> {
    /**
     * 根据用户ID获取用户离职记录
     *
     * @param userID
     * @return
     */
    HrmsUserDimissionRecordDO getByUserId(Long userID);

    /**
     * 根据用户ID获取用户离职记录  tms操作，无需登录，即不用判断公司
     *
     * @param userId
     * @return
     */
    HrmsUserDimissionRecordDO getByUserIdNoAuth(Long userId);

    /**
     * 获取离职记录
     *
     * @param query
     * @return
     */
    HrmsUserDimissionRecordDO getDimissionRecord(DimissionQuery query);

    /**
     * 通过状态获取离职记录
     */
    HrmsUserDimissionRecordDO getDimissionRecordByStatus(Long userId, List<String> statusList);


    /**
     * 查询用户离职记录
     *
     * @param userIds
     * @return
     */
    List<HrmsUserDimissionRecordDO> listByUserIds(List<Long> userIds);

    /**
     * 查询某天离职员工
     */
    List<HrmsUserDimissionRecordDO> listByDate(Date startDate, Date endDate, List<Long> userIdList);

}
