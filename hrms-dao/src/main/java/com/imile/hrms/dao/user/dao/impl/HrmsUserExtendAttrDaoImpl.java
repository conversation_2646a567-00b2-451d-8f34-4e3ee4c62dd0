package com.imile.hrms.dao.user.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.HrmsUserExtendAttrDao;
import com.imile.hrms.dao.user.mapper.HrmsUserExtendAttrMapper;
import com.imile.hrms.dao.user.model.HrmsUserExtendAttrDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户信息扩展属性表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Service
public class HrmsUserExtendAttrDaoImpl extends ServiceImpl<HrmsUserExtendAttrMapper, HrmsUserExtendAttrDO> implements HrmsUserExtendAttrDao {

    @Override
    public List<HrmsUserExtendAttrDO> selectByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserExtendAttrDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsUserExtendAttrDO::getUserId, userId);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsUserExtendAttrDO> listByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserExtendAttrDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserExtendAttrDO::getUserId, userIds);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsUserExtendAttrDO> listByAttrKey(List<String> attrKeyList) {
        if (CollectionUtils.isEmpty(attrKeyList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserExtendAttrDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserExtendAttrDO::getAttrKey, attrKeyList);

        return list(queryWrapper);
    }

    @Override
    public List<HrmsUserExtendAttrDO> listByAttrKeyAndUserId(List<String> attrKeyList, Long userId) {
        if (CollectionUtils.isEmpty(attrKeyList) || userId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserExtendAttrDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserExtendAttrDO::getAttrKey, attrKeyList);

        if (userId != null) {
            queryWrapper.eq(HrmsUserExtendAttrDO::getUserId, userId);
        }

        return list(queryWrapper);
    }

    @Override
    public List<HrmsUserExtendAttrDO> listByAttrKeyAndUserIds(List<String> attrKeyList, List<Long> userIds) {
        if (CollectionUtils.isEmpty(attrKeyList) || CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsUserExtendAttrDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserExtendAttrDO::getAttrKey, attrKeyList);
        queryWrapper.in(HrmsUserExtendAttrDO::getUserId, userIds);
        return list(queryWrapper);
    }
}
