package com.imile.hrms.dao.organization.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 组织模板部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_org_template_dept")
public class HrmsOrgTemplateDeptDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 组织模板ID
     */
    private Long templateId;

    /**
     * 部门名称（中文）
     */
    private String deptNameCn;

    /**
     * 部门名称（英文）
     */
    private String deptNameEn;

    /**
     * 上级部门ID
     */
    private Long parentId;

    /**
     * 组织定位
     */
    private String deptPosition;

    /**
     * 组织职责
     */
    private String deptDuty;

    /**
     * 组织类型（1:实体组织 2:项目组织 3:职能模块 4:网点）
     */
    private Integer deptOrgType;

    /**
     * 业务领域（1:销售 2:客服 3:运营 4:商务 5:财务 6:人事 7:产研 8:采购 9:加盟 10:综合管理 11:综合管理-区域）
     */
    private String bizArea;


}
