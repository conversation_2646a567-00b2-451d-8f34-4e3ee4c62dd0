package com.imile.hrms.dao.blacklist.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.blacklist.model.HrmsBlacklistPhoneDO;

import java.util.List;

/**
 * <p>
 * 黑名单电话号码表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-31
 */
public interface HrmsBlacklistPhoneDao extends IService<HrmsBlacklistPhoneDO> {

    /**
     * 根据黑名单ID查询
     *
     * @param blacklistId 黑名单ID
     * @return List<HrmsBlacklistPhoneDO>
     */
    List<HrmsBlacklistPhoneDO> selectByBlacklistId(Long blacklistId);

    /**
     * 根据电话号码查询
     *
     * @param callingPhone 电话号码
     * @return List<HrmsBlacklistPhoneDO>
     */
    List<HrmsBlacklistPhoneDO> selectByCallingPhone(String callingPhone);
}
