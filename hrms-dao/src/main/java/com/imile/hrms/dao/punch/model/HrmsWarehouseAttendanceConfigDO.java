package com.imile.hrms.dao.punch.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 仓内考勤配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_warehouse_attendance_config")
public class HrmsWarehouseAttendanceConfigDO extends BaseDO {


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 考勤规则名称
     */
    private String attendanceConfigName;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间 2099-12-31 23:23:59
     */
    private Date expireTime;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 使用部门
     */
    private String deptIds;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 是否允许上多班次 0不允许, 1允许
     */
    private Integer isMultipleShifts;

    /**
     * 满勤配置,允许实际总工作时长小于班次总时长分钟内
     */
    private Integer minute;

    /**
     * 是否分段计算工时 0计算总工时 1分段计算工时
     */
    private Integer isSegmentedCalculation;
}
