package com.imile.hrms.dao.punch.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/10
 */
@Data
public class EffectiveAttendanceHelpDTO {
    /**
     * 日期dauId
     */
    private Long dayId;
    /**
     * 日期类型
     */
    private String dayType;
    /**
     * 配置上班时间
     */
    private Date configPunchInTime;
    /**
     * 配置下班时间
     */
    private Date configPunchOutTime;
    /**
     * 最早上班时间
     */
    private Date earListConfigPunchInTime;
    /**
     * 最晚下班时间
     */
    private Date latestConfigPunchOutTime;
    /**
     * 工作时长
     */
    private BigDecimal punchTimeInterval;
    /**
     * 是否没有配置打卡
     */
    private Boolean isNoPunchConfig;
    /**
     * 是否设置班次
     */
    private Boolean isSetClass;

    public Boolean getNoPunchConfig() {
        return isNoPunchConfig == null ? Boolean.FALSE : isNoPunchConfig;
    }


}
