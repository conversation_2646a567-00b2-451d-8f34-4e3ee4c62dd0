package com.imile.hrms.dao.user.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-18
 */
@Data
@Builder
public class HrmsUserRelevanceInfoSelectQuery {

    /**
     * 部门id列表
     */
    private List<Long> deptIds;

    /**
     * 网点id列表
     */
    private List<Long> ocIds;

    /**
     * 岗位id列表
     */
    private List<Long> postIds;

    /**
     * 业务节点id列表
     */
    private List<Long> bizModelIds;

    /**
     * 项目id列表
     */
    private List<Long> projectIds;

}
