package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalarySubmitTemplateItemConfigDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalarySubmitTemplateItemConfigMapper;
import com.imile.hrms.dao.salary.model.HrmsSalarySubmitTemplateItemConfigDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySubmitTemplateItemConfigQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 薪资数据提报科目范围设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Service
public class HrmsSalarySubmitTemplateItemConfigDaoImpl extends ServiceImpl<HrmsSalarySubmitTemplateItemConfigMapper, HrmsSalarySubmitTemplateItemConfigDO> implements HrmsSalarySubmitTemplateItemConfigDao {

    @Override
    public List<HrmsSalarySubmitTemplateItemConfigDO> listByQuery(HrmsSalarySubmitTemplateItemConfigQuery query) {
        LambdaQueryWrapper<HrmsSalarySubmitTemplateItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsSalarySubmitTemplateItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (query.getSubmitTemplateConfigId() != null) {
            queryWrapper.eq(HrmsSalarySubmitTemplateItemConfigDO::getSubmitTemplateConfigId, query.getSubmitTemplateConfigId());
        }
        if (CollectionUtils.isNotEmpty(query.getSubmitTemplateConfigIdList())) {
            queryWrapper.in(HrmsSalarySubmitTemplateItemConfigDO::getSubmitTemplateConfigId, query.getSubmitTemplateConfigIdList());
        }
        return list(queryWrapper);
    }
}
