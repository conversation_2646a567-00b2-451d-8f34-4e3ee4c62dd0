package com.imile.hrms.dao.attendance.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/14
 */
@Data
public class AttendanceConfigUpdateParam extends AttendanceConfigAddParam {
    private static final long serialVersionUID = 7322093493736415865L;
    /**
     * 考勤配置ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 修改日历考勤日集合
     */
    private List<Long> updateDayIds;
}
