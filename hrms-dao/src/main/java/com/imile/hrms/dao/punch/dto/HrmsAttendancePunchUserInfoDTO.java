package com.imile.hrms.dao.punch.dto;

import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 打卡规则绑定人员DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
public class HrmsAttendancePunchUserInfoDTO {

    private Long id;

    private String userCode;

    private String userName;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

    /**
     * 核算国
     */
    private String originCountry;

    /**
     * 部门主键
     */
    private Long deptId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 地理国
     */
    private String country;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 绑定时间
     */
    private Date createDate;
}
