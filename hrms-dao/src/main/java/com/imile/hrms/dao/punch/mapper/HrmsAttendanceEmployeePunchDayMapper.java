package com.imile.hrms.dao.punch.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.punch.dto.PunchDayDetailDTO;
import com.imile.hrms.dao.punch.dto.PunchResultDTO;
import com.imile.hrms.dao.punch.model.HrmsAttendanceEmployeePunchDayDO;
import com.imile.hrms.dao.punch.query.AttendancePunchStatisticalQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 员工每日打卡记录表 只记录最新的记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Mapper
@Repository
public interface HrmsAttendanceEmployeePunchDayMapper extends HrmsBaseMapper<HrmsAttendanceEmployeePunchDayDO> {

    /**
     * 查询打卡记录表
     * @param recordListQuery
     * @return
     */
   // List<HrmsAttendanceEmployeePunchListDTO> listPunchRecordList(AttendancePunchRecordListQuery recordListQuery);

    /**
     * 获取一个月的打卡记录
     * @param statisticalQuery
     * @return
     */
    List<PunchDayDetailDTO> statisticalList(AttendancePunchStatisticalQuery statisticalQuery);

    /**
     * 获取一个中早退和迟到的次数
     * @param statisticalQuery
     * @return
     */
    PunchResultDTO totalMonthTimeResult(AttendancePunchStatisticalQuery statisticalQuery);
}
