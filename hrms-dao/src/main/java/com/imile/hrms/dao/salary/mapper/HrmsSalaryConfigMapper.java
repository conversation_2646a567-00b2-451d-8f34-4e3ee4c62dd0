package com.imile.hrms.dao.salary.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.salary.dto.SalaryConfigDetailDTO;
import com.imile.hrms.dao.salary.model.HrmsSalaryConfigDO;

import com.imile.hrms.dao.salary.query.ConfigDetailQuery;
import org.apache.ibatis.annotations.Mapper;


/**
 * <p>
 * 计薪方案配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Mapper
public interface HrmsSalaryConfigMapper extends HrmsBaseMapper<HrmsSalaryConfigDO> {

    /**
     * 获取详情
     *
     * @param configDetailQuery
     * @return
     */
    SalaryConfigDetailDTO getDetailDTO(ConfigDetailQuery configDetailQuery);



}
