package com.imile.hrms.dao.primary.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.primary.entity.CountryCertificateConfigDO;
import com.imile.hrms.dao.primary.entity.condition.CountryCertificateConfigFilter;

import java.util.List;

/**
 * <p>
 * 国家证件配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public interface CountryCertificateConfigDao extends IService<CountryCertificateConfigDO> {

    /**
     * 根据过滤器查询
     *
     * @param filter CountryCertificateConfigFilter
     * @return List<CountryCertificateConfigDO>
     */
    List<CountryCertificateConfigDO> selectByFilter(CountryCertificateConfigFilter filter);

    /**
     * 根据证件类型分组
     *
     * @param country 国家
     * @return List<CountryCertificateConfigDO>
     */
    List<CountryCertificateConfigDO> selectGroupByCertificateType(String country);
}
