package com.imile.hrms.dao.salary.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.salary.model.HrmsSalarySchemeConfigDO;
import com.imile.hrms.dao.salary.query.SalarySchemeConfigQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 计薪方案配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@Mapper
public interface HrmsSalarySchemeConfigMapper extends HrmsBaseMapper<HrmsSalarySchemeConfigDO> {

    List<HrmsSalarySchemeConfigDO> selectNoticeSchemeConfigList(SalarySchemeConfigQuery query);

}
