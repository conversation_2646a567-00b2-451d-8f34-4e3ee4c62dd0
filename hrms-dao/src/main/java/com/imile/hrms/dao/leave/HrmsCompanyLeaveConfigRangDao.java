package com.imile.hrms.dao.leave;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigRangDO;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyLeaveConfigRangDao
 * {@code @since:} 2024-04-09 11:10
 * {@code @description:}
 */
public interface HrmsCompanyLeaveConfigRangDao extends IService<HrmsCompanyLeaveConfigRangDO> {
    /**
     * 获取假期配置范围
     * @param allCompanyLeaveConfigIdList 假期配置id
     * @return 假期配置范围
     */
    List<HrmsCompanyLeaveConfigRangDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList);

    /**
     * 获取假期配置范围
     *
     * @param userCodeList 用户编码
     * @return 假期配置范围
     */
    List<HrmsCompanyLeaveConfigRangDO> selectRangByUserCode(List<String> userCodeList);

    void handlerCompanyLeaveConfigRang(List<HrmsCompanyLeaveConfigRangDO> addLeaveRang, List<HrmsCompanyLeaveConfigRangDO> updateLeaveRang);
}
