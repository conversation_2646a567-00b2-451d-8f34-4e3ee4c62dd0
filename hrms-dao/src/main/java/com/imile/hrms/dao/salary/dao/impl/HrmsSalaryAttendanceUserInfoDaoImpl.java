package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryAttendanceUserInfoDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryAttendanceUserInfoMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryAttendanceUserInfoDO;
import com.imile.hrms.dao.salary.query.SalaryAttendanceUserQuery;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 薪资用户考勤数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Service
public class HrmsSalaryAttendanceUserInfoDaoImpl extends ServiceImpl<HrmsSalaryAttendanceUserInfoMapper, HrmsSalaryAttendanceUserInfoDO> implements HrmsSalaryAttendanceUserInfoDao {

    @Override
    public List<HrmsSalaryAttendanceUserInfoDO> selectUserAttendanceList(SalaryAttendanceUserQuery query) {
        LambdaQueryWrapper<HrmsSalaryAttendanceUserInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (query.getSalaryAttendanceSchemeId() != null) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getSalaryAttendanceSchemeId, query.getSalaryAttendanceSchemeId());
        }
        if (query.getSettlementDate() != null) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getSettlementDate, query.getSettlementDate());
        }
        if (StringUtils.isNotBlank(query.getApplyCountry())) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getApplyCountry, query.getApplyCountry());
        }
        if (StringUtils.isNotBlank(query.getSalaryStatus())) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getSalaryStatus, query.getSalaryStatus());
        }
        if (StringUtils.isNotBlank(query.getSalarySchemeConfigNo())) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getSalarySchemeConfigNo, query.getSalarySchemeConfigNo());
        }
        if (StringUtils.isNotBlank(query.getUserCodeOrName())) {
            queryWrapper.and(wrapper -> wrapper.like(HrmsSalaryAttendanceUserInfoDO::getUserCode, query.getUserCodeOrName())
                    .or().like(HrmsSalaryAttendanceUserInfoDO::getUserName, query.getUserCodeOrName()));
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIdList())) {
            queryWrapper.in(HrmsSalaryAttendanceUserInfoDO::getDeptId, query.getDeptIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getPostIdList())) {
            queryWrapper.in(HrmsSalaryAttendanceUserInfoDO::getPostId, query.getPostIdList());
        }
        if (StringUtils.isNotBlank(query.getWorkStatus())) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getWorkStatus, query.getWorkStatus());
        }
        if (CollectionUtils.isNotEmpty(query.getEmployeeTypeList())) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getEmployeeType, query.getEmployeeTypeList());
        }
        if (query.getIsDriver() != null) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getIsDriver, query.getIsDriver());
        }
        if (query.getSalarySchemeConfigId() != null) {
            queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getSalarySchemeConfigId, query.getSalarySchemeConfigId());
        }
        if (CollectionUtils.isNotEmpty(query.getSalarySchemeConfigIdList())) {
            queryWrapper.in(HrmsSalaryAttendanceUserInfoDO::getSalarySchemeConfigId, query.getSalarySchemeConfigIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getSalarySchemeConfigNoList())) {
            queryWrapper.in(HrmsSalaryAttendanceUserInfoDO::getSalarySchemeConfigNo, query.getSalarySchemeConfigNoList());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            queryWrapper.in(HrmsSalaryAttendanceUserInfoDO::getUserId, query.getUserIdList());
        }
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalaryAttendanceUserInfoDO> selectUserAttendanceByAttendanceSchemeIdList(List<Long> salaryAttendanceSchemeIdList) {
        if (CollectionUtils.isEmpty(salaryAttendanceSchemeIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalaryAttendanceUserInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryAttendanceUserInfoDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsSalaryAttendanceUserInfoDO::getSalaryAttendanceSchemeId, salaryAttendanceSchemeIdList);
        return list(queryWrapper);
    }
}
