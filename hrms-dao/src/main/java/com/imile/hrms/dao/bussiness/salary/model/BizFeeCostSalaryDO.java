package com.imile.hrms.dao.bussiness.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * biz_fee_cost_salary
 *
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("biz_fee_cost_salary")
public class BizFeeCostSalaryDO extends BaseDO {
    private Long id;

    private Long orgId;

    /**
     * 唯一码
     */
    private String uniqueCode;

    /**
     * 员工编码（TMS员工、司机）
     */
    private String empCode;

    /**
     * 司机名称
     */
    private String empName;

    /**
     * 成本归属部门
     */
    private String costIncidenceDept;

    /**
     * 国家
     */
    private String businessCountry;

    /**
     * 网点编码
     */
    private String ocName;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 部门
     */
    private Long deptId;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 月份
     */
    private String month;

    /**
     * 司机队长
     */
    private String dtlUserCode;

    /**
     * 队长
     */
    private String dtlUserName;

    /**
     * 金额
     */
    private BigDecimal amount;



    /**
     * 币种
     */
    private String currency;

    /**
     * 业务类型 EMP：员工薪资金额 ； OC :网点金额
     */

    private String bizType;

    /**
     * 员工类型 operational ：运营 driver : 司机
     */
    private String empType;


    private static final long serialVersionUID = 1L;
}
