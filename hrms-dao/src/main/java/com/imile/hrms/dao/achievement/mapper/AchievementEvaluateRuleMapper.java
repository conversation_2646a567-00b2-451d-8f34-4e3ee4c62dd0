package com.imile.hrms.dao.achievement.mapper;

import com.imile.hrms.dao.achievement.model.AchievementEvaluateGradeRelationDO;
import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.achievement.model.AchievementEvaluateRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 绩效评价规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Mapper
public interface AchievementEvaluateRuleMapper extends HrmsBaseMapper<AchievementEvaluateRuleDO> {

    /**
     * 查询明细规则根据活动id
     * @param eventId
     * @return
     */
    List<AchievementEvaluateGradeRelationDO> listByEventId(Long eventId);
}
