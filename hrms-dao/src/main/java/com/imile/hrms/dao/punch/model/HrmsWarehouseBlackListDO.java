package com.imile.hrms.dao.punch.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;

import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓内考勤黑名单记录表
 * <AUTHOR>
 * @since 2025/2/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "hrms_warehouse_black_list")
public class HrmsWarehouseBlackListDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    @TableField(value = "user_code")
    private String userCode;

    /**
     * 网点
     */
    @TableField(value = "oc_id")
    private Long ocId;

    /**
     * 供应商id
     */
    @TableField(value = "vendor_code")
    private String vendorCode;

    /**
     * 班次id
     */
    @TableField(value = "classes_id")
    private Long classesId;

    /**
     * 仓内日期
     */
    @TableField(value = "warehouse_date")
    private Date warehouseDate;

    /**
     * 环节：1注册、2入仓、3出仓
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 用工类型
     */
    @TableField(value = "employee_type")
    private String employeeType;
}