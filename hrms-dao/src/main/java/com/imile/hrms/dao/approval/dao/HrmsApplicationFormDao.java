package com.imile.hrms.dao.approval.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.approval.model.HrmsApplicationFormDO;
import com.imile.hrms.dao.approval.query.ApplicationFormQuery;
import com.imile.hrms.dao.approval.query.AttendanceApprovalInfoQuery;

import java.util.List;

/**
 * <p>
 * 申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
public interface HrmsApplicationFormDao extends IService<HrmsApplicationFormDO> {

    List<HrmsApplicationFormDO> selectForm(ApplicationFormQuery query);

    List<HrmsApplicationFormDO> selectAttendanceApprovalInfo(AttendanceApprovalInfoQuery query);

}
