package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 绩效活动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("achievements_events")
public class AchievementsEventsDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联组织活动id
     */
    private Long relateAchievementOrgId;

    /**
     * 活动名称
     */
    private String eventName;

    /**
     * 年
     */
    private Integer year;

    /**
     * 考核周期类型
     */
    private String cycleType;

    /**
     * 组织目标人员id集合
     */
    private String targetUserIds;

    /**
     * 员工人员id集合
     */
    private String userIds;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 考核开始时间
     */
    private Date startExamineTime;

    /**
     * 活动适用范围
     */
    private String eventScope;


    /**
     * 状态
     */
    private String status;

    /**
     * 停用/启用
     */
    private String type;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否开启考核(01开启 02未开启)
     */
    private String isExamine;

    /**
     * 开启考核(开始自评) 01开启 02未开启
     */
    private String isEvaluation;

    /**
     * 部门负责人是否可填写组织目标
     */
    private Integer isLeaderEditable;

    /**
     * 绩效评价规则id
     */
    private Long achievementEvaluateRuleId;

}
