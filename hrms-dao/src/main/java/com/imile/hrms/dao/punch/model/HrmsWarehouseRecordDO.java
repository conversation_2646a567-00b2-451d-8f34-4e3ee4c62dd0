package com.imile.hrms.dao.punch.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 仓内记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_warehouse_record")
public class HrmsWarehouseRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 考勤结果表ID
     */
    private Long warehouseDetailId;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 网点id
     */
    private Long ocId;


    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 人员id
     */
    private Long userId;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 用户所属网点
     * 劳务派遣员工的网点和供应商经常会变
     * 这里存一份快照
     */
    private Long userOcId;

    /**
     * 用户所属供应商ID
     */
    private Long userVendorId;

    /**
     * 用户所属供应商编码
     */
    private String userVendorCode;

    /**
     * 记录类型（1:入仓 2离仓）
     */
    private Integer recordType;

    /**
     * 来源（0:仓内打卡 1:补卡）
     */
    private Integer source;

    /**
     * 仓内日期
     */
    private Date warehouseDate;

    /**
     * 仓内记录时间
     */
    private Date warehouseTime;

    /**
     * 刷脸记录id
     */
    private Long faceRecordId;

    /**
     * 是否提前离仓(0:否 1:是)
     */
    private Integer isEarlyOut;

    /**
     * 打卡网点经度
     */
    private BigDecimal ocLongitude;

    /**
     * 打卡网点纬度
     */
    private BigDecimal ocLatitude;

    /**
     * 工作网点距离
     */
    private BigDecimal distance;
}
