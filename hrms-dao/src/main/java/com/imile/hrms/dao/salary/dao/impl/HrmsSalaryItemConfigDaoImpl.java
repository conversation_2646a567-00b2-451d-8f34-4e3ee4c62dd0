package com.imile.hrms.dao.salary.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.salary.dao.HrmsSalaryItemConfigDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryItemConfigMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigDO;
import com.imile.hrms.dao.salary.query.SalaryItemConfigQuery;
import com.imile.hrms.dao.salary.query.SalaryItemReflectionQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 薪资项配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
public class HrmsSalaryItemConfigDaoImpl extends ServiceImpl<HrmsSalaryItemConfigMapper, HrmsSalaryItemConfigDO> implements HrmsSalaryItemConfigDao {

    @Override
    public List<HrmsSalaryItemConfigDO> selectItemConfigList(SalaryItemConfigQuery query) {
        LambdaQueryWrapper<HrmsSalaryItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsLatest, BusinessConstant.Y);
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.like(HrmsSalaryItemConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.and(i -> query.getCountryList().forEach(country -> i.like(HrmsSalaryItemConfigDO::getCountry, country).or()));
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getStatus, query.getStatus());
        }
        if (query.getIsForceUse() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getIsForceUse, query.getIsForceUse());
        }
        if (query.getItemAttribute() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getItemAttribute, query.getItemAttribute());
        }
        if (StringUtils.isNotBlank(query.getItemNameOrNo())) {
            queryWrapper.and(wrapper -> wrapper.like(HrmsSalaryItemConfigDO::getItemNameCn, query.getItemNameOrNo())
                    .or().like(HrmsSalaryItemConfigDO::getItemNameEn, query.getItemNameOrNo())
                    .or().like(HrmsSalaryItemConfigDO::getItemNo, query.getItemNameOrNo()));
        }
        if (StringUtils.isNotBlank(query.getItemType())) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getItemType, query.getItemType());
        }
        if (query.getIsSalaryArchivesUse() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getIsSalaryArchivesUse, query.getIsSalaryArchivesUse());
        }
        if (StringUtils.isNotBlank(query.getItemValueType())) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getItemValueType, query.getItemValueType());
        }
        queryWrapper.orderByDesc(HrmsSalaryItemConfigDO::getDataSource);
        queryWrapper.orderByDesc(HrmsSalaryItemConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalaryItemConfigDO> selectItemConfigByReflectionList(SalaryItemReflectionQuery query) {
        LambdaQueryWrapper<HrmsSalaryItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsLatest, BusinessConstant.Y);
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getStatus, query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getItemType())) {
            queryWrapper.eq(HrmsSalaryItemConfigDO::getItemType, query.getItemType());
        }
        if (query.getIsReflect() == 1) {
            queryWrapper.isNotNull(HrmsSalaryItemConfigDO::getCostType);
            queryWrapper.ne(HrmsSalaryItemConfigDO::getCostType, "");
        }
        if (query.getIsReflect() == 0) {
            queryWrapper.and(wrapper -> wrapper.isNull(HrmsSalaryItemConfigDO::getCostType).or().eq(HrmsSalaryItemConfigDO::getCostType, ""));
        }
        if (CollectionUtils.isNotEmpty(query.getItemAttributeList())) {
            queryWrapper.in(HrmsSalaryItemConfigDO::getItemAttribute, query.getItemAttributeList());
        }
        queryWrapper.orderByDesc(HrmsSalaryItemConfigDO::getDataSource);
        queryWrapper.orderByDesc(HrmsSalaryItemConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalaryItemConfigDO> selectItemConfigByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalaryItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsSalaryItemConfigDO::getId, idList);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalaryItemConfigDO> selectItemConfigByNoList(List<String> itemConfigNoList) {
        if (CollectionUtils.isEmpty(itemConfigNoList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalaryItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.in(HrmsSalaryItemConfigDO::getItemNo, itemConfigNoList);
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalaryItemConfigDO> selectAllItemConfigList() {
        LambdaQueryWrapper<HrmsSalaryItemConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryItemConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
