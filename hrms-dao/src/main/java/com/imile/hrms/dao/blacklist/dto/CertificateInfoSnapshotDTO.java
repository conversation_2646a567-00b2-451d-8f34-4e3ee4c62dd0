package com.imile.hrms.dao.blacklist.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


/**
 * 黑名单操作记录，证件快照
 *
 * @ClassName CertificateInfoSnapshotDTO
 * <AUTHOR>
 * @Date 2023/5/24 18:39
 */
@Data
public class CertificateInfoSnapshotDTO {

    /**
     * 证件类型编码
     */
    private String certificateTypeCode;

    /**
     * 证件类型名称
     */
    private String certificateName;
    /**
     * 证件号码
     */
    private String certificateCode;

    /**
     * 证件到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String certificateExpireDate;
}
