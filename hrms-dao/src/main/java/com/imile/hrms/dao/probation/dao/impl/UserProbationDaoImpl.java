package com.imile.hrms.dao.probation.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.entity.BaseDO;
import com.imile.hrms.common.enums.probation.ProbationStatusEnum;
import com.imile.hrms.dao.common.CommonCountDO;
import com.imile.hrms.dao.probation.condition.UserProbationSuperiorGroupStatusCondition;
import com.imile.hrms.dao.probation.dao.UserProbationDao;
import com.imile.hrms.dao.probation.dto.ProbationListDTO;
import com.imile.hrms.dao.probation.mapper.UserProbationMapper;
import com.imile.hrms.dao.probation.model.HrmsUserProbationDO;
import com.imile.hrms.dao.probation.po.ProbationListPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户试用期表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class UserProbationDaoImpl
        extends ServiceImpl<UserProbationMapper, HrmsUserProbationDO>
        implements UserProbationDao {

    @Resource
    private UserProbationMapper userProbationMapper;

    @Override
    public HrmsUserProbationDO selectByUserId(Long userId) {
        LambdaQueryWrapper<HrmsUserProbationDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserProbationDO.class);
        queryWrapper.eq(HrmsUserProbationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsUserProbationDO::getUserId, userId);
        return super.getOne(queryWrapper);
    }

    @Override
    public List<HrmsUserProbationDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsUserProbationDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserProbationDO.class);
        queryWrapper.eq(HrmsUserProbationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserProbationDO::getUserId, userIdList);
        return super.list(queryWrapper);
    }

    @Override
    public Page<HrmsUserProbationDO> pageSelectWaitRegular(int pageNum, int size) {
        return this.getBaseMapper().selectPage(new Page<>(pageNum, size),
                new LambdaQueryWrapper<>(HrmsUserProbationDO.class)
                        .eq(BaseDO::getIsDelete, IsDeleteEnum.NO.getCode())
                        .eq(HrmsUserProbationDO::getProbationStatus, ProbationStatusEnum.WAIT_REGULAR.getStatus())
                        .le(HrmsUserProbationDO::getConfirmationDate, new Date()));
    }

    @Override
    public Page<HrmsUserProbationDO> pageSelectByStatus(int pageNum, int size, Integer status) {
        return this.getBaseMapper().selectPage(new Page<>(pageNum, size),
                new LambdaQueryWrapper<>(HrmsUserProbationDO.class)
                        .eq(BaseDO::getIsDelete, IsDeleteEnum.NO.getCode())
                        .eq(HrmsUserProbationDO::getProbationStatus, status));
    }

    @Override
    public Integer countDimissionProbation(UserProbationSuperiorGroupStatusCondition condition) {
        return this.baseMapper.countDimissionProbation(condition);
    }

    @Override
    public List<HrmsUserProbationDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsUserProbationDO> queryWrapper = Wrappers.lambdaQuery(HrmsUserProbationDO.class);
        queryWrapper.eq(HrmsUserProbationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsUserProbationDO::getId, idList);
        return super.list(queryWrapper);
    }

    @Override
    public List<ProbationListPO> probationList(ProbationListDTO dto) {
        return userProbationMapper.probationList(dto);
    }

    @Override
    public List<CommonCountDO> selectCountGroupByProbationStatus(UserProbationSuperiorGroupStatusCondition condition) {
        return this.baseMapper.selectCountGroupByProbationStatus(condition);
    }
}
