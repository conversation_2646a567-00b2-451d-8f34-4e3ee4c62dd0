package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryUserRoleDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryUserRoleMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryUserRoleDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryUserRoleQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户薪资权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Service
public class HrmsSalaryUserRoleDaoImpl extends ServiceImpl<HrmsSalaryUserRoleMapper, HrmsSalaryUserRoleDO> implements HrmsSalaryUserRoleDao {

    @Override
    public List<HrmsSalaryUserRoleDO> listByQuery(HrmsSalaryUserRoleQuery query) {
        LambdaQueryWrapper<HrmsSalaryUserRoleDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryUserRoleDO::getIsDelete, IsDeleteEnum.NO.getCode());

        return list(queryWrapper);

    }

    @Override
    public List<HrmsSalaryUserRoleDO> selectRoleByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalaryUserRoleDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryUserRoleDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsSalaryUserRoleDO::getUserId, userIdList);
        return list(queryWrapper);
    }
}
