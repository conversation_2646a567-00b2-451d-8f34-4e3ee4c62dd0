package com.imile.hrms.dao.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.freelancer.dto.FreelancerAutoDeliveryPreferenceDTO;
import com.imile.hrms.dao.freelancer.dto.FreelancerAutoRegistryAttachmentDTO;
import com.imile.hrms.dao.freelancer.dto.FreelancerDriverDeliveryPreferenceZoneDTO;
import com.imile.hrms.dao.freelancer.dto.FreelancerVehicleDetailInfoDTO;
import com.imile.hrms.dao.freelancer.dto.FreelancerVerificationDetailInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 自主注册众包司机
 *
 * <AUTHOR>
 */
@Data
public class FreelancerInfoAutoRegistryEditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * firstName
     */
    private String firstName;

    /**
     * middleName
     */
    private String middleName;

    /**
     * lastName
     */
    private String lastName;

    /**
     * 出生日期
     */
    private Date birthDate;

    /**
     * 工作经验 1：三年以下，2：三年-5年，3：五年以上
     */
    private String workExperience;

    /**
     * 区号
     */
    private String countryCallingId;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省
     */
    private FreelancerDriverDeliveryPreferenceZoneDTO state;

    /**
     * 市
     */
    private FreelancerDriverDeliveryPreferenceZoneDTO city;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 邮编
     */
    private List<DriverZipCodeDTO> zipCodeList;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * CPF NUMBER
     */
    private String cpfNumber;

    /**
     * CNPJ NUMBER
     */
    private String cnpjNumber;

    /**
     * CPF证件信息(文件类型，文件名称，路径)
     */
    private FreelancerAutoRegistryAttachmentDTO cpfDocument;

    /**
     * CNPJ证件信息(文件类型，文件名称，路径)
     */
    private FreelancerAutoRegistryAttachmentDTO cnpjDocument;

    /**
     * 税务id
     */
    private String taxId;

    /**
     * 驾驶证等级
     */
    private String driverLicenseLevel;

    /**
     * EAR 1：yes / 0：no
     */
    private Integer ear;

    /**
     * 驾驶证编码
     */
    private String driverLicenseCode;

    /**
     * 证件到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date certificateExpireDate;

    /**
     * 驾驶证证件信息(文件类型，文件名称，路径)
     */
    private FreelancerAutoRegistryAttachmentDTO driverLicenseDocument;

    /**
     * 车辆行驶证信息
     */
    private List<FreelancerVehicleDetailInfoDTO> vehicleInfoList;

    /**
     * 开户银行/银行编码(字典)
     */
    private String accountBankName;

    /**
     * 开户支行
     */
    private String agency;

    /**
     * 银行账号
     */
    private String accountNumber;

    /**
     * 电子银行 yes/no
     */
    private Integer digitalBank;

    /**
     * 派送偏好 -- 自主注册
     */
    private List<FreelancerAutoDeliveryPreferenceDTO> autoDeliveryPreferenceList;

    /**
     * 背景审查资料信息
     */
    private FreelancerVerificationDetailInfoDTO freelancerVerificationInfo;

    /**
     * 签名
     */
    private String autograph;

    /**
     * 签名时间
     */
    private Date autographSignOnDate;

    /**
     * 附件信息
     */
    private List<FreelancerAutoRegistryAttachmentDTO> attachmentList;

}
