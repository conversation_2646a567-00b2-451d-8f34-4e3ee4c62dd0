package com.imile.hrms.dao.newAttendance.punchConfig.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.newAttendance.punchConfig.dao.PunchWifiConfigDao;
import com.imile.hrms.dao.newAttendance.punchConfig.mapper.PunchWifiConfigMapper;
import com.imile.hrms.dao.newAttendance.punchConfig.model.PunchWifiConfigDO;
import com.imile.hrms.dao.newAttendance.punchConfig.query.PunchWifiConfigQuery;
import com.imile.hrms.dao.punch.model.HrmsAttendanceWifiConfigDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/13
 * @Description
 */
@Component
@RequiredArgsConstructor
public class PunchWifiConfigDaoImpl extends ServiceImpl<PunchWifiConfigMapper, PunchWifiConfigDO> implements PunchWifiConfigDao {


    @Override
    public List<PunchWifiConfigDO> list(PunchWifiConfigQuery query) {
        LambdaQueryWrapper<PunchWifiConfigDO> queryWrapper = Wrappers.lambdaQuery();
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            queryWrapper.in(PunchWifiConfigDO::getId, query.getIds());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(PunchWifiConfigDO::getCountry, query.getCountry());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(PunchWifiConfigDO::getCountry, query.getCountryList());
        }
        if (StringUtils.isNotBlank(query.getLocationCity())) {
            queryWrapper.eq(PunchWifiConfigDO::getLocationCity, query.getLocationCity());
        }
        if (StringUtils.isNotBlank(query.getWifiName())) {
            queryWrapper.like(PunchWifiConfigDO::getWifiName, query.getWifiName());
        }
        if (StringUtils.isNotBlank(query.getMacAddress())) {
            queryWrapper.eq(PunchWifiConfigDO::getMacAddress, query.getMacAddress());
        }
        if (StringUtils.isNotBlank(query.getBizCountry())) {
            queryWrapper.apply("FIND_IN_SET('" + query.getBizCountry() + "', biz_country)");
//            queryWrapper.apply("FIND_IN_SET(?, biz_country)", query.getBizCountry());
        }
        queryWrapper.eq(PunchWifiConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());

        return list(queryWrapper);
    }

    @Override
    public List<PunchWifiConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<PunchWifiConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }
}
