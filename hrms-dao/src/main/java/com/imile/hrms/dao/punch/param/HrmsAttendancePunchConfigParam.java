package com.imile.hrms.dao.punch.param;

import com.imile.hrms.dao.punch.dto.HrmsAttendancePunchConfigRangeDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 考勤打卡规则DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Data
public class HrmsAttendancePunchConfigParam implements Serializable {


    private Long id;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     */
    private String punchConfigType;

    /**
     * 使用范围记录
     */
    List<HrmsAttendancePunchConfigRangeDTO> rangeRecords;

    /**
     * 已绑定员工数
     */
    private Integer employeeCount;
    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;
    /**
     * 最近修改日期
     */
    private Date lastUpdDate;


}
