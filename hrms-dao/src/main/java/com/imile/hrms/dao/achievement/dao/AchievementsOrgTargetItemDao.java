package com.imile.hrms.dao.achievement.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.achievement.model.AchievementsOrgTargetItemDO;
import com.imile.hrms.dao.achievement.query.AchievementsOrgTargetItemQuery;

import java.util.List;

/**
 * <p>
 * 组织目标明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface AchievementsOrgTargetItemDao extends IService<AchievementsOrgTargetItemDO> {

    List<AchievementsOrgTargetItemDO> getByTargetId(AchievementsOrgTargetItemQuery orgTargetItemQuery);
}
