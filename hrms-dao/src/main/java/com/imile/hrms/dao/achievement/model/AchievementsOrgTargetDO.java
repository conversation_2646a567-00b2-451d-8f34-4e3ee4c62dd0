package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 组织目标
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("achievements_org_target")
public class AchievementsOrgTargetDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 活动id
     */
    private Long eventId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 状态
     */
    private String status;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 扩展信息
     */
    private String extend;

    private Integer isLatest;

    /**
     * 子考核周期 默认0:无周期 1季度 2月度 3周 4全年
     */
    private Integer cycleType;

    /**
     * 是否关联运营指标：默认0否 1是
     */
    private Integer isRelatedOperationalIndicators;

    /**
     * 运营指标范围
     */
    private String scopeOfOperationalIndicators;

    /**
     * 归组编码
     */
    private String groupCode;

    /**
     * 组织id的父级id
     */
    private Long parentId;

    /**
     * 总得分
     */
    private BigDecimal score;

}
