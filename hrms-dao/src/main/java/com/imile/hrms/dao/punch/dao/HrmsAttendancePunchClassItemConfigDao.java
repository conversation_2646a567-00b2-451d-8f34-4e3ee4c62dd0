package com.imile.hrms.dao.punch.dao;

import com.imile.hrms.dao.punch.model.HrmsAttendancePunchClassItemConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 考勤打卡规则班次时间配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-27
 */
public interface HrmsAttendancePunchClassItemConfigDao extends IService<HrmsAttendancePunchClassItemConfigDO> {

    List<HrmsAttendancePunchClassItemConfigDO> selectItemConfigByClassId(List<Long> classIds);

    List<HrmsAttendancePunchClassItemConfigDO> listByPage(int currentPage, int pageSize);
}
