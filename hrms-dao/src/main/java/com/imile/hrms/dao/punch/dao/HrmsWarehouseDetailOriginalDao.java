package com.imile.hrms.dao.punch.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailOriginalDO;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓内考勤初始表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface HrmsWarehouseDetailOriginalDao extends IService<HrmsWarehouseDetailOriginalDO> {

    List<HrmsWarehouseDetailOriginalDO> selectByWarehouseDateAndUserIds(Date warehouseDate, List<Long> userIdList);

}
