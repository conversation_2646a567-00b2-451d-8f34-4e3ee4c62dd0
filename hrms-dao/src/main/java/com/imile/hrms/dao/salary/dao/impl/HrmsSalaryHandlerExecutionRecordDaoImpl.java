package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryHandlerExecutionRecordDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryHandlerExecutionRecordMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryHandlerExecutionRecordDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryHandlerExecutionRecordQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 定时任务执行记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Service
public class HrmsSalaryHandlerExecutionRecordDaoImpl extends ServiceImpl<HrmsSalaryHandlerExecutionRecordMapper, HrmsSalaryHandlerExecutionRecordDO> implements HrmsSalaryHandlerExecutionRecordDao {

    @Override
    public List<HrmsSalaryHandlerExecutionRecordDO> listByQuery(HrmsSalaryHandlerExecutionRecordQuery query) {
        LambdaQueryWrapper<HrmsSalaryHandlerExecutionRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsSalaryHandlerExecutionRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());

        if (CollectionUtils.isNotEmpty(query.getTaskConfigIdList())) {
            queryWrapper.in(HrmsSalaryHandlerExecutionRecordDO::getSalaryTaskConfigId, query.getTaskConfigIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(HrmsSalaryHandlerExecutionRecordDO::getPaymentCountry, query.getCountryList());
        }
        if (StringUtils.isNotBlank(query.getSource())) {
            queryWrapper.eq(HrmsSalaryHandlerExecutionRecordDO::getSource, query.getSource());
        }

        return list(queryWrapper);
    }
}
