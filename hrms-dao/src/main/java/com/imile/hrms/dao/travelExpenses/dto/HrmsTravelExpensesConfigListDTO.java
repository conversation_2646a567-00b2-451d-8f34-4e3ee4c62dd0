package com.imile.hrms.dao.travelExpenses.dto;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 差旅费用配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
public class HrmsTravelExpensesConfigListDTO{

    /**
     * 费用项
     */
    private String expense;

    /**
     * 费用项
     */
    private String expenseEn;


    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 生效时间
     */
    private Date effectTime;


}
