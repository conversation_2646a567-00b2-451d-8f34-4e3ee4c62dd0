package com.imile.hrms.dao.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.hrms.dao.salary.model.HrmsEmployeeSalaryInfoDO;
import com.imile.hrms.dao.salary.query.SalaryUserDetailQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 员工薪资 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-14
 */
@Mapper
@Repository
public interface HrmsEmployeeSalaryInfoMapper extends BaseMapper<HrmsEmployeeSalaryInfoDO> {

    /**
     * 员工薪资明细列表
     *
     * @param query
     * @return
     */
    //List<SalaryEmployeeDetailInfoDTO> infoList(SalaryEmployeeDetailQuery query);
    //List<HrmsEmployeeSalaryInfoDO> infoList(SalaryEmployeeDetailQuery query);

    List<HrmsEmployeeSalaryInfoDO> salaryUserInfoList(SalaryUserDetailQuery query);

}
