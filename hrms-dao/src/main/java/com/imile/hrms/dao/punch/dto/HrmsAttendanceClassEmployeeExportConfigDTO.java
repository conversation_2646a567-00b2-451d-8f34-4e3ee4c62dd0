package com.imile.hrms.dao.punch.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 排班导出
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@Data
public class HrmsAttendanceClassEmployeeExportConfigDTO implements Serializable {

    /**
     * 日期
     */
    private Long dayId;

    /**
     * 日期
     */
    private String date;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 国家
     */
    private String originCountry;

    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchRuleName;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 时刻1-上班时间
     */
    private String punchInTime1;

    /**
     * 时刻1-下班时间
     */
    private String punchOutTime1;

    /**
     * 时刻1-上下班打卡间隔时长
     */
    private String punchTimeInterval1;

    /**
     * 时刻2-上班时间
     */
    private String punchInTime2;

    /**
     * 时刻2-下班时间
     */
    private String punchOutTime2;

    /**
     * 时刻2-上下班打卡间隔时长
     */
    private String punchTimeInterval2;

    /**
     * 时刻3-上班时间
     */
    private String punchInTime3;

    /**
     * 时刻3-下班时间
     */
    private String punchOutTime3;

    /**
     * 时刻3-上下班打卡间隔时长
     */
    private String punchTimeInterval3;

    /**
     * 总时长
     */
    private String totalHours;

}
