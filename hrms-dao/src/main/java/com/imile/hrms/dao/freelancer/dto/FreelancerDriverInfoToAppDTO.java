package com.imile.hrms.dao.freelancer.dto;

import lombok.Data;

import java.util.List;

@Data
public class FreelancerDriverInfoToAppDTO {

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 审批类型
     */
    private String reviewType;

    /**
     * firstName
     */
    private String firstName;

    /**
     * middleName
     */
    private String middleName;

    /**
     * lastName
     */
    private String lastName;

    /**
     * 国家
     */
    private FreelancerDriverDeliveryPreferenceZoneToAppDTO country;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 区号
     */
    private String countryCallingId;

    /**
     * 电话
     */
    private String phone;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 资料完善度
     */
    private String dataCompleteness;

    /**
     * driver license Australian/Overseas
     */
    private String licenseType;

    /**
     * driver license Type  Automatic/manual
     */
    private String licenseClassification;

    /**
     * 扩展信息
     */
    private FreelancerDriverExtendInfoToAppDTO extendInfo;

    /**
     * 银行卡信息
     */
    private FreelancerDriverBankInfoDTO bankInfo;

    /**
     * 身份证件信息
     */
    private FreelancerDriverCertificateInfoToAppDTO idCard;

    /**
     * 驾驶证件信息
     */
    private FreelancerDriverCertificateInfoToAppDTO driverLicense;

    /**
     * 派送偏好
     */
    private FreelancerDriverDeliveryPreferenceToAppDTO deliveryPreference;

}