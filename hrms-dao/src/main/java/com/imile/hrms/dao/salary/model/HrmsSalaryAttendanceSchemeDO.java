package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 薪资每月计薪方案信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_attendance_scheme")
public class HrmsSalaryAttendanceSchemeDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 结算时间-年月(202308)
     */
    private Long settlementDate;

    /**
     * 申请国家
     */
    private String applyCountry;

    /**
     * 计薪方案编码
     */
    private String salarySchemeConfigNo;

    /**
     * 当前月份整个计薪方案算薪状态(已锁定/未锁定/已关账/部分未锁定/部分关账等)
     */
    private String status;

    /**
     * 当前月份计薪方案的标头展示副本
     */
    private String titleInfo;


}
