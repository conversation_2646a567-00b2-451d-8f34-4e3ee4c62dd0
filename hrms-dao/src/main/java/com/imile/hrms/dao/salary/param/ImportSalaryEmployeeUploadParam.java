package com.imile.hrms.dao.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelImport;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/4
 */
@Data
public class ImportSalaryEmployeeUploadParam extends ExcelImport {
    /**
     * 日期
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String salaryDate;
    /**
     * 账号
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String userCode;
    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 激励薪资
     */
    private String incentiveSalary;

    /**
     * 其他增项
     */
    private String otherIncreaseSalary;

    /**
     * 其他减项
     */
    private String otherDecreaseSalary;

    /**
     * 税收
     */
    private String taxation;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 国家
     */
    private String country;

}
