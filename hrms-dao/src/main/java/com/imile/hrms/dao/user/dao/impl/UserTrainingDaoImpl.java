package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserTrainingDao;
import com.imile.hrms.dao.user.mapper.UserTrainingMapper;
import com.imile.hrms.dao.user.model.UserTrainingDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/07
 */
@Service
public class UserTrainingDaoImpl
        extends ServiceImpl<UserTrainingMapper, UserTrainingDO>
        implements UserTrainingDao {

    @Resource
    private UserTrainingMapper userTrainingMapper;

    @Override
    public List<UserTrainingDO> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserTrainingDO> queryWrapper = Wrappers.lambdaQuery(UserTrainingDO.class);
        queryWrapper.eq(UserTrainingDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserTrainingDO::getUserId, userId);
        return super.list(queryWrapper);
    }
}
