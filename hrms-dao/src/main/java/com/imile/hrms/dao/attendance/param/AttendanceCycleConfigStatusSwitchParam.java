package com.imile.hrms.dao.attendance.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} AttendanceCycleConfigStatusSwitchParam
 * {@code @since:} 2024-10-31 11:21
 * {@code @description:}
 */
@Data
public class AttendanceCycleConfigStatusSwitchParam implements Serializable {

    private static final long serialVersionUID = -896662834591482460L;

    /**
     * 考勤周期id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 考勤周期状态
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String status;
}
