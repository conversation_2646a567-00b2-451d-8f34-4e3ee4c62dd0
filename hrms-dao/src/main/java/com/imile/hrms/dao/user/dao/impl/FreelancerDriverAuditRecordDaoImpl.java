package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.enums.user.FreelancerDriverAuditStatusEnum;
import com.imile.hrms.dao.common.CommonCountDO;
import com.imile.hrms.dao.user.condition.FreelancerDriverAuditRecordCustomConditionBuilder;
import com.imile.hrms.dao.user.dao.FreelancerDriverAuditRecordDao;
import com.imile.hrms.dao.user.mapper.FreelancerDriverAuditRecordMapper;
import com.imile.hrms.dao.user.model.FreelancerDriverAuditRecordDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 众包司机审核记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Service
public class FreelancerDriverAuditRecordDaoImpl extends ServiceImpl<FreelancerDriverAuditRecordMapper, FreelancerDriverAuditRecordDO> implements FreelancerDriverAuditRecordDao {

    @Override
    public List<FreelancerDriverAuditRecordDO> selectByCustomCondition(FreelancerDriverAuditRecordCustomConditionBuilder builder) {
        return baseMapper.selectByCustomCondition(builder);
    }

    @Override
    public List<CommonCountDO> countGroupByAuditStatus(List<Long> ocDeptIdList) {
        if (CollectionUtils.isEmpty(ocDeptIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.countGroupByAuditStatus(ocDeptIdList);
    }

    @Override
    public FreelancerDriverAuditRecordDO selectUnfinishedByUserId(Long userId) {
        LambdaQueryWrapper<FreelancerDriverAuditRecordDO> queryWrapper = Wrappers.lambdaQuery(FreelancerDriverAuditRecordDO.class);
        queryWrapper.eq(FreelancerDriverAuditRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(FreelancerDriverAuditRecordDO::getUserId, userId);
        queryWrapper.ne(FreelancerDriverAuditRecordDO::getAuditStatus, FreelancerDriverAuditStatusEnum.PASSED.getStatus());
        List<FreelancerDriverAuditRecordDO> list = super.list(queryWrapper);
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public FreelancerDriverAuditRecordDO selectLatestByUserId(Long userId) {
        LambdaQueryWrapper<FreelancerDriverAuditRecordDO> queryWrapper = Wrappers.lambdaQuery(FreelancerDriverAuditRecordDO.class);
        queryWrapper.eq(FreelancerDriverAuditRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(FreelancerDriverAuditRecordDO::getUserId, userId);
        queryWrapper.orderByDesc(FreelancerDriverAuditRecordDO::getCreateDate);
        List<FreelancerDriverAuditRecordDO> list = super.list(queryWrapper);
        return list.isEmpty() ? null : list.get(0);
    }
}
