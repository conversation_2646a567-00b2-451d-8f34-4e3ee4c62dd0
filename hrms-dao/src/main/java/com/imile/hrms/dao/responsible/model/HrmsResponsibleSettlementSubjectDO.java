package com.imile.hrms.dao.responsible.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_responsible_settlement_subject")
public class HrmsResponsibleSettlementSubjectDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 在业状态
     */
    private String status;

    /**
     * 简称
     */
    private String settlementShortName;

    /**
     * 全称
     */
    private String settlementCenterName;

    /**
     * 注册国
     */
    private String registerCountry;

    /**
     * 结算主体id
     */
    private Long companyOrgId;


}
