package com.imile.hrms.dao.leave.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.leave.HrmsCompanyLeaveConfigCarryOverDao;
import com.imile.hrms.dao.leave.mapper.HrmsCompanyLeaveConfigCarryOverMapper;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigCarryOverDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyLeaveConfigCarryOverDaoImpl
 * {@code @since:} 2024-04-10 15:04
 * {@code @description:}
 */
@Service
public class HrmsCompanyLeaveConfigCarryOverDaoImpl extends ServiceImpl<HrmsCompanyLeaveConfigCarryOverMapper, HrmsCompanyLeaveConfigCarryOverDO> implements HrmsCompanyLeaveConfigCarryOverDao {

    /**
     * 根据假期方案id查询假期结转配置
     *
     * @param allCompanyLeaveConfigIdList 假期方案id
     * @return 假期结转配置
     */
    @Override
    public List<HrmsCompanyLeaveConfigCarryOverDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList) {
        if (CollUtil.isEmpty(allCompanyLeaveConfigIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HrmsCompanyLeaveConfigCarryOverDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsCompanyLeaveConfigCarryOverDO::getLeaveId, allCompanyLeaveConfigIdList);
        queryWrapper.eq(HrmsCompanyLeaveConfigCarryOverDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
