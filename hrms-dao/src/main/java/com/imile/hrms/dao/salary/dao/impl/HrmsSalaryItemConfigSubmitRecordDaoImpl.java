package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryItemConfigSubmitRecordDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryItemConfigSubmitRecordMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryItemConfigSubmitRecordDO;
import com.imile.hrms.dao.salary.query.HrmsSalaryItemConfigSubmitRecordPageQuery;
import com.imile.hrms.dao.salary.query.HrmsSalaryItemConfigSubmitRecordQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 薪资月结数据科目提报记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-17
 */
@Service
public class HrmsSalaryItemConfigSubmitRecordDaoImpl extends ServiceImpl<HrmsSalaryItemConfigSubmitRecordMapper, HrmsSalaryItemConfigSubmitRecordDO> implements HrmsSalaryItemConfigSubmitRecordDao {

    @Override
    public List<HrmsSalaryItemConfigSubmitRecordDO> listByQuery(HrmsSalaryItemConfigSubmitRecordQuery query) {

        LambdaQueryWrapper<HrmsSalaryItemConfigSubmitRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsSalaryItemConfigSubmitRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (query.getSalarySettlementUserId() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigSubmitRecordDO::getSalarySettlementUserInfoId, query.getSalarySettlementUserId());
        }
        if (CollectionUtils.isNotEmpty(query.getSalarySettlementUserIdList())) {
            queryWrapper.in(HrmsSalaryItemConfigSubmitRecordDO::getSalarySettlementUserInfoId, query.getSalarySettlementUserIdList());
        }
        if (query.getItemId() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigSubmitRecordDO::getItemId, query.getItemId());
        }
        if (CollectionUtils.isNotEmpty(query.getItemIdList())) {
            queryWrapper.in(HrmsSalaryItemConfigSubmitRecordDO::getItemId, query.getItemIdList());
        }
        return list(queryWrapper);
    }

    @Override
    public List<HrmsSalaryItemConfigSubmitRecordDO> listByPageQuery(HrmsSalaryItemConfigSubmitRecordPageQuery query) {

        LambdaQueryWrapper<HrmsSalaryItemConfigSubmitRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsSalaryItemConfigSubmitRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (query.getSalarySettlementUserId() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigSubmitRecordDO::getSalarySettlementUserInfoId, query.getSalarySettlementUserId());
        }
        if (CollectionUtils.isNotEmpty(query.getSalarySettlementUserIdList())) {
            queryWrapper.in(HrmsSalaryItemConfigSubmitRecordDO::getSalarySettlementUserInfoId, query.getSalarySettlementUserIdList());
        }
        if (query.getItemId() != null) {
            queryWrapper.eq(HrmsSalaryItemConfigSubmitRecordDO::getItemId, query.getItemId());
        }
        if (CollectionUtils.isNotEmpty(query.getItemIdList())) {
            queryWrapper.in(HrmsSalaryItemConfigSubmitRecordDO::getItemId, query.getItemIdList());
        }

        queryWrapper.orderByDesc(HrmsSalaryItemConfigSubmitRecordDO::getCreateDate);
        return list(queryWrapper);
    }

}
