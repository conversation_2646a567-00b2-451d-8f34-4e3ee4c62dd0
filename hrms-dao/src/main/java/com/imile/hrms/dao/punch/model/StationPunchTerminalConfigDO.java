package com.imile.hrms.dao.punch.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
//@TableName("station_punch_terminal_config")
public class StationPunchTerminalConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 终端sn
     */
    private String terminalSn;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 排序
     */
    private BigDecimal orderby;


}
