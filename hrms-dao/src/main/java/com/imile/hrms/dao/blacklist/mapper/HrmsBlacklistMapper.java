package com.imile.hrms.dao.blacklist.mapper;


import com.imile.hrms.dao.blacklist.dto.BlacklistPageDTO;
import com.imile.hrms.dao.blacklist.dto.LockdownInfoDTO;
import com.imile.hrms.dao.blacklist.model.HrmsBlacklistDO;
import com.imile.hrms.dao.blacklist.query.BlacklistPageQuery;
import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.user.dto.UserCertificateInfoParamDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 黑名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Mapper
public interface HrmsBlacklistMapper extends HrmsBaseMapper<HrmsBlacklistDO> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return List<BlacklistPageDTO>
     */
    List<BlacklistPageDTO> getPageList(BlacklistPageQuery query);

    /**
     * 查询黑名单里的证件信息
     *
     * @param certificateList 证件信息
     * @return List<LockdownInfoDTO>
     */
    List<LockdownInfoDTO>  getLockdownInfo(@Param("certificateList") List<UserCertificateInfoParamDTO> certificateList);
}
