package com.imile.hrms.dao.sys.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 平台id关系映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_platform_relation")
public class HrmsPlatformRelationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务 业务id,如dept_id,user_code
     */
    private String bizId;

    /**
     * 业务类型 业务类型，如部门DEPT,用户USER
     */
    private String bizType;

    /**
     * 映射
     */
    private String relationId;

    /**
     * 平台类别 数据来源：钉钉DingTalk，企业微信，TMS
     */
    private String platformType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否启用
     */
    private String status;

    /**
     * 是否为最新 是否最新，结合版本号使用
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;


}
