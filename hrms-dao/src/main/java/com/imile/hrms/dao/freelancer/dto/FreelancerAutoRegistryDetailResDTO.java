package com.imile.hrms.dao.freelancer.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.dao.user.dto.DriverZipCodeDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FreelancerAutoRegistryDetailResDTO {
    /**
     * 审批流状态
     */
    private String approvalStatus;

    /**
     * 状态
     */
    private String reviewStatus;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * firstName
     */
    private String firstName;

    /**
     * middleName
     */
    private String middleName;

    /**
     * lastName
     */
    private String lastName;

    /**
     * 注册邮箱
     */
    private String email;

    /**
     * 出生日期
     */
    private Date birthDate;

    /**
     * 工作经验 1：三年以下，2：三年-5年，3：五年以上
     */
    private String workExperience;

    /**
     * 区号
     */
    private String countryCallingId;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省
     */
    private FreelancerDriverDeliveryPreferenceZoneDTO state;

    /**
     * 市
     */
    private FreelancerDriverDeliveryPreferenceZoneDTO city;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 邮编
     */
    private List<DriverZipCodeDTO> zipCodeList;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * CPF NUMBER
     */
    private String cpfNumber;

    /**
     * CNPJ NUMBER
     */
    private String cnpjNumber;

    /**
     * CPF证件信息
     */
    @HyperLink(ref = "cpfCertificatePathHttps")
    private String cpfCertificatePath;
    private String cpfCertificatePathHttps;

    /**
     * CNPJ证件信息
     */
    @HyperLink(ref = "cnpjCertificatePathHttps")
    private String cnpjCertificatePath;
    private String cnpjCertificatePathHttps;

    /**
     * 驾驶证等级
     */
    private String driverLicenseLevel;

    /**
     * EAR 1：yes / 0：no
     */
    private Integer ear;

    /**
     * 驾驶证编码
     */
    private String driverLicenseCode;

    /**
     * CPF证件信息(文件类型，文件名称，路径)
     */
    private FreelancerAutoRegistryAttachmentDTO cpfDocument;

    /**
     * CNPJ证件信息(文件类型，文件名称，路径)
     */
    private FreelancerAutoRegistryAttachmentDTO cnpjDocument;

    /**
     * 驾驶证证件信息(文件类型，文件名称，路径)
     */
    private FreelancerAutoRegistryAttachmentDTO driverLicenseDocument;

    /**
     * 证件到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date certificateExpireDate;

    /**
     * 驾驶证证件路径
     */
    @HyperLink(ref = "certificatePathHttps")
    private String certificatePath;
    private String certificatePathHttps;

    /**
     * 车辆行驶证信息
     */
    private List<FreelancerVehicleDetailInfoDTO> vehicleInfoList;

    /**
     * 开户银行/银行编码(字典)
     */
    private String accountBankName;

    /**
     * 开户支行
     */
    private String agency;

    /**
     * 银行账号
     */
    private String accountNumber;

    /**
     * 电子银行 yes/no
     */
    private Integer digitalBank;

    /**
     * 派送偏好 -- 自主注册
     */
    private List<FreelancerAutoDeliveryPreferenceDTO> autoDeliveryPreferenceList;

    /**
     * 背景审查资料信息
     */
    private FreelancerVerificationDetailInfoDTO freelancerVerificationInfo;

    /**
     * 签名
     */
    private String autograph;

    /**
     * 签名时间
     */
    private Date autographSignOnDate;

    /**
     * 税务id
     */
    private String taxId;

    /**
     * 税务ID来源
     */
    private String taxIdSource;

}
