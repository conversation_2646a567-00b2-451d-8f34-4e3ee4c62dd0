package com.imile.hrms.dao.salary.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/3/6 17:10
 * @version: 1.0
 */
@Data
@Builder
public class HrmsSalarySettlementAgentRecordQuery {

    private Long id;

    private List<Long> idList;

    /**
     * 计薪国
     */
    private String paymentCountry;

    /**
     * 计薪国
     */
    private List<String> paymentCountryList;

    /**
     * 计薪月份(202308)
     */
    private String paymentMonth;

    /**
     * 计薪月份(202308)
     */
    private List<String> paymentMonthList;

    /**
     * 计薪方案编码
     */
    private String salarySchemeConfigNo;

    /**
     * 薪资数据提报模版配置ID
     */
    private Long salarySubmitTemplateConfigId;

    /**
     * 薪资数据提报模版配置ID
     */
    private List<Long> salarySubmitTemplateConfigIdList;

    /**
     * 薪资数据提报模版配置编码
     */
    private String salarySubmitTemplateConfigNo;


}
