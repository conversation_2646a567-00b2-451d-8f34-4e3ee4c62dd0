package com.imile.hrms.dao.organization.dto;

import lombok.Data;

/**
 * 部门下员工信息
 */
@Data
public class DeptUserInfoDTO {
    private Long id;

    /**
     * 状态
     */
    private String workStatus;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 部门
     */
    private Long deptId;
    /**
     * 部门
     */
    private String deptName;

    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 岗位
     */
    private Long postId;
    /**
     * 岗位
     */
    private String postName;

    /**
     * 性质
     */
    private String employeeType;

}
