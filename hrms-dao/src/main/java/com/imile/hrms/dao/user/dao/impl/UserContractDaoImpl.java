package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserContractDao;
import com.imile.hrms.dao.user.mapper.UserContractMapper;
import com.imile.hrms.dao.user.model.UserContractDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
@Service
public class UserContractDaoImpl extends ServiceImpl<UserContractMapper, UserContractDO> implements UserContractDao {
    @Override
    public List<UserContractDO> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserContractDO> queryWrapper = Wrappers.lambdaQuery(UserContractDO.class);
        queryWrapper.eq(UserContractDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserContractDO::getUserId, userId);
        queryWrapper.orderByAsc(UserContractDO::getCreateDate);
        return super.list(queryWrapper);
    }

    @Override
    public List<UserContractDO> selectByUserIdList(List<Long> userIdList) {
        LambdaQueryWrapper<UserContractDO> queryWrapper = Wrappers.lambdaQuery(UserContractDO.class);
        queryWrapper.eq(UserContractDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserContractDO::getUserId, userIdList);
        queryWrapper.orderByAsc(UserContractDO::getCreateDate);
        return super.list(queryWrapper);
    }
}
