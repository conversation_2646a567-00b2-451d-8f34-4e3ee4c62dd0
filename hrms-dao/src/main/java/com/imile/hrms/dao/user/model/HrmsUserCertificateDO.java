package com.imile.hrms.dao.user.model;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 用户证件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_certificate")
public class HrmsUserCertificateDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属国
     */
    private String country;

    /**
     * 用户_id
     */
    private Long userId;
    /**
     * y员工编码
     */
    private String userCode;


    /**
     * 证件类型
     */
    private String certificateTypeCode;
    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 证件到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateExpireDate;


    /**
     * 领证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateReceiptDate;


    /**
     * 证件正面路径
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String certificateFrontPath;

    /**
     * 证件背面路径
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String certificateBackPath;
    /**
     * 处理方法 MUST_HANDLER,NEED_HANDLER,NEED_NOT_HANDLER
     */
    private String handlerMethod;


    /**
     * 是否填写必填信息完毕
     */
    private Integer isFinish;

    /**
     * 扩展字段
     */
    private String extend;

    private String remark;

    private String orderby;


}
