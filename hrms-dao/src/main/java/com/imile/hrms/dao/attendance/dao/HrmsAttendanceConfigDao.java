package com.imile.hrms.dao.attendance.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceConfigDO;
import com.imile.hrms.dao.attendance.query.AttendanceConfigQuery;

import java.util.Collection;
import java.util.List;


/**
 * <p>
 * 考勤配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
public interface HrmsAttendanceConfigDao extends IService<HrmsAttendanceConfigDO> {

    /**
     * 查询国家下的所有日历
     */
    List<HrmsAttendanceConfigDO> selectByCountryList(List<String> countryList);

    /**
     * 根据ID查询
     */
    HrmsAttendanceConfigDO getActiveById(Long id);

    /**
     * 统计国家下默认方案数量
     *
     * @param country
     * @return
     */
    Integer countDefaultAttendanceConfig(String country);

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    List<HrmsAttendanceConfigDO> list(AttendanceConfigQuery query);

    /**
     * 根据配置类型返回对应的考勤方案
     *
     * @param configType
     * @param country
     * @return
     */
    List<HrmsAttendanceConfigDO> config(String configType, String country);

    /**
     * 根据编号批量查询
     *
     * @param attendanceConfigNos
     * @return
     */
    List<HrmsAttendanceConfigDO> listByNos(Collection<String> attendanceConfigNos);

    /**
     * 根据country查询
     *
     * @param country
     * @return
     */
    List<HrmsAttendanceConfigDO> listByCountry(String country);


    List<HrmsAttendanceConfigDO> listByPage(int currentPage, int pageSize);
}
