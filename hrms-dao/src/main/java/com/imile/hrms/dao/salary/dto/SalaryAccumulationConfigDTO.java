package com.imile.hrms.dao.salary.dto;

import lombok.Data;

import java.util.Date;

/**
 * 公积金DTO
 * <AUTHOR>
 */
@Data
public class SalaryAccumulationConfigDTO {
    private Long id;
    /**
     * 社保唯一编码
     */
    private String accumulationConfigNo;
    /**
     * 社保方案名称
     */
    private String accumulationConfigName;
    /**
     * 国家
     */
    private String country;
    /**
     * 绑定的员工数
     */
    private Integer employeeCount;
    /**
     * 创建人名称
     */
    private String createUserName;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 最近修改员工姓名
     */
    private String lastUpdUserName;
    /**
     * 最近修改时间
     */
    private Date  lastUpdDate;
    /**
     * 是否默认方案
     */
    private Integer isDefault;
    /**
     * 启用状态
     */
    private String status;

}
