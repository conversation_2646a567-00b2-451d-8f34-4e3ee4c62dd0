package com.imile.hrms.dao.vendor.dto;

import com.imile.common.page.PaginationResult;
import lombok.Data;

import java.util.List;

/**
 * @author: milo
 * @createDate: 2022-10-20
 */
@Data
public class HrmsDriverPerformanceDetailInfoDTO {

    /**
     * 司机考核信息（周期内）
     */
    private PaginationResult<HrmsDriverIndicatorsDetailDTO> indicatorsDetailPage;

    /**
     * 司机考核信息（周期内）
     */
    private List<HrmsDriverIndicatorsDetailDTO> indicatorsDetailList;


    /**
     * 网点总分KPI
     */
    private String totalScores;

    /**
     * 网点绩效列表
     */
    private List<HrmsVendorPerformanceDTO> vendorPerformanceList;



}
