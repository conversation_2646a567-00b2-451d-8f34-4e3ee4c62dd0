package com.imile.hrms.dao.bpm.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-12-7
 * @version: 1.0
 */
@Data
public class ApprovalTipQuery extends BaseQuery {
    /**
     * 网点ID
     */
    private List<Long> deptIdList;

    /**
     * 审批状态 1审批中 2 通过 -1拒绝 -2驳回 -3撤回
     */
    private List<Long> approvalStatusList;

    /**
     * 是否已读 0: 否 1:是
     */
    private Integer isRead;

    /**
     * 界面来源，是司机管理NEW还是员工管理NEW
     * 1司机管理ENW  0员工管理NEW
     */
    private Integer isDriver;

}
