package com.imile.hrms.dao.punch.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * GPS配置修改传参
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
public class HrmsAttendanceGpsConfigUpdateParam implements Serializable {

    /**
     * 主键id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 有效范围
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer effectiveRange;

}
