package com.imile.hrms.dao.primary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.primary.dao.UserCertificateDao;
import com.imile.hrms.dao.primary.entity.UserCertificateDO;
import com.imile.hrms.dao.primary.entity.condition.UserCertificateConditionBuilder;
import com.imile.hrms.dao.primary.mapper.UserCertificateMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 人员证件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
public class UserCertificateDaoImpl extends ServiceImpl<UserCertificateMapper, UserCertificateDO> implements UserCertificateDao {

    @Override
    public List<UserCertificateDO> selectByCertificateList(List<UserCertificateConditionBuilder.Certificate> certificateList) {
        return baseMapper.selectByCertificateList(certificateList);
    }

    @Override
    public List<UserCertificateDO> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserCertificateDO> queryWrapper = Wrappers.lambdaQuery(UserCertificateDO.class);
        queryWrapper.eq(UserCertificateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserCertificateDO::getUserId, userId);
        return this.list(queryWrapper);
    }

    @Override
    public List<UserCertificateDO> selectByCondition(UserCertificateConditionBuilder builder) {
        LambdaQueryWrapper<UserCertificateDO> queryWrapper = Wrappers.lambdaQuery(UserCertificateDO.class);
        queryWrapper.eq(UserCertificateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(builder.getUserIdList())) {
            queryWrapper.in(UserCertificateDO::getUserId, builder.getUserIdList());
        }
        if (CollectionUtils.isNotEmpty(builder.getCertificateTypeCodeList())) {
            queryWrapper.in(UserCertificateDO::getCertificateTypeCode, builder.getCertificateTypeCodeList());
        }
        return this.list(queryWrapper);
    }
}
