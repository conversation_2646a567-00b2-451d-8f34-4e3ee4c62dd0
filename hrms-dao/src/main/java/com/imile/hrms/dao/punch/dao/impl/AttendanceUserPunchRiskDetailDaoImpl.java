package com.imile.hrms.dao.punch.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.dao.punch.dao.AttendanceUserPunchRiskDetailDao;
import com.imile.hrms.dao.punch.mapper.AttendanceUserPunchRiskDetailMapper;
import com.imile.hrms.dao.punch.model.AttendanceUserPunchRiskDetailDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/5
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceUserPunchRiskDetailDaoImpl extends ServiceImpl<AttendanceUserPunchRiskDetailMapper, AttendanceUserPunchRiskDetailDO> implements AttendanceUserPunchRiskDetailDao {

    @Override
    public List<AttendanceUserPunchRiskDetailDO> selectByRiskIds(List<Long> riskIds) {
        if (CollUtil.isEmpty(riskIds)) {
            return CollUtil.newArrayList();
        }

        LambdaQueryWrapper<AttendanceUserPunchRiskDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AttendanceUserPunchRiskDetailDO::getUserPunchRiskId, riskIds)
                .eq(AttendanceUserPunchRiskDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());

        return this.list(queryWrapper);
    }

    @Override
    public int deleteByRiskIds(List<Long> riskIds) {
        if (CollUtil.isEmpty(riskIds)) {
            return 0;
        }

        LambdaUpdateWrapper<AttendanceUserPunchRiskDetailDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AttendanceUserPunchRiskDetailDO::getUserPunchRiskId, riskIds)
                .eq(AttendanceUserPunchRiskDetailDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .set(AttendanceUserPunchRiskDetailDO::getIsDelete, IsDeleteEnum.YES.getCode())
                .set(AttendanceUserPunchRiskDetailDO::getLastUpdDate, new Date())
                .set(AttendanceUserPunchRiskDetailDO::getLastUpdUserCode, RequestInfoHolder.getUserCode())
                .set(AttendanceUserPunchRiskDetailDO::getLastUpdUserName, RequestInfoHolder.getUserName());

        return this.update(updateWrapper) ? 1 : 0;
    }
}
