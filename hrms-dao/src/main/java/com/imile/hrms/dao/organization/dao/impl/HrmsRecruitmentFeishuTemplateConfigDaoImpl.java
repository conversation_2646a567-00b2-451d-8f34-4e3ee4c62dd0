package com.imile.hrms.dao.organization.dao.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.organization.mapper.HrmsRecruitmentFeishuTemplateConfigMapper;
import com.imile.hrms.dao.organization.model.HrmsRecruitmentFeishuTemplateConfig;
import com.imile.hrms.dao.organization.dao.HrmsRecruitmentFeishuTemplateConfigDao;
/**
 * <AUTHOR>
 * @since 2025/1/10
 */

@Service
public class HrmsRecruitmentFeishuTemplateConfigDaoImpl extends ServiceImpl<HrmsRecruitmentFeishuTemplateConfigMapper, HrmsRecruitmentFeishuTemplateConfig> implements HrmsRecruitmentFeishuTemplateConfigDao{

}
