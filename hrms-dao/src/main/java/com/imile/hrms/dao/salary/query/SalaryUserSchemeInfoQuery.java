package com.imile.hrms.dao.salary.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/27 17:51
 * @version: 1.0
 */
@Data
public class SalaryUserSchemeInfoQuery {

    private String country;

    private List<String> countryList;

    private List<Long> deptIdList;

    private Long postId;

    private List<String> employeeTypeList;

    private String userNameOrCode;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 是否多国发薪
     */
    private Integer isSalaryMultinational;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 离职开始时间
     */
    private Date dimissionStartDate;

    /**
     * 离职结束时间
     */
    private Date dimissionEndDate;

    /**
     * 页面(待定薪，在职，冻结/离职)(WAIT_SALARY,ON_JOB,DIMISSION)
     */
    private String pageType;

    /**
     * 特殊查询条件:计薪方案发生变动用户查询
     */
    private Integer isSchemeChange;

    /**
     * 特殊查询条件:用户常驻国发生变动用户查询
     */
    private Integer isUserCountryChange;

    /**
     * 消息提示类型，配合isSchemeChange&isUserCountryChange使用
     */
    private List<String> messageKeyList;

    /**
     * 消息提示类型，配合isSchemeChange&isUserCountryChange使用
     */
    private String status;

    /**
     * 消息提示类型，配合isSchemeChange&isUserCountryChange使用
     */
    private String workStatus;

    /**
     * 是否薪资配置
     * 消息提示类型，配合isSchemeChange&isUserCountryChange使用
     */
    private Integer isSalaryConfig;

    /**
     * 记薪方案id列表（在职、离职）
     */
    private List<Long> schemeConfigIdList;

    /**
     * 供应商id列表
     */
    private List<Long> vendorIdList;
}