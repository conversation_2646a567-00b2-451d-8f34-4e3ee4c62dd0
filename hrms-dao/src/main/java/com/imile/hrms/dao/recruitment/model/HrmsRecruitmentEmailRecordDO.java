package com.imile.hrms.dao.recruitment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/6/21
 * 招聘邮件发送记录
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "hrms_recruitment_email_record")
public class HrmsRecruitmentEmailRecordDO extends BaseDO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 邮件类型（OFFER_LETTER）
     */
    @TableField(value = "email_type")
    private String emailType;

    /**
     * 业务表主键
     */
    @TableField(value = "biz_id")
    private Long bizId;

    /**
     * 模版id
     */
    @TableField(value = "template_id")
    private Long templateId;

    /**
     * 收件邮箱
     */
    @TableField(value = "receiver_email_addresses")
    private String receiverEmailAddresses;

    /**
     * 抄送邮箱
     */
    @TableField(value = "cc_email_addresses")
    private String ccEmailAddresses;

    /**
     * 邮件主题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 邮件正文
     */
    @TableField(value = "content")
    private String content;

    /**
     * 附件
     */
    @TableField(value = "file_urls")
    private String fileUrls;

    /**
     * 发送状态（NOT_SEND: 未发送；SEND_SUCCESS: 发送成功; SEND_FAIL: 发送失败）
     */
    @TableField(value = "send_status")
    private String sendStatus;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;
}