package com.imile.hrms.dao.punch.query;

import lombok.Data;

import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} WarehouseAttendanceDayByClassIdQuery
 * {@code @since:} 2024-09-05 11:46
 * {@code @description:}
 */
@Data
public class WarehouseAttendanceDayByClassIdQuery {
    /**
     * 针对仓内外包人员：必填班次id
     */
    private Long classId;
    /**
     * 当前时间：必填，当地时间
     */
    private Date now;
}
