package com.imile.hrms.dao.organization.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PostConditionBuilder {

    /**
     * 岗位ID
     */
    private Long id;

    /**
     * 岗位ID列表
     */
    private List<Long> idList;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位状态
     */
    private String status;

    /**
     * 族id列表
     */
    private List<Long> familyIdList;

    /**
     * 类id列表
     */
    private List<Long> categoryIdList;

    /**
     * 子类id列表
     */
    private List<Long> subCategoryIdList;
}
