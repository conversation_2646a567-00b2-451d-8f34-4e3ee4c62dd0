package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryFrameConfigDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryFrameConfigMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryFrameConfigDO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.salary.query.HrmsSalaryFrameConfigListQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 薪资框架配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Service
public class HrmsSalaryFrameConfigDaoImpl extends ServiceImpl<HrmsSalaryFrameConfigMapper, HrmsSalaryFrameConfigDO> implements HrmsSalaryFrameConfigDao {


    @Override
    public List<HrmsSalaryFrameConfigDO> listByQuery(HrmsSalaryFrameConfigListQuery query) {
        LambdaQueryWrapper<HrmsSalaryFrameConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsSalaryFrameConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(query.getIdList())) {
            queryWrapper.in(HrmsSalaryFrameConfigDO::getId, query.getIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(HrmsSalaryFrameConfigDO::getPaymentCountry, query.getCountryList());
        }
        if (query.getGradeId() != null) {
            queryWrapper.eq(HrmsSalaryFrameConfigDO::getGradeId, query.getGradeId());
        }
        queryWrapper.orderByDesc(HrmsSalaryFrameConfigDO::getLastUpdDate);
        queryWrapper.orderByDesc(HrmsSalaryFrameConfigDO::getPaymentCountry);
        queryWrapper.orderByAsc(HrmsSalaryFrameConfigDO::getGradeId);
        queryWrapper.orderByAsc(HrmsSalaryFrameConfigDO::getJobLevel);
        return list(queryWrapper);
    }
}
