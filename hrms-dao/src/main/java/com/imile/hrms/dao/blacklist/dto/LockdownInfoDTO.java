package com.imile.hrms.dao.blacklist.dto;

import com.imile.hrms.dao.user.dto.UserCertificateInfoParamDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName LockdownInfoDTO
 * <AUTHOR>
 * @Date 2023/5/17 20:43
 */
@Data
public class LockdownInfoDTO {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String userCode;


    /**
     * 黑名单截止日期
     */
    private Date endDate;

    /**
     * 封禁理由
     */
    private String reason;

    /**
     * 封禁状态，1封禁中、2封禁结束、3封禁处理中
     */
    private Integer banStatus;

    /**
     * 操作用户编码
     */
    private String recordUserCode;

    /**
     * 操作用户
     */
    private String recordUserName;

    /**
     * 证件类型
     */
    private String certificateTypeCode;
    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 证件名称
     */
    private String certificateName;


    /**
     * 用户名字
     */
    private String userName;

    private List<UserCertificateInfoParamDTO> certificateList;

    /**
     * 电话号码
     */
    private String callingPhone;
}
