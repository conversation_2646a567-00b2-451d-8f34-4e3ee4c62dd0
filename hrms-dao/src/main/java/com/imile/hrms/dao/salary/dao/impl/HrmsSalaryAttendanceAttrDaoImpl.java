package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryAttendanceAttrDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryAttendanceAttrMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryAttendanceAttrDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 薪资用户具体考勤薪资明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Service
public class HrmsSalaryAttendanceAttrDaoImpl extends ServiceImpl<HrmsSalaryAttendanceAttrMapper, HrmsSalaryAttendanceAttrDO> implements HrmsSalaryAttendanceAttrDao {

    @Override
    public List<HrmsSalaryAttendanceAttrDO> selectBySalaryAttendanceIdList(List<Long> salaryAttendanceUserInfoIdList) {
        if (CollectionUtils.isEmpty(salaryAttendanceUserInfoIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsSalaryAttendanceAttrDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryAttendanceAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsSalaryAttendanceAttrDO::getSalaryAttendanceUserInfoId, salaryAttendanceUserInfoIdList);
        return list(queryWrapper);
    }
}
