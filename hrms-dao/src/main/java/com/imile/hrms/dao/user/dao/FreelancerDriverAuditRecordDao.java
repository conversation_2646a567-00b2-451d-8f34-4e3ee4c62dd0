package com.imile.hrms.dao.user.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.common.CommonCountDO;
import com.imile.hrms.dao.user.condition.FreelancerDriverAuditRecordCustomConditionBuilder;
import com.imile.hrms.dao.user.model.FreelancerDriverAuditRecordDO;

import java.util.List;

/**
 * <p>
 * 众包司机审核记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface FreelancerDriverAuditRecordDao extends IService<FreelancerDriverAuditRecordDO> {

    /**
     * 自定义查询
     *
     * @param builder FreelancerDriverAuditRecordConditionBuilder
     * @return List<FreelancerDriverAuditRecordPO>
     */
    List<FreelancerDriverAuditRecordDO> selectByCustomCondition(FreelancerDriverAuditRecordCustomConditionBuilder builder);

    /**
     * 根据审核状态分组统计
     *
     * @param ocDeptIdList 网点部门ID列表
     * @return List<CommonCountDO>
     */
    List<CommonCountDO> countGroupByAuditStatus(List<Long> ocDeptIdList);

    /**
     * 根据人员ID查询待完结审核记录
     *
     * @param userId 人员ID
     * @return FreelancerDriverAuditRecordDO
     */
    FreelancerDriverAuditRecordDO selectUnfinishedByUserId(Long userId);

    /**
     * 根据人员ID查询最新审核记录
     *
     * @param userId 人员ID
     * @return FreelancerDriverAuditRecordDO
     */
    FreelancerDriverAuditRecordDO selectLatestByUserId(Long userId);
}
