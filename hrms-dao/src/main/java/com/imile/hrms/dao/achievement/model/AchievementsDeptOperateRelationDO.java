package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 绩效运营组织和行政组织关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("achievements_dept_operate_relation")
public class AchievementsDeptOperateRelationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 组织id
     */
    private Long deptId;

    /**
     * 组织编码
     */
    private String deptCode;

    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 网点编码
     */
    private String ocCode;


}
