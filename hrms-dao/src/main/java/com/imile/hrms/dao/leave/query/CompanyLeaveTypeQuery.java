package com.imile.hrms.dao.leave.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * {@code @author:} han.wang
 * {@code @className:} CompanyLeaveTypeQuery
 * {@code @since:} 2025-02-17 16:52
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CompanyLeaveTypeQuery extends BaseQuery implements Serializable {

    private static final long serialVersionUID = -818694672305139820L;

    /**
     * 国家
     */
    private String country;

    /**
     * 假期类型
     */
    private String leaveType;

}
