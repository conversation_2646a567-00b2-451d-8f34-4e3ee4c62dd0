package com.imile.hrms.dao.primary.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.primary.entity.UserCertificateDO;
import com.imile.hrms.dao.primary.entity.condition.UserCertificateConditionBuilder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 人员证件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Mapper
public interface UserCertificateMapper extends HrmsBaseMapper<UserCertificateDO> {

    /**
     * 根据证件列表查询
     *
     * @param certificateList 证件列表
     * @return List<UserCertificateDO>
     */
    List<UserCertificateDO> selectByCertificateList(@Param("certificateList") List<UserCertificateConditionBuilder.Certificate> certificateList);
}
