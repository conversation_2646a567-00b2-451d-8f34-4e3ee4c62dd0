package com.imile.hrms.dao.user.dao.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.user.dao.HrmsDriverLevelDao;
import com.imile.hrms.dao.user.mapper.HrmsDriverLevelMapper;
import com.imile.hrms.dao.user.model.HrmsDriverLevelDO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 司机等级表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-28
 */
@Service
public class HrmsDriverLevelDaoImpl extends ServiceImpl<HrmsDriverLevelMapper, HrmsDriverLevelDO> implements HrmsDriverLevelDao {

}
