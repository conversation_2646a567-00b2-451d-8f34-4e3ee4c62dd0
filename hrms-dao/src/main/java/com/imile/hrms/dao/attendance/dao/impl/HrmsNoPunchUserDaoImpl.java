package com.imile.hrms.dao.attendance.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.attendance.dao.HrmsNoPunchUserDao;
import com.imile.hrms.dao.attendance.mapper.HrmsNoPunchUserMapper;
import com.imile.hrms.dao.attendance.model.HrmsAttendancePunchStatisticsDO;
import com.imile.hrms.dao.attendance.model.HrmsNoPunchUserDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-19
 */
@Service
public class HrmsNoPunchUserDaoImpl extends ServiceImpl<HrmsNoPunchUserMapper, HrmsNoPunchUserDO> implements HrmsNoPunchUserDao {

    @Override
    public List<HrmsNoPunchUserDO> listByDate(List<Date> dates, String country) {
        if (CollectionUtils.isEmpty(dates)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsNoPunchUserDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsNoPunchUserDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(HrmsNoPunchUserDO::getDate, dates);
        if (country != null) {
            queryWrapper.eq(HrmsNoPunchUserDO::getCountry, country);
        }
        return this.list(queryWrapper);
    }
}
