package com.imile.hrms.dao.punch.query;

import lombok.Data;

import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} WarehouseAttendanceConfigQuery
 * {@code @since:} 2024-08-22 20:08
 * {@code @description:}
 */
@Data
public class WarehouseAttendanceConfigQuery {
    /**
     * 常国家：必填
     */
    private String locationCountry;
    /**
     * 用户id：必填
     */
    private Long userId;
    /**
     * 当前时间：当地时间，必填
     */
    private Date now;
}
