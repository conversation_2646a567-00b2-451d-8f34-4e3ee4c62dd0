package com.imile.hrms.dao.salary.query;

import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/17
 */
@Data
public class HrmsSalaryCalculateTaskPageQuery extends ResourceQuery {

    /**
     * id
     */
    private Long id;

    /**
     * ids
     */
    private List<Long> idList;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家列表
     */
    private List<String> countryList;

    /**
     * 结算时间-年月
     */
    private Long settlementDate;

}
