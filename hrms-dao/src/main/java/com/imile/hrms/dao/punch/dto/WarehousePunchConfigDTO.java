package com.imile.hrms.dao.punch.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * {@code @author:} allen
 * {@code @className:} WarehousePunchConfigDTO
 * {@code @since:} 2024-08-20 20:31
 * {@code @description:}
 */
@Data
public class WarehousePunchConfigDTO {
    /**
     * 打卡规则id
     */
    private Long punchConfigId;
    /**
     * 打卡规则类型：固定上下班、班次上下班、自由上下班
     */
    private String punchConfigType;
    /**
     * 打卡规则no
     */
    private String punchConfigNo;
    /**
     * 打卡规则名称
     */
    private String punchConfigName;


    /**
     * 班次id
     */
    private Long classId;
    /**
     * 班次名称
     */
    private String className;
    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 出勤时间（法定时长 + 休息时长）
     */
    private BigDecimal attendanceHours;

    /**
     * 法定时长
     */
    private BigDecimal legalWorkingHours;

    /**
     * dayPunchType：固定上下班、自由上下班取打卡规则名称，班次上下班取班次名称
     */
    private String dayPunchType;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross = 0;
}
