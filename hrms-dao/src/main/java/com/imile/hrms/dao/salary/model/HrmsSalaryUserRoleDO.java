package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户薪资权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_user_role")
public class HrmsSalaryUserRoleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否有数据权限
     */
    private Integer isAuth;

    /**
     * 适用国(全球为ALL)
     */
    private String country;


}
