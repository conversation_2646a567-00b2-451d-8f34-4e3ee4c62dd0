package com.imile.hrms.dao.base.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.base.dao.OperationSceneDao;
import com.imile.hrms.dao.base.mapper.OperationSceneMapper;
import com.imile.hrms.dao.base.model.OperationSceneDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/3
 */
@Service
public class OperationSceneDaoImpl extends ServiceImpl<OperationSceneMapper, OperationSceneDO> implements OperationSceneDao {
    @Override
    public List<OperationSceneDO> selectByOperationModuleCode(String operationModuleCode) {
        if (StringUtils.isBlank(operationModuleCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OperationSceneDO> queryWrapper = Wrappers.lambdaQuery(OperationSceneDO.class);
        queryWrapper.eq(OperationSceneDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(OperationSceneDO::getOperationModuleCode, operationModuleCode);
        return super.list(queryWrapper);
    }

    @Override
    public List<OperationSceneDO> selectByOperationSceneCode(List<String> operationSceneCodeList) {
        if (CollectionUtils.isEmpty(operationSceneCodeList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OperationSceneDO> queryWrapper = Wrappers.lambdaQuery(OperationSceneDO.class);
        queryWrapper.eq(OperationSceneDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(OperationSceneDO::getOperationSceneCode, operationSceneCodeList);
        return super.list(queryWrapper);
    }
}
