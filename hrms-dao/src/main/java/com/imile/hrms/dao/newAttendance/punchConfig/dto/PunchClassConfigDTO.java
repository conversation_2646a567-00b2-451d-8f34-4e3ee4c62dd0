package com.imile.hrms.dao.newAttendance.punchConfig.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@Data
public class PunchClassConfigDTO {

    /**
     * 班次id
     */
    private Long id;

    /**
     * classId
     */
    private Long classId;

    /**
     * dayID
     */
    private Long dayId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 出勤时长
     */
    private BigDecimal attendanceHours;

    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;

    /**
     * 打卡时间设置
     */
    private List<PunchClassItemConfigDTO> classItemConfigList;
}
