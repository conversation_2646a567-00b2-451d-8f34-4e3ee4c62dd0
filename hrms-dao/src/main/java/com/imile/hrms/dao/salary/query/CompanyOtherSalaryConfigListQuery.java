package com.imile.hrms.dao.salary.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;

import java.util.List;

@Data
public class CompanyOtherSalaryConfigListQuery extends BaseQuery {
    private static final long serialVersionUID = -2977607086023386729L;

    /**
     * 适用国家列表
     */
    private List<String> countryList;

    /**
     * 状态
     */
    private String status;

    /**
     * 类型
     */
    private String type;

}
