package com.imile.hrms.dao.face.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.face.dao.FaceFeatureDao;
import com.imile.hrms.dao.face.mapper.FaceFeatureMapper;
import com.imile.hrms.dao.face.model.FaceFeatureDO;
import com.imile.hrms.dao.face.query.FaceFeatureQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Service
public class FaceFeatureDaoImpl extends ServiceImpl<FaceFeatureMapper, FaceFeatureDO> implements FaceFeatureDao {


    @Override
    public FaceFeatureDO getByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        LambdaQueryWrapper<FaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaceFeatureDO::getUserCode, userCode);
        queryWrapper.eq(FaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return super.getOne(queryWrapper);
    }

    @Override
    public List<FaceFeatureDO> getByUserCodes(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FaceFeatureDO::getUserCode, userCodeList);
        queryWrapper.eq(FaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public List<FaceFeatureDO> selectAll() {
        LambdaQueryWrapper<FaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(FaceFeatureDO::getUserCode, FaceFeatureDO::getFeatureData);
        queryWrapper.eq(FaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public int deleteByUserCode(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return 0;
        }
        FaceFeatureDO faceFeatureDO = getByUserCode(userCode);
        if (Objects.isNull(faceFeatureDO)) {
            return 0;
        }
        faceFeatureDO.setIsDelete(IsDeleteEnum.YES.getCode());
        return baseMapper.updateById(faceFeatureDO);
    }

    @Override
    public List<FaceFeatureDO> selectByCondition(FaceFeatureQuery query) {
        LambdaQueryWrapper<FaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getUserCodeList()),FaceFeatureDO::getUserCode,query.getUserCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getCountryList()),FaceFeatureDO::getCountry,query.getCountryList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getEmployeeTypeList()),FaceFeatureDO::getEmployeeType,query.getEmployeeTypeList());
        queryWrapper.eq(FaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }
}
