package com.imile.hrms.dao.attendance.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.attendance.dao.HrmsAttendanceCycleConfigDao;
import com.imile.hrms.dao.attendance.mapper.HrmsAttendanceCycleConfigMapper;
import com.imile.hrms.dao.attendance.model.HrmsAttendanceCycleConfigDO;
import com.imile.hrms.dao.attendance.query.AttendanceCycleConfigPageQuery;
import com.imile.hrms.dao.attendance.query.AttendanceCycleConfigQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsAttendanceCycleConfigDaoImpl
 * {@code @since:} 2024-10-30 17:42
 * {@code @description:}
 */
@Service
@Slf4j
public class HrmsAttendanceCycleConfigDaoImpl extends ServiceImpl<HrmsAttendanceCycleConfigMapper, HrmsAttendanceCycleConfigDO> implements HrmsAttendanceCycleConfigDao {

    @Override
    public List<HrmsAttendanceCycleConfigDO> selectByCondition(AttendanceCycleConfigQuery query) {

        if (ObjectUtil.isNull(query) || ObjectUtil.isEmpty(query.getCountry()) || ObjectUtil.isEmpty(query.getCycleType())) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<HrmsAttendanceCycleConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(ObjectUtil.isNotEmpty(query.getCountry()), HrmsAttendanceCycleConfigDO::getCountry, query.getCountry());
        lambdaQuery.eq(ObjectUtil.isNotNull(query.getCycleType()), HrmsAttendanceCycleConfigDO::getCycleType, query.getCycleType());
        lambdaQuery.eq(HrmsAttendanceCycleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(lambdaQuery);
    }

    @Override
    @Transactional
    public void saveAndUpdateAttendanceCycleConfig(HrmsAttendanceCycleConfigDO oldAttendanceCycleConfig, HrmsAttendanceCycleConfigDO model) {
        if (ObjectUtil.isNotNull(oldAttendanceCycleConfig)) {
            updateById(oldAttendanceCycleConfig);
        }
        if (ObjectUtil.isNotNull(model)) {
            save(model);
        }
    }

    @Override
    public List<HrmsAttendanceCycleConfigDO> selectListByCondition(AttendanceCycleConfigPageQuery query) {

        if (ObjectUtil.isNull(query)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<HrmsAttendanceCycleConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(HrmsAttendanceCycleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        lambdaQuery.eq(ObjectUtil.isNotEmpty(query.getCountry()), HrmsAttendanceCycleConfigDO::getCountry, query.getCountry());
        lambdaQuery.in(CollUtil.isNotEmpty(query.getCountryList()), HrmsAttendanceCycleConfigDO::getCountry, query.getCountryList());
        lambdaQuery.eq(ObjectUtil.isNotEmpty(query.getStatus()), HrmsAttendanceCycleConfigDO::getStatus, query.getStatus());
        return list(lambdaQuery);
    }
}
