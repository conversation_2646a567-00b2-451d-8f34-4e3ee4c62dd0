package com.imile.hrms.dao.user.mapper;

import com.imile.hrms.dao.common.HrmsBaseMapper;
import com.imile.hrms.dao.user.dto.UserExtendAttrDTO;
import com.imile.hrms.dao.user.model.UserExtendAttrDO;
import com.imile.hrms.dao.user.query.UserExtendAttrQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 人员扩展属性表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
public interface UserExtendAttrMapper extends HrmsBaseMapper<UserExtendAttrDO> {

    List<UserExtendAttrDTO> selectByAttrKeyCondition(UserExtendAttrQuery query);

}
