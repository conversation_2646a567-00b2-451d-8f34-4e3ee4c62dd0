package com.imile.hrms.dao.user.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseOptionDTO {
    /**
     * 一般是对象的id
     */
    private String key;
    /**
     * 一般是对象的编码
     */
    private String value;
    /**
     * 一般是对象的名称
     */
    private String label;
}
