package com.imile.hrms.dao.newAttendance.punchConfig.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/14
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PunchClassQuery extends ResourceQuery {


    private String country;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 是否有部门权限
     */
    private Boolean hasDeptPermission;

    /**
     * 是否有国家权限
     */
    private Boolean hasCountryPermission;
    /**
     * 是否有与国家、部门权限
     */
    private Boolean hasAndDeptAndCountryPermission;

    /**
     * 是否有或国家、部门权限
     */
    private Boolean hasOrDeptAndCountryPermission;

    /**
     * 姓名或邮箱
     */
    private String userNameOrEmail;

    /**
     * 账号或姓名
     */
    private String userCodeOrName;
    /**
     * 姓名
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;
    /**
     * 工号
     */
    private String workNo;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 部门名称
     */
    private Long deptId;

    /**
     * 打卡规则ID
     */
    private Long punchConfigId;

    /**
     * 打卡规则No
     */
    private String punchConfigNo;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 岗位集合
     */
    private List<Long> postIdList;

    /**
     * 性质集合
     */
    private List<String> employeeTypeList;

    /**
     * 供应商编码
     */
    private List<String> vendorCodeList;

    /**
     * 查询起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;


    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 前端不需要传该参数
     */
    private List<Long> deptIds;


    /**
     * 前端不需要传递该参数
     * 查询出符合规则的userId
     */
    private Collection<Long> effectUserIds;

    /**
     * 前端不需要传递该参数
     */
    private Long userId;

    /**
     * 开始时间 前端不需要传递该参数
     */
    private Long startDayId;

    /**
     * 打卡规则Id集合
     */
    private List<Long> punchConfigIdList;

    /**
     * 是否需要打卡 0:false,1:true
     */
    private Integer isNeedPunch;

    private Integer driver;

    /**
     * 考勤日历编码
     */
    private String calendarConfigNo;

    /**
     * 选中导出用
     */
    private List<String> userIds;

    /**
     * 前端是否选择了部门
     */
    private Boolean isChooseDept;
}
