package com.imile.hrms.dao.primary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.primary.dao.UserDao;
import com.imile.hrms.dao.primary.entity.UserDO;
import com.imile.hrms.dao.primary.entity.condition.UserConditionBuilder;
import com.imile.hrms.dao.primary.mapper.UserMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
public class UserDaoImpl extends ServiceImpl<UserMapper, UserDO> implements UserDao {

    @Override
    public List<UserDO> selectByCondition(UserConditionBuilder builder) {
        LambdaQueryWrapper<UserDO> queryWrapper = Wrappers.lambdaQuery(UserDO.class);
        queryWrapper.eq(UserDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(builder.getIdList())) {
            queryWrapper.in(UserDO::getId, builder.getIdList());
        }
        if (StringUtils.isNotBlank(builder.getUserCode())) {
            queryWrapper.eq(UserDO::getUserCode, builder.getUserCode());
        }
        if (CollectionUtils.isNotEmpty(builder.getUserCodeList())) {
            queryWrapper.in(UserDO::getUserCode, builder.getUserCodeList());
        }
        if (StringUtils.isNotBlank(builder.getUserName())) {
            queryWrapper.and(s -> s.like(UserDO::getUserName, builder.getUserName())
                    .or(i -> i.like(UserDO::getUserNameEn, builder.getUserName())));
        }
        if (StringUtils.isNotBlank(builder.getEmployeeType())) {
            queryWrapper.eq(UserDO::getEmployeeType, builder.getEmployeeType());
        }
        if (Objects.nonNull(builder.getIsDriver())) {
            queryWrapper.eq(UserDO::getIsDriver, builder.getIsDriver());
        }
        if (Objects.nonNull(builder.getPostId())) {
            queryWrapper.eq(UserDO::getPostId, builder.getPostId());
        }
        if (Objects.nonNull(builder.getDeptId())) {
            queryWrapper.eq(UserDO::getDeptId, builder.getDeptId());
        }
        if (Objects.nonNull(builder.getOcId())) {
            queryWrapper.eq(UserDO::getOcId, builder.getOcId());
        }
        if (StringUtils.isNotBlank(builder.getOcCode())) {
            queryWrapper.eq(UserDO::getOcCode, builder.getOcCode());
        }
        if (CollectionUtils.isNotEmpty(builder.getOcCodeList())) {
            queryWrapper.in(UserDO::getOcCode, builder.getOcCodeList());
        }
        if (StringUtils.isNotBlank(builder.getOriginCountry())) {
            queryWrapper.eq(UserDO::getOriginCountry, builder.getOriginCountry());
        }
        if (StringUtils.isNotBlank(builder.getSettlementCenterCode())) {
            queryWrapper.eq(UserDO::getSettlementCenterCode, builder.getSettlementCenterCode());
        }
        if (StringUtils.isNotBlank(builder.getVendorCode())) {
            queryWrapper.eq(UserDO::getVendorCode, builder.getVendorCode());
        }
        if (StringUtils.isNotBlank(builder.getStatus())) {
            queryWrapper.eq(UserDO::getStatus, builder.getStatus());
        }
        if (StringUtils.isNotBlank(builder.getDataSource())) {
            queryWrapper.eq(UserDO::getDataSource, builder.getDataSource());
        }
        queryWrapper.last("limit " + BusinessConstant.THOUSAND);
        return super.list(queryWrapper);
    }

    @Override
    public UserDO selectByUserCode(String userCode) {
        LambdaQueryWrapper<UserDO> queryWrapper = Wrappers.lambdaQuery(UserDO.class);
        queryWrapper.eq(UserDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserDO::getUserCode, userCode);
        return super.getOne(queryWrapper);
    }

    @Override
    public List<UserDO> selectProfilePhotoUrlIsNotNull() {
        LambdaQueryWrapper<UserDO> queryWrapper = Wrappers.lambdaQuery(UserDO.class);
        queryWrapper.select(UserDO::getId,UserDO::getUserCode,UserDO::getVendorId,UserDO::getProfilePhotoUrl);
        queryWrapper.eq(UserDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.isNotNull(UserDO::getProfilePhotoUrl);
        return super.list(queryWrapper);
    }
}
