package com.imile.hrms.dao.user.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.dao.user.dao.HrmsCountryConfigDao;

import com.imile.hrms.dao.user.mapper.HrmsCountryConfigMapper;
import com.imile.hrms.dao.user.model.HrmsCountryConfigDO;
import com.imile.hrms.dao.user.query.CountryConfigQuery;
import com.imile.hrms.dao.user.query.CountryQuery;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 国家配置表 国家配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Service
public class HrmsCountryConfigDaoImpl extends ServiceImpl<HrmsCountryConfigMapper, HrmsCountryConfigDO> implements HrmsCountryConfigDao {

    @Override
    public List<HrmsCountryConfigDO> listByQuery(CountryConfigQuery query) {
        LambdaQueryWrapper<HrmsCountryConfigDO> queryWrapper = Wrappers.lambdaQuery(HrmsCountryConfigDO.class);
        queryWrapper.eq(HrmsCountryConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(query.getIdList())) {
            queryWrapper.in(HrmsCountryConfigDO::getId, query.getIdList());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(HrmsCountryConfigDO::getStatus, query.getStatus());
        }

        return list(queryWrapper);
    }

    @Override
    public List<HrmsCountryConfigDO> getCountryConfig(CountryQuery countryQuery) {
        LambdaQueryWrapper<HrmsCountryConfigDO> queryWrapper = Wrappers.lambdaQuery(HrmsCountryConfigDO.class);
        queryWrapper.eq(HrmsCountryConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsCountryConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        if (!StringUtil.isEmpty(countryQuery.getCountryCode())) {
            queryWrapper.eq(HrmsCountryConfigDO::getShortName, countryQuery.getCountryCode());
        }
        if (CollectionUtils.isNotEmpty(countryQuery.getCountryCodeList())) {
            queryWrapper.in(HrmsCountryConfigDO::getShortName, countryQuery.getCountryCodeList());
        }
        if (!StringUtil.isEmpty(countryQuery.getCountryCallingCode())) {
            queryWrapper.eq(HrmsCountryConfigDO::getCountryCallingCode, countryQuery.getCountryCallingCode());
        }
        if (!CollUtil.isEmpty(countryQuery.getCountryCodes())) {
            queryWrapper.in(HrmsCountryConfigDO::getShortName, countryQuery.getCountryCodes());
        }
        if (StringUtils.isNotEmpty(countryQuery.getCountryName())) {
            // AND ( country_name_cn = ? OR country_name_en  = ?)
            queryWrapper.and(param ->
                    param.eq(HrmsCountryConfigDO::getCountryNameCn, countryQuery.getCountryName())
                            .or(i -> i.eq(HrmsCountryConfigDO::getCountryNameEn, countryQuery.getCountryName())));
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<HrmsCountryConfigDO> getCountryConfig(String countryName) {
        return this.getCountryConfig(CountryQuery.builder().countryName(countryName).build());
    }

    @Override
    public List<HrmsCountryConfigDO> selectCountryConfigByNameEn(List<String> countryNameList) {
        if (CollectionUtils.isEmpty(countryNameList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<HrmsCountryConfigDO> queryWrapper = Wrappers.lambdaQuery(HrmsCountryConfigDO.class);
        queryWrapper.eq(HrmsCountryConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsCountryConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.in(HrmsCountryConfigDO::getCountryNameEn, countryNameList);
        return this.list(queryWrapper);
    }
}
