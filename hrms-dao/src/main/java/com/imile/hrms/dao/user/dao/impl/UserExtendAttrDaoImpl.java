package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserExtendAttrDao;
import com.imile.hrms.dao.user.dto.UserExtendAttrDTO;
import com.imile.hrms.dao.user.mapper.UserExtendAttrMapper;
import com.imile.hrms.dao.user.model.UserExtendAttrDO;
import com.imile.hrms.dao.user.query.UserExtendAttrQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22
 */
@Service
public class UserExtendAttrDaoImpl extends ServiceImpl<UserExtendAttrMapper, UserExtendAttrDO> implements UserExtendAttrDao {
    @Override
    public List<UserExtendAttrDO> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserExtendAttrDO> queryWrapper = Wrappers.lambdaQuery(UserExtendAttrDO.class);
        queryWrapper.eq(UserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserExtendAttrDO::getUserId, userId);
        return super.list(queryWrapper);
    }

    @Override
    public UserExtendAttrDO selectByUserIdAndAttrKey(Long userId, String attrKey) {
        LambdaQueryWrapper<UserExtendAttrDO> queryWrapper = Wrappers.lambdaQuery(UserExtendAttrDO.class);
        queryWrapper.eq(UserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserExtendAttrDO::getUserId, userId);
        queryWrapper.eq(UserExtendAttrDO::getAttrKey, attrKey);
        return super.getOne(queryWrapper);
    }

    @Override
    public List<UserExtendAttrDO> selectByUserIdAndAttrKey(List<Long> userIdList, List<String> attrKeyList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserExtendAttrDO> queryWrapper = Wrappers.lambdaQuery(UserExtendAttrDO.class);
        queryWrapper.eq(UserExtendAttrDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(UserExtendAttrDO::getUserId, userIdList);
        queryWrapper.in(UserExtendAttrDO::getAttrKey, attrKeyList);
        return super.list(queryWrapper);
    }

    @Override
    public List<UserExtendAttrDTO> selectByAttrKeyCondition(UserExtendAttrQuery query) {
        return this.baseMapper.selectByAttrKeyCondition(query);
    }
}
