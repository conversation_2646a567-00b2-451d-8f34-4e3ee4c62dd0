package com.imile.hrms.dao.training.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.training.model.HrmsUserTrainingStatisticalDO;
import com.imile.hrms.dao.training.query.UserTrainingStatisticalQuery;

import java.util.List;

/**
 * <p>
 * 员工培训次数统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
public interface HrmsUserTrainingStatisticalDao extends IService<HrmsUserTrainingStatisticalDO> {

    /**
     * 条件查询
     */
    List<HrmsUserTrainingStatisticalDO> getStatisticalList(UserTrainingStatisticalQuery query);

}
