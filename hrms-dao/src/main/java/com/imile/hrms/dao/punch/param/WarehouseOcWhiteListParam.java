package com.imile.hrms.dao.punch.param;

import lombok.Data;

import java.util.List;


/**
 * 仓内考勤网点白名单配置表 参数
 *
 * <AUTHOR>
 * @since 2024/8/12
 */
@Data
public class WarehouseOcWhiteListParam{

    /**
     * userIdList
     */
    private List<Long> userIdList;

    /**
     * userId
     */
    private Long userId;

    /**
     * 是否超级管理员
     */
    private Integer superAdmin;

    /**
     * 创建人编码
     */
    private String createUserCode;
}
