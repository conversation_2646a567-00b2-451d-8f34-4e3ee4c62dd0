package com.imile.hrms.dao.achievement.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 组织目标明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
public class TargetItemBoardListDTO {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 目标id
     */
    private Long targetId;

    /**
     * 完成值
     */
    private BigDecimal completionValue;

    /**
     * kpi分类
     */
    private String kpiType;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 部门负责人
     */
    private String leaderName;

    /**
     * 组织名称
     */
    private String deptName;


    /**
     * 计量单位
     */
    private String unit;

    /**
     * 是否可查看
     */
    private Boolean isLook;

    /**
     * 可见性
     */
    private String visibility;

    /**
     * 对齐目标
     */
    private String parentTargetId;

    /**
     * 对齐目标名称
     */
    private String parentTargetName;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 基线值
     */
    private BigDecimal baseValue;

    /**
     * 目标值
     */
    private BigDecimal targetValue;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标名称
     */
    private String targetNameEn;

    /**
     * 挑战值
     */
    private BigDecimal challengeValue;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 子节点数量
     */
    private Integer childCount;

    /**
     * 扩展信息
     */
    private String extend;

    private Integer isLatest;


}
