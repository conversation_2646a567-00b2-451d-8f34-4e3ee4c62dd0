package com.imile.hrms.dao.salary.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementApprovalItemInfoDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementApprovalItemInfoQuery;

import java.util.List;

/**
 * <p>
 * 薪资结算申请单结算人员科目具体信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-09
 */
public interface HrmsSalarySettlementApprovalItemInfoDao extends IService<HrmsSalarySettlementApprovalItemInfoDO> {

    List<HrmsSalarySettlementApprovalItemInfoDO> listByQuery(HrmsSalarySettlementApprovalItemInfoQuery query);

}
