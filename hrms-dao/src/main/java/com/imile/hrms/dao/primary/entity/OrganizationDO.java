package com.imile.hrms.dao.primary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 组织表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("base_organization")
public class OrganizationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 组织别名
     */
    private String organizationAlias;

    /**
     * 组织类型（详见数据字典OrganizationType）
     */
    private String organizationType;

    /**
     * 负责人人员编码
     */
    private String leaderUserCode;

    /**
     * 组织状态（ACTIVE:生效 DISABLED:失效）
     */
    private String organizationStatus;

    /**
     * 国家
     */
    private String organizationCountry;

    /**
     * 省份
     */
    private String organizationProvince;

    /**
     * 城市
     */
    private String organizationCity;

    /**
     * 区
     */
    private String organizationDistrict;

    /**
     * 详细地址
     */
    private String organizationAddress;


}
