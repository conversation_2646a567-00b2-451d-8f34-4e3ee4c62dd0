package com.imile.hrms.dao.salary.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalaryPaymentCountryConfigDO;
import com.imile.hrms.dao.salary.query.SalaryPaymentCountryQuery;

import java.util.List;

/**
 * <p>
 * 计薪国配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public interface HrmsSalaryPaymentCountryConfigDao extends IService<HrmsSalaryPaymentCountryConfigDO> {

    List<HrmsSalaryPaymentCountryConfigDO> selectPaymentCountryConfigList(SalaryPaymentCountryQuery query);
}
