package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户教育经历信息表 用户教育经历信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_education_info")
public class HrmsUserEducationInfoDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 学历 本科/中专...
     */
    private String education;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 专业
     */
    private String major;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 是否取得学位证（0:否 1:是）
     */
    @Deprecated
    private Integer isDegree;

    /**
     * 是否全日制（0:否 1:是）
     */
    private Integer isFullTime;

    /**
     * 学历证书路径
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String educationPath;

    /**
     * 学位证书路径
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String degreePath;

    /**
     * 其他文件
     */
    private String otherFiles;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否启用
     */
    private String status;


    /**
     * 排序
     */
    private BigDecimal orderby;


}
