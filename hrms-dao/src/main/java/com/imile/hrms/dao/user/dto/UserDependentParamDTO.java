package com.imile.hrms.dao.user.dto;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Data
public class UserDependentParamDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 依赖人类型（1:配偶 2:伴侣 3:孩子）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer dependentType;

    /**
     * 税号
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String taxId;

    /**
     * 证明文件列表
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<AttachmentParamDTO> proveFileList;
}
