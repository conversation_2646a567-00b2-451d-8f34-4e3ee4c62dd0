package com.imile.hrms.dao.salary.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 记薪方案关联员工信息条件查询
 *
 * <AUTHOR>
 * @since 2023/12/19
 */
@Data
@Builder
public class HrmsSalaryUserSchemeInfoQuery {

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工id列表
     */
    private List<Long> userIdList;

    /**
     * 计薪方案配置ID
     */
    private Long schemeConfigId;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 计薪方案配置编码
     */
    private List<String> schemeConfigNoList;

    /**
     * 计薪国
     */
    private String country;


}
