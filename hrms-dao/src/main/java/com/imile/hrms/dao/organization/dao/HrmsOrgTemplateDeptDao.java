package com.imile.hrms.dao.organization.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.organization.model.HrmsOrgTemplateDeptDO;

import java.util.List;

/**
 * <p>
 * 组织模板部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-01
 */
public interface HrmsOrgTemplateDeptDao extends IService<HrmsOrgTemplateDeptDO> {

    /**
     * 根据组织模板ID查询
     *
     * @param templateId 组织模板ID
     * @return List<HrmsOrgTemplateDeptDO>
     */
    List<HrmsOrgTemplateDeptDO> selectByTemplateId(Long templateId);

    List<HrmsOrgTemplateDeptDO> getByIdList(List<Long> idList);
}
