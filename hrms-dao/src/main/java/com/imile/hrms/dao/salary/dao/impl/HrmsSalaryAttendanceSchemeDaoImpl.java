package com.imile.hrms.dao.salary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalaryAttendanceSchemeDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalaryAttendanceSchemeMapper;
import com.imile.hrms.dao.salary.model.HrmsSalaryAttendanceSchemeDO;
import com.imile.hrms.dao.salary.query.SalaryAttendanceSchemeQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/12/11 17:02
 * @version: 1.0
 */
@Service
public class HrmsSalaryAttendanceSchemeDaoImpl extends ServiceImpl<HrmsSalaryAttendanceSchemeMapper, HrmsSalaryAttendanceSchemeDO> implements HrmsSalaryAttendanceSchemeDao {
    @Override
    public List<HrmsSalaryAttendanceSchemeDO> selectAttendanceScheme(SalaryAttendanceSchemeQuery query) {
        LambdaQueryWrapper<HrmsSalaryAttendanceSchemeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalaryAttendanceSchemeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (query.getSettlementDate() != null) {
            queryWrapper.eq(HrmsSalaryAttendanceSchemeDO::getSettlementDate, query.getSettlementDate());
        }
        if (query.getStartSettlementDate() != null) {
            queryWrapper.ge(HrmsSalaryAttendanceSchemeDO::getSettlementDate, query.getStartSettlementDate());
        }
        if (CollectionUtils.isNotEmpty(query.getSettlementDateList())) {
            queryWrapper.in(HrmsSalaryAttendanceSchemeDO::getSettlementDate, query.getSettlementDateList());
        }
        if (StringUtils.isNotBlank(query.getApplyCountry())) {
            queryWrapper.eq(HrmsSalaryAttendanceSchemeDO::getApplyCountry, query.getApplyCountry());
        }
        if (StringUtils.isNotBlank(query.getSalarySchemeConfigNo())) {
            queryWrapper.eq(HrmsSalaryAttendanceSchemeDO::getSalarySchemeConfigNo, query.getSalarySchemeConfigNo());
        }
        return list(queryWrapper);
    }
}
