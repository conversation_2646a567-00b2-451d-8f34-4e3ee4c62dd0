package com.imile.hrms.dao.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.hrms.dao.organization.dto.EntPostDTO;
import com.imile.hrms.dao.organization.model.HrmsEntPostDO;
import com.imile.hrms.dao.organization.query.PostQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Mapper
@Repository
public interface HrmsEntPostMapper extends BaseMapper<HrmsEntPostDO> {
    List<EntPostDTO> listPost(PostQuery query);

    /**
     * 只匹配英文名
     * @param query
     * @return
     */
    List<EntPostDTO> listPostV2(PostQuery query);

    List<HrmsEntPostDO> getPostDO(PostQuery query);
}
