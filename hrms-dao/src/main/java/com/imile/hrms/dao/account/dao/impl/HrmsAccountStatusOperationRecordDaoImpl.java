package com.imile.hrms.dao.account.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.account.dao.HrmsAccountStatusOperationRecordDao;
import com.imile.hrms.dao.account.mapper.HrmsAccountStatusOperationRecordMapper;
import com.imile.hrms.dao.account.model.HrmsAccountStatusOperationRecordDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 账号停用启用操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Service
public class HrmsAccountStatusOperationRecordDaoImpl extends ServiceImpl<HrmsAccountStatusOperationRecordMapper, HrmsAccountStatusOperationRecordDO> implements HrmsAccountStatusOperationRecordDao {

    @Override
    public List<HrmsAccountStatusOperationRecordDO> listMaxUpdateTimeByUserCode(List<String> userCodeList) {
        return getBaseMapper().listMaxUpdateTimeByUserCode(userCodeList);
    }
}
