package com.imile.hrms.dao.punch.dto;

import lombok.Data;

/**
 * <p>
 * 打卡后的状态
 * </p>
 *
 * <AUTHOR>
 * @menu
 * @since 2022/2/12
 */
@Data
public class PunchResultDTO {
    /**
     * 早退
     */
    private Integer early = 0;
    /**
     * 迟到
     */
    private Integer late = 0;
    /**
     * 缺勤
     */
    private Integer absence = 0;

    /**
     * 缺卡
     */
    private Integer missingCard = 0;

    /**
     * 外勤
     */
    private Integer outSide = 0;

    /**
     * 上班卡外勤
     */
    private Integer outSideIn;

    /**
     * 下班卡外勤
     */
    private Integer outSideOut;
}
