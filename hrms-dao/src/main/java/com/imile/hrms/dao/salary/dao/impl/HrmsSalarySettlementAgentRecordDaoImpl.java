package com.imile.hrms.dao.salary.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.salary.dao.HrmsSalarySettlementAgentRecordDao;
import com.imile.hrms.dao.salary.mapper.HrmsSalarySettlementAgentRecordMapper;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementAgentRecordDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementAgentRecordQuery;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 薪资代办结算数据记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Service
public class HrmsSalarySettlementAgentRecordDaoImpl extends ServiceImpl<HrmsSalarySettlementAgentRecordMapper, HrmsSalarySettlementAgentRecordDO> implements HrmsSalarySettlementAgentRecordDao {

    @Override
    public List<HrmsSalarySettlementAgentRecordDO> listByQuery(HrmsSalarySettlementAgentRecordQuery query) {
        LambdaQueryWrapper<HrmsSalarySettlementAgentRecordDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());

        if (query.getId() != null) {
            queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getId, query.getId());
        }
        if (CollectionUtils.isNotEmpty(query.getIdList())) {
            queryWrapper.in(HrmsSalarySettlementAgentRecordDO::getId, query.getIdList());
        }

        if (StringUtils.isNotBlank(query.getPaymentCountry())) {
            queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getPaymentCountry, query.getPaymentCountry());
        }

        if (StringUtils.isNotBlank(query.getPaymentMonth())) {
            queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getPaymentMonth, query.getPaymentMonth());
        }

        if (StringUtils.isNotBlank(query.getSalarySchemeConfigNo())) {
            queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getSalarySchemeConfigNo, query.getSalarySchemeConfigNo());
        }

        if (query.getSalarySubmitTemplateConfigId() != null) {
            queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getSalarySubmitTemplateConfigId, query.getSalarySubmitTemplateConfigId());
        }
        if (CollectionUtils.isNotEmpty(query.getSalarySubmitTemplateConfigIdList())) {
            queryWrapper.in(HrmsSalarySettlementAgentRecordDO::getSalarySubmitTemplateConfigId, query.getSalarySubmitTemplateConfigIdList());
        }
        if (StringUtils.isNotBlank(query.getSalarySubmitTemplateConfigNo())) {
            queryWrapper.eq(HrmsSalarySettlementAgentRecordDO::getSalarySubmitTemplateConfigNo, query.getSalarySubmitTemplateConfigNo());
        }
        if (CollectionUtils.isNotEmpty(query.getPaymentMonthList())) {
            queryWrapper.in(HrmsSalarySettlementAgentRecordDO::getPaymentMonth, query.getPaymentMonthList());
        }
        if (CollectionUtils.isNotEmpty(query.getPaymentCountryList())) {
            queryWrapper.in(HrmsSalarySettlementAgentRecordDO::getPaymentCountry, query.getPaymentCountryList());
        }
        return list(queryWrapper);
    }
}
