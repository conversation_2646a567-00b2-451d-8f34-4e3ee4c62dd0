package com.imile.hrms.dao.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SocialConfigAddAndUpdateParam {
    /**
     * 国家
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class,Groups.Update.class})
    private String country;
    /**
     * JsonString
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class,Groups.Update.class})
    private String socialConfigParamStr;
}
