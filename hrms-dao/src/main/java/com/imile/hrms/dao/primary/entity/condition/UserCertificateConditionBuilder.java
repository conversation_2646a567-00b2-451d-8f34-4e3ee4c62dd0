package com.imile.hrms.dao.primary.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCertificateConditionBuilder {

    /**
     * 人员编码列表
     */
    private List<Long> userIdList;

    /**
     * 证件类型编码列表
     */
    private List<String> certificateTypeCodeList;

    /**
     * 证件列表
     */
    private List<Certificate> certificateList;

    @Data
    public static class Certificate {

        /**
         * 证件类型编码
         */
        private String certificateTypeCode;

        /**
         * 证件号码
         */
        private String certificateCode;
    }
}
