package com.imile.hrms.dao.punch.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.punch.dto.HrmsPunchCardRecordDTO;
import com.imile.hrms.dao.punch.model.EmployeePunchRecordDO;
import com.imile.hrms.dao.punch.query.EmployeePunchCardRecordQuery;
import com.imile.hrms.dao.punch.query.UserPunchCardRecordQuery;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface EmployeePunchRecordDao extends IService<EmployeePunchRecordDO> {

    List<EmployeePunchRecordDO> getPunchList(Long dayId, String userCode, String punchType);

    List<EmployeePunchRecordDO> listRecords(EmployeePunchCardRecordQuery recordQuery);

    /**
     * 根据条件获取打卡记录
     */
    List<EmployeePunchRecordDO> selectListByCOndition(Long dayId, String country, List<String> userCodes);

    /**
     * 根据条件获取打卡记录
     */
    List<EmployeePunchRecordDO> selectListByCondition(List<String> countryList, Date startTime, Date endTime);

    /**
     * 获取最近一条绑定手机的打卡记录
     */
    List<EmployeePunchRecordDO> getPunchListByMobileConfigId(List<Long> mobileConfigId);


    List<HrmsPunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query);

    List<HrmsPunchCardRecordDTO> listRecordNewAttendance(UserPunchCardRecordQuery query);

    /**
     * 查询指定时间范围和用户的打卡记录（用于风险分析）
     *
     * @param startDayId 开始日期 (YYYYMMDD)
     * @param endDayId 结束日期 (YYYYMMDD)
     * @param userCodes 用户编码列表，为空则查询所有用户
     * @return 打卡记录列表
     */
    List<EmployeePunchRecordDO> selectForRiskAnalysis(Long startDayId, Long endDayId, List<String> userCodes);

}
