package com.imile.hrms.dao.primary.entity.condition;

import com.imile.hrms.dao.user.model.HrmsUserInfoDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserConditionBuilder {

    /**
     * 人员ID列表
     */
    private List<Long> idList;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员编码列表
     */
    private List<String> userCodeList;

    /**
     * 人员证件名
     */
    private String userName;

    /**
     * 用工类型（详见数据字典：EmploymentType）
     */
    private String employeeType;

    /**
     * 用工类型列表
     */
    private List<String> employeeTypeList;

    /**
     * 是否司机（0:否 1:是）
     */
    private Integer isDriver;

    /**
     * 工作岗位ID
     */
    private Long postId;

    /**
     * 岗位ID列表
     */
    private List<Long> postIdList;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 所属部门ID列表
     */
    private List<Long> deptIdList;

    /**
     * 所属网点部门ID
     */
    private Long ocId;

    /**
     * 所属网点编码
     */
    private String ocCode;

    /**
     * 所属网点编码列表
     */
    private List<String> ocCodeList;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 所属结算主体编码
     */
    private String settlementCenterCode;

    /**
     * 所属供应商编码
     */
    private String vendorCode;

    /**
     * 人员状态（ACTIVE:生效中 DISABLED:已失效）
     */
    private String status;

    /**
     * 工作状态
     *
     * @see com.imile.hrms.common.enums.WorkStatusEnum
     */
    private String workStatus;

    /**
     * 国籍编码
     *
     * @see com.imile.hrms.common.enums.CountryCodeEnum
     */
    private String countryCode;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 过滤条件列表
     */
    private List<FilterConditionBuilder<HrmsUserInfoDO>> filterConditionList;
}
