package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.user.dao.UserLanguageAbilityDao;
import com.imile.hrms.dao.user.mapper.UserLanguageAbilityMapper;
import com.imile.hrms.dao.user.model.UserLanguageAbilityDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 人员语言能力表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Service
public class UserLanguageAbilityDaoImpl extends ServiceImpl<UserLanguageAbilityMapper, UserLanguageAbilityDO> implements UserLanguageAbilityDao {

    @Override
    public List<UserLanguageAbilityDO> selectByUserId(Long userId) {
        LambdaQueryWrapper<UserLanguageAbilityDO> queryWrapper = Wrappers.lambdaQuery(UserLanguageAbilityDO.class);
        queryWrapper.eq(UserLanguageAbilityDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserLanguageAbilityDO::getUserId, userId);
        return super.list(queryWrapper);
    }
}
