package com.imile.hrms.dao.vendor.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 司机考核指标表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_driver_performance_target")
public class HrmsDriverPerformanceTargetDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 绩效方案表id
     */
    private String performancePlanId;

    /**
     * 指标类型：1、司机出勤 2、运单签收  3、COD货款当日未归班 4、货物当日未归班 5、投诉 6、货损及遗失
     */
    private String targetType;

    /**
     * 预警值
     */
    private String warningValue;

    /**
     * 目标值
     */
    private String targetValue;

    /**
     * 分值占比
     */
    private String scoresOf;

    /**
     * 分值
     */
    private String score;

    /**
     * 加分项/减分项
     */
    private Integer addMinus;


}
