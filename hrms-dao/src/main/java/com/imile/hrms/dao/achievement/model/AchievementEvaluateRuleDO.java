package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 绩效评价规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("achievement_evaluate_rule")
public class AchievementEvaluateRuleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则名称(英文)
     */
    private String ruleNameEn;

    /**
     * 状态:1禁用 0启用
     */
    private Integer status;

    /**
     * 配置强制比例人数少于x人，不做校验
     */
    private Integer ruleNum;


}
