package com.imile.hrms.dao.driver.attendance.query;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsDriverPunchRecordQuery
 * {@code @since:} 2024-01-20 15:58
 * {@code @description:}
 */
@Data
@Builder
public class HrmsDriverPunchRecordQuery implements Serializable {

    private static final long serialVersionUID = -5503172250984572399L;
    /**
     * 司机账号
     */
    private String userCode;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * 数据来源：1：TMS 2：司机App 3：考勤系统
     */
    private Integer sourceType;

    /**
     * 操作类型：1：DLD签收 2：轨迹打卡 3：请假 4：修改考勤 ...
     */
    private Integer operationType;


    /**
     * 操作时间
     */
    private Date operatingTime;

    /**
     * 申请单id
     */
    private Long formId;
}
