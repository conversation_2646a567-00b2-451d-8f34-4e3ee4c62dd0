package com.imile.hrms.dao.bpm.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_bpm_approval_record")
public class HrmsBpmApprovalRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 组织来源(imile等不同系统，目前都传10L即可)
     */
    private Long orgId;

    /**
     * 业务数据来源(不同业务系统，比如HRMS,CRM)
     */
    private String dataSource;

    /**
     * 来源(客户端/小程序)
     */
    private String clientType;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 审批单ID
     */
    private Long approvalId;

    /**
     * 审批编号
     */
    private String approvalCode;

    /**
     * 审批类型
     */
    private String approvalType;

    /**
     * 审批推送类型(NODE:节点  APPROVAL:整体)
     */
    private String approvalPushStatusType;

    /**
     * 审批状态 1审批中 2 通过 -1拒绝 -2驳回 -3撤回
     */
    private Integer status;

    /**
     * 申请人编号
     */
    private String applyUserCode;

    /**
     * 审批节点信息(去除附件，用不到)
     */
    private String approvalProcessInfo;

    /**
     * 是否最新 0: 否 1:是
     */
    private Integer isLatest;

    /**
     * 是否已读 0: 否 1:是
     */
    private Integer isRead;

    /**
     * 备注
     */
    private String remark;


}
