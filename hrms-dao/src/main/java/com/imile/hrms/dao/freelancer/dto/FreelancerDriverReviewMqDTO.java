package com.imile.hrms.dao.freelancer.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 众包司机审核发送MQ
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverReviewMqDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 众包司机账号
     */
    private String userCode;

    /**
     * 审批时间
     */
    private Date createDate;

    /**
     * 审批结果
     */
    private String reviewResult;

    /**
     * 审批原因
     */
    private String reason;
}
