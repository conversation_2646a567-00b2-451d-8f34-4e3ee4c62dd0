package com.imile.hrms.dao.user.condition;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class FreelancerDriverCustomConditionBuilder {

    /**
     * 关键字类型（1:账号/姓名 2:邮箱/电话）
     */
    private Integer keywordType;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 账号状态（ACTIVE:启用 DISABLED:停用）
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 网点型部门ID列表
     */
    private List<Long> ocDeptIdList;

    /**
     * 数据来源
     */
    private String dataSource;
}
