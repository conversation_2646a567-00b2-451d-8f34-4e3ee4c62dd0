package com.imile.hrms.dao.achievement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 指标库表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("achievements_indicator_library")
public class AchievementsIndicatorLibraryDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标名称en
     */
    private String targetNameEn;

    /**
     * 指标种类01定性 02定量)
     */
    private String targetType;

    /**
     * 指标分类
     */
    private String kpiType;

    /**
     * 指标性质01正向 02反向)
     */
    private String targetProperties;

    /**
     * 衡量标准
     */
    private String metrics;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 完成值来源(1是 0否)
     */
    private String isComplet;

    /**
     * 映射code
     */
    private String mappedCode;

    /**
     * 映射名称
     */
    private String mappedName;

    /**
     * 指标说明
     */
    private String remark;

    /**
     * 0开启 1关闭
     */
    private String type;

    /**
     * 指标描述中文
     */
    private String itemDescription;

    /**
     * 指标描述英文
     */
    private String itemDescriptionEn;

    /**
     * 统计说明
     */
    private String statisticalDescription;

    /**
     * 统计说明英文
     */
    private String statisticalDescriptionEn;

    /**
     * 累计规则01平均值 02求和
     */
    private String accumulationRuleType;

    /**
     * 计算规则01按比例 02按绝对 03手动填写
     */
    private String calculationRuleType;

    /**
     * 最大得分权重得分:01是 02否
     */
    private String maximumScoreWeightScore;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 01全部 02组织可见 03员工
     */
    private String visibility;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 区间得分：得分按照顺序,隔开 1.2,1,0.6,0
     */
    private String intervalScore;

}
