package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 薪资结算申请单结算人员科目具体信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_settlement_approval_item_info")
public class HrmsSalarySettlementApprovalItemInfoDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 薪资结算申请单ID
     */
    private Long salarySettlementApprovalFormId;

    /**
     * 薪资结算人员ID
     */
    private Long salarySettlementUserInfoId;

    /**
     * 薪资项ID
     */
    private Long itemId;

    /**
     * 薪资项编码
     */
    private String itemNo;

    /**
     * 薪资项具体值
     */
    private String itemValue;


}
