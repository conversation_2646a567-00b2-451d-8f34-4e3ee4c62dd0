package com.imile.hrms.dao.attendance.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 考勤方案使用范围  返回DTO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/14
 */
@Data
public class AttendanceConfigRangeDTO implements Serializable {

    private static final long serialVersionUID = -6442046905116900066L;

    /**
     * 部门ID/员工ID
     */
    private Long bizId;
    /**
     * 部门名称
     */
    private String bizNameByLang;
    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;
    /**
     * DEPT:部门/ ACCOUNT 用户
     */
    private String type;
    /**
     * 考勤配置名称/日历名称
     */
    private String attendanceConfigName;
    /**
     * 考勤配置id
     */
    private Long attendanceConfigId;

}
