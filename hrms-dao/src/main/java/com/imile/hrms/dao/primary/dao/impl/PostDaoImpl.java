package com.imile.hrms.dao.primary.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.organization.query.PostConditionBuilder;
import com.imile.hrms.dao.primary.dao.PostDao;
import com.imile.hrms.dao.primary.entity.PostDO;
import com.imile.hrms.dao.primary.mapper.PostMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
@Service
public class PostDaoImpl extends ServiceImpl<PostMapper, PostDO> implements PostDao {
    @Override
    public List<PostDO> selectByCondition(PostConditionBuilder builder) {
        LambdaQueryWrapper<PostDO> queryWrapper = Wrappers.lambdaQuery(PostDO.class);
        queryWrapper.eq(PostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (Objects.nonNull(builder.getId())) {
            queryWrapper.eq(PostDO::getId, builder.getId());
        }
        if (CollectionUtils.isNotEmpty(builder.getIdList())) {
            queryWrapper.in(PostDO::getId, builder.getIdList());
        }
        if (StringUtils.isNotBlank(builder.getPostName())) {
            queryWrapper.and(s -> s.like(PostDO::getPostNameCn, builder.getPostName())
                    .or(i -> i.like(PostDO::getPostNameEn, builder.getPostName()))
            );
        }
        if (CollectionUtils.isNotEmpty(builder.getFamilyIdList())) {
            queryWrapper.in(PostDO::getJobFamilyId, builder.getFamilyIdList());
        }
        if (CollectionUtils.isNotEmpty(builder.getCategoryIdList())) {
            queryWrapper.in(PostDO::getJobCategoryId, builder.getCategoryIdList());
        }
        if (CollectionUtils.isNotEmpty(builder.getSubCategoryIdList())) {
            queryWrapper.in(PostDO::getJobSubCategoryId, builder.getSubCategoryIdList());
        }
        if (StringUtils.isNotBlank(builder.getStatus())) {
            queryWrapper.eq(PostDO::getStatus, builder.getStatus());
        }
        queryWrapper.orderByDesc(PostDO::getCreateDate);
        return super.list(queryWrapper);
    }

    @Override
    public List<PostDO> selectByPostNameList(List<String> postNameList) {
        if (CollectionUtils.isEmpty(postNameList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PostDO> queryWrapper = Wrappers.lambdaQuery(PostDO.class);
        queryWrapper.eq(PostDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.and(m -> m.in(PostDO::getPostNameCn, postNameList)
                .or(n -> n.in(PostDO::getPostNameEn, postNameList)));
        return super.list(queryWrapper);
    }
}
