package com.imile.hrms.dao.user.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * 前端交互查询类
 * <AUTHOR>
 */
@Data
public class UserQuery extends ResourceQuery {

    /**
     * 工号
     */
    private String workNo;

    /**
     *
     */
    private String email;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 员工编码
     */
    private String userCode;
    /**
     * 姓名或邮箱
     */
    private String emailOrUserName;

    private String userCodeOrName;

    /**
     * 用户id
     */
    private List<Long> userIds;
    /**
     * 部门名称
     */
    private Long deptId;
    private List<Long> deptIds;

    private String deptIdList;

    /**
     * 网点ID
     */
    private List<Long> ocIds;

    /**
     * 用于绩效考核部门数据权限筛选
     */
    private List<Long> examineDeptIds;

    /**
     * 如果有筛选部门 优先部门筛选 如果包含绩效表的用examineCheckDeptIds(clover系统绩效的走考核部门id筛选)
     */
    private List<Long> examineCheckDeptIds;
    /**
     * 如果有筛选部门 优先部门筛选 如果不包含绩效表的用deptIds(hr系统绩效列表的走所属部门id筛选)
     */
    private List<Long> checkDeptIds;

    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位ID列表
     */
    private List<Long> postIdList;
    /**
     * 状态
     */
    private String status;
    /**
     * 工作状态
     */
    private String workStatus;
    /**
     * 工作状态
     */
    private List<String> workStatusList;

    /**
     * 状态
     */
    private List<String> queryStatusList;

    /**
     * 工作状态
     */
    private List<String> queryWorkStatusList;

    /**
     * 查询起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 时间搜索类型 entry入职/transfer调用/dimission离职
     */
    private String dateSearchType;
    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    private List<String> employeeTypes;
    /**
     * 调岗后部门id
     */
    private Long destinationDeptId;

    private String langType;
    /**
     * 状态
     */
    private String transferStatus;

    //=============================司机管理页面临时增加两个字段===============================
    /**
     * 是否是司机
     */
    private Integer isDriver;
    /**
     * 司机类型
     */
    private String driverType;
    //=============================司机管理页面临时增加两个字段===============================


    /**
     * 隔离系统账号
     */
    private String isolateAdminCode;
    //=============================员工考核列表依赖用户列表 临时增加四个字段===============================
    /**
     * 考核活动ID
     */
    private Long eventId;

    /**
     * 考核状态
     */
    private Integer appraisalStatus;

    /**
     * 考核责任人ID
     */
    private Long appraiserUserId;

    /**
     * 考核委托人ID
     */
    private Long principalUserId;

    /**
     * 执行人id
     */
    private Long appraisalExecutorId;

    /**
     * 非当前执行人id
     */
    private Long noAppraisalExecutorId;

    private Integer level;

    /**
     * 是否查看所有用户
     */
    private Boolean isAllUser;

    /**
     * 排除的子公司
     */
    private List<String> excludeCountryList;

    /**
     * 是否是考核过来的
     */
    private Boolean isAchievement;

    /**
     * 招聘offer id
     */
    private List<Long> offerIds;

    /**
     * 招聘hc id
     */
    private List<Long> hcIds;

    /**
     * 考核等级
     */
    private String leaderRate;

    /**
     * 行权人结论 01同意 02不同意
     */
    private String exerciserConclusion;

    /**
     * 查出所有职级过滤后的userId再次查询
     */
    private List<Long> userIdsByGrade;
    //=============================员工考核列表依赖用户列表 临时增加七个字段===============================

    /**
     * 国家
     */
    private String country;

    //=============================离职管理&&异动记录相关===============================
    /**
     * 查询真实离职结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualDimissionDateQ;

    /**
     * 查询真实离职起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualDimissionDateZ;

    /**
     * 是否是离职记录
     */
    private Boolean isRecord;

    /**
     * 职级序列
     */
    private String rankSequence;

    /**
     * 考核责任人筛选
     */
    private Long appraiserFilter;

    /**
     * 行权人筛选
     */
    private Long exerciserFilter;

    /**
     * 自评阶段状态 0默认状态 1待审核 2通过 3驳回
     */
    private String selfStatus;
}
