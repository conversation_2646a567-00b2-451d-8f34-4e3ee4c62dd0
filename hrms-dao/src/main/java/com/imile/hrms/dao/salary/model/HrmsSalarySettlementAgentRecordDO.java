package com.imile.hrms.dao.salary.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 薪资代办结算数据记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_salary_settlement_agent_record")
public class HrmsSalarySettlementAgentRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 代办薪资结算记录名称
     */
    private String name;

    /**
     * 计薪国
     */
    private String paymentCountry;

    /**
     * 计薪月份(202308)
     */
    private String paymentMonth;

    /**
     * 计薪方案编码
     */
    private String salarySchemeConfigNo;

    /**
     * 薪资数据提报模版配置ID
     */
    private Long salarySubmitTemplateConfigId;

    /**
     * 薪资数据提报模版配置编码
     */
    private String salarySubmitTemplateConfigNo;

    /**
     * 提报周期-开始时间
     */
    private Date submitStartDate;

    /**
     * 提报周期-结束时间
     */
    private Date submitEndDate;

    /**
     * 数据统计周期-开始时间(计薪方案周期)
     */
    private Date dataCollectStartDate;

    /**
     * 数据统计周期-结束时间(计薪方案周期)
     */
    private Date dataCollectEndDate;

    /**
     * 催办次数
     */
    private Integer noticeNumber;

    /**
     * 是否需要提示  0不需要  1需要
     */
    private Integer isNeedNotice;


}
