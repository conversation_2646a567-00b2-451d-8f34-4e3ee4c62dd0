package com.imile.hrms.dao.probation.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.entity.BaseDO;
import com.imile.hrms.dao.probation.dao.UserProbationSummaryDao;
import com.imile.hrms.dao.probation.mapper.UserProbationSummaryMapper;
import com.imile.hrms.dao.probation.model.UserProbationSummaryDO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 员工试用期目标综合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
public class UserProbationSummaryDaoImpl
        extends ServiceImpl<UserProbationSummaryMapper, UserProbationSummaryDO>
        implements UserProbationSummaryDao {

    @Override
    public UserProbationSummaryDO selectNullableByProbationId(Long probationId) {
        return this.getOne(new LambdaQueryWrapper<UserProbationSummaryDO>()
                .eq(UserProbationSummaryDO::getUserProbationId, probationId)
                .eq(BaseDO::getIsDelete, IsDeleteEnum.NO.getCode())
        );
    }
}
