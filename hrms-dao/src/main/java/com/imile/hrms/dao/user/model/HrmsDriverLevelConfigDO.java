package com.imile.hrms.dao.user.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_driver_level_config")
public class HrmsDriverLevelConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 部门(多个，存在ALL)
     */
    //private String deptId;

    /**
     * 员工性质
     */
    private String employeeType;

    /**
     * 结算日期
     */
    private String conclusionDate;

    /**
     * 适用场景
     */
    private String affectFunction;

    /**
     * 结算指标
     */
    private String settlementIndex;

    /**
     * 最少出勤天数
     */
    private Integer limitPresentDays;

    /**
     * 具体等级信息(JSON格式)
     */
    private String levelInfo;

    /**
     * 状态(停启用)
     */
    private String status;


}
