package com.imile.hrms.dao.punch.dto;

import lombok.Data;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} WarehousePunchInTimeConfigDTO
 * {@code @since:} 2024-08-23 16:34
 * {@code @description:}
 */
@Data
public class WarehousePunchConfigInTimeDTO {

    /**
     * 特殊情况：0：表示没有绑定考勤组，1：表示没有排班 未排班
     */
    private Integer specialCircumstancesFlag;

    /**
     * 打卡规则主键id
     */
    private Long punchConfigId;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     */
    private String punchConfigType;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 具体班次信息
     */
    private List<WarehousePunchInTimeDTO> warehousePunchInTimeList;
}
