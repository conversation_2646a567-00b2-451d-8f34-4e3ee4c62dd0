package com.imile.hrms.dao.account.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 账号开通重试接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/6
 */
@Data
public class AccountRetryPramDTO implements Serializable {
    private static final long serialVersionUID = -315519882942306324L;
    /**
     * 业务ID，如用户ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String bizId;

    /**
     * 账号类型编码
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String accountTypeCode;

}
