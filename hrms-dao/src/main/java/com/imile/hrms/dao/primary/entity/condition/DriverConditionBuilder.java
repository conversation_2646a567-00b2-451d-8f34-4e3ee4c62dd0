package com.imile.hrms.dao.primary.entity.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverConditionBuilder {

    /**
     * 司机编码
     */
    private String driverCode;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 网点编码
     */
    private String ocCode;

    /**
     * 网点编码列表
     */
    private List<String> ocCodeList;

    /**
     * 职能
     */
    private String functional;

    /**
     * 账号状态（ACTIVE:生效中 DISABLED:已失效）
     */
    private String accountStatus;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 用工类型列表
     */
    private List<String> employeeTypeList;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 审核状态（WAIT_AUDIT:待审核 NO_THROUGH:审核不通过 PASS:审核通过）
     */
    private String auditStatus;
}
