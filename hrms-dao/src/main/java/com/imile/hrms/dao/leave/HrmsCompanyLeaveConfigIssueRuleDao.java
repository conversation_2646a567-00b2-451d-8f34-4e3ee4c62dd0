package com.imile.hrms.dao.leave;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigIssueRuleDO;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} HrmsCompanyLeaveConfigIssueRuleDao
 * {@code @since:} 2024-04-10 14:38
 * {@code @description:}
 */
public interface HrmsCompanyLeaveConfigIssueRuleDao extends IService<HrmsCompanyLeaveConfigIssueRuleDO> {
    /**
     * 获取假期配置发放规则
     * @param allCompanyLeaveConfigIdList 假期配置id
     * @return 假期配置发放规则
     */
    List<HrmsCompanyLeaveConfigIssueRuleDO> selectByLeaveId(List<Long> allCompanyLeaveConfigIdList);
}
