package com.imile.hrms.dao.user.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.dao.user.dao.HrmsDraftRecordDao;
import com.imile.hrms.dao.user.mapper.HrmsDraftRecordMapper;
import com.imile.hrms.dao.user.model.HrmsDraftRecordDO;
import com.imile.hrms.dao.user.query.DraftQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 草稿记录表 草稿记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-13
 */
@Service
public class HrmsDraftRecordDaoImpl extends ServiceImpl<HrmsDraftRecordMapper, HrmsDraftRecordDO> implements HrmsDraftRecordDao {

    @Override
    public HrmsDraftRecordDO listByForeignKey(DraftQuery draftQuery) {
        LambdaQueryWrapper<HrmsDraftRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsDraftRecordDO.class);
        queryWrapper.eq(HrmsDraftRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsDraftRecordDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(HrmsDraftRecordDO::getForeignKey, draftQuery.getForeignKey());
        queryWrapper.eq(HrmsDraftRecordDO::getDraftType, draftQuery.getDraftTypeEnum().getCode());
        List<HrmsDraftRecordDO> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public void removeByUserId(DraftQuery draftQuery) {
        LambdaQueryWrapper<HrmsDraftRecordDO> queryWrapper = Wrappers.lambdaQuery(HrmsDraftRecordDO.class);
        queryWrapper.eq(HrmsDraftRecordDO::getForeignKey, draftQuery.getForeignKey());
        queryWrapper.eq(HrmsDraftRecordDO::getDraftType, draftQuery.getDraftTypeEnum().getCode());
        this.remove(queryWrapper);

    }
}
