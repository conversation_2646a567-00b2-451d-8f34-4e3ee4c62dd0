package com.imile.hrms.dao.blacklist.dto;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.excel.ExcelImport;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * 黑名单导入dto
 *
 * @ClassName ImportBlacklistParamDTO
 * <AUTHOR>
 * @Date 2023/5/18 11:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ImportBlacklistParamDTO extends ExcelImport {

    /**
     * 员工账号
     */
    @NotEmpty(message = ValidCodeConstant.NOT_NULL)
    private String employeeId;

    /**
     * 黑名单截止日期
     */
    private String endDate;


    /**
     * 封禁理由
     */
    @NotEmpty(message = ValidCodeConstant.NOT_NULL)
    @Length(max=500,message = ValidCodeConstant.LENGTH)
    private String reason;


}
