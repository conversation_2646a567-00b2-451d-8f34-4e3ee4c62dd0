package com.imile.hrms.dao.salary.param;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/29
 */
@Data
public class SalaryConfigUpdateParam extends SalaryConfigAddParam {
    /**
     * 计薪方案编码
     */
    @NotEmpty(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String salaryConfigNo;
}
