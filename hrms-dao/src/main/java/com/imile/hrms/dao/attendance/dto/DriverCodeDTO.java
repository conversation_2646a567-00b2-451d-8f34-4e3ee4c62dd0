package com.imile.hrms.dao.attendance.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DriverCodeDTO {
    private String driverCode;

    private Long id;

    /**
     * 用户(员工)id
     */
    private Long userId;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 日期
     */
    private Date date;

    private Long dayId;

    /**
     * 出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日
     */
    private String attendanceType;

    /**
     * 是否出勤
     */
    private Integer isAttendance;

    /**
     * 加班小时数
     */
    private BigDecimal overtimeHours;

    /**
     * 出勤小时数
     */
    private BigDecimal attendanceHours;

    /**
     * 考勤开始时间
     */
    private Date attendanceStartTime;

    /**
     * 考勤结束时间
     */
    private Date attendanceEndTime;

    /**
     * 派件数
     */
    private Integer deliveredCount;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 排序
     */
    private BigDecimal orderby;

    private BigDecimal attendanceRate;

    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;


    /**
     * 是否删除
     */
    private Integer isDelete;


    /**
     * 版本号
     */
    private Long recordVersion;
}
