package com.imile.hrms.dao.recruitment.dao.impl;

import com.imile.hrms.common.enums.recruitment.HcCooperatorStatusEnum;
import com.imile.hrms.common.util.collection.CollectionUtil;
import com.imile.hrms.dao.recruitment.model.HrmsRecruitmentJobHeadcountCooperatorDO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.dao.recruitment.mapper.HrmsRecruitmentJobHeadcountCooperatorDOMapper;
import com.imile.hrms.dao.recruitment.dao.HrmsRecruitmentJobHeadcountCooperatorDao;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/22
 */

@Service
public class HrmsRecruitmentJobHeadcountCooperatorDaoImpl extends ServiceImpl<HrmsRecruitmentJobHeadcountCooperatorDOMapper, HrmsRecruitmentJobHeadcountCooperatorDO> implements HrmsRecruitmentJobHeadcountCooperatorDao {

    @Override
    public Boolean updateCooperatorStatusBy(List<Long> hcIds, List<Long> userIds, HcCooperatorStatusEnum status) {
        return baseMapper.updateCooperatorStatusBy(status.getValue(), hcIds, userIds) > 0;
    }
}
