package com.imile.hrms.dao.attendance.query;


import lombok.Data;

import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} AttendanceCycleConfigQuery
 * {@code @since:} 2024-10-30 17:46
 * {@code @description:}
 */
@Data
public class AttendanceCycleConfigQuery implements Serializable {
    private static final long serialVersionUID = -2986495843368977751L;

    /**
     * 国家
     */
    private String country;

    /**
     * 考勤周期类型：1:月，2:周，3:自定义
     */
    private Integer cycleType;
}
