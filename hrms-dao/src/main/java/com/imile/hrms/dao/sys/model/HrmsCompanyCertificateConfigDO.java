package com.imile.hrms.dao.sys.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_company_certificate_config")
public class HrmsCompanyCertificateConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属国
     */
    private String country;
    /**
     * 是否是司机
     */
    private Integer driver;
    /**
     * 员工类型
     */
    private String employeeType;
    /**
     * 证件类型编码
     */
    private String certificateTypeCode;

    /**
     * 证件类型中文名
     */
    private String certificateTypeNameCn;

    /**
     * 证件类型英文名
     */
    private String certificateTypeNameEn;

    /**
     * s是否必填
     */
    private String handlerMethod;


}
