package com.imile.hrms.dao.base.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountryConditionBuilder {

    /**
     * 国家编码（三字码）
     */
    private String countryCode;

    /**
     * 是否启用
     */
    private String status;
}
