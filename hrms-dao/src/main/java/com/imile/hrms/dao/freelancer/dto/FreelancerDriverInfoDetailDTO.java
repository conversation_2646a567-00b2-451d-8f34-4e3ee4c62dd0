package com.imile.hrms.dao.freelancer.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 众包司机详情
 *
 * <AUTHOR>
 */
@Data
public class FreelancerDriverInfoDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long userId;

    /**
     * 员工编码
     */
    private String userCode;

    /**
     * 员工编码
     */
    private String userName;

    /**
     * 基础信息
     */
    private FreelancerDriverBasicDetailInfoDTO basicInfo;

    /**
     * 身份证件信息
     */
    private FreelancerDriverStateDetailInfoDTO stateInfo;

    /**
     * 驾驶证证件信息
     */
    private FreelancerDriverLicenseDetailInfoDTO driverLicenseInfo;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 签名
     */
    private String autograph;

    /**
     * 签名时间
     */
    private Date autographSignOnDate;

    /**
     * police check文件地址
     */
    private List<VerificationDetailInfoDTO> policeCheckFileUrlList;

    /**
     * work authorization证明文件地址
     */
    private List<VerificationDetailInfoDTO> workAuthorizationFileUrlList;

    /**
     * 工资卡信息
     */
    private FreelancerDriverBankDetailInfoDTO paymentInfo;

    /**
     * 派送偏好信息
     */
    private FreelancerDriverDeliveryPreferenceDetailDTO deliveryPreference;

    /**
     * 审批信息
     */
    private List<FreelancerDriverReviewRecordDetailInfoDTO> reviewInfo;

}
