package com.imile.hrms.dao.idwork;

import lombok.Getter;

/**
 * 编码前缀，请不要重复!
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/29
 */
@Getter
public enum NoPrefixEnum {
    ATTENDANCE("K", "考勤方案"),
    SALARY("S", "计薪方案"),
    PUNCH("P", "打卡方案"),
    SOCIAL_CONFIG("SC", "社保方案"),
    FLOW_NO("F", "审批编码");

    NoPrefixEnum(String prefix, String remark) {
        this.prefix = prefix;
        this.remark = remark;
    }

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 模块说明
     */
    private String remark;
}
