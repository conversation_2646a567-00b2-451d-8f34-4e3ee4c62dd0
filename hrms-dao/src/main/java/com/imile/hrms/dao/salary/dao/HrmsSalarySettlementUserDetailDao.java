package com.imile.hrms.dao.salary.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.salary.model.HrmsSalarySettlementUserDetailDO;
import com.imile.hrms.dao.salary.query.HrmsSalarySettlementUserDetailQuery;

import java.util.List;

/**
 * <p>
 * 薪资结算人员数据项明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
public interface HrmsSalarySettlementUserDetailDao extends IService<HrmsSalarySettlementUserDetailDO> {

    List<HrmsSalarySettlementUserDetailDO> listByIdList(List<Long> idList);

    List<HrmsSalarySettlementUserDetailDO> listByQuery(HrmsSalarySettlementUserDetailQuery query);

}
