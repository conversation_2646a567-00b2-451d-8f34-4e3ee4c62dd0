package com.imile.hrms.dao.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.hrms.common.annotation.HyperLink;
import com.imile.hrms.common.annotation.WithDict;
import com.imile.hrms.common.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
@Data
public class UserVisaDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 签证类型（1:旅游签证 2:商务签证 3:落地签证 4:工作签证）
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.VISA_TYPE, ref = "visaTypeDesc")
    private Integer visaType;

    /**
     * 签证类型描述
     */
    private String visaTypeDesc;

    /**
     * 签证号
     */
    private String visaNo;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 签发组织
     */
    private String issueOrganization;

    /**
     * 签发地点
     */
    private String issuePlace;

    /**
     * 签证正面照路径
     */
    @HyperLink(ref = "visaFrontUrl")
    private String visaFrontPath;

    /**
     * 签证正面照链接
     */
    private String visaFrontUrl;

    /**
     * 签证背面照路径
     */
    @HyperLink(ref = "visaBackUrl")
    private String visaBackPath;

    /**
     * 签证背面照链接
     */
    private String visaBackUrl;
}
