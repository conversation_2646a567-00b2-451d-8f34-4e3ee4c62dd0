package com.imile.hrms.dao.base.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.hrms.common.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 国家表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_country_config")
public class CountryDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国际域名缩写（未来废弃 统一使用三字码）
     */
    private String shortName;

    /**
     * 国家编码（三字码）
     */
    private String countryCode;

    /**
     * 国家名称（中文）
     */
    private String countryNameCn;

    /**
     * 国家名称（英文）
     */
    private String countryNameEn;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 国际区号
     */
    private String countryCallingCode;

    /**
     * 是否为国家（0:否 1:是）
     */
    private Integer isCountry;

    /**
     * 是否启用
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Double orderby;
}
