package com.imile.hrms.dao.user.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户信息扩展属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_user_extend_attr")
public class HrmsUserExtendAttrDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 属性key
     */
    private String attrKey;

    /**
     * 属性value
     */
    private String attrValue;

    /**
     * 排序
     */
    private BigDecimal orderby;


}
