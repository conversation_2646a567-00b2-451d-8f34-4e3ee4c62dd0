package com.imile.hrms.dao.recruitment.params;

import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.dao.user.query.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Data
public class RecruitmentBaseQuery extends ResourceQuery {

    /**
     * 部门id
     */
    private List<Long> permissionDeptIds;

    /**
     * 系统管理员
     */
    private Boolean isSysAdmin;

    private String createUserCode;

    public void addPermission(Boolean isSysAdmin, Boolean hasDeptPermission, List<Long> deptIds) {
        this.setCreateUserCode(RequestInfoHolder.getUserCode());
        this.setIsSysAdmin(isSysAdmin);
        if (hasDeptPermission) {
            this.setPermissionDeptIds(deptIds);
        }
    }

}
