package com.imile.hrms.dao.promotion.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.promotion.model.UserPromotionReviewDO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface UserPromotionReviewDao extends IService<UserPromotionReviewDO> {

    /**
     * 根据人员晋升ID批量查询
     *
     * @param userPromotionIdList 人员晋升ID
     * @return List<UserPromotionReviewDO>
     */
    List<UserPromotionReviewDO> selectByUserPromotionIdList(List<Long> userPromotionIdList);
}
