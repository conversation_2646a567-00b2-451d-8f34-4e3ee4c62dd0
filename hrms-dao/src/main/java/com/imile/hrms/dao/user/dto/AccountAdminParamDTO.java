package com.imile.hrms.dao.user.dto;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设置管理员类
 * <AUTHOR>
 */
@Data
public class AccountAdminParamDTO {
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;
    /**
     * 管理员属性
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private String administratorAttributes;
    /**
     * 管理员属性对应的组织id
     */
    private List<Long> organizationIds;
}
