package com.imile.hrms.dao.attendance.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.imile.hrms.common.entity.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 考勤配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_attendance_config")
public class HrmsAttendanceConfigDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 考勤方案编码
     */
    private String attendanceConfigNo;
    /**
     * 考勤配置名称
     */
    private String attendanceConfigName;

    /**
     * 配置类型 缺省方案、自定义方案
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 适用部门
     */
    private String deptIds;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 排序
     */
    private BigDecimal orderby;


}
