package com.imile.hrms.dao.organization.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.dao.organization.dao.HrmsEntPostFamilyDao;
import com.imile.hrms.dao.organization.mapper.HrmsEntPostFamilyMapper;
import com.imile.hrms.dao.organization.model.HrmsEntPostFamilyDO;
import com.imile.hrms.dao.organization.query.PostFamilyConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */

@Service
public class HrmsEntPostFamilyDaoImpl extends ServiceImpl<HrmsEntPostFamilyMapper, HrmsEntPostFamilyDO> implements HrmsEntPostFamilyDao {

    @Override
    public List<HrmsEntPostFamilyDO> selectByCondition(PostFamilyConditionBuilder builder) {
        LambdaQueryWrapper<HrmsEntPostFamilyDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostFamilyDO.class);
        queryWrapper.eq(HrmsEntPostFamilyDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (Objects.nonNull(builder.getId())) {
            queryWrapper.eq(HrmsEntPostFamilyDO::getId, builder.getId());
        }
        if (CollectionUtils.isNotEmpty(builder.getIdList())) {
            queryWrapper.in(HrmsEntPostFamilyDO::getId, builder.getIdList());
        }
        if (CollectionUtils.isNotEmpty(builder.getRootIdList())) {
            queryWrapper.in(HrmsEntPostFamilyDO::getRootId, builder.getRootIdList());
        }
        if (CollectionUtils.isNotEmpty(builder.getParentIdList())) {
            queryWrapper.in(HrmsEntPostFamilyDO::getParentId, builder.getParentIdList());
        }
        if (CollectionUtils.isNotEmpty(builder.getTypeList())) {
            queryWrapper.in(HrmsEntPostFamilyDO::getType, builder.getTypeList());
        }
        if (StringUtils.isNotBlank(builder.getStatus())) {
            queryWrapper.eq(HrmsEntPostFamilyDO::getStatus, builder.getStatus());
        }
        if (StringUtils.isNotBlank(builder.getName()) || StringUtils.isNotBlank(builder.getNameEn())) {
            queryWrapper.and(e -> e.eq(StringUtils.isNotBlank(builder.getName()), HrmsEntPostFamilyDO::getName, builder.getName()).
                    or().eq(StringUtils.isNotBlank(builder.getNameEn()), HrmsEntPostFamilyDO::getNameEn, builder.getNameEn()));
        }
        queryWrapper.last("ORDER BY FIELD(type, 'FAMILY', 'CATEGORY', 'SUB_CATEGORY'), name_en");
        return super.list(queryWrapper);
    }

    @Override
    public HrmsEntPostFamilyDO selectByIdAndStatus(Long id, String status) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<HrmsEntPostFamilyDO> queryWrapper = Wrappers.lambdaQuery(HrmsEntPostFamilyDO.class);
        queryWrapper.eq(HrmsEntPostFamilyDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(HrmsEntPostFamilyDO::getId, id);
        queryWrapper.eq(StringUtils.isNotBlank(status), HrmsEntPostFamilyDO::getStatus, status);
        return super.getOne(queryWrapper);
    }
}
