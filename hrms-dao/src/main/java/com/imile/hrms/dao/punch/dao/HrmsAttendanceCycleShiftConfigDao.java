package com.imile.hrms.dao.punch.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.hrms.dao.punch.model.HrmsAttendanceCycleShiftConfigDO;

import java.util.List;

/**
 * <p>
 * 司机等级表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-13
 */
public interface HrmsAttendanceCycleShiftConfigDao extends IService<HrmsAttendanceCycleShiftConfigDO> {

    /**
     * 查询用户的循环排班规则
     */
    List<HrmsAttendanceCycleShiftConfigDO> selectShiftConfigByUserIdList(List<Long> userIdList);

    /**
     * 查询所有有效的循环排班规则
     */
    List<HrmsAttendanceCycleShiftConfigDO> selectAllShiftConfigByUserIdList();

    /**
     * 更新员工的循环排班
     */
    void batchUpdateShiftConfig(List<HrmsAttendanceCycleShiftConfigDO> shiftConfigDOS);

    void deleteByUserId(Long userId);

}
