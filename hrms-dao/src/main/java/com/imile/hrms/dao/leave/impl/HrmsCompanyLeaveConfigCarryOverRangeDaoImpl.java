package com.imile.hrms.dao.leave.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.dao.leave.HrmsCompanyLeaveConfigCarryOverRangeDao;
import com.imile.hrms.dao.leave.mapper.HrmsCompanyLeaveConfigCarryOverRangeMapper;
import com.imile.hrms.dao.leave.model.HrmsCompanyLeaveConfigCarryOverRangeDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * {@code @author:} han.wang
 * {@code @className:} HrmsCompanyLeaveConfigCarryOverRangeDaoImpl
 * {@code @since:} 2025-04-02 19:49
 * {@code @description:}
 */
@Service
public class HrmsCompanyLeaveConfigCarryOverRangeDaoImpl extends ServiceImpl<HrmsCompanyLeaveConfigCarryOverRangeMapper, HrmsCompanyLeaveConfigCarryOverRangeDO> implements HrmsCompanyLeaveConfigCarryOverRangeDao {

    @Override
    public List<HrmsCompanyLeaveConfigCarryOverRangeDO> selectByCarryOverId(List<Long> carryOverIdList) {
        if (CollUtil.isEmpty(carryOverIdList)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<HrmsCompanyLeaveConfigCarryOverRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(HrmsCompanyLeaveConfigCarryOverRangeDO::getCarryOverId, carryOverIdList);
        queryWrapper.eq(HrmsCompanyLeaveConfigCarryOverRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(queryWrapper);
    }
}
