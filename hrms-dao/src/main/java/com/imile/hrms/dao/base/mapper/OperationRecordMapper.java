package com.imile.hrms.dao.base.mapper;

import com.imile.hrms.dao.base.model.OperationRecordDO;
import com.imile.hrms.dao.base.model.condition.OperationRecordCustomConditionBuilder;
import com.imile.hrms.dao.common.HrmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 操作记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Mapper
public interface OperationRecordMapper extends HrmsBaseMapper<OperationRecordDO> {

    /**
     * 自定义查询
     *
     * @param condition OperationRecordCustomConditionBuilder
     * @return List<OperationRecordDO>
     */
    List<OperationRecordDO> selectByCustomCondition(OperationRecordCustomConditionBuilder condition);
}
