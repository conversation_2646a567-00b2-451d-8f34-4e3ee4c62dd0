package com.imile.hrms.dao.punch.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.hrms.common.enums.WhetherEnum;
import com.imile.hrms.dao.punch.dao.HrmsWarehouseDetailAbnormalDao;
import com.imile.hrms.dao.punch.dto.HrmsWarehouseDetailAbnormalDTO;
import com.imile.hrms.dao.punch.mapper.HrmsWarehouseDetailAbnormalMapper;
import com.imile.hrms.dao.punch.model.HrmsWarehouseDetailAbnormalDO;
import com.imile.hrms.dao.punch.param.WarehouseDetailAbnormalParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 仓内统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class HrmsWarehouseDetailAbnormalDaoImpl extends ServiceImpl<HrmsWarehouseDetailAbnormalMapper, HrmsWarehouseDetailAbnormalDO> implements HrmsWarehouseDetailAbnormalDao {

    @Override
    public List<HrmsWarehouseDetailAbnormalDO> selectByWarehouseDetailId(Long warehouseDetailId, Integer processed) {
        if (Objects.isNull(warehouseDetailId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailId);
        queryWrapper.eq(Objects.nonNull(processed), HrmsWarehouseDetailAbnormalDO::getProcessed, processed);
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }

    @Override
    public List<HrmsWarehouseDetailAbnormalDO> selectByWarehouseDetailIds(List<Long> warehouseDetailIds) {
        if (CollectionUtils.isEmpty(warehouseDetailIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailIds);
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }


    @Override
    public HrmsWarehouseDetailAbnormalDO selectByAbnormalId(Long abnormalId) {
        if (Objects.isNull(abnormalId)) {
            return new HrmsWarehouseDetailAbnormalDO();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getAbnormalId, abnormalId);
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.NO.getKey());
        queryWrapper.last("limit 1");
        return super.getOne(queryWrapper);
    }

    @Override
    public List<HrmsWarehouseDetailAbnormalDO> selectByAbnormalIdList(List<Long> abnormalIdList) {
        if (CollectionUtils.isEmpty(abnormalIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HrmsWarehouseDetailAbnormalDO::getAbnormalId, abnormalIdList);
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }

    @Override
    public boolean deleteByWarehouseDetailId(Long warehouseDetailId) {
        UpdateWrapper<HrmsWarehouseDetailAbnormalDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.YES.getKey());
        updateWrapper.lambda().eq(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailId);
        return super.update(updateWrapper);
    }

    @Override
    public boolean removeByWarehouseDetailId(Long warehouseDetailId) {
        if (Objects.isNull(warehouseDetailId)){
            return false;
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailId);
        return super.remove(queryWrapper);
    }

    @Override
    public boolean deleteByWarehouseDetailIds(Collection<Long> warehouseDetailIdList) {
        UpdateWrapper<HrmsWarehouseDetailAbnormalDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.YES.getKey());
        updateWrapper.lambda().in(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailIdList);
        return super.update(updateWrapper);
    }

    @Override
    public boolean deleteByIds(Collection<Long> ids) {
        UpdateWrapper<HrmsWarehouseDetailAbnormalDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.YES.getKey());
        updateWrapper.lambda().in(HrmsWarehouseDetailAbnormalDO::getId, ids);
        return super.update(updateWrapper);
    }

    @Override
    public List<HrmsWarehouseDetailAbnormalDO> selectByCondition(WarehouseDetailAbnormalParam param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getOcIdList()), HrmsWarehouseDetailAbnormalDO::getOcId, param.getOcIdList());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getVendorIdList()), HrmsWarehouseDetailAbnormalDO::getVendorId, param.getVendorIdList());
        queryWrapper.eq(Objects.nonNull(param.getOcId()), HrmsWarehouseDetailAbnormalDO::getOcId, param.getOcId());
        queryWrapper.eq(Objects.nonNull(param.getVendorId()), HrmsWarehouseDetailAbnormalDO::getVendorId, param.getVendorId());
        queryWrapper.eq(StringUtils.isNotEmpty(param.getEmploymentForm()), HrmsWarehouseDetailAbnormalDO::getVendorId, param.getEmploymentForm());
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }

    @Override
    public List<HrmsWarehouseDetailAbnormalDTO> selectJoinWarehouseDetailList(WarehouseDetailAbnormalParam param) {
        return this.baseMapper.selectJoinWarehouseDetailList(param);
    }
}
