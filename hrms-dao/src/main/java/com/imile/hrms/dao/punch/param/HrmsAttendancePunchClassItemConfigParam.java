package com.imile.hrms.dao.punch.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class HrmsAttendancePunchClassItemConfigParam implements Serializable {
    private static final long serialVersionUID = 2807056231145060209L;

    private Long id;
    /**
     * 序号
     */
    private Integer sortNo;
    /**
     * 上班时间 仅有时分秒信息
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String punchInTime;

    /**
     * 下班时间 仅有时分秒信息
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String latestPunchOutTime;

    /**
     * 上下班打卡时间间隔 单位：小时
     */
    @Max(value = 12, message = ValidCodeConstant.MAX)
    @Min(value = 0, message = ValidCodeConstant.MIN)
    private BigDecimal punchTimeInterval;

    /**
     * 上班和最早上班打卡时间间隔 单位：小时
     */
    @Max(value = 12, message = ValidCodeConstant.MAX)
    @Min(value = 0, message = ValidCodeConstant.MIN)
    private BigDecimal punchInTimeInterval;

    /**
     * 弹性时间 单位：小时
     */
    @Max(value = 12, message = ValidCodeConstant.MAX)
    @Min(value = 0, message = ValidCodeConstant.MIN)
    private BigDecimal elasticTime;


    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00  前端传过来的是18:42这种形式
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String restStartTime;


    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00  前端传过来的是18:42这种形式
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String restEndTime;

}
