package com.imile.hrms.dao.vendor.dto;

import com.imile.common.page.PaginationResult;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class HrmsDriverPerformanceViewDTO {

    /**
     * 供应商考核情况
     */
    private PaginationResult<HrmsDriverPerformanceVendorViewDTO> vendorViewPage;

    /**
     * 供应商考核情况
     */
    private List<HrmsDriverPerformanceVendorViewDTO> vendorViewList;

    /**
     * 司机出勤率
     */
    private String attendanceRateTotal;

    /**
     * COD签收率
     */
    private String codSignRateTotal;

    /**
     * COD货款未当日归班比例
     */
    private String notCashierRateTotal;

    /**
     * 货物未当日归班比例
     */
    private String notCargoRateTotal;

    /**
     * 投诉订单比例
     */
    private String complaintRateTotal;

    /**
     * 货物损坏或丢失订单比例
     */
    private String lostRateTotal;
}

