package com.imile.hrms.dao.achievement.mapper;

import com.imile.hrms.dao.achievement.model.AchievementsNoticeDO;
import com.imile.hrms.dao.achievement.query.AchievementsNoticeQueryDTO;
import com.imile.hrms.dao.common.HrmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 绩效通知表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Mapper
public interface AchievementsNoticeMapper extends HrmsBaseMapper<AchievementsNoticeDO> {

    List<AchievementsNoticeDO> list(@Param("dto") AchievementsNoticeQueryDTO achievementsNoticeQueryDTO);

}
