package com.imile.hrms.integration.waukeen.impl;

import com.alibaba.fastjson.JSON;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.hrms.integration.waukeen.WaukeenReimbursementService;
import com.imile.rpc.common.RpcResult;
import com.imile.waukeen.api.base.api.ExpenseExchangeRateApi;
import com.imile.waukeen.api.base.api.FeeProjectApi;
import com.imile.waukeen.api.base.dto.ExpenseExchangeRateDTO;
import com.imile.waukeen.api.base.dto.FeeProjectReqApiDTO;
import com.imile.waukeen.api.base.dto.FeeProjectRespApiDTO;
import com.imile.waukeen.api.business.BizCheckApi;
import com.imile.waukeen.api.business.BizExpenseReportInfoApi;
import com.imile.waukeen.api.business.BizFundAccountInfoApi;
import com.imile.waukeen.api.business.ClientExchangeRateApi;
import com.imile.waukeen.api.business.dto.BizCheckApiDTO;
import com.imile.waukeen.api.business.dto.BizCheckDetailApiDTO;
import com.imile.waukeen.api.business.dto.BizExpenseReportInfoApiDTO;
import com.imile.waukeen.api.business.dto.BizFundAccountDetailApiDTO;
import com.imile.waukeen.api.business.dto.ClientExchangeRateDTO;
import com.imile.waukeen.api.business.dto.FundChangeAmountStatusSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-1-11
 * @version: 1.0
 */
@Slf4j
@Service
public class WaukeenReimbursementServiceImpl implements WaukeenReimbursementService {

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private BizFundAccountInfoApi bizFundAccountInfoApi;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private BizExpenseReportInfoApi bizExpenseReportInfoApi;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private ClientExchangeRateApi clientExchangeRateApi;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private BizCheckApi bizCheckApi;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private ExpenseExchangeRateApi expenseExchangeRateApi;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private FeeProjectApi feeProjectApi;

/*    @Override
    public BizFundAccountApiDTO queryFundAccountInfo(String userCode) {
        return RpcResultProcessor.process(bizFundAccountInfoApi.queryFundAccountInfo(userCode));
    }*/

    @Override
    public boolean submitExpenseReportInfo(BizExpenseReportInfoApiDTO bizExpenseReportInfoApiDTO) {
        log.info("submitExpenseReportInfo||bizExpenseReportInfoApiDTO:{}", JSON.toJSONString(bizExpenseReportInfoApiDTO));
        return RpcResultProcessor.process(bizExpenseReportInfoApi.submitExpenseReportInfo(bizExpenseReportInfoApiDTO));
    }

    @Override
    public boolean bizCheckInsertInfo(BizCheckApiDTO bizCheckApiDTO, List<BizCheckDetailApiDTO> detailApiDTOList) {
        log.info("bizCheckInsertInfo||bizCheckApiDTO:{},detailApiDTOList:{}", JSON.toJSONString(bizCheckApiDTO), JSON.toJSONString(detailApiDTOList));
        return RpcResultProcessor.process(bizCheckApi.insert(bizCheckApiDTO, detailApiDTOList));
    }

    @Override
    public List<ClientExchangeRateDTO> getClientExchangeRate(String sourceCurrency, String targetCurrency, Long submitOrgId) {
        return RpcResultProcessor.process(clientExchangeRateApi.getClientExchangeRate(sourceCurrency, targetCurrency, submitOrgId));
    }

    @Override
    public List<ExpenseExchangeRateDTO> getExpenseExchangeRate(String sourceCurrency, String targetCurrency) {
        log.info("getExpenseExchangeRate||sourceCurrency:{},targetCurrency:{}", sourceCurrency, targetCurrency);
        List<ExpenseExchangeRateDTO> rateDTOList = RpcResultProcessor.process(expenseExchangeRateApi.getExpenseExchangeRate(sourceCurrency, targetCurrency));
        log.info("getExpenseExchangeRate||rateDTOList:{}", JSON.toJSONString(rateDTOList));
        return rateDTOList;
    }

    @Override
    public BizFundAccountDetailApiDTO queryFundAccountDetail(String userCode) {
        log.info("queryFundAccountDetail||userCode:{}", userCode);
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        RpcResult<BizFundAccountDetailApiDTO> rpcResult = bizFundAccountInfoApi.queryFundAccountDetail(userCode, null);
        if (rpcResult == null) {
            return null;
        }
        BizFundAccountDetailApiDTO apiDTO = RpcResultProcessor.process(rpcResult);
        log.info("queryFundAccountDetail||BizFundAccountDetailApiDTO:{}", JSON.toJSONString(apiDTO));
        return apiDTO;
    }

    @Override
    public void fundAvailableAmountStatusSync(FundChangeAmountStatusSyncDTO fundChangeAmountStatusSyncDTO) {
        log.info("fundAvailableAmountStatusSync||fundChangeAmountStatusSyncDTO:{}", JSON.toJSONString(fundChangeAmountStatusSyncDTO));
        if (StringUtils.isBlank(fundChangeAmountStatusSyncDTO.getEmpNo())) {
            log.info("fundAvailableAmountStatusSync||fundChangeAmountStatusSyncDTO:入参值为空,不调用接口");
            return;
        }
        bizFundAccountInfoApi.fundAvailableAmountStatusSync(fundChangeAmountStatusSyncDTO);
    }

    @Override
    public List<FeeProjectRespApiDTO> getFeeProjectList(FeeProjectReqApiDTO feeProjectReqApiDTO) {
        log.info("getFeeProjectList||feeProjectReqApiDTO:{}", JSON.toJSONString(feeProjectReqApiDTO));
        List<FeeProjectRespApiDTO> rateDTOList = RpcResultProcessor.process(feeProjectApi.getFeeProjectList(feeProjectReqApiDTO));
        log.info("getFeeProjectList||rateDTOList:{}", JSON.toJSONString(rateDTOList));
        return rateDTOList;
    }
}
