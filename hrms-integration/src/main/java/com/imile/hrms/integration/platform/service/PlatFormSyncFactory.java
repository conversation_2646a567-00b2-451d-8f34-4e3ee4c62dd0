package com.imile.hrms.integration.platform.service;

import com.imile.hrms.common.util.SpringApplicationUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/4/6
 */

public class PlatFormSyncFactory {
    private PlatFormSyncFactory() {
    }

    private static final Map<String, PlatFormSyncService> cacheService = new HashMap<>();


    public static PlatFormSyncService getInstance(String platFormTypeEnum) {

        if (cacheService.isEmpty()) {
            init();
        }
        return cacheService.get(platFormTypeEnum);

    }

    /**
     * 初始化
     */
    public static void init() {
        synchronized (cacheService) {
            if (!cacheService.isEmpty()) {
                return;
            }
            List<PlatFormSyncService> platFormSyncServices = SpringApplicationUtil.getBeansOfType(PlatFormSyncService.class);
            cacheService.putAll(platFormSyncServices.stream().filter(service -> service.getPlatFormType() != null).collect(Collectors.toMap(PlatFormSyncService::getPlatFormType, x -> x, (a1, a2) -> a1)));
        }

    }
}
