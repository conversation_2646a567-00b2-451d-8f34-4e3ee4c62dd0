package com.imile.hrms.integration.exmail.service.impl;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.integration.exmail.service.ExmailService;
import com.imile.hrms.integration.exmail.service.dto.AccessTokenDTO;
import com.imile.hrms.integration.exmail.service.dto.ExmailDetailDTO;
import com.imile.hrms.integration.exmail.service.param.ExmailCreateParam;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 腾讯企业邮箱
 * 开发中心：https://exmail.qq.com/qy_mng_logic/doc#10003
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/15
 */
@Slf4j
@Component
public class ExmailServiceImpl implements ExmailService {

    private static final String API_HOST = "https://api.exmail.qq.com";
    /**
     * 获取请求秘钥
     */
    private static final String GET_TOKEN_URL = API_HOST + "/cgi-bin/gettoken";
    /**
     * 创建 邮箱url
     */
    private static final String CREATE_URL = API_HOST + "/cgi-bin/user/create";
    /**
     * 获取员工详情 url
     */
    private static final String DETAIL_URL = API_HOST + "/cgi-bin/user/get";
    /**
     * 删除邮箱 url
     */
    private static final String DELETE_URL = API_HOST + "/cgi-bin/user/delete";
    /**
     * 修改邮箱 url
     */
    private static final String UPDATE_URL = API_HOST + "/cgi-bin/user/update";
    /**
     * 企业ID key
     */
    private static final String CORP_ID_KEY = "corpid";
    /**
     * 企业秘钥KEY
     */
    private static final String CORP_SECRET_KEY = "corpsecret";
    /**
     * 企业秘钥KEY
     */
    private static final String ACCESS_TOKEN_KEY = "access_token";
    /**
     * expires_in KEY
     */
    private static final String EXPIRES_IN_KEY = "expires_in";
    /**
     * errcode KEY
     */
    private static final String ERR_CODE_KEY = "errcode";
    /**
     * errmsg KEY
     */
    private static final String ERR_MSG_KEY = "errmsg";
    /**
     * userId KEY
     */
    private static final String USERID_KEY = "userid";
    /**
     * 请求成功 errcode的值
     */
    private static final Integer REQUEST_SUCCESS_VALUE = 0;

    /**
     * 邮箱删除方式，只有当值为DELETE时，才会采取真删除，否则都是禁用
     */
    public static final String DELETE_METHOD = "DELETE";

    @Autowired
    private HrmsProperties hrmsProperties;

    /**
     * 用以缓存
     */
    private static final AccessTokenDTO ACCESS_TOKEN = new AccessTokenDTO();
    /**
     * 当距离过期还有10 秒时，则强制重新请求
     */
    private static final Long EXPIRE_TIME_DIFFERENCE = 10000L;

    @Override
    public AccessTokenDTO getAccessToken() {
        if (!isExpired()) {
            return ACCESS_TOKEN;
        }
        synchronized (ACCESS_TOKEN) {
            if (!isExpired()) {
                return ACCESS_TOKEN;
            }
            // 设置参数
            JSONObject paramObj = new JSONObject();
            paramObj.fluentPut(CORP_ID_KEY, hrmsProperties.getExmail().getCorpId()).put(CORP_SECRET_KEY, hrmsProperties.getExmail().getCorpSecret());
            String resultStr = HttpUtil.get(GET_TOKEN_URL, paramObj);

            // 结果解析
            JSONObject resultObj = JSON.parseObject(resultStr);
            String accessToken = resultObj.getString(ACCESS_TOKEN_KEY);
            if (StringUtils.isBlank(accessToken)) {
                throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), resultObj.getString(ERR_MSG_KEY));
            }
            // 此处单位是秒
            Long expiresIn = resultObj.getLong(EXPIRES_IN_KEY);
            // 刷新
            ACCESS_TOKEN.refresh(accessToken, System.currentTimeMillis() + expiresIn * 1000);
        }

        return ACCESS_TOKEN;
    }


    @Override
    public Boolean create(ExmailCreateParam exmailCreateParam) {
        String accessToken = getAccessToken().getAccessToken();
        String resultStr = HttpUtil.createPost(CREATE_URL + "?" + ACCESS_TOKEN_KEY + "=" + accessToken)
                .body(JSON.toJSONString(exmailCreateParam), ContentType.JSON.getValue())
                .execute()
                .body();
        // 结果解析
        JSONObject resultObj = JSON.parseObject(resultStr);
        Integer errCode = resultObj.getInteger(ERR_CODE_KEY);
        if (!isSuccess(errCode)) {
            log.info("create email error 入参:{}， 出参:{}" , JSONObject.toJSONString(exmailCreateParam), resultObj.toJSONString());
            throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), resultObj.getString(ERR_MSG_KEY));
        }
        return Boolean.TRUE;
    }

    @Override
    public ExmailDetailDTO detail(String userId) {
        ExmailDetailDTO exmailDetailDTO = getDetailWithoutException(userId);

        if (exmailDetailDTO == null || !isSuccess(exmailDetailDTO.getErrcode())) {
            throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), exmailDetailDTO == null ? I18nUtils.getMessage(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc()) : exmailDetailDTO.getErrmsg());
        }
        return exmailDetailDTO;
    }

    @Override
    public ExmailDetailDTO getDetailWithoutException(String userId) {
        String accessToken = getAccessToken().getAccessToken();
        // 设置参数
        JSONObject paramObj = new JSONObject();
        paramObj.fluentPut(ACCESS_TOKEN_KEY, accessToken).put(USERID_KEY, userId);
        String resultStr = HttpUtil.get(DETAIL_URL, paramObj);
        ExmailDetailDTO exmailDetailDTO = JSON.parseObject(resultStr, ExmailDetailDTO.class);
        if (exmailDetailDTO == null || !isSuccess(exmailDetailDTO.getErrcode())) {
            log.warn("获取邮箱详情错误:{}", exmailDetailDTO == null ? null : exmailDetailDTO.getErrmsg());
            return null;
        }
        // 结果解析
        return exmailDetailDTO;
    }

    @Override
    public Boolean switchStatus(String userId, StatusEnum statusEnum) {
        if (StringUtils.isBlank(userId) || statusEnum == null) {
            log.warn("userId or statsu is null");
            return false;
        }
        ExmailDetailDTO detailInfo = this.getDetailWithoutException(userId);
        if (detailInfo == null) {
            log.warn("未找到对应的企业邮箱!,email:{}", userId);
            return false;
        }
        String accessToken = getAccessToken().getAccessToken();
        // 设置参数
        JSONObject paramObj = new JSONObject();
        paramObj.fluentPut("enable", StatusEnum.DISABLED == statusEnum ? BusinessConstant.N : BusinessConstant.Y).put(USERID_KEY, userId);
        // 日志记录
        log.warn("当前用户:{},正在{}邮箱:{},邮箱详情:{}", RequestInfoHolder.getUserId(), statusEnum.getValue(), userId, JSON.toJSONString(detailInfo));

        String resultStr = HttpUtil.createPost(UPDATE_URL + "?" + ACCESS_TOKEN_KEY + "=" + accessToken)
                .body(JSON.toJSONString(paramObj), ContentType.JSON.getValue())
                .execute()
                .body();
        // 结果解析
        JSONObject resultObj = JSON.parseObject(resultStr);
        Integer errCode = resultObj.getInteger(ERR_CODE_KEY);
        if (!isSuccess(errCode)) {
            throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), resultObj.getString(ERR_MSG_KEY));
        }
        return Boolean.TRUE;
    }


    @Override
    public void delete(String userId) {
        if (!DELETE_METHOD.equalsIgnoreCase(hrmsProperties.getExmail().getDeleteMethod())) {
            // 删除方式 非真实删除，则采取禁用的方式
            this.switchStatus(userId, StatusEnum.DISABLED);
            return;
        }
        if (StringUtils.isBlank(userId)) {
            log.warn("userId is null");
            throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), "email is null!");
        }
        ExmailDetailDTO detailInfo = detail(userId);
        if (detailInfo == null) {
            log.warn("未找到对应的企业邮箱!,email:{}", userId);
            throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), "email does not exist!");
        }

        // 日志记录
        log.warn("当前用户:{},正在删除邮箱:{},邮箱详情:{}", RequestInfoHolder.getUserId(), userId, JSON.toJSONString(detailInfo));

        String accessToken = getAccessToken().getAccessToken();
        // 设置参数
        JSONObject paramObj = new JSONObject();
        paramObj.fluentPut(ACCESS_TOKEN_KEY, accessToken).put(USERID_KEY, userId);
        String resultStr = HttpUtil.get(DELETE_URL, paramObj);
        // 结果解析
        JSONObject resultObj = JSON.parseObject(resultStr);
        Integer errCode = resultObj.getInteger(ERR_CODE_KEY);
        if (!isSuccess(errCode)) {
            throw BusinessException.get(HrmsErrorCodeEnums.EMAIL_REQUEST_ERROR.getDesc(), resultObj.getString(ERR_MSG_KEY));
        }
    }

    @Override
    public String generateEmail(String userName) {
        if (StringUtils.isBlank(userName)) {
            log.warn("userName 不能为空");
            return null;
        }
        String userId;
        userName = replaceSpaceWithPoint(userName);
        while (true) {
            // 生成邮箱格式
            userId = userName + hrmsProperties.getExmail().getDefaultSuffix();
            // 判断是否存在
            ExmailDetailDTO exmailDetailDTO = this.getDetailWithoutException(userId);
            if (exmailDetailDTO == null) {
                return userId;
            }
            // 如果已存在，则数字加一
            char lastChar = userName.charAt(userName.length() - 1);
            if (Character.isDigit(lastChar)) {
                StringBuilder sb = new StringBuilder(userName);
                // 数字
                int lastNum = Character.getNumericValue(lastChar);
                lastNum = lastNum + 1;
                userName = sb.deleteCharAt(userName.length() - 1).append(lastNum).toString();
            } else {
                userName = userName + 1;
            }

        }
    }


    private  String replaceSpaceWithPoint(String userName){
        if (userName==null){
            return null;
        }
        return userName.trim().replaceAll(" ",".");
    }




    /**
     * ACCESS_TOKEN是否过期
     *
     * @return
     */
    private Boolean isExpired() {
        return ACCESS_TOKEN.getExpiresTime() == null || ACCESS_TOKEN.getExpiresTime() - System.currentTimeMillis() < EXPIRE_TIME_DIFFERENCE;
    }

    /**
     * 请求是否成功
     *
     * @param errCode
     * @return
     */
    private Boolean isSuccess(Integer errCode) {
        return REQUEST_SUCCESS_VALUE.equals(errCode);
    }

}
