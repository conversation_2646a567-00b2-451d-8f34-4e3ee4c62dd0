package com.imile.hrms.integration.oa.service;

import com.imile.oa.api.hrms.query.HrUserNameApiQuery;
import com.imile.oa.api.hrms.query.UserPaymentInfoApiQuery;
import com.imile.rpc.common.RpcResult;

public interface UserPaymentService {

    /**
     * HR用户工资卡变动更新到OA
     */
    boolean updateUserPaymentInfo(UserPaymentInfoApiQuery query);

    /**
     * HR修改用户证件名，同步OA修改
     */
    boolean updateHrUserName(HrUserNameApiQuery query);
}
