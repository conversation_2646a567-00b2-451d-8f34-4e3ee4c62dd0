package com.imile.hrms.integration.prism.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.enums.HrmsErrorCodeEnums;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.dao.attendance.query.DeliveryCountSyncQuery;
import com.imile.hrms.integration.prism.DeliveryCountService;
import com.imile.hrms.integration.prism.dto.DeliveryAndPickUpCountDTO;
import com.imile.hrms.integration.prism.dto.DeliveryCountDTO;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.prism.api.hrms.api.DriverApi;
import com.imile.prism.api.hrms.dto.*;
import com.imile.prism.api.hrms.query.*;
import com.imile.rpc.common.RpcResult;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.nio.BufferOverflowException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 司机每日派件数数据同步service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeliveryCountServiceImpl implements DeliveryCountService {

    //todo 这里暂且放到30秒，等prism那边优化SQL
    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private DriverApi driverApi;


    @Override
    public List<DeliveryCountDTO> fetchSyncData(DeliveryCountSyncQuery query) {
        if (CollUtil.isEmpty(query.getDriverCodes())) {
            return new ArrayList<>();
        }
        //拉取数据查询类封装

        DriverDldCntQuery driverDldCntQuery = BeanUtil.copyProperties(query, DriverDldCntQuery.class);
        //派件拉取
        RpcResult<List<DriverDldCntApiDTO>> listRpcResult = driverApi.listDldCnt(driverDldCntQuery);

        return HrmsCollectionUtils.convert(RpcResultProcessor.process(listRpcResult), DeliveryCountDTO.class);

    }

    @Override
    public List<DeliveryAndPickUpCountDTO> statisticalDeliveryAndPickUpData(DeliveryCountSyncQuery query) {
        if (query == null || CollectionUtils.isEmpty(query.getDriverCodes())) {
            return new ArrayList<>();
        }
        DriverDeliveredQuery deliveredQuery = new DriverDeliveredQuery();
        deliveredQuery.setDriverCodes(query.getDriverCodes());
        deliveredQuery.setStartDate(query.getStartTime());
        deliveredQuery.setEndDate(query.getEndTime());
        deliveredQuery.setTimeZone(query.getTimeZone());
        RpcResult<List<DriverDeliveredApiDTO>> rpcResult = driverApi.listDelivered(deliveredQuery);

        return HrmsCollectionUtils.convert(RpcResultProcessor.process(rpcResult), DeliveryAndPickUpCountDTO.class);
    }

    @Override
    public Map<String, List<String>> findUserDiff(UserDiffQuery query) {
        RpcResult<Map<String, List<String>>> rpcResult = new RpcResult<>();

        try {
            rpcResult = driverApi.findUserDiff(query);

        } catch (Exception e) {
            e.getStackTrace();
            throw BusinessException.get(HrmsErrorCodeEnums.RPC_CALL_FAILURE.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.RPC_CALL_FAILURE.getDesc()));
        }
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<DriverStationApiDTO> findStationByUser(DriverDldCntQuery query) {
        RpcResult<List<DriverStationApiDTO>> rpcResult = driverApi.findStationByUser(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<WareHouseOfdApiDTO> listWareHouseOfd(WareHouseOfdQuery query) {
        RpcResult<List<WareHouseOfdApiDTO>> rpcResult = driverApi.listWareHouseOfd(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<DriverSchedulingApiDTO> listDriverSchedulingInfo(DriverSchedulingInfoQuery query) {
        RpcResult<List<DriverSchedulingApiDTO>> rpcResult = driverApi.listDriverSchedulingInfo(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<DriverAssessApiDTO> listDriverAssessInfo(DriverAssessInfoQuery query) {
        RpcResult<List<DriverAssessApiDTO>> rpcResult = driverApi.listDriverAssessInfo(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<DriverCodCntApiDTO> listDriverCodCntInfo(DriverCodCntApiQuery query) {
        RpcResult<List<DriverCodCntApiDTO>> rpcResult = driverApi.listDriverCodCntInfo(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<DriverCodOfdApiDTO> listDriverCodOfdInfo(DriverCodOfdApiQuery query) {
        RpcResult<List<DriverCodOfdApiDTO>> rpcResult = driverApi.listDriverCodOfdInfo(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }

    @Override
    public List<DriverSettlementIndexApiDTO> listDriverSettlementIndexInfo(DriverSettlementIndexApiQuery query) {
        RpcResult<List<DriverSettlementIndexApiDTO>> rpcResult = driverApi.listDriverSettlementIndexInfo(query);
        RpcResultProcessor.process(rpcResult);
        return rpcResult.getResult();
    }


}
