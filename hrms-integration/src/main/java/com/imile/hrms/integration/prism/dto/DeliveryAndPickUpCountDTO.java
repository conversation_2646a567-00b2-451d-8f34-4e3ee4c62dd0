package com.imile.hrms.integration.prism.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 司机派件、取件数量
 * <AUTHOR>
 * @date 2022-04-06
 */
@Data
public class DeliveryAndPickUpCountDTO implements Serializable {
    private static final long serialVersionUID = -4626051162160695532L;
    /**
     * 司机编码
     */
    private String driverCode;
    /**
     * 司机派件量
     */
    private Integer deliveryCount;
    /**
     * 派件签收数量
     */
    private Integer deliveredCount;
    /**
     * 收件数量
     */
    private Integer pickUpCount;
    /**
     * 员工类型
     */
    private String employeeType;

    private String stationCode;
}
