package com.imile.hrms.integration.hermes.service.impl;

import com.imile.hermes.business.api.BizZipCodeApi;
import com.imile.hermes.business.dto.BizZipCodeDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.integration.hermes.service.ZipCodeService;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/6/20
 */
@Service
public class ZipCodeServiceImpl implements ZipCodeService {

    @Reference(version = "1.0.0", check = false, timeout = 6000)
    private BizZipCodeApi bizZipCodeApi;

    @Override
    public BizZipCodeDTO getZipCode(String country, String zipCode) {
        return RpcResultProcessor.process(bizZipCodeApi.queryInfoByKey(zipCode, country, BusinessConstant.DEFAULT_ORG_ID));
    }
}
