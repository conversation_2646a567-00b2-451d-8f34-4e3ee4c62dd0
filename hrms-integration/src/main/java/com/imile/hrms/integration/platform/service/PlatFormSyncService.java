package com.imile.hrms.integration.platform.service;


import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hrms.common.config.HrmsProperties;
import com.imile.hrms.integration.platform.service.dto.PlatFormDept;
import com.imile.hrms.integration.platform.service.dto.PlatFormUserInfo;
import com.imile.hrms.integration.platform.service.dto.SimplePlatFormDept;
import com.imile.hrms.integration.platform.service.param.CloseJobParam;
import com.imile.hrms.integration.platform.service.param.CreateJobParam;
import com.imile.hrms.integration.platform.service.param.UpdateJobParam;
import com.imile.hrms.integration.platform.service.param.PLatFormDeptParam;
import com.imile.hrms.integration.platform.service.param.TextMessageSendParam;
import com.imile.hrms.integration.platform.service.param.UserIdByEmailParam;
import com.imile.hrms.integration.platform.service.param.UserIdByPhoneParam;
import com.imile.hrms.integration.platform.service.param.UserParam;
import com.lark.oapi.core.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/3/30
 */
public abstract class PlatFormSyncService {

    @Autowired
    protected ImileRedisClient redisClient;

    @Autowired
    protected HrmsProperties hrmsProperties;


    /**
     * 获取钉钉、企业微信、飞书访问权限
     *
     * @return
     */
    public String getAccessToken() throws Exception {
        return null;
    }

    /**
     * 获取应用消息推送访问权限
     *
     * @return
     */
    public String getSendAccessToken() throws Exception {
        return null;
    }

    /**
     * 创建成员
     *
     * @param userParam
     */
    public Boolean createUser(UserParam userParam) {
        return Boolean.FALSE;
    }

    /**
     * 获取成员
     *
     * @param userId
     * @return
     */
    public PlatFormUserInfo getUserByUserId(String userId) {
        return null;
    }

    /**
     * 更新成员
     *
     * @param userParam
     */
    public Boolean updateUser(UserParam userParam) {
        return Boolean.FALSE;
    }

    /**
     * 删除成员
     *
     * @param userId
     */
    public void deleteUser(String userId) {

    }

    /**
     * 批量删除成员
     *
     * @param userIdList
     */
    public void batchDeleteUser(List<String> userIdList) {

    }

    /**
     * 获取部门下所有成员
     *
     * @param deptId
     * @param fetchChild 是否递归获取子部门下面的成员：1-递归获取，0-只获取本部门
     * @return
     */
    public List<String> listSimpleUserByDeptId(Long deptId, Integer fetchChild) {
        return Lists.newArrayList();
    }

    /**
     * 获取部门下成员详情
     *
     * @param deptId
     * @param fetchChild 1/0：是否递归获取子部门下面的成员
     * @return
     */
    public List<PlatFormUserInfo> listUserByDeptId(Long deptId, Integer fetchChild) {
        return Lists.newArrayList();
    }

    /**
     * 根据手机号获取userId
     *
     * @param param
     * @return
     */
    public String getUserIdByMobile(UserIdByPhoneParam param) {
        return null;
    }

    /**
     * 根据邮箱获取userId
     *
     * @param param
     * @return
     */
    public String getUserIdByEmail(UserIdByEmailParam param) {
        return null;
    }

    /**
     * 创建部门
     *
     * @param PLatFormDeptParam
     */
    public abstract Long createDept(PLatFormDeptParam PLatFormDeptParam);

    /**
     * 更新部门
     *
     * @param PLatFormDeptParam
     */
    public abstract Boolean updateDept(PLatFormDeptParam PLatFormDeptParam);

    /**
     * 删除部门
     *
     * @param deptId
     */
    public Boolean deleteDept(Long deptId) {
        return null;
    }

    /**
     * 获取部门列表
     *
     * @param rootDeptId
     * @return
     */
    public List<SimplePlatFormDept> listDept(Long rootDeptId) {
        return Lists.newArrayList();
    }

    /**
     * 获取自部门id列表
     *
     * @param rootDeptId
     * @return
     */
    public List<Long> listSimpleDept(Long rootDeptId) {
        return Lists.newArrayList();
    }

    /**
     * 获取单个部门详情
     *
     * @param deptId
     * @return
     */
    public PlatFormDept getDeptByDeptId(Long deptId) throws Exception {
        return null;
    }

    /**
     * 平台类型
     *
     * @return
     */
    public String getPlatFormType() {
        return null;
    }

    /**
     * 上传临时文件
     *
     * @param key
     * @param type
     * @param file
     * @return
     */
    public String uploadMedia(String key, String type, File file) {
        return null;
    }

    /**
     * 机器人推送文件
     *
     * @param key
     * @param type
     * @param file
     */
    public void sendMedia(String key, String type, File file) {

    }

    /**
     * 发送应用消息
     *
     * @param param
     */
    public Boolean sendTextMessage(TextMessageSendParam param) {
        return Boolean.FALSE;
    }

    /**
     * 发送企微消息
     *
     * @param map
     * @return
     */
    public Boolean sendWeChatMessage(Map<?, ?> map) {
        return false;
    }

    /**
     * 新增招聘职位
     */
    public String createJob(CreateJobParam param) {
        return null;
    }

    /**
     * 更新招聘职位
     */
    public Boolean updateJob(CreateJobParam param) {
        return false;
    }

    /**
     * 关闭招聘职位
     */
    public Boolean closeJob(CloseJobParam param) {
        return false;
    }
}
