package com.imile.hrms.integration.hermes.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/19
 */
@Data
public class EntOcDTO implements Serializable {
    private static final long serialVersionUID = -5775253463336726707L;

    private Long id;
    /**
     * 企业id
     */
    private Long orgId;
    /**
     * 所属结算企业id
     */
    private Long settleOrgId;
    /**
     * 加盟商企业id
     */
    private Long vendorOrgId;
    /**
     * 业务组织编号
     */
    private String ocCode;
    /**
     * 业务组织名称
     */
    private String ocName;
    /**
     * 业务组织简称
     */
    private String ocShortName;
    /**
     * 父网点Id
     */
    private Long parentOcId;
    /**
     * 父网点编码
     */
    private String parentOcCode;
    /**
     * 网点类型
     */
    private String ocType;
    /**
     * 网点级数
     */
    private Integer ocLevel;
    /**
     * 父网点Id
     */
    private Integer parentLevelId;
    /**
     * 区域中心编码
     */
    private String ocCenterCode;
    /**
     * 地址
     */
    private String address;
    /**
     * 区域
     */
    private String region;
    /**
     * 城市
     */
    private String city;
    /**
     * 省份
     */
    private String province;
    /**
     * 国家
     */
    private String country;
    /**
     * 废弃字段(老邮编，数据库目前没赋值)
     */
    private String postCode;
    /**
     * 电话
     */
    private String tel;
    /**
     * 手机
     */
    private String cell;
    /**
     * 传真
     */
    private String fax;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 联系人
     */
    private String attentionTo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private String status;
    /**
     * 负责人
     */
    private String ocPrincipal;
    /**
     * 负责人电话
     */
    private String ocPrincipalCall;
    /**
     * 负责人邮箱
     */
    private String ocPrincipalMail;
    /**
     * 经理
     */
    private String ocManager;
    /**
     * 经理电话
     */
    private String ocManagerCall;
    /**
     * 电话区号
     */
    private String phoneAreaCode;
    /**
     * 客服人员
     */
    private String customerService;
    /**
     * 客服电话
     */
    private String consumerHotline;
    /**
     * 版本编码
     */
    private Long recordVersion;
    /**
     * 新增时间
     */
    private Date createDate;
    /**
     * 新增用户编码
     */
    private String createUserCode;
    /**
     * 新增用户名
     */
    private String createUserName;
    /**
     * 最后修改时间
     */
    private Date lastUpdDate;
    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;
    /**
     * 最后修改用户名
     */
    private String lastUpdUserName;
    /**
     * 经营类别
     */
    private String businessType;
    /**
     * 默认配载网点Id
     */
    private Long defaultOcId;
    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 纬度
     */
    private BigDecimal latitude;
    /**
     * 投诉电话
     */
    private String complaintCall;
    /**
     * 内部电话
     */
    private String internalTelephone;
    /**
     * 服务区
     */
    private String serviceArea;
    /**
     * 非服务区
     */
    private String nonServiceArea;
    /**
     * 默认配载网点编码
     */
    private String defaultOcCode;
    /**
     * 网点经理邮箱
     */
    private String ocManagerEmail;
    /**
     * 客服人员编码
     */
    private String customerServiceCode;
    /**
     * 经营类别
     */
    private String businessCategory;

    /**
     * 提示音名称
     */
    private String voiceName;

    /**
     * 提示音链接
     */
    private String voiceUrl;

    /**
     * 社区
     */
    private String suburb;

    /**
     * 街道
     */
    private String street;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 外部号码（街道号）
     */
    private String externalNo;

    /**
     * 内部号码（门牌号码）
     */
    private String internalNo;

    /**
     * 币种
     */
    private String currency;
}
