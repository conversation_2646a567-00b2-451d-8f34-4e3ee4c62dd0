package com.imile.hrms.integration.dict.service.impl;

import com.alibaba.fastjson.JSON;
import com.imile.hermes.resource.api.SysDictApi;
import com.imile.hermes.resource.dto.DictDataDTO;
import com.imile.hrms.common.constants.BusinessConstant;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.integration.dict.service.DictService;
import com.imile.hrms.integration.dict.vo.DictVO;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DictServiceImpl implements DictService {

    @Reference(version = "1.0.0")
    private SysDictApi sysDictApi;

    @Override
    public Map<String, DictVO> getByTypeCode(String typeCode) {
        if (StringUtils.isEmpty(typeCode)) {
            return new HashMap<>(0);
        }
        RpcResult<List<DictDataDTO>> rpc = this.sysDictApi.findDictDataByOrgIdAndType(RequestInfoHolder.getOrgId(), typeCode);

        return RpcResultProcessor.process(rpc, new ArrayList<>())
                .stream()
                // 过滤掉null
                .filter(Objects::nonNull)
                // 找到合适的语言
                .filter(dict -> Objects.equals(RequestInfoHolder.getLocaleTxt(), dict.getLangType()))
                // 映射为对应的值
                .map(dict -> new DictVO().setTypeCode(dict.getTypeCode()).setDataCode(dict.getDataCode()).setDataValue(dict.getDataValue()).setSort(dict.getSort()))
                // 转化为map
                .collect(Collectors.toMap(DictVO::getDataCode, v -> v, (v1, v2) -> v1));
    }

    @Override
    public Map<String, Map<String, DictVO>> getByTypeCodes(List<String> typeCodes) {
        return getByTypeCodes(typeCodes, RequestInfoHolder.getOrgId());

    }

    @Override
    public Map<String, Map<String, DictVO>> getAllLangByTypeCodes(List<String> typeCodes) {
        try {
            RpcResult<Map<String, List<DictDataDTO>>> rpc = this.sysDictApi.findDictDataByTypeCodeMap(typeCodes, RequestInfoHolder.getOrgId());
            Map<String, List<DictDataDTO>> result = RpcResultProcessor.process(rpc, new HashMap<>(0));
            Set<String> keys = result.keySet();
            Map<String, Map<String, DictVO>> resultMap = new HashMap<>((int) (result.size() / 0.75));
            for (String key : keys) {
                Map<String, DictVO> map = ObjectUtils.defaultIfNull(result.get(key), new ArrayList<DictDataDTO>()).stream()
                        // 过滤掉null
                        .filter(Objects::nonNull)
                        // 映射为对应的值
                        .filter(dict -> Objects.equals(Locale.CHINA.toString(), dict.getLangType()) || Objects.equals(Locale.US.toString(), dict.getLangType()))
                        .map(dict -> {
                            DictVO dictVO = new DictVO().setTypeCode(dict.getTypeCode()).setDataCode(dict.getDataCode()).setDataValue(dict.getDataValue());
                            if (Objects.equals(Locale.CHINA.toString(), dict.getLangType())) {
                                dictVO.setDataValueCn(dict.getDataValue());
                                return dictVO;
                            }
                            dictVO.setDataValueEn(dict.getDataValue());
                            return dictVO;
                        })
                        // 转化为map
                        .collect(Collectors.toMap(DictVO::getDataCode, v -> v, (v1, v2) -> {
                            if (!StringUtils.isBlank(v2.getDataValueEn())) {
                                v1.setDataValueEn(v2.getDataValueEn());
                                return v1;
                            }
                            if (!StringUtils.isBlank(v2.getDataValueCn())) {
                                v1.setDataValueCn(v2.getDataValueCn());
                            }
                            return v1;
                        }));
                resultMap.put(key, map);
            }
            return resultMap;
        } catch (Exception e) {
            log.error(this.getClass().toString(), e);
            return new HashMap<>(0);
        }

    }

    @Override
    public List<DictDataDTO> getByTypeCodeList(String typeCode) {
        return getByTypeCodeList(typeCode, RequestInfoHolder.getOrgId());
    }

    @Override
    public Map<String, Map<String, DictVO>> getByTypeCodes(List<String> typeCodes, Long orgId) {
        try {
            RpcResult<Map<String, List<DictDataDTO>>> rpc = this.sysDictApi.findDictDataByTypeCodeMap(typeCodes, orgId);
            Map<String, List<DictDataDTO>> result = RpcResultProcessor.process(rpc, new HashMap<>(0));
            Set<String> keys = result.keySet();
            Map<String, Map<String, DictVO>> resultMap = new HashMap<>((int) (result.size() / 0.75));
            for (String key : keys) {
                Map<String, DictVO> map = ObjectUtils.defaultIfNull(result.get(key), new ArrayList<DictDataDTO>()).stream()
                        // 过滤掉null
                        .filter(Objects::nonNull)
                        // 找到合适的语言
                        .filter(dict -> Objects.equals(RequestInfoHolder.getLocaleTxt(), dict.getLangType()))
                        // 映射为对应的值
                        .map(dict -> new DictVO().setTypeCode(dict.getTypeCode()).setDataCode(dict.getDataCode()).setDataValue(dict.getDataValue()))
                        // 转化为map
                        .collect(Collectors.toMap(DictVO::getDataCode, v -> v, (v1, v2) -> v1));
                resultMap.put(key, map);
            }
            return resultMap;
        } catch (Exception e) {
            log.error(this.getClass().toString(), e);
            return new HashMap<>(0);
        }
    }

    @Override
    public List<DictDataDTO> getByTypeCodeList(String typeCode, Long orgId) {
        if (StringUtils.isEmpty(typeCode)) {
            return new ArrayList<>();
        }
        RpcResult<List<DictDataDTO>> rpc = this.sysDictApi.findDictDataByOrgIdAndType(orgId, typeCode);
        return rpc.getResult();
    }

    /**
     * 根据typeCode查询code->value
     *
     * @param typeCode type code
     * @param orgId
     * @return 映射
     */
    @Override
    public Map<String, DictVO> getByTypeCodeAndOrgId(String typeCode, Long orgId) {
        if (StringUtils.isEmpty(typeCode)) {
            return new HashMap<>(0);
        }
        RpcResult<List<DictDataDTO>> rpc = this.sysDictApi.findDictDataByOrgIdAndType(orgId, typeCode);
        log.debug("dictRpc:" + JSON.toJSONString(rpc) + ",lang:" + RequestInfoHolder.getLocaleTxt() + "");
        return RpcResultProcessor.process(rpc, new ArrayList<>())
                .stream()
                // 过滤掉null
                .filter(Objects::nonNull)
                // 找到合适的语言
                .filter(dict -> Objects.equals(RequestInfoHolder.getLocaleTxt(), dict.getLangType()))
                // 映射为对应的值
                .map(dict -> new DictVO().setTypeCode(dict.getTypeCode()).setDataCode(dict.getDataCode()).setDataValue(dict.getDataValue()))
                // 转化为map
                .collect(Collectors.toMap(DictVO::getDataCode, v -> v, (v1, v2) -> v1));
    }

    @Override
    public Map<String, DictVO> getByTypeCodeAndOrgIdAndLocale(String typeCode, Long orgId, String localeTxt) {
        if (StringUtils.isEmpty(typeCode)) {
            return new HashMap<>(0);
        }
        RpcResult<List<DictDataDTO>> rpc = this.sysDictApi.findDictDataByOrgIdAndType(orgId, typeCode);
        return RpcResultProcessor.process(rpc, new ArrayList<>())
                .stream()
                // 过滤掉null
                .filter(Objects::nonNull)
                // 找到合适的语言
                .filter(dict -> Objects.equals(localeTxt, dict.getLangType()))
                // 映射为对应的值
                .map(dict -> new DictVO().setTypeCode(dict.getTypeCode()).setDataCode(dict.getDataCode()).setDataValue(dict.getDataValue()))
                // 转化为map
                .collect(Collectors.toMap(DictVO::getDataCode, v -> v, (v1, v2) -> v1));
    }

    @Override
    public Map<String, List<DictDataDTO>> getDictCodeDataListMap(List<String> typeCodeList) {
        if (CollectionUtils.isEmpty(typeCodeList)) {
            return Collections.emptyMap();
        }
        RpcResult<Map<String, List<DictDataDTO>>> rpcResult
                = sysDictApi.findDictDataByTypeCodeAndLangMap(typeCodeList, BusinessConstant.DEFAULT_ORG_ID, RequestInfoHolder.getLocaleTxt());
        return RpcResultProcessor.process(rpcResult, Collections.emptyMap());
    }
}
