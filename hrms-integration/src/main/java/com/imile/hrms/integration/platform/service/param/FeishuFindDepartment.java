package com.imile.hrms.integration.platform.service.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/4
 */
@Data
public class FeishuFindDepartment {

    /**
     * 部门id
     */
    @JSONField(name = "department_ids")
    private List<String> departmentIds;

    /**
     * 请求字段
     */
    @JSONField(name = "required_fields")
    private List<String> requiredFields;
}
