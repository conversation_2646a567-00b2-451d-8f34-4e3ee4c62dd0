package com.imile.hrms.integration.hermes.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.hermes.business.api.CountryApi;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.hrms.common.constants.RedisConstants;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.integration.hermes.service.CountryService;
import com.imile.hrms.integration.hermes.vo.CountryDTO;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import jodd.util.StringUtil;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CountryServiceImpl implements CountryService {
    @Reference(version = "1.0.0")
    private CountryApi countryApi;

    @Override
    public List<CountryDTO> listAllCountry() {
        RpcResult<List<CountryConfigDTO>> rpcResult = countryApi.queryAllCountryConfigList();
        return HrmsCollectionUtils.convert(RpcResultProcessor.process(rpcResult), CountryDTO.class);
    }

    @Override
    public List<CountryConfigDTO> queryAllCountryConfigList() {
        return RpcResultProcessor.process(countryApi.queryAllCountryConfigList());
    }


    @Override
    public CountryDTO queryCountry(String countryName) {
        if (StringUtil.isEmpty(countryName)) {
            return null;
        }

        CountryApiQuery countryApiQuery = new CountryApiQuery();
        Long orgId = RequestInfoHolder.getOrgId() == null ? 10L : RequestInfoHolder.getOrgId();
        countryApiQuery.setOrgId(orgId);
        countryApiQuery.setCountryName(countryName);
        RpcResult<CountryConfigDTO> rpcResult = countryApi.queryCountryConfig(countryApiQuery);
        return HrmsCollectionUtils.convertSingle(RpcResultProcessor.process(rpcResult), CountryDTO.class);
    }

    @Override
    public CountryConfigDTO queryCountryConfig(CountryApiQuery query) {
        RpcResult<CountryConfigDTO> rpc = countryApi.queryCountryConfig(query);
        return RpcResultProcessor.process(rpc);
    }

    @Override
    public List<CountryConfigDTO> selectCountryConfigList(CountryApiQuery query) {
        RpcResult<List<CountryConfigDTO>> rpc = countryApi.queryCountryConfigList(query);
        return RpcResultProcessor.process(rpc);
    }

}
