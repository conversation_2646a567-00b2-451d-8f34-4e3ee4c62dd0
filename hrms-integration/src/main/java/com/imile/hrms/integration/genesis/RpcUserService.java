package com.imile.hrms.integration.genesis;

import com.imile.genesis.api.model.param.user.UserAddParam;
import com.imile.genesis.api.model.param.user.UserStatusSwitchParam;
import com.imile.genesis.api.model.param.user.UserUpdateParam;
import com.imile.genesis.api.model.result.user.UserDTO;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public interface RpcUserService {

    /**
     * 生成人员编码
     *
     * @param isDriver     是否为司机
     * @param employeeType 用工类型
     * @return String
     */
    String generateUserCode(Integer isDriver, String employeeType);

    /**
     * 添加人员
     *
     * @param param UserAddParam
     * @return String
     */
    String addUser(UserAddParam param);

    /**
     * 更新人员
     *
     * @param param UserUpdateParam
     * @return Boolean
     */
    Boolean updateUser(UserUpdateParam param);

    /**
     * 切换人员状态
     *
     * @param param UserStatusSwitchParam
     * @return Boolean
     */
    Boolean switchUserStatus(UserStatusSwitchParam param);

    /**
     * 根据人员编码获取人员
     *
     * @param userCode 人员编码
     * @return UserDTO
     */
    UserDTO getUserByCode(String userCode);
}
