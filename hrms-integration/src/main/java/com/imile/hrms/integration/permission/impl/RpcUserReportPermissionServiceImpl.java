package com.imile.hrms.integration.permission.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.hrms.integration.permission.RpcUserReportPermissionService;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.permission.api.UserDataPermissionApi;
import com.imile.permission.api.custom.HRMSOperationApi;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.HRMSRoleMenuDTO;
import com.imile.permission.api.dto.HRMSUserRoleDTO;
import com.imile.permission.api.dto.MenuTreeDTO;
import com.imile.permission.api.dto.UserDataPermissionApiDTO;
import com.imile.permission.api.dto.UserWithSystemApiDTO;
import com.imile.permission.api.enums.SystemApiEnums;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/1/7
 */
@Component
public class RpcUserReportPermissionServiceImpl implements RpcUserReportPermissionService {


    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private HRMSOperationApi operationApi;
    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private UserDataPermissionApi userDataPermissionApi;

    /**
     * 单个userCode查角色
     */
    @Override
    public HRMSUserRoleDTO getUserRole(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return new HRMSUserRoleDTO();
        }
        List<HRMSUserRoleDTO> roleList = RpcResultProcessor.process(operationApi.listRoleByUserCode(Lists.newArrayList(userCode)));
        if (CollectionUtils.isNotEmpty(roleList)) {
            return roleList.get(0);
        }
        return new HRMSUserRoleDTO();
    }

    @Override
    public Map<String, HRMSUserRoleDTO> getUserRoleList(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Maps.newHashMap();
        }
        List<HRMSUserRoleDTO> process = new ArrayList<>();
        for (List<String> subUserCodeList : Lists.partition(userCodeList, 500)) {
            List<HRMSUserRoleDTO> data = RpcResultProcessor.process(operationApi.listRoleByUserCode(subUserCodeList));
            if (CollectionUtils.isNotEmpty(data)) {
                process.addAll(data);
            }
        }
        return process.stream()
                .collect(Collectors.toMap(HRMSUserRoleDTO::getUserCode, it -> it, (it1, it2) -> it1));
    }

    @Override
    public List<MenuTreeDTO> getMenuTree() {
        return RpcResultProcessor.process(operationApi.getMenuTree());
    }

    @Override
    public List<HRMSRoleMenuDTO> getPermissionMenuByRoleId(Long roleId) {
        return RpcResultProcessor.process(operationApi.listMenuByRoleId(Lists.newArrayList(roleId)));
    }

    @Override
    public DataPermissionApiDTO getDeptMasterPermission(String userCode) {
        UserWithSystemApiDTO apiDTO = new UserWithSystemApiDTO();
        apiDTO.setUserCode(userCode);
        apiDTO.setSystemCode(SystemApiEnums.HRMS.getCode());
        UserDataPermissionApiDTO process = RpcResultProcessor.process(userDataPermissionApi.getUserDataPermission(apiDTO));
        if (CollectionUtils.isEmpty(process.getDataPermissionApiDTOList())) {
            return null;
        }
        return process.getDataPermissionApiDTOList().stream()
                .filter(it -> "BaseDeptData".equals(it.getTypeCode()))
                .findFirst()
                .orElse(null);
    }
}
