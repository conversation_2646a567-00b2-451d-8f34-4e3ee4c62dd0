package com.imile.hrms.integration.accounting;

import com.imile.accounting.center.dto.ProjectPageResponseDto;
import com.imile.hrms.dao.organization.model.HrmsEntProjectDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
public interface RpcProjectService {

    /**
     * 根据项目编码获取
     *
     * @param projectCode 项目编码
     * @return ProjectPageResponseDto
     */
    ProjectPageResponseDto getProjectByCode(String projectCode);

    /**
     * 根据项目编码列表获取
     *
     * @param projectCodeList 项目编码列表
     * @return List<ProjectPageResponseDto>
     */
    List<ProjectPageResponseDto> getProjectByCodeList(List<String> projectCodeList);

    /**
     * 根据国家编码获取项目列表
     * @param countryList
     * @return
     */
    List<ProjectPageResponseDto> getProjectByCountryList(List<String> countryList);
}
