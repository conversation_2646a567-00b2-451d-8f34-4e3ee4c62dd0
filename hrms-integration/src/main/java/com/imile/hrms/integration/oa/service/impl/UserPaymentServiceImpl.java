package com.imile.hrms.integration.oa.service.impl;

import com.imile.bpm.api.BpmApprovalApi;
import com.imile.hrms.integration.oa.service.UserPaymentService;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.oa.api.hrms.api.BizUserPaymentApi;
import com.imile.oa.api.hrms.query.HrUserNameApiQuery;
import com.imile.oa.api.hrms.query.UserPaymentInfoApiQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-3-9
 * @version: 1.0
 */
@Slf4j
@Service
public class UserPaymentServiceImpl implements UserPaymentService {

    @Reference(version = "1.0.0", check = false, timeout = 6000)
    private BizUserPaymentApi bizUserPaymentApi;

    @Override
    public boolean updateUserPaymentInfo(UserPaymentInfoApiQuery query) {
        return RpcResultProcessor.process(bizUserPaymentApi.updateUserPaymentInfo(query));
    }

    @Override
    public boolean updateHrUserName(HrUserNameApiQuery query) {
        return RpcResultProcessor.process(bizUserPaymentApi.updateHrUserName(query));
    }
}
