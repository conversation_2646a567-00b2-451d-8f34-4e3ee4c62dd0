package com.imile.hrms.integration.pcs.impl;

import com.imile.WareHouseEmployeeApi;
import com.imile.hrms.integration.pcs.PcsService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/12/11 17:05
 * @version: 1.0
 */
@Component
public class PcsServiceImpl implements PcsService {

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private WareHouseEmployeeApi wareHouseEmployeeApi;

    @Override
    public void addEmployeeCertificateDiff(String userCode) {
        wareHouseEmployeeApi.addEmployeeCertificateDiff(userCode);
    }
}
