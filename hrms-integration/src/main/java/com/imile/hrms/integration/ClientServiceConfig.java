package com.imile.hrms.integration;

import com.imile.hermes.enterprise.api.EntEmpDriverApi;
import com.imile.hermes.enterprise.api.EntEmployeeApi;
import com.imile.hermes.enterprise.api.EntOcApi;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Title:消费者bean配置类
 * @date 2021/10/28 4:21 下午
 */
@Configuration
public class ClientServiceConfig {

    @Reference(version = "1.0.0",timeout = 8000, protocol = "${dubbo.protocol.id}", application = "${dubbo.application.id}")
    private EntEmpDriverApi entEmpDriverApi;

    @Bean
    public EntEmpDriverApi entEmpDriverApi() {
        return entEmpDriverApi;
    }

    @Reference(version = "1.0.0",timeout = 8000, protocol = "${dubbo.protocol.id}", application = "${dubbo.application.id}")
    private EntEmployeeApi entEmployeeApi;

    @Bean
    public EntEmployeeApi entEmployeeApi() {
        return entEmployeeApi;
    }

    @Reference(version = "1.0.0",timeout = 8000, protocol = "${dubbo.protocol.id}", application = "${dubbo.application.id}")
    private EntOcApi entOcApi;

    @Bean
    public EntOcApi entOcApi() {
        return entOcApi;
    }
}
