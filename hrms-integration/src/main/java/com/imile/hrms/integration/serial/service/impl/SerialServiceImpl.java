package com.imile.hrms.integration.serial.service.impl;

import com.imile.hrms.common.exception.BusinessLogicException;
import com.imile.hrms.integration.serial.service.SerialService;
import com.imile.serial.api.util.SerialNoApiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.imile.hrms.common.enums.HrmsErrorCodeEnums.PARAM_VALID_ERROR;
import static com.imile.hrms.common.util.HrmsStringUtil.UNDERLINE;

/**
 * Serial服务实现类
 *
 * <AUTHOR>
 */
@Component
public class SerialServiceImpl implements SerialService {

    @Autowired
    SerialNoApiUtil serialNoApiUtil;

    /**
     * 众包司机编号前缀
     */
    public static final String PREX_FREELANCER_DRIVER = "F";

    public static final String PREX_FREELANCER_DRIVER_NEW = "D";

    /**
     * 众包司机编码规则Code
     */
    public static final String CODE_FREELANCER_DRIVER = "freelancerDriver";

    /**
     * 众包司机编码生成
     *
     * @param orgId 企业id
     * @return 众包司机编码
     */
    @Override
    public String genFreelancerDriverCode(Long orgId) {
        if (Objects.isNull(orgId) || orgId <= 0) {
            throw BusinessLogicException.getException(PARAM_VALID_ERROR);
        }
        return serialNoApiUtil.getNoPermanentIncr(CODE_FREELANCER_DRIVER + UNDERLINE + orgId, PREX_FREELANCER_DRIVER + orgId.toString().length() + orgId, "", "000");
    }

    /**
     * 众包司机编码生成
     *
     * @param orgId 企业id
     * @return 众包司机编码
     */
    @Override
    public String genFreelancerDriverCodeNew(Long orgId) {
        if (Objects.isNull(orgId) || orgId <= 0) {
            throw BusinessLogicException.getException(PARAM_VALID_ERROR);
        }
        return serialNoApiUtil.getNoPermanentIncr(CODE_FREELANCER_DRIVER + UNDERLINE + orgId, PREX_FREELANCER_DRIVER_NEW + orgId.toString().length() + orgId, "", "000");
    }
}
