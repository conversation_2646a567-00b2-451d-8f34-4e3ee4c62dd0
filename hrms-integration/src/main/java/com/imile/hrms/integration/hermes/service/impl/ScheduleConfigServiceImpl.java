package com.imile.hrms.integration.hermes.service.impl;

import com.imile.hermes.scheduleConfig.ScheduleConfigNewApi;
import com.imile.hermes.scheduleConfig.dto.ScheduleConfigNewApiDTO;
import com.imile.hrms.integration.hermes.service.ScheduleConfigService;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ScheduleConfigServiceImpl implements ScheduleConfigService {

    @Reference(version = "1.0.0", check = false, timeout = 50000)
    private ScheduleConfigNewApi scheduleConfigNewApi;

    @Override
    public ScheduleConfigNewApiDTO selectByOrgIdAndCountry(Long orgId, String country) {
        RpcResult<ScheduleConfigNewApiDTO> rpcResult = scheduleConfigNewApi.selectByOrgIdAndCountry(orgId, country);
        return RpcResultProcessor.process(rpcResult);
    }

    @Override
    public List<ScheduleConfigNewApiDTO> selectByOrgIdAndCountryList(Long orgId, List<String> counties) {
        RpcResult<List<ScheduleConfigNewApiDTO>> rpcResult = scheduleConfigNewApi.selectByOrgIdAndCountryList(orgId, counties);
        return RpcResultProcessor.process(rpcResult);
    }

    @Override
    public List<String> getScheduleExistsCountry(Long orgId) {
        RpcResult<List<String>> rpcResult= scheduleConfigNewApi.getScheduleExistsCountry(orgId);
        return RpcResultProcessor.process(rpcResult);
    }

}
