package com.imile.hrms.integration.waukeen.impl;

import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.hrms.integration.waukeen.BizDeptService;
import com.imile.rpc.common.RpcResult;
import com.imile.waukeen.api.business.BizDeptApi;
import com.imile.waukeen.api.business.dto.BizDeptApiDTO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * BizDept服务
 *
 * @ClassName BizDeptServiceImpl
 * <AUTHOR>
 * @Date 2023/6/1 11:05
 */
@Component
public class BizDeptServiceImpl implements BizDeptService {

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private BizDeptApi bizDeptApi;

    @Override
    public Boolean deptAdd(BizDeptApiDTO dto){
        RpcResult<Boolean> rpcResult = bizDeptApi.deptAdd(dto);
        return RpcResultProcessor.process(rpcResult);
    }



}
