package com.imile.hrms.integration.bpm.service;

import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalUpdateContentApiDTO;

import java.util.List;
import java.util.Map;

public interface BpmCreateApprovalService {

    /**
     * 发起审批
     */
    ApprovalInfoCreateResultDTO addApprovalInfo(ApprovalInitInfoApiDTO initInfoApiDTO);

    /**
     * 审批模板预览
     */
    List<ApprovalEmptyRecordApiDTO> getEmptyApprovalRecords(ApprovalEmptyRecordApiQuery query);

    /**
     * HR撤销操作
     */
    void backApply(Long approvalId);

    /**
     * 查询审批的所有审批人
     *
     * @param approvalId
     * @return
     */
    List<String> getApprovalUserCodes(Long approvalId);

    Void updateApprovalContentV2(ApprovalUpdateContentApiDTO updateContentApiDTO);

    /**
     * 根据审批单据ID查询审批状态
     *
     * @param approvalId 审批ID
     * @return Integer
     */
    Integer getApprovalStatus(Long approvalId);

    /**
     * 获取审批单据ID与审批状态映射
     *
     * @param approvalIdList 审批单据ID列表
     * @return Map
     */
    Map<Long, Integer> getApprovalStatusMap(List<Long> approvalIdList);
}
