package com.imile.hrms.integration.genesis.impl;

import com.imile.genesis.api.model.param.organization.OrganizationAddParam;
import com.imile.genesis.api.model.param.organization.OrganizationStatusSwitchParam;
import com.imile.genesis.api.model.param.organization.OrganizationUpdateParam;
import com.imile.genesis.api.model.result.organization.OrganizationDTO;
import com.imile.genesis.api.service.OrganizationApi;
import com.imile.hrms.integration.genesis.RpcOrganizationService;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Service
public class RpcOrganizationServiceImpl implements RpcOrganizationService {

    @Reference(version = "${dubbo.consumer.version.genesis}")
    private OrganizationApi organizationApi;

    @Override
    public String addOrganization(OrganizationAddParam param) {
        return RpcResultProcessor.handle(organizationApi.addOrganization(param));
    }

    @Override
    public Boolean updateOrganization(OrganizationUpdateParam param) {
        return RpcResultProcessor.handle(organizationApi.updateOrganization(param));
    }

    @Override
    public Boolean switchOrganizationStatus(OrganizationStatusSwitchParam param) {
        return RpcResultProcessor.handle(organizationApi.switchOrganizationStatus(param));
    }

    @Override
    public OrganizationDTO getOrganizationByCode(String organizationCode) {
        return RpcResultProcessor.handle(organizationApi.getOrganizationByCode(organizationCode));
    }
}
