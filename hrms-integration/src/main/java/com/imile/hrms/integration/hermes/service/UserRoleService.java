package com.imile.hrms.integration.hermes.service;


import com.imile.hermes.permission.dto.RoleResourceApiDTO;
import com.imile.hermes.permission.query.RoleResourceApiQuery;

import java.util.List;

/**
 * 用户角色服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/2
 */
public interface UserRoleService {
    /**
     * 添加用户角色绑定关系
     *
     * @param userId
     * @param tmsRoleId
     * @return
     */
    Boolean add(Long userId, Long tmsRoleId);

    /**
     * 添加用户角色绑定关系
     *
     * @param userId
     * @param tmsRoleIds
     * @return
     */
    Boolean add(Long userId, List<Long> tmsRoleIds);

    List<RoleResourceApiDTO> selectRoleByResource(RoleResourceApiQuery query);
}
