package com.imile.hrms.integration.kingdee.impl;

import com.alibaba.fastjson.JSON;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.hrms.common.constants.KingdeeConstant;
import com.imile.hrms.common.enums.EnvEnum;
import com.imile.hrms.common.enums.RelationBizTypeEnum;
import com.imile.hrms.dao.sys.dao.HrmsPlatformRelationDao;
import com.imile.hrms.dao.sys.model.HrmsPlatformRelationDO;
import com.imile.hrms.integration.kingdee.KingdeeIntegration;
import com.imile.hrms.integration.kingdee.dto.AuditDTO;
import com.imile.hrms.integration.kingdee.dto.DeleteDTO;
import com.imile.hrms.integration.kingdee.dto.KingDeeCostDeptInfoDTO;
import com.imile.hrms.integration.kingdee.dto.KingDeeCountryInfoDTO;
import com.imile.hrms.integration.kingdee.dto.KingDeeOcInfoDTO;
import com.imile.hrms.integration.kingdee.dto.KingDeeUserInfoDTO;
import com.imile.hrms.integration.kingdee.dto.OperationDTO;
import com.imile.hrms.integration.kingdee.dto.UnAuditDTO;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.openapi.api.KingdeeApi;
import com.imile.openapi.api.kingdee.KingdeeResult;
import com.imile.openapi.api.kingdee.NewKingdeeResult;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
@Service
public class KingdeeIntegrationImpl implements KingdeeIntegration {

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private KingdeeApi kingdeeApi;

    @Autowired
    private HrmsPlatformRelationDao hrmsPlatformRelationDao;

    @Value(value = "${server.env}")
    private String env;

    @Override
    public NewKingdeeResult syncOfCountryInfo(KingDeeCountryInfoDTO countryInfoDTO) {
        log.info("kingdee sync countryInfo,env:{},param:{}", env, countryInfoDTO);
        if (EnvEnum.isInformal(env)) {
            NewKingdeeResult result = new NewKingdeeResult();
            result.setId(countryInfoDTO.getId().toString());
            return result;
        }
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        List<String> keyList = new ArrayList<>();
        keyList.add("FId");
        map.put("IsAutoSubmitAndAudit", true);
        log.info("kingdee对接:{}", countryInfoDTO.toString());
        RpcResult<NewKingdeeResult> rpcResult = kingdeeApi.saveResultStringId(KingdeeConstant.BusKey.COUNTRY_KEY, JSON.toJSONString(countryInfoDTO), keyList, map);
        log.info("调用金蝶save接口保存国家,param:{},result:{}", countryInfoDTO, rpcResult);
        NewKingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    @Override
    public KingdeeResult syncOfCostDeptInfo(KingDeeCostDeptInfoDTO costDeptInfoDTO) {
        log.info("kingdee sync costDeptInfo,env:{},param:{}", env, costDeptInfoDTO);
        if (EnvEnum.isInformal(env)) {
            return this.mockKingdeeResult(costDeptInfoDTO.getId());
        }
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        List<String> keyList = new ArrayList<>();
        keyList.add("FCreateOrgId");
        keyList.add("FUseOrgId");
        keyList.add("FDeptProperty");
        map.put("IsAutoSubmitAndAudit", true);
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.save(KingdeeConstant.BusKey.DEPT_KEY, JSON.toJSONString(costDeptInfoDTO), keyList, map);
        log.info("调用金蝶save接口保存部门,param:{},result:{}", costDeptInfoDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    @Override
    public KingdeeResult syncOfOcInfo(KingDeeOcInfoDTO ocInfoDTO) {
        log.info("kingdee sync ocInfo,env:{},param:{}", env, ocInfoDTO);
        if (EnvEnum.isInformal(env)) {
            return this.mockKingdeeResult(ocInfoDTO.getId());
        }
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        List<String> keyList = new ArrayList<>();
        keyList.add("FCreateOrgId");
        keyList.add("FUseOrgId");
        keyList.add("Fparent_oc_code");
        keyList.add("FGroup");
        map.put("IsAutoSubmitAndAudit", true);
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.save(KingdeeConstant.BusKey.OC_KEY, JSON.toJSONString(ocInfoDTO), keyList, map);
        log.info("调用金蝶save接口保存网点,param:{},result:{}", ocInfoDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    @Override
    public KingdeeResult syncOfUserInfo(KingDeeUserInfoDTO userInfoDTO) {
        log.info("kingdee sync userInfo,env:{},param:{}", env, userInfoDTO);
        if (EnvEnum.isInformal(env)) {
            return this.mockKingdeeResult(userInfoDTO.getId());
        }
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        List<String> keyList = new ArrayList<>();
        keyList.add("FCreateOrgId");
        keyList.add("FUseOrgId");
        keyList.add("F_IMI_Base");
        keyList.add("F_IMI_SSBM");
        map.put("IsAutoSubmitAndAudit", true);
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.save(KingdeeConstant.BusKey.USER_KEY, JSON.toJSONString(userInfoDTO), keyList, map);
        log.info("调用金蝶save接口保存用户,param:{},result:{}", userInfoDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    /**
     * 审核
     *
     * @param auditDTO
     * @param busKey
     * @return
     */
    @Override
    public KingdeeResult audit(AuditDTO auditDTO, String busKey) {
        log.info("kingdee audit,env:{},param:{},busKey:{}", env, auditDTO, busKey);
        if (EnvEnum.isInformal(env)) {
            return new KingdeeResult();
        }
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.audit(busKey, JSON.toJSONString(auditDTO));
        log.info("调用金蝶audit接口,param:{},result:{}", auditDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        return kingdeeResult;
    }

    /**
     * 反审核
     *
     * @param unAuditDTO
     * @return
     */
    @Override
    public KingdeeResult unAudit(UnAuditDTO unAuditDTO, String busKey) {
        log.info("kingdee unAudit,env:{},param:{},busKey:{}", env, unAuditDTO, busKey);
        if (EnvEnum.isInformal(env)) {
            return new KingdeeResult();
        }
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.unaudit(busKey, JSON.toJSONString(unAuditDTO));
        log.info("调用金蝶unAudit接口,param:{},result:{}", unAuditDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    @Override
    public KingdeeResult delete(String busKey, DeleteDTO deleteDTO) {
        log.info("kingdee delete,env:{},param:{},busKey:{}", env, deleteDTO, busKey);
        if (EnvEnum.isInformal(env)) {
            return new KingdeeResult();
        }
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.delete(busKey, JSON.toJSONString(deleteDTO));
        log.info("调用金蝶delete接口,param:{},result:{}", deleteDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    /**
     * 禁用接口
     *
     * @param operationDTO
     * @return boolean
     */
    @Override
    public KingdeeResult forbidOperation(OperationDTO operationDTO, String busKey) {
        log.info("kingdee forbidOperation,env:{},param:{},busKey:{}", env, operationDTO, busKey);
        List<String> busKeys = new ArrayList<>();
        busKeys.add(busKey);
        busKeys.add(KingdeeConstant.OPERATION.FORBID);
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.forbidOperation(busKeys, JSON.toJSONString(operationDTO));
        log.info("调用金蝶forbidOperation接口,param:{},result:{}", operationDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    /**
     * 反禁用接口
     *
     * @param operationDTO
     * @return boolean
     */
    @Override
    public KingdeeResult enableOperation(OperationDTO operationDTO, String busKey) {
        log.info("kingdee enableOperation,env:{},param:{},busKey:{}", env, operationDTO, busKey);
        List<String> busKeys = new ArrayList<>();
        busKeys.add(busKey);
        busKeys.add(KingdeeConstant.OPERATION.ENABLE);
        RpcResult<KingdeeResult> rpcResult = kingdeeApi.enableOperation(busKeys, JSON.toJSONString(operationDTO));
        log.info("调用金蝶enableOperation接口,param:{},result:{}", operationDTO, rpcResult);
        KingdeeResult kingdeeResult = RpcResultProcessor.process(rpcResult);
        if (kingdeeResult == null) {
            throw BusinessException.get(MsgCodeConstant.RPC_ERROR, "call kingdee service exception");
        }
        return kingdeeResult;
    }

    @Override
    public void noAuditAndStatusSwitch(String bizId, String bizType, String relationId, String platForm, String status, String key) {
        log.info("kingdee noAuditAndStatusSwitch,biz_id:{},biz_type:{},relation_id:{},platform:{},status:{},key:{}",
                bizId, bizType, relationId, platForm, status, key);
        if (EnvEnum.isInformal(env)) {
            return;
        }
        // 人员/部门停启用时状态暂时无需同步金蝶，以防业务在金蝶后台无法使用
        if (RelationBizTypeEnum.KINGDEE_USER.getCode().equals(bizType)
                || RelationBizTypeEnum.KINGDEE_DEPT.getCode().equals(bizType)
                || RelationBizTypeEnum.KINGDEE_COUNTRY.getCode().equals(bizType)) {
            log.info("kingdee noAuditAndStatusSwitch ignore,biz_id:{},biz_type:{},relation_id:{}", bizId, bizType, relationId);
            return;
        }
        HrmsPlatformRelationDO platFormRelation = hrmsPlatformRelationDao.getPlatFormRelation(bizId, bizType, relationId, platForm);
        //未同步到金蝶，需报错
        if (platFormRelation == null || StringUtils.isBlank(platFormRelation.getRelationId())) {
            log.info("kingdee noAuditAndStatusSwitch cancel,biz_id:{},biz_type:{},relation_id:{}", bizId, bizType, relationId);
            return;
        }
        Long ocKingdeeId = Long.parseLong(platFormRelation.getRelationId());
        //若不为空,修改信息前说明已经是审核通过了，需要先做反审核操作
        UnAuditDTO unAuditDTO = new UnAuditDTO();
        unAuditDTO.setId(ocKingdeeId);
        unAudit(unAuditDTO, key);

        //如状态为禁用 则调用反审核后即可
        if (StatusEnum.DISABLED.getCode().equals(status)) {
            OperationDTO operationDTO = new OperationDTO();
            operationDTO.setId(ocKingdeeId);
            forbidOperation(operationDTO, key);
        } else {
            OperationDTO operationDTO = new OperationDTO();
            operationDTO.setId(ocKingdeeId);
            enableOperation(operationDTO, key);
        }
//        AuditDTO auditDTO = new AuditDTO();
//        auditDTO.setId(ocKingdeeId);
//        audit(auditDTO, key);
    }

    /**
     * 模拟金蝶返回结果（非正式环境使用，以便下游可以正常保存关联关系）
     *
     * @param id 源数据ID
     * @return KingdeeResult
     */
    private KingdeeResult mockKingdeeResult(Long id) {
        KingdeeResult result = new KingdeeResult();
        result.setId(id);
        return result;
    }
}
