package com.imile.hrms.integration.recognition;

import com.imile.recognition.api.face.model.dto.OssDTO;
import com.imile.recognition.api.face.model.dto.UserFaceSearchDTO;
import com.imile.recognition.api.ocr.model.dto.CertificatesDTO;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
public interface RecognitionService {

    /**
     * 证件识别
     */
    CertificatesDTO certificatesRecognition(String fileKey, String country, String certificateType);

    /**
     * 人脸特征检测
     */
    Boolean detectFace(String fileKey, String imageBase64);

    /**
     * 人脸录入
     */
    Boolean faceFeatureSave(String sourceType, String userCode, Long userId, String country, String employeeType, String fileKey, String imageBase64);

    /**
     * 人脸识别搜索
     * 返回得分最高的匹配用户
     */
    UserFaceSearchDTO faceRecognition(String fileKey, String imageBase64, String country, List<String> employeeTypeList);

    /**
     * 人脸一对一识别检测
     */
    UserFaceSearchDTO faceSingleCheck(String userCode, String fileKey, String imageBase64);

    /**
     * 人脸识别搜索
     * 返回得分通过的所有用户
     */
    List<UserFaceSearchDTO> faceRecognitionPlus(String fileKey, String imageBase64);

    /**
     * 根据账号查询人脸照片
     * @param userCode
     * @return
     */
    OssDTO getFaceRecognitionPhoto(String userCode);

    /**
     * 根据ID查询人脸照片
     * @param userId
     * @return
     */
    OssDTO getFaceRecognitionPhotoByUserId(Long userId);


    /**
     * 清除指定用户人脸照片
     *
     * @param userCode 用户编码
     * @param userId   用户ID
     * @return Boolean
     */
    Boolean clearUserFacePhoto(String userCode, Long userId);

}
