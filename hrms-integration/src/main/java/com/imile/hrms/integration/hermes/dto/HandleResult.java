package com.imile.hrms.integration.hermes.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 同步结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/30
 */
@Data
public class HandleResult<T> implements Serializable {
    private static final long serialVersionUID = -5062195204237599824L;
    /**
     * 是否成功
     */
    private Boolean isSuccess;
    /**
     * 返回数据
     */
    private T data;

    /**
     * 消息
     */
    private String msg;

    public HandleResult(Boolean isSuccess, T data, String msg) {
        this.isSuccess = isSuccess;
        this.data = data;
        this.msg = msg;
    }

    public HandleResult(T data) {
        this(Boolean.TRUE, data, null);
    }

    public HandleResult(Boolean isSuccess, String msg) {
        this.isSuccess = isSuccess;
        this.msg = msg;
    }

    public HandleResult() {
        this(Boolean.FALSE, null, null);
    }

    public Boolean isSuccess() {
        return this.isSuccess != null && this.isSuccess;
    }
}
