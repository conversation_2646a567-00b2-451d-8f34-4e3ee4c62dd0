package com.imile.hrms.integration.genesis;

import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.user.UserCertificateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public interface RpcUserCertificateService {

    /**
     * 获取人员证件列表
     *
     * @param userCode 人员编码
     * @return List<UserCertificateDTO>
     */
    List<UserCertificateDTO> getUserCertificateList(String userCode);


    /**
     * 批量获取人员证件列表
     *
     * @param userCodeList 人员编码
     * @return Map<String, List < UserCertificateDTO>>
     */
    Map<String, Map<String, UserCertificateDTO>> getUserCode2TypeMap(List<String> userCodeList, List<String> certificateTypeCodeList);

    /**
     * 校验人员证件是否合法
     *
     * @param param UserCertificateCheckParam
     * @return UserCertificateCheckResultDTO
     */
    UserCertificateCheckResultDTO checkUserCertificate(UserCertificateCheckParam param);

    /**
     * 校验人员证件是否重复
     *
     * @param param UserCertificateCheckParam
     * @return UserCertificateCheckResultDTO
     */
    UserCertificateCheckResultDTO checkUserCertificateDuplicate(UserCertificateDuplicateCheckParam param);

    /**
     * 保存人员证件
     *
     * @param userCode       人员编码
     * @param paramList      人员证件列表
     * @param operationScene 操作场景
     * @return Boolean
     */
    Boolean saveUserCertificate(String userCode, List<UserCertificateSaveParam> paramList, OperationSceneEnum
            operationScene);

    /**
     * 保存人员证件（人员表拆分前hrms临时使用）
     *
     * @param userId    人员ID
     * @param paramList 人员证件列表
     * @return Boolean
     */
    Boolean saveUserCertificate(Long userId, List<UserCertificateSaveParam> paramList);
}
