package com.imile.hrms.integration.platform.service.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/15
 */
@Data
public class AccessTokenDTO {
    /**
     * 访问秘钥
     */
    private String accessToken;
    /**
     * 过期时间，毫秒
     */
    private Long expiresTime;

    /**
     * 刷新
     *
     * @param accessToken
     * @param expiresTime
     */
    public void refresh(String accessToken, Long expiresTime) {
        this.accessToken = accessToken;
        this.expiresTime = expiresTime;
    }
}
