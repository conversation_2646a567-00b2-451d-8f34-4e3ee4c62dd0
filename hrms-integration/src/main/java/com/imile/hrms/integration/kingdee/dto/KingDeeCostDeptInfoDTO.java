package com.imile.hrms.integration.kingdee.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.imile.hrms.common.entity.KingDeeLangNameDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KingDeeCostDeptInfoDTO implements Serializable {
    private static final long serialVersionUID = 8561918828113031942L;

    @JSONField(name = "FDEPTID", ordinal = 1)
    /**
     * 金蝶id
     */
    private Long id;

    @JSONField(name = "FName", ordinal = 2)
    /**
     * 部门名称
     */
    private List<KingDeeLangNameDTO> deptName;

    @JSONField(name = "FNumber", ordinal = 3)
    /**
     * 部门编码
     */
    private String deptCode;

    @JSONField(name = "FCreateOrgId", ordinal = 4)
    /**
     * 创建组织id
     */
    private Long createOrgId;

    @JSONField(name = "FUseOrgId", ordinal = 5)
    /**
     * 使用组织id
     */
    private Long useOrgId;

    @JSONField(name = "FDeptProperty", ordinal = 6)
    /**
     * 部门属性
     */
    private String deptAttr;

}
