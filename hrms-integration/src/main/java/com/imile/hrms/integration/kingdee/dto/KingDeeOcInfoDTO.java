package com.imile.hrms.integration.kingdee.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.imile.hrms.common.entity.KingDeeLangNameDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KingDeeOcInfoDTO implements Serializable {
    private static final long serialVersionUID = 3978475232669278798L;

    /**
     * 归属公司 ID
     */
    @JSONField(name = "FID",ordinal = 1)
    private Long id;
    /**
     * 归属公司 ID
     */
    @JSONField(name = "FCreateOrgId",ordinal = 3)
    private Long orgId;
    /**
     * 归属公司 ID
     */
    @JSONField(name = "FUseOrgId",ordinal = 4)
    private Long userOrgId;
    /**
     * 名称信息-网点编号
     */
    @JSONField(name = "FNumber",ordinal = 2)
    private String ocCode;
    /**
     * 名称信息-网点名称
     */
    @JSONField(name = "FName",ordinal = 5)
    private List<KingDeeLangNameDTO> ocName;
    /**
     * 名称信息-网点简称
     */
    @JSONField(name = "Foc_short_name",ordinal = 6)
    private String ocShortName;

    /**
     * 层级信息-父节点编码
     */
    @JSONField(name = "Fparent_oc_code",ordinal = 7)
    private String parentOcCode;

    /**
     * 名称信息-经营类别code
     */
    @JSONField(name = "Fbusiness_category",ordinal = 8)
    private String businessCategory;

    /**
     * 网点类型code
     */
    @JSONField(name = "Foc_type",ordinal = 9)
    private String ocType;

    /**
     * 分组
     */
    @JSONField(name = "FGroup", ordinal = 10)
    private String group;

    @JSONField(serialize = false)
    private String status;
}
