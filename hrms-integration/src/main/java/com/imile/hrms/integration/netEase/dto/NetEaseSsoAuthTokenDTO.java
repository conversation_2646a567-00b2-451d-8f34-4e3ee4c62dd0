package com.imile.hrms.integration.netEase.dto;

import lombok.Data;

/**
 * 获取Token返回值
 *
 * <AUTHOR>
 * @date 2022/11/22
 */
@Data
public class NetEaseSsoAuthTokenDTO {
    /**
     * 单点登录辅助凭证
     */
    private String ssoAuthToken;
    /**
     * 单点登录辅助凭证有效期 截止
     */
    private String ssoAuthTokenExpiredTime;

    /**
     * 刷新
     *
     * @param dto
     */
    public void refresh(NetEaseSsoAuthTokenDTO dto) {
        this.ssoAuthToken = dto.ssoAuthToken;
        this.ssoAuthTokenExpiredTime = dto.ssoAuthTokenExpiredTime;
    }
}
