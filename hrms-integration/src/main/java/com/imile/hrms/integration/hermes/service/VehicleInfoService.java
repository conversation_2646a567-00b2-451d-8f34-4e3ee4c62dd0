package com.imile.hrms.integration.hermes.service;

import com.imile.hermes.vehicle.dto.VehicleDriverDateInfoApiDTO;
import com.imile.hermes.vehicle.dto.VehicleInfoApiDTO;
import com.imile.hermes.vehicle.dto.VehicleStationDateInfoApiDTO;
import com.imile.hermes.vehicle.query.VehicleDriverDateInfoApiQuery;
import com.imile.rpc.common.RpcResult;

import java.util.List;

public interface VehicleInfoService {

    /**
     * 获取指定时间车辆的司机
     */
    List<VehicleDriverDateInfoApiDTO> selectVehicleDriverByDate(List<VehicleDriverDateInfoApiQuery> queryList);

    /**
     * 获取指定时间车辆的网点
     */
    List<VehicleStationDateInfoApiDTO> selectVehicleStationByDate(List<VehicleDriverDateInfoApiQuery> queryList);


}
