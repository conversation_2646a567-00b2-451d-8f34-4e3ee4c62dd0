package com.imile.hrms.integration.permission.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Data
public class DataPermissionDTO implements Serializable {
    /**
     * 数据字典类型
     */
    private String typeCode;

    /**
     * 数据字典名称
     */
    private String typeName;

    /**
     * 字典数据
     */
    private List<String> dataCodeList;
}
