package com.imile.hrms.integration.platform.service.logrecord;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/1/19
 */
public interface LogRecordIntegration {
    /**
     * 对比新旧数据，查看新旧数据字段更改情况
     *
     * @param newObject
     * @param oldObject
     * @return
     */
    <T> void diffObj(T newObject, T oldObject, String operationType);

    /**
     * 对比新旧数据，带操作描述
     *
     */
    <T> void diffObjWithRemark(T newObject, T oldObject, String operationType, String remark);

    /**
     * 对比新旧数据，查看新旧数据字段更改情况
     *
     * @param newObject
     * @param oldObject
     * @param operationType
     * @param fieldName
     * @param <T>
     */
    <T> void diffObj(T newObject, T oldObject, String operationType, String fieldName);

    /**
     * 对比新旧数据，查看新旧数据字段更改情况
     *
     * @param newList
     * @param oldList
     * @param <T>
     */
    <T> void diffList(List<T> newList, List<T> oldList, String operationType);

    /**
     * 对比新旧数据，查看新旧数据字段更改情况
     *
     * @param newList
     * @param oldList
     * @param operationType
     * @param fieldName
     * @param <T>
     */
    <T> void diffList(List<T> newList, List<T> oldList, String operationType, String fieldName);
}
