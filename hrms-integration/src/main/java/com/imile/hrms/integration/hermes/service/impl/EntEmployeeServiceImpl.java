package com.imile.hrms.integration.hermes.service.impl;

import com.imile.common.page.PaginationResult;
import com.imile.hermes.enterprise.api.EntEmployeeApi;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hermes.enterprise.dto.EntEmployeeSaveApiDTO;
import com.imile.hermes.enterprise.dto.SwitchStatusParamDTO;
import com.imile.hermes.enterprise.dto.UpdateHrUserIdParamDTO;
import com.imile.hermes.enterprise.query.EntEmployeeApiPageQuery;
import com.imile.hermes.enterprise.query.EntEmployeeApiQuery;
import com.imile.hermes.enterprise.query.SynHermes2HrmsApiPageQuery;
import com.imile.hrms.common.context.RequestInfoHolder;
import com.imile.hrms.common.util.HrmsCollectionUtils;
import com.imile.hrms.integration.hermes.service.EntEmployeeService;
import com.imile.hrms.integration.hermes.vo.HrUserInfoDTO;
import com.imile.hrms.integration.utils.RpcResultProcessor;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/16
 */
@Service
@Slf4j
public class EntEmployeeServiceImpl implements EntEmployeeService {
    @Reference(version = "1.0.0", check = false, timeout = 6000)
    private EntEmployeeApi entEmployeeApi;

    @Override
    public List<String> queryDtlUserCode(EntEmployeeApiQuery entEmployeeApiQuery) {
        RpcResult<List<EntEmployeeApiDTO>> rpcResult = entEmployeeApi.getEmployeeList(entEmployeeApiQuery);
        List<EntEmployeeApiDTO> entEmployeeApiDTOList = RpcResultProcessor.process(rpcResult);
        return entEmployeeApiDTOList.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
    }

    @Override
    public String doCreate(EntEmployeeSaveApiDTO saveApiDTO) {
        // 设置登录信息
        saveApiDTO.setRequestMeta(RequestInfoHolder.meta());
        RpcResult<String> rpcResult = entEmployeeApi.doCreate(saveApiDTO);
        return RpcResultProcessor.process(rpcResult);
    }

    @Override
    public String doUpdate(EntEmployeeSaveApiDTO saveApiDTO) {
        // 设置登录信息
        saveApiDTO.setRequestMeta(RequestInfoHolder.meta());
        RpcResult<String> rpcResult = entEmployeeApi.doUpdate(saveApiDTO);
        return RpcResultProcessor.process(rpcResult);
    }

    @Override
    public String doDeleteHrUser(EntEmployeeSaveApiDTO saveApiDTO) {
        // 设置登录信息
        saveApiDTO.setRequestMeta(RequestInfoHolder.meta());
        RpcResult<String> rpcResult = entEmployeeApi.doDeleteHrUser(saveApiDTO);
        return RpcResultProcessor.process(rpcResult);
    }

    @Override
    public String switchHrUserStatus(SwitchStatusParamDTO switchStatusParamDTO) {
        // 设置登录信息
        switchStatusParamDTO.setRequestMeta(RequestInfoHolder.meta());
        RpcResult<String> rpcResult = entEmployeeApi.switchHrUserStatus(switchStatusParamDTO);
        return RpcResultProcessor.process(rpcResult);
    }

    @Override
    public List<HrUserInfoDTO> getUnSyncEmployeeInfoList(EntEmployeeApiQuery queryDTO) {
        RpcResult<List<EntEmployeeApiDTO>> unSyncEmployeeInfoList = entEmployeeApi.getUnSyncEmployeeInfoList(queryDTO);
        return HrmsCollectionUtils.convert(RpcResultProcessor.process(unSyncEmployeeInfoList), HrUserInfoDTO.class);
    }

    @Override
    public String updateHrUserId(List<UpdateHrUserIdParamDTO> userIdParamDTOS) {
        return RpcResultProcessor.process(entEmployeeApi.updateHrUserId(userIdParamDTOS));
    }

    @Override
    public boolean checkExisted(String userCode) {
        EntEmployeeApiQuery apiQuery = new EntEmployeeApiQuery();
        apiQuery.setUserCodes(Collections.singletonList(userCode));
        List<EntEmployeeApiDTO> entEmployeeApiDTOS = RpcResultProcessor.process(entEmployeeApi.listBaseInfo(apiQuery));
        return CollectionUtils.isNotEmpty(entEmployeeApiDTOS);
    }

    @Override
    public EntEmployeeApiDTO getEmployeeByCode(String userCode) {
        EntEmployeeApiQuery apiQuery = new EntEmployeeApiQuery();
        apiQuery.setOrgId(RequestInfoHolder.getOrgId());
        apiQuery.setUserCodes(Collections.singletonList(userCode));
        List<EntEmployeeApiDTO> employeeList = RpcResultProcessor.process(entEmployeeApi.getEmployeeList(apiQuery));
        return CollectionUtils.isNotEmpty(employeeList) ? employeeList.get(0) : null;
    }

    @Override
    public List<EntEmployeeApiDTO> getEmployeeList(EntEmployeeApiQuery query) {
        return RpcResultProcessor.process(entEmployeeApi.getEmployeeList(query));
    }

    @Override
    public PaginationResult<EntEmployeeApiDTO> getEmployeePage(EntEmployeeApiPageQuery query) {
        return RpcResultProcessor.process(entEmployeeApi.getEmployeePage(query));
    }

    @Override
    public PaginationResult<EntEmployeeApiDTO> getSynHermes2HrmsEmployee(SynHermes2HrmsApiPageQuery query) {
        return RpcResultProcessor.process(entEmployeeApi.getSynHermes2HrmsEmployee(query));
    }

    @Override
    public List<EntEmployeeApiDTO> listEmployee(EntEmployeeApiQuery query) {
        return RpcResultProcessor.process(entEmployeeApi.getEmployeeList(query));
    }

    @Override
    public List<EntEmployeeApiDTO> listAllEmployee(EntEmployeeApiQuery query) {
        return RpcResultProcessor.process(entEmployeeApi.listAllEmployee(query));
    }

    @Override
    public String accountConvert(EntEmployeeSaveApiDTO saveApiDTO) {
        // 设置登录信息
        saveApiDTO.setRequestMeta(RequestInfoHolder.meta());
        RpcResult<String> rpcResult = entEmployeeApi.accountConvert(saveApiDTO);
        return RpcResultProcessor.process(rpcResult);
    }
}
